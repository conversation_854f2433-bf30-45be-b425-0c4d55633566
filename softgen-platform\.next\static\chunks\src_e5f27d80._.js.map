{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/admin.ts"], "sourcesContent": ["const LEGACY_ADMIN_EMAILS = [\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n  \"<EMAIL>\",\r\n] as const;\r\n\r\nconst ADMIN_DOMAINS = [\"softgen.ai\"] as const;\r\n\r\nexport const isAdminEmail = (email: string | null | undefined): boolean => {\r\n  if (!email) {\r\n    return false;\r\n  }\r\n\r\n  const normalizedEmail = email.toLowerCase().trim();\r\n\r\n  const isLegacyAdmin = LEGACY_ADMIN_EMAILS.some(\r\n    (adminEmail) => adminEmail.toLowerCase() === normalizedEmail,\r\n  );\r\n\r\n  if (isLegacyAdmin) {\r\n    return true;\r\n  }\r\n\r\n  const isDomainAdmin = ADMIN_DOMAINS.some((domain) => normalizedEmail.endsWith(`@${domain}`));\r\n\r\n  if (isDomainAdmin) {\r\n    return true;\r\n  }\r\n\r\n  return false;\r\n};\r\n"], "names": [], "mappings": ";;;AAAA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IAAC;CAAa;AAE7B,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,kBAAkB,MAAM,WAAW,GAAG,IAAI;IAEhD,MAAM,gBAAgB,oBAAoB,IAAI,CAC5C,CAAC,aAAe,WAAW,WAAW,OAAO;IAG/C,IAAI,eAAe;QACjB,OAAO;IACT;IAEA,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAC,SAAW,gBAAgB,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ;IAE1F,IAAI,eAAe;QACjB,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-project-access.ts"], "sourcesContent": ["import { isAdminEmail } from \"@/lib/admin\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { Project } from \"@/providers/project-provider\";\r\nimport { useCallback } from \"react\";\r\n\r\nexport const useProjectAccess = () => {\r\n  const { user } = useAuth();\r\n\r\n  const checkProjectAccess = useCallback(\r\n    (project: Project | null | undefined) => {\r\n      if (!project || !user?.userFromDb?.email) return false;\r\n\r\n      const isTeam = project.team_emails?.includes(user.userFromDb.email) || false;\r\n      const isUser = project.owner_id === user.userFromDb.id;\r\n      const isAdmin = isAdminEmail(user.userFromDb.email);\r\n\r\n      return isAdmin || isTeam || isUser;\r\n    },\r\n    [user],\r\n  );\r\n\r\n  return { checkProjectAccess };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;;AAEO,MAAM,mBAAmB;;IAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,qBAAqB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;4DACnC,CAAC;YACC,IAAI,CAAC,WAAW,CAAC,MAAM,YAAY,OAAO,OAAO;YAEjD,MAAM,SAAS,QAAQ,WAAW,EAAE,SAAS,KAAK,UAAU,CAAC,KAAK,KAAK;YACvE,MAAM,SAAS,QAAQ,QAAQ,KAAK,KAAK,UAAU,CAAC,EAAE;YACtD,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU,CAAC,KAAK;YAElD,OAAO,WAAW,UAAU;QAC9B;2DACA;QAAC;KAAK;IAGR,OAAO;QAAE;IAAmB;AAC9B;GAjBa;;QACM,wIAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/project-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useProjectAccess } from \"@/hooks/use-project-access\";\r\nimport { getProject, getProjectThreads, useCheckAndStart } from \"@/lib/api\";\r\nimport { Thread } from \"@/types/thread-message\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { notFound } from \"next/navigation\";\r\nimport { useTopLoader } from \"nextjs-toploader\";\r\nimport { createContext, useContext, useEffect, useRef } from \"react\";\r\nimport { CACHE_PARAMS } from \"./query-provider\";\r\n\r\nexport const SandBoxState = {\r\n  CREATING: \"creating\",\r\n  RESTORING: \"restoring\",\r\n  DESTROYED: \"destroyed\",\r\n  DESTROYING: \"destroying\",\r\n  STARTED: \"started\",\r\n  STOPPED: \"stopped\",\r\n  STARTING: \"starting\",\r\n  STOPPING: \"stopping\",\r\n  ERROR: \"error\",\r\n  BUILD_FAILED: \"build_failed\",\r\n  PENDING_BUILD: \"pending_build\",\r\n  BUILDING_SNAPSHOT: \"building_snapshot\",\r\n  UNKNOWN: \"unknown\",\r\n  PULLING_SNAPSHOT: \"pulling_snapshot\",\r\n  ARCHIVING: \"archiving\",\r\n  ARCHIVED: \"archived\",\r\n};\r\n\r\nexport type SanboxPreviewState = \"starting\" | \"sleeping\" | \"idle\" | \"unavailable\";\r\n\r\nexport type Project = {\r\n  project_id: string;\r\n  name: string;\r\n  creation_date: string;\r\n  last_updated_date: string;\r\n  sandbox_state: SanboxPreviewState;\r\n  env_id: string;\r\n  env_domain: string;\r\n  env_url: string;\r\n  frontend_page_routes: string[];\r\n  github_repo: string;\r\n  isRunning: number;\r\n  preview_image_url: string | null;\r\n  onboarding_completed: boolean;\r\n  tech_stack_prompt: string[] | null;\r\n  custom_instructions: string | null;\r\n  users_prompt_Array: string[] | null;\r\n  deployment: {\r\n    is_deployed?: boolean;\r\n    deployment_url?: string;\r\n    last_deployed?: string;\r\n    production_url?: string;\r\n    vercel_token?: string | null;\r\n  } | null;\r\n  isPublic: boolean;\r\n  owner_id: number;\r\n  team_emails: string[];\r\n  supabase?: {\r\n    connected: boolean;\r\n    organization_id?: string;\r\n    organization_name?: string;\r\n    supabase_project_id?: string;\r\n    api_key?: string;\r\n    api_url?: string;\r\n    service_role_key?: string;\r\n    database_password?: string;\r\n    region?: string;\r\n    created_at?: string;\r\n    ref?: string;\r\n    anon_key?: string;\r\n    access_token?: string;\r\n    refresh_token?: string;\r\n  };\r\n  supabase_organizations?: {\r\n    organization_id: string;\r\n    name: string;\r\n    access_token: string;\r\n    refresh_token: string;\r\n  }[];\r\n  isSupabaseConnected?: boolean;\r\n};\r\n\r\ntype ProjectContextType = {\r\n  projectId: string;\r\n  project: Project | null;\r\n  isProjectLoading: boolean;\r\n  threads: Thread[];\r\n  isLoadingThreads: boolean;\r\n  hasAccess: boolean;\r\n  refetch: () => void;\r\n  sandboxState: SanboxPreviewState;\r\n};\r\n\r\nconst ProjectContext = createContext<ProjectContextType | null>(null);\r\n\r\nexport const ProjectProvider = ({\r\n  children,\r\n  projectId,\r\n}: {\r\n  children: React.ReactNode;\r\n  projectId: string;\r\n}) => {\r\n  const MAX_NO_DOMAIN_RETRIES = 15;\r\n\r\n  const {\r\n    data: rawProject,\r\n    isLoading: isProjectLoading,\r\n    error,\r\n    refetch,\r\n  } = useQuery<\r\n    Omit<Project, \"team_emails\"> & {\r\n      team_emails: string;\r\n      tech_stack_prompt: string;\r\n      users_prompt_Array: string;\r\n    },\r\n    { detail: string }\r\n  >({\r\n    queryKey: [\"get-project\", projectId],\r\n    queryFn: () => getProject(projectId),\r\n    retry: (count, error) => {\r\n      if (error?.detail?.includes(\"Not authorized to access this project\")) return false;\r\n      return count < 1;\r\n    },\r\n    refetchOnWindowFocus: true,\r\n    refetchOnReconnect: true,\r\n    ...CACHE_PARAMS,\r\n    refetchInterval: (query) => {\r\n      if (\r\n        query.state.data?.isRunning === 0 &&\r\n        query.state.dataUpdateCount < MAX_NO_DOMAIN_RETRIES\r\n      ) {\r\n        return 1000 * 5;\r\n      }\r\n\r\n      return 30000;\r\n    },\r\n  });\r\n\r\n  const loader = useTopLoader();\r\n\r\n  useEffect(() => {\r\n    if (isProjectLoading) {\r\n      loader.start();\r\n    } else {\r\n      loader.done();\r\n    }\r\n\r\n    return () => {\r\n      loader.done();\r\n    };\r\n  }, [isProjectLoading]);\r\n\r\n  const project = rawProject\r\n    ? {\r\n        ...rawProject,\r\n        team_emails: parseData(rawProject.team_emails),\r\n        tech_stack_prompt: parseData(rawProject.tech_stack_prompt),\r\n        users_prompt_Array: parseData(rawProject.users_prompt_Array),\r\n      }\r\n    : null;\r\n\r\n  const { checkProjectAccess } = useProjectAccess();\r\n  const hasAccess = checkProjectAccess(project);\r\n\r\n  const startAttemptRef = useRef(false);\r\n\r\n  const startEnvironment = useCheckAndStart();\r\n  useEffect(() => {\r\n    if (hasAccess && rawProject?.project_id) {\r\n      startEnvironment.mutate(rawProject.project_id, {\r\n        onSettled: () => {\r\n          refetch().finally(() => {\r\n            startAttemptRef.current = true;\r\n          });\r\n        },\r\n      });\r\n    }\r\n  }, [hasAccess, rawProject?.project_id]);\r\n\r\n  const { data: threads = [], isLoading: isLoadingThreads } = useQuery({\r\n    queryKey: [\"threads\", projectId],\r\n    queryFn: async () => {\r\n      try {\r\n        const threads = await getProjectThreads(projectId);\r\n        return threads.reverse();\r\n      } catch (error) {\r\n        if ((error as { detail: string })?.detail === \"No threads found for this project\") {\r\n          return [];\r\n        }\r\n        throw error;\r\n      }\r\n    },\r\n    enabled: !isProjectLoading && hasAccess,\r\n    ...CACHE_PARAMS,\r\n  });\r\n\r\n  if (isProjectLoading) return null;\r\n\r\n  const hasReadAccess = !error?.detail?.includes(\"Not authorized to access this project\");\r\n  const projectNotFound = error?.detail?.includes(\"Project not found\");\r\n\r\n  if (!hasReadAccess || projectNotFound) return notFound();\r\n\r\n  // no start attempt or sandbox is starting or restoring\r\n  const sandboxStarting =\r\n    !startAttemptRef.current ||\r\n    project?.sandbox_state === SandBoxState.STARTING ||\r\n    project?.sandbox_state === SandBoxState.RESTORING;\r\n\r\n  const isSandboxSleeping =\r\n    startAttemptRef.current &&\r\n    (project?.sandbox_state === SandBoxState.STOPPED ||\r\n      project?.sandbox_state === SandBoxState.ARCHIVED);\r\n\r\n  const sandboxState = sandboxStarting\r\n    ? \"starting\"\r\n    : isSandboxSleeping\r\n      ? \"sleeping\"\r\n      : project?.sandbox_state === SandBoxState.ERROR ||\r\n          // @ts-expect-error error type is not defined\r\n          startEnvironment.error?.error === \"workspace_unavailable\"\r\n        ? \"unavailable\"\r\n        : \"idle\";\r\n\r\n  return (\r\n    <ProjectContext.Provider\r\n      value={{\r\n        projectId,\r\n        isProjectLoading,\r\n        project,\r\n        threads,\r\n        isLoadingThreads,\r\n        hasAccess,\r\n        refetch,\r\n        sandboxState,\r\n      }}\r\n    >\r\n      {children}\r\n    </ProjectContext.Provider>\r\n  );\r\n};\r\n\r\nfunction parseData(data: string): string[] {\r\n  try {\r\n    return JSON.parse(data);\r\n  } catch (error) {\r\n    console.error(\"Failed to parse data:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport const useProject = (): ProjectContextType => {\r\n  const context = useContext(ProjectContext);\r\n  if (!context) {\r\n    throw new Error(\"useProjectContext must be used within a ProjectProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWO,MAAM,eAAe;IAC1B,UAAU;IACV,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;IACT,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,cAAc;IACd,eAAe;IACf,mBAAmB;IACnB,SAAS;IACT,kBAAkB;IAClB,WAAW;IACX,UAAU;AACZ;AAmEA,MAAM,+BAAiB,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAA6B;AAEzD,MAAM,kBAAkB,CAAC,EAC9B,QAAQ,EACR,SAAS,EAIV;;IACC,MAAM,wBAAwB;IAE9B,MAAM,EACJ,MAAM,UAAU,EAChB,WAAW,gBAAgB,EAC3B,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAOT;QACA,UAAU;YAAC;YAAe;SAAU;QACpC,OAAO;wCAAE,IAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE;;QAC1B,KAAK;wCAAE,CAAC,OAAO;gBACb,IAAI,OAAO,QAAQ,SAAS,0CAA0C,OAAO;gBAC7E,OAAO,QAAQ;YACjB;;QACA,sBAAsB;QACtB,oBAAoB;QACpB,GAAG,yIAAA,CAAA,eAAY;QACf,eAAe;wCAAE,CAAC;gBAChB,IACE,MAAM,KAAK,CAAC,IAAI,EAAE,cAAc,KAChC,MAAM,KAAK,CAAC,eAAe,GAAG,uBAC9B;oBACA,OAAO,OAAO;gBAChB;gBAEA,OAAO;YACT;;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,6PAAA,CAAA,eAAY,AAAD;IAE1B,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,kBAAkB;gBACpB,OAAO,KAAK;YACd,OAAO;gBACL,OAAO,IAAI;YACb;YAEA;6CAAO;oBACL,OAAO,IAAI;gBACb;;QACF;oCAAG;QAAC;KAAiB;IAErB,MAAM,UAAU,aACZ;QACE,GAAG,UAAU;QACb,aAAa,UAAU,WAAW,WAAW;QAC7C,mBAAmB,UAAU,WAAW,iBAAiB;QACzD,oBAAoB,UAAU,WAAW,kBAAkB;IAC7D,IACA;IAEJ,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,mBAAgB,AAAD;IAC9C,MAAM,YAAY,mBAAmB;IAErC,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,MAAM,mBAAmB,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD;IACxC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,aAAa,YAAY,YAAY;gBACvC,iBAAiB,MAAM,CAAC,WAAW,UAAU,EAAE;oBAC7C,SAAS;qDAAE;4BACT,UAAU,OAAO;6DAAC;oCAChB,gBAAgB,OAAO,GAAG;gCAC5B;;wBACF;;gBACF;YACF;QACF;oCAAG;QAAC;QAAW,YAAY;KAAW;IAEtC,MAAM,EAAE,MAAM,UAAU,EAAE,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAE;QACnE,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;wCAAE;gBACP,IAAI;oBACF,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE;oBACxC,OAAO,QAAQ,OAAO;gBACxB,EAAE,OAAO,OAAO;oBACd,IAAI,AAAC,OAA8B,WAAW,qCAAqC;wBACjF,OAAO,EAAE;oBACX;oBACA,MAAM;gBACR;YACF;;QACA,SAAS,CAAC,oBAAoB;QAC9B,GAAG,yIAAA,CAAA,eAAY;IACjB;IAEA,IAAI,kBAAkB,OAAO;IAE7B,MAAM,gBAAgB,CAAC,OAAO,QAAQ,SAAS;IAC/C,MAAM,kBAAkB,OAAO,QAAQ,SAAS;IAEhD,IAAI,CAAC,iBAAiB,iBAAiB,OAAO,CAAA,GAAA,oPAAA,CAAA,WAAQ,AAAD;IAErD,uDAAuD;IACvD,MAAM,kBACJ,CAAC,gBAAgB,OAAO,IACxB,SAAS,kBAAkB,aAAa,QAAQ,IAChD,SAAS,kBAAkB,aAAa,SAAS;IAEnD,MAAM,oBACJ,gBAAgB,OAAO,IACvB,CAAC,SAAS,kBAAkB,aAAa,OAAO,IAC9C,SAAS,kBAAkB,aAAa,QAAQ;IAEpD,MAAM,eAAe,kBACjB,aACA,oBACE,aACA,SAAS,kBAAkB,aAAa,KAAK,IAC3C,6CAA6C;IAC7C,iBAAiB,KAAK,EAAE,UAAU,0BAClC,gBACA;IAER,qBACE,4SAAC,eAAe,QAAQ;QACtB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAjJa;;QAcP,8QAAA,CAAA,WAAQ;QA6BG,6PAAA,CAAA,eAAY;QAuBI,2IAAA,CAAA,mBAAgB;QAKtB,oHAAA,CAAA,mBAAgB;QAamB,8QAAA,CAAA,WAAQ;;;KApFzD;AAmJb,SAAS,UAAU,IAAY;IAC7B,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,EAAE;IACX;AACF;AAEO,MAAM,aAAa;;IACxB,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white  hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n        \"outline-primary\":\r\n          \"border border-primary/20 dark:border-primary/15 bg-accent/80 hover:bg-accent/90 text-primary/80 hover:text-primary\",\r\n        success:\r\n          \"border border-emerald-800 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-800\",\r\n        info: \"border border-blue-800 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800\",\r\n        invert:\r\n          \"w-fit dark:bg-[#0a0a0a] dark:hover:bg-[#0a0a0a]/95 dark:text-[#fafafa] bg-[#fafafa] hover:bg-[#fafafa]/95 text-[#0a0a0a]\",\r\n        \"invert-outline-primary\":\r\n          \"border border-background/10 bg-primary/90 hover:bg-primary text-background hover:text-background\",\r\n        transparent: \"bg-transparent hover:bg-transparent text-primary\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-lg px-6 has-[>svg]:px-4\",\r\n        icon: \"size-8\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;YACN,mBACE;YACF,SACE;YACF,MAAM;YACN,QACE;YACF,0BACE;YACF,aAAa;QACf;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\ntype Props = {\r\n  className?: string;\r\n};\r\n\r\nconst Loading = ({ className }: Props) => {\r\n  return (\r\n    <svg\r\n      className={cn(\"size-5 animate-spin text-primary\", className)}\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"none\"\r\n      viewBox=\"0 0 24 24\"\r\n    >\r\n      <circle\r\n        className=\"opacity-25\"\r\n        cx=\"12\"\r\n        cy=\"12\"\r\n        r=\"10\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"4\"\r\n      ></circle>\r\n      <path\r\n        className=\"opacity-75\"\r\n        fill=\"currentColor\"\r\n        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default Loading;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAMA,MAAM,UAAU,CAAC,EAAE,SAAS,EAAS;IACnC,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAClD,OAAM;QACN,MAAK;QACL,SAAQ;;0BAER,4SAAC;gBACC,WAAU;gBACV,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,4SAAC;gBACC,WAAU;gBACV,MAAK;gBACL,GAAE;;;;;;;;;;;;AAIV;KAvBM;uCAyBS", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/global/toast.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { <PERSON>, <PERSON>, <PERSON> } from \"@mynaui/icons-react\";\r\nimport { toast } from \"sonner\";\r\n\r\ntype ToastOptions = {\r\n  description?: string;\r\n  duration?: number;\r\n  position?:\r\n    | \"bottom-right\"\r\n    | \"bottom-left\"\r\n    | \"top-right\"\r\n    | \"top-left\"\r\n    | \"top-center\"\r\n    | \"bottom-center\";\r\n  action?: {\r\n    label: string;\r\n    onClick: () => void;\r\n  };\r\n  button?: React.ReactNode;\r\n};\r\n\r\nconst DEFAULT_DURATION = 3000;\r\nconst DEFAULT_POSITION = \"bottom-right\";\r\n\r\nexport const successToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-[#56eda1]\"\r\n              strokeWidth={1.2}\r\n              aria-hidden=\"true\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0\" />\r\n              <path d=\"m8.667 12.633 1.505 1.721a1 1 0 0 0 1.564-.073L15.333 9.3\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const loadingToast = (\r\n  message: string,\r\n  promise: Promise<unknown>,\r\n  options?: ToastOptions,\r\n  successMessage?: string,\r\n) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  const toastId = Math.random().toString(36).substring(2, 9);\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <Loading className=\"mt-0.5 size-4 animate-spin text-primary\" />\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      id: toastId,\r\n      duration: Infinity,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n\r\n  return promise.then(\r\n    (data) => {\r\n      toast.dismiss(toastId);\r\n      successToast(successMessage || \"Completed\", options);\r\n      return data;\r\n    },\r\n    (error) => {\r\n      toast.dismiss(toastId);\r\n      errorToast(error?.message || \"An error occurred\", options);\r\n      throw error;\r\n    },\r\n  );\r\n};\r\n\r\nexport const errorToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div\r\n        className={cn(\r\n          \"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\",\r\n        )}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-red-500\"\r\n              strokeWidth={1.2}\r\n              aria-hidden=\"true\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0\" />\r\n              <path d=\"m12 8 .01 4\" />\r\n              <path d=\"M12 16h.01\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n              {options?.button && options.button}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const infoToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-blue-500\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"1.5\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0\" />\r\n              <path d=\"M12 16v-5h-.5m0 5h1M12 8.5V8\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-muted-foreground\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-muted-foreground\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const warningToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-yellow-500\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"1.5\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M12 8.5V14m0 3.247v-.5m-6.02-5.985C8.608 5.587 9.92 3 12 3s3.393 2.587 6.02 7.762l.327.644c2.182 4.3 3.274 6.45 2.287 8.022C19.648 21 17.208 21 12.327 21h-.654c-4.88 0-7.321 0-8.307-1.572s.105-3.722 2.287-8.022z\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const copyToast = (message: string = \"Copied to clipboard\", options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  toast(message, {\r\n    description: options?.description,\r\n    duration: options?.duration || DEFAULT_DURATION,\r\n    position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    icon: (\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        width=\"16\"\r\n        height=\"16\"\r\n        fill=\"none\"\r\n        aria-hidden=\"true\"\r\n        viewBox=\"0 0 16 16\"\r\n        className=\"h-5 w-5 font-semibold\"\r\n        strokeWidth={1.2}\r\n      >\r\n        <path\r\n          fill=\"#10B981\"\r\n          d=\"M14.548 3.488a.75.75 0 0 1-.036 1.06l-8.572 8a.75.75 0 0 1-1.023 0l-3.429-3.2a.75.75 0 0 1 1.024-1.096l2.917 2.722 8.06-7.522a.75.75 0 0 1 1.06.036Z\"\r\n        />\r\n      </svg>\r\n    ),\r\n    style: {\r\n      backgroundColor: \"hsl(var(--background))\",\r\n      color: \"hsl(var(--primary))\",\r\n      border: \"2px solid hsl(var(--border) / 0.7)\",\r\n    },\r\n    className:\r\n      \"rounded-2xl border-2 border-border/80 py-2.5  shadow-lg flex items-center gap-2 text-sm font-medium\",\r\n    action: options?.action,\r\n  });\r\n};\r\n\r\nexport const notificationToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  toast(message, {\r\n    description: options?.description,\r\n    duration: options?.duration || DEFAULT_DURATION,\r\n    position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    icon: (\r\n      <Bell\r\n        className={cn(\"h-5 w-5 font-semibold\", options?.description && \"mt-3\")}\r\n        size={16}\r\n        strokeWidth={1.2}\r\n      />\r\n    ),\r\n    style: {\r\n      backgroundColor: \"rgb(88, 28, 135)\",\r\n      color: \"hsl(var(--primary))\",\r\n      border: \"2px solid hsl(var(--border) / 0.7)\",\r\n    },\r\n    className: cn(\r\n      \"rounded-2xl shadow-lg flex items-center gap-3 py-2 text-sm font-medium justify-start\",\r\n      options?.description && \"items-start justify-start\",\r\n    ),\r\n    action: options?.action,\r\n  });\r\n};\r\n\r\nexport const congratsToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  toast(message, {\r\n    description: options?.description,\r\n    duration: options?.duration || DEFAULT_DURATION,\r\n    position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    icon: (\r\n      <Confetti\r\n        className={cn(\"h-5 w-5 font-semibold\", options?.description && \"mt-3\")}\r\n        size={16}\r\n        strokeWidth={1.2}\r\n      />\r\n    ),\r\n    style: {\r\n      backgroundColor: \"#004014\",\r\n      color: \"#56eda1\",\r\n      border: \"none\",\r\n    },\r\n    className: cn(\r\n      \"rounded-2xl py-3 px-4  shadow-lg flex items-center gap-2 text-sm font-medium\",\r\n      options?.description && \"items-start justify-start\",\r\n    ),\r\n    action: options?.action,\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;AAmBA,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AAElB,MAAM,eAAe,CAAC,SAAiB;IAC5C,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,aAAa;gCACb,eAAY;gCACZ,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;;kDAEN,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,eAAe,CAC1B,SACA,SACA,SACA;IAEA,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,MAAM,UAAU,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAExD,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,sIAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,IAAI;QACJ,UAAU;QACV,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;IAGF,OAAO,QAAQ,IAAI,CACjB,CAAC;QACC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,aAAa,kBAAkB,aAAa;QAC5C,OAAO;IACT,GACA,CAAC;QACC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,WAAW,OAAO,WAAW,qBAAqB;QAClD,MAAM;IACR;AAEJ;AAEO,MAAM,aAAa,CAAC,SAAiB;IAC1C,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;sBAGF,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,aAAa;gCACb,eAAY;gCACZ,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;;kDAEN,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;oCAKJ,SAAS,UAAU,QAAQ,MAAM;;;;;;;;;;;;;kCAGtC,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,YAAY,CAAC,SAAiB;IACzC,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;;kDAEN,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAA+C;;;;;;4CAC3D,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,eAAe,CAAC,SAAiB;IAC5C,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;0CAEN,cAAA,4SAAC;oCAAK,GAAE;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,YAAY,CAAC,UAAkB,qBAAqB,EAAE;IACjE,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,SAAS;QACb,aAAa,SAAS;QACtB,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;QACzD,oBACE,4SAAC;YACC,OAAM;YACN,OAAM;YACN,QAAO;YACP,MAAK;YACL,eAAY;YACZ,SAAQ;YACR,WAAU;YACV,aAAa;sBAEb,cAAA,4SAAC;gBACC,MAAK;gBACL,GAAE;;;;;;;;;;;QAIR,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,QAAQ;QACV;QACA,WACE;QACF,QAAQ,SAAS;IACnB;AACF;AAEO,MAAM,oBAAoB,CAAC,SAAiB;IACjD,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,SAAS;QACb,aAAa,SAAS;QACtB,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;QACzD,oBACE,4SAAC,6SAAA,CAAA,OAAI;YACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,SAAS,eAAe;YAC/D,MAAM;YACN,aAAa;;;;;;QAGjB,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,QAAQ;QACV;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA,SAAS,eAAe;QAE1B,QAAQ,SAAS;IACnB;AACF;AAEO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,SAAS;QACb,aAAa,SAAS;QACtB,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;QACzD,oBACE,4SAAC,qTAAA,CAAA,WAAQ;YACP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,SAAS,eAAe;YAC/D,MAAM;YACN,aAAa;;;;;;QAGjB,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,QAAQ;QACV;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,SAAS,eAAe;QAE1B,QAAQ,SAAS;IACnB;AACF", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/current-thread.ts"], "sourcesContent": ["import { debug } from \"@/lib/debug\";\r\nimport { ParsedCommand, ParsedSection, ThreadMessage } from \"@/types/thread-message\";\r\nimport { useEffect } from \"react\";\r\nimport { create } from \"zustand\";\r\nimport { subscribeWithSelector } from \"zustand/middleware\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\n\r\nexport type SelectedSectionAction = {\r\n  section?: ParsedSection | null;\r\n  action?: ParsedCommand | null;\r\n};\r\n\r\ninterface CurrentThread {\r\n  id: number | null;\r\n  threadMessages: ThreadMessage[];\r\n  isAgentRunning: boolean;\r\n  noTokenModalOpen: boolean;\r\n  selectedSectionAction: SelectedSectionAction | null;\r\n  threadName: string;\r\n  isSoftgenProcessing: boolean;\r\n}\r\n\r\ninterface Actions {\r\n  setId: (id: number | null) => void;\r\n  addThreadMessages: (messages: ThreadMessage[]) => void;\r\n  setThreadMessages: (messages: ThreadMessage[]) => void;\r\n  setIsAgentRunning: (isRunning: boolean) => void;\r\n  setNoTokenModalOpen: (open: boolean) => void;\r\n  setSelectedSectionAction: (action: SelectedSectionAction | null) => void;\r\n  setThreadName: (name: string) => void;\r\n  setIsSoftgenProcessing: (isProcessing: boolean) => void;\r\n}\r\n\r\nconst initialState: CurrentThread = {\r\n  id: null,\r\n  threadMessages: [],\r\n  isAgentRunning: false,\r\n  noTokenModalOpen: false,\r\n  selectedSectionAction: null,\r\n  threadName: \"\",\r\n  isSoftgenProcessing: false,\r\n};\r\n\r\nexport const useCurrentThreadStore = create<CurrentThread & Actions>()(\r\n  subscribeWithSelector((set) => ({\r\n    ...initialState,\r\n    setId: (id) => set({ id }),\r\n    addThreadMessages: (messages) =>\r\n      set((state) => ({ threadMessages: [...state.threadMessages, ...messages] })),\r\n    setThreadMessages: (messages) => set({ threadMessages: messages }),\r\n    setIsAgentRunning: (isRunning) => set({ isAgentRunning: isRunning }),\r\n    setNoTokenModalOpen: (open) => set({ noTokenModalOpen: open }),\r\n    setSelectedSectionAction: (action) => set({ selectedSectionAction: action }),\r\n    setThreadName: (name) => set({ threadName: name }),\r\n    setIsSoftgenProcessing: (isProcessing) => set({ isSoftgenProcessing: isProcessing }),\r\n  })),\r\n);\r\n\r\nexport const useCurrentThread = () => {\r\n  const { currentThreadId, setCurrentThreadId } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      currentThreadId: state.id,\r\n      setCurrentThreadId: state.setId,\r\n    })),\r\n  );\r\n\r\n  return {\r\n    currentThreadId,\r\n    setCurrentThreadId,\r\n  };\r\n};\r\n\r\nexport const useCurrentThreadId = () => {\r\n  return useCurrentThreadStore((state) => state.id);\r\n};\r\n\r\nexport const useIsAgentRunning = () => {\r\n  return useCurrentThreadStore((state) => state.isAgentRunning);\r\n};\r\n\r\nexport const useSubscribeToAgentRunning = (\r\n  callback: (isRunning: boolean, prevIsRunning: boolean) => void,\r\n) => {\r\n  useEffect(() => {\r\n    const unsubscribeAgentRunning = useCurrentThreadStore.subscribe(\r\n      (state) => state.isAgentRunning,\r\n      callback,\r\n    );\r\n\r\n    return () => {\r\n      debug(\"Unsubscribing from agent running state\");\r\n      unsubscribeAgentRunning();\r\n    };\r\n  }, []);\r\n};\r\n\r\nexport const useSelectedSectionAction = () => {\r\n  const { selectedSectionAction, setSelectedSectionAction } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      selectedSectionAction: state.selectedSectionAction,\r\n      setSelectedSectionAction: state.setSelectedSectionAction,\r\n    })),\r\n  );\r\n\r\n  return {\r\n    selectedSectionAction,\r\n    setSelectedSectionAction,\r\n  };\r\n};\r\n\r\nexport const useResetCurrentThread = () => {\r\n  const { setId, setThreadMessages } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      setId: state.setId,\r\n      setThreadMessages: state.setThreadMessages,\r\n    })),\r\n  );\r\n\r\n  return {\r\n    resetCurrentThread: () => {\r\n      setId(null);\r\n      setThreadMessages([]);\r\n    },\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;AACA;AACA;AACA;;;;;;;AA4BA,MAAM,eAA8B;IAClC,IAAI;IACJ,gBAAgB,EAAE;IAClB,gBAAgB;IAChB,kBAAkB;IAClB,uBAAuB;IACvB,YAAY;IACZ,qBAAqB;AACvB;AAEO,MAAM,wBAAwB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IACxC,CAAA,GAAA,4PAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,MAAQ,CAAC;QAC9B,GAAG,YAAY;QACf,OAAO,CAAC,KAAO,IAAI;gBAAE;YAAG;QACxB,mBAAmB,CAAC,WAClB,IAAI,CAAC,QAAU,CAAC;oBAAE,gBAAgB;2BAAI,MAAM,cAAc;2BAAK;qBAAS;gBAAC,CAAC;QAC5E,mBAAmB,CAAC,WAAa,IAAI;gBAAE,gBAAgB;YAAS;QAChE,mBAAmB,CAAC,YAAc,IAAI;gBAAE,gBAAgB;YAAU;QAClE,qBAAqB,CAAC,OAAS,IAAI;gBAAE,kBAAkB;YAAK;QAC5D,0BAA0B,CAAC,SAAW,IAAI;gBAAE,uBAAuB;YAAO;QAC1E,eAAe,CAAC,OAAS,IAAI;gBAAE,YAAY;YAAK;QAChD,wBAAwB,CAAC,eAAiB,IAAI;gBAAE,qBAAqB;YAAa;IACpF,CAAC;AAGI,MAAM,mBAAmB;;IAC9B,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,sBAC9C,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;6DAAE,CAAC,QAAU,CAAC;gBACrB,iBAAiB,MAAM,EAAE;gBACzB,oBAAoB,MAAM,KAAK;YACjC,CAAC;;IAGH,OAAO;QACL;QACA;IACF;AACF;GAZa;;QACqC;;;AAa3C,MAAM,qBAAqB;;IAChC,OAAO;oDAAsB,CAAC,QAAU,MAAM,EAAE;;AAClD;IAFa;;QACJ;;;AAGF,MAAM,oBAAoB;;IAC/B,OAAO;mDAAsB,CAAC,QAAU,MAAM,cAAc;;AAC9D;IAFa;;QACJ;;;AAGF,MAAM,6BAA6B,CACxC;;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;gDAAE;YACR,MAAM,0BAA0B,sBAAsB,SAAS;gFAC7D,CAAC,QAAU,MAAM,cAAc;+EAC/B;YAGF;wDAAO;oBACL,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;oBACN;gBACF;;QACF;+CAAG,EAAE;AACP;IAda;AAgBN,MAAM,2BAA2B;;IACtC,MAAM,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,GAAG,sBAC1D,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;qEAAE,CAAC,QAAU,CAAC;gBACrB,uBAAuB,MAAM,qBAAqB;gBAClD,0BAA0B,MAAM,wBAAwB;YAC1D,CAAC;;IAGH,OAAO;QACL;QACA;IACF;AACF;IAZa;;QACiD;;;AAavD,MAAM,wBAAwB;;IACnC,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,sBACnC,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;kEAAE,CAAC,QAAU,CAAC;gBACrB,OAAO,MAAM,KAAK;gBAClB,mBAAmB,MAAM,iBAAiB;YAC5C,CAAC;;IAGH,OAAO;QACL,oBAAoB;YAClB,MAAM;YACN,kBAAkB,EAAE;QACtB;IACF;AACF;IAda;;QAC0B", "debugId": null}}, {"offset": {"line": 1440, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/navigate-file.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\n\r\ntype NavigateFileStore = {\r\n  file: string;\r\n  activeTab: \"preview\" | \"code-editor\" | \"terminal\";\r\n  showDrawer: boolean;\r\n  showFileTree: boolean;\r\n};\r\n\r\ntype Actions = {\r\n  setFile: (file: string, openInEditor?: boolean) => void;\r\n  setActiveTab: (tab: string) => void;\r\n  setShowDrawer: (show: boolean) => void;\r\n  setShowFileTree: (show: boolean) => void;\r\n  reset: () => void;\r\n};\r\n\r\nconst initialState: NavigateFileStore = {\r\n  file: \"\",\r\n  activeTab: \"preview\",\r\n  showDrawer: false,\r\n  showFileTree: true,\r\n};\r\n\r\nexport const useNavigateFile = create<NavigateFileStore & Actions>()((set) => ({\r\n  ...initialState,\r\n  setFile: (file, openInEditor = false) => {\r\n    set({ file });\r\n    if (openInEditor) {\r\n      set({ activeTab: \"code-editor\" });\r\n      set({ showDrawer: true });\r\n    }\r\n  },\r\n  setActiveTab: (tab) => set({ activeTab: tab as \"preview\" | \"code-editor\" | \"terminal\" }),\r\n  setShowDrawer: (show) => set({ showDrawer: show }),\r\n  setShowFileTree: (show) => set({ showFileTree: show }),\r\n  reset: () => set(initialState),\r\n}));\r\n\r\nexport const useSetFile = () => {\r\n  return useNavigateFile((state) => state.setFile);\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAiBA,MAAM,eAAkC;IACtC,MAAM;IACN,WAAW;IACX,YAAY;IACZ,cAAc;AAChB;AAEO,MAAM,kBAAkB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IAAiC,CAAC,MAAQ,CAAC;QAC7E,GAAG,YAAY;QACf,SAAS,CAAC,MAAM,eAAe,KAAK;YAClC,IAAI;gBAAE;YAAK;YACX,IAAI,cAAc;gBAChB,IAAI;oBAAE,WAAW;gBAAc;gBAC/B,IAAI;oBAAE,YAAY;gBAAK;YACzB;QACF;QACA,cAAc,CAAC,MAAQ,IAAI;gBAAE,WAAW;YAA8C;QACtF,eAAe,CAAC,OAAS,IAAI;gBAAE,YAAY;YAAK;QAChD,iBAAiB,CAAC,OAAS,IAAI;gBAAE,cAAc;YAAK;QACpD,OAAO,IAAM,IAAI;IACnB,CAAC;AAEM,MAAM,aAAa;;IACxB,OAAO;sCAAgB,CAAC,QAAU,MAAM,OAAO;;AACjD;GAFa;;QACJ", "debugId": null}}, {"offset": {"line": 1499, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/websocket-manager.ts"], "sourcesContent": ["/**\r\n * WebSocketManager - A WebSocket client with batching and reconnection support\r\n *\r\n * Features:\r\n * - Automatic reconnection with exponential backoff\r\n * - Message batching for high-frequency messages\r\n * - Memory-efficient message handling\r\n * - Type-safe message passing\r\n */\r\n\r\nimport { WebSocketMessage } from \"@/types/thread-message\";\r\nimport { captureException } from \"@sentry/nextjs\";\r\nimport { debug } from \"./debug\";\r\n\r\ntype Subscriber = (message: WebSocketMessage) => void;\r\ntype UnsubscribeFn = () => void;\r\n\r\n// Constants\r\nconst MAX_SUBSCRIBERS = 1000;\r\nconst BATCH_INTERVAL_MS = 16; // ~60fps\r\nconst MAX_RECONNECT_ATTEMPTS = 5;\r\nconst INITIAL_RECONNECT_DELAY = 1000; // 1 second\r\nconst MAX_RECONNECT_DELAY = 30000; // 30 seconds\r\nconst CONNECTION_TIMEOUT = 15000; // 15 seconds\r\n\r\nexport class WebSocketManager {\r\n  // Connection state\r\n  private socket: WebSocket | null = null;\r\n  private connectionUrl: string | null = null;\r\n  private connectionPromise: Promise<WebSocket> | null = null;\r\n  private connectionTimeoutId: NodeJS.Timeout | null = null;\r\n\r\n  // State tracking for recovery\r\n  private connectionState: \"disconnected\" | \"connecting\" | \"connected\" | \"error\" = \"disconnected\";\r\n  private lastError: Error | Event | null = null;\r\n\r\n  // Reconnection state\r\n  private reconnectAttempts = 0;\r\n  private reconnectTimeout: NodeJS.Timeout | null = null;\r\n\r\n  // Batching state\r\n  private messageBatch: WebSocketMessage[] = [];\r\n  private batchScheduled = false;\r\n  private rafId: number | null = null;\r\n  private subscriberCallbacks = new Map<symbol, Subscriber>();\r\n  private messageBuffer = \"\";\r\n\r\n  constructor() {\r\n    // Bind methods once in constructor to avoid creating new function references\r\n    this.handleMessage = this.handleMessage.bind(this);\r\n    this.handleClose = this.handleClose.bind(this);\r\n    this.handleError = this.handleError.bind(this);\r\n    this.processMessageBatch = this.processMessageBatch.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Connect to a WebSocket server\r\n   * @param url WebSocket server URL\r\n   * @returns Promise that resolves when connected\r\n   */\r\n  public async connect(url: string): Promise<WebSocket> {\r\n    // Return existing connection or promise if available\r\n    if (this.connectionPromise) return this.connectionPromise;\r\n    if (this.socket?.readyState === WebSocket.OPEN) return Promise.resolve(this.socket);\r\n\r\n    this.connectionUrl = url;\r\n    this.connectionState = \"connecting\";\r\n    this.lastError = null;\r\n    debug(`[WebSocket] Connecting to ${url}...`);\r\n\r\n    // Set connection timeout\r\n    if (this.connectionTimeoutId) clearTimeout(this.connectionTimeoutId);\r\n\r\n    this.connectionPromise = new Promise((resolve, reject) => {\r\n      try {\r\n        this.socket = new WebSocket(url);\r\n\r\n        // Set connection timeout\r\n        this.connectionTimeoutId = setTimeout(() => {\r\n          if (this.socket?.readyState !== WebSocket.OPEN) {\r\n            console.error(\"[WebSocket] Connection timeout\");\r\n            this.lastError = new Error(\"Connection timeout\");\r\n            this.connectionState = \"error\";\r\n            this.cleanup();\r\n            reject(new Error(\"Connection timeout\"));\r\n          }\r\n        }, CONNECTION_TIMEOUT);\r\n\r\n        // Set up event handlers\r\n        this.socket.onopen = () => {\r\n          debug(`[WebSocket] Connected to ${url}`);\r\n          this.reconnectAttempts = 0;\r\n          this.connectionState = \"connected\";\r\n          if (this.connectionTimeoutId) {\r\n            clearTimeout(this.connectionTimeoutId);\r\n            this.connectionTimeoutId = null;\r\n          }\r\n          if (this.socket) resolve(this.socket);\r\n\r\n          // Notify subscribers about connection established\r\n          this.addToBatch({\r\n            type: \"connection_status\",\r\n            data: { status: \"connected\" },\r\n            timestamp: Date.now(),\r\n          });\r\n        };\r\n\r\n        this.socket.onmessage = this.handleMessage;\r\n        this.socket.onclose = this.handleClose;\r\n        this.socket.onerror = this.handleError;\r\n      } catch (error) {\r\n        captureException(error);\r\n        this.connectionPromise = null;\r\n        this.connectionState = \"error\";\r\n        this.lastError = error as Error;\r\n        reject(error);\r\n      }\r\n    });\r\n\r\n    return this.connectionPromise;\r\n  }\r\n\r\n  /**\r\n   * Disconnect from the WebSocket server\r\n   * @param code Close code (default: 1000)\r\n   * @param reason Close reason (default: \"Normal closure\")\r\n   */\r\n  public disconnect(code = 1000, reason = \"Normal closure\"): void {\r\n    // Clear reconnect timeout if exists\r\n    if (this.reconnectTimeout) {\r\n      clearTimeout(this.reconnectTimeout);\r\n      this.reconnectTimeout = null;\r\n    }\r\n\r\n    // Close socket if exists\r\n    if (this.socket) {\r\n      this.socket.onclose = null; // Prevent reconnection\r\n      this.socket.close(code, reason);\r\n      this.cleanup();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Send a message to the WebSocket server\r\n   * @param message Message to send (will be stringified if not a string)\r\n   * @returns boolean indicating if the message was sent successfully\r\n   */\r\n  public send(message: string | Record<string, unknown>): boolean {\r\n    if (this.socket?.readyState === WebSocket.OPEN) {\r\n      try {\r\n        const messageString = typeof message === \"string\" ? message : JSON.stringify(message);\r\n\r\n        this.socket.send(messageString);\r\n        return true;\r\n      } catch (error) {\r\n        captureException(error);\r\n        console.error(\"[WebSocket] Error sending message:\", error);\r\n        return false;\r\n      }\r\n    } else {\r\n      console.warn(\r\n        `[WebSocket] Cannot send message - socket not connected (state: ${this.connectionState})`,\r\n      );\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if the WebSocket is connected\r\n   * @returns boolean indicating connection status\r\n   */\r\n  public isConnected(): boolean {\r\n    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;\r\n  }\r\n\r\n  /**\r\n   * Subscribe to WebSocket messages\r\n   * @param callback Function to call when messages are received\r\n   * @returns Unsubscribe function\r\n   */\r\n  public subscribe(callback: Subscriber): UnsubscribeFn {\r\n    // Check subscriber limit\r\n    if (this.subscriberCallbacks.size >= MAX_SUBSCRIBERS) {\r\n      console.warn(\"[WebSocket] Maximum number of subscribers reached\");\r\n      return () => {};\r\n    }\r\n\r\n    // Create unique symbol as ID\r\n    const id = Symbol();\r\n    this.subscriberCallbacks.set(id, callback);\r\n\r\n    // Return unsubscribe function\r\n    return () => this.subscriberCallbacks.delete(id);\r\n  }\r\n\r\n  /**\r\n   * Handle incoming WebSocket messages\r\n   */\r\n  private handleMessage(event: MessageEvent): void {\r\n    try {\r\n      // Only handle string messages\r\n      let data = event.data;\r\n\r\n      if (typeof data !== \"string\") {\r\n        console.warn(\"[WebSocket] Received non-string message, ignoring\");\r\n        return;\r\n      }\r\n\r\n      // Try to parse directly - the most reliable way to check if JSON is complete\r\n      try {\r\n        const message: WebSocketMessage = JSON.parse(data);\r\n\r\n        // Handle ping messages\r\n        if (message.type === \"ping\") {\r\n          console.debug(\"[WebSocket] Received ping, sending pong\");\r\n          this.send({ type: \"pong\" });\r\n          return;\r\n        }\r\n\r\n        this.addToBatch(message);\r\n        return;\r\n      } catch {\r\n        // Not complete or valid JSON, continue to buffer handling\r\n      }\r\n\r\n      // Check for obvious partial messages\r\n      const firstChar = data.charAt(0);\r\n      const lastChar = data.charAt(data.length - 1);\r\n      const isStartOfJson = firstChar === \"{\" || firstChar === \"[\";\r\n      const isEndOfJson = lastChar === \"}\" || lastChar === \"]\";\r\n\r\n      // Add to buffer if it looks like the start of a JSON object/array but not the end\r\n      if (isStartOfJson && !isEndOfJson) {\r\n        this.messageBuffer += data;\r\n        return;\r\n      }\r\n\r\n      // Add to buffer if we already have buffered content\r\n      if (this.messageBuffer) {\r\n        // If this chunk looks like the end of JSON, try parsing the combined content\r\n        data = this.messageBuffer + data;\r\n\r\n        // Try to parse the combined data\r\n        try {\r\n          const message = JSON.parse(data) as WebSocketMessage;\r\n          this.addToBatch(message);\r\n          this.messageBuffer = \"\"; // Reset buffer after successful parse\r\n        } catch (error) {\r\n          // Still not complete JSON\r\n          if (isEndOfJson) {\r\n            // If it ends with a brace/bracket but still can't parse, it might be malformed\r\n            console.error(\"[WebSocket] Error parsing combined JSON:\", error);\r\n            this.messageBuffer = \"\"; // Reset buffer to avoid accumulating invalid data\r\n          } else {\r\n            // Otherwise keep accumulating data\r\n            this.messageBuffer = data;\r\n          }\r\n        }\r\n        return;\r\n      }\r\n\r\n      // If we get here, we have a standalone chunk that didn't parse as JSON\r\n      // Try one more time with explicit error handling\r\n      try {\r\n        const message = JSON.parse(data) as WebSocketMessage;\r\n        this.addToBatch(message);\r\n      } catch (error) {\r\n        console.error(\"[WebSocket] Error parsing JSON:\", error);\r\n        // Don't buffer data that doesn't look like JSON\r\n        if (isStartOfJson) {\r\n          this.messageBuffer = data; // Only buffer if it looks like the start of JSON\r\n        }\r\n      }\r\n    } catch (error) {\r\n      captureException(error);\r\n      console.error(\"[WebSocket] Error in handleMessage:\", error);\r\n      this.messageBuffer = \"\"; // Reset buffer on error\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add a message to the current batch and schedule processing\r\n   */\r\n  private addToBatch(message: WebSocketMessage): void {\r\n    this.messageBatch.push(message);\r\n\r\n    // Schedule batch processing if not already scheduled\r\n    if (!this.batchScheduled) {\r\n      this.batchScheduled = true;\r\n\r\n      // Use requestAnimationFrame for synchronizing with UI updates\r\n      if (typeof window !== \"undefined\" && \"requestAnimationFrame\" in window) {\r\n        this.rafId = window.requestAnimationFrame(this.processMessageBatch);\r\n      } else {\r\n        // Fallback for environments without requestAnimationFrame\r\n        setTimeout(this.processMessageBatch, BATCH_INTERVAL_MS);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process all messages in the current batch\r\n   */\r\n  private processMessageBatch(): void {\r\n    this.batchScheduled = false;\r\n    this.rafId = null;\r\n\r\n    if (!this.messageBatch.length) return;\r\n\r\n    // Get current batch and reset for next batch\r\n    const batch = this.messageBatch;\r\n    this.messageBatch = [];\r\n\r\n    // Create a single batch message if needed (only once)\r\n    const batchMessage =\r\n      batch.length > 1\r\n        ? {\r\n            type: \"batch\",\r\n            batch,\r\n            timestamp: Date.now(),\r\n          }\r\n        : null;\r\n\r\n    // Notify subscribers with batched messages\r\n    this.subscriberCallbacks.forEach((callback, id) => {\r\n      try {\r\n        if (batch.length === 1) {\r\n          // Single message - send as is\r\n          callback(batch[0]);\r\n        } else {\r\n          // Multiple messages - send as batch\r\n          callback(batchMessage!);\r\n        }\r\n      } catch (error) {\r\n        captureException(error);\r\n        console.error(\"[WebSocket] Error in subscriber:\", error);\r\n        this.subscriberCallbacks.delete(id);\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleClose(event: CloseEvent): void {\r\n    debug(`[WebSocket] Connection closed: ${event.code} ${event.reason}`);\r\n    this.connectionState = \"disconnected\";\r\n    this.cleanup();\r\n\r\n    // Notify subscribers about disconnection\r\n    this.addToBatch({\r\n      type: \"connection_status\",\r\n      data: {\r\n        status: \"disconnected\",\r\n        code: event.code,\r\n        reason: event.reason,\r\n      },\r\n      timestamp: Date.now(),\r\n    });\r\n\r\n    // Don't reconnect on normal closure (code 1000)\r\n    if (event.code !== 1000) {\r\n      this.attemptReconnect();\r\n    }\r\n  }\r\n\r\n  private handleError(error: Event): void {\r\n    console.error(\"[WebSocket] Error:\", error);\r\n    this.connectionState = \"error\";\r\n    this.lastError = error;\r\n    this.cleanup();\r\n\r\n    // Notify subscribers about error\r\n    this.addToBatch({\r\n      type: \"connection_status\",\r\n      data: {\r\n        status: \"error\",\r\n        message: error.toString(),\r\n      },\r\n      timestamp: Date.now(),\r\n    });\r\n\r\n    this.attemptReconnect();\r\n  }\r\n\r\n  private attemptReconnect(): void {\r\n    // Check if we should stop reconnecting\r\n    if (this.reconnectAttempts >= MAX_RECONNECT_ATTEMPTS || !this.connectionUrl) {\r\n      console.warn(\"[WebSocket] Max reconnection attempts reached\");\r\n      return;\r\n    }\r\n\r\n    // Calculate exponential backoff delay\r\n    this.reconnectAttempts++;\r\n    const delay = Math.min(\r\n      INITIAL_RECONNECT_DELAY * Math.pow(2, this.reconnectAttempts - 1),\r\n      MAX_RECONNECT_DELAY,\r\n    );\r\n\r\n    debug(\r\n      `[WebSocket] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`,\r\n    );\r\n\r\n    // Schedule reconnection\r\n    this.reconnectTimeout = setTimeout(() => {\r\n      if (this.connectionUrl) {\r\n        this.connect(this.connectionUrl).catch((error) => {\r\n          console.error(\"[WebSocket] Reconnection failed:\", error);\r\n          this.attemptReconnect();\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  private cleanup(): void {\r\n    // Clear scheduled batch processing\r\n    if (this.rafId !== null && typeof window !== \"undefined\" && \"cancelAnimationFrame\" in window) {\r\n      window.cancelAnimationFrame(this.rafId);\r\n      this.rafId = null;\r\n    }\r\n    this.batchScheduled = false;\r\n\r\n    if (this.reconnectTimeout) {\r\n      clearTimeout(this.reconnectTimeout);\r\n      this.reconnectTimeout = null;\r\n    }\r\n\r\n    if (this.connectionTimeoutId) {\r\n      clearTimeout(this.connectionTimeoutId);\r\n      this.connectionTimeoutId = null;\r\n    }\r\n\r\n    // Clean up WebSocket\r\n    if (this.socket) {\r\n      // Remove event handlers\r\n      this.socket.onopen = null;\r\n      this.socket.onclose = null;\r\n      this.socket.onerror = null;\r\n      this.socket.onmessage = null;\r\n\r\n      // Close if still open\r\n      if (this.socket.readyState === WebSocket.OPEN) {\r\n        this.socket.close(1000, \"Cleanup\");\r\n      }\r\n\r\n      this.socket = null;\r\n    }\r\n\r\n    // Reset state\r\n    this.connectionPromise = null;\r\n    this.messageBatch = [];\r\n    this.messageBuffer = \"\";\r\n  }\r\n}\r\n\r\n// Create a singleton instance\r\nexport const webSocketManager = new WebSocketManager();\r\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAGD;AACA;;;AAKA,YAAY;AACZ,MAAM,kBAAkB;AACxB,MAAM,oBAAoB,IAAI,SAAS;AACvC,MAAM,yBAAyB;AAC/B,MAAM,0BAA0B,MAAM,WAAW;AACjD,MAAM,sBAAsB,OAAO,aAAa;AAChD,MAAM,qBAAqB,OAAO,aAAa;AAExC,MAAM;IACX,mBAAmB;IACX,SAA2B,KAAK;IAChC,gBAA+B,KAAK;IACpC,oBAA+C,KAAK;IACpD,sBAA6C,KAAK;IAE1D,8BAA8B;IACtB,kBAAyE,eAAe;IACxF,YAAkC,KAAK;IAE/C,qBAAqB;IACb,oBAAoB,EAAE;IACtB,mBAA0C,KAAK;IAEvD,iBAAiB;IACT,eAAmC,EAAE,CAAC;IACtC,iBAAiB,MAAM;IACvB,QAAuB,KAAK;IAC5B,sBAAsB,IAAI,MAA0B;IACpD,gBAAgB,GAAG;IAE3B,aAAc;QACZ,6EAA6E;QAC7E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;IAC/D;IAEA;;;;GAIC,GACD,MAAa,QAAQ,GAAW,EAAsB;QACpD,qDAAqD;QACrD,IAAI,IAAI,CAAC,iBAAiB,EAAE,OAAO,IAAI,CAAC,iBAAiB;QACzD,IAAI,IAAI,CAAC,MAAM,EAAE,eAAe,UAAU,IAAI,EAAE,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM;QAElF,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,SAAS,GAAG;QACjB,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,0BAA0B,EAAE,IAAI,GAAG,CAAC;QAE3C,yBAAyB;QACzB,IAAI,IAAI,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,mBAAmB;QAEnE,IAAI,CAAC,iBAAiB,GAAG,IAAI,QAAQ,CAAC,SAAS;YAC7C,IAAI;gBACF,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU;gBAE5B,yBAAyB;gBACzB,IAAI,CAAC,mBAAmB,GAAG,WAAW;oBACpC,IAAI,IAAI,CAAC,MAAM,EAAE,eAAe,UAAU,IAAI,EAAE;wBAC9C,QAAQ,KAAK,CAAC;wBACd,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM;wBAC3B,IAAI,CAAC,eAAe,GAAG;wBACvB,IAAI,CAAC,OAAO;wBACZ,OAAO,IAAI,MAAM;oBACnB;gBACF,GAAG;gBAEH,wBAAwB;gBACxB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;oBACnB,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,yBAAyB,EAAE,KAAK;oBACvC,IAAI,CAAC,iBAAiB,GAAG;oBACzB,IAAI,CAAC,eAAe,GAAG;oBACvB,IAAI,IAAI,CAAC,mBAAmB,EAAE;wBAC5B,aAAa,IAAI,CAAC,mBAAmB;wBACrC,IAAI,CAAC,mBAAmB,GAAG;oBAC7B;oBACA,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,MAAM;oBAEpC,kDAAkD;oBAClD,IAAI,CAAC,UAAU,CAAC;wBACd,MAAM;wBACN,MAAM;4BAAE,QAAQ;wBAAY;wBAC5B,WAAW,KAAK,GAAG;oBACrB;gBACF;gBAEA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa;gBAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;gBACtC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;YACxC,EAAE,OAAO,OAAO;gBACd,CAAA,GAAA,4NAAA,CAAA,mBAAgB,AAAD,EAAE;gBACjB,IAAI,CAAC,iBAAiB,GAAG;gBACzB,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,CAAC,SAAS,GAAG;gBACjB,OAAO;YACT;QACF;QAEA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;;;GAIC,GACD,AAAO,WAAW,OAAO,IAAI,EAAE,SAAS,gBAAgB,EAAQ;QAC9D,oCAAoC;QACpC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,aAAa,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QAEA,yBAAyB;QACzB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,MAAM,uBAAuB;YACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;YACxB,IAAI,CAAC,OAAO;QACd;IACF;IAEA;;;;GAIC,GACD,AAAO,KAAK,OAAyC,EAAW;QAC9D,IAAI,IAAI,CAAC,MAAM,EAAE,eAAe,UAAU,IAAI,EAAE;YAC9C,IAAI;gBACF,MAAM,gBAAgB,OAAO,YAAY,WAAW,UAAU,KAAK,SAAS,CAAC;gBAE7E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACjB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,CAAA,GAAA,4NAAA,CAAA,mBAAgB,AAAD,EAAE;gBACjB,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO;YACT;QACF,OAAO;YACL,QAAQ,IAAI,CACV,CAAC,+DAA+D,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAE3F,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,AAAO,cAAuB;QAC5B,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI;IAC1E;IAEA;;;;GAIC,GACD,AAAO,UAAU,QAAoB,EAAiB;QACpD,yBAAyB;QACzB,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,IAAI,iBAAiB;YACpD,QAAQ,IAAI,CAAC;YACb,OAAO,KAAO;QAChB;QAEA,6BAA6B;QAC7B,MAAM,KAAK;QACX,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI;QAEjC,8BAA8B;QAC9B,OAAO,IAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;IAC/C;IAEA;;GAEC,GACD,AAAQ,cAAc,KAAmB,EAAQ;QAC/C,IAAI;YACF,8BAA8B;YAC9B,IAAI,OAAO,MAAM,IAAI;YAErB,IAAI,OAAO,SAAS,UAAU;gBAC5B,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,6EAA6E;YAC7E,IAAI;gBACF,MAAM,UAA4B,KAAK,KAAK,CAAC;gBAE7C,uBAAuB;gBACvB,IAAI,QAAQ,IAAI,KAAK,QAAQ;oBAC3B,QAAQ,KAAK,CAAC;oBACd,IAAI,CAAC,IAAI,CAAC;wBAAE,MAAM;oBAAO;oBACzB;gBACF;gBAEA,IAAI,CAAC,UAAU,CAAC;gBAChB;YACF,EAAE,OAAM;YACN,0DAA0D;YAC5D;YAEA,qCAAqC;YACrC,MAAM,YAAY,KAAK,MAAM,CAAC;YAC9B,MAAM,WAAW,KAAK,MAAM,CAAC,KAAK,MAAM,GAAG;YAC3C,MAAM,gBAAgB,cAAc,OAAO,cAAc;YACzD,MAAM,cAAc,aAAa,OAAO,aAAa;YAErD,kFAAkF;YAClF,IAAI,iBAAiB,CAAC,aAAa;gBACjC,IAAI,CAAC,aAAa,IAAI;gBACtB;YACF;YAEA,oDAAoD;YACpD,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,6EAA6E;gBAC7E,OAAO,IAAI,CAAC,aAAa,GAAG;gBAE5B,iCAAiC;gBACjC,IAAI;oBACF,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,IAAI,CAAC,UAAU,CAAC;oBAChB,IAAI,CAAC,aAAa,GAAG,IAAI,sCAAsC;gBACjE,EAAE,OAAO,OAAO;oBACd,0BAA0B;oBAC1B,IAAI,aAAa;wBACf,+EAA+E;wBAC/E,QAAQ,KAAK,CAAC,4CAA4C;wBAC1D,IAAI,CAAC,aAAa,GAAG,IAAI,kDAAkD;oBAC7E,OAAO;wBACL,mCAAmC;wBACnC,IAAI,CAAC,aAAa,GAAG;oBACvB;gBACF;gBACA;YACF;YAEA,uEAAuE;YACvE,iDAAiD;YACjD,IAAI;gBACF,MAAM,UAAU,KAAK,KAAK,CAAC;gBAC3B,IAAI,CAAC,UAAU,CAAC;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,gDAAgD;gBAChD,IAAI,eAAe;oBACjB,IAAI,CAAC,aAAa,GAAG,MAAM,iDAAiD;gBAC9E;YACF;QACF,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,4NAAA,CAAA,mBAAgB,AAAD,EAAE;YACjB,QAAQ,KAAK,CAAC,uCAAuC;YACrD,IAAI,CAAC,aAAa,GAAG,IAAI,wBAAwB;QACnD;IACF;IAEA;;GAEC,GACD,AAAQ,WAAW,OAAyB,EAAQ;QAClD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QAEvB,qDAAqD;QACrD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG;YAEtB,8DAA8D;YAC9D,IAAI,aAAkB,eAAe,2BAA2B,QAAQ;gBACtE,IAAI,CAAC,KAAK,GAAG,OAAO,qBAAqB,CAAC,IAAI,CAAC,mBAAmB;YACpE,OAAO;gBACL,0DAA0D;gBAC1D,WAAW,IAAI,CAAC,mBAAmB,EAAE;YACvC;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,GAAG;QAEb,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;QAE/B,6CAA6C;QAC7C,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,IAAI,CAAC,YAAY,GAAG,EAAE;QAEtB,sDAAsD;QACtD,MAAM,eACJ,MAAM,MAAM,GAAG,IACX;YACE,MAAM;YACN;YACA,WAAW,KAAK,GAAG;QACrB,IACA;QAEN,2CAA2C;QAC3C,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,UAAU;YAC1C,IAAI;gBACF,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,8BAA8B;oBAC9B,SAAS,KAAK,CAAC,EAAE;gBACnB,OAAO;oBACL,oCAAoC;oBACpC,SAAS;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,CAAA,GAAA,4NAAA,CAAA,mBAAgB,AAAD,EAAE;gBACjB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAClC;QACF;IACF;IAEQ,YAAY,KAAiB,EAAQ;QAC3C,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,+BAA+B,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QACpE,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,OAAO;QAEZ,yCAAyC;QACzC,IAAI,CAAC,UAAU,CAAC;YACd,MAAM;YACN,MAAM;gBACJ,QAAQ;gBACR,MAAM,MAAM,IAAI;gBAChB,QAAQ,MAAM,MAAM;YACtB;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,gDAAgD;QAChD,IAAI,MAAM,IAAI,KAAK,MAAM;YACvB,IAAI,CAAC,gBAAgB;QACvB;IACF;IAEQ,YAAY,KAAY,EAAQ;QACtC,QAAQ,KAAK,CAAC,sBAAsB;QACpC,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO;QAEZ,iCAAiC;QACjC,IAAI,CAAC,UAAU,CAAC;YACd,MAAM;YACN,MAAM;gBACJ,QAAQ;gBACR,SAAS,MAAM,QAAQ;YACzB;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,CAAC,gBAAgB;IACvB;IAEQ,mBAAyB;QAC/B,uCAAuC;QACvC,IAAI,IAAI,CAAC,iBAAiB,IAAI,0BAA0B,CAAC,IAAI,CAAC,aAAa,EAAE;YAC3E,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,sCAAsC;QACtC,IAAI,CAAC,iBAAiB;QACtB,MAAM,QAAQ,KAAK,GAAG,CACpB,0BAA0B,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAC/D;QAGF,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EACF,CAAC,4BAA4B,EAAE,MAAM,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC;QAGxG,wBAAwB;QACxB,IAAI,CAAC,gBAAgB,GAAG,WAAW;YACjC,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;oBACtC,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,IAAI,CAAC,gBAAgB;gBACvB;YACF;QACF,GAAG;IACL;IAEQ,UAAgB;QACtB,mCAAmC;QACnC,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,aAAkB,eAAe,0BAA0B,QAAQ;YAC5F,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK;YACtC,IAAI,CAAC,KAAK,GAAG;QACf;QACA,IAAI,CAAC,cAAc,GAAG;QAEtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,aAAa,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QAEA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,aAAa,IAAI,CAAC,mBAAmB;YACrC,IAAI,CAAC,mBAAmB,GAAG;QAC7B;QAEA,qBAAqB;QACrB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,wBAAwB;YACxB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;YACrB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG;YACtB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG;YACtB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG;YAExB,sBAAsB;YACtB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;gBAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;YAC1B;YAEA,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,cAAc;QACd,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,aAAa,GAAG;IACvB;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/thread-provider/hooks.ts"], "sourcesContent": ["import { debug } from \"@/lib/debug\";\r\nimport { webSocketManager } from \"@/lib/websocket-manager\";\r\nimport { WebSocketMessage } from \"@/types/thread-message\";\r\nimport { useCallback, useEffect, useMemo, useRef } from \"react\";\r\nimport { ErrorMessage, NewMessage, SessionStatusMessage } from \"./types\";\r\n\r\ntype WebSocketMessageHandler<T = WebSocketMessage> = (message: T) => void;\r\n\r\ntype Props = {\r\n  onMessage?: WebSocketMessageHandler;\r\n  onComplete?: (content: string) => void;\r\n  setIsAgentRunning: (isRunning: boolean) => void;\r\n  onError?: (message: WebSocketMessage) => void;\r\n  onErrorMessage?: (message: ErrorMessage) => void;\r\n  onSoftgenProcessing?: (isProcessing: boolean) => void;\r\n  setContextLimitReached?: (isLimitReached: boolean) => void;\r\n};\r\n\r\nexport const useWebSocketManager = (callbacks: Props) => {\r\n  const connectedUrlRef = useRef<string | null>(null);\r\n  const messageHandlerRef = useRef<WebSocketMessageHandler | null>(null);\r\n  const streamingContentRef = useRef<string>(\"\");\r\n  const isMountedRef = useRef(true);\r\n\r\n  /**\r\n   * Store callbacks in a ref to maintain stable references across renders.\r\n   * This allows us to:\r\n   * 1. Avoid including callbacks in dependency arrays of useCallback/useEffect\r\n   * 2. Always have access to the latest callback implementations\r\n   * 3. Prevent unnecessary effect re-runs when parent components re-render\r\n   */\r\n  const callbacksRef = useRef(callbacks);\r\n\r\n  // Update the ref value whenever callbacks change\r\n  useEffect(() => {\r\n    callbacksRef.current = callbacks;\r\n  }, [callbacks]);\r\n\r\n  // Memoize the message handler to prevent unnecessary re-renders\r\n  const handleSingleMessage = useCallback((message: WebSocketMessage) => {\r\n    if (!isMountedRef.current) return;\r\n\r\n    // Handle connection status messages\r\n    if (message.type === \"connection_status\") {\r\n      const statusData = message.data as Record<string, unknown>;\r\n      const status = statusData?.status as string;\r\n      console.debug(`[WebSocket] Connection status changed to: ${status}`);\r\n\r\n      if (status === \"error\") {\r\n        // Notify about connection issues\r\n        callbacksRef.current?.onError?.(message);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (\r\n      message.type === \"error\" &&\r\n      message.isCompleteMessage &&\r\n      message.termination &&\r\n      typeof message.data === \"string\" &&\r\n      message.data.includes(\"Context window is full\")\r\n    ) {\r\n      callbacksRef.current?.setContextLimitReached?.(true);\r\n      callbacksRef.current?.onErrorMessage?.(message as ErrorMessage);\r\n      callbacksRef.current?.setIsAgentRunning(false);\r\n      return;\r\n    }\r\n\r\n    switch (message.type) {\r\n      case \"content\":\r\n        if (typeof message.data === \"string\") {\r\n          if (message.isCompleteMessage) {\r\n            callbacksRef.current?.onComplete?.(streamingContentRef.current);\r\n            streamingContentRef.current = \"\";\r\n            callbacks.onSoftgenProcessing?.(true);\r\n          } else {\r\n            streamingContentRef.current = message.data;\r\n          }\r\n        }\r\n        break;\r\n\r\n      case \"session_status\": {\r\n        const { status } = message as SessionStatusMessage;\r\n        debug(`Session status changed to: ${status}`);\r\n        callbacksRef.current?.setIsAgentRunning(status === \"running\");\r\n        break;\r\n      }\r\n\r\n      case \"stop_session\":\r\n        debug(\"Session stopped\");\r\n        callbacksRef.current?.setIsAgentRunning(false);\r\n        break;\r\n\r\n      case \"error\": {\r\n        debug(\"Session error\");\r\n        callbacksRef.current?.onErrorMessage?.(message as ErrorMessage);\r\n        break;\r\n      }\r\n\r\n      default:\r\n        callbacksRef.current?.onMessage?.(message);\r\n    }\r\n  }, []);\r\n\r\n  // Handle both batched and single messages\r\n  const handleMessage = useCallback(\r\n    (message: WebSocketMessage) => {\r\n      if (!isMountedRef.current) return;\r\n\r\n      if (message.type === \"batch\" && message.batch) {\r\n        message.batch.forEach(handleSingleMessage);\r\n      } else {\r\n        handleSingleMessage(message);\r\n      }\r\n    },\r\n    [handleSingleMessage],\r\n  );\r\n\r\n  useEffect(() => {\r\n    isMountedRef.current = true;\r\n    messageHandlerRef.current = handleMessage;\r\n    const unsubscribe = webSocketManager.subscribe(handleMessage);\r\n    const currentUrl = connectedUrlRef.current;\r\n\r\n    return () => {\r\n      isMountedRef.current = false;\r\n\r\n      console.debug(\"Cleaning up WebSocket message handler\");\r\n      unsubscribe();\r\n      messageHandlerRef.current = null;\r\n      streamingContentRef.current = \"\";\r\n      if (currentUrl && webSocketManager.isConnected()) {\r\n        console.debug(\"Disconnecting WebSocket on unmount\");\r\n        webSocketManager.disconnect(1000, \"Component unmounted\");\r\n        connectedUrlRef.current = null;\r\n      }\r\n    };\r\n  }, [handleMessage]);\r\n\r\n  const connect = useCallback(async (url: string): Promise<boolean> => {\r\n    try {\r\n      if (webSocketManager.isConnected() && connectedUrlRef.current === url) {\r\n        return true;\r\n      }\r\n\r\n      // Disconnect from previous connection if exists\r\n      if (connectedUrlRef.current) {\r\n        await webSocketManager.disconnect(1000, \"Reconnecting\");\r\n      }\r\n\r\n      // Reset state before connecting\r\n      streamingContentRef.current = \"\";\r\n      await webSocketManager.connect(url);\r\n      connectedUrlRef.current = url;\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"WebSocket connection failed:\", error);\r\n      connectedUrlRef.current = null;\r\n      return false;\r\n    }\r\n  }, []);\r\n\r\n  const disconnect = useCallback((code = 1000, reason = \"Normal closure\"): void => {\r\n    console.debug(\"Disconnecting WebSocket...\");\r\n    webSocketManager.disconnect(code, reason);\r\n\r\n    if (isMountedRef.current) {\r\n      callbacksRef.current?.setIsAgentRunning(false);\r\n      streamingContentRef.current = \"\"; // Clear streaming content\r\n      connectedUrlRef.current = null;\r\n    }\r\n  }, []);\r\n\r\n  const send = useCallback((message: NewMessage): void => {\r\n    if (!isMountedRef.current || !webSocketManager.isConnected()) {\r\n      console.warn(\"Cannot send message: WebSocket not connected\");\r\n      return;\r\n    }\r\n    webSocketManager.send(message);\r\n  }, []);\r\n\r\n  return useMemo(\r\n    () => ({\r\n      connect,\r\n      disconnect,\r\n      send,\r\n      isConnected: webSocketManager.isConnected(),\r\n    }),\r\n    [connect, disconnect, send],\r\n  );\r\n};\r\n\r\nexport function useWebSocketMessage<T = WebSocketMessage>(\r\n  messageType: string,\r\n  callback: WebSocketMessageHandler<T>,\r\n) {\r\n  const callbackRef = useRef(callback);\r\n\r\n  useEffect(() => {\r\n    callbackRef.current = callback;\r\n  }, [callback]);\r\n\r\n  useEffect(() => {\r\n    const handleMessage = (message: WebSocketMessage) => {\r\n      if (message.type === messageType) {\r\n        callbackRef.current(message as T);\r\n      } else if (message.type === \"batch\" && message.batch) {\r\n        // Handle batched messages\r\n        message.batch\r\n          .filter((m) => m.type === messageType)\r\n          .forEach((m) => callbackRef.current(m as T));\r\n      }\r\n    };\r\n\r\n    const unsubscribe = webSocketManager.subscribe(handleMessage);\r\n    return () => unsubscribe();\r\n  }, [messageType]);\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAeO,MAAM,sBAAsB,CAAC;;IAClC,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAiB;IAC9C,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkC;IACjE,MAAM,sBAAsB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAU;IAC3C,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAE5B;;;;;;GAMC,GACD,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,iDAAiD;IACjD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;yCAAE;YACR,aAAa,OAAO,GAAG;QACzB;wCAAG;QAAC;KAAU;IAEd,gEAAgE;IAChE,MAAM,sBAAsB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;gEAAE,CAAC;YACvC,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,oCAAoC;YACpC,IAAI,QAAQ,IAAI,KAAK,qBAAqB;gBACxC,MAAM,aAAa,QAAQ,IAAI;gBAC/B,MAAM,SAAS,YAAY;gBAC3B,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,QAAQ;gBAEnE,IAAI,WAAW,SAAS;oBACtB,iCAAiC;oBACjC,aAAa,OAAO,EAAE,UAAU;gBAClC;gBACA;YACF;YAEA,IACE,QAAQ,IAAI,KAAK,WACjB,QAAQ,iBAAiB,IACzB,QAAQ,WAAW,IACnB,OAAO,QAAQ,IAAI,KAAK,YACxB,QAAQ,IAAI,CAAC,QAAQ,CAAC,2BACtB;gBACA,aAAa,OAAO,EAAE,yBAAyB;gBAC/C,aAAa,OAAO,EAAE,iBAAiB;gBACvC,aAAa,OAAO,EAAE,kBAAkB;gBACxC;YACF;YAEA,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,IAAI,OAAO,QAAQ,IAAI,KAAK,UAAU;wBACpC,IAAI,QAAQ,iBAAiB,EAAE;4BAC7B,aAAa,OAAO,EAAE,aAAa,oBAAoB,OAAO;4BAC9D,oBAAoB,OAAO,GAAG;4BAC9B,UAAU,mBAAmB,GAAG;wBAClC,OAAO;4BACL,oBAAoB,OAAO,GAAG,QAAQ,IAAI;wBAC5C;oBACF;oBACA;gBAEF,KAAK;oBAAkB;wBACrB,MAAM,EAAE,MAAM,EAAE,GAAG;wBACnB,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,2BAA2B,EAAE,QAAQ;wBAC5C,aAAa,OAAO,EAAE,kBAAkB,WAAW;wBACnD;oBACF;gBAEA,KAAK;oBACH,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;oBACN,aAAa,OAAO,EAAE,kBAAkB;oBACxC;gBAEF,KAAK;oBAAS;wBACZ,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACN,aAAa,OAAO,EAAE,iBAAiB;wBACvC;oBACF;gBAEA;oBACE,aAAa,OAAO,EAAE,YAAY;YACtC;QACF;+DAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;0DAC9B,CAAC;YACC,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,KAAK,EAAE;gBAC7C,QAAQ,KAAK,CAAC,OAAO,CAAC;YACxB,OAAO;gBACL,oBAAoB;YACtB;QACF;yDACA;QAAC;KAAoB;IAGvB,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;yCAAE;YACR,aAAa,OAAO,GAAG;YACvB,kBAAkB,OAAO,GAAG;YAC5B,MAAM,cAAc,qIAAA,CAAA,mBAAgB,CAAC,SAAS,CAAC;YAC/C,MAAM,aAAa,gBAAgB,OAAO;YAE1C;iDAAO;oBACL,aAAa,OAAO,GAAG;oBAEvB,QAAQ,KAAK,CAAC;oBACd;oBACA,kBAAkB,OAAO,GAAG;oBAC5B,oBAAoB,OAAO,GAAG;oBAC9B,IAAI,cAAc,qIAAA,CAAA,mBAAgB,CAAC,WAAW,IAAI;wBAChD,QAAQ,KAAK,CAAC;wBACd,qIAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,MAAM;wBAClC,gBAAgB,OAAO,GAAG;oBAC5B;gBACF;;QACF;wCAAG;QAAC;KAAc;IAElB,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACjC,IAAI;gBACF,IAAI,qIAAA,CAAA,mBAAgB,CAAC,WAAW,MAAM,gBAAgB,OAAO,KAAK,KAAK;oBACrE,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,IAAI,gBAAgB,OAAO,EAAE;oBAC3B,MAAM,qIAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,MAAM;gBAC1C;gBAEA,gCAAgC;gBAChC,oBAAoB,OAAO,GAAG;gBAC9B,MAAM,qIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC;gBAC/B,gBAAgB,OAAO,GAAG;gBAC1B,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,gBAAgB,OAAO,GAAG;gBAC1B,OAAO;YACT;QACF;mDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,OAAO,IAAI,EAAE,SAAS,gBAAgB;YACpE,QAAQ,KAAK,CAAC;YACd,qIAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,MAAM;YAElC,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,EAAE,kBAAkB;gBACxC,oBAAoB,OAAO,GAAG,IAAI,0BAA0B;gBAC5D,gBAAgB,OAAO,GAAG;YAC5B;QACF;sDAAG,EAAE;IAEL,MAAM,OAAO,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YACxB,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,qIAAA,CAAA,mBAAgB,CAAC,WAAW,IAAI;gBAC5D,QAAQ,IAAI,CAAC;gBACb;YACF;YACA,qIAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC;QACxB;gDAAG,EAAE;IAEL,OAAO,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;uCACX,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA,aAAa,qIAAA,CAAA,mBAAgB,CAAC,WAAW;YAC3C,CAAC;sCACD;QAAC;QAAS;QAAY;KAAK;AAE/B;GA5Ka;AA8KN,SAAS,oBACd,WAAmB,EACnB,QAAoC;;IAEpC,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;yCAAE;YACR,YAAY,OAAO,GAAG;QACxB;wCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;+DAAgB,CAAC;oBACrB,IAAI,QAAQ,IAAI,KAAK,aAAa;wBAChC,YAAY,OAAO,CAAC;oBACtB,OAAO,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,KAAK,EAAE;wBACpD,0BAA0B;wBAC1B,QAAQ,KAAK,CACV,MAAM;2EAAC,CAAC,IAAM,EAAE,IAAI,KAAK;0EACzB,OAAO;2EAAC,CAAC,IAAM,YAAY,OAAO,CAAC;;oBACxC;gBACF;;YAEA,MAAM,cAAc,qIAAA,CAAA,mBAAgB,CAAC,SAAS,CAAC;YAC/C;iDAAO,IAAM;;QACf;wCAAG;QAAC;KAAY;AAClB;IAzBgB", "debugId": null}}, {"offset": {"line": 2113, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/thread-provider/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { errorToast } from \"@/features/global/toast\";\r\nimport { API_BASE_URL, createThread, getThread, getToken } from \"@/lib/api\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { useCurrentThreadStore, useSubscribeToAgentRunning } from \"@/stores/current-thread\";\r\nimport { Thread, WebSocketMessage } from \"@/types/thread-message\";\r\nimport { captureException } from \"@sentry/nextjs\";\r\nimport { useMutation, UseMutationResult, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { nanoid } from \"nanoid\";\r\nimport React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport { useNavigateFile } from \"../../stores/navigate-file\";\r\nimport { useProject } from \"../project-provider\";\r\nimport { useWebSocketManager } from \"./hooks\";\r\nimport { ErrorMessage, ThreadUpdateMessage } from \"./types\";\r\n\r\ninterface ThreadContextType {\r\n  currentThread?: Thread;\r\n  isLoading: boolean;\r\n  createThreadFn: UseMutationResult<Thread, Error, void>;\r\n  sendMessageFn: UseMutationResult<\r\n    void,\r\n    Error,\r\n    {\r\n      content: string;\r\n      images?: File[];\r\n      selectedPaths?: string[];\r\n      promptSet?: number;\r\n      model: \"creative\" | \"standard\" | \"plan\";\r\n    }\r\n  >;\r\n  stopSession: () => Promise<boolean>;\r\n  selectThreadId: () => number | null;\r\n  selectFirstThread: () => number | undefined;\r\n  contextLimitReached: boolean;\r\n  setContextLimitReached: (isLimitReached: boolean) => void;\r\n}\r\n\r\ninterface ThreadProviderProps {\r\n  projectId: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const ThreadContext = createContext<ThreadContextType | undefined>(undefined);\r\n\r\nconst getModelId = (model: \"creative\" | \"standard\" | \"plan\") => {\r\n  switch (model) {\r\n    case \"creative\":\r\n      return \"2\";\r\n    case \"standard\":\r\n      return \"1\";\r\n    case \"plan\":\r\n      return \"8\";\r\n    default:\r\n      return \"2\";\r\n  }\r\n};\r\n\r\nconst AUTONOMOUS_ITERATIONS = 20;\r\n\r\nexport const ThreadProvider: React.FC<ThreadProviderProps> = ({ projectId, children }) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  const [contextLimitReached, setContextLimitReached] = useState(false);\r\n\r\n  const { threads, refetch } = useProject();\r\n\r\n  const { setFile, setActiveTab } = useNavigateFile(\r\n    useShallow((state) => ({\r\n      setFile: state.setFile,\r\n      setActiveTab: state.setActiveTab,\r\n    })),\r\n  );\r\n\r\n  const {\r\n    currentThreadId,\r\n    setCurrentThreadId,\r\n    addThreadMessages,\r\n    setThreadMessages,\r\n    setIsAgentRunning,\r\n    setNoTokenModalOpen,\r\n    setThreadName,\r\n    setIsSoftgenProcessing,\r\n  } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      currentThreadId: state.id,\r\n      setCurrentThreadId: state.setId,\r\n      addThreadMessages: state.addThreadMessages,\r\n      setThreadMessages: state.setThreadMessages,\r\n      setIsAgentRunning: state.setIsAgentRunning,\r\n      setNoTokenModalOpen: state.setNoTokenModalOpen,\r\n      setThreadName: state.setThreadName,\r\n      setIsSoftgenProcessing: state.setIsSoftgenProcessing,\r\n    })),\r\n  );\r\n\r\n  useSubscribeToAgentRunning((isRunning, prevWasRunning) => {\r\n    console.debug(`Agent running state changed: ${isRunning} (prev: ${prevWasRunning})`);\r\n\r\n    if (!isRunning && prevWasRunning) {\r\n      refetch();\r\n      setActiveTab(\"preview\");\r\n      setFile(\"\");\r\n    }\r\n  });\r\n\r\n  const { data: currentThread, isLoading } = useQuery<Thread>({\r\n    queryKey: [\"get-thread\", currentThreadId],\r\n    queryFn: async () => {\r\n      if (!currentThreadId) return null;\r\n      const thread = await getThread(currentThreadId);\r\n      return thread;\r\n    },\r\n    enabled: !!currentThreadId,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n\r\n  // update thread messages when currentThread changes\r\n  useEffect(() => {\r\n    if (currentThread) {\r\n      setThreadMessages(currentThread.messages);\r\n    }\r\n  }, [currentThread]);\r\n\r\n  const onComplete = (content: string) => {\r\n    addThreadMessages([\r\n      {\r\n        role: \"assistant\",\r\n        content,\r\n        message_id: nanoid(),\r\n      },\r\n    ]);\r\n  };\r\n\r\n  const onMessage = (message: WebSocketMessage) => {\r\n    if (message.type === \"thread_update\") {\r\n      const { data } = message as ThreadUpdateMessage;\r\n      if (data?.messages) {\r\n        setThreadMessages(data.messages);\r\n      }\r\n    }\r\n  };\r\n\r\n  const onError = (error: WebSocketMessage) => {\r\n    console.error(\"WebSocket error:\", error);\r\n\r\n    // Show a toast notification for WebSocket errors\r\n    if (error.type === \"connection_error\") {\r\n      toast.error(\"Connection issue detected\", {\r\n        description:\r\n          \"The connection to the AI agent was interrupted. You may need to refresh the page if messages stop appearing.\",\r\n        duration: 10000,\r\n      });\r\n    }\r\n  };\r\n\r\n  const onErrorMessage = (errorMsg: ErrorMessage) => {\r\n    if (typeof errorMsg.data === \"string\" && errorMsg.data.includes(\"No tokens available\")) {\r\n      setNoTokenModalOpen(true);\r\n    }\r\n    setIsAgentRunning(false);\r\n  };\r\n\r\n  const onSoftgenProcessing = (isProcessing: boolean) => {\r\n    setIsSoftgenProcessing(isProcessing);\r\n  };\r\n\r\n  const {\r\n    connect: connectWebSocket,\r\n    disconnect: disconnectWebSocket,\r\n    send: sendWebSocketMessage,\r\n  } = useWebSocketManager({\r\n    onComplete,\r\n    onMessage,\r\n    onErrorMessage,\r\n    setIsAgentRunning,\r\n    onError,\r\n    onSoftgenProcessing,\r\n    setContextLimitReached,\r\n  });\r\n\r\n  useEffect(() => {\r\n    // Clean up WebSocket when thread changes to null/undefined\r\n    if (!currentThreadId) {\r\n      setThreadMessages([]);\r\n      disconnectWebSocket(1000, \"Thread cleared\");\r\n      setIsAgentRunning(false);\r\n    }\r\n\r\n    // cleanup on component unmount\r\n    return () => {\r\n      setThreadMessages([]);\r\n      disconnectWebSocket(1000, currentThreadId ? \"Thread disconnected\" : \"Component unmounted\");\r\n      setIsAgentRunning(false);\r\n    };\r\n  }, [currentThreadId, disconnectWebSocket]);\r\n  const createThreadFn = useMutation({\r\n    mutationFn: (): Promise<Thread> => createThread(projectId),\r\n    onSuccess: (newThread: Thread) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"threads\", projectId] });\r\n      setCurrentThreadId(newThread.thread_id);\r\n      setThreadName(newThread.name);\r\n      // return newThread;\r\n    },\r\n    onError: (error) => {\r\n      errorToast(error.toString());\r\n    },\r\n  });\r\n\r\n  const selectThreadId = useCallback(() => {\r\n    if (currentThreadId) return currentThreadId;\r\n\r\n    console.warn(\"No current thread ID available, selecting latest thread\");\r\n\r\n    const latestThread = threads?.[0];\r\n\r\n    if (latestThread) {\r\n      setCurrentThreadId(latestThread.thread_id);\r\n      return latestThread.thread_id;\r\n    }\r\n\r\n    return null;\r\n  }, [currentThreadId, setCurrentThreadId, threads]);\r\n\r\n  const sendMessageFn = useMutation({\r\n    mutationFn: async ({\r\n      content,\r\n      images = [],\r\n      selectedPaths = [],\r\n      promptSet = 10,\r\n      model = \"standard\",\r\n    }: {\r\n      content: string;\r\n      images?: File[];\r\n      selectedPaths?: string[];\r\n      promptSet?: number;\r\n      model: \"creative\" | \"standard\" | \"plan\";\r\n    }) => {\r\n      try {\r\n        setIsAgentRunning(true);\r\n\r\n        let effectiveThreadId = selectThreadId();\r\n\r\n        if (!effectiveThreadId) {\r\n          debug(\"No thread ID available, creating a new thread...\");\r\n\r\n          try {\r\n            const newThread = await createThreadFn.mutateAsync();\r\n            effectiveThreadId = newThread.thread_id;\r\n            debug(`Created new thread with ID: ${effectiveThreadId}`);\r\n          } catch (error) {\r\n            console.error(\"Failed to create a new thread:\", error);\r\n            throw new Error(\r\n              \"Could not create a new thread to send your message. Please try again.\",\r\n            );\r\n          }\r\n        }\r\n\r\n        const newUserMessage = {\r\n          role: \"user\" as const,\r\n          content,\r\n          message_id: nanoid(),\r\n          type: \"user_message\",\r\n          created_at: new Date().toISOString(),\r\n          updated_at: new Date().toISOString(),\r\n          include_in_llm_message_history: true,\r\n        };\r\n\r\n        addThreadMessages([newUserMessage]);\r\n\r\n        const encodeImageAsBase64 = (file: File) => {\r\n          return new Promise<string>((resolve, reject) => {\r\n            const reader = new FileReader();\r\n            reader.readAsDataURL(file);\r\n            reader.onload = () => {\r\n              const result = reader.result?.toString().split(\",\")[1];\r\n              resolve(result || \"\");\r\n            };\r\n            reader.onerror = (error) => reject(error);\r\n          });\r\n        };\r\n\r\n        const encodedImages = await Promise.all(\r\n          images.map((file: File) => encodeImageAsBase64(file)),\r\n        );\r\n\r\n        const messageData = {\r\n          action: \"new_message\",\r\n          message: content,\r\n          objective_images: encodedImages,\r\n          autonomous_iterations: AUTONOMOUS_ITERATIONS,\r\n          mode: \"autonomous\",\r\n          selected_paths: selectedPaths,\r\n          prompt_set: promptSet,\r\n          model_id: getModelId(model),\r\n          chat_mode: model === \"plan\",\r\n        };\r\n\r\n        const token = getToken(); // Make sure to import getToken\r\n        const wsUrl = `${API_BASE_URL.replace(\"http\", \"ws\")}/start_thread_websocket/${projectId}/${effectiveThreadId}?token=${token}`;\r\n        await connectWebSocket(wsUrl);\r\n        sendWebSocketMessage(messageData);\r\n      } catch (error) {\r\n        captureException(error);\r\n        errorToast(\"Failed to send message\");\r\n      } finally {\r\n        setIsAgentRunning(false);\r\n      }\r\n    },\r\n    onError: (error: Error) => {\r\n      console.error(\"Error sending message:\", error);\r\n      errorToast(error.message);\r\n      setIsAgentRunning(false);\r\n    },\r\n  });\r\n\r\n  const stopSession = useCallback(async () => {\r\n    try {\r\n      sendWebSocketMessage({ action: \"stop\" });\r\n      disconnectWebSocket(1000, \"Session stopped by user\");\r\n      setIsAgentRunning(false);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Error stopping session:\", error);\r\n      errorToast(\"Failed to stop session\");\r\n      return false;\r\n    }\r\n  }, [disconnectWebSocket, sendWebSocketMessage]);\r\n\r\n  const selectFirstThread = useCallback(() => {\r\n    const latestThread = threads?.[0];\r\n    if (latestThread) {\r\n      setCurrentThreadId(latestThread.thread_id);\r\n      return latestThread.thread_id;\r\n    }\r\n  }, [threads, setCurrentThreadId]);\r\n\r\n  const contextValue = useMemo<ThreadContextType>(\r\n    () => ({\r\n      currentThread,\r\n      isLoading,\r\n      createThreadFn,\r\n      sendMessageFn,\r\n      selectThreadId,\r\n      stopSession,\r\n      selectFirstThread,\r\n      contextLimitReached,\r\n      setContextLimitReached,\r\n    }),\r\n    [\r\n      currentThread,\r\n      isLoading,\r\n      createThreadFn,\r\n      sendMessageFn,\r\n      selectThreadId,\r\n      stopSession,\r\n      selectFirstThread,\r\n      contextLimitReached,\r\n      setContextLimitReached,\r\n    ],\r\n  );\r\n\r\n  return <ThreadContext.Provider value={contextValue}>{children}</ThreadContext.Provider>;\r\n};\r\n\r\nexport const useThreadContext = () => {\r\n  const context = useContext(ThreadContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useThreadContext must be used within a ThreadProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;AA6CO,MAAM,8BAAgB,CAAA,GAAA,4QAAA,CAAA,gBAAa,AAAD,EAAiC;AAE1E,MAAM,aAAa,CAAC;IAClB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,wBAAwB;AAEvB,MAAM,iBAAgD,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;;IACnF,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IAEtC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAC9C,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;qDAAE,CAAC,QAAU,CAAC;gBACrB,SAAS,MAAM,OAAO;gBACtB,cAAc,MAAM,YAAY;YAClC,CAAC;;IAGH,MAAM,EACJ,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,mBAAmB,EACnB,aAAa,EACb,sBAAsB,EACvB,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD,EACtB,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;2DAAE,CAAC,QAAU,CAAC;gBACrB,iBAAiB,MAAM,EAAE;gBACzB,oBAAoB,MAAM,KAAK;gBAC/B,mBAAmB,MAAM,iBAAiB;gBAC1C,mBAAmB,MAAM,iBAAiB;gBAC1C,mBAAmB,MAAM,iBAAiB;gBAC1C,qBAAqB,MAAM,mBAAmB;gBAC9C,eAAe,MAAM,aAAa;gBAClC,wBAAwB,MAAM,sBAAsB;YACtD,CAAC;;IAGH,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD;qDAAE,CAAC,WAAW;YACrC,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,UAAU,QAAQ,EAAE,eAAe,CAAC,CAAC;YAEnF,IAAI,CAAC,aAAa,gBAAgB;gBAChC;gBACA,aAAa;gBACb,QAAQ;YACV;QACF;;IAEA,MAAM,EAAE,MAAM,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAU;QAC1D,UAAU;YAAC;YAAc;SAAgB;QACzC,OAAO;uCAAE;gBACP,IAAI,CAAC,iBAAiB,OAAO;gBAC7B,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE;gBAC/B,OAAO;YACT;;QACA,SAAS,CAAC,CAAC;QACX,sBAAsB;IACxB;IAEA,oDAAoD;IACpD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,eAAe;gBACjB,kBAAkB,cAAc,QAAQ;YAC1C;QACF;mCAAG;QAAC;KAAc;IAElB,MAAM,aAAa,CAAC;QAClB,kBAAkB;YAChB;gBACE,MAAM;gBACN;gBACA,YAAY,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;YACnB;SACD;IACH;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,QAAQ,IAAI,KAAK,iBAAiB;YACpC,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,MAAM,UAAU;gBAClB,kBAAkB,KAAK,QAAQ;YACjC;QACF;IACF;IAEA,MAAM,UAAU,CAAC;QACf,QAAQ,KAAK,CAAC,oBAAoB;QAElC,iDAAiD;QACjD,IAAI,MAAM,IAAI,KAAK,oBAAoB;YACrC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,6BAA6B;gBACvC,aACE;gBACF,UAAU;YACZ;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,OAAO,SAAS,IAAI,KAAK,YAAY,SAAS,IAAI,CAAC,QAAQ,CAAC,wBAAwB;YACtF,oBAAoB;QACtB;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,uBAAuB;IACzB;IAEA,MAAM,EACJ,SAAS,gBAAgB,EACzB,YAAY,mBAAmB,EAC/B,MAAM,oBAAoB,EAC3B,GAAG,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;oCAAE;YACR,2DAA2D;YAC3D,IAAI,CAAC,iBAAiB;gBACpB,kBAAkB,EAAE;gBACpB,oBAAoB,MAAM;gBAC1B,kBAAkB;YACpB;YAEA,+BAA+B;YAC/B;4CAAO;oBACL,kBAAkB,EAAE;oBACpB,oBAAoB,MAAM,kBAAkB,wBAAwB;oBACpE,kBAAkB;gBACpB;;QACF;mCAAG;QAAC;QAAiB;KAAoB;IACzC,MAAM,iBAAiB,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU;0DAAE,IAAuB,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;;QAChD,SAAS;0DAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;gBACjE,mBAAmB,UAAU,SAAS;gBACtC,cAAc,UAAU,IAAI;YAC5B,oBAAoB;YACtB;;QACA,OAAO;0DAAE,CAAC;gBACR,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,QAAQ;YAC3B;;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;sDAAE;YACjC,IAAI,iBAAiB,OAAO;YAE5B,QAAQ,IAAI,CAAC;YAEb,MAAM,eAAe,SAAS,CAAC,EAAE;YAEjC,IAAI,cAAc;gBAChB,mBAAmB,aAAa,SAAS;gBACzC,OAAO,aAAa,SAAS;YAC/B;YAEA,OAAO;QACT;qDAAG;QAAC;QAAiB;QAAoB;KAAQ;IAEjD,MAAM,gBAAgB,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QAChC,UAAU;yDAAE,OAAO,EACjB,OAAO,EACP,SAAS,EAAE,EACX,gBAAgB,EAAE,EAClB,YAAY,EAAE,EACd,QAAQ,UAAU,EAOnB;gBACC,IAAI;oBACF,kBAAkB;oBAElB,IAAI,oBAAoB;oBAExB,IAAI,CAAC,mBAAmB;wBACtB,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBAEN,IAAI;4BACF,MAAM,YAAY,MAAM,eAAe,WAAW;4BAClD,oBAAoB,UAAU,SAAS;4BACvC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,4BAA4B,EAAE,mBAAmB;wBAC1D,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kCAAkC;4BAChD,MAAM,IAAI,MACR;wBAEJ;oBACF;oBAEA,MAAM,iBAAiB;wBACrB,MAAM;wBACN;wBACA,YAAY,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;wBACjB,MAAM;wBACN,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;wBAClC,gCAAgC;oBAClC;oBAEA,kBAAkB;wBAAC;qBAAe;oBAElC,MAAM;yFAAsB,CAAC;4BAC3B,OAAO,IAAI;iGAAgB,CAAC,SAAS;oCACnC,MAAM,SAAS,IAAI;oCACnB,OAAO,aAAa,CAAC;oCACrB,OAAO,MAAM;yGAAG;4CACd,MAAM,SAAS,OAAO,MAAM,EAAE,WAAW,MAAM,IAAI,CAAC,EAAE;4CACtD,QAAQ,UAAU;wCACpB;;oCACA,OAAO,OAAO;yGAAG,CAAC,QAAU,OAAO;;gCACrC;;wBACF;;oBAEA,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,OAAO,GAAG;qEAAC,CAAC,OAAe,oBAAoB;;oBAGjD,MAAM,cAAc;wBAClB,QAAQ;wBACR,SAAS;wBACT,kBAAkB;wBAClB,uBAAuB;wBACvB,MAAM;wBACN,gBAAgB;wBAChB,YAAY;wBACZ,UAAU,WAAW;wBACrB,WAAW,UAAU;oBACvB;oBAEA,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,KAAK,+BAA+B;oBACzD,MAAM,QAAQ,GAAG,oHAAA,CAAA,eAAY,CAAC,OAAO,CAAC,QAAQ,MAAM,wBAAwB,EAAE,UAAU,CAAC,EAAE,kBAAkB,OAAO,EAAE,OAAO;oBAC7H,MAAM,iBAAiB;oBACvB,qBAAqB;gBACvB,EAAE,OAAO,OAAO;oBACd,CAAA,GAAA,4NAAA,CAAA,mBAAgB,AAAD,EAAE;oBACjB,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;gBACb,SAAU;oBACR,kBAAkB;gBACpB;YACF;;QACA,OAAO;yDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO;gBACxB,kBAAkB;YACpB;;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,IAAI;gBACF,qBAAqB;oBAAE,QAAQ;gBAAO;gBACtC,oBAAoB,MAAM;gBAC1B,kBAAkB;gBAClB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;gBACX,OAAO;YACT;QACF;kDAAG;QAAC;QAAqB;KAAqB;IAE9C,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;yDAAE;YACpC,MAAM,eAAe,SAAS,CAAC,EAAE;YACjC,IAAI,cAAc;gBAChB,mBAAmB,aAAa,SAAS;gBACzC,OAAO,aAAa,SAAS;YAC/B;QACF;wDAAG;QAAC;QAAS;KAAmB;IAEhC,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;gDACzB,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;+CACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,qBAAO,4SAAC,cAAc,QAAQ;QAAC,OAAO;kBAAe;;;;;;AACvD;GA/Sa;;QACS,yRAAA,CAAA,iBAAc;QAIL,2IAAA,CAAA,aAAU;QAEL,oIAAA,CAAA,kBAAe;QAgB7C,qIAAA,CAAA,wBAAqB;QAazB,qIAAA,CAAA,6BAA0B;QAUiB,8QAAA,CAAA,WAAQ;QAiE/C,kJAAA,CAAA,sBAAmB;QAyBA,iRAAA,CAAA,cAAW;QA4BZ,iRAAA,CAAA,cAAW;;;KApKtB;AAiTN,MAAM,mBAAmB;;IAC9B,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}]}