type Key =
  | "access_token"
  | "kinde_id"
  | "email"
  | "discount_code"
  | "projectIdea"
  | "newProjectIdea"
  | "initial_plan";

export const setItemWithExpiry = (key: Key, value: string, ttl: number) => {
  // Check if we're in a browser environment
  if (typeof window === "undefined") return;

  const now = Date.now();
  const item = {
    value: value,
    expiry: now + ttl,
  };
  localStorage.setItem(key, JSON.stringify(item));
};

export const getItemWithExpiry = (key: Key) => {
  // Check if we're in a browser environment
  if (typeof window === "undefined") return null;

  const itemStr = localStorage.getItem(key);
  if (!itemStr) {
    return null;
  }

  const item = JSON.parse(itemStr);
  const now = Date.now();

  if (now > item.expiry) {
    localStorage.removeItem(key);
    return null;
  }
  return item.value;
};

export const removeItemWithExpiry = (key: Key) => {
  // Check if we're in a browser environment
  if (typeof window === "undefined") return;

  localStorage.removeItem(key);
};

// Cookie utility functions
export const setCookieWithExpiry = (name: Key, value: string, ttlMs: number) => {
  // Check if we're in a browser environment
  if (typeof document === "undefined") return;

  const isProd = process.env.NODE_ENV === "production";
  const maxAge = Math.floor(ttlMs / 1000); // Convert milliseconds to seconds for cookie
  document.cookie = `${name}=${value}; path=/; max-age=${maxAge}; SameSite=Lax${
    isProd ? "; Secure" : ""
  }`;
};

export const getCookie = (name: Key): string | null => {
  // Check if we're in a browser environment
  if (typeof document === "undefined") return null;

  try {
    return (
      document.cookie
        .split("; ")
        .find((row) => row.startsWith(`${name}=`))
        ?.split("=")[1] || null
    );
  } catch (error) {
    console.error(`Error getting cookie ${name}:`, error);
    return null;
  }
};

export const removeCookie = (name: Key) => {
  // Check if we're in a browser environment
  if (typeof document === "undefined") return;

  const isProd = process.env.NODE_ENV === "production";
  // Set multiple cookie deletion attempts with different paths to ensure complete removal
  document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${
    isProd ? "; Secure" : ""
  }`;
  document.cookie = `${name}=; path=/; domain=${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${
    isProd ? "; Secure" : ""
  }`;
  document.cookie = `${name}=; path=/; domain=.${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${
    isProd ? "; Secure" : ""
  }`;
};

/**
 * Clears all cookies for the current domain
 */
export const clearAllCookies = () => {
  // Check if we're in a browser environment
  if (typeof document === "undefined") return;

  const cookies = document.cookie.split(";");

  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i];
    const eqPos = cookie.indexOf("=");
    const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();

    // Skip empty names
    if (!name) continue;

    // Use the removeCookie function for each cookie
    removeCookie(name as Key);
  }
};
