import json
import logging
import asyncio
import time
import jwt
from collections import defaultdict
from datetime import datetime
from typing import Optional, Dict
from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState
from sqlalchemy import select
from core.db import Database
from core.models import ProjectThread, User
from core.config import settings
from core.envs.env_manager import check_and_maintain_environments
from core.utils.project_utils import add_remix_suffix

# Initialize database
db = Database()

# --- Remix background task management ---
# project_id -> { 'task': asyncio.Task, 'listeners': set(), 'last_ping': float }
_REMIX_STATE = defaultdict(lambda: {
    'task': None,
    'listeners': set(),
    'last_ping': 0.0,
    'is_active': False
})

async def _broadcast_remix_update(project_id, update):
    """Broadcast update directly to all connected clients for remix."""
    if project_id not in _REMIX_STATE:
        logging.warning(f"No remix state found for project {project_id}")
        return

    state = _REMIX_STATE[project_id]

    # Log the update being broadcast
    logging.info(f"Broadcasting remix update for project {project_id}: {update}")

    # Create a task for each WebSocket to send messages concurrently
    send_tasks = []
    disconnected = set()

    for websocket in state['listeners']:
        try:
            if websocket.client_state == WebSocketState.CONNECTED and not getattr(websocket, 'is_closing', False):
                # Create a task for sending the message
                send_task = asyncio.create_task(websocket.send_json(update))
                send_tasks.append(send_task)
            else:
                disconnected.add(websocket)
        except Exception:
            logging.exception("Error creating send task")
            disconnected.add(websocket)

    # Wait for all send tasks to complete
    if send_tasks:
        try:
            await asyncio.gather(*send_tasks)
        except Exception:
            logging.exception("Error sending messages")

    # Clean up disconnected listeners
    if disconnected:
        state['listeners'] -= disconnected
        logging.info(f"Removed {len(disconnected)} disconnected listeners for project {project_id}")

async def _run_remix_background(project_id: str, current_user: User, env_values: Optional[Dict[str, str]] = None):
    """Run remix process in background with improved error handling."""
    # Import here to avoid circular import
    from core.platform.project import ProjectService, is_admin
    
    state = _REMIX_STATE[project_id]
    state['is_active'] = True
    state['last_ping'] = asyncio.get_event_loop().time()

    try:
        # Step 1: Validate project access and get original project
        await _broadcast_remix_update(project_id, {
            "step": "validate_access",
            "status": "started",
            "message": "Validating project access..."
        })

        project_manager = ProjectService(db)
        original_project = await project_manager.get_project_for_remix(project_id)
        if not original_project:
            await _broadcast_remix_update(project_id, {
                "step": "validate_access",
                "status": "failed",
                "error": "Original project not found"
            })
            return

        # Check if the project is public or user has access
        is_public = original_project.get("isPublic", False)
        is_owner = original_project.get("owner_id") == current_user.id
        is_user_admin = is_admin(current_user)

        if not is_public and not (is_owner or is_user_admin):
            await _broadcast_remix_update(project_id, {
                "step": "validate_access",
                "status": "failed",
                "error": "Not authorized to remix this project"
            })
            return

        await _broadcast_remix_update(project_id, {
            "step": "validate_access",
            "status": "completed",
            "message": "Project access validated successfully"
        })

        # Step 2: Check project limit
        await _broadcast_remix_update(project_id, {
            "step": "check_limit",
            "status": "started",
            "message": "Checking project limit..."
        })

        if current_user.project_limit <= 0:
            await _broadcast_remix_update(project_id, {
                "step": "check_limit",
                "status": "failed",
                "error": "Project limit reached. Unable to create new project."
            })
            return

        await _broadcast_remix_update(project_id, {
            "step": "check_limit",
            "status": "completed",
            "message": "Project limit check passed"
        })

        # Step 3: Create remixed project
        await _broadcast_remix_update(project_id, {
            "step": "create_project",
            "status": "started",
            "message": "Creating remixed project..."
        })

        new_name = add_remix_suffix(original_project['name'])
        project_data = await project_manager.remix_project(new_name, current_user.id, original_project, env_values)

        await _broadcast_remix_update(project_id, {
            "step": "create_project",
            "status": "completed",
            "message": "Project created successfully",
            "project_data": project_data
        })

        # Step 4: Deduct project limit
        await _broadcast_remix_update(project_id, {
            "step": "deduct_limit",
            "status": "started",
            "message": "Updating project limit..."
        })

        success = await db.project_service.deduct_project_limit(current_user.kinde_id)
        if not success:
            await _broadcast_remix_update(project_id, {
                "step": "deduct_limit",
                "status": "failed",
                "error": "Failed to update project limit"
            })
            return

        await _broadcast_remix_update(project_id, {
            "step": "deduct_limit",
            "status": "completed",
            "message": "Project limit updated successfully"
        })

        # Step 5: Create initial thread
        await _broadcast_remix_update(project_id, {
            "step": "create_thread",
            "status": "started",
            "message": "Creating initial thread..."
        })

        async with db.get_async_session() as session:
            new_thread = ProjectThread(
                project_id=project_data["project_id"],
                messages=json.dumps([]),
                creation_date=datetime.now().isoformat(),
                last_updated_date=datetime.now().isoformat(),
                name="New Thread"
            )
            session.add(new_thread)
            await session.commit()

        project_data["initial_thread_id"] = new_thread.thread_id

        await _broadcast_remix_update(project_id, {
            "step": "create_thread",
            "status": "completed",
            "message": "Initial thread created successfully"
        })

        # Step 6: Finalize
        await _broadcast_remix_update(project_id, {
            "step": "finalize",
            "status": "completed",
            "message": "Project remixed successfully",
            "project_data": project_data
        })

        # Fire and forget the environment maintenance check
        if settings.env == 'prod':
            asyncio.create_task(check_and_maintain_environments())

    except Exception as e:
        logging.error(f"Error in remix process: {str(e)}")
        await _broadcast_remix_update(project_id, {
            "error": str(e),
            "status": "failed"
        })
    finally:
        # Clean up state after done
        if project_id in _REMIX_STATE:
            # Clean up all listeners
            for websocket in _REMIX_STATE[project_id]['listeners'].copy():
                try:
                    if websocket.client_state == WebSocketState.CONNECTED:
                        await websocket.close()
                except Exception as e:
                    logging.error(f"Error closing websocket: {str(e)}")

            # Remove the entire project state
            del _REMIX_STATE[project_id]
            logging.info(f"Cleaned up remix state for project {project_id}")

async def send_remix_ping(websocket: WebSocket, project_id: str):
    """Send periodic ping messages to keep the WebSocket connection alive for remix."""
    try:
        while True:
            await asyncio.sleep(10)  # Send ping every 10 seconds
            try:
                if websocket.client_state == WebSocketState.CONNECTED and not getattr(websocket, 'is_closing', False):
                    timestamp = asyncio.get_event_loop().time()
                    await websocket.send_json({"type": "ping", "timestamp": timestamp})
                    # Update last ping time
                    if project_id in _REMIX_STATE:
                        _REMIX_STATE[project_id]['last_ping'] = timestamp
                else:
                    # Break the loop if WebSocket is not connected or is closing
                    logging.info(f"WebSocket not connected for project {project_id}, stopping ping loop")
                    break
            except Exception:
                logging.exception("Error sending ping")
                break
    except asyncio.CancelledError:
        logging.info(f"Ping task cancelled for project {project_id}")
        pass

async def remix_project_websocket_handler(
    websocket: WebSocket,
    project_id: str,
    token: str,
    env_values: Optional[str] = None,  # JSON string of env values
):
    """WebSocket handler for remix project functionality."""
    await websocket.accept()
    logging.info(f"Remix WebSocket connection accepted for project {project_id}")

    try:
        # Authenticate user using the token
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            email = payload.get("sub")
            if email is None:
                await websocket.send_json({"error": "Could not validate credentials"})
                return

            async with db.get_async_session() as session:
                result = await session.execute(select(User).where(User.email == email))
                current_user = result.scalar_one_or_none()

            if not current_user or not current_user.is_active:
                await websocket.send_json({"error": "Invalid or inactive user"})
                return

        except jwt.PyJWTError as e:
            await websocket.send_json({"error": f"Could not validate credentials: {str(e)}"})
            return

        # Check if user is on free tier
        if current_user.plan == "free-tier":
            await websocket.send_json({"error": "Project remixing features are not available on the free tier plan"})
            return

        # Parse env_values if provided
        parsed_env_values = None
        if env_values:
            try:
                parsed_env_values = json.loads(env_values)
                logging.info(f"Received env values for remix: {parsed_env_values}")
            except json.JSONDecodeError:
                await websocket.send_json({"error": "Invalid env_values JSON format"})
                return

        # Initialize state
        if project_id not in _REMIX_STATE:
            _REMIX_STATE[project_id] = {
                'listeners': set(),
                'last_ping': time.time(),
                'is_active': False,
                'task': None
            }
            logging.info(f"Initialized new remix state for project {project_id}")

        state = _REMIX_STATE[project_id]
        state['listeners'].add(websocket)
        logging.info(f"Added WebSocket to listeners for project {project_id}. Total listeners: {len(state['listeners'])}")

        # Start ping task
        ping_task = asyncio.create_task(send_remix_ping(websocket, project_id))
        logging.info(f"Started ping task for project {project_id}")

        # Start remix task if not already running
        if not state['is_active']:
            state['is_active'] = True
            remix_task = asyncio.create_task(
                _run_remix_background(
                    project_id=project_id,
                    current_user=current_user,
                    env_values=parsed_env_values
                )
            )
            logging.info(f"Started remix task for project {project_id}")

        try:
            while True:
                # Check if websocket is still connected and not closing
                if websocket.client_state != WebSocketState.CONNECTED or getattr(websocket, 'is_closing', False):
                    logging.info(f"WebSocket not connected or closing for project {project_id}, stopping message loop")
                    break

                try:
                    # Wait for client messages
                    message = await websocket.receive_json()
                    logging.info(f"Received message from client for project {project_id}: {message}")

                    # Handle pong messages
                    if message.get("type") == "pong":
                        state['last_ping'] = time.time()
                except WebSocketDisconnect:
                    logging.info(f"WebSocket disconnected for project {project_id}")
                    break
                except Exception:
                    logging.exception("Error receiving message")
                    # Mark websocket as closing to prevent further send attempts
                    if websocket and not getattr(websocket, 'is_closing', False):
                        websocket.is_closing = True
                    break

        finally:
            # Cleanup
            if 'ping_task' in locals() and not ping_task.done():
                ping_task.cancel()
                try:
                    await ping_task
                except asyncio.CancelledError:
                    pass
                logging.info(f"Cancelled ping task for project {project_id}")

            if websocket in state['listeners']:
                state['listeners'].remove(websocket)
                logging.info(f"Removed WebSocket from listeners for project {project_id}. Remaining listeners: {len(state['listeners'])}")

            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.close()
                    logging.info(f"Closed WebSocket connection for project {project_id}")
                except Exception as e:
                    logging.info(f"WebSocket already closed for project {project_id}: {type(e).__name__}: {str(e)}")

            # Clean up remix state if no more listeners
            if not state['listeners']:
                state['is_active'] = False
                logging.info(f"Cleaned up remix state for project {project_id}")

    except Exception as e:
        logging.error(f"Unexpected error in remix websocket handler for project {project_id}: {str(e)}")
        if websocket.client_state == WebSocketState.CONNECTED:
            await websocket.close(code=1011, reason="Internal server error") 