import { ActionError, ActionResult, ActionSuccess } from "@/types/thread-message";

interface ToolCall {
  id: string;
  function: {
    name: string;
    arguments: string;
  };
}

interface ToolResult {
  tool_call_id: string;
  content: string;
}

export type MessageType = {
  role?: "user" | "assistant";
  content:
    | string
    | Record<string, unknown>
    | { type: "text"; text: string }
    | { type: "image_url"; image_url: { url: string; detail?: "low" | "high" | "auto" } };
  reasoning_content?: string;
  type?:
    | "user_message"
    | "assistant_message"
    | "content"
    | "error"
    | "start"
    | "session_status"
    | "task_status"
    | "thread_update"
    | string;
  data?: string | Record<string, unknown>;
  tool_calls?: ToolCall[];
  tool_results?: ToolResult[];
  actions?: unknown[] | string;
  failed_actions?: ActionError[];
  success_actions?: ActionSuccess[];
  is_executing?: boolean;
  commit_hash?: string;
  total_tokens?: number;
  model_name?: string;
  status?: string;
  isStreaming?: boolean;
  hasToolCalls?: boolean;
  hasCodeBlock?: boolean;
  timestamp?: string;
  agent_cost?: { input_cost: number; output_cost: number; total_cost: number };
};

export type MessageProps = {
  message: MessageType;
  actions: ActionResult[];
};
