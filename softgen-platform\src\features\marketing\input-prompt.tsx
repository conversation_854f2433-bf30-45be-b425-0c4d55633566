"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Loading from "@/components/ui/loading";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { useInputPromptStore } from "@/stores/input-prompt";
import { ArrowUp } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  FaBriefcase,
  FaCloud,
  FaCode,
  FaComments,
  FaImage,
  FaShoppingCart,
  FaTasks,
  FaUser,
  FaUsers,
  FaUtensils,
} from "react-icons/fa";

const SUGGESTIONS = [
  {
    icon: <FaCode />,
    title: "Create Personal Blog",
    content:
      "Create a modern personal blog with a clean design, blog posts, and social sharing features",
  },
  {
    icon: <FaImage />,
    title: "Build Photo Gallery",
    content:
      "Build an elegant photo gallery with image uploads, categories, and a responsive grid layout",
  },
  {
    icon: <FaUtensils />,
    title: "Design Recipe App",
    content: "Design a recipe sharing app with search, filtering, and user-submitted recipes",
  },
  {
    icon: <FaUser />,
    title: "Make Portfolio Site",
    content:
      "Make a professional portfolio website showcasing projects, skills, and contact information",
  },
  {
    icon: <FaBriefcase />,
    title: "Create Portfolio Site",
    content:
      "Create a professional portfolio website showcasing projects, skills, and contact information",
  },
  {
    icon: <FaUser />,
    title: "Create Portfolio Site",
    content:
      "Create a professional portfolio website showcasing projects, skills, and contact information",
  },
  {
    icon: <FaTasks />,
    title: "Build Task Manager",
    content:
      "Build a task management app with drag-and-drop lists, due dates and team collaboration",
  },
  {
    icon: <FaUsers />,
    title: "Create Social Network",
    content:
      "Create a social networking platform with user profiles, posts and real-time messaging",
  },
  {
    icon: <FaShoppingCart />,
    title: "Design E-commerce Store",
    content: "Design an online store with product catalog, shopping cart and secure checkout",
  },
  {
    icon: <FaCloud />,
    title: "Make Weather App",
    content: "Make a weather forecast app with location detection and animated weather icons",
  },
  {
    icon: <FaComments />,
    title: "Build Chat Application",
    content: "Build a real-time chat application with private messaging and group channels",
  },
];

const SURPRISE_SUGGESTION = [
  "Collaborative Travel Itinerary Planner\nCreate a multi-user travel planner to centralize group trip organization and eliminate confusion.\n- Features a real-time collaborative timeline with drag-and-drop activities.\n- Integrates an interactive map for visualizing routes and points of interest.\n- Includes a shared expense ledger for transparent budget tracking.",
  "Smart Recipe & Meal Planner\nDesign an intelligent recipe app that reduces food waste by helping users plan their weekly meals.\n- Generates weekly meal plans based on diet, available ingredients, and taste profiles.\n- Automatically creates a categorized shopping list from the selected meal plan.\n- Lets users save recipes from the web and upload their own.",
  "Virtual Book Club Platform\nBuild a community platform for readers to create, join, and manage virtual book clubs for online discussion.\n- Includes shared reading lists with member progress tracking.\n- Features integrated discussion forums organized by chapter or topic.\n- Provides a polling system for choosing the next book.\n- Offers a built-in scheduler for virtual video meetings.",
  "Minimalist Habit Tracker\nCreate a clean habit tracker that uses behavioral science to help users build lasting routines.\n- Features interactive charts and heatmaps to visualize streaks and completion rates.\n- Includes customizable reminders and a library of pre-built habit templates.",
  "Peer-to-Peer Skill Exchange\nBuild a community marketplace where individuals can trade their professional skills and talents using a credit-based system.\n- Features detailed user profiles with skill endorsements, reviews, and portfolios.\n- Implements a credit-based system for fair and easy skill swapping.\n- Includes a secure messaging system for project coordination.",
  "Local Event Discovery App\nMake a vibrant application for finding and sharing local events, from concerts to community markets.\n- Uses an interactive, map-based interface with powerful filters for date, category, and price.\n- Allows users to save events to a personal calendar and receive notifications.\n- Features a social feed to see which events friends are interested in.",
  "Dynamic Workout & Fitness Planner\nDesign a personal fitness planner that acts as a virtual coach with a vast library of exercises and customizable routines.\n- Allows users to build and share workout plans with a drag-and-drop interface.\n- Tracks performance with visual charts for metrics like weight lifted and reps.\n- Includes video demonstrations for each exercise to ensure proper form.",
  "Digital Gardening & Plant Care Journal\nCreate a specialized journal for plant enthusiasts to track the growth and health of their garden.\n- Features individual profiles for each plant with a photo gallery and care log.\n- Includes a smart reminder system for watering, fertilizing, and pest control.\n- Integrates an AI tool to help identify diseases or pests from a photo.",
  "Language Exchange Community\nBuild a platform to connect language learners with native speakers for conversation practice and cultural exchange.\n- Features a matching system to find partners based on language, interests, and availability.\n- Includes integrated text/video chat with collaborative correction tools.\n- Implements a reputation system and progress tracker for fluency.",
  '"What to Watch" Movie & TV Tracker\nDesign a sleek, social app for tracking movies and TV shows that provides deeply personalized recommendations.\n- Uses a recommendation engine that learns from user ratings and viewing habits.\n- Features a friend activity feed to see what others are watching and reviewing.\n- Creates curated collections based on genres, actors, and trending topics.',
  "Personal Finance & Budgeting Dashboard\nBuild a secure dashboard that empowers users to control their finances by tracking expenses and visualizing spending habits.\n- Securely connects to bank accounts to automatically import and categorize transactions.\n- Generates interactive charts and reports to show spending trends.\n- Allows users to set and track progress towards savings goals.",
  "Niche Job Board for Remote Creatives\nCreate a curated job board focused on remote positions for designers, writers, and other creative professionals.\n- Features advanced filtering by timezone, contract type, required tools, and salary.\n- Includes visually-rich company profiles that showcase culture and past projects.\n- Provides a resource center with guides on building a remote creative career.",
  "Interactive Product Customizer\nDesign an e-commerce experience where users can visually build, preview, and purchase their own custom products.\n- Features a high-fidelity, 3D real-time visualizer that updates with user selections.\n- Allows users to save their unique creations to a personal gallery.\n- Generates a shareable link for a specific custom design.",
  "Focus & Productivity Timer\nCreate a minimalist timer app based on the Pomodoro technique to help users defeat procrastination and improve focus.\n- Features a clean interface with customizable work/break intervals.\n- Offers a library of ambient background sounds like rain or a coffee shop.\n- Includes a session history log with productivity stats and notes.",
  "Collaborative Storytelling Platform\nBuild an imaginative web app where users write stories together, one paragraph at a time, creating unique narratives.\n- Allows for branching narratives where the community votes on the plot direction.\n- Gives users a profile page showcasing their story contributions.\n- Features themed writing rooms and daily prompts to inspire creativity.",
  "Smart Home Inventory Manager\nCreate a practical app to catalog personal belongings for insurance, organization, or moving.\n- Allows users to add items by scanning barcodes to auto-fetch product details.\n- Organizes items by room and category, with support for receipts and warranty info.\n- Generates printable PDF reports for insurance claims or estate planning.",
  "Guided Meditation & Mindfulness App\nDesign a serene web app offering a library of guided meditation sessions to help users reduce stress.\n- Features a filterable library of sessions by length, topic, and experience level.\n- Includes a personal dashboard to track listening history and mindful minutes.\n- Offers unguided timers with customizable ambient sounds.",
  "Community Help & Task Board\nDesign a hyperlocal platform for neighbors to request and offer help for small, everyday tasks.\n- Features a map-based view of active requests and offers in the community.\n- Includes a simple reputation system with badges and reviews to build trust.\n- Implements a private messaging system to coordinate details safely.",
  "AI-Powered Trip Planner\nBuild a travel planner that uses AI to generate a personalized itinerary based on a user's interests, budget, and pace.\n- Asks users questions to understand their unique travel style.\n- Automatically generates a day-by-day plan with activities, restaurants, and routes.\n- Allows users to easily regenerate or customize parts of the AI-suggested plan.",
  "Online Course & Workshop Platform\nCreate a platform for instructors to build, market, and sell their own online courses with interactive tools.\n- Provides an intuitive course builder with support for video, text, and resources.\n- Features student progress tracking, interactive quizzes, and a Q&A section.\n- Includes a secure payment gateway and tools for creating promotions.",
];

const MessageInput = ({ handleInputClick }: { handleInputClick: () => void }) => {
  const setPrompt = useInputPromptStore((state) => state.setPrompt);
  const { user } = useAuth();
  const router = useRouter();

  const [content, setContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "0px";
      const scrollHeight = textarea.scrollHeight;
      textarea.style.height = `${Math.min(scrollHeight, 384)}px`;
    }
  }, []);

  useEffect(() => {
    adjustTextareaHeight();
  }, [content, adjustTextareaHeight]);

  const handleSubmit = async () => {
    console.log("handleSubmit");
    const trimmedContent = content.trim();
    if (!trimmedContent) {
      return;
    }

    try {
      setIsSubmitting(true);
      setPrompt(trimmedContent);

      if (user?.access_token) {
        console.log("redirecting to app");
        router.push("/app");
        console.log("redirecting to app");
      } else {
        console.log("redirecting to register");
        window.location.href = `${
          process.env.NEXT_PUBLIC_API_BASE_URL
        }/register?post_login_redirect_uri=${encodeURIComponent(
          `${process.env.NEXT_PUBLIC_APP_URL}/app`,
        )}`;
      }
    } catch (error) {
      console.error("Error saving message:", error);
      setIsSubmitting(false);
    }
  };

  const setSuggestionWithTypewriter = (suggestion: string) => {
    setContent(suggestion);
    textareaRef.current?.focus();
    handleInputClick();
  };

  return (
    <>
      {/* <PromptInput
        value={content}
        onValueChange={(value: string) => setContent(value)}
        isLoading={isSubmitting}
        onSubmit={handleSubmit}
        maxHeight={180}
        className="mx-auto w-full max-h-60 max-w-3xl rounded-3xl border-border bg-[#F5F5F5] p-0 dark:bg-background"
      >
        <PromptInputTextarea
          className="p-4 pt-0 text-base mt-4"
          placeholder="What do you want to create today?"
        />
        <PromptInputActions className="justify-end p-3">
          <PromptInputAction tooltip={isSubmitting ? "Stop generation" : "Send message"}>
            <Button
              variant="default"
              size="icon"
              disabled={isSubmitting}
              onClick={handleSubmit}
              className="size-7 rounded-md"
            >
              {isSubmitting ? (
                <Loading className="size-4 text-background" />
              ) : (
                <ArrowUp className="size-[1.1rem]" strokeWidth={3} />
              )}
            </Button>
          </PromptInputAction>
        </PromptInputActions>
      </PromptInput> */}
      <form onSubmit={handleSubmit} className="relative mx-auto h-fit w-full">
        <div
          className={cn(
            "relative rounded-3xl border border-border bg-[#F5F5F5] p-1 dark:bg-background",
            "transition-all duration-100 ease-in-out focus-within:border-primary/15",
          )}
        >
          <textarea
            ref={textareaRef}
            value={content}
            placeholder="What do you want to create today?"
            className="mb-8 max-h-[160px] min-h-[80px] w-full resize-none bg-transparent p-2.5 text-base text-primary shadow-none outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
            rows={1}
            disabled={isSubmitting}
            onChange={(e) => setContent(e.target.value)}
            onClick={handleInputClick}
          />

          <div className="absolute bottom-3 right-3 flex w-full">
            {/* <Button
                type="submit"
                variant={!content.trim() ? "outline" : "default"}
                disabled={!content.trim() || isSubmitting}
                size="icon"
                className={cn(
                  "flex items-center justify-center  text-muted-foreground transition-all duration-300 ease-in-out hover:text-primary",
                  content.trim()
                    ? "bg-primary text-background hover:bg-primary/90 hover:text-background"
                    : "cursor-not-allowed border bg-primary/50 text-primary hover:text-primary",
                )}
              >
                {isSubmitting ? (
                  <Loading className="size-4 text-background" />
                ) : (
                  <ArrowUp className="size-[1.1rem]" strokeWidth={3} />
                )}
              </Button> */}
            <Button
              variant="default"
              size="icon"
              disabled={isSubmitting}
              onClick={handleSubmit}
              className="ml-auto size-7 rounded-md"
            >
              {isSubmitting ? (
                <Loading className="size-4 text-background" />
              ) : (
                <ArrowUp className="size-[1.1rem]" strokeWidth={3} />
              )}
            </Button>
          </div>
        </div>
      </form>

      <div className="relative mx-auto mt-6 flex w-full max-w-sm flex-wrap items-center justify-start gap-2 overflow-hidden sm:max-w-xl md:max-w-3xl">
        <div
          className="group relative flex w-full gap-6 overflow-hidden p-2 md:w-[85%]"
          style={{
            maskImage:
              "linear-gradient(to left, transparent 0%, black 10%, black 90%, transparent 100%)",
          }}
        >
          {Array(5)
            .fill(null)
            .map((index) => (
              <div
                key={index}
                className="flex shrink-0 animate-marquee flex-row justify-around gap-3"
              >
                {SUGGESTIONS.map((suggestion, index) => (
                  <div
                    key={`${suggestion.title}-${suggestion.content.slice(0, 10)}-${index}`}
                    className={cn(
                      "relative overflow-hidden rounded-5xl border border-primary/10 bg-background text-base text-primary transition-all ease-in hover:cursor-pointer",
                    )}
                    onClick={() => setSuggestionWithTypewriter(suggestion.content)}
                  >
                    <AnimatePresence mode="wait" initial={false}>
                      <motion.span
                        key={suggestion.title}
                        transition={{
                          type: "spring",
                          stiffness: 200,
                          damping: 20,
                          duration: 0.2,
                        }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20, transition: { duration: 0.2 } }}
                        className="flex cursor-default items-center justify-center gap-1.5 px-4 py-1.5 text-sm text-primary/80"
                      >
                        <span className="font-semibold text-muted-foreground transition-all duration-500 ease-in-out hover:text-primary">
                          {suggestion.icon}
                        </span>
                        <span className="pr-1 font-semibold text-muted-foreground transition-all duration-500 ease-in-out hover:text-primary">
                          {suggestion.title}
                        </span>
                      </motion.span>
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div
          className={cn(
            "absolute right-0 top-[15%] w-fit cursor-pointer overflow-hidden rounded-5xl border border-primary/10 bg-primary text-base text-background transition-all ease-in",
          )}
          onClick={() =>
            setSuggestionWithTypewriter(
              SURPRISE_SUGGESTION[Math.floor(Math.random() * SURPRISE_SUGGESTION.length)],
            )
          }
        >
          <AnimatePresence mode="wait" initial={false}>
            <motion.span
              key={
                "Create a personal blog with a clean design, blog posts, and social sharing features"
              }
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 20,
                duration: 0.2,
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20, transition: { duration: 0.2 } }}
              className="group flex items-center justify-center gap-1.5 px-4 py-1.5 text-sm"
            >
              <span className="font-semibold transition-all duration-500 ease-in-out">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 109 109"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style={{ stopColor: "#FF6B6B", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#4ECDC4", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#45B7D1", stopOpacity: 1 }} />
                    </linearGradient>
                    <linearGradient id="grad2" x1="0%" y1="100%" x2="100%" y2="0%">
                      <stop offset="0%" style={{ stopColor: "#0D93AB", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#0D93AB", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#DDA0DD", stopOpacity: 1 }} />
                    </linearGradient>
                    <linearGradient id="grad3" x1="100%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style={{ stopColor: "#FD79A8", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#FDCB6E", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#6C5CE7", stopOpacity: 1 }} />
                    </linearGradient>
                    <linearGradient id="grad4" x1="100%" y1="100%" x2="0%" y2="0%">
                      <stop offset="0%" style={{ stopColor: "#A29BFE", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#FD79A8", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#00B894", stopOpacity: 1 }} />
                    </linearGradient>

                    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                      <feDropShadow dx="2" dy="2" stdDeviation="3" floodOpacity="0.3" />
                    </filter>
                  </defs>

                  <g clipPath="url(#clip0_1503_289)" filter="url(#shadow)">
                    <path
                      d="M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z"
                      fill="url(#grad1)"
                    />

                    <path
                      d="M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z"
                      fill="url(#grad2)"
                    />

                    <path
                      d="M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z"
                      fill="url(#grad3)"
                    />

                    <path
                      d="M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z"
                      fill="url(#grad4)"
                    />
                  </g>

                  <defs>
                    <clipPath id="clip0_1503_289">
                      <rect width="109" height="109" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
              <span className="pr-1 font-semibold text-background transition-all duration-500 ease-in-out">
                Surprise me
              </span>
            </motion.span>
          </AnimatePresence>
        </div>
      </div>

      {/* <div className="relative mx-auto my-6 flex w-full max-w-sm items-center justify-start gap-2 overflow-hidden sm:max-w-xl ">
        <div
          className="group relative flex w-full gap-6 overflow-hidden p-2"
          style={{
            maskImage:
              "linear-gradient(to left, transparent 0%, black 10%, black 90%, transparent 100%)",
          }}
        >
          {Array(5)
            .fill(null)
            .map((index) => (
              <div
                key={index}
                className="animate-marquee flex shrink-0 flex-row justify-around gap-3"
              >
                {SUGGESTIONS.map((suggestion, index) => (
                  <div
                    key={`${suggestion.title}-${suggestion.content.slice(0, 10)}-${index}`}
                    className={cn(
                      "relative overflow-hidden rounded-5xl border border-primary/10 bg-background text-base text-primary transition-all ease-in hover:cursor-pointer",
                    )}
                    onClick={() => setSuggestionWithTypewriter(suggestion.content)}
                  >
                    <AnimatePresence mode="wait" initial={false}>
                      <motion.span
                        key={suggestion.title}
                        transition={{
                          type: "spring",
                          stiffness: 200,
                          damping: 20,
                          duration: 0.2,
                        }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20, transition: { duration: 0.2 } }}
                        className="flex cursor-default items-center justify-center gap-1.5 px-4 py-1.5 text-sm text-primary/80"
                      >
                        <span className="font-semibold text-muted-foreground transition-all duration-500 ease-in-out hover:text-primary">
                          {suggestion.icon}
                        </span>
                        <span className="pr-1 font-semibold text-muted-foreground transition-all duration-500 ease-in-out hover:text-primary">
                          {suggestion.title}
                        </span>
                      </motion.span>
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div
          className={cn(
            "absolute right-0 top-[15%] w-fit cursor-pointer overflow-hidden rounded-5xl border border-primary/10 bg-primary text-base text-background transition-all ease-in",
          )}
          onClick={() =>
            setSuggestionWithTypewriter(
              SURPRISE_SUGGESTION[Math.floor(Math.random() * SURPRISE_SUGGESTION.length)],
            )
          }
        >
          <AnimatePresence mode="wait" initial={false}>
            <motion.span
              key={
                "Create a personal blog with a clean design, blog posts, and social sharing features"
              }
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 20,
                duration: 0.2,
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20, transition: { duration: 0.2 } }}
              className="group flex items-center justify-center gap-1.5 px-4 py-1.5 text-sm"
            >
              <span className="font-semibold transition-all duration-500 ease-in-out">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 109 109"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style={{ stopColor: "#FF6B6B", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#4ECDC4", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#45B7D1", stopOpacity: 1 }} />
                    </linearGradient>
                    <linearGradient id="grad2" x1="0%" y1="100%" x2="100%" y2="0%">
                      <stop offset="0%" style={{ stopColor: "#0D93AB", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#0D93AB", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#DDA0DD", stopOpacity: 1 }} />
                    </linearGradient>
                    <linearGradient id="grad3" x1="100%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style={{ stopColor: "#FD79A8", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#FDCB6E", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#6C5CE7", stopOpacity: 1 }} />
                    </linearGradient>
                    <linearGradient id="grad4" x1="100%" y1="100%" x2="0%" y2="0%">
                      <stop offset="0%" style={{ stopColor: "#A29BFE", stopOpacity: 1 }} />
                      <stop offset="50%" style={{ stopColor: "#FD79A8", stopOpacity: 1 }} />
                      <stop offset="100%" style={{ stopColor: "#00B894", stopOpacity: 1 }} />
                    </linearGradient>

                    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                      <feDropShadow dx="2" dy="2" stdDeviation="3" floodOpacity="0.3" />
                    </filter>
                  </defs>

                  <g clipPath="url(#clip0_1503_289)" filter="url(#shadow)">
                    <path
                      d="M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z"
                      fill="url(#grad1)"
                    />

                    <path
                      d="M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z"
                      fill="url(#grad2)"
                    />

                    <path
                      d="M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z"
                      fill="url(#grad3)"
                    />

                    <path
                      d="M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z"
                      fill="url(#grad4)"
                    />
                  </g>

                  <defs>
                    <clipPath id="clip0_1503_289">
                      <rect width="109" height="109" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
              <span className="pr-1 font-semibold text-background transition-all duration-500 ease-in-out">
                Surprise me
              </span>
            </motion.span>
          </AnimatePresence>
        </div>
      </div> */}
    </>
  );
};

export default MessageInput;
