import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/providers/auth-provider";
import { useAdvancedMode } from "@/stores/settings";
import { useSettingsStore } from "@/stores/settings-tab";
import {
  CartSolid,
  CogOneSolid,
  CreditCardSolid,
  Logout,
  UserSolid,
  UsersSolid,
} from "@mynaui/icons-react";
import { Coins, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import Link from "next/link";
import { useTokenInfo } from "../../hooks/use-token-info";

const AccountDropdown = () => {
  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const { getTokenDisplayInfo } = useTokenInfo();
  const tokenInfo = getTokenDisplayInfo();
  const { isAdvancedMode, toggleAdvancedMode } = useAdvancedMode();

  const setSettingsTab = useSettingsStore((state) => state.setSettingsTab);

  const isFreeTier = user?.userFromDb?.plan === "free-tier";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="secondary" size="icon">
          <UserSolid />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-full p-0" align="end">
        <div className="p-2 pb-0.5">
          <DropdownMenuLabel className="mb-1">My Account</DropdownMenuLabel>

          {!isFreeTier && (
            <DropdownMenuItem
              onClick={() => setSettingsTab("billing")}
              className="justify-start gap-3"
            >
              <CreditCardSolid /> Billing
            </DropdownMenuItem>
          )}

          {user.wholesalePlan ? (
            <DropdownMenuItem className="justify-start gap-3" asChild>
              <Link href="/wallet">
                <CartSolid strokeWidth={2} className="h-4 w-4" />
                Wallet
              </Link>
            </DropdownMenuItem>
          ) : (
            <>
              {tokenInfo.remainingTokens && (
                <DropdownMenuItem className="justify-start gap-3">
                  <Coins className="h-4 w-4" strokeWidth={2} />
                  {tokenInfo.remainingTokens} Tokens
                </DropdownMenuItem>
              )}

              <DropdownMenuItem
                onClick={() => setSettingsTab("purchase")}
                className="justify-start gap-3"
              >
                <CartSolid /> Purchase Tokens
              </DropdownMenuItem>
            </>
          )}
        </div>

        <DropdownMenuSeparator />

        <div className="p-1 px-2 pt-0.5">
          <DropdownMenuLabel className="mb-1">Project</DropdownMenuLabel>

          <DropdownMenuItem
            onClick={() => setSettingsTab("project")}
            className="justify-start gap-3"
          >
            <CogOneSolid /> Project
          </DropdownMenuItem>

          <DropdownMenuItem onClick={() => setSettingsTab("team")} className="justify-start gap-3">
            <UsersSolid /> Team
          </DropdownMenuItem>
          <DropdownMenuSeparator className="-mx-2" />

          <DropdownMenuItem
            onClick={() => setTheme(theme === "light" ? "dark" : "light")}
            className="justify-start gap-3"
          >
            {theme === "light" ? (
              <Moon className="h-4 w-4" strokeWidth={2} />
            ) : (
              <Sun className="h-4 w-4" strokeWidth={2} />
            )}

            {theme === "light" ? "Dark Mode" : "Light Mode"}
          </DropdownMenuItem>

          {!isFreeTier && (
            <DropdownMenuItem onClick={toggleAdvancedMode} className="justify-start gap-3">
              <UserSolid className="h-4 w-4" strokeWidth={2} />
              {isAdvancedMode ? "Return to Default Mode" : "Enable Advanced Mode"}
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator className="-mx-2" />

          <DropdownMenuItem className="mb-1" onClick={() => logout()}>
            <span className="flex w-full items-center justify-start gap-3 text-red-500 hover:text-red-600">
              <Logout className="h-4 w-4" strokeWidth={2} />
              Log out
            </span>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AccountDropdown;
