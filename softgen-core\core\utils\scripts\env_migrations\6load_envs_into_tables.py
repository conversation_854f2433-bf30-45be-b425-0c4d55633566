import json
import asyncio
import sys
from pathlib import Path
from sqlalchemy import text
from core.db import Database

MIGRATION_DIR = Path("/root/softgen/env-migration-marko")

async def load_environment_matches():
    try:
        file_path = MIGRATION_DIR / 'environment_matches.json'
        with open(file_path, 'r') as f:
            data = json.load(f)
            if not isinstance(data, dict) or 'matched' not in data:
                raise ValueError("Invalid format in environment_matches.json - missing 'matched' key")
            return data['matched']
    except FileNotFoundError:
        print(f"Error: environment_matches.json file not found at {file_path}")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in environment_matches.json")
        sys.exit(1)

async def update_environments_table(session, matches):
    """Update the environments table with matched environments"""
    
    updated_count = 0
    for env_data in matches.values():
        env_id = env_data['env_id']
        project_id = env_data['project_id']
        github_repo = env_data['github_repo']
        workspace_id = env_data['matching_workspaces'][0]  # Get the first matching workspace
        
        try:
            # First try to insert
            insert_query = text("""
                INSERT INTO environments 
                (env_id, assigned, status, project_id, github_repo, template)
                VALUES 
                (:env_id, TRUE, 'ready', :project_id, :github_repo, :template)
                ON CONFLICT (env_id) DO UPDATE SET
                    assigned = TRUE,
                    status = 'ready',
                    project_id = :project_id,
                    github_repo = :github_repo,
                    template = :template
            """)
            
            result = await session.execute(insert_query, {
                'env_id': env_id,
                'project_id': project_id,
                'github_repo': github_repo,
                'template': 'docker.io/kortixmarko/sg-firebase-nextjs:2.0.5'
            })
            
            updated_count += 1
            print(f"Successfully processed environment {env_id} with workspace {workspace_id}")
            
        except Exception as e:
            print(f"Error processing environment {env_id}: {str(e)}")
            raise
    
    await session.commit()
    print(f"\nSuccessfully processed {updated_count} environment records")
    return updated_count

async def update_projects_table(session, matches):
    """Update the projects table with corresponding env_ids"""
    
    updated_count = 0
    for env_data in matches.values():
        env_id = env_data['env_id']
        project_id = env_data['project_id']
        
        try:
            update_query = text("""
                UPDATE projects 
                SET env_id = :env_id
                WHERE project_id = :project_id
            """)
            
            result = await session.execute(update_query, {
                'env_id': env_id,
                'project_id': project_id
            })
            
            if result.rowcount > 0:
                updated_count += 1
                print(f"Updated project {project_id} with env_id {env_id}")
            else:
                print(f"Warning: Project {project_id} not found in database")
        
        except Exception as e:
            print(f"Error updating project {project_id}: {str(e)}")
            raise
    
    await session.commit()
    print(f"\nSuccessfully updated {updated_count} project records")
    return updated_count

async def main():
    # Initialize database
    db = Database()
    
    try:
        # Load matched environments data
        print("\nLoading environment matches...")
        matches = await load_environment_matches()
        print(f"Loaded {len(matches)} environment matches")
        
        async with db.get_async_session() as session:
            # First update environments table
            print("\nUpdating environments table...")
            env_count = await update_environments_table(session, matches)
            
            # Ask user if they want to update projects table
            while True:
                response = input("\nDo you want to update the projects table with matching env_ids? (yes/no): ").lower()
                if response in ['yes', 'no']:
                    break
                print("Please enter 'yes' or 'no'")
            
            if response == 'yes':
                print("\nUpdating projects table...")
                proj_count = await update_projects_table(session, matches)
                print("\nUpdate completed successfully!")
                print(f"Total environments processed: {env_count}")
                print(f"Total projects updated: {proj_count}")
            else:
                print("\nSkipping projects table update.")
                print(f"Total environments processed: {env_count}")
                
    except Exception as e:
        print(f"\nAn error occurred: {str(e)}")
        sys.exit(1)
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(main())
