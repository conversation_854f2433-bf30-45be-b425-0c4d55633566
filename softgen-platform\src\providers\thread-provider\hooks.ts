import { debug } from "@/lib/debug";
import { webSocketManager } from "@/lib/websocket-manager";
import { WebSocketMessage } from "@/types/thread-message";
import { useCallback, useEffect, useMemo, useRef } from "react";
import { ErrorMessage, NewMessage, SessionStatusMessage } from "./types";

type WebSocketMessageHandler<T = WebSocketMessage> = (message: T) => void;

type Props = {
  onMessage?: WebSocketMessageHandler;
  onComplete?: (content: string) => void;
  setIsAgentRunning: (isRunning: boolean) => void;
  onError?: (message: WebSocketMessage) => void;
  onErrorMessage?: (message: ErrorMessage) => void;
  onSoftgenProcessing?: (isProcessing: boolean) => void;
  setContextLimitReached?: (isLimitReached: boolean) => void;
};

export const useWebSocketManager = (callbacks: Props) => {
  const connectedUrlRef = useRef<string | null>(null);
  const messageHandlerRef = useRef<WebSocketMessageHandler | null>(null);
  const streamingContentRef = useRef<string>("");
  const isMountedRef = useRef(true);

  /**
   * Store callbacks in a ref to maintain stable references across renders.
   * This allows us to:
   * 1. Avoid including callbacks in dependency arrays of useCallback/useEffect
   * 2. Always have access to the latest callback implementations
   * 3. Prevent unnecessary effect re-runs when parent components re-render
   */
  const callbacksRef = useRef(callbacks);

  // Update the ref value whenever callbacks change
  useEffect(() => {
    callbacksRef.current = callbacks;
  }, [callbacks]);

  // Memoize the message handler to prevent unnecessary re-renders
  const handleSingleMessage = useCallback((message: WebSocketMessage) => {
    if (!isMountedRef.current) return;

    // Handle connection status messages
    if (message.type === "connection_status") {
      const statusData = message.data as Record<string, unknown>;
      const status = statusData?.status as string;
      console.debug(`[WebSocket] Connection status changed to: ${status}`);

      if (status === "error") {
        // Notify about connection issues
        callbacksRef.current?.onError?.(message);
      } else if (status === "disconnected") {
        callbacksRef.current?.setIsAgentRunning(false);
      }
      return;
    }

    if (
      message.type === "error" &&
      message.isCompleteMessage &&
      message.termination &&
      typeof message.data === "string" &&
      message.data.includes("Context window is full")
    ) {
      callbacksRef.current?.setContextLimitReached?.(true);
      callbacksRef.current?.onErrorMessage?.(message as ErrorMessage);
      callbacksRef.current?.setIsAgentRunning(false);
      return;
    }

    switch (message.type) {
      case "content":
        if (typeof message.data === "string") {
          if (message.isCompleteMessage) {
            callbacksRef.current?.onComplete?.(streamingContentRef.current);
            streamingContentRef.current = "";
            callbacks.onSoftgenProcessing?.(true);
          } else {
            streamingContentRef.current = message.data;
          }
        }
        break;

      case "session_status": {
        const { status } = message as SessionStatusMessage;
        debug(`Session status changed to: ${status}`);
        callbacksRef.current?.setIsAgentRunning(status === "running");
        break;
      }

      case "stop_session":
        debug("Session stopped");
        callbacksRef.current?.setIsAgentRunning(false);
        break;

      case "error": {
        debug("Session error");
        callbacksRef.current?.onErrorMessage?.(message as ErrorMessage);
        break;
      }

      default:
        callbacksRef.current?.onMessage?.(message);
    }
  }, []);

  // Handle both batched and single messages
  const handleMessage = useCallback(
    (message: WebSocketMessage) => {
      if (!isMountedRef.current) return;

      if (message.type === "batch" && message.batch) {
        message.batch.forEach(handleSingleMessage);
      } else {
        handleSingleMessage(message);
      }
    },
    [handleSingleMessage],
  );

  useEffect(() => {
    isMountedRef.current = true;
    messageHandlerRef.current = handleMessage;
    const unsubscribe = webSocketManager.subscribe(handleMessage);
    const currentUrl = connectedUrlRef.current;

    return () => {
      isMountedRef.current = false;

      console.debug("Cleaning up WebSocket message handler");
      unsubscribe();
      messageHandlerRef.current = null;
      streamingContentRef.current = "";
      if (currentUrl && webSocketManager.isConnected()) {
        console.debug("Disconnecting WebSocket on unmount");
        webSocketManager.disconnect(1000, "Component unmounted");
        connectedUrlRef.current = null;
      }
    };
  }, [handleMessage]);

  const connect = useCallback(async (url: string): Promise<boolean> => {
    try {
      if (webSocketManager.isConnected() && connectedUrlRef.current === url) {
        return true;
      }

      // Disconnect from previous connection if exists
      if (connectedUrlRef.current) {
        await webSocketManager.disconnect(1000, "Reconnecting");
      }

      // Reset state before connecting
      streamingContentRef.current = "";
      await webSocketManager.connect(url);
      connectedUrlRef.current = url;
      return true;
    } catch (error) {
      console.error("WebSocket connection failed:", error);
      connectedUrlRef.current = null;
      return false;
    }
  }, []);

  const disconnect = useCallback((code = 1000, reason = "Normal closure"): void => {
    console.debug("Disconnecting WebSocket...");
    webSocketManager.disconnect(code, reason);

    if (isMountedRef.current) {
      callbacksRef.current?.setIsAgentRunning(false);
      streamingContentRef.current = ""; // Clear streaming content
      connectedUrlRef.current = null;
    }
  }, []);

  const send = useCallback((message: NewMessage): void => {
    if (!isMountedRef.current || !webSocketManager.isConnected()) {
      console.warn("Cannot send message: WebSocket not connected");
      return;
    }
    webSocketManager.send(message);
  }, []);

  return useMemo(
    () => ({
      connect,
      disconnect,
      send,
      isConnected: webSocketManager.isConnected(),
    }),
    [connect, disconnect, send],
  );
};

export function useWebSocketMessage<T = WebSocketMessage>(
  messageType: string,
  callback: WebSocketMessageHandler<T>,
) {
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    const handleMessage = (message: WebSocketMessage) => {
      if (message.type === messageType) {
        callbackRef.current(message as T);
      } else if (message.type === "batch" && message.batch) {
        // Handle batched messages
        message.batch
          .filter((m) => m.type === messageType)
          .forEach((m) => callbackRef.current(m as T));
      }
    };

    const unsubscribe = webSocketManager.subscribe(handleMessage);
    return () => unsubscribe();
  }, [messageType]);
}
