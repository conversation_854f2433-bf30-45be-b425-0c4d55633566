import json
import asyncio
import sys
from pathlib import Path
from sqlalchemy import text
from core.db import Database

MIGRATION_DIR = Path("/root/softgen/env-migration-marko")

async def load_environment_matches():
    """Load the environment matches from JSON"""
    try:
        file_path = MIGRATION_DIR / 'environment_matches.json'
        with open(file_path, 'r') as f:
            data = json.load(f)
            if not isinstance(data, dict) or 'matched' not in data:
                raise ValueError("Invalid format in environment_matches.json - missing 'matched' key")
            return data['matched']
    except FileNotFoundError:
        print(f"Error: environment_matches.json file not found at {file_path}")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in environment_matches.json")
        sys.exit(1)

async def get_projects_with_preview_envs(session):
    """Get all non-deleted projects that have preview-* env_ids"""
    query = text("""
        SELECT project_id, env_id
        FROM projects 
        WHERE project_id NOT LIKE 'deleted_%'
        AND env_id LIKE 'preview-%'
    """)
    
    result = await session.execute(query)
    return result.fetchall()

async def update_links(session, project_id, preview_env_id, workspace_id):
    """Update both projects and environments tables with correct workspace ID"""
    try:
        # Update project's env_id to use the workspace ID
        update_project = text("""
            UPDATE projects
            SET env_id = :workspace_id
            WHERE project_id = :project_id
        """)
        
        project_result = await session.execute(update_project, {
            'workspace_id': workspace_id,
            'project_id': project_id
        })
        
        # Update environment's project_id
        update_env = text("""
            UPDATE environments
            SET 
                project_id = :project_id,
                env_id = :workspace_id
            WHERE env_id = :preview_env_id
        """)
        
        env_result = await session.execute(update_env, {
            'project_id': project_id,
            'workspace_id': workspace_id,
            'preview_env_id': preview_env_id
        })
        
        await session.commit()
        return True
        
    except Exception as e:
        await session.rollback()
        print(f"Error updating links for project {project_id}: {str(e)}")
        return False

async def main():
    db = Database()
    updated_count = 0
    error_count = 0
    failed_updates = []
    
    try:
        # Load environment matches data
        matches = await load_environment_matches()
        
        async with db.get_async_session() as session:
            print("\nFetching projects with preview-* env_ids...")
            projects = await get_projects_with_preview_envs(session)
            total = len(projects)
            print(f"Found {total} projects to process")
            
            for project in projects:
                project_id = project.project_id
                preview_env_id = project.env_id
                
                try:
                    # Find matching data in environment_matches.json
                    if preview_env_id in matches:
                        match_data = matches[preview_env_id]
                        workspace_id = match_data['matching_workspaces'][0]  # Get the first workspace ID
                        
                        print(f"\nProcessing {preview_env_id} for project {project_id}")
                        print(f"Found matching workspace ID: {workspace_id}")
                        
                        # Update both tables with the workspace ID
                        if await update_links(session, project_id, preview_env_id, workspace_id):
                            print(f"Successfully updated links for project {project_id}")
                            print(f"  Project env_id: {preview_env_id} -> {workspace_id}")
                            print(f"  Environment ID updated to: {workspace_id}")
                            updated_count += 1
                        else:
                            error_count += 1
                            failed_updates.append({
                                "project_id": project_id,
                                "preview_env_id": preview_env_id,
                                "error": "Failed to update database"
                            })
                    else:
                        print(f"No matching data found for {preview_env_id}")
                        error_count += 1
                        failed_updates.append({
                            "project_id": project_id,
                            "preview_env_id": preview_env_id,
                            "error": "No matching data found in environment_matches.json"
                        })
                        
                except Exception as e:
                    print(f"Error processing project {project_id}: {str(e)}")
                    error_count += 1
                    failed_updates.append({
                        "project_id": project_id,
                        "preview_env_id": preview_env_id,
                        "error": str(e)
                    })
            
            print("\nUpdate completed!")
            print(f"Successfully updated: {updated_count}/{total} projects")
            if error_count > 0:
                print(f"Errors encountered: {error_count}")
                
                # Save failed updates to JSON
                error_file = MIGRATION_DIR / 'failed_env_links.json'
                with open(error_file, 'w') as f:
                    json.dump({
                        "total_projects": total,
                        "successful_updates": updated_count,
                        "failed_updates": failed_updates
                    }, f, indent=2)
                print(f"Failed updates have been saved to {error_file}")
                
    except Exception as e:
        print(f"\nAn error occurred: {str(e)}")
        sys.exit(1)
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(main()) 