"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import Typography from "@/components/ui/typography";
import { EmptyState } from "@/features/global/empty-state";
import { Layout } from "@/features/global/layout";
import { CACHE_PARAMS } from "@/providers/query-provider";
import { ArrowLeft } from "@mynaui/icons-react";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

type Props = {
  params: Promise<{ slug: string }>;
};

const templates = [
  {
    name: "ChatGPT Clone",
    description: "A very barebone ChatGPT clone with chat history.",
    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
    tags: ["AI", "Chat", "OpenAI"],
    demoUrl: "https://softgen.ai/project/a5303ba5-319f-47ed-80ed-438e0ba8ad3c",
    type: "frontend",
  },
  {
    name: "Blog Starter",
    description: "A modern blog platform with a clean design and essential blogging features.",
    image: "https://images.unsplash.com/photo-1464822759023-fed622ff2c3b",
    tags: ["Blog", "Content", "Publishing"],
    demoUrl: "https://softgen.ai/project/7e6b5a00-59c3-47aa-81b3-279e065f3539",
    type: "frontend",
  },
  {
    name: "Elegance Store",
    description:
      "A modern e-commerce template with product catalog, shopping cart, and wishlist features.",
    image: "https://images.unsplash.com/photo-1454496522488-7a8e488e8606",
    tags: ["E-commerce", "Shopping", "Retail"],
    demoUrl: "https://softgen.ai/project/8707ae2a-bb27-493a-88f4-aa67cb5e6def",
    type: "frontend",
  },
  {
    name: "Job Board",
    description: "A modern job board platform for posting and finding job opportunities.",
    image: "https://images.unsplash.com/photo-1491904768633-2b7e3e7fede5",
    tags: ["Jobs", "Recruitment", "Career"],
    demoUrl: "https://softgen.ai/project/39a53573-b55e-401f-a39d-7ead2c500460",
    type: "fullstack",
  },
  {
    name: "Project Management",
    description:
      "A comprehensive project management solution with task tracking, team collaboration, and progress monitoring.",
    image: "https://images.unsplash.com/photo-1493246507139-91e8fad9978e",
    tags: ["Project Management", "Tasks", "Team Collaboration"],
    demoUrl: "https://softgen.ai/project/31c682bc-3fec-48b8-be1c-8389d39f5311",
    type: "fullstack",
  },
  {
    name: "Digital Marketplace",
    description:
      "A versatile marketplace platform for buying and selling digital products and services.",
    image: "https://images.unsplash.com/photo-1483728642387-6c3bdd6c93e5",
    tags: ["Marketplace", "Digital Products", "E-commerce"],
    demoUrl: "https://softgen.ai/project/bc108914-063e-42e4-b3a2-cffa3b41ca82",
    type: "fullstack",
  },
  {
    name: "SaaS Directory",
    description:
      "A comprehensive directory platform for discovering and comparing software solutions.",
    image: "https://images.unsplash.com/photo-1519681393784-d120267933ba",
    tags: ["SaaS", "Directory", "Software Solutions"],
    demoUrl: "https://softgen.ai/project/20d666a2-8c3e-4ce1-a5b0-90f842eab21c",
    type: "frontend",
  },
];

const Page = (props: Props) => {
  const [slug, setSlug] = useState<string>("");

  useEffect(() => {
    const fetchSlug = async () => {
      const { slug } = await props.params;
      setSlug(slug);
    };
    fetchSlug();
  }, [props.params]);

  const { data: template, isLoading } = useQuery({
    queryKey: ["template", slug],
    queryFn: () => {
      const template = templates.find(
        (template) => template.name.toLowerCase().replaceAll(" ", "-") === slug,
      );
      if (!template) {
        throw new Error("Template not found");
      }
      return template;
    },
    ...CACHE_PARAMS,
  });

  if (!template && !isLoading) {
    return (
      <Layout>
        <main className="mx-auto h-full w-full px-4 py-12">
          <EmptyState
            title="Template Not Found"
            description="The template you're looking for doesn't exist."
            border={false}
            action={
              <Button asChild>
                <Link href="/templates">Back to Templates</Link>
              </Button>
            }
          />
        </main>
      </Layout>
    );
  }

  if (isLoading) {
    return (
      <Layout>
        <main className="h-full w-full px-4 py-12">
          <div className="mx-auto">
            <div className="grid gap-12 lg:grid-cols-2">
              <div className="space-y-8">
                <Skeleton className="h-8 w-24" />

                <div className="mt-8">
                  <div className="mb-4 flex items-center gap-2">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-20" />
                  </div>

                  <Skeleton className="mb-4 h-12 w-3/4" />
                  <Skeleton className="mb-8 h-20 w-full" />

                  <div className="mb-12 flex gap-3">
                    <Skeleton className="h-11 w-32" />
                    <Skeleton className="h-11 w-32" />
                  </div>

                  <div className="space-y-4 border-t pt-4">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-48" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-12">
                <Skeleton className="overflow-hidden rounded-lg border bg-muted">
                  <div className="relative aspect-video w-full" />
                </Skeleton>
              </div>
            </div>
          </div>
        </main>
      </Layout>
    );
  }

  return (
    <Layout>
      <main className="h-full w-full px-4 py-12">
        <div className="mx-auto">
          <div className="grid gap-12 lg:grid-cols-2">
            <div className="space-y-8">
              <Link
                href="/templates"
                className="inline-flex items-center text-sm text-muted-foreground transition-colors hover:text-foreground"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Templates
              </Link>

              <div className="mt-8">
                <div className="mb-4 flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full bg-primary" />
                  <span className="text-sm text-muted-foreground">{template?.type}</span>
                </div>

                <h1 className="mb-4 text-5xl font-bold">{template?.name}</h1>

                <Typography.P className="mb-8">{template?.description}</Typography.P>

                <div className="mb-12 flex gap-3">
                  <Button
                    size="lg"
                    className="w-32"
                    onClick={() => window.open(template?.demoUrl || "", "_blank")}
                  >
                    Start Building
                  </Button>
                  <Button variant="outline" size="lg" className="w-32">
                    Preview Demo
                  </Button>
                </div>

                <div className="space-y-4 border-t pt-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Type</span>
                    <span>{template?.type}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Tags</span>
                    <span>{template?.tags.join(", ")}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Demo URL</span>
                    <span>{template?.demoUrl}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-12">
              <div className="overflow-hidden rounded-lg border bg-muted">
                <div className="relative aspect-video w-full">
                  <Image
                    src={template?.image || "/placeholder.svg"}
                    alt={template?.name || ""}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </Layout>
  );
};

export default Page;
