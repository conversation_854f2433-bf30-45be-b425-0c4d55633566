from daytona import Daytona, DaytonaConfig
import async<PERSON>
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from typing import List

# Initialize the Daytona client
config = DaytonaConfig(
    api_key="dtn_858727beec575bf6d960c089fc4e4e397008e0e6383bd205082ed5de724e1f63",
    server_url="https://daytona.work/api",
    target="us"
)
daytona = Daytona(config)

# List of workspaces to remove
# WORKSPACES_TO_REMOVE = get_duplicate_workspace_ids()
WORKSPACES_TO_REMOVE = ["sandbox-1071", "sandbox-1073", "sandbox-543"]

def remove_workspace(workspace_id: str) -> bool:
    try:
        print(f"Attempting to remove workspace: {workspace_id}")
        # First get the workspace object
        workspace = daytona.get(workspace_id)
        # Then remove it
        daytona.delete(workspace)
        print(f"Successfully removed workspace: {workspace_id}")
        return True
    except Exception as e:
        print(f"Error removing workspace {workspace_id}: {str(e)}")
        return False

async def remove_workspaces(workspace_ids: List[str]):
    successful_removals = []
    failed_removals = []
    
    # Process removals concurrently
    with ThreadPoolExecutor(max_workers=5) as executor:
        # Create tasks for each workspace
        futures = [
            executor.submit(remove_workspace, workspace_id)
            for workspace_id in workspace_ids
        ]
        
        # Process results as they complete
        for workspace_id, future in zip(workspace_ids, futures):
            try:
                success = future.result()
                if success:
                    successful_removals.append(workspace_id)
                else:
                    failed_removals.append(workspace_id)
            except Exception as e:
                print(f"Error processing {workspace_id}: {str(e)}")
                failed_removals.append(workspace_id)
    
    # Print summary
    print("\n=== Removal Summary ===")
    print(f"Total workspaces processed: {len(workspace_ids)}")
    print(f"Successfully removed: {len(successful_removals)}")
    print(f"Failed to remove: {len(failed_removals)}")
    
    if failed_removals:
        print("\nFailed removals:")
        for workspace_id in failed_removals:
            print(f"- {workspace_id}")

async def main():
    print(f"Starting removal of {len(WORKSPACES_TO_REMOVE)} workspaces...")
    await remove_workspaces(WORKSPACES_TO_REMOVE)

if __name__ == "__main__":
    asyncio.run(main())
