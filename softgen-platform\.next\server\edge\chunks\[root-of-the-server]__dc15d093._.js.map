{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import type { NextRequest } from \"next/server\";\r\nimport { NextResponse } from \"next/server\";\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  const path = request.nextUrl.pathname;\r\n  const access_token = request.cookies.get(\"access_token\")?.value;\r\n  const kinde_id = request.cookies.get(\"kinde_id\")?.value;\r\n\r\n  // Detect country from Vercel geolocation headers\r\n  const detectedCountry = request.headers.get(\"x-vercel-ip-country\") || \"US\";\r\n  const currentCountryCookie = request.cookies.get(\"detected_country\")?.value;\r\n\r\n  const privatePathPatterns = [\r\n    /^\\/app($|\\/.*)/,\r\n    /^\\/project($|\\/.*)/,\r\n    /^\\/settings($|\\/.*)/,\r\n    /^\\/profile($|\\/.*)/,\r\n    /^\\/integration($|\\/.*)/,\r\n    /^\\/test\\/[^\\/]+$/,\r\n    /^\\/admin($|\\/.*)/,\r\n  ];\r\n\r\n  const isPrivatePath = privatePathPatterns.some((pattern) => pattern.test(path));\r\n\r\n  if (!access_token && isPrivatePath) {\r\n    return NextResponse.redirect(new URL(\"/\", request.url));\r\n  }\r\n\r\n  if (access_token && [\"/signin\", \"/register\"].includes(path)) {\r\n    return NextResponse.redirect(new URL(\"/app\", request.url));\r\n  }\r\n\r\n  if (access_token && isPrivatePath) {\r\n    if (!kinde_id) {\r\n      return NextResponse.redirect(new URL(\"/pricing\", request.url));\r\n    }\r\n\r\n    try {\r\n      const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/kinde_user/${kinde_id}`;\r\n\r\n      const response = await fetch(apiUrl, {\r\n        cache: \"no-store\",\r\n        headers: {\r\n          Authorization: `Bearer ${access_token}`,\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        mode: \"cors\",\r\n        credentials: \"include\",\r\n      });\r\n\r\n      if (!response.ok) {\r\n        return NextResponse.redirect(new URL(\"/pricing\", request.url));\r\n      }\r\n\r\n      const userData = await response.json();\r\n\r\n      if (!userData.plan && userData.total_free_request <= 0) {\r\n        return NextResponse.redirect(new URL(\"/pricing\", request.url));\r\n      }\r\n    } catch (error: unknown) {\r\n      console.error(\"error\", error);\r\n      // return NextResponse.redirect(new URL(\"/pricing\", request.url));\r\n    }\r\n  }\r\n\r\n  // Set country cookie if not already set or if country has changed\r\n  const response = NextResponse.next();\r\n\r\n  if (!currentCountryCookie || currentCountryCookie !== detectedCountry) {\r\n    response.cookies.set(\"detected_country\", detectedCountry, {\r\n      httpOnly: false, // Allow client-side access\r\n      secure: process.env.NODE_ENV === \"production\",\r\n      sameSite: \"lax\",\r\n      maxAge: 60 * 60 * 24 * 7, // 7 days\r\n    });\r\n  }\r\n\r\n  return response;\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\"/((?!_next|api|static|favicon.ico).*)\"],\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,OAAO,QAAQ,OAAO,CAAC,QAAQ;IACrC,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IAC1D,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,aAAa;IAElD,iDAAiD;IACjD,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC,0BAA0B;IACtE,MAAM,uBAAuB,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB;IAEtE,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,gBAAgB,oBAAoB,IAAI,CAAC,CAAC,UAAY,QAAQ,IAAI,CAAC;IAEzE,IAAI,CAAC,gBAAgB,eAAe;QAClC,OAAO,4SAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,IAAI,gBAAgB;QAAC;QAAW;KAAY,CAAC,QAAQ,CAAC,OAAO;QAC3D,OAAO,4SAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,QAAQ,GAAG;IAC1D;IAEA,IAAI,gBAAgB,eAAe;QACjC,IAAI,CAAC,UAAU;YACb,OAAO,4SAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,YAAY,QAAQ,GAAG;QAC9D;QAEA,IAAI;YACF,MAAM,SAAS,6DAAwC,YAAY,EAAE,UAAU;YAE/E,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,OAAO;gBACP,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,cAAc;oBACvC,gBAAgB;gBAClB;gBACA,MAAM;gBACN,aAAa;YACf;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,OAAO,4SAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,YAAY,QAAQ,GAAG;YAC9D;YAEA,MAAM,WAAW,MAAM,SAAS,IAAI;YAEpC,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,kBAAkB,IAAI,GAAG;gBACtD,OAAO,4SAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,YAAY,QAAQ,GAAG;YAC9D;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,SAAS;QACvB,kEAAkE;QACpE;IACF;IAEA,kEAAkE;IAClE,MAAM,WAAW,4SAAA,CAAA,eAAY,CAAC,IAAI;IAElC,IAAI,CAAC,wBAAwB,yBAAyB,iBAAiB;QACrE,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB,iBAAiB;YACxD,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK;QACzB;IACF;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAwC;AACpD"}}]}