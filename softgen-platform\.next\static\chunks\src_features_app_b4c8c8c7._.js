(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/features/app/welcome-modal.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_530baddd._.js",
  "static/chunks/src_features_app_welcome-modal_tsx_56052c93._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/welcome-modal.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/features/app/modal/create-project-modal.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_e13b46cf._.js",
  "static/chunks/node_modules__pnpm_b8e83ebc._.js",
  "static/chunks/src_features_app_modal_create-project-modal_tsx_56052c93._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/modal/create-project-modal.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/features/app/projects/project-card-setting-modal.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_59bae4a0._.js",
  "static/chunks/src_ca27681a._.js",
  "static/chunks/src_features_app_projects_project-card-setting-modal_tsx_56052c93._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/projects/project-card-setting-modal.tsx [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/features/app/settings.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_6dc574ec._.js",
  "static/chunks/8a1dc_zod_dist_esm_493ac589._.js",
  "static/chunks/b2692_react-icons_fa_index_mjs_290cc807._.js",
  "static/chunks/b2692_react-icons_lib_d4addfaa._.js",
  "static/chunks/node_modules__pnpm_250fde20._.js",
  "static/chunks/src_features_app_settings_tsx_56052c93._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/settings.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);