import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/classic/dropdown-menu";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import Hint from "@/components/ui/hint";
import Loading from "@/components/ui/loading";
import { MessageMarkdown } from "@/components/ui/message-markdown";
import Typography from "@/components/ui/typography";
import { errorToast, loadingToast, successToast } from "@/features/global/toast";
import { useCopy } from "@/hooks/use-copy";
import { revertGithubCommit } from "@/lib/api";
import { cn } from "@/lib/utils";
import { parseCommunication } from "@/lib/xml-parser";
import { useProject } from "@/providers/project-provider";
import { DotsVertical, Refresh } from "@mynaui/icons-react";
import { useMutation } from "@tanstack/react-query";
import { memo, useCallback, useMemo, useState } from "react";
import { LuCopy } from "react-icons/lu";
import { formatTimestamp } from "../../../../lib/format-timestamp";
import SoftgenIcon from "../softgen-icon";
import { CollapsibleXmlContent } from "./collapsible-content/content/content";
import { extractToolResult, parseContent } from "./collapsible-content/utils";
import ReasoningContent from "./reasoning-content";
import SupabaseStatusCard from "./supabase-status-card";
import ToolResult from "./tool-result";
import { MessageProps } from "./types";

const TOOL_CALL_REGEX = /<(tool_call|function_call)/;

const TOKEN_THRESHOLDS = [
  {
    min: 0,
    max: 80000,
    color: "bg-green-500",
    label: "Safe",
    message: "Context is in the safe range",
  },
  {
    min: 80000,
    max: 99000,
    color: "bg-yellow-500",
    label: "Warning!",
    message: "High context usage - think about closing files, or preparing to start a new thread",
  },
  {
    min: 99000,
    max: 119000,
    color: "bg-orange-500",
    label: "Alert!",
    message:
      "Context pressure rising; AI output quality may degrade. Close unnecessary files now, or start a new thread",
  },
  {
    min: 119000,
    max: Infinity,
    color: "bg-red-500",
    label: "Danger",
    message:
      "Context window likely too full — AI may hallucinate or break. Strongly recommend closing files or starting a new task/thread.",
  },
] as const;

const TokenIndicator = memo(
  ({ tokens, hasFailedActions, short }: { tokens: number; hasFailedActions: boolean; short?: boolean }) => {
    const indicator = useMemo(() => {
      if (hasFailedActions) {
        return {
          color: "bg-green-500",
          label: "Auto Fix",
          message: "No tokens charged due to failed actions.",
        };
      }
      return (
        TOKEN_THRESHOLDS.find((threshold) => tokens >= threshold.min && tokens <= threshold.max) ||
        TOKEN_THRESHOLDS[TOKEN_THRESHOLDS.length - 1]
      );
    }, [tokens, hasFailedActions]);

    const formattedMessage = useMemo(() => {
      if (hasFailedActions) {
        return `${indicator.label} - ${indicator.message}`;
      }
      const separator = indicator.label === "Safe" ? " - " : " ";
      let msg = `${indicator.label}${separator}${indicator.message}`;
      if (short) {
        msg += ` (${tokens} tokens)`;
      }
      return msg;
    }, [indicator, hasFailedActions, short]);

    return (
      <Hint
        side="bottom"
        sideOffset={-2}
        align="end"
        content={
          <div className="flex flex-col gap-2">
            <Typography.P className="mt-0 text-sm text-inherit">{formattedMessage}</Typography.P>
          </div>
        }
      >
        <div className="flex h-7 cursor-default items-center gap-1.5 text-sm font-medium text-primary">
          <div className="relative">
            <div className={`size-2.5 rounded-full ${indicator.color} animate-pulse`} />
            <div
              className={`absolute inset-0 rounded-full ${indicator.color} opacity-50 blur-sm`}
            />
          </div>
          <p className="text-sm font-medium text-primary transition-all duration-300 ease-in-out group-hover:text-primary/80">
            {hasFailedActions ? "Free" : tokens === 0 ? "Auto Fix" : short ? null : tokens.toString()}
          </p>
        </div>
      </Hint>
    );
  },
);

TokenIndicator.displayName = "TokenIndicator";

const CostIndicator = memo(
  ({ cost }: { cost: { input_cost: number; output_cost: number; total_cost: number } }) => {
    return (
      <Hint
        side="bottom"
        sideOffset={-2}
        align="end"
        content={
          <div className="flex flex-col gap-2">
            <Typography.P className="mt-0 text-sm text-inherit">
              Input: ${cost.input_cost}
            </Typography.P>
            <Typography.P className="mt-0 text-sm text-inherit">
              Output: ${cost.output_cost}
            </Typography.P>
          </div>
        }
      >
        <div className="flex h-7 cursor-default items-center gap-1.5 text-sm font-medium text-primary">
          <p className="text-sm font-medium text-primary transition-all duration-300 ease-in-out group-hover:text-primary/80">
            {Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(cost.total_cost)}
          </p>
        </div>
      </Hint>
    );
  },
);

CostIndicator.displayName = "CostIndicator";

const RevertBtn = ({
  message,
  actions,
}: {
  message: MessageProps["message"];
  actions: MessageProps["actions"];
}) => {
  const { copy } = useCopy();
  const { projectId } = useProject();
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);

  const parsedSections = useMemo(() => {
    return parseContent(message.content as string, actions, message.failed_actions);
  }, [actions, message.content, message.failed_actions]);

  const revertCommitFn = useMutation({
    mutationFn: async (): Promise<void> => {
      await loadingToast(
        "Reverting commit...",
        revertGithubCommit(projectId, message.commit_hash || ""),
        {},
        "Commit reverted",
      );
    },
    onError: (error) => {
      console.error("Error reverting commit", error);
      errorToast("Failed to revert commit", {
        description: error.message,
      });
    },
  });

  const onRevertClick = useCallback(() => {
    setShowDeleteAlert(false);
    revertCommitFn.mutate();
  }, [revertCommitFn]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="size-7">
          <DotsVertical className="size-6 rotate-90" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          className="group flex items-center justify-between gap-2 px-2.5"
          onClick={() =>
            copy(parsedSections[0]?.type === "communication" ? parsedSections[0].content : "")
              .then(() => {
                successToast("Copied to clipboard");
              })
              .catch(() => {
                errorToast("Failed to copy");
              })
          }
        >
          Copy
          <LuCopy className="h-4 w-4 text-primary/85 group-hover:text-primary" />
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setShowDeleteAlert(true)}
          className="group flex items-center justify-between gap-2 px-2.5"
        >
          Revert
          <Refresh className="h-4 w-4 text-primary/85 group-hover:text-primary" />
        </DropdownMenuItem>
      </DropdownMenuContent>
      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to revert this commit?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will revert the commit to the previous state.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex w-full flex-row gap-2 md:gap-0">
            <Button
              variant="outline"
              className="w-full md:w-fit"
              disabled={revertCommitFn.isPending}
              onClick={() => setShowDeleteAlert(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              className="w-full md:w-fit"
              onClick={onRevertClick}
              disabled={revertCommitFn.isPending}
            >
              {revertCommitFn.isPending ? (
                <>
                  <Loading className="size-4 animate-spin" />
                  Reverting...
                </>
              ) : (
                "Revert"
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DropdownMenu>
  );
};

export const AssistantMessage = ({ actions, message }: MessageProps) => {
  const { project } = useProject();
  const hasToolCalls =
    message.hasToolCalls === true ||
    (typeof message.content === "string" && TOOL_CALL_REGEX.test(message.content));

  const toolresult = useMemo(() => extractToolResult(message.content as string), [message.content]);

  const communication = useMemo(
    () => parseCommunication(message.content as string)?.communication,
    [message.content],
  );

  return (
    <div className="group flex w-full flex-col space-y-2 pb-10 pt-0">
      <div className="flex w-full flex-col items-start">
        <div className="mb-1.5 flex w-full items-center justify-between gap-2">
          <div className="flex items-center gap-4">
            <SoftgenIcon />

            {message.timestamp && (
              <p className="hidden text-sm text-muted-foreground transition-all duration-1000 ease-in-out group-hover:flex">
                {formatTimestamp(message.timestamp)}
              </p>
            )}
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4">
              {message.agent_cost !== undefined && <CostIndicator cost={message.agent_cost} />}

              {message.total_tokens !== undefined && (
                <TokenIndicator
                  short={message.agent_cost !== undefined}
                  tokens={message.total_tokens}
                  hasFailedActions={!!(message.failed_actions && message.failed_actions.length > 0)}
                />
              )}
            </div>

            {message.commit_hash && <RevertBtn message={message} actions={actions} />}
          </div>
        </div>

        <div
          className={cn(
            "prose dark:prose-invert prose-p:whitespace-break-spaces prose-code:m-0 prose-code:whitespace-break-spaces prose-pre:m-0 prose-pre:w-full prose-pre:p-0 dark:prose-pre:bg-secondary w-full max-w-full select-text gap-1 overflow-x-visible text-wrap break-words py-0.5 font-normal",
            message.isStreaming && "animate-pulse",
            hasToolCalls && "bg-muted/50",
          )}
        >
          {message.reasoning_content && <ReasoningContent content={message.reasoning_content} />}

          {typeof message.content === "string" && (
            <>
              {message.content.includes("<") && message.content.includes(">") ? (
                <>
                  {communication && <MessageMarkdown>{communication}</MessageMarkdown>}

                  <CollapsibleXmlContent
                    content={message.content}
                    actions={actions}
                    failedActions={message.failed_actions}
                  />
                </>
              ) : toolresult ? (
                <>
                  <ToolResult toolresult={toolresult} />
                </>
              ) : (
                <MessageMarkdown>{message.content}</MessageMarkdown>
              )}
            </>
          )}

          {!project?.isSupabaseConnected &&
            typeof message.content === "string" &&
            message.content.toLowerCase().includes("<supabase_connected>") && (
              <SupabaseStatusCard />
            )}
        </div>
      </div>
    </div>
  );
};
