import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Disclosure, DisclosureContent, DisclosureTrigger } from "@/components/ui/disclosure";
import { Form, FormField, FormItem } from "@/components/ui/form";
import Loading from "@/components/ui/loading";
import { LazyModal, Modal, ModalContent, ModalHeader, ModalTitle } from "@/components/ui/modal";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Typography from "@/components/ui/typography";
import { errorToast, successToast } from "@/features/global/toast";
import { vercelDomainStatus } from "@/lib/api";
import { formatTimestamp } from "@/lib/format-timestamp";
import { useProject } from "@/providers/project-provider";
import { CalendarSolid, ChevronDown, Globe } from "@mynaui/icons-react";
import { useMutation } from "@tanstack/react-query";
import Link from "next/link";
import { memo, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { publishProjectSchema } from "../type";
import AddOwnVercelTokenModal from "./add-own-vercel-token";
import CustomDomainModal from "./custom-domain-modal";

const ensureHttps = (url: string | null): string => {
  if (!url) return "#";
  return url.startsWith("http") ? url : `https://${url}`;
};

const getDisplayUrl = (url: string | null): string => {
  if (!url) return "";
  return url.replace(/^https?:\/\//, "");
};

export const DeployedProjectView = memo(
  ({
    form,
    onUnlink,
    onRedeploy,
    invalidateQueries,
    setStartDeployment,
    isRemovingDomain,
    open,
    onOpenChange,
    isRedeploying,
    setIsRedeploying,
    setIsRedeploy,
    vercelToken,
  }: {
    form: UseFormReturn<z.infer<typeof publishProjectSchema>>;
    onUnlink: () => void;
    onRedeploy: () => void;
    invalidateQueries: () => void;
    setStartDeployment: (startDeployment: boolean) => void;
    isRemovingDomain: boolean;
    open: boolean;
    onOpenChange: (open: boolean) => void;
    isRedeploying: boolean;
    setIsRedeploying: (isRedeploying: boolean) => void;
    setIsRedeploy: (isRedeploy: boolean) => void;
    vercelToken: string | null;
  }) => {
    const { project } = useProject();
    const [showCustomDomainForm, setShowCustomDomainForm] = useState(false);
    const [showAddOwnVercelTokenForm, setShowAddOwnVercelTokenForm] = useState(false);
    const [alertRemoveDomain, setAlertRemoveDomain] = useState(false);
    const [showDomainConfiguration, setShowDomainConfiguration] = useState(true);

    const displayUrl =
      project?.deployment?.production_url || project?.deployment?.deployment_url || null;

    const domainStatusMutation = useMutation({
      mutationFn: async (domain: string) => {
        if (!project?.project_id) {
          throw new Error("Project ID is required");
        }
        const useCustomToken = form.getValues("ownVercelDeployment");
        return vercelDomainStatus(project.project_id, domain, useCustomToken ? vercelToken : null);
      },
      onSuccess: (data) => {
        if (data.success) {
          if (data.status === "verified") {
            successToast("Domain Verified", {
              description: "Domain is properly configured and verified.",
            });
          } else if (data.status === "not_found") {
            errorToast("Domain Not Found", {
              description: data.message || "Domain not found in your Vercel account.",
            });
          } else {
            successToast("Domain Status Retrieved", {
              description: data.message || "Domain status information has been loaded.",
            });
          }
        } else {
          errorToast("Domain Status Check Failed", {
            description: data.message || "Failed to check domain status.",
          });
        }
      },
      onError: (error: Error) => {
        errorToast("Error", {
          description: `Failed to check domain status: ${error.message}`,
        });
      },
    });

    const handleCheckDomainStatus = async (domainOverride: string | null) => {
      const domainToCheck = domainOverride || getDisplayUrl(displayUrl);
      if (!domainToCheck) {
        errorToast("Missing Information", {
          description: "Please enter a domain name.",
        });
        return;
      }

      // Basic domain validation
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
      if (!domainRegex.test(domainToCheck)) {
        errorToast("Invalid Domain", {
          description: "Please enter a valid domain name (e.g., example.com).",
        });
        return;
      }

      domainStatusMutation.mutate(domainToCheck);
    };

    return (
      <Modal open={open} onOpenChange={onOpenChange}>
        <ModalContent className="lg:max-w-3xl">
          <ModalHeader>
            <ModalTitle>Deployed Project</ModalTitle>
          </ModalHeader>

          <div className="flex w-full flex-col gap-4 px-6 py-4">
            <div className="w-full space-y-3">
              {project?.deployment?.production_url ? (
                <Disclosure
                  open={showDomainConfiguration}
                  onOpenChange={setShowDomainConfiguration}
                >
                  <DisclosureTrigger>
                    <div className="flex w-full items-center justify-between gap-1 py-3">
                      <div className="flex flex-col gap-0">
                        <Typography.H5>Connected Domain</Typography.H5>
                        <Typography.P className="mt-0">View connected domain.</Typography.P>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          <Globe className="size-4 text-muted-foreground" />
                          <Button variant="link" className="p-0" asChild>
                            <Link
                              href={ensureHttps(displayUrl)}
                              target="_blank"
                              className="text-sm text-primary"
                            >
                              {getDisplayUrl(displayUrl)}
                            </Link>
                          </Button>
                        </div>

                        <ChevronDown />
                      </div>
                    </div>
                  </DisclosureTrigger>

                  <DisclosureContent>
                    <div className="flex w-full items-center justify-between pb-3">
                      <div className="w-full space-y-3 rounded-md border bg-background p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex flex-col gap-0">
                            <Typography.H5>Current Status</Typography.H5>
                            <Typography.P className="mt-0">
                              {project?.deployment?.production_url
                                ? "Domain is properly configured and verified."
                                : "Domain is not properly configured and verified."}
                            </Typography.P>
                          </div>

                          <Button
                            size="sm"
                            onClick={() => handleCheckDomainStatus(null)}
                            disabled={domainStatusMutation.isPending}
                          >
                            {domainStatusMutation.isPending ? (
                              <>
                                <Loading className="mr-2 size-4" />
                                Checking...
                              </>
                            ) : (
                              "Check Status"
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="flex w-full items-center justify-between pb-3">
                      <div className="w-full space-y-3 rounded-md border bg-background p-4">
                        <Typography.H5>Domain Configuration Steps</Typography.H5>
                        <Tabs defaultValue="vercel" className="w-full">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="vercel">Vercel Dashboard</TabsTrigger>
                            <TabsTrigger value="manual">Manual DNS</TabsTrigger>
                          </TabsList>

                          <TabsContent value="vercel">
                            <ol className="list-inside list-decimal space-y-2 text-sm text-muted-foreground">
                              <li>Go to your Vercel project settings</li>
                              <li>
                                Click on <span className="font-bold text-primary">Domains</span> in
                                the left sidebar
                              </li>
                              <li>Find your domain in the list</li>
                              <li>
                                Click on{" "}
                                <span className="font-bold text-primary">
                                  View DNS Configuration
                                </span>
                              </li>
                              <li>Follow the DNS configuration instructions provided by Vercel</li>
                              <li>Wait for DNS propagation (can take up to 48 hours)</li>
                            </ol>
                          </TabsContent>

                          <TabsContent value="manual">
                            <ol className="list-inside list-decimal space-y-2 text-sm text-muted-foreground">
                              <li>Add these DNS records to your domain provider:</li>
                              <div className="ml-6 mt-2 rounded bg-muted p-3 font-mono text-sm">
                                <p>Type: A</p>
                                <p>Name: @</p>
                                <p>Value: 76.76.21.21</p>
                              </div>
                              <div className="ml-6 mt-2 rounded bg-muted p-3 font-mono text-sm">
                                <p>Type: CNAME</p>
                                <p>Name: www</p>
                                <p>Value: cname.vercel-dns.com</p>
                              </div>
                              <li>Wait for DNS propagation (can take up to 48 hours)</li>
                              <li>
                                Your domain will automatically be verified once DNS is propagated
                              </li>
                            </ol>
                          </TabsContent>
                        </Tabs>
                      </div>
                    </div>
                  </DisclosureContent>
                </Disclosure>
              ) : (
                <div className="flex w-full items-center justify-between gap-1 py-3">
                  <div className="flex flex-col gap-0">
                    <Typography.H5>Connected Domain</Typography.H5>
                    <Typography.P className="mt-0">View connected domain.</Typography.P>
                  </div>

                  <div className="flex items-center gap-2">
                    <Globe className="size-4 text-muted-foreground" />
                    <Button variant="link" className="p-0" asChild>
                      <Link
                        href={ensureHttps(displayUrl)}
                        target="_blank"
                        className="text-sm text-primary"
                      >
                        {getDisplayUrl(displayUrl)}
                      </Link>
                    </Button>
                  </div>
                </div>
              )}

              <div className="flex w-full items-center justify-between pb-3">
                <div className="flex flex-col gap-0">
                  <Typography.H5>Last Deployed</Typography.H5>
                  <Typography.P className="mt-0">View last deployed version.</Typography.P>
                </div>

                <div className="flex items-center gap-2">
                  <CalendarSolid className="size-4 text-muted-foreground" />
                  <span className="text-sm text-primary">
                    {project?.deployment?.last_deployed
                      ? formatTimestamp(project.deployment.last_deployed, "deployed")
                      : "Never"}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between gap-1 pb-3">
                <div className="flex flex-col gap-0">
                  <Typography.H5>Custom Domain</Typography.H5>
                  <Typography.P className="mt-0">Connect a domain you already own.</Typography.P>
                </div>

                <Button size="sm" onClick={() => setShowCustomDomainForm(true)}>
                  Connect Domain
                </Button>
              </div>

              <Form {...form}>
                <>
                  <FormField
                    name="ownVercelDeployment"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between gap-1 space-y-1 pb-3">
                          <div className="flex flex-col gap-0">
                            <Typography.H5>Use own Vercel account</Typography.H5>
                            <Typography.P>
                              Use your own Vercel account to deploy your project.
                            </Typography.P>
                          </div>

                          <Switch
                            checked={field.value}
                            onCheckedChange={(value) => {
                              field.onChange(value);
                              if (value) {
                                setShowAddOwnVercelTokenForm(true);
                              }
                            }}
                          />
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    name="pushEnvBeforeDeploy"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between gap-1 space-y-1 pb-3">
                          <div className="flex flex-col gap-0">
                            <Typography.H5>Push Environment Variables Before Deploy</Typography.H5>
                            <Typography.P>
                              Push environment variables before deploying your project.
                            </Typography.P>
                          </div>

                          <Switch
                            checked={field.value}
                            onCheckedChange={(value) => {
                              field.onChange(value);
                            }}
                          />
                        </div>
                      </FormItem>
                    )}
                  />
                </>
              </Form>

              <div className="flex items-center justify-end gap-2 py-3">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    if (isRedeploying) {
                      errorToast("Please wait to redeploy");
                      return;
                    }
                    setAlertRemoveDomain(true);
                  }}
                  disabled={isRemovingDomain}
                  className="h-8"
                >
                  {isRemovingDomain ? (
                    <>
                      <Loading className="size-4" />
                      <span className="text-sm font-medium">Removing...</span>
                    </>
                  ) : (
                    "Delete Deployment"
                  )}
                </Button>

                <Button
                  size="sm"
                  className="h-8"
                  onClick={() => {
                    onOpenChange(false);
                    setStartDeployment(true);
                    setIsRedeploying(true);
                    setIsRedeploy(true);
                    onRedeploy();
                  }}
                  disabled={isRedeploying}
                >
                  {isRedeploying ? (
                    <>
                      <Loading className="size-4 text-background" />
                      <span className="text-sm font-medium">Redeploying...</span>
                    </>
                  ) : (
                    "Redeploy"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </ModalContent>

        <LazyModal open={showCustomDomainForm} onOpenChange={setShowCustomDomainForm}>
          <CustomDomainModal
            open={showCustomDomainForm}
            setShowModal={setShowCustomDomainForm}
            invalidateQueries={invalidateQueries}
          />
        </LazyModal>

        <LazyModal
          open={showAddOwnVercelTokenForm}
          onOpenChange={(open) => {
            setShowAddOwnVercelTokenForm(open);
            if (!open) {
              const token = form.getValues("vercelToken");
              if (!token) {
                form.setValue("ownVercelDeployment", false);
              }
            }
          }}
        >
          <AddOwnVercelTokenModal
            open={showAddOwnVercelTokenForm}
            setShowModal={setShowAddOwnVercelTokenForm}
            projectForm={form}
          />
        </LazyModal>

        {/* Unlink Confirmation Dialog */}
        <AlertDialog open={alertRemoveDomain} onOpenChange={setAlertRemoveDomain}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Unlink</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to unlink this project from Vercel? This action cannot be
                undone. Your deployment will remain active but will no longer be connected to this
                project.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex items-center justify-end">
              <AlertDialogCancel
                className="w-full md:w-fit"
                onClick={() => {
                  setAlertRemoveDomain(false);
                }}
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => onUnlink()}
                className="h-8 w-full bg-destructive text-destructive-foreground hover:bg-destructive/90 md:w-fit"
              >
                {isRemovingDomain ? "Removing..." : "Remove"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Modal>
    );
  },
);

DeployedProjectView.displayName = "DeployedProjectView";
