import { <PERSON><PERSON> } from "@/components/ui/button";
import { CardContent } from "@/components/ui/card";
import Loading from "@/components/ui/loading";
import { LazyModal, Modal, ModalContent } from "@/components/ui/modal";
import { SandboxSleepingCard } from "@/components/ui/sandbox-sleeping-card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useIsMobile } from "@/hooks/use-mobile";
import { useThread } from "@/hooks/use-threads";
import { debug } from "@/lib/debug";
import { cn } from "@/lib/utils";
import { useProject } from "@/providers/project-provider";
import { useCurrentThreadStore, useSubscribeToAgentRunning } from "@/stores/current-thread";
import { useNavigateFile } from "@/stores/navigate-file";
import { ExternalLink, Plus } from "@mynaui/icons-react";
import { RotateCcw } from "lucide-react";
import { lazy, useCallback, useEffect, useRef, useState } from "react";
import { useShallow } from "zustand/react/shallow";
import Hint from "../../components/ui/hint";
import CodeEditor from "../code-editor/code-editor";
import MessageInput from "../thread/message-input";
import ThreadContainer from "../thread/thread-container";
import ThreadHistory from "../thread/thread-history";
import { BugFinder } from "./bug-finder";
import LoadingPreview from "./loading-preview";
import SandboxUnavailable from "./modal/contact-support-modal";
import SupabaseSheet from "./modal/supabase-sheet";

const RenameModalContent = lazy(() => import("../thread/rename-modal"));

interface AppError extends Error {
  id?: string;
  timestamp?: string | Date;
  pageUrl?: string;
  type?: string;
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  details?: any;
}

type Props = {
  projectId: string;
};

const useErrors = () => {
  const [errors, setErrors] = useState<AppError[]>([]);

  const handleFixError = (error: unknown) => {
    setErrors((prev) => prev.filter((e) => e !== (error as AppError)));
  };

  return { errors, setErrors, handleFixError };
};

const MobileView = ({ projectId }: Props) => {
  const [currentPage, setCurrentPage] = useState("/");
  const [iframeUrl, setIframeUrl] = useState("");
  const isMobile = useIsMobile();
  const [isIframeLoading, setIsIframeLoading] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const [showSupabaseSheet, setShowSupabaseSheet] = useState(false);

  const [, setShowLongPreviewWarning] = useState(false);
  const previewWarningTimer = useRef<number>(null);

  const { project, isProjectLoading, sandboxState } = useProject();
  const { createThreadFn } = useThread();
  const { currentThreadId, isAgentRunning } = useCurrentThreadStore(
    useShallow((state) => ({
      currentThreadId: state.id,
      isAgentRunning: state.isAgentRunning,
    })),
  );
  const { activeTab, setActiveTab, showDrawer, setShowDrawer } = useNavigateFile(
    useShallow((state) => ({
      activeTab: state.activeTab,
      setActiveTab: state.setActiveTab,
      showDrawer: state.showDrawer,
      setShowDrawer: state.setShowDrawer,
    })),
  );

  const [threadToRename, setThreadToRename] = useState<{ id: number; name: string } | null>(null);

  const { errors, setErrors } = useErrors();

  const filteredRoutes =
    project?.frontend_page_routes?.filter((route: string) => !route.includes("/api/")) || [];

  const isCreatingThread = createThreadFn.isPending;

  const handleCreateThread = () => {
    createThreadFn.mutate();
  };

  useEffect(() => {
    if (project?.env_id) {
      const baseUrl = `${project?.env_url}`;
      const newUrl = `${baseUrl}${currentPage}`;

      setIsIframeLoading(sandboxState === "starting");

      if (project.isRunning === 0) {
        return;
      }

      if (newUrl !== iframeUrl) {
        setIsIframeLoading(true);
        setIframeUrl(newUrl);
      }
    }
  }, [project, currentPage, iframeUrl, sandboxState]);

  const handleRouteChange = (route: string) => {
    const cleanedRoute = route.includes("?") ? route.split("?")[0] : route;
    setCurrentPage(cleanedRoute);

    const now = Date.now();
    setErrors((prev: AppError[]) =>
      prev.map((error: AppError) => {
        if (
          error.timestamp &&
          now - new Date(error.timestamp).getTime() < 2000 &&
          error.pageUrl !== "/404"
        ) {
          return {
            ...error,
            pageUrl: cleanedRoute,
          };
        }
        return error;
      }),
    );
  };

  const handleIframeLoad = () => {
    // Only mark as loaded if we have a valid iframe URL
    if (iframeUrl) {
      // setIsIframeLoading(false);

      // Inject error monitoring script if needed
      if (iframeRef.current && iframeRef.current.contentWindow) {
        try {
          // Check if we can access the iframe content (same-origin policy)
          const iframeDocument = iframeRef.current.contentWindow.document;
          if (!iframeDocument || !iframeDocument.head) {
            debug("[Alert] Cannot access iframe document or head");
            return;
          }

          const scriptExists = iframeDocument.querySelector(
            'script[src*="cdn.softgen.ai/script.js"]',
          );

          if (!scriptExists) {
            const script = iframeDocument.createElement("script");
            script.src = "https://cdn.softgen.ai/script.js";
            script.async = true;
            if (iframeDocument.head) {
              iframeDocument.head.appendChild(script);
              debug("Injected error monitoring script into iframe");
            }
          }
        } catch (error) {
          // This is expected for cross-origin iframes
          debug("Cannot access iframe content due to same-origin policy", error);
        }
      }
    }
  };

  useEffect(() => {
    if (iframeRef.current) {
      iframeRef.current.style.display = activeTab === "preview" ? "block" : "none";
    }
  }, [activeTab]);

  useEffect(() => {
    const handleIframeMessage = (event: MessageEvent) => {
      if (!iframeUrl || !event.data || typeof event.data !== "object") return;

      try {
        if (event.data.type === "MONITOR_SCRIPT_LOADED") {
          setIsIframeLoading(false);
          return;
        }

        if (
          [
            "RUNTIME_ERROR",
            "CONSOLE_OUTPUT",
            "NETWORK_REQUEST",
            "RESOURCE_ERROR",
            "UNHANDLED_PROMISE_REJECTION",
          ].includes(event.data.type)
        ) {
          debug("Received error from iframe:", event.data);

          let newError: AppError = {
            id: `iframe-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
            timestamp: new Date(),
            pageUrl: currentPage,
            name: "Error",
            message: "Unknown error",
          };

          if (event.data.type === "RUNTIME_ERROR") {
            newError = {
              ...newError,
              message: event.data.error.message || "Unknown runtime error",
              stack: event.data.error.stack,
              name: "RuntimeError",
              type: "runtime",
            };
          } else if (
            event.data.type === "CONSOLE_OUTPUT" &&
            (event.data.level === "error" || event.data.level === "warning")
          ) {
            newError = {
              ...newError,
              message: event.data.message || "Console error",
              name: "ConsoleError",
              type: "console",
            };
          } else if (event.data.type === "NETWORK_REQUEST" && event.data.request) {
            if (event.data.request.status >= 400 || event.data.request.error) {
              newError = {
                ...newError,
                message:
                  event.data.request.error?.message ||
                  `${event.data.request.method} ${event.data.request.url} ${event.data.request.status} (${event.data.request.statusText})`,
                name: "NetworkError",
                type: "network",
                details: {
                  url: event.data.request.url,
                  status: event.data.request.status,
                  method: event.data.request.method,
                },
              };
            } else {
              return;
            }
          } else if (event.data.type === "RESOURCE_ERROR") {
            newError = {
              ...newError,
              message: event.data.error.message || "Resource loading error",
              name: "ResourceError",
              type: "resource",
            };
          } else if (event.data.type === "UNHANDLED_PROMISE_REJECTION") {
            newError = {
              ...newError,
              message: event.data.error.message || "Unhandled promise rejection",
              stack: event.data.error.stack,
              name: "PromiseRejection",
              type: "promise",
            };
          } else {
            return;
          }

          setErrors((prev) => [...prev, newError]);
        } else if (event.data.type === "URL_CHANGED") {
          const newPath = event.data.path || "/";
          if (newPath !== currentPage) {
            setCurrentPage(newPath);
          }
        }
      } catch (error) {
        console.error("Error processing iframe message:", error);
      }
    };

    window.addEventListener("message", handleIframeMessage);

    return () => {
      window.removeEventListener("message", handleIframeMessage);
    };
  }, [iframeUrl, currentPage, setErrors]);

  useEffect(() => {
    if (isIframeLoading) {
      previewWarningTimer.current = window.setTimeout(() => {
        setShowLongPreviewWarning(true);
      }, 6000);
    } else {
      window.clearTimeout(previewWarningTimer?.current ?? undefined);
      setShowLongPreviewWarning(false);
    }
    return () => {
      window.clearTimeout(previewWarningTimer?.current ?? undefined);
    };
  }, [isIframeLoading]);

  const openInNewTab = () => {
    if (iframeUrl) {
      window.open(iframeUrl, "_blank");
    }
  };

  const handleRefreshPreview = useCallback(() => {
    setErrors([]);

    setIsIframeLoading(true);

    const timer = setTimeout(() => {
      setIframeUrl((currentUrl) => {
        if (!currentUrl) return "";
        const separator = currentUrl.includes("?") ? "&" : "?";
        return `${currentUrl}${separator}refresh=${Math.random().toString(36).substr(2, 9)}`;
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [setErrors, setIsIframeLoading, setIframeUrl]);

  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {
    if (!isAgentRunning && wasRunning) {
      handleRefreshPreview();
    }
  });

  return (
    <div
      className="relative flex h-[calc(100vh-55px)] w-full flex-col md:h-full"
      style={
        isMobile
          ? {
              height: "calc(100vh - 55px)",
              minHeight: "calc(100vh - 55px)",
            }
          : undefined
      }
    >
      <Tabs defaultValue="tasks" className="flex h-full flex-col">
        <TabsList
          defaultValue="tasks"
          className={cn(
            "flex w-full items-center justify-center border-b border-primary/20 px-3 py-0 dark:border-primary/10",
            currentThreadId ? "hidden" : "mt-2 pb-2",
          )}
        >
          <TabsTrigger value="tasks" className="w-full">
            Tasks
          </TabsTrigger>
          <TabsTrigger value="version-history" className="w-full">
            Version History
          </TabsTrigger>
        </TabsList>
        <TabsContent
          value="tasks"
          className="mt-0 hidden h-full flex-col overflow-hidden bg-transparent data-[state=active]:flex"
        >
          <div className={cn("flex-1 overflow-auto pb-4")}>
            <ThreadContainer setThreadToRename={setThreadToRename} />

            <LazyModal open={!!threadToRename} onOpenChange={() => setThreadToRename(null)}>
              <RenameModalContent
                projectId={projectId}
                threadToRename={threadToRename}
                onClose={() => setThreadToRename(null)}
              />
            </LazyModal>
          </div>

          <div className="relative sticky bottom-0 z-20 border-t border-border/50 bg-background/95 backdrop-blur-sm">
            {currentThreadId ? (
              <MessageInput />
            ) : (
              <div className="mx-2 mb-2 flex items-center">
                <Button size="lg" className="h-12 w-full" onClick={() => handleCreateThread()}>
                  {isCreatingThread ? (
                    <>
                      <Loading className="size-4 animate-spin text-background" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="size-4" />
                      Create New Thread
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </TabsContent>
        <TabsContent
          value="version-history"
          className="hidden h-full overflow-auto data-[state=active]:flex"
        >
          <ThreadHistory id={projectId} />
        </TabsContent>
      </Tabs>

      <Modal open={showDrawer} onOpenChange={setShowDrawer}>
        <ModalContent
          className="max-h-[95%] min-h-[95%] w-full items-end overflow-y-clip lg:max-w-full"
          closeClassName="hidden"
        >
          <Tabs
            defaultValue="preview"
            className="h-full w-full rounded-none border-r-[1px] border-primary/10"
            value={activeTab}
            onValueChange={(value) => {
              setActiveTab(value as "preview" | "code-editor");
              if (isMobile) {
                setShowDrawer(true);
              }
            }}
          >
            <div className="flex items-center gap-2 border-b border-primary/10">
              <div className="flex w-full flex-col items-start justify-between divide-y divide-primary/20 dark:divide-primary/10">
                <div className="flex w-full items-center justify-between p-1">
                  <div className="flex items-center gap-1">
                    <TabsList className="flex w-fit items-center justify-center gap-2 border-l-0 px-2">
                      <TabsTrigger
                        value="preview"
                        className="w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0"
                      >
                        Preview
                      </TabsTrigger>
                      <TabsTrigger value="code-editor" className="w-fit">
                        Code Editor
                      </TabsTrigger>
                      {/* {!isFreeTier && (
                        <TabsTrigger value="terminal" className="w-fit">
                          Terminal
                        </TabsTrigger>
                      )} */}
                    </TabsList>
                  </div>
                </div>

                <div
                  className={cn(
                    "flex w-full items-center gap-1 p-1",
                    activeTab === "code-editor" || (activeTab === "terminal" && "hidden"),
                  )}
                >
                  <div className="flex items-center gap-1">
                    <Hint label="Refresh Preview" side="bottom" delayDuration={125}>
                      <Button
                        size="icon"
                        className="size-7"
                        variant="ghost"
                        onClick={handleRefreshPreview}
                        disabled={isAgentRunning}
                      >
                        {isIframeLoading ? (
                          <Loading className="size-4 animate-spin" />
                        ) : (
                          <RotateCcw
                            className="size-4 text-primary/90 hover:text-primary"
                            strokeWidth={2}
                          />
                        )}
                      </Button>
                    </Hint>
                  </div>
                  {isProjectLoading ? (
                    <Skeleton className="mx-0.5 my-0 h-7 w-full" />
                  ) : (
                    filteredRoutes.length > 0 && (
                      <Select value={currentPage} onValueChange={handleRouteChange}>
                        <SelectTrigger className="mx-0.5 my-0 h-7 w-full rounded-full bg-accent py-0 hover:bg-accent/40">
                          <SelectValue placeholder="Select route" />
                        </SelectTrigger>
                        <SelectContent align="center" className="w-64 px-2">
                          {filteredRoutes.map((route: string) => (
                            <SelectItem key={route} value={route}>
                              {route}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )
                  )}

                  <div className="flex items-center">
                    <Button variant="ghost" size="icon" className="size-7" onClick={openInNewTab}>
                      <ExternalLink className="size-4 text-primary/90 hover:text-primary" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative flex-1 overflow-y-clip">
              <TabsContent
                value="preview"
                className={cn("m-0 mx-auto flex w-full flex-1 flex-col", "h-[calc(100vh-110px)]")}
                forceMount
                style={{ display: activeTab === "preview" ? "flex" : "none" }}
              >
                <CardContent className="h-[calc(100vh-120px)] p-0">
                  <div className="relative h-full w-full">
                    {sandboxState === "unavailable" ? (
                      <div className="flex h-full items-center justify-center">
                        <SandboxUnavailable />
                      </div>
                    ) : sandboxState === "sleeping" ? (
                      <div className="flex h-full items-center justify-center">
                        <SandboxSleepingCard />
                      </div>
                    ) : (
                      <>
                        {iframeUrl && (
                          <iframe
                            src={iframeUrl}
                            className="h-full w-full"
                            title="Web Application Preview"
                            style={{
                              overflow: "auto",
                              scrollbarWidth: "thin",
                              scrollbarColor: "var(--muted) transparent",
                              colorScheme: "normal",
                            }}
                            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-clipboard-write allow-clipboard-read allow-storage-access-by-user-activation allow-presentation"
                            allow="clipboard-write; clipboard-read; geolocation *; microphone *; camera *; display-capture *; fullscreen *; web-share *"
                            allowFullScreen
                            onLoad={handleIframeLoad}
                            ref={iframeRef}
                            referrerPolicy="no-referrer"
                            loading="lazy"
                          />
                        )}
                        {isIframeLoading && <LoadingPreview />}
                      </>
                    )}
                  </div>
                </CardContent>
                {sandboxState === "idle" && (
                  <div className="mx-auto flex w-full items-center justify-center">
                    <BugFinder errors={errors} setErrors={setErrors} projectId={projectId} />
                  </div>
                )}
              </TabsContent>
              <TabsContent
                value="code-editor"
                className={cn(
                  "m-0 mx-auto flex w-full flex-1 flex-col overflow-hidden",
                  "h-[calc(100vh-90px)]",
                  "data-[state=active]:block data-[state=inactive]:hidden",
                )}
                forceMount
                style={{ display: activeTab === "code-editor" ? "flex" : "none" }}
              >
                <CardContent className="h-full overflow-hidden p-0">
                  <div className="h-full overflow-auto">
                    <CodeEditor projectId={projectId} envId={project?.env_id || ""} />
                  </div>
                </CardContent>
              </TabsContent>
              {/* {!isFreeTier && (
                <TabsContent
                  value="terminal"
                  className={cn(
                    "m-0 mx-auto flex w-full flex-1 flex-col overflow-hidden",
                    "h-[calc(100vh-5.9rem)]",
                    "data-[state=active]:block data-[state=inactive]:hidden",
                  )}
                  forceMount
                  style={{ display: activeTab === "terminal" ? "flex" : "none" }}
                >
                  <Terminal projectId={projectId} />
                </TabsContent>
              )} */}
            </div>
          </Tabs>
        </ModalContent>
      </Modal>

      <SupabaseSheet isOpen={showSupabaseSheet} onClose={() => setShowSupabaseSheet(false)} />
    </div>
  );
};

// function MobilePreviewPanel({
//   iframeUrl,
//   iframeRef,
//   isIframeLoading,
//   handleIframeLoad,
// }: {
//   iframeUrl: string;
//   iframeRef: React.RefObject<HTMLIFrameElement | null>;
//   isIframeLoading: boolean;
//   handleIframeLoad: () => void;
// }) {
//   const { isSandboxSleeping } = useProject();

//   return (
//     <div className="relative h-full w-full">
//       {isSandboxSleeping ? (
//         <div className="flex h-full items-center justify-center">
//           <SandboxSleepingCard />
//         </div>
//       ) : (
//         <>
//           {iframeUrl && (
//             <iframe
//               src={iframeUrl}
//               className="h-full w-full"
//               title="Web Application Preview"
//               style={{
//                 overflow: "auto",
//                 scrollbarWidth: "thin",
//                 scrollbarColor: "var(--muted) transparent",
//                 colorScheme: "normal",
//               }}
//               sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-clipboard-write allow-clipboard-read allow-storage-access-by-user-activation allow-presentation"
//               allow="clipboard-write; clipboard-read; geolocation *; microphone *; camera *; display-capture *; fullscreen *; web-share *"
//               allowFullScreen
//               onLoad={handleIframeLoad}
//               ref={iframeRef}
//               referrerPolicy="no-referrer"
//               loading="lazy"
//             />
//           )}
//           {isIframeLoading && <LoadingPreview />}
//         </>
//       )}
//     </div>
//   );
// }

export default MobileView;
