from contextlib import asynccontextmanager
import json
import logging
from core.envs.page_routes import get_frontend_page_routes
from fastapi import APIRouter, HTTPException, Depends, Body, Query, WebSocket
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select, delete, union
from sqlalchemy.future import select as future_select
from core.models import Deployment, ProjectThread, Project, ProjectAgentRun, SupabaseConnection, SupabaseOrganization
from core.db import Database
from core.platform.user import User, get_current_active_user
from typing import Optional, Dict
from core.utils.llm import make_llm_api_call
from core.config import settings
from core.platform.email import send_team_invitation_email
import uuid
from datetime import datetime
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, aliased
from pydantic import BaseModel, HttpUrl
from core.envs.env_manager import EnvManager
from core.envs.env_ops import env_ops
from core.envs.env_manager import check_and_maintain_environments
from core.platform.is_admin import (
    is_admin,
    is_admin_or_owner,
    is_admin_or_owner_or_team,
    is_admin_or_owner_or_team_or_public
)
from core.utils.posthog.client import capture_event
from core.platform.remix_websocket import remix_project_websocket_handler
from core.utils.project_utils import add_remix_suffix

# Initialize APIRouter
router = APIRouter()
db = Database()

@asynccontextmanager
async def project_creation_lock(session: AsyncSession, user_id: int):
    user = await session.execute(select(User).where(User.id == user_id))
    user = user.scalar_one_or_none()
    if user.is_creating_project:
        raise HTTPException(status_code=400, detail="Project creation already in progress")

    user.is_creating_project = True
    await session.commit()
    try:
        yield
    except Exception as e:
        # Handle specific exceptions if needed
        raise e
    finally:
        # Ensure the flag is reset only if no exception was raised
        user.is_creating_project = False
        await session.commit()

async def create_project_with_thread(db, name: str, current_user):
    project_manager = ProjectService(db)
    async with db.get_async_session() as session:
        async with project_creation_lock(session, current_user.id):
            try:
                # First validate project limit
                if current_user.project_limit <= 0:
                    raise HTTPException(status_code=400, detail="Project limit reached")

                # Create the project
                project_data = await project_manager.create_project(name, current_user.id)

                # After successful project creation, deduct the limit
                success = await db.project_service.deduct_project_limit(current_user.kinde_id)
                if not success:
                    # If deduction fails, we should clean up the created project
                    await cleanup_project_data(db, project_data["project_id"], project_data["env_id"])
                    raise HTTPException(status_code=500, detail="Failed to update project limit")

                # Create the first thread for the project
                new_thread = ProjectThread(
                    project_id=project_data["project_id"],
                    messages=json.dumps([]),
                    creation_date=datetime.now().isoformat(),
                    last_updated_date=datetime.now().isoformat(),
                    name="New Thread"  # Default name for new threads
                )
                session.add(new_thread)
                await session.commit()

                asyncio.create_task(check_and_maintain_environments())

            except ValueError as ve:
                raise HTTPException(status_code=400, detail=str(ve))
            except SQLAlchemyError as e:
                raise HTTPException(status_code=500, detail=f"Error creating project: {str(e)}")

    project_data["initial_thread_id"] = new_thread.thread_id
    return {"message": "Project created successfully with initial thread", "project_data": project_data}

async def cleanup_project_data(db: Database, project_id: str, env_id: str):
    """Clean up all data related to a project asynchronously"""
    try:
        async with db.get_async_session() as session:
            # Delete project threads in a single transaction
            await session.execute(
                delete(ProjectThread)
                .where(ProjectThread.project_id == project_id)
            )
            await session.commit()

            # Delete environment separately since it's an external operation
            if env_id:
                envs = EnvManager()
                await envs.env_db_service.delete_environment(env_id)

            logging.info(f"Successfully cleaned up data for project {project_id}")
    except Exception as e:
        logging.error(f"Error during project data cleanup for {project_id}: {str(e)}")

async def remove_project_service(db, project_id: str, current_user):
    try:
        async with db.get_async_session() as session:
            logging.info(f"Removing project {project_id}")
            # Get project and user in a single query
            stmt = select(Project, User).join(
                User, Project.owner_id == User.id
            ).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            row = result.first()
            logging.info(f"project deletion row: {row}")

            if row is None:
                raise HTTPException(status_code=404, detail="Project not found")

            project, user = row

            # Use the utility function to check access
            if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to remove this project")

            # Store project data for cleanup
            env_id = project.env_id

            # Update user's project limit and delete project in the same transaction
            if user:
                user.project_limit += 1

            # Delete Supabase connections
            await session.execute(
                delete(SupabaseConnection)
                .where(SupabaseConnection.project_id == project_id)
            )

            logging.info("supabase connection deleted")

            # Delete the project
            await session.delete(project)

            logging.info("project deleted")

            # Commit all changes at once
            await session.commit()

            logging.info("all changes committed and going to cleanup")

            # Start cleanup in background
            asyncio.create_task(cleanup_project_data(db, project_id, env_id))

            return {"message": "Project removed successfully"}
    except HTTPException as e:
        raise e
    except SQLAlchemyError as e:
        logging.error(f"Database error during project removal: {str(e)}")
        raise HTTPException(status_code=500, detail="Error removing project")
    except Exception as e:
        logging.error(f"Unexpected error during project removal: {str(e)}")
        raise HTTPException(status_code=500, detail="Error removing project")

async def get_project_service(db, project_id: str, current_user: User):
    project_manager = ProjectService(db)
    try:
        project = await project_manager.get_project(project_id, current_user)

        if not project:
            logging.error(f"Project {project_id} not found for user {current_user.id}")
            raise HTTPException(status_code=404, detail="Project not found")

        # Ensure .env.local exists
        if project["isRunning"] and project.get("env_id"):
            asyncio.create_task(env_ops.ensure_project_files(project["env_id"]))

        deployment = None
        if project.get("deployment"):
            deployment = {
                "deployment_url": project["deployment"].get("deployment_url"),
                "is_deployed": project["deployment"].get("is_deployed", False),
                "last_deployed": project["deployment"].get("last_deployed"),
                "vercel_token": project["deployment"].get("vercel_token"),
                "production_url": project["deployment"].get("production_url")
            }

        # Get Supabase connection details
        supabase_connection = None
        try:
            async with db.get_async_session() as session:
                # Get Supabase connection from database
                supabase_stmt = select(SupabaseConnection).where(
                    SupabaseConnection.project_id == project_id
                ).order_by(SupabaseConnection.last_updated.desc())  # Get the most recently updated one

                supabase_result = await session.execute(supabase_stmt)
                supabase_conn = supabase_result.scalars().first()  # Use first() instead of scalar_one_or_none()

                if supabase_conn:
                    # Get the organization to access organization name
                    org_stmt = select(SupabaseOrganization).where(
                        SupabaseOrganization.organization_id == supabase_conn.organization_id
                    ).order_by(SupabaseOrganization.last_updated.desc())  # Get the most recently updated one

                    org_result = await session.execute(org_stmt)
                    organization = org_result.scalars().first()  # Use first() instead of scalar_one_or_none()

                    organization_name = organization.organization_name if organization else "Unknown Organization"

                    supabase_connection = {
                        "connected": True,
                        "organization_id": supabase_conn.organization_id,
                        "organization_name": organization_name,
                        "supabase_project_id": supabase_conn.supabase_project_id,
                        "api_key": supabase_conn.api_key,
                        "api_url": supabase_conn.api_url,
                        "service_role_key": supabase_conn.service_role_key,
                        "database_password": supabase_conn.database_password
                    }
                else:
                    supabase_connection = {
                        "connected": False
                    }
        except Exception as e:
            logging.error(f"Error getting Supabase connection: {str(e)}")
            supabase_connection = {
                "connected": False,
                "error": str(e)
            }

        return {
            "project_id": project["project_id"],
            "name": project["name"],
            "creation_date": project["creation_date"],
            "last_updated_date": project["last_updated_date"],
            "env_id": project["env_id"],
            "env_url": project["env_url"],
            "frontend_page_routes": project["frontend_page_routes"],
            "github_repo": project["github_repo"],
            "isRunning": project["isRunning"],
            "sandbox_state": project.get("sandbox_state"),
            "preview_image_url": project["preview_image_url"],
            "onboarding_completed": project["onboarding_completed"],
            "tech_stack_prompt": project["tech_stack_prompt"],
            "custom_instructions": project["custom_instructions"],
            "users_prompt_Array": project["users_prompt_Array"],
            "deployment": deployment,
            "owner_id": project["owner_id"],
            "isPublic": project["isPublic"] if project.get("isPublic") is not None else False,
            "team_emails": project["team_emails"] if project.get("team_emails") is not None else [],
            "supabase": supabase_connection,
            "isSupabaseConnected": project.get("isSupabaseConnected", False)
        }
    except HTTPException as e:
        logging.error(f"HTTPException in get_project: {str(e)}")
        raise e
    except Exception as e:
        logging.error(f"Unexpected error in get_project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error retrieving project: {str(e)}")

async def list_projects_service(db, current_user_id: int, current_user_email: str):
    project_manager = ProjectService(db)
    projects = await project_manager.list_projects(current_user_id, current_user_email)

    # Get all unique owner IDs from projects where current user is not the owner
    owner_ids = list(set(p.owner_id for p in projects if p.owner_id != current_user_id))

    # Get owner emails in a single query if there are any owners to look up
    owner_emails = {}
    if owner_ids:
        async with db.get_async_session() as session:
            stmt = select(User.id, User.email).where(User.id.in_(owner_ids))
            result = await session.execute(stmt)
            owner_emails = {user_id: email for user_id, email in result}

    return [{
        "project_id": p.project_id,
        "name": p.name,
        "creation_date": p.creation_date,
        "last_updated_date": p.last_updated_date,
        "env_id": p.env_id,
        "preview_image_url": p.preview_image_url,
        "isPublic": p.isPublic,
        "owner_email": owner_emails.get(p.owner_id) if p.owner_id != current_user_id else None
    } for p in projects]

async def update_project_service(db, project_id: str, name: Optional[str], onboarding_completed: Optional[bool], current_user_id: int, current_user_email: str, isPublic: Optional[bool] = None, preview_image_url: Optional[str] = None):
    try:
        async with db.get_async_session() as session:
            async with session.begin():
                stmt = future_select(Project).where(Project.project_id == project_id)
                result = await session.execute(stmt)
                project = result.scalar_one_or_none()

                if project is None:
                    raise HTTPException(status_code=404, detail="Project not found")

                # Use the utility function to check access
                if not is_admin_or_owner_or_team(project, current_user_id, current_user_email):
                    raise HTTPException(status_code=403, detail="Not authorized to update this project")

                if name:
                    project.name = name

                if onboarding_completed is not None:
                    project.onboarding_completed = onboarding_completed

                if isPublic is not None:
                    project.isPublic = isPublic

                if preview_image_url is not None:
                    project.preview_image_url = preview_image_url

                project.last_updated_date = datetime.now().isoformat()
                await session.commit()

        return {
            "message": "Project updated successfully",
            "project_id": project.project_id,
            "name": project.name,
            "onboarding_completed": project.onboarding_completed,
            "isPublic": project.isPublic,
            "preview_image_url": project.preview_image_url
        }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Error updating project: {str(e)}")

async def get_project_threads_service(db, project_id: str, current_user_id: int, current_user_email: str):
    async with db.get_async_session() as session:
        # First check if project exists and user has access
        stmt = future_select(Project).where(Project.project_id == project_id)
        result = await session.execute(stmt)
        project = result.scalar_one_or_none()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        if not is_admin_or_owner_or_team(project, current_user_id, current_user_email):
            raise HTTPException(status_code=403, detail="Not authorized to access this project")

        # Only select the columns we need, excluding the large messages column
        stmt = future_select(
            ProjectThread.thread_id,
            ProjectThread.project_id,
            ProjectThread.creation_date,
            ProjectThread.last_updated_date,
            ProjectThread.page_route,
            ProjectThread.name
        ).where(ProjectThread.project_id == project_id)

        result = await session.execute(stmt)
        threads = result.all()  # Use .all() instead of .scalars().all() since we're selecting specific columns

        if not threads:
            raise HTTPException(status_code=404, detail="No threads found for this project")

        # Get the latest agent run for each thread
        thread_ids = [thread.thread_id for thread in threads]
        stmt = (
            select(ProjectAgentRun)
            .where(ProjectAgentRun.thread_id.in_(thread_ids))
            .order_by(ProjectAgentRun.creation_date.desc())
            .distinct(ProjectAgentRun.thread_id)
        )
        result = await session.execute(stmt)
        latest_agent_runs = {run.thread_id: run for run in result.scalars().all()}

        threads_response = []
        for thread in threads:
            latest_run = latest_agent_runs.get(thread.thread_id)
            thread_data = {
                "thread_id": thread.thread_id,
                "creation_date": thread.creation_date,
                "last_updated_date": thread.last_updated_date,
                "page_route": thread.page_route,
                "name": thread.name or "New Thread"  # Include the thread name
            }
            if latest_run:
                thread_data["latest_session_status"] = {
                    "project_agent_run": latest_run.run_id,
                    "status": latest_run.status,
                    "creation_date": latest_run.creation_date,
                    "objective": latest_run.objective
                }
            threads_response.append(thread_data)

        return threads_response


@router.post("/project")
async def create_project(name: str, current_user: User = Depends(get_current_active_user)):
    return await create_project_with_thread(db, name, current_user)

@router.delete("/project/{project_id}")
async def remove_project(project_id: str, current_user: User = Depends(get_current_active_user)):
    return await remove_project_service(db, project_id, current_user)

@router.get("/project/{project_id}")
async def get_project(project_id: str, current_user: User = Depends(get_current_active_user)):
    return await get_project_service(db, project_id, current_user)

@router.get("/projects")
async def list_projects(current_user: User = Depends(get_current_active_user)):
    return await list_projects_service(db, current_user.id, current_user.email)

@router.put("/project/{project_id}")
async def update_project(
    project_id: str,
    name: Optional[str] = Query(None),
    onboarding_completed: Optional[bool] = Query(None),
    isPublic: Optional[bool] = Query(None),
    preview_image_url: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    update_data: Optional[dict] = Body(None)
):
    final_name = update_data.get('name') if update_data else name
    final_onboarding = update_data.get('onboarding_completed') if update_data else onboarding_completed
    final_isPublic = update_data.get('isPublic') if update_data else isPublic
    final_preview_image = update_data.get('preview_image_url') if update_data else preview_image_url

    return await update_project_service(
        db,
        project_id,
        final_name,
        final_onboarding,
        current_user.id,
        current_user.email,
        final_isPublic,
        final_preview_image
    )

@router.get("/project/{project_id}/threads")
async def get_project_threads(project_id: str, current_user: User = Depends(get_current_active_user)):
    return await get_project_threads_service(db, project_id, current_user.id, current_user.email)

# Add this class for request validation
class RemixProjectRequest(BaseModel):
    env_values: Optional[Dict[str, str]] = None

@router.post("/project/{project_id}/remix")
async def remix_project(
    project_id: str,
    request: RemixProjectRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Create a new project as a remix of an existing project."""
    # Check if user is on free tier
    if current_user.plan == "free-tier":
        raise HTTPException(status_code=403, detail="Project remixing features are not available on the free tier plan")

    # Log the environment values if provided
    if request.env_values:
        logging.info(f"Received env values for remix: {request.env_values}")

    return await remix_project_service(db, project_id, current_user, request.env_values)

@router.websocket("/project/{project_id}/remix")
async def remix_project_websocket(
    websocket: WebSocket,
    project_id: str,
    token: str = Query(...),
    env_values: str = Query(None),  # JSON string of env values
):
    """WebSocket endpoint for remixing projects."""
    await remix_project_websocket_handler(websocket, project_id, token, env_values)

async def enhance_prompt_service(prompt: str) -> dict:
    """Enhance a given prompt using LLM to make it more specific and effective."""
    try:
        messages = [
            {
                "role": "system",
                "content": (
                    "You are an expert in enhancing prompts for AI software development. "
                    "Your task is to refine the given prompt to ensure it is clear, specific, and actionable for an AI software developer. "
                    "Focus on technical clarity, precision, and relevance to software development tasks. "
                    "Provide ONLY the enhanced prompt without any introductory text or explanations."
                )
            },
            {
                "role": "user",
                "content": f"Enhance this prompt: {prompt}"
            }
        ]

        response = await make_llm_api_call(
            messages=messages,
            model_name="gpt-4o",  # Faster model
            temperature=0.7,
            max_tokens=400  # Limit response length
        )

        enhanced_prompt = response.choices[0].message['content']

        return {
            "original_prompt": prompt,
            "enhanced_prompt": enhanced_prompt
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error enhancing prompt: {str(e)}")

@router.post("/project/enhance-prompt")
async def enhance_prompt(
    prompt: str = Body(..., embed=True),
    current_user: User = Depends(get_current_active_user)
):
    """API endpoint to enhance a prompt."""
    return await enhance_prompt_service(prompt)



# Configure logging
logging.basicConfig(level=logging.INFO)

class ProjectService:
    def __init__(self, db: Database):
        self.db = db
        self.env_manager = EnvManager()
        # self.files_tool = FilesTool(db)

    async def create_project(self, name: str, user_id: int, agent_instructions: str = None, agent_rules_to_follow: str = None) -> dict:
        project_id = str(uuid.uuid4())

        # Try to find an unassigned environment first
        env, needs_creation = await self.env_manager.env_db_service.assign_or_create_environment(project_id)

        if needs_creation:
            # Create new environment if none are available
            env_data = await self.env_manager.create_env()
            if not env_data:
                raise ValueError("Failed to create new environment")

            env_id = env_data.get('env_id')
            if not env_id:
                raise ValueError("Failed to get env_id from created environment")

            # Assign the newly created environment
            env = await self.env_manager.env_db_service.assign_to_project(
                env_id=env_id,
                project_id=project_id,
                github_repo=env_data.get('github', {}).get('html_url')
            )
        else:
            env_id = env.env_id

        creation_date = datetime.now().isoformat()
        last_updated_date = creation_date

        async with self.db.get_async_session() as session:
            async with session.begin():
                new_project = Project(
                    project_id=project_id,
                    name=name,
                    owner_id=user_id,
                    creation_date=creation_date,
                    last_updated_date=last_updated_date,
                    env_id=env_id,
                    agent_instructions=agent_instructions,
                    agent_rules_to_follow=agent_rules_to_follow,
                    onboarding_completed=False  # Set to False by default
                )
                session.add(new_project)
                await session.flush()

                capture_event(
                    user_id,
                    'project_created',
                    { 'project_id': project_id }
                )

                return {
                    "project_id": new_project.project_id,
                    "name": new_project.name,
                    "creation_date": new_project.creation_date,
                    "last_updated_date": new_project.last_updated_date,
                    "env_id": new_project.env_id,
                    "agent_instructions": new_project.agent_instructions,
                    "agent_rules_to_follow": new_project.agent_rules_to_follow,
                    "onboarding_completed": new_project.onboarding_completed
                }

    async def get_project(self, project_id: str, current_user: User) -> dict:
        try:
            async with self.db.get_async_session() as session:
                stmt = select(Project).options(selectinload(Project.deployment)).where(
                    Project.project_id == project_id
                )
                result = await session.execute(stmt)
                project = result.scalar_one_or_none()

                if not project:
                    raise HTTPException(status_code=404, detail="Project not found")

                # Check access using the function
                if not is_admin_or_owner_or_team_or_public(project, current_user.id, current_user.email):
                    raise HTTPException(status_code=403, detail="Not authorized to access this project")

                # Get environment details using the new EnvManager
                env_data = await self.env_manager.get_environment(project.env_id)
                if not env_data:
                    raise HTTPException(status_code=500, detail="Failed to fetch environment data")

                is_running = env_data.get('isRunning')

                if is_running:
                    frontend_page_routes = await get_frontend_page_routes(env_data.get('env_id'))
                else:
                    frontend_page_routes = []

                # Get deployment data
                deployment = project.deployment
                deployment_data = None
                if deployment:
                    deployment_data = {
                        "deployment_url": deployment.deployment_url,
                        "last_deployed": deployment.last_deployed,
                        "vercel_token": deployment.vercel_token,
                        "is_deployed": deployment.is_deployed,
                        "production_url": deployment.production_url
                    }

                # Get Supabase connection details
                supabase_connection = None
                try:
                    # Get Supabase connection from database
                    supabase_stmt = select(SupabaseConnection).where(
                        SupabaseConnection.project_id == project_id
                    ).order_by(SupabaseConnection.last_updated.desc())  # Get the most recently updated one

                    supabase_result = await session.execute(supabase_stmt)
                    supabase_conn = supabase_result.scalars().first()  # Use first() instead of scalar_one_or_none()

                    if supabase_conn:
                        # Get the organization to access organization name
                        org_stmt = select(SupabaseOrganization).where(
                            SupabaseOrganization.organization_id == supabase_conn.organization_id
                        ).order_by(SupabaseOrganization.last_updated.desc())  # Get the most recently updated one

                        org_result = await session.execute(org_stmt)
                        organization = org_result.scalars().first()  # Use first() instead of scalar_one_or_none()

                        organization_name = organization.organization_name if organization else "Unknown Organization"

                        supabase_connection = {
                            "connected": True,
                            "organization_id": supabase_conn.organization_id,
                            "organization_name": organization_name,
                            "supabase_project_id": supabase_conn.supabase_project_id,
                            "api_key": supabase_conn.api_key,
                            "api_url": supabase_conn.api_url,
                            "service_role_key": supabase_conn.service_role_key,
                            "database_password": supabase_conn.database_password
                        }
                    else:
                        supabase_connection = {
                            "connected": False
                        }
                except Exception as e:
                    logging.error(f"Error getting Supabase connection: {str(e)}")
                    supabase_connection = {
                        "connected": False,
                        "error": str(e)
                    }

                return {
                    "project_id": project.project_id,
                    "name": project.name,
                    "creation_date": project.creation_date,
                    "last_updated_date": project.last_updated_date,
                    "env_id": env_data.get('env_id'),
                    "env_url": env_data.get('env_url'),
                    "frontend_page_routes": frontend_page_routes,
                    "github_repo": "TOBEIMPLEMENTED",
                    "isRunning": is_running,
                    "sandbox_state": env_data.get('sandbox_state'),
                    "createdAt": env_data.get('createdAt'),
                    "preview_image_url": env_data.get("preview_image_url"),
                    "agent_instructions": project.agent_instructions,
                    "agent_rules_to_follow": project.agent_rules_to_follow,
                    "tech_stack_prompt": project.tech_stack_prompt,
                    "custom_instructions": project.custom_instructions,
                    "users_prompt_Array": project.users_prompt_Array,
                    "onboarding_completed": project.onboarding_completed,
                    "deployment": deployment_data,
                    "browser_session_id": project.browser_session_id,
                    "isPublic": project.isPublic if project.isPublic is not None else False,
                    "owner_id": project.owner_id,
                    "team_emails": project.team_emails if project.team_emails is not None else [],
                    "supabase": supabase_connection,
                    "isSupabaseConnected": project.isSupabaseConnected if hasattr(project, 'isSupabaseConnected') else False
                }
        except SQLAlchemyError as e:
            logging.error(f"Database error in get_project: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="Database error occurred")
        except Exception as e:
            logging.error(f"Unexpected error in get_project: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

    async def list_projects(self, user_id: int, user_email: str) -> list[Project]:
        async with self.db.get_async_session() as session:
            # First query: projects where user is owner
            stmt1 = select(Project).where(Project.owner_id == user_id)

            # Second query: projects with team_emails containing the email
            stmt2 = select(Project).where(
                Project.team_emails.like(f'%"{user_email}"%')
            )

            # Need to use subquery and aliased to avoid SQLAlchemy type conversion issues
            union_subq = union(stmt1, stmt2).subquery()
            ProjectAlias = aliased(Project, union_subq)

            stmt = select(ProjectAlias)
            result = await session.execute(stmt)
            projects = result.scalars().all()

            # TODO: remove after QA changes
            # Filter in Python to ensure exact email match
            filtered_projects = [
                p for p in projects if
                p.owner_id == user_id or
                (p.team_emails and user_email in json.loads(p.team_emails))
            ]

            return filtered_projects

    async def update_project(self, project_id: str, user_id: int, name: str = None) -> Project:
        async with self.db.get_async_session() as session:
            async with session.begin():
                project = await self.get_project(project_id, user_id)
                if name:
                    project.name = name
                project.last_updated_date = datetime.now().isoformat()
                await session.commit()
                return project

    async def update_project_total_request(self, project_id: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(Project).filter(Project.project_id == project_id))
            project = result.scalar_one_or_none()
            if project:
                project.total_request += 1
                await session.commit()

    async def deduct_project_limit(self, kinde_id: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
            user = result.scalar_one_or_none()
            if user and user.project_limit > 0:
                user.project_limit -= 1
                user.is_active = True  # Set is_active to True
                session.add(user)
                await session.commit()
                return True
            return False

    async def get_project_and_user_by_thread_id(self, thread_id: int):
        async with self.db.get_async_session() as session:
            stmt = select(Project, User).join(User, Project.owner_id == User.id).join(ProjectThread, Project.project_id == ProjectThread.project_id).where(ProjectThread.thread_id == thread_id)
            result = await session.execute(stmt)
            project, user = result.first()
            return project, user

    async def update_vercel_info(self, project_id: str, vercel_token: str = None, deployment_url: str = None, production_url: str = None, is_deployed: bool = None, last_deployed: str = None) -> dict:
        async with self.db.get_async_session() as session:
            async with session.begin():
                # Use selectinload to eagerly load the deployment relationship
                stmt = select(Project).options(selectinload(Project.deployment)).where(Project.project_id == project_id)
                result = await session.execute(stmt)
                project = result.scalar_one_or_none()

                if not project:
                    raise HTTPException(status_code=404, detail="Project not found")

                if not project.deployment:
                    project.deployment = Deployment(project_id=project_id)

                if vercel_token is not None:
                    project.deployment.vercel_token = vercel_token
                if deployment_url is not None:
                    project.deployment.deployment_url = deployment_url
                if production_url is not None:
                    project.deployment.production_url = production_url
                if is_deployed is not None:
                    project.deployment.is_deployed = is_deployed
                if last_deployed is not None:
                    project.deployment.last_deployed = last_deployed

                project.last_updated_date = datetime.now().isoformat()
                await session.commit()

                return {
                    "project_id": project.project_id,
                    "vercel_token": project.deployment.vercel_token,
                    "deployment_url": project.deployment.deployment_url,
                    "production_url": project.deployment.production_url,
                    "is_deployed": project.deployment.is_deployed,
                    "last_deployed": project.deployment.last_deployed
                }

    async def get_project_for_remix(self, project_id: str) -> dict:
        """Get project without user restrictions for remix purposes."""
        try:
            async with self.db.get_async_session() as session:
                stmt = select(Project).options(selectinload(Project.deployment)).where(
                    Project.project_id == project_id
                )

                result = await session.execute(stmt)
                project = result.scalar_one_or_none()

                if not project:
                    raise HTTPException(status_code=404, detail="Project not found")

                # Get environment details
                env_data = await self.env_manager.get_environment(project.env_id)
                if not env_data:
                    raise HTTPException(status_code=500, detail="Failed to fetch environment data")

                return {
                    "project_id": project.project_id,
                    "name": project.name,
                    "env_id": project.env_id,
                    "agent_instructions": project.agent_instructions,
                    "agent_rules_to_follow": project.agent_rules_to_follow,
                    "isPublic": project.isPublic if project.isPublic is not None else False,
                    "owner_id": project.owner_id
                }
        except Exception as e:
            logging.error(f"Error in get_project_for_remix: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error retrieving project: {str(e)}")

    async def remix_project(self, name: str, user_id: int, original_project: dict, env_values: Optional[Dict[str, str]] = None) -> dict:
        project_id = str(uuid.uuid4())

        # Get source environment ID from original project
        source_env_id = original_project.get("env_id")
        if not source_env_id:
            raise ValueError("Source environment ID not found in original project")

        # Use the new remix_and_assign_env function from EnvManager
        env_data = await self.env_manager.remix_and_assign_env(project_id, source_env_id, env_values)
        if not env_data:
            raise ValueError("Failed to remix and assign environment")

        env_id = env_data.get('env_id')
        if not env_id:
            raise ValueError("Failed to get env_id from remixed environment")

        creation_date = datetime.now().isoformat()
        last_updated_date = creation_date

        async with self.db.get_async_session() as session:
            async with session.begin():
                new_project = Project(
                    project_id=project_id,
                    name=name,
                    owner_id=user_id,
                    creation_date=creation_date,
                    last_updated_date=last_updated_date,
                    env_id=env_id,
                    agent_instructions=original_project.get("agent_instructions"),
                    agent_rules_to_follow=original_project.get("agent_rules_to_follow"),
                    onboarding_completed=False
                )
                session.add(new_project)
                await session.flush()

                capture_event(
                    user_id,
                    'project_cloned',
                    {
                        'name': name,
                        'project_id': new_project.project_id,
                        'original_project_id': original_project.get('project_id')
                    }
                )

                return {
                    "project_id": new_project.project_id,
                    "name": new_project.name,
                    "creation_date": new_project.creation_date,
                    "last_updated_date": new_project.last_updated_date,
                    "env_id": new_project.env_id,
                    "agent_instructions": new_project.agent_instructions,
                    "agent_rules_to_follow": new_project.agent_rules_to_follow,
                    "onboarding_completed": new_project.onboarding_completed
                }

    async def reset_vercel_deployment(self, project_id: str) -> dict:
        """
        Reset all Vercel deployment information to null values.
        """
        async with self.db.get_async_session() as session:
            async with session.begin():
                # Use selectinload to eagerly load the deployment relationship
                stmt = select(Project).options(selectinload(Project.deployment)).where(Project.project_id == project_id)
                result = await session.execute(stmt)
                project = result.scalar_one_or_none()

                if not project:
                    raise HTTPException(status_code=404, detail="Project not found")

                if project.deployment:
                    # Reset all deployment-related fields to null
                    project.deployment.vercel_token = None
                    project.deployment.deployment_url = None
                    project.deployment.is_deployed = False
                    project.deployment.last_deployed = None
                    project.deployment.production_url = None

                project.last_updated_date = datetime.now().isoformat()
                await session.commit()

                return {
                    "project_id": project.project_id,
                    "vercel_token": None,
                    "deployment_url": None,
                    "production_url": None,
                    "is_deployed": False,
                    "last_deployed": None
                }

async def remix_project_with_thread(db, name: str, current_user, original_project: dict, env_values: Optional[Dict[str, str]] = None):
    """Create a new project with thread as a remix of an existing project."""
    project_manager = ProjectService(db)
    async with db.get_async_session() as session:
        async with project_creation_lock(session, current_user.id):
            try:
                project_data = await project_manager.remix_project(name, current_user.id, original_project, env_values)
                success = await db.project_service.deduct_project_limit(current_user.kinde_id)
                if not success:
                    raise HTTPException(status_code=400, detail="Project limit reached")

                # Create the first thread for the project
                new_thread = ProjectThread(
                    project_id=project_data["project_id"],
                    messages=json.dumps([]),
                    creation_date=datetime.now().isoformat(),
                    last_updated_date=datetime.now().isoformat(),
                    name="New Thread"  # Default name for new threads
                )
                session.add(new_thread)
                await session.commit()
                if settings.env == 'prod':
                # Fire and forget the environment maintenance check
                    asyncio.create_task(check_and_maintain_environments())
            except ValueError as ve:
                raise HTTPException(status_code=400, detail=str(ve))
            except SQLAlchemyError as e:
                raise HTTPException(status_code=500, detail=f"Error creating project: {str(e)}")

    project_data["initial_thread_id"] = new_thread.thread_id
    return {"message": "Project remixed successfully with initial thread", "project_data": project_data}

async def remix_project_service(db, project_id: str, current_user: User, env_values: Optional[Dict[str, str]] = None) -> dict:
    """Create a new project as a remix of an existing project."""
    try:
        # Check project limit directly from current_user
        if current_user.project_limit <= 0:
            raise HTTPException(status_code=400, detail="Project limit reached. Unable to create new project.")

        project_manager = ProjectService(db)

        # Get the original project without user restrictions
        original_project = await project_manager.get_project_for_remix(project_id)
        if not original_project:
            raise HTTPException(status_code=404, detail="Original project not found")

        # Check if the project is public or user has access
        is_public = original_project.get("isPublic", False)
        is_owner = original_project.get("owner_id") == current_user.id
        is_user_admin = is_admin(current_user)

        if not is_public and not (is_owner or is_user_admin):
            raise HTTPException(status_code=403, detail="Not authorized to remix this project")

        # Create new project with same name but "[remix]" suffix
        new_name = add_remix_suffix(original_project['name'])

        # Create the new project using the remix-specific function
        return await remix_project_with_thread(db, new_name, current_user, original_project, env_values)

    except HTTPException as e:
        raise e
    except Exception as e:
        logging.error(f"Error in remix_project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error remixing project: {str(e)}")

async def add_team_member_service(db, project_id: str, team_email: str, current_user: User):
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Team features are not available on the free tier plan")

        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if current user is admin or owner
            if not is_admin_or_owner(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to add team members")

            # Get or create user for team member
            user_stmt = select(User).where(User.email == team_email)
            result = await session.execute(user_stmt)
            team_user = result.scalar_one_or_none()

            if not team_user:
                # Create new user with generated kinde_id
                new_kinde_id = f"unknown_{uuid.uuid4().hex[:8]}"
                team_user = User(
                    kinde_id=new_kinde_id,
                    email=team_email,
                    plan="team",  # New users get team plan
                    is_active=True,
                    free_total_token=1
                )
                session.add(team_user)
                await session.flush()
            elif not team_user.plan:
                # Existing user without a plan gets team plan
                team_user.plan = "team"
                team_user.is_active = True
                team_user.free_total_token += 1
                session.add(team_user)
                await session.flush()

            # Update team_emails
            try:
                team_emails = json.loads(project.team_emails) if project.team_emails else []
                if team_email not in team_emails:
                    team_emails.append(team_email)
                    project.team_emails = json.dumps(team_emails)
                    project.last_updated_date = datetime.now().isoformat()
                    await session.commit()

                    # Send invitation email
                    await send_team_invitation_email(
                        invitee_email=team_email,
                        inviter_email=current_user.email,
                        project_name=project.name,
                        project_id=project.project_id
                    )

                    return {"message": "Team member added successfully"}
                else:
                    return {"message": "Email already in team"}
            except json.JSONDecodeError:
                raise HTTPException(status_code=500, detail="Error processing team emails")

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

async def remove_team_member_service(db, project_id: str, team_email: str, current_user: User):
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if current user is admin or owner
            if not is_admin_or_owner(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to remove team members")

            # Update team_emails
            try:
                team_emails = json.loads(project.team_emails) if project.team_emails else []
                if team_email in team_emails:
                    team_emails.remove(team_email)
                    project.team_emails = json.dumps(team_emails)
                    project.last_updated_date = datetime.now().isoformat()
                    await session.commit()
                    return {"message": "Team member removed successfully"}
                else:
                    return {"message": "Email not found in team"}
            except json.JSONDecodeError:
                raise HTTPException(status_code=500, detail="Error processing team emails")

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.post("/project/{project_id}/team")
async def add_team_member(
    project_id: str,
    team_email: str = Body(..., embed=True),
    current_user: User = Depends(get_current_active_user)
):
    return await add_team_member_service(db, project_id, team_email, current_user)

@router.delete("/project/{project_id}/team/{team_email}")
async def remove_team_member(
    project_id: str,
    team_email: str,
    current_user: User = Depends(get_current_active_user)
):
    return await remove_team_member_service(db, project_id, team_email, current_user)

# Add these new Pydantic models for request validation
class TechStackItem(BaseModel):
    key: str
    value: str

class TechStackUpdateRequest(BaseModel):
    tech_stack_array: list[TechStackItem]

class CustomInstructionsUpdateRequest(BaseModel):
    custom_instructions: Optional[str] = None

class PromptItem(BaseModel):
    key: str
    value: str

class PromptUpdateRequest(BaseModel):
    prompts_array: list[PromptItem]

# Add these new API endpoints
@router.post("/project/{project_id}/tech-stack")
async def update_tech_stack(
    project_id: str,
    request: TechStackUpdateRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Update project's tech stack prompt."""
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if user has permission (admin or owner)
            if not is_admin_or_owner(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to update this project")

            # Convert the Pydantic models to dict for JSON serialization
            tech_stack_array = [item.model_dump() for item in request.tech_stack_array]

            # Update the project with new JSON array
            project.tech_stack_prompt = json.dumps(tech_stack_array)
            project.last_updated_date = datetime.now().isoformat()
            await session.commit()

            return {
                "message": "Tech stack prompt updated successfully",
                "project_id": project_id,
                "tech_stack_prompt": tech_stack_array
            }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.post("/project/{project_id}/custom-instructions")
async def update_custom_instructions(
    project_id: str,
    request: CustomInstructionsUpdateRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Update project's custom instructions."""
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if user has permission (admin or owner)
            if not is_admin_or_owner(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to update this project")

            # Update custom instructions
            project.custom_instructions = request.custom_instructions
            project.last_updated_date = datetime.now().isoformat()
            await session.commit()

            return {
                "message": "Custom instructions updated successfully",
                "project_id": project_id,
                "custom_instructions": project.custom_instructions
            }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.post("/project/{project_id}/prompts")
async def update_prompts(
    project_id: str,
    request: PromptUpdateRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Update project's prompts array."""
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if user has permission (admin or owner)
            if not is_admin_or_owner(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to update this project")

            # Convert the Pydantic models to dict for JSON serialization
            prompts_array = [item.model_dump() for item in request.prompts_array]

            # Update the project with new JSON array
            project.users_prompt_Array = json.dumps(prompts_array)
            project.last_updated_date = datetime.now().isoformat()
            await session.commit()

            return {
                "message": "Prompts updated successfully",
                "project_id": project_id,
                "prompts": prompts_array
            }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/project/{project_id}/prompts")
async def get_prompts(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get project's prompts array."""
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if user has permission (admin, owner, or team member)
            if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to access this project")

            # Parse the JSON array or return empty list if None
            prompts = json.loads(project.users_prompt_Array) if project.users_prompt_Array else []

            return {
                "project_id": project_id,
                "prompts": prompts
            }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="Error parsing prompts data")

@router.delete("/project/{project_id}/prompts/{prompt_key}")
async def delete_prompt(
    project_id: str,
    prompt_key: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a specific prompt from the project's prompts array."""
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if user has permission (admin or owner)
            if not is_admin_or_owner(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to update this project")

            # Parse existing prompts or initialize empty list
            prompts = json.loads(project.users_prompt_Array) if project.users_prompt_Array else []

            # Filter out the prompt with matching key
            updated_prompts = [p for p in prompts if p.get('key') != prompt_key]

            # Update the project with new JSON array
            project.users_prompt_Array = json.dumps(updated_prompts)
            project.last_updated_date = datetime.now().isoformat()
            await session.commit()

            return {
                "message": "Prompt deleted successfully",
                "project_id": project_id,
                "prompts": updated_prompts
            }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="Error parsing prompts data")

@router.put("/project/{project_id}/prompts/{prompt_key}")
async def update_prompt(
    project_id: str,
    prompt_key: str,
    request: PromptItem,
    current_user: User = Depends(get_current_active_user)
):
    """Update a specific prompt in the project's prompts array."""
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if user has permission (admin or owner)
            if not is_admin_or_owner(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to update this project")

            # Parse existing prompts or initialize empty list
            prompts = json.loads(project.users_prompt_Array) if project.users_prompt_Array else []

            # Find and update the prompt with matching key
            prompt_found = False
            for prompt in prompts:
                if prompt.get('key') == prompt_key:
                    prompt['value'] = request.value
                    prompt_found = True
                    break

            if not prompt_found:
                raise HTTPException(status_code=404, detail=f"Prompt with key '{prompt_key}' not found")

            # Update the project with new JSON array
            project.users_prompt_Array = json.dumps(prompts)
            project.last_updated_date = datetime.now().isoformat()
            await session.commit()

            return {
                "message": "Prompt updated successfully",
                "project_id": project_id,
                "prompts": prompts
            }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="Error parsing prompts data")

class ImportGitHubRequest(BaseModel):
    github_url: HttpUrl
    name: str  # Add name field

@router.post("/project/import-github")
async def import_github_project(
    request: ImportGitHubRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Create a new project by importing a public GitHub repository."""
    try:
        # Check project limit
        if current_user.project_limit <= 0:
            raise HTTPException(status_code=400, detail="Project limit reached")

        env_manager = EnvManager()

        async with db.get_async_session() as session:
            async with project_creation_lock(session, current_user.id):
                # Create new project
                project_id = str(uuid.uuid4())
                creation_date = datetime.now().isoformat()

                # Import GitHub repository and create environment
                env_data = await env_manager.import_github_repo(
                    str(request.github_url),
                    project_id
                )

                if not env_data:
                    raise HTTPException(status_code=500, detail="Failed to import GitHub repository")

                # Create project in database with provided name
                new_project = Project(
                    project_id=project_id,
                    name=request.name,  # Use the name from the request
                    owner_id=current_user.id,
                    creation_date=creation_date,
                    last_updated_date=creation_date,
                    env_id=env_data.get('env_id'),
                    onboarding_completed=False
                )
                session.add(new_project)

                # Create initial thread
                new_thread = ProjectThread(
                    project_id=project_id,
                    messages=json.dumps([]),
                    creation_date=creation_date,
                    last_updated_date=creation_date
                )
                session.add(new_thread)

                # Deduct project limit
                success = await db.project_service.deduct_project_limit(current_user.kinde_id)
                if not success:
                    raise HTTPException(status_code=400, detail="Project limit reached")

                await session.commit()

                if settings.env == 'prod':
                    # Fire and forget the environment maintenance check
                    asyncio.create_task(check_and_maintain_environments())

                return {
                    "message": "GitHub project imported successfully",
                    "project_data": {
                        "project_id": project_id,
                        "name": request.name,
                        "env_id": env_data.get('env_id'),
                        "initial_thread_id": new_thread.thread_id,
                        "github": env_data.get('github')
                    }
                }

    except HTTPException as e:
        raise e
    except Exception as e:
        logging.error(f"Error importing GitHub project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error importing GitHub project: {str(e)}")

@router.get("/project/{project_id}/environment")
async def get_project_environment(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get environment data for a specific project."""
    try:
        async with db.get_async_session() as session:
            # Get project
            stmt = select(Project).where(Project.project_id == project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(status_code=404, detail="Project not found")

            # Check if user has permission (admin, owner, or team member)
            if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to access this project")

            # Get environment data using EnvManager
            env_manager = EnvManager()
            env_data = await env_manager.get_environment(project.env_id)

            if not env_data:
                raise HTTPException(status_code=404, detail="Environment data not found")

            return {
                "env_id": env_data.get('env_id'),
                "env_url": env_data.get('env_url'),
            }
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        logging.error(f"Error getting environment data: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error retrieving environment data: {str(e)}")


