from sqlalchemy import select
from fastapi import HTTPException
import json
import logging
from core.db import Database
from core.models import Project
from core.platform.user import User
from core.platform.is_admin import is_admin

async def get_env_id_and_verify_access(project_id: str, current_user: User = None) -> str:
    """
    Get environment ID and verify user access.
    
    Args:
        project_id: The ID of the project to check
        current_user: Optional current user object to verify access
        
    Returns:
        str: The environment ID if access is granted
        
    Raises:
        HTTPException: If project not found or user not authorized
    """
    db = Database()
    
    async with db.get_async_session() as session:
        # Get project
        project = await session.execute(select(Project).where(Project.project_id == project_id))
        project = project.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
            
        # If no user provided, just return env_id
        if not current_user:
            return project.env_id
            
        # Check if the current user is an admin
        if is_admin(current_user):
            return project.env_id
            
        # Check if user is owner
        if project.owner_id == current_user.id:
            return project.env_id
            
        # Check if user is in team_emails
        try:
            team_emails = json.loads(project.team_emails) if project.team_emails else []
            if current_user.email in team_emails:
                return project.env_id
        except json.JSONDecodeError:
            logging.error("Failed to parse team_emails JSON")
            
        raise HTTPException(status_code=403, detail="Not authorized to access this project") 