"use client";

import { MessageMarkdown } from "@/components/ui/message-markdown";
import { debug } from "@/lib/debug";
import { parseCommunication } from "@/lib/xml-parser";
import { useWebSocketMessage } from "@/providers/thread-provider/hooks";
import { ContentMessage } from "@/providers/thread-provider/types";
import { useCurrentThreadStore } from "@/stores/current-thread";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useShallow } from "zustand/react/shallow";
import ReasoningContent from "../messages/reasoning-content";
import SoftgenIcon from "../softgen-icon";
import { FileOperations } from "./file-operations";

const StreamingMessage = memo(() => {
  const { isAgentRunning } = useCurrentThreadStore(
    useShallow((state) => ({
      isAgentRunning: state.isAgentRunning,
    })),
  );

  const [content, setContent] = useState("");
  const [reasoningContent, setReasoningContent] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());
  const [sessionComplete, setSessionComplete] = useState(false);

  const handleContentMessage = useCallback((message: ContentMessage) => {
    if (message.isCompleteMessage) {
      setContent("");
      setReasoningContent("");
      setIsStreaming(false);
      setSessionComplete(true);
      // Reset session complete after a delay
      setTimeout(() => setSessionComplete(false), 2000);
    } else {
      setContent(message.data);
      setIsStreaming(true);
      setLastUpdateTime(Date.now());
      setSessionComplete(false);
    }
  }, []);

  // Detect when streaming has stopped (no updates for a while)
  useEffect(() => {
    if (!isStreaming) return;

    const timer = setTimeout(() => {
      setIsStreaming(false);
    }, 1000); // Consider streaming stopped after 1 second of no updates

    return () => clearTimeout(timer);
  }, [lastUpdateTime, isStreaming]);

  // Reset session state when agent stops running
  useEffect(() => {
    if (!isAgentRunning) {
      setSessionComplete(false);
      setIsStreaming(false);
      setContent("");
    }
  }, [isAgentRunning]);

  const handleReasoningMessage = useCallback((message: ContentMessage) => {
    setReasoningContent((prev) => prev + message.data);
  }, []);

  useWebSocketMessage<ContentMessage>("content", handleContentMessage);
  useWebSocketMessage<ContentMessage>("reasoning", handleReasoningMessage);

  const hasActionsXML = useMemo(() => (content ? content.includes("<actions") : false), [content]);

  const communication = useMemo(
    () => parseCommunication(content as string)?.communication,
    [content],
  );

  // Early return to avoid rendering empty component
  if (!content && !reasoningContent) return null;

  debug("StreamingMessage", {
    content,
    isStreaming,
    hasActionsXML,
    sessionComplete,
    isAgentRunning,
  });

  return (
    <>
      <div className="flex w-full flex-col space-y-2 rounded-md">
        <div className="flex w-full flex-col items-start">
          <div className="mb-2 flex w-full flex-row items-start justify-start gap-2">
            <div className="flex h-fit w-fit items-center justify-center">
              <SoftgenIcon />
            </div>
          </div>

          <div className="w-full">
            {reasoningContent && (
              <ReasoningContent isInProgress collapsed={false} content={reasoningContent} />
            )}

            {communication && (
              <div className="mb-3">
                <MessageMarkdown>
                  {communication
                    .replaceAll("<communication>", "")
                    .replaceAll("</communication>", "")}
                </MessageMarkdown>
              </div>
            )}

            {content && <FileOperations content={content} />}

            {!hasActionsXML && !communication && content && (
              <div className="whitespace-pre-wrap text-[0.95rem]">{content}</div>
            )}
          </div>
        </div>
      </div>
    </>
  );
});

StreamingMessage.displayName = "StreamingMessage";
export default StreamingMessage;
