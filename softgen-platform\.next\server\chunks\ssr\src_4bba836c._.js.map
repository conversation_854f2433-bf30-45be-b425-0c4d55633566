{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/project-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProjectProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProjectProvider() from the server but ProjectProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/project-provider.tsx <module evaluation>\",\n    \"ProjectProvider\",\n);\nexport const SandBoxState = registerClientReference(\n    function() { throw new Error(\"Attempted to call SandBoxState() from the server but SandBoxState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/project-provider.tsx <module evaluation>\",\n    \"SandBoxState\",\n);\nexport const useProject = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProject() from the server but useProject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/project-provider.tsx <module evaluation>\",\n    \"useProject\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oEACA;AAEG,MAAM,aAAa,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,oEACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/project-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProjectProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProjectProvider() from the server but ProjectProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/project-provider.tsx\",\n    \"ProjectProvider\",\n);\nexport const SandBoxState = registerClientReference(\n    function() { throw new Error(\"Attempted to call SandBoxState() from the server but SandBoxState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/project-provider.tsx\",\n    \"SandBoxState\",\n);\nexport const useProject = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProject() from the server but useProject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/project-provider.tsx\",\n    \"useProject\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,gDACA;AAEG,MAAM,aAAa,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,gDACA", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/thread-provider/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThreadContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThreadContext() from the server but ThreadContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/thread-provider/index.tsx <module evaluation>\",\n    \"ThreadContext\",\n);\nexport const ThreadProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThreadProvider() from the server but ThreadProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/thread-provider/index.tsx <module evaluation>\",\n    \"ThreadProvider\",\n);\nexport const useThreadContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThread<PERSON>ontext() from the server but useThread<PERSON>ontext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/thread-provider/index.tsx <module evaluation>\",\n    \"useThreadContext\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yEACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/thread-provider/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThreadContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThreadContext() from the server but ThreadContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/thread-provider/index.tsx\",\n    \"ThreadContext\",\n);\nexport const ThreadProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThreadProvider() from the server but ThreadProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/thread-provider/index.tsx\",\n    \"ThreadProvider\",\n);\nexport const useThreadContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call useThreadContext() from the server but useThread<PERSON>ontext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/thread-provider/index.tsx\",\n    \"useThreadContext\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qDACA", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/app/%28main%29/project/%5Bid%5D/layout.tsx"], "sourcesContent": ["import { ProjectProvider } from \"@/providers/project-provider\";\r\nimport { ThreadProvider } from \"@/providers/thread-provider\";\r\nimport React from \"react\";\r\n\r\ntype Props = {\r\n  children: React.ReactNode;\r\n  params: Promise<{\r\n    id: string;\r\n  }>;\r\n};\r\n\r\nconst Layout = async (props: Props) => {\r\n  const { id: projectId } = await props.params;\r\n\r\n  if (!projectId) return null;\r\n\r\n  return (\r\n    <ProjectProvider projectId={projectId}>\r\n      <ThreadProvider projectId={projectId}>{props.children}</ThreadProvider>\r\n    </ProjectProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,SAAS,OAAO;IACpB,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG,MAAM,MAAM,MAAM;IAE5C,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6VAAC,wIAAA,CAAA,kBAAe;QAAC,WAAW;kBAC1B,cAAA,6VAAC,gJAAA,CAAA,iBAAc;YAAC,WAAW;sBAAY,MAAM,QAAQ;;;;;;;;;;;AAG3D;uCAEe", "debugId": null}}]}