import asyncio
from core.envs.env_manager import EnvManager

async def main():
    # Initialize EnvManager
    env_manager = EnvManager()
    
    # GitHub URL to import
    github_url = "https://github.com/softgenai/sg-adcec30a-0a84-4b5a-8ef2-6894ededd5c1-1747944935"
    
    # Generate a new project ID
    project_id = "dda107de-c68b-472d-9711-c399b733b11a"
    
    # Import the repository
    env_data = await env_manager.import_github_repo(github_url, project_id)
    
    if env_data:
        print("Successfully imported GitHub repository!")
        print(f"Environment ID: {env_data.get('env_id')}")
        print(f"GitHub Info: {env_data.get('github')}")
    else:
        print("Failed to import GitHub repository")

if __name__ == "__main__":
    asyncio.run(main()) 