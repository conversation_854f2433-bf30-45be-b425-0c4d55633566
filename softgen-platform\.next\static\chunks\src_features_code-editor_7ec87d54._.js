(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/features/code-editor/file-tree/index.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/classic/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/modal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/select.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/skeleton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/navigate-file.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$ChevronDown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/ChevronDown.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$ChevronRight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/ChevronRight.js [app-client] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$DotsVertical$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DotsVertical$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/DotsVertical.js [app-client] (ecmascript) <export default as DotsVertical>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons-solid/FileTextSolid.js [app-client] (ecmascript) <export default as FileTextSolid>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FolderPlusSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FolderPlusSolid$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons-solid/FolderPlusSolid.js [app-client] (ecmascript) <export default as FolderPlusSolid>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Move$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/Move.js [app-client] (ecmascript) <export default as Move>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Trash$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/Trash.js [app-client] (ecmascript) <export default as Trash>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/Upload.js [app-client] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$475$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2d$minus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CopyMinusIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.475.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy-minus.js [app-client] (ecmascript) <export default as CopyMinusIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/io5/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/lu/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
const MoveFileModalContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/src/features/code-editor/file-tree/move-file-modal.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i));
_c = MoveFileModalContent;
const NewFileModalContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/src/features/code-editor/file-tree/new-file-modal.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i));
_c1 = NewFileModalContent;
const NewFolderModalContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/src/features/code-editor/file-tree/new-folder-modal.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i));
_c2 = NewFolderModalContent;
const DeleteModalContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/src/features/code-editor/file-tree/delete-modal.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i));
_c3 = DeleteModalContent;
const UploadFileModalContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/src/features/code-editor/file-tree/upload-file-modal-content.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i));
_c4 = UploadFileModalContent;
const FileTreeSkeleton = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-1",
        children: Array.from({
            length: 25
        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex animate-pulse items-center gap-2 rounded-sm pb-1.5",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                    className: "h-2 w-full rounded-sm"
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 64,
                    columnNumber: 11
                }, this)
            }, index, false, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
};
_c5 = FileTreeSkeleton;
const flattenFileTree = (nodes, basePath = "")=>{
    const paths = [];
    nodes.forEach((node)=>{
        const fullPath = basePath ? `${basePath}/${node.name}` : node.name;
        if (!node.isDirectory) {
            paths.push(fullPath);
        } else if (node.children) {
            paths.push(...flattenFileTree(node.children, fullPath));
        }
    });
    return paths;
};
const FileTree = ({ projectId, onFileSelect, selectedFile, className, filesAndFolders, isLoadingFilePaths, allFilePaths, refetchFileTree })=>{
    _s();
    const [expandedFolders, setExpandedFolders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [loadingFolders, setLoadingFolders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [directoryContents, setDirectoryContents] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const setFile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSetFile"])();
    const prevFilesAndFoldersRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(filesAndFolders);
    const previousPathsLengthRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const [showContextMenuModal, setShowContextMenuModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        file: {
            state: false,
            file: ""
        },
        folder: {
            state: false,
            file: ""
        },
        move: {
            state: false,
            file: ""
        },
        delete: {
            state: false,
            files: ""
        },
        upload: {
            state: false,
            files: ""
        }
    });
    // Convert FileNode tree to flat array of file paths
    const flatFilePaths = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FileTree.useMemo[flatFilePaths]": ()=>{
            return flattenFileTree(allFilePaths || []);
        }
    }["FileTree.useMemo[flatFilePaths]"], [
        allFilePaths
    ]);
    // Store the previous filesAndFolders to prevent flickering during updates
    const displayedFilesAndFolders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FileTree.useMemo[displayedFilesAndFolders]": ()=>{
            // Use previous value if current is empty but we have data from before
            if (filesAndFolders.length === 0 && prevFilesAndFoldersRef.current.length > 0) {
                return prevFilesAndFoldersRef.current;
            }
            // Otherwise use the current value and update the ref
            prevFilesAndFoldersRef.current = filesAndFolders;
            return filesAndFolders;
        }
    }["FileTree.useMemo[displayedFilesAndFolders]"], [
        filesAndFolders
    ]);
    // Load saved expanded folders from localStorage when component mounts or project changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FileTree.useEffect": ()=>{
            const savedExpandedFolders = localStorage.getItem(`expandedFolders-${projectId}`);
            if (savedExpandedFolders) {
                try {
                    setExpandedFolders(JSON.parse(savedExpandedFolders));
                } catch (e) {
                    console.error("Failed to parse saved expanded folders", e);
                }
            }
        }
    }["FileTree.useEffect"], [
        projectId
    ]);
    // Save expanded folders to localStorage whenever they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FileTree.useEffect": ()=>{
            if (Object.keys(expandedFolders).length > 0) {
                localStorage.setItem(`expandedFolders-${projectId}`, JSON.stringify(expandedFolders));
            }
        }
    }["FileTree.useEffect"], [
        expandedFolders,
        projectId
    ]);
    // Check if the file structure has changed and update expandedFolders if needed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FileTree.useEffect": ()=>{
            // If the file paths have been updated (especially after streaming)
            if (flatFilePaths.length !== previousPathsLengthRef.current) {
                previousPathsLengthRef.current = flatFilePaths.length;
                // Get the currently saved folder structure
                const savedExpandedFolders = localStorage.getItem(`expandedFolders-${projectId}`);
                if (savedExpandedFolders) {
                    try {
                        const parsedFolders = JSON.parse(savedExpandedFolders);
                        // Find and expand parent folders of the selected file if it exists
                        if (selectedFile) {
                            const pathParts = selectedFile.split("/");
                            let currentPath = "";
                            // Create an updated expanded folders object
                            const updatedExpandedFolders = {
                                ...parsedFolders
                            };
                            // Expand all parent directories of the selected file
                            for(let i = 0; i < pathParts.length - 1; i++){
                                currentPath = currentPath ? `${currentPath}/${pathParts[i]}` : pathParts[i];
                                updatedExpandedFolders[currentPath] = true;
                            }
                            setExpandedFolders(updatedExpandedFolders);
                            localStorage.setItem(`expandedFolders-${projectId}`, JSON.stringify(updatedExpandedFolders));
                        }
                    } catch (e) {
                        console.error("Failed to process expanded folders after file structure change", e);
                    }
                }
            }
        }
    }["FileTree.useEffect"], [
        allFilePaths,
        selectedFile,
        projectId
    ]);
    const toggleFolder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileTree.useCallback[toggleFolder]": async (path, event)=>{
            event.stopPropagation();
            const isCurrentlyExpanded = expandedFolders[path];
            if (!isCurrentlyExpanded && !directoryContents[path]) {
                setLoadingFolders({
                    "FileTree.useCallback[toggleFolder]": (prev)=>({
                            ...prev,
                            [path]: true
                        })
                }["FileTree.useCallback[toggleFolder]"]);
                try {
                    const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFiles"])(projectId, path);
                    if (response && response.files) {
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const newFiles = response.files.map({
                            "FileTree.useCallback[toggleFolder].newFiles": (file)=>({
                                    name: file.name,
                                    isDirectory: file.isDirectory,
                                    children: file.children || []
                                })
                        }["FileTree.useCallback[toggleFolder].newFiles"]);
                        setDirectoryContents({
                            "FileTree.useCallback[toggleFolder]": (prev)=>({
                                    ...prev,
                                    [path]: newFiles
                                })
                        }["FileTree.useCallback[toggleFolder]"]);
                    }
                } catch (error) {
                    console.error("Error fetching directory contents:", error);
                } finally{
                    setLoadingFolders({
                        "FileTree.useCallback[toggleFolder]": (prev)=>({
                                ...prev,
                                [path]: false
                            })
                    }["FileTree.useCallback[toggleFolder]"]);
                }
            }
            setExpandedFolders({
                "FileTree.useCallback[toggleFolder]": (prev)=>{
                    const newState = {
                        ...prev
                    };
                    newState[path] = !prev[path];
                    localStorage.setItem(`expandedFolders-${projectId}`, JSON.stringify(newState));
                    return newState;
                }
            }["FileTree.useCallback[toggleFolder]"]);
        }
    }["FileTree.useCallback[toggleFolder]"], [
        expandedFolders,
        projectId,
        directoryContents
    ]);
    const getFileIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileTree.useCallback[getFileIcon]": (fileName)=>{
            const extension = fileName.split(".").pop()?.toLowerCase();
            switch(extension){
                case "js":
                case "mjs":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-yellow-500",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 246,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 245,
                        columnNumber: 11
                    }, this);
                case "jsx":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-blue-400",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 252,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 251,
                        columnNumber: 11
                    }, this);
                case "ts":
                case "tsx":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-blue-600",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 259,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 258,
                        columnNumber: 11
                    }, this);
                case "html":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-orange-500",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 265,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 264,
                        columnNumber: 11
                    }, this);
                case "css":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-purple-500",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 271,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 270,
                        columnNumber: 11
                    }, this);
                case "md":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-gray-500",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 277,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 276,
                        columnNumber: 11
                    }, this);
                case "png":
                case "jpg":
                case "jpeg":
                case "webp":
                case "gif":
                case "svg":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-green-500",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 288,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 287,
                        columnNumber: 11
                    }, this);
                case "json":
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-amber-500",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                            className: "h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 294,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 293,
                        columnNumber: 11
                    }, this);
                default:
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                        className: "h-4 w-4"
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 298,
                        columnNumber: 16
                    }, this);
            }
        }
    }["FileTree.useCallback[getFileIcon]"], []);
    const renderTreeNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileTree.useCallback[renderTreeNode]": (file, basePath, depth)=>{
            const fullPath = `${basePath}${file.name}`;
            const isSelected = fullPath === selectedFile;
            const isExpanded = expandedFolders[fullPath];
            const isLoading = loadingFolders[fullPath];
            const children = directoryContents[fullPath] || file.children || [];
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "file-tree-node",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 transition-colors", isSelected ? "bg-primary/10 font-medium text-primary" : "hover:bg-foreground/5"),
                        style: {
                            paddingLeft: `${depth * 12 + 8}px`
                        },
                        onClick: {
                            "FileTree.useCallback[renderTreeNode]": (e)=>{
                                if (file.isDirectory) {
                                    setFile("");
                                    toggleFolder(fullPath, e);
                                } else {
                                    onFileSelect(fullPath);
                                }
                            }
                        }["FileTree.useCallback[renderTreeNode]"],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex w-full items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex min-w-0 flex-1 items-center gap-2",
                                    children: [
                                        file.isDirectory ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mr-1 flex h-4 w-4 items-center justify-center",
                                                    children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "h-3.5 w-3.5 animate-spin rounded-full border-2 border-primary border-t-transparent"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 333,
                                                        columnNumber: 25
                                                    }, this) : isExpanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$ChevronDown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                        className: "h-3.5 w-3.5 text-muted-foreground"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 335,
                                                        columnNumber: 25
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$ChevronRight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                        className: "h-3.5 w-3.5 text-muted-foreground"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 337,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                    lineNumber: 331,
                                                    columnNumber: 21
                                                }, this),
                                                isExpanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoFolderOpen"], {
                                                    className: "h-4 w-4 text-primary"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                    lineNumber: 341,
                                                    columnNumber: 23
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoFolder"], {
                                                    className: "h-4 w-4 text-primary"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                    lineNumber: 343,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 330,
                                            columnNumber: 19
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-5",
                                            children: getFileIcon(file.name)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 347,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "min-w-0 flex-1 truncate text-sm",
                                            children: file.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 349,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 328,
                                    columnNumber: 15
                                }, this),
                                !file.isDirectory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                            asChild: true,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                variant: "ghost",
                                                size: "icon",
                                                className: "h-fit w-fit flex-shrink-0 items-center justify-between p-0 text-primary hover:text-primary",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$DotsVertical$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DotsVertical$3e$__["DotsVertical"], {
                                                    className: "size-4 text-primary"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                    lineNumber: 360,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 355,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 354,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                            className: "min-w-28",
                                            side: "bottom",
                                            align: "end",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                    className: "flex items-center justify-between",
                                                    onClick: {
                                                        "FileTree.useCallback[renderTreeNode]": ()=>{
                                                            setShowContextMenuModal({
                                                                ...showContextMenuModal,
                                                                move: {
                                                                    state: true,
                                                                    file: fullPath
                                                                }
                                                            });
                                                        }
                                                    }["FileTree.useCallback[renderTreeNode]"],
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: "Move"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                            lineNumber: 374,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Move$3e$__["Move"], {
                                                            className: "size-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                            lineNumber: 375,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                    lineNumber: 365,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                    onClick: {
                                                        "FileTree.useCallback[renderTreeNode]": ()=>{
                                                            setShowContextMenuModal({
                                                                ...showContextMenuModal,
                                                                delete: {
                                                                    state: true,
                                                                    files: fullPath
                                                                }
                                                            });
                                                        }
                                                    }["FileTree.useCallback[renderTreeNode]"],
                                                    className: "flex items-center justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-destructive/90 hover:text-destructive",
                                                            children: "Delete"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                            lineNumber: 386,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Trash$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__["Trash"], {
                                                            className: "size-4 text-destructive/90 hover:text-destructive"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                            lineNumber: 387,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                    lineNumber: 377,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 364,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 353,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 327,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 312,
                        columnNumber: 11
                    }, this),
                    file.isDirectory && isExpanded && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "nested-files",
                        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "py-1.5 text-xs italic text-muted-foreground",
                            style: {
                                paddingLeft: `${(depth + 1) * 12 + 24}px`
                            },
                            children: "Loading..."
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 397,
                            columnNumber: 17
                        }, this) : children.length > 0 ? children.map({
                            "FileTree.useCallback[renderTreeNode]": (childFile)=>renderTreeNode(childFile, `${fullPath}/`, depth + 1)
                        }["FileTree.useCallback[renderTreeNode]"]) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "py-1.5 text-xs italic text-muted-foreground",
                            style: {
                                paddingLeft: `${(depth + 1) * 12 + 24}px`
                            },
                            children: "Empty folder"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                            lineNumber: 408,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 395,
                        columnNumber: 13
                    }, this)
                ]
            }, fullPath, true, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 311,
                columnNumber: 9
            }, this);
        }
    }["FileTree.useCallback[renderTreeNode]"], [
        expandedFolders,
        selectedFile,
        onFileSelect,
        toggleFolder,
        getFileIcon,
        setFile,
        loadingFolders,
        directoryContents
    ]);
    const onCloseFileModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FileTree.useCallback[onCloseFileModal]": ()=>{
            setShowContextMenuModal({
                file: {
                    state: false,
                    file: ""
                },
                folder: {
                    state: false,
                    file: ""
                },
                move: {
                    state: false,
                    file: ""
                },
                delete: {
                    state: false,
                    files: ""
                },
                upload: {
                    state: false,
                    files: ""
                }
            });
        }
    }["FileTree.useCallback[onCloseFileModal]"], []);
    const folderList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FileTree.useMemo[folderList]": ()=>{
            const folders = allFilePaths.filter({
                "FileTree.useMemo[folderList].folders": (file)=>file.isDirectory
            }["FileTree.useMemo[folderList].folders"]).map({
                "FileTree.useMemo[folderList].folders": (folder)=>folder.name
            }["FileTree.useMemo[folderList].folders"]).sort();
            return [
                "/",
                ...folders
            ];
        }
    }["FileTree.useMemo[folderList]"], [
        allFilePaths
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex h-screen w-full flex-1 flex-col", className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "hidden items-center justify-between border-b px-3 py-2 md:flex",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-xs font-semibold uppercase text-primary",
                        children: "Explorer"
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 453,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                className: "size-7",
                                onClick: ()=>{
                                    setExpandedFolders({});
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$475$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2d$minus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CopyMinusIcon$3e$__["CopyMinusIcon"], {
                                    className: "size-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 464,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 456,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "icon",
                                            className: "size-7",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LuUpload"], {
                                                className: "size-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 470,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 469,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                        lineNumber: 468,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                        align: "end",
                                        className: "min-w-[10rem]",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>{
                                                    setShowContextMenuModal({
                                                        ...showContextMenuModal,
                                                        file: {
                                                            state: true,
                                                            file: ""
                                                        }
                                                    });
                                                },
                                                className: "group flex items-center justify-between gap-2 px-2.5",
                                                children: [
                                                    "New File",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                                                        className: "size-4 text-primary/85 group-hover:text-primary"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 484,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 474,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>{
                                                    setShowContextMenuModal({
                                                        ...showContextMenuModal,
                                                        folder: {
                                                            state: true,
                                                            file: ""
                                                        }
                                                    });
                                                },
                                                className: "group flex items-center justify-between gap-2 px-2.5",
                                                children: [
                                                    "New Folder",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FolderPlusSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FolderPlusSolid$3e$__["FolderPlusSolid"], {
                                                        className: "size-4 text-primary/85 group-hover:text-primary"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 497,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 487,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>{
                                                    setShowContextMenuModal({
                                                        ...showContextMenuModal,
                                                        upload: {
                                                            state: true,
                                                            files: selectedFile
                                                        }
                                                    });
                                                },
                                                className: "group flex items-center justify-between gap-2 px-2.5",
                                                children: [
                                                    "Upload Files",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                        className: "size-4 text-primary/85 group-hover:text-primary"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 510,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 500,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                        lineNumber: 473,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 467,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 455,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 452,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "hidden h-screen flex-1 flex-col overflow-y-auto bg-background p-1 pb-24 md:flex",
                children: isLoadingFilePaths && flatFilePaths.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FileTreeSkeleton, {}, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 518,
                    columnNumber: 11
                }, this) : displayedFilesAndFolders.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex h-full items-center justify-center text-sm text-muted-foreground",
                    children: "No files found"
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 520,
                    columnNumber: 11
                }, this) : displayedFilesAndFolders.map((file)=>renderTreeNode(file, "", 0))
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 516,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex w-full items-center gap-2 p-2 md:hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"], {
                        value: selectedFile,
                        onValueChange: (value)=>{
                            onFileSelect(value);
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectTrigger"], {
                                className: "w-[83%] justify-between bg-background px-3 font-normal",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectValue"], {
                                    placeholder: "Search file...",
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(!selectedFile && "text-primary/80"),
                                    children: selectedFile ? flatFilePaths.find((filePath)=>filePath === selectedFile)?.split("/").pop() : "Search file..."
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 536,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 535,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectContent"], {
                                className: "w-full min-w-[var(--radix-popper-anchor-width)] border-primary/10",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectGroup"], {
                                    children: flatFilePaths.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-2 py-4 text-center text-sm text-muted-foreground",
                                        children: "No files found."
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                        lineNumber: 551,
                                        columnNumber: 17
                                    }, this) : flatFilePaths.map((filePath)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectItem"], {
                                            value: filePath,
                                            children: filePath
                                        }, filePath, false, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 556,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 549,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 548,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 529,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-1.5",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "icon",
                                className: "m-0 text-primary hover:text-primary",
                                onClick: ()=>{
                                    setShowContextMenuModal({
                                        ...showContextMenuModal,
                                        file: {
                                            state: true,
                                            file: ""
                                        }
                                    });
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FileTextSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileTextSolid$3e$__["FileTextSolid"], {
                                    className: "size-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 577,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 566,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "icon",
                                className: "m-0 text-primary hover:text-primary",
                                onClick: ()=>{
                                    setShowContextMenuModal({
                                        ...showContextMenuModal,
                                        folder: {
                                            state: true,
                                            file: ""
                                        }
                                    });
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2d$solid$2f$FolderPlusSolid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FolderPlusSolid$3e$__["FolderPlusSolid"], {
                                    className: "size-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 590,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 579,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "icon",
                                className: "m-0 text-primary hover:text-primary",
                                onClick: ()=>{
                                    setShowContextMenuModal({
                                        ...showContextMenuModal,
                                        upload: {
                                            state: true,
                                            files: selectedFile
                                        }
                                    });
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                    className: "size-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                    lineNumber: 604,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 593,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                        asChild: true,
                                        className: "ml-auto",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            size: "icon",
                                            disabled: !selectedFile,
                                            className: "ml-auto flex items-center justify-center text-primary hover:text-primary",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$DotsVertical$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DotsVertical$3e$__["DotsVertical"], {
                                                className: "size-4 text-primary/90 hover:text-primary"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 615,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                            lineNumber: 609,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                        lineNumber: 608,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                        className: "min-w-28",
                                        side: "bottom",
                                        align: "end",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                className: "flex items-center justify-between text-primary/90 hover:text-primary",
                                                onClick: ()=>{
                                                    setShowContextMenuModal({
                                                        ...showContextMenuModal,
                                                        move: {
                                                            state: true,
                                                            file: selectedFile
                                                        }
                                                    });
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "Move"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 629,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Move$3e$__["Move"], {
                                                        className: "size-4"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 630,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 620,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$classic$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                onClick: ()=>{
                                                    setShowContextMenuModal({
                                                        ...showContextMenuModal,
                                                        delete: {
                                                            state: true,
                                                            files: selectedFile
                                                        }
                                                    });
                                                },
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-destructive/90 hover:text-destructive",
                                                        children: "Delete"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 641,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Trash$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__["Trash"], {
                                                        className: "size-4 text-destructive/90 hover:text-destructive"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                        lineNumber: 642,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                                lineNumber: 632,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                        lineNumber: 619,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                                lineNumber: 607,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                        lineNumber: 565,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 528,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LazyModal"], {
                open: showContextMenuModal.move.state,
                onOpenChange: onCloseFileModal,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MoveFileModalContent, {
                    onClose: onCloseFileModal,
                    file: showContextMenuModal.move.file,
                    folderList: folderList || [
                        "/"
                    ],
                    projectId: projectId,
                    refetchFileTree: refetchFileTree
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 650,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 649,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LazyModal"], {
                open: showContextMenuModal.file.state,
                onOpenChange: onCloseFileModal,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(NewFileModalContent, {
                    onClose: onCloseFileModal,
                    file: showContextMenuModal.file.file,
                    folderList: folderList || [
                        "/"
                    ],
                    projectId: projectId,
                    refetchFileTree: refetchFileTree
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 660,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 659,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LazyModal"], {
                open: showContextMenuModal.folder.state,
                onOpenChange: onCloseFileModal,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(NewFolderModalContent, {
                    onClose: onCloseFileModal,
                    file: showContextMenuModal.folder.file,
                    folderList: folderList || [
                        "/"
                    ],
                    projectId: projectId,
                    refetchFileTree: refetchFileTree
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 670,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 669,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LazyModal"], {
                open: showContextMenuModal.delete.state,
                onOpenChange: onCloseFileModal,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DeleteModalContent, {
                    onClose: onCloseFileModal,
                    file: showContextMenuModal.delete.files,
                    projectId: projectId,
                    refetchFileTree: refetchFileTree
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 680,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 679,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LazyModal"], {
                open: showContextMenuModal.upload.state,
                onOpenChange: onCloseFileModal,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(UploadFileModalContent, {
                    onClose: onCloseFileModal,
                    folderList: folderList || [
                        "/"
                    ],
                    projectId: projectId,
                    refetchFileTree: refetchFileTree
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                    lineNumber: 689,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
                lineNumber: 688,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/code-editor/file-tree/index.tsx",
        lineNumber: 451,
        columnNumber: 5
    }, this);
};
_s(FileTree, "xTa1x/jaWYVKE3cYIXg4PqhIK9A=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSetFile"]
    ];
});
_c6 = FileTree;
const __TURBOPACK__default__export__ = FileTree;
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__turbopack_context__.k.register(_c, "MoveFileModalContent");
__turbopack_context__.k.register(_c1, "NewFileModalContent");
__turbopack_context__.k.register(_c2, "NewFolderModalContent");
__turbopack_context__.k.register(_c3, "DeleteModalContent");
__turbopack_context__.k.register(_c4, "UploadFileModalContent");
__turbopack_context__.k.register(_c5, "FileTreeSkeleton");
__turbopack_context__.k.register(_c6, "FileTree");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/code-editor/text-editor/file-update-modal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/loading.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$DangerTriangle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DangerTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/DangerTriangle.js [app-client] (ecmascript) <export default as DangerTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$motion$40$12$2e$18$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$motion$2f$dist$2f$es$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/motion@12.18.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/motion/dist/es/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$motion$40$12$2e$18$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$motion$2f$dist$2f$es$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/motion@12.18.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/motion/dist/es/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
const getFileStatusLabel = (fileStatus)=>{
    switch(fileStatus){
        case "unsaved":
            return "Unsaved Changes";
        case "saved":
            return "Saved Changes";
        case "error":
            return "Discard Changes";
        case "no-changes":
            return "No Changes";
    }
};
const FileUpdateModal = ({ side, fileStatus, onReset, isMobile, onSave, isSaving = false })=>{
    const shouldShowSaveButton = fileStatus === "unsaved";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("z-50 flex flex-col gap-2", side === "left" && "absolute -right-[35%] bottom-20 z-50 w-full", side === "right" && "absolute bottom-20 left-[25%] z-50 w-full", side === "left" && isSaving && "absolute -right-[35%] bottom-20 z-50 w-full", side === "right" && isSaving && "absolute bottom-20 left-[0%] z-50 w-full", isMobile && "absolute bottom-20 left-1/4 z-50 w-full"),
        children: shouldShowSaveButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$motion$40$12$2e$18$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$motion$2f$dist$2f$es$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
            mode: "sync",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$motion$40$12$2e$18$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$motion$2f$dist$2f$es$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 10
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                exit: {
                    opacity: 0,
                    y: 10
                },
                transition: {
                    duration: 0.2
                },
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex w-full max-w-sm flex-col", isSaving && "mx-auto max-w-fit transition-all duration-200", isMobile && "w-fit"),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                    className: "w-full bg-primary text-primary-foreground shadow-sm",
                    children: isSaving ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex w-full items-center justify-between gap-2 p-3 py-2 text-sm", isMobile ? "flex-col gap-4" : "flex-row"),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    className: "size-5 animate-spin text-background"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                    lineNumber: 75,
                                    columnNumber: 21
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    className: "text-sm font-medium text-primary-foreground",
                                    children: "Saving..."
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                    lineNumber: 76,
                                    columnNumber: 21
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                            lineNumber: 74,
                            columnNumber: 19
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                        lineNumber: 68,
                        columnNumber: 17
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex w-full items-center justify-between gap-2 p-3 py-2 text-sm", isMobile ? "flex-col gap-4" : "flex-row"),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center gap-2", isMobile && "items-start justify-start"),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$DangerTriangle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DangerTriangle$3e$__["DangerTriangle"], {
                                        className: "h-5 w-5 font-semibold"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                        lineNumber: 92,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                        className: "text-sm font-medium text-primary-foreground",
                                        children: getFileStatusLabel(fileStatus)
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                        lineNumber: 93,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                lineNumber: 86,
                                columnNumber: 19
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-end gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "invert-outline-primary",
                                        size: "sm",
                                        className: "h-8 px-3 text-xs",
                                        onClick: ()=>{
                                            if (onReset) onReset();
                                        },
                                        children: "Reset"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                        lineNumber: 99,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "invert",
                                        size: "sm",
                                        className: "h-8 text-xs text-primary",
                                        onClick: onSave,
                                        disabled: isSaving,
                                        children: isSaving ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "size-3 animate-spin"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                                    lineNumber: 118,
                                                    columnNumber: 27
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "Saving..."
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                                    lineNumber: 119,
                                                    columnNumber: 27
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                            lineNumber: 117,
                                            columnNumber: 25
                                        }, this) : "Save Changes"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                        lineNumber: 109,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                                lineNumber: 98,
                                columnNumber: 19
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                        lineNumber: 80,
                        columnNumber: 17
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                    lineNumber: 66,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
                lineNumber: 55,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
            lineNumber: 54,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/code-editor/text-editor/file-update-modal.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
};
_c = FileUpdateModal;
const __TURBOPACK__default__export__ = FileUpdateModal;
var _c;
__turbopack_context__.k.register(_c, "FileUpdateModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/code-editor/text-editor/index.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/alert-dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/breadcrumb.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/loading.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$text$2d$shimmer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/text-shimmer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/typography.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/navigate-file.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$File$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__File$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/File.js [app-client] (ecmascript) <export default as File>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$themes$40$0$2e$4$2e$6_react$2d$dom_e207e685aa9cc81adf4eaedb8666d505$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/path-browserify/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$tb$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/tb/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react/shallow.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$hint$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/hint.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/global/toast.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$text$2d$editor$2f$file$2d$update$2d$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/code-editor/text-editor/file-update-modal.tsx [app-client] (ecmascript)");
;
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Editor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@monaco-editor+react@4.7.0__ecaa62d5fac79b011ca01832728852b0/node_modules/@monaco-editor/react/dist/index.mjs [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/node_modules/.pnpm/@monaco-editor+react@4.7.0__ecaa62d5fac79b011ca01832728852b0/node_modules/@monaco-editor/react/dist/index.mjs [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
_c = Editor;
const darkThemeData = {
    base: "vs-dark",
    inherit: true,
    rules: [
        {
            token: "comment",
            foreground: "eef0f98f"
        },
        {
            token: "keyword",
            foreground: "54b9ff"
        },
        {
            token: "string",
            foreground: "ffd493"
        },
        {
            token: "number",
            foreground: "ffd493"
        },
        {
            token: "operator",
            foreground: "eef0f9"
        },
        {
            token: "identifier",
            foreground: "4bf3c8"
        },
        {
            token: "type",
            foreground: "acafff"
        },
        {
            token: "function",
            foreground: "00daef"
        }
    ],
    colors: {
        "editor.background": "#111115",
        "editor.foreground": "#eef0f9",
        "editorCursor.foreground": "#aeafad",
        "editor.lineHighlightBackground": "#23262d",
        "editorLineNumber.foreground": "#545864",
        "editor.selectionBackground": "#ad5dca44",
        "editor.inactiveSelectionBackground": "#2a2d34",
        "editorIndentGuide.background": "#343841",
        "editor.selectionHighlightBackground": "#add6ff34",
        "editor.wordHighlightBackground": "#494949b8",
        "editorBracketMatch.background": "#545864",
        "editorBracketMatch.border": "#ffffff00"
    }
};
const lightThemeData = {
    base: "vs",
    inherit: true,
    rules: [
        {
            token: "comment",
            foreground: "6a7280"
        },
        {
            token: "keyword",
            foreground: "0077cc"
        },
        {
            token: "string",
            foreground: "d97706"
        },
        {
            token: "number",
            foreground: "d97706"
        },
        {
            token: "operator",
            foreground: "111827"
        },
        {
            token: "identifier",
            foreground: "059669"
        },
        {
            token: "type",
            foreground: "6366f1"
        },
        {
            token: "function",
            foreground: "0891b2"
        },
        {
            token: "variable",
            foreground: "111827"
        },
        {
            token: "constant",
            foreground: "0369a1"
        },
        {
            token: "regexp",
            foreground: "be123c"
        },
        {
            token: "entity.name.tag",
            foreground: "7f1d1d"
        },
        {
            token: "entity.other.attribute-name",
            foreground: "b91c1c"
        },
        {
            token: "storage",
            foreground: "1e40af"
        },
        {
            token: "keyword.control",
            foreground: "7e22ce"
        }
    ],
    colors: {
        "editor.background": "#fafafa",
        "editor.foreground": "#000000",
        "editorCursor.foreground": "#000000",
        "editor.lineHighlightBackground": "#F8F8F8",
        "editorLineNumber.foreground": "#767676",
        "editor.selectionBackground": "#ADD6FF",
        "editor.inactiveSelectionBackground": "#E5EBF1",
        "editorIndentGuide.background1": "#D3D3D3",
        "editorIndentGuide.activeBackground1": "#939393",
        "editor.selectionHighlightBackground": "#ADD6FF80",
        "editor.wordHighlightBackground": "#E8E8E8",
        "editorBracketMatch.background": "#C9DDFC",
        "editorBracketMatch.border": "#2760BB",
        "sideBarSectionHeader.background": "#0000",
        "sideBarSectionHeader.border": "#61616130",
        "sideBarTitle.foreground": "#6F6F6F"
    }
};
const EDITOR_OPTIONS = {
    minimap: {
        enabled: false,
        showSlider: "always",
        size: "proportional"
    },
    fontFamily: "monospace",
    fontSize: 13,
    tabCompletion: "on",
    readOnlyMessage: {
        value: "Agent is running... Please wait"
    },
    lineNumbers: "on",
    padding: {
        top: 10,
        bottom: 10
    },
    scrollBeyondLastLine: false,
    automaticLayout: true,
    formatOnPaste: true,
    formatOnType: true,
    folding: true,
    foldingHighlight: true,
    renderLineHighlight: "all",
    cursorBlinking: "blink",
    cursorSmoothCaretAnimation: "on",
    cursorWidth: 1,
    smoothScrolling: true,
    mouseWheelScrollSensitivity: 3,
    fastScrollSensitivity: 20,
    wordWrap: "on",
    bracketPairColorization: {
        enabled: true
    }
};
const LANGUAGES_BY_FILE_EXTENSION = {
    ts: "typescript",
    tsx: "typescript",
    js: "javascript",
    jsx: "javascript",
    cjs: "javascript",
    mjs: "javascript",
    css: "css",
    json: "json",
    yml: "yaml",
    yaml: "yaml",
    md: "markdown",
    sql: "sql"
};
const configureMonaco = (monaco)=>{
    monaco.editor.defineTheme("custom-dark", darkThemeData);
    monaco.editor.defineTheme("custom-light", lightThemeData);
    try {
        monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
            noSemanticValidation: true,
            noSyntaxValidation: true,
            noSuggestionDiagnostics: true
        });
        monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
            allowNonTsExtensions: true,
            allowJs: true,
            checkJs: false,
            noUnusedLocals: false,
            noUnusedParameters: false,
            strict: false,
            target: monaco.languages.typescript.ScriptTarget.Latest
        });
    } catch (error) {
        console.warn("Error configuring Monaco editor:", error);
    }
};
const Breadcrumbs = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(({ file, isMobile })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Breadcrumb"], {
        className: "truncate px-4 pb-2 pt-1.5 text-sm font-normal text-muted-foreground md:py-1.5",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbList"], {
            children: file.split("/").map((part, index, array)=>{
                // If mobile and there are more than 3 parts, show only the last part
                if (isMobile && array.length > 3) {
                    if (index === array.length - 1) {
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbItem"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbPage"], {
                                    className: "font-medium text-foreground",
                                    children: part
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 198,
                                    columnNumber: 21
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                lineNumber: 197,
                                columnNumber: 19
                            }, this)
                        }, index, false, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 196,
                            columnNumber: 17
                        }, this);
                    }
                    if (index === 0) {
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbItem"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                                asChild: true,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                    variant: "ghost",
                                                    size: "sm",
                                                    className: "h-6 px-1 text-xs",
                                                    children: "..."
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                                    lineNumber: 210,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                                lineNumber: 209,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                                align: "start",
                                                className: "w-56",
                                                children: array.slice(0, array.length - 1).map((middlePart, middleIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                                                        children: middlePart
                                                    }, middleIndex, false, {
                                                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                                        lineNumber: 216,
                                                        columnNumber: 27
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                                lineNumber: 214,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                        lineNumber: 208,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 207,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbSeparator"], {
                                    className: "mt-0.5"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 221,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, index, true, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 206,
                            columnNumber: 17
                        }, this);
                    }
                    return null;
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                    children: [
                        index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbSeparator"], {
                            className: "mt-0.5"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 230,
                            columnNumber: 29
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbItem"], {
                            children: index === array.length - 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbPage"], {
                                className: "font-medium text-foreground",
                                children: part
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                lineNumber: 233,
                                columnNumber: 19
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BreadcrumbLink"], {
                                children: part
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                lineNumber: 235,
                                columnNumber: 19
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 231,
                            columnNumber: 15
                        }, this)
                    ]
                }, index, true, {
                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                    lineNumber: 229,
                    columnNumber: 13
                }, this);
            })
        }, void 0, false, {
            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
            lineNumber: 190,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
        lineNumber: 189,
        columnNumber: 5
    }, this);
});
_c1 = Breadcrumbs;
Breadcrumbs.displayName = "TextEditorBreadcrumbs";
const TextEditor = (props)=>{
    _s();
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$themes$40$0$2e$4$2e$6_react$2d$dom_e207e685aa9cc81adf4eaedb8666d505$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showFileTree, setShowFileTree } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNavigateFile"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"])({
        "TextEditor.useNavigateFile.useShallow": (state)=>({
                showFileTree: state.showFileTree,
                setShowFileTree: state.setShowFileTree
            })
    }["TextEditor.useNavigateFile.useShallow"]));
    const [showGitDialog, setShowGitDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [fileStatus, setFileStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("no-changes");
    const editorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const currentFileRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const contentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const { data: content, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "fileContent",
            props.projectId,
            props.file
        ],
        queryFn: {
            "TextEditor.useQuery": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFileContent"])(props.projectId, props.file)
        }["TextEditor.useQuery"],
        enabled: !!props.projectId && !!props.file,
        staleTime: 1000 * 30,
        gcTime: 1000 * 60
    });
    // Handle content and file changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TextEditor.useEffect": ()=>{
            contentRef.current = content;
            if (!editorRef.current) return;
            // Clear editor when loading or no content
            if (isLoading || !content) {
                editorRef.current.setValue("");
                return;
            }
            const isNewFile = props.file !== currentFileRef.current;
            if (isNewFile) {
                currentFileRef.current = props.file;
                setFileStatus("no-changes");
            }
            const currentValue = editorRef.current.getValue();
            if (isNewFile || currentValue !== content) {
                editorRef.current.setValue(content || "");
            }
        }
    }["TextEditor.useEffect"], [
        content,
        props.file,
        isLoading
    ]);
    // Handle editor mount
    const handleEditorDidMount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TextEditor.useCallback[handleEditorDidMount]": (editor)=>{
            editorRef.current = editor;
            if (contentRef.current) {
                editor.setValue(contentRef.current);
            }
        }
    }["TextEditor.useCallback[handleEditorDidMount]"], []);
    // Handle save content mutation
    const { mutate: saveContent, isPending: isSaving } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "TextEditor.useMutation": (content)=>{
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateFile"])(props.projectId, props.file, content);
            }
        }["TextEditor.useMutation"],
        onSuccess: {
            "TextEditor.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        "fileContent",
                        props.projectId,
                        props.file
                    ]
                });
                setFileStatus("saved");
                setShowGitDialog(true);
            }
        }["TextEditor.useMutation"],
        onError: {
            "TextEditor.useMutation": (error)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorToast"])(`Failed to save file: ${error instanceof Error ? error.message : "Unknown error"}`);
            }
        }["TextEditor.useMutation"]
    });
    // Handle commit changes mutation
    const { mutate: commitChanges, isPending: isCommitting } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "TextEditor.useMutation": ()=>{
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadingToast"])("Committing changes...", (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["gitCommit"])(props.projectId, `Edit ${props.file}`));
            }
        }["TextEditor.useMutation"],
        onError: {
            "TextEditor.useMutation": (error)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorToast"])(`Failed to commit changes: ${error instanceof Error ? error.message : "Unknown error"}`);
            }
        }["TextEditor.useMutation"]
    });
    const handleSave = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TextEditor.useCallback[handleSave]": ()=>{
            if (editorRef.current) {
                const content = editorRef.current.getValue();
                saveContent(content);
            }
        }
    }["TextEditor.useCallback[handleSave]"], [
        saveContent
    ]);
    const handleGitCommit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TextEditor.useCallback[handleGitCommit]": ()=>{
            commitChanges();
        }
    }["TextEditor.useCallback[handleGitCommit]"], [
        commitChanges
    ]);
    const handleDiscard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TextEditor.useCallback[handleDiscard]": ()=>{
            if (content !== undefined) {
                editorRef.current?.setValue(content);
                setFileStatus("no-changes");
            }
        }
    }["TextEditor.useCallback[handleDiscard]"], [
        content,
        editorRef,
        setFileStatus
    ]);
    const handleEditorChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TextEditor.useCallback[handleEditorChange]": ()=>{
            if (!editorRef.current) return;
            const currentValue = editorRef.current.getValue();
            const isChanged = currentValue !== (content || "");
            setFileStatus(isChanged ? "unsaved" : "no-changes");
        }
    }["TextEditor.useCallback[handleEditorChange]"], [
        content
    ]);
    // cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TextEditor.useEffect": ()=>{
            return ({
                "TextEditor.useEffect": ()=>{
                    if (editorRef.current) {
                        editorRef.current.dispose();
                        editorRef.current = null;
                    }
                }
            })["TextEditor.useEffect"];
        }
    }["TextEditor.useEffect"], []);
    if (!props.file) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex h-full w-full flex-col items-center justify-center rounded-none border-none bg-transparent shadow-none"),
            border: false,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "flex w-full max-w-xl flex-col items-center justify-center space-y-6 p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$File$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__File$3e$__["File"], {
                            className: "size-8 text-muted-foreground"
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 393,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                        lineNumber: 392,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-1.5 text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].H4, {
                                    children: "No file selected"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 398,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].P, {
                                    className: "mt-0 text-balance leading-normal",
                                    children: "Please select a file to edit."
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 399,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 397,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                        lineNumber: 396,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                lineNumber: 391,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
            lineNumber: 385,
            columnNumber: 7
        }, this);
    }
    const fileExtension = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$path$2d$browserify$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].extname(props.file || "").slice(1);
    const language = LANGUAGES_BY_FILE_EXTENSION[fileExtension] || "plaintext";
    const editorTheme = theme === "dark" ? "custom-dark" : "custom-light";
    const readOnlyEditor = props.isAgentRunning || isLoading;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative flex h-full w-full flex-col overflow-hidden", props.className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center justify-between py-1.5 text-sm font-medium md:border-b md:border-t-0"),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex w-full items-center justify-between md:pl-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$hint$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            label: showFileTree ? "Hide File Tree" : "Show File Tree",
                            side: "bottom",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                size: "icon",
                                className: "hidden rounded-lg md:flex",
                                variant: "ghost",
                                onClick: ()=>setShowFileTree(!showFileTree),
                                children: showFileTree ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$tb$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TbLayoutSidebarFilled"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 432,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$icons$40$5$2e$5$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$icons$2f$tb$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TbLayoutSidebar"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 434,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                lineNumber: 425,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 424,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Breadcrumbs, {
                            file: props.file,
                            isMobile: props.isMobile
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 439,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2 py-1 pr-2 md:px-0 md:py-0",
                            children: props.isAgentRunning && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: "terminal",
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "size-3 animate-spin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                        lineNumber: 444,
                                        columnNumber: 17
                                    }, this),
                                    "Agent is running..."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                lineNumber: 443,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 441,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                    lineNumber: 423,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                lineNumber: 418,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 tracking-wider",
                children: [
                    isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex h-full w-full items-center justify-center bg-background text-muted-foreground",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$text$2d$shimmer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextShimmer"], {
                                    className: "text-base font-normal",
                                    children: "Fetching file content..."
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 456,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    className: "size-4 animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 457,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 455,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                        lineNumber: 454,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Editor, {
                        height: "100%",
                        language: language,
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("h-full pb-5", isLoading && "hidden"),
                        theme: editorTheme,
                        onMount: handleEditorDidMount,
                        beforeMount: configureMonaco,
                        onChange: handleEditorChange,
                        options: {
                            ...EDITOR_OPTIONS,
                            readOnly: readOnlyEditor
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                        lineNumber: 462,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                lineNumber: 452,
                columnNumber: 7
            }, this),
            !props.isAgentRunning && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$text$2d$editor$2f$file$2d$update$2d$modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                side: "right",
                fileStatus: fileStatus ?? "saved",
                onReset: handleDiscard,
                onSave: handleSave,
                isSaving: isSaving,
                isMobile: props.isMobile
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                lineNumber: 478,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialog"], {
                open: showGitDialog,
                onOpenChange: setShowGitDialog,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogContent"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogTitle"], {
                                    children: "Commit Changes to Git?"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 491,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogDescription"], {
                                    children: "Do you want to commit these changes to the Git version history?"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 492,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 490,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogFooter"], {
                            className: "flex items-center justify-end",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogCancel"], {
                                    className: "w-full md:w-fit",
                                    onClick: ()=>setShowGitDialog(false),
                                    children: "No"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 497,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDialogAction"], {
                                    onClick: handleGitCommit,
                                    disabled: isCommitting,
                                    className: "h-8 w-full md:w-fit",
                                    children: isCommitting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                className: "size-4 animate-spin"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                                lineNumber: 507,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Committing..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                                lineNumber: 508,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                        lineNumber: 506,
                                        columnNumber: 17
                                    }, this) : "Yes, Commit"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                                    lineNumber: 500,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                            lineNumber: 496,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                    lineNumber: 489,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
                lineNumber: 488,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/code-editor/text-editor/index.tsx",
        lineNumber: 417,
        columnNumber: 5
    }, this);
};
_s(TextEditor, "GY2wWi+tS+WAEqRvnmBj3+DmIg0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$themes$40$0$2e$4$2e$6_react$2d$dom_e207e685aa9cc81adf4eaedb8666d505$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNavigateFile"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
_c2 = TextEditor;
TextEditor.displayName = "TextEditor";
const __TURBOPACK__default__export__ = TextEditor;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "Editor");
__turbopack_context__.k.register(_c1, "Breadcrumbs");
__turbopack_context__.k.register(_c2, "TextEditor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/features/code-editor/code-editor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$resizable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/resizable.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-mobile.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/current-thread.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/navigate-file.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react/shallow.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$file$2d$tree$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/code-editor/file-tree/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$text$2d$editor$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/code-editor/text-editor/index.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
const CodeEditor = ({ projectId, envId })=>{
    _s();
    const isMobile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsMobile"])();
    const { file, setFile, showFileTree } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNavigateFile"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"])({
        "CodeEditor.useNavigateFile.useShallow": (state)=>({
                file: state.file,
                setFile: state.setFile,
                showFileTree: state.showFileTree
            })
    }["CodeEditor.useNavigateFile.useShallow"]));
    const isAgentRunning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsAgentRunning"])();
    const { data: filesData, isLoading: isLoadingFiles, refetch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "files",
            projectId,
            envId
        ],
        queryFn: {
            "CodeEditor.useQuery": async ()=>{
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFiles"])(projectId, "/");
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const sortedFiles = (result?.files || []).sort({
                    "CodeEditor.useQuery.sortedFiles": (a, b)=>{
                        if (a.isDirectory && !b.isDirectory) return -1;
                        if (!a.isDirectory && b.isDirectory) return 1;
                        return a.name.localeCompare(b.name);
                    }
                }["CodeEditor.useQuery.sortedFiles"]);
                return sortedFiles;
            }
        }["CodeEditor.useQuery"],
        enabled: !!projectId,
        placeholderData: {
            "CodeEditor.useQuery": (previousData)=>previousData
        }["CodeEditor.useQuery"]
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSubscribeToAgentRunning"])({
        "CodeEditor.useSubscribeToAgentRunning": (isAgentRunning, wasRunning)=>{
            if (!isAgentRunning && wasRunning) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Agent streaming complete, refetching files");
                refetch();
            }
        }
    }["CodeEditor.useSubscribeToAgentRunning"]);
    const filesAndFolders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CodeEditor.useMemo[filesAndFolders]": ()=>{
            return filesData || [];
        }
    }["CodeEditor.useMemo[filesAndFolders]"], [
        filesData
    ]);
    const handleFileSelect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CodeEditor.useCallback[handleFileSelect]": (filepath)=>{
            setFile(filepath);
        }
    }["CodeEditor.useCallback[handleFileSelect]"], []);
    if (isMobile) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative flex h-full grid-cols-12 flex-col overflow-hidden md:grid",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$file$2d$tree$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    className: "md:col-span-2 md:border-r",
                    projectId: projectId,
                    onFileSelect: handleFileSelect,
                    selectedFile: file,
                    filesAndFolders: filesAndFolders,
                    isLoadingFilePaths: isLoadingFiles,
                    allFilePaths: filesData || [],
                    refetchFileTree: refetch
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/code-editor.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$text$2d$editor$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    isMobile: true,
                    className: showFileTree ? "md:col-span-10" : "md:col-span-12",
                    file: file,
                    isAgentRunning: isAgentRunning,
                    projectId: projectId
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/code-editor.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/features/code-editor/code-editor.tsx",
            lineNumber: 69,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$resizable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ResizablePanelGroup"], {
        direction: "horizontal",
        className: "relative flex h-full flex-col overflow-hidden md:flex-row",
        children: [
            showFileTree && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$resizable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ResizablePanel"], {
                        id: "file-tree",
                        order: 1,
                        defaultSize: 20,
                        minSize: 20,
                        maxSize: 45,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$file$2d$tree$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            className: "h-full border-r",
                            projectId: projectId,
                            onFileSelect: handleFileSelect,
                            selectedFile: file,
                            filesAndFolders: filesAndFolders,
                            isLoadingFilePaths: isLoadingFiles,
                            allFilePaths: filesData || [],
                            refetchFileTree: refetch
                        }, void 0, false, {
                            fileName: "[project]/src/features/code-editor/code-editor.tsx",
                            lineNumber: 100,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/code-editor/code-editor.tsx",
                        lineNumber: 99,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$resizable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ResizableHandle"], {}, void 0, false, {
                        fileName: "[project]/src/features/code-editor/code-editor.tsx",
                        lineNumber: 112,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$resizable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ResizablePanel"], {
                id: "code-editor",
                order: 2,
                defaultSize: showFileTree ? 80 : 100,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$code$2d$editor$2f$text$2d$editor$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    isAgentRunning: isAgentRunning,
                    projectId: projectId,
                    className: "h-full",
                    file: file
                }, void 0, false, {
                    fileName: "[project]/src/features/code-editor/code-editor.tsx",
                    lineNumber: 117,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/code-editor/code-editor.tsx",
                lineNumber: 116,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/code-editor/code-editor.tsx",
        lineNumber: 93,
        columnNumber: 5
    }, this);
};
_s(CodeEditor, "VPaBfN4L2GUQyJRbJngubByOswk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsMobile"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNavigateFile"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useIsAgentRunning"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSubscribeToAgentRunning"]
    ];
});
_c = CodeEditor;
const __TURBOPACK__default__export__ = /*#__PURE__*/ _c1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(CodeEditor);
var _c, _c1;
__turbopack_context__.k.register(_c, "CodeEditor");
__turbopack_context__.k.register(_c1, "%default%");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_features_code-editor_7ec87d54._.js.map