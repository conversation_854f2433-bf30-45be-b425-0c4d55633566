from daytona import Daytona, DaytonaConfig, CreateWorkspaceParams
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
import random
import string
from datetime import datetime
import logging
import os
from typing import Dict, List, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Daytona client
config = DaytonaConfig(
    api_key="dtn_858727beec575bf6d960c089fc4e4e397008e0e6383bd205082ed5de724e1f63",
    server_url="https://daytona.work/api",
    target="us"
)
daytona = Daytona(config)

class WorkspaceTracker:
    def __init__(self, timestamp: str):
        self.timestamp = timestamp
        self.tracking_file = f'env-migration-marko/workspace_tracking_{timestamp}.json'
        self.id_mapping_file = f'env-migration-marko/workspace_id_mapping_{timestamp}.json'
        self.load_tracking_data()

    def load_tracking_data(self):
        """Load existing tracking data or initialize new tracking"""
        if os.path.exists(self.tracking_file):
            with open(self.tracking_file, 'r') as f:
                data = json.load(f)
                self.processed = data.get('processed', {})
                self.successful = data.get('successful', [])
                self.failed = data.get('failed', [])
                self.id_mapping = data.get('id_mapping', {})
        else:
            self.processed = {}  # env_id -> workspace_data mapping
            self.successful = []
            self.failed = []
            self.id_mapping = {}  # preview_number -> workspace_id mapping
            self.save_tracking_data()

    def save_tracking_data(self):
        """Save current tracking data"""
        with open(self.tracking_file, 'w') as f:
            json.dump({
                'timestamp': self.timestamp,
                'processed': self.processed,
                'successful': self.successful,
                'failed': self.failed,
                'id_mapping': self.id_mapping
            }, f, indent=4)
        
        # Save ID mapping separately for easy reference
        with open(self.id_mapping_file, 'w') as f:
            json.dump({
                'timestamp': self.timestamp,
                'id_mapping': self.id_mapping
            }, f, indent=4)

    def add_id_mapping(self, preview_number: str, workspace_id: str):
        """Track the mapping between preview number and workspace ID"""
        self.id_mapping[preview_number] = workspace_id
        self.save_tracking_data()

    def get_workspace_id(self, preview_number: str) -> str:
        """Get existing workspace ID for preview number if it exists"""
        return self.id_mapping.get(preview_number)

    def mark_successful(self, env_data: Dict[str, Any], workspace_data: Dict[str, Any]):
        """Mark an environment as successfully processed"""
        self.processed[env_data['env_id']] = workspace_data
        self.successful.append(workspace_data)
        # Extract preview number and add mapping
        preview_number = env_data['env_id'].split('-')[1]
        self.add_id_mapping(preview_number, workspace_data['workspace_id'])
        self.save_tracking_data()

    def mark_failed(self, env_data: Dict[str, Any]):
        """Mark an environment as failed"""
        self.failed.append(env_data['env_id'])
        self.save_tracking_data()

    def is_processed(self, env_id: str) -> bool:
        """Check if an environment has already been processed"""
        return env_id in self.processed or env_id in self.failed

def generate_workspace_id(preview_number):
    """Generate a random workspace ID that includes the preview number"""
    random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    return f"{random_chars}-{preview_number}"

async def create_workspace_for_env(env_data: Dict[str, Any], executor: ThreadPoolExecutor, 
                                 loop: asyncio.AbstractEventLoop, tracker: WorkspaceTracker) -> Dict[str, Any]:
    """Create a Daytona workspace for a single environment"""
    try:
        # Skip if already processed
        if tracker.is_processed(env_data['env_id']):
            logger.info(f"Skipping already processed environment: {env_data['env_id']}")
            return None

        # Extract the number from env_id
        preview_number = env_data["env_id"].split("-")[1]
        
        # Check if we already have a workspace ID for this preview number
        existing_workspace_id = tracker.get_workspace_id(preview_number)
        if existing_workspace_id:
            workspace_id = existing_workspace_id
            logger.info(f"Using existing workspace ID mapping: {workspace_id}")
        else:
            workspace_id = generate_workspace_id(preview_number)
            logger.info(f"Generated new workspace ID: {workspace_id}")
        
        params = CreateWorkspaceParams(
            id=workspace_id,
            language="python",
            image="docker.io/kortixmarko/sg-firebase-nextjs:2.0.5",
            env_vars={
                "PORT": "3000",
                "LOG_LEVEL": "debug",
            },
        )

        # Create workspace
        workspace = await loop.run_in_executor(
            executor,
            daytona.create,
            params
        )
        logger.info(f"Created workspace: {workspace.id}")

        # Set labels
        await loop.run_in_executor(
            executor,
            workspace.set_labels,
            {"public": True}
        )
        logger.info(f"Set labels for workspace: {workspace.id}")

        workspace_data = {
            "workspace_id": workspace.id,
            "original_env_id": env_data["env_id"],
            "project_id": env_data["project_id"],
            "github_repo": env_data["github_repo"],
            "has_env_contents": env_data["has_env_contents"]
        }

        # Track successful creation
        tracker.mark_successful(env_data, workspace_data)
        return workspace_data

    except Exception as e:
        logger.error(f"Error creating workspace for {env_data['env_id']}: {str(e)}")
        tracker.mark_failed(env_data)
        return None

async def process_batch(batch: List[Dict[str, Any]], executor: ThreadPoolExecutor, 
                       loop: asyncio.AbstractEventLoop, batch_num: int, 
                       timestamp: str, tracker: WorkspaceTracker) -> tuple:
    """Process a batch of environments"""
    tasks = []
    for env_data in batch:
        task = create_workspace_for_env(env_data, executor, loop, tracker)
        tasks.append(task)

    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful = [r for r in results if r is not None]
    failed = [env["env_id"] for env, r in zip(batch, results) if r is None]

    # Save batch results
    if successful:
        with open(f'env-migration-marko/successful_workspaces_{timestamp}_batch{batch_num}.json', 'w') as f:
            json.dump(successful, f, indent=4)
    if failed:
        with open(f'env-migration-marko/failed_workspaces_{timestamp}_batch{batch_num}.json', 'w') as f:
            json.dump(failed, f, indent=4)

    return successful, failed

async def main():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    tracker = WorkspaceTracker(timestamp)

    # Load unmatched environments
    with open('env-migration-marko/environment_matches.json', 'r') as f:
        data = json.load(f)
        unmatched_envs = data["unmatched"]

    # Filter out already processed environments
    unmatched_envs = [env for env in unmatched_envs if not tracker.is_processed(env['env_id'])]
    
    logger.info(f"Found {len(unmatched_envs)} unprocessed environments")

    # Process in batches
    batch_size = 100
    batches = [
        unmatched_envs[i:i + batch_size]
        for i in range(0, len(unmatched_envs), batch_size)
    ]

    logger.info(f"Processing {len(unmatched_envs)} environments in {len(batches)} batches")

    # Create thread pool executor
    with ThreadPoolExecutor(max_workers=10) as executor:
        loop = asyncio.get_event_loop()

        # Process each batch
        for batch_num, batch in enumerate(batches, 1):
            logger.info(f"\nBatch {batch_num}/{len(batches)} ({len(batch)} environments)")
            
            # Add approval prompt
            while True:
                approval = input(f"\nDo you want to process batch {batch_num}/{len(batches)} with {len(batch)} environments? (y/n): ").lower()
                if approval in ['y', 'n']:
                    break
                print("Please enter 'y' for yes or 'n' for no.")
            
            if approval == 'n':
                logger.info(f"Skipping batch {batch_num} as per user request")
                continue
                
            successful, failed = await process_batch(batch, executor, loop, batch_num, timestamp, tracker)

            # Print batch summary
            logger.info(f"\nBatch {batch_num} complete:")
            logger.info(f"Successful: {len(successful)}")
            logger.info(f"Failed: {len(failed)}")

    # Save final results
    with open('env-migration-marko/new_workspaces.json', 'w') as f:
        json.dump({
            "successful": tracker.successful,
            "failed": tracker.failed,
            "timestamp": timestamp,
            "total_processed": len(unmatched_envs),
            "total_successful": len(tracker.successful),
            "total_failed": len(tracker.failed)
        }, f, indent=4)

    # Print final summary
    logger.info("\n=== Processing Complete ===")
    logger.info(f"Total environments: {len(unmatched_envs)}")
    logger.info(f"Total successful: {len(tracker.successful)}")
    logger.info(f"Total failed: {len(tracker.failed)}")
    logger.info("\nResults saved to: env-migration-marko/new_workspaces.json")

if __name__ == "__main__":
    asyncio.run(main())
