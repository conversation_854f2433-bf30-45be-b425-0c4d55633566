"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import Loading from "@/components/ui/loading";
import { LazyModal } from "@/components/ui/modal";
import { Preview, PreviewContent, PreviewTrigger } from "@/components/ui/preview";
import {
  PreviewImage,
  PreviewImageContent,
  PreviewImageTrigger,
} from "@/components/ui/preview-image";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import Typography from "@/components/ui/typography";
import { useIsMobile } from "@/hooks/use-mobile";
import { useThread } from "@/hooks/use-threads";
import { getFileContent, getFilePaths, uploadFile } from "@/lib/api";
import { debug } from "@/lib/debug";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { useProject } from "@/providers/project-provider";
import { useWebSocketMessage } from "@/providers/thread-provider/hooks";
import { useAgentInput, useSubscribeToAgentInput } from "@/stores/agent-input";
import { useCurrentThreadStore, useSubscribeToAgentRunning } from "@/stores/current-thread";
import { useNavigateFile } from "@/stores/navigate-file";
import { AuthState } from "@/types";
import { formatCurrency } from "@/utils/currency";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ArrowUp,
  CogOneSolid,
  DangerTriangleSolid,
  FileTextSolid,
  LightningSolid,
  MicrophoneSlashSolid,
  Paperclip,
  Sparkles,
  Square,
  Upload,
  X,
} from "@mynaui/icons-react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { BrainIcon, Mic, Zap } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { lazy, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import SpeechRecognition, { useSpeechRecognition } from "react-speech-recognition";
import { z } from "zod";
import { useShallow } from "zustand/react/shallow";
import Hint from "../../components/ui/hint";
import {
  MAX_IMAGES_PER_MESSAGE,
  validateImage,
  validateImageCount,
} from "../../constants/image-validation";
import { useTokenInfo } from "../../hooks/use-token-info";
import { errorToast, successToast } from "../global/toast";
import { parseErrorDetails } from "./utils/parse-error-details";

const FileUploadModalContent = lazy(() => import("../project/modal/file-upload-modal"));
const AgentSettingModalContent = lazy(() => import("./agent-setting-modal"));

const messageSchema = z.object({
  content: z
    .string()
    .min(1, "Message cannot be empty")
    .max(10000, "Message cannot be longer than 10000 characters"),
  selectedModel: z.enum(["creative", "standard", "plan"]).default("creative"),
  files: z.array(z.custom<File>()).default([]),
  selectedPaths: z.array(z.string()).default([]),
  rawErrors: z
    .array(
      z.object({
        id: z.string().optional(),
        timestamp: z.union([z.string(), z.date()]).optional(),
        pageUrl: z.string().optional(),
        name: z.string(),
        message: z.string(),
        type: z.string().optional(),
        stack: z.string().optional(),
        details: z.any().optional(),
        url: z.string().optional(),
        status: z.number().optional(),
        method: z.string().optional(),
      }),
    )
    .optional(),
  networkErrors: z
    .array(
      z.object({
        id: z.string().optional(),
        timestamp: z.union([z.string(), z.date()]).optional(),
        pageUrl: z.string().optional(),
        name: z.string(),
        message: z.string(),
        type: z.string().optional(),
        stack: z.string().optional(),
        details: z.any().optional(),
        url: z.string().optional(),
        status: z.number().optional(),
        method: z.string().optional(),
      }),
    )
    .optional(),
  contentWithErrors: z.string().optional(),
});

type MessageFormData = z.infer<typeof messageSchema>;

const FilePathPreview = ({
  path,
  projectId,
  onRemove,
}: {
  path: string;
  projectId: string;
  onRemove: (path: string) => void;
}) => {
  const { data: content, isLoading } = useQuery({
    queryKey: ["fileContent", projectId, path],
    queryFn: () => getFileContent(projectId, path),
    enabled: !!projectId && !!path,
    notifyOnChangeProps: ["data", "isLoading"],
  });

  return (
    <Preview>
      <PreviewTrigger asChild>
        <div className="group relative">
          <div
            className={cn(
              "flex h-fit w-fit items-center gap-2 rounded-xl border border-primary/10 p-1 text-sm font-medium leading-none shadow-sm transition-all duration-300",
              "border border-primary/20 bg-accent/80 text-primary/80 hover:bg-accent/90 hover:text-primary dark:border-primary/15",
            )}
          >
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-md border border-border/60 bg-sidebar-primary/80 p-1 shadow-sm dark:bg-sidebar-primary/40">
                <FileTextSolid className="h-full w-full text-background/90" />
              </div>
              <div className="flex flex-col gap-0 pr-4">
                <span className="truncate font-medium text-primary/90">
                  {path.split("/").pop()}
                </span>
                <span className="text-left text-xs font-normal text-muted-foreground">File</span>
              </div>
            </div>
          </div>
          <Button
            type="button"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              onRemove(path);
            }}
            className="absolute -right-1.5 -top-1.5 z-[100] size-4 rounded-full p-0 opacity-0 shadow-md ring-1 ring-border/50 group-hover:opacity-100"
          >
            <X className="size-3.5" />
          </Button>
        </div>
      </PreviewTrigger>

      <PreviewContent
        fileContent={isLoading ? "Loading content..." : content || ""}
        fileName={path}
        border
      />
    </Preview>
  );
};

const MessageInput = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const dropAreaRef = useRef<HTMLDivElement>(null);
  const selectedItemRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [showFileSelector, setShowFileSelector] = useState(false);
  const [showAgentSettings, setShowAgentSettings] = useState(false);
  const [selectedFileIndex, setSelectedFileIndex] = useState(0);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [isStoppingSession, setIsStoppingSession] = useState(false);
  const userHasSelectedModel = useRef(false);
  const [showFileUploadModal, setShowFileUploadModal] = useState(false);
  const [fileQueue, setFileQueue] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [fileSearchQuery, setFileSearchQuery] = useState("");
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: {
      status: "idle" | "uploading" | "success" | "error";
      progress: number;
      error?: string;
    };
  }>({});

  // Speech recognition state
  const messageBeforeListening = useRef("");
  const isListeningRef = useRef(false);

  const setActiveTab = useNavigateFile((state) => state.setActiveTab);
  const { inputContent, mode, setMode, setInputContent, reset } = useAgentInput();
  const { getTokenDisplayInfo } = useTokenInfo();
  const tokenInfo = getTokenDisplayInfo();
  const { user } = useAuth();

  const [animateKey, setAnimateKey] = useState(0);
  const [isTextareaFocused, setIsTextareaFocused] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [walletBalance, setWalletBalance] = useState<number | undefined>(undefined);

  const {
    isLoading: isThreadLoading,
    sendMessage,
    stopSession,
    contextLimitReached,
    currentThread,
  } = useThread();

  const { isAgentRunning, setNoTokenModalOpen } = useCurrentThreadStore(
    useShallow((state) => ({
      isAgentRunning: state.isAgentRunning,
      setNoTokenModalOpen: state.setNoTokenModalOpen,
    })),
  );

  const { projectId } = useProject();
  const isMobile = useIsMobile();

  const handleWalletBalance = useCallback(
    (message: { type: string; data: { balance: number } }) => {
      setWalletBalance(message.data.balance);
    },
    [],
  );

  useWebSocketMessage<{ type: string; data: { balance: number } }>(
    "wallet_balance",
    handleWalletBalance,
  );

  const scrollTextareaIntoView = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea || !(textarea instanceof HTMLTextAreaElement)) return;

    try {
      if (typeof textarea.scrollIntoView === "function") {
        textarea.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      } else {
        // Fallback for older browsers or when scrollIntoView is not available
        try {
          const rect = textarea.getBoundingClientRect();
          if (!rect) {
            // If getBoundingClientRect fails, fall back to simple scroll
            window.scrollTo({
              top: document.body.scrollHeight,
              behavior: "smooth",
            });
            return;
          }

          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const targetScroll = rect.top + scrollTop - window.innerHeight / 2;

          window.scrollTo({
            top: targetScroll,
            behavior: "smooth",
          });
        } catch (error) {
          console.error("Error in scrollTextareaIntoView:", error);
          // If getBoundingClientRect throws, fall back to simple scroll
          window.scrollTo({
            top: document.body.scrollHeight,
            behavior: "smooth",
          });
        }
      }
    } catch (error) {
      console.error("Error in scrollTextareaIntoView:", error);
      // Final fallback
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: "smooth",
      });
    }
  }, []);

  const setFormContent = (content: string) => {
    form.setValue("content", content);
  };

  const handleTextareaFocus = useCallback(() => {
    setIsTextareaFocused(true);
    if (isMobile && textareaRef.current) {
      setTimeout(() => {
        const textarea = textareaRef.current;
        if (!textarea || !(textarea instanceof HTMLTextAreaElement)) return;

        try {
          const rect = textarea.getBoundingClientRect();
          if (!rect) return;

          const windowHeight = window.innerHeight;
          const keyboardHeight = windowHeight * 0.4;
          const safeAreaBottom = parseInt(
            getComputedStyle(document.documentElement).getPropertyValue("--sat") || "0",
          );

          if (rect.bottom > windowHeight - keyboardHeight - safeAreaBottom) {
            scrollTextareaIntoView();
          }
        } catch (error) {
          console.error("Error in handleTextareaFocus:", error);
        }
      }, 150);
    }
  }, [isMobile, scrollTextareaIntoView]);

  const handleTextareaClick = useCallback(() => {
    if (isMobile) {
      setTimeout(() => {
        scrollTextareaIntoView();
      }, 100);
    }
  }, [isMobile, scrollTextareaIntoView]);

  const handleTextareaBlur = useCallback(() => {
    setIsTextareaFocused(false);
    setIsKeyboardVisible(false);
    if (isMobile) {
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }, 100);
    }
  }, [isMobile]);

  useEffect(() => {
    setAnimateKey((prevKey) => prevKey + 1);
  }, [tokenInfo.remainingTokens, tokenInfo.effectiveTotalTokens]);

  const form = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      content: inputContent || "",
      files: [],
      selectedPaths: [],
    },
  });

  const emptyThread = currentThread?.messages?.length === 0;

  useEffect(() => {
    if (!isThreadLoading && !userHasSelectedModel.current && emptyThread) {
      setMode("plan");
    }
  }, [emptyThread, isThreadLoading]);

  useSubscribeToAgentInput((content) => {
    if (content) {
      form.setValue("content", content);
      setInputContent(null);
    }
  });

  useEffect(() => {
    // reset agent input on unmount
    return () => {
      console.debug("[MessageInput] Unmounting message input");
      reset();
    };
  }, []);

  useEffect(() => {
    form.setFocus("content");
  }, []);

  useEffect(() => {
    if (isMobile) {
      const viewport = document.querySelector('meta[name="viewport"]');
      if (!viewport) {
        const meta = document.createElement("meta");
        meta.name = "viewport";
        meta.content =
          "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover";
        document.head.appendChild(meta);
      }
      let initialHeight = window.innerHeight;
      let resizeTimeout: NodeJS.Timeout;
      const visualViewport = window.visualViewport;

      const handleResize = () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          const currentHeight = window.innerHeight;
          const heightDifference = initialHeight - currentHeight;

          if (heightDifference > 150) {
            setIsKeyboardVisible(true);
            if (isTextareaFocused) {
              setTimeout(() => {
                scrollTextareaIntoView();
              }, 100);
            }
          } else {
            setIsKeyboardVisible(false);
          }

          initialHeight = currentHeight;
        }, 100);
      };

      const handleVisualViewportChange = () => {
        if (visualViewport) {
          const heightDifference = initialHeight - visualViewport.height;

          if (heightDifference > 150) {
            setIsKeyboardVisible(true);
            if (isTextareaFocused) {
              setTimeout(() => {
                scrollTextareaIntoView();
              }, 100);
            }
          } else {
            setIsKeyboardVisible(false);
          }
        }
      };

      window.addEventListener("resize", handleResize);

      if (visualViewport) {
        visualViewport.addEventListener("resize", handleVisualViewportChange);
      }

      return () => {
        window.removeEventListener("resize", handleResize);
        if (visualViewport) {
          visualViewport.removeEventListener("resize", handleVisualViewportChange);
        }
        clearTimeout(resizeTimeout);
      };
    }
  }, [isMobile, isTextareaFocused]);

  const selectedPaths = form.watch("selectedPaths");
  const content = form.watch("content");
  const rawErrors = form.watch("rawErrors");
  const formNetworkErrors = form.watch("networkErrors");

  const addMessageMutation = useMutation({
    mutationFn: async ({ content, selectedPaths }: MessageFormData) => {
      debug("Sending message with content:", content);

      if (contextLimitReached) {
        errorToast("Context window is full. Please start a new thread/task.");
        throw new Error("Context window is full. Please start a new thread/task.");
      }

      if (content.trim().length === 0) {
        errorToast("Message cannot be empty");
        throw new Error("Message cannot be empty");
      }

      const validImages = selectedImages.filter(
        (file) => file instanceof File && file.type.startsWith("image/"),
      );

      validateImageCount(validImages.length);

      for (const image of validImages) {
        validateImage(image);
      }

      const formattedPaths = selectedPaths.map((path) => {
        return `attached_file_${path}`;
      });

      const contentWithErrors = reconstructErrorDetails(content);
      const finalContent =
        selectedPaths.length > 0
          ? formattedPaths.join(" ") + " " + contentWithErrors
          : contentWithErrors;

      try {
        await sendMessage({
          content: finalContent,
          options: {
            images: validImages,
            selectedPaths,
            model: mode,
          },
        });
        return { success: true };
      } catch (error: unknown) {
        console.error("Error sending message:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to send message";
        errorToast(errorMessage);
        throw error;
      }
    },
    onSuccess: () => {
      form.setValue("content", "");
      form.setValue("files", []);
      form.setValue("selectedPaths", []);
      form.setValue("contentWithErrors", "");
      form.setValue("rawErrors", []);
      form.setValue("networkErrors", []);
      setSelectedImages([]);
      setSelectedFiles([]);
      setInputContent(null);
      successToast("Message sent successfully");
    },
    onError: (error: unknown) => {
      errorToast(error instanceof Error ? error.message : "Failed to send message");
    },
  });

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      try {
        setFileQueue(files);
        setShowFileUploadModal(true);
      } catch (error) {
        errorToast(error instanceof Error ? error.message : "Failed to add images");
        e.target.value = "";
      }
    }
  };

  const handleFileDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (!e.dataTransfer.files || e.dataTransfer.files.length === 0) return;

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      const imageFiles = droppedFiles.filter((file) => file.type.startsWith("image/"));

      try {
        if (imageFiles.length > 0) {
          validateImageCount(selectedImages.length, imageFiles.length);
        }

        setFileQueue(droppedFiles);
        setShowFileUploadModal(true);
      } catch (error) {
        errorToast(error instanceof Error ? error.message : "Failed to add images");
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleFileUploadConfirm = async (files: File[], action: "attach" | "upload" | "both") => {
    try {
      let progressInterval: NodeJS.Timeout | undefined;

      if (action === "upload" || action === "both") {
        // Initialize upload progress for all files
        files.forEach((file) => {
          setUploadProgress((prev) => ({
            ...prev,
            [file.name]: {
              status: "uploading",
              progress: 0,
              fileSize: file.size,
              fileName: file.name,
              fileType: file.type,
            },
          }));
        });

        // Simulate progress updates
        progressInterval = setInterval(() => {
          setUploadProgress((prev) => {
            const newProgress = { ...prev };
            let hasActiveUploads = false;

            files.forEach((file) => {
              const currentProgress = prev[file.name];
              if (
                currentProgress &&
                currentProgress.status === "uploading" &&
                currentProgress.progress < 90
              ) {
                newProgress[file.name] = {
                  ...currentProgress,
                  progress: Math.min(currentProgress.progress + Math.random() * 15, 90),
                };
                hasActiveUploads = true;
              }
            });

            // Clear interval if no active uploads
            if (!hasActiveUploads && progressInterval) {
              clearInterval(progressInterval);
            }

            return newProgress;
          });
        }, 300);
      }

      if (action === "attach" || action === "both") {
        const imageFiles = files.filter((file) => file.type.startsWith("image/"));
        if (imageFiles.length > 0) {
          validateImageCount(selectedImages.length, imageFiles.length);
        }

        files.forEach((file) => {
          if (file.type.startsWith("image/")) {
            validateImage(file);
            setSelectedImages((prev) => [...prev, file]);
          } else {
            setSelectedFiles((prev) => [...prev, file]);
          }
        });

        if (action === "attach") {
          files.forEach((file) => {
            appendFileReferenceToMessage(file, "attachment");
          });

          // Remove files from queue immediately
          setFileQueue((prev) => {
            const remainingFiles = prev.filter((f) => !files.includes(f));
            if (remainingFiles.length === 0) {
              setShowFileUploadModal(false);
            }
            return remainingFiles;
          });
          return;
        }
      }

      if (action === "upload" || action === "both") {
        try {
          // Upload files sequentially with proper progress tracking
          for (const file of files) {
            const uploadPath = `/public/${file.name}`;

            // Update progress to show upload starting
            setUploadProgress((prev) => ({
              ...prev,
              [file.name]: {
                ...prev[file.name],
                status: "uploading",
                progress: 95,
              },
            }));

            await uploadFile({
              projectId,
              file,
              relativePath: uploadPath,
              onProgress: (progress) => {
                setUploadProgress((prev) => ({
                  ...prev,
                  [file.name]: {
                    ...prev[file.name],
                    progress: Math.min(progress, 95),
                  },
                }));
              },
            });

            // Mark as successful and remove from queue
            setUploadProgress((prev) => ({
              ...prev,
              [file.name]: {
                ...prev[file.name],
                status: "success",
                progress: 100,
              },
            }));

            // Remove from file queue immediately after successful upload
            setFileQueue((prev) => prev.filter((f) => f.name !== file.name));

            appendFileReferenceToMessage(file, "public", uploadPath);

            // Remove from upload progress after a short delay to show success
            setTimeout(() => {
              setUploadProgress((prev) => {
                const newProgress = { ...prev };
                delete newProgress[file.name];
                return newProgress;
              });
            }, 2000);
          }

          if (progressInterval) {
            clearInterval(progressInterval);
          }

          successToast("Files uploaded successfully");
        } catch (error) {
          console.error("Error uploading files:", error);
          const errorMessage = error instanceof Error ? error.message : "Failed to upload files";
          errorToast(errorMessage);

          if (progressInterval) {
            clearInterval(progressInterval);
          }

          files.forEach((file) => {
            setUploadProgress((prev) => ({
              ...prev,
              [file.name]: {
                ...prev[file.name],
                status: "error",
                progress: 0,
                error: errorMessage,
              },
            }));
          });

          if (action === "both") {
            files.forEach((file) => {
              appendFileReferenceToMessage(file, "public", `/public/${file.name}`);
            });
          } else {
            return;
          }
        }
      }

      // Update remaining files in queue
      setFileQueue((prev) => {
        const remainingFiles = prev.filter((f) => !files.includes(f));
        if (remainingFiles.length === 0) {
          setShowFileUploadModal(false);
        }
        return remainingFiles;
      });

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Error handling files:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to process files";
      errorToast(errorMessage);

      if (action !== "attach") {
        files.forEach((file) => {
          setUploadProgress((prev) => ({
            ...prev,
            [file.name]: {
              ...prev[file.name],
              status: "error",
              progress: 0,
              error: errorMessage,
            },
          }));
        });
      }
    }
  };

  const appendFileReferenceToMessage = (
    file: File,
    type: "attachment" | "public",
    uploadPath?: string,
  ) => {
    const currentMessage = form.getValues("content");
    let markdownReference = "";

    if (file.type.startsWith("image/")) {
      if (type === "public" && uploadPath) {
        markdownReference = `\n![${file.name}](${uploadPath})\n`;
      } else {
        return;
      }
    } else {
      if (type === "public" && uploadPath) {
        markdownReference = `\n[${file.name}](${uploadPath})\n`;
      } else {
        markdownReference = `\n*File attached: ${file.name}*\n`;
      }
    }

    form.setValue(
      "content",
      currentMessage.trim().trimStart().trimEnd() + markdownReference.trim().trimStart().trimEnd(),
    );
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const items = Array.from(e.clipboardData.items);
    const imageItems = items.filter(
      (item) => item.kind === "file" && item.type.startsWith("image/"),
    );

    if (imageItems.length === 0) {
      return;
    }

    e.preventDefault();

    try {
      validateImageCount(selectedImages.length, imageItems.length);

      imageItems.forEach((item) => {
        const file = item.getAsFile();
        if (!file) return;

        validateImage(file);

        const timestamp = Date.now();
        const fileName = `pasted-image-${timestamp}.png`;
        const blob = file.slice(0, file.size, file.type);
        const renamedFile = new File([blob], fileName, { type: file.type });

        setSelectedImages((prev) => [...prev, renamedFile]);
      });
    } catch (error) {
      errorToast(error instanceof Error ? error.message : "Failed to add pasted image");
    }
  };

  const removeFile = (file: File) => {
    setSelectedFiles((prev) => prev.filter((f) => f !== file));
  };

  const removeImage = (file: File) => {
    setSelectedImages((prev) => {
      const updatedImages = prev.filter((f) => f !== file);
      return updatedImages;
    });
  };

  const files = [...selectedImages, ...selectedFiles];

  const { data: availableFiles = [], refetch: refetchFilePaths } = useQuery({
    queryKey: ["project-filePaths", projectId],
    queryFn: async () => {
      const result = await getFilePaths(projectId, "/app/", 999);
      return result?.paths || [];
    },
    enabled: !!projectId,
    staleTime: 1000 * 60,
  });

  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {
    if (!isAgentRunning && wasRunning) {
      refetchFilePaths();
    }
  });

  const insertFilePath = (file: string) => {
    const textBeforeCursor = (content || "").substring(0, cursorPosition);
    const lastAtIndex = textBeforeCursor.lastIndexOf("@");
    const textAfterCursor = (content || "").substring(cursorPosition);

    const newContent = textBeforeCursor.substring(0, lastAtIndex) + textAfterCursor;
    form.setValue("content", newContent);
    form.setValue("selectedPaths", [...(selectedPaths || []), file]);
    setShowFileSelector(false);
    setFileSearchQuery("");
    form.setFocus("content");
  };

  const filteredFiles = useMemo(() => {
    if (!showFileSelector) return [];

    const textBeforeCursor = (content || "").substring(0, cursorPosition);
    const lastAtIndex = textBeforeCursor.lastIndexOf("@");
    const searchText = fileSearchQuery || textBeforeCursor.substring(lastAtIndex + 1).toLowerCase();

    return availableFiles.filter(
      (file: string) =>
        file.toLowerCase().includes(searchText.toLowerCase()) &&
        !(selectedPaths || []).includes(file),
    );
  }, [content, cursorPosition, availableFiles, selectedPaths, showFileSelector, fileSearchQuery]);

  useEffect(() => {
    if (showFileSelector) {
      setSelectedFileIndex(0);
      setFileSearchQuery("");
    }
  }, [showFileSelector]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      if (showFileSelector) {
        e.preventDefault();
        if (filteredFiles.length > 0) {
          insertFilePath(filteredFiles[selectedFileIndex]);
        }
        return;
      }

      e.preventDefault();
      form.handleSubmit((data) => addMessageMutation.mutate(data))();
      return;
    }

    if (!showFileSelector) return;

    if (e.key === "ArrowDown" || e.key === "ArrowUp") {
      e.preventDefault();

      const newIndex =
        e.key === "ArrowDown"
          ? selectedFileIndex < filteredFiles.length - 1
            ? selectedFileIndex + 1
            : selectedFileIndex
          : selectedFileIndex > 0
            ? selectedFileIndex - 1
            : selectedFileIndex;

      setSelectedFileIndex(newIndex);

      setTimeout(() => {
        const selectedItem = document.querySelector(`[data-file-index="${newIndex}"]`);
        selectedItem?.scrollIntoView({ block: "nearest", behavior: "smooth" });
      }, 0);
    } else if (e.key === "Escape") {
      e.preventDefault();
      setShowFileSelector(false);
      setFileSearchQuery("");
    }
  };

  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart;

    // If user starts typing while listening, stop speech recognition
    if (isListeningRef.current && listening) {
      stopListening();
    }

    // Don't update form content if we're currently listening to speech
    if (!isListeningRef.current) {
      form.setValue("content", value);
    }
    setCursorPosition(cursorPos);

    const textBeforeCursor = value.substring(0, cursorPos);
    const lastAtIndex = textBeforeCursor.lastIndexOf("@");

    if (lastAtIndex !== -1) {
      const searchText = textBeforeCursor.substring(lastAtIndex + 1);
      const hasSpaceAfterAt = /\s/.test(searchText);

      if (!hasSpaceAfterAt) {
        setShowFileSelector(true);
        setSelectedFileIndex(0);
        return;
      }
    }

    setShowFileSelector(false);
    setFileSearchQuery("");
  };

  const handleStopSession = async () => {
    try {
      setIsStoppingSession(true);
      const result = await stopSession();
      if (result) {
        successToast("Session stopped");
      }
      setActiveTab("preview");
    } catch (error) {
      console.error("Error in handleStopSession:", error);
    } finally {
      setIsStoppingSession(false);
    }
  };

  const { errorDetails, networkErrors, cleanedContent } = useMemo(() => {
    const { errorDetails, networkErrors, cleanedContent } = parseErrorDetails(content || "");
    return { errorDetails, networkErrors, cleanedContent };
  }, [content]);

  useEffect(() => {
    if (errorDetails && !form.getValues("rawErrors")?.length) {
      form.setValue("rawErrors", errorDetails);
    }

    if (networkErrors && !form.getValues("networkErrors")?.length) {
      form.setValue("networkErrors", networkErrors);
    }

    if (cleanedContent && (errorDetails || networkErrors)) {
      const currentContent = form.getValues("content");
      if (currentContent !== cleanedContent) {
        form.setValue("content", cleanedContent);
      }
    }
  }, [errorDetails, networkErrors, cleanedContent]);

  const removeErrorDetails = useCallback(() => {
    form.setValue("rawErrors", []);
    form.setValue("contentWithErrors", "");
  }, [form]);

  const removeNetworkErrors = useCallback(() => {
    form.setValue("networkErrors", []);
    form.setValue("contentWithErrors", "");
  }, [form]);

  const reconstructErrorDetails = useCallback(
    (content: string): string => {
      let finalContent = content;

      const rawErrors = form.getValues("rawErrors");
      const networkErrors = form.getValues("networkErrors");

      if (rawErrors && rawErrors.length > 0) {
        const errorDetailsTag = `<error_details errors=${JSON.stringify(rawErrors)} />`;
        finalContent = finalContent + " " + errorDetailsTag;
      }

      if (networkErrors && networkErrors.length > 0) {
        const networkErrorsTag = `<network_errors errors=${JSON.stringify(networkErrors)} />`;
        finalContent = finalContent + " " + networkErrorsTag;
      }

      return finalContent;
    },
    [form],
  );

  // Speech recognition hook
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable,
  } = useSpeechRecognition({
    commands: [
      {
        command: "send message",
        callback: () => {
          const currentContent = form.getValues("content");
          if (currentContent.trim()) {
            // Stop listening before sending the message
            stopListening();
            form.handleSubmit((data) => addMessageMutation.mutate(data))();
          }
        },
        matchInterim: true,
      },
      {
        command: "clear",
        callback: () => {
          form.setValue("content", "");
          resetTranscript();
        },
        matchInterim: true,
      },
    ],
  });

  // Update speech transcript when transcript changes
  useEffect(() => {
    if (listening && transcript && isListeningRef.current) {
      const separator = messageBeforeListening.current && transcript ? " " : "";
      const newContent = messageBeforeListening.current + separator + transcript;
      form.setValue("content", newContent);
    }
  }, [transcript, listening, form]);

  // Update listening ref when speech recognition state changes
  useEffect(() => {
    isListeningRef.current = listening;
  }, [listening]);

  // Speech recognition control functions
  const startListening = () => {
    if (!browserSupportsSpeechRecognition) {
      errorToast("Speech Recognition Not Supported", {
        description: "Your browser does not support speech recognition.",
      });
      return;
    }

    if (!isMicrophoneAvailable) {
      errorToast("Microphone Access Required", {
        description: "Please allow microphone access to use speech recognition.",
      });
      return;
    }

    try {
      // Store the current content before starting
      messageBeforeListening.current = form.getValues("content");
      isListeningRef.current = true;
      resetTranscript();
      SpeechRecognition.startListening({
        continuous: true,
        language: "en-US",
      });
      successToast("Listening...", {
        description: "Speak your message. Say 'send message' to send or 'clear' to clear.",
      });
    } catch (error) {
      console.error("Error starting speech recognition:", error);
      isListeningRef.current = false;
      errorToast("Error", { description: "Failed to start speech recognition." });
    }
  };

  const stopListening = () => {
    try {
      SpeechRecognition.stopListening();
      // The transcript will be preserved in the form content
      // and the hook will handle the state transition
    } catch (error) {
      console.error("Error stopping speech recognition:", error);
    }
  };

  return (
    <>
      <div
        className={cn(
          "relative bottom-0 left-0 right-0 z-30 flex w-full flex-col items-center bg-transparent transition-all duration-300",
          isMobile ? "pb-4" : "pb-2",
          isMobile && isTextareaFocused && "pb-6",
          isMobile && isKeyboardVisible && "pb-8",
          isMobile && "min-h-[80px]",
        )}
        style={
          isMobile
            ? {
                paddingBottom: `calc(1rem + env(safe-area-inset-bottom))`,
              }
            : undefined
        }
      >
        <BalanceInfo
          tokenInfo={tokenInfo}
          walletBalance={walletBalance}
          authState={user}
          animateKey={animateKey}
        />

        <div
          className={cn(
            "max-w-(--breakpoint-md) z-20 flex w-full gap-3",
            isMobile ? "px-3" : "px-2",
            isMobile && "min-h-[60px]",
          )}
        >
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit((data) => {
                if (
                  user.wholesalePlan &&
                  user.userFromDb?.wallet_balance != null &&
                  user.userFromDb?.wallet_balance <= 0
                ) {
                  setNoTokenModalOpen(true);
                  return;
                }

                if (
                  !user.userFromDb?.isRequestBased &&
                  user.userFromDb?.free_total_token !== undefined &&
                  user.userFromDb.free_total_token <= 0 &&
                  user.userFromDb?.token_event_name === null
                ) {
                  setNoTokenModalOpen(true);
                  return;
                }

                addMessageMutation.mutate(data);
              })}
              className="mx-auto h-fit w-full"
            >
              <div
                ref={dropAreaRef}
                className={cn(
                  "relative rounded-2xl border-[1px] border-primary/15 bg-background shadow-md transition-colors duration-300 ease-in-out",
                  isDragging && "border-2 border-primary/50 bg-primary/5",
                  contextLimitReached && "border-destructive",
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleFileDrop}
              >
                {isDragging && (
                  <div className="absolute inset-0 z-10 flex flex-col items-center justify-center rounded-2xl bg-primary/5 backdrop-blur-sm">
                    <Upload className="mb-2 h-10 w-10 text-primary/70" />
                    <p className="text-sm font-medium text-primary/70">Drop files to upload</p>
                  </div>
                )}
                <div className="flex w-full flex-col p-2">
                  {(rawErrors?.length ?? 0) > 0 && (
                    <div className="flex flex-wrap gap-2 pb-2 transition-all duration-300">
                      <div className="relative w-full max-w-md">
                        <Collapsible className="group">
                          <CollapsibleTrigger className="flex w-full items-center justify-between">
                            <Button
                              variant="destructive"
                              size="sm"
                              className="w-full justify-between gap-2 pr-1.5 group-data-[state=open]:rounded-b-none"
                              type="button"
                            >
                              <div className="flex items-center gap-2">
                                <DangerTriangleSolid className="size-4 text-primary" />
                                <span>Error</span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={removeErrorDetails}
                                className="size-6"
                              >
                                <X className="size-3" />
                              </Button>
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="w-full">
                            <div className="flex flex-col gap-2 overflow-x-auto rounded-lg rounded-t-none border border-destructive/20 bg-destructive/5 p-3 py-1">
                              {rawErrors?.map((error) => (
                                <Typography.P
                                  key={error.message}
                                  className="mt-0 font-mono text-sm text-destructive"
                                >
                                  {error.message} on{" "}
                                  {error.pageUrl === "/" ? "index.tsx" : error.pageUrl}
                                </Typography.P>
                              ))}
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      </div>
                    </div>
                  )}

                  {(formNetworkErrors?.length ?? 0) > 0 && (
                    <div className="flex flex-wrap gap-2 pb-2 transition-all duration-300">
                      <div className="relative w-full max-w-md">
                        <Collapsible className="group">
                          <CollapsibleTrigger className="flex w-full items-center justify-between">
                            <Button
                              variant="destructive"
                              size="sm"
                              className="w-full justify-between gap-2 pr-1.5 group-data-[state=open]:rounded-b-none"
                              type="button"
                            >
                              <div className="flex items-center gap-2">
                                <DangerTriangleSolid className="size-4 text-primary" />
                                <span>Network Error</span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={removeNetworkErrors}
                                className="size-6"
                              >
                                <X className="size-3" />
                              </Button>
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="w-full">
                            <div className="flex flex-col gap-2 overflow-x-auto rounded-lg rounded-t-none border border-destructive/20 bg-destructive/5 p-3 py-1">
                              {formNetworkErrors?.map((error) => (
                                <Typography.P
                                  key={error.message}
                                  className="mt-0 font-mono text-sm text-destructive"
                                >
                                  {error.message} on {error.details?.url} with status{" "}
                                  {error.details?.status} and method {error.details?.method}
                                </Typography.P>
                              ))}
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      </div>
                    </div>
                  )}

                  {files.length > 0 && (
                    <div className="flex flex-col gap-2 pb-2 pt-1 transition-all duration-300">
                      <div className="flex items-center justify-between">
                        <span
                          className={cn(
                            "text-xs",
                            selectedImages.length > MAX_IMAGES_PER_MESSAGE
                              ? "text-destructive"
                              : "text-muted-foreground",
                          )}
                        >
                          {selectedImages.length} of {MAX_IMAGES_PER_MESSAGE} images
                        </span>
                      </div>
                      <div className="flex cursor-pointer flex-wrap gap-2">
                        {files.map((file, index) =>
                          file.type.startsWith("image/") ? (
                            <PreviewImage key={`image-${index}`}>
                              <PreviewImageTrigger asChild>
                                <div className="relative">
                                  <div
                                    className={cn(
                                      "h-16 w-16 cursor-pointer overflow-hidden rounded-md",
                                    )}
                                  >
                                    <img
                                      src={URL.createObjectURL(file) || "/placeholder.svg"}
                                      alt="User uploaded image"
                                      className="h-full w-full rounded-md border border-border object-cover"
                                      loading="lazy"
                                    />

                                    <Button
                                      type="button"
                                      size="icon"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeImage(file);
                                      }}
                                      className="absolute -right-1.5 -top-1.5 z-[100] h-5 w-5 rounded-full border border-border bg-background p-0 text-primary shadow-md hover:bg-secondary/90 hover:text-destructive"
                                    >
                                      <X className="size-4" />
                                    </Button>
                                  </div>
                                </div>
                              </PreviewImageTrigger>
                              <PreviewImageContent
                                fileContent={URL.createObjectURL(file)}
                                file={URL.createObjectURL(file)}
                                fileType="image"
                                fullscreen
                              />
                            </PreviewImage>
                          ) : (
                            <Preview key={`file-${index}`}>
                              <PreviewTrigger asChild>
                                <div className="relative">
                                  <div
                                    className={cn(
                                      "flex h-fit w-fit items-center gap-2 rounded-xl border border-primary/10 p-1 text-sm font-medium leading-none shadow-sm transition-all duration-300",
                                      "border border-primary/20 bg-accent/80 text-primary/80 hover:bg-accent/90 hover:text-primary dark:border-primary/15",
                                    )}
                                    key={`user-mentioned-file-${index}`}
                                  >
                                    <div className="flex items-center gap-3">
                                      <div className="flex h-8 w-8 items-center justify-center rounded-md border border-border/60 bg-sidebar-primary/80 p-1 shadow-sm">
                                        <FileTextSolid className="h-full w-full text-background dark:text-primary/80" />
                                      </div>
                                      <div className="flex flex-col gap-0 pr-4">
                                        <span className="truncate font-medium text-primary/90">
                                          {file.name.split("/").pop()}
                                        </span>
                                        <span className="text-left text-xs font-normal text-muted-foreground">
                                          File
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <Button
                                    type="button"
                                    size="icon"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      removeFile(file);
                                    }}
                                    className="absolute -right-1.5 -top-1.5 z-[100] h-5 w-5 rounded-full bg-background p-0 shadow-md ring-1 ring-border/50 hover:bg-secondary/90 hover:text-destructive"
                                  >
                                    <X className="size-3.5" />
                                  </Button>
                                </div>
                              </PreviewTrigger>
                              <PreviewContent file={file} fileName={file.name} border />
                            </Preview>
                          ),
                        )}
                      </div>
                    </div>
                  )}

                  {(selectedPaths || []).length > 0 && (
                    <div className="flex cursor-pointer flex-wrap gap-2 pb-2 pt-1 transition-all duration-300">
                      {(selectedPaths || []).map((path, index) => (
                        <FilePathPreview
                          key={index}
                          path={path}
                          projectId={projectId}
                          onRemove={(path) => {
                            form.setValue(
                              "selectedPaths",
                              (selectedPaths || []).filter((p) => p !== path),
                            );
                          }}
                        />
                      ))}
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem dir="ltr" className="relative mb-2">
                        <FormControl>
                          <div className="relative">
                            <Textarea
                              className={cn(
                                "border-default placeholder:text-placeholder relative m-0 box-border flex h-12 h-fit w-full resize-none overflow-y-auto rounded-md border-0 bg-background px-3 py-2 pl-2 pt-1.5 text-[0.9rem] outline-none ring-offset-background transition-all duration-200 focus-visible:outline-0 focus-visible:ring-0 focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50",
                              )}
                              placeholder={
                                "Tell softgen what to do, @ to mention specific files to edit"
                              }
                              minHeight={22}
                              maxHeight={256}
                              {...field}
                              onChange={handleInput}
                              onKeyDown={handleKeyDown}
                              onPaste={handlePaste}
                              onFocus={handleTextareaFocus}
                              onBlur={handleTextareaBlur}
                              onClick={handleTextareaClick}
                              ref={(e) => {
                                field.ref(e);
                                textareaRef.current = e as unknown as HTMLTextAreaElement;
                              }}
                              aria-label="Message input"
                              aria-multiline="true"
                              role="textbox"
                            />

                            {showFileSelector && (
                              <div
                                ref={dropdownRef}
                                className="absolute bottom-8 left-2 z-[100] w-80 rounded-lg border border-border bg-background shadow-lg backdrop-blur-sm"
                              >
                                <div className="flex flex-col">
                                  <div className="border-b border-border p-3 py-1">
                                    <Label>Code</Label>
                                  </div>

                                  <div className="max-h-64 overflow-y-auto">
                                    {filteredFiles.length > 0 ? (
                                      filteredFiles.map((file: string, index: number) => {
                                        const fileName = file.split("/").pop();
                                        const displayName =
                                          fileName === "index.tsx"
                                            ? `${file.split("/").slice(0, -1).pop()}/index.tsx`
                                            : fileName;

                                        return (
                                          <div
                                            key={file}
                                            ref={
                                              index === selectedFileIndex ? selectedItemRef : null
                                            }
                                            data-file-index={index}
                                            className={cn(
                                              "flex cursor-pointer items-center gap-3 p-2 text-sm transition-colors hover:bg-sidebar-ring/20",
                                              selectedFileIndex === index && "bg-sidebar-ring/20",
                                            )}
                                            onClick={() => insertFilePath(file)}
                                          >
                                            <div className="flex size-6 items-center justify-center rounded-lg border-[1px] border-border/60 bg-sidebar-primary/80 p-1">
                                              <FileTextSolid className="h-full w-full text-background/90" />
                                            </div>
                                            <div className="flex w-full items-center justify-between gap-0.5 overflow-hidden">
                                              <span className="truncate font-medium text-foreground">
                                                {displayName}
                                              </span>
                                            </div>
                                          </div>
                                        );
                                      })
                                    ) : (
                                      <div className="px-3 py-4 text-center text-sm text-muted-foreground">
                                        No files found
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center justify-between gap-2 pt-2">
                    <div className="flex items-center gap-2">
                      <Select
                        name="selectedModel"
                        value={mode}
                        onValueChange={(value: "creative" | "standard" | "plan") => {
                          if (value) setMode(value);
                        }}
                      >
                        <SelectTrigger className="h-0 w-fit gap-1.5 rounded-lg border-0 bg-transparent py-4 text-xs hover:bg-secondary/50 focus:ring-1 focus:ring-primary/20 [&>span]:flex [&>span]:items-center [&>span]:gap-2 [&>span_svg]:shrink-0 [&>span_svg]:text-muted-foreground/80">
                          <SelectValue>
                            {mode === "creative" ? (
                              <div className="flex items-center gap-1.5 text-muted-foreground">
                                <Sparkles className="h-3.5 w-3.5 fill-purple-500 stroke-primary" />
                                <span className="font-medium">Creative</span>
                              </div>
                            ) : mode === "standard" ? (
                              <div className="flex items-center gap-1.5 text-muted-foreground">
                                <BrainIcon className="h-3.5 w-3.5 fill-blue-500 stroke-primary" />
                                <span className="font-medium">Standard</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-1.5 text-muted-foreground">
                                <Zap className="h-3.5 w-3.5 fill-amber-500 stroke-primary" />
                                <span className="font-medium">Plan</span>
                              </div>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent className="min-w-72 max-w-3xl [&_*[role=option]>span>svg]:shrink-0 [&_*[role=option]>span>svg]:text-muted-foreground/80 [&_*[role=option]>span]:end-2 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:flex [&_*[role=option]>span]:items-center [&_*[role=option]>span]:gap-2 [&_*[role=option]]:pe-8 [&_*[role=option]]:ps-2">
                          <SelectItem value="creative" className="max-w-sm py-2 text-xs">
                            <div className="flex items-center gap-2">
                              <Sparkles className="h-4 w-4 fill-purple-500 stroke-primary" />
                              <div className="flex flex-col gap-0.5">
                                <Label className="text-sm font-medium">Creative</Label>
                                <p className="text-xs text-muted-foreground">
                                  More creative, better for complex changes
                                </p>
                              </div>
                            </div>
                          </SelectItem>
                          <SelectItem value="standard" className="py-2 text-xs">
                            <div className="flex items-center gap-2">
                              <BrainIcon className="h-4 w-4 fill-blue-500 stroke-primary" />
                              <div className="flex flex-col gap-0.5">
                                <Label className="text-sm font-medium">Standard</Label>
                                <p className="text-xs text-muted-foreground">
                                  Better instruction following, more focused
                                </p>
                              </div>
                            </div>
                          </SelectItem>
                          <SelectItem value="plan" className="py-2 text-xs">
                            <div className="flex items-center gap-2">
                              <LightningSolid className="h-4 w-4 fill-amber-500 stroke-primary" />
                              <div className="flex flex-col gap-0.5">
                                <Label className="text-sm font-medium">Plan</Label>
                                <p className="text-xs text-muted-foreground">
                                  Best for planning and high-level tasks
                                </p>
                              </div>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>

                      <Hint label={"Attach files"} side="top">
                        <div data-tooltip={"Attach files"}>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className={cn(
                              "flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg transition-colors duration-200",
                            )}
                            onClick={() => {
                              fileInputRef.current?.click();
                            }}
                            aria-label="Attach files"
                          >
                            <input
                              type="file"
                              multiple
                              accept="image/*"
                              onChange={handleFileUpload}
                              className="hidden"
                              id="file-upload"
                              ref={fileInputRef}
                              aria-label="File upload input"
                            />
                            <Paperclip className="text-primary" />
                          </Button>
                        </div>
                      </Hint>

                      <Hint
                        label={
                          !browserSupportsSpeechRecognition
                            ? "Voice input not supported in this browser. Try Chrome for the best experience."
                            : listening
                              ? "Stop listening"
                              : "Start voice input"
                        }
                        side="top"
                        className={cn(!browserSupportsSpeechRecognition && "max-w-52")}
                      >
                        <div data-tooltip={listening ? "Stop listening" : "Start voice input"}>
                          <Button
                            type="button"
                            variant={
                              !isMicrophoneAvailable || !browserSupportsSpeechRecognition
                                ? "destructive"
                                : "ghost"
                            }
                            size="icon"
                            className={cn(
                              "flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg transition-colors duration-200",
                              listening &&
                                "bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400",
                            )}
                            onClick={listening ? stopListening : startListening}
                            disabled={
                              !browserSupportsSpeechRecognition ||
                              !isMicrophoneAvailable ||
                              isAgentRunning
                            }
                            aria-label={listening ? "Stop voice input" : "Start voice input"}
                          >
                            {!isMicrophoneAvailable || !browserSupportsSpeechRecognition ? (
                              <MicrophoneSlashSolid className="text-primary" />
                            ) : listening ? (
                              <MicrophoneSlashSolid className="text-destructive" />
                            ) : (
                              <Mic className="text-primary" />
                            )}
                          </Button>
                        </div>
                      </Hint>
                    </div>

                    <div className="flex items-center gap-2">
                      <Hint label="Settings" side="top">
                        <div data-tooltip="Settings">
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="flex size-8 cursor-pointer items-center justify-center rounded-lg"
                            onClick={() => setShowAgentSettings(true)}
                          >
                            <CogOneSolid className="text-primary" />
                          </Button>
                        </div>
                      </Hint>
                      <div
                        data-tooltip={
                          isAgentRunning
                            ? "Stop AI"
                            : addMessageMutation.isPending
                              ? "Sending..."
                              : "Send message (Ctrl+Enter)"
                        }
                      >
                        <Button
                          type={isAgentRunning ? "button" : "submit"}
                          size="icon"
                          className={cn(
                            "flex size-7 items-center justify-center transition-all duration-500",
                          )}
                          disabled={addMessageMutation.isPending || isStoppingSession}
                          onClick={isAgentRunning ? handleStopSession : undefined}
                        >
                          {isAgentRunning ? (
                            isStoppingSession ? (
                              <Loading className="size-6 animate-spin text-background" />
                            ) : (
                              <Square
                                className="size-4 rounded-md text-background"
                                strokeWidth={2.5}
                              />
                            )
                          ) : (
                            <ArrowUp className="size-4 text-background" strokeWidth={3} />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Microphone Access Required Message */}
                  {!isMicrophoneAvailable && browserSupportsSpeechRecognition && (
                    <div className="px-3 pb-1 pt-2">
                      <div className="rounded-lg border border-orange-200 bg-orange-50 p-2 dark:border-orange-800 dark:bg-orange-950/20">
                        <p className="text-xs text-orange-700 dark:text-orange-300">
                          🎤 Microphone access is required for voice input. Please allow microphone
                          permissions.
                        </p>
                      </div>
                    </div>
                  )}

                  {listening && (
                    <div className="my-1 flex w-full items-center justify-between rounded-lg border border-yellow-200 bg-yellow-50 px-3 py-1 dark:border-yellow-800 dark:bg-yellow-950/20">
                      <Typography.P className="mt-0 text-sm text-primary">
                        Listening
                        <span className="jumping-dots">
                          <span className="dot-1">.</span>
                          <span className="dot-2">.</span>
                          <span className="dot-3">.</span>
                        </span>
                      </Typography.P>

                      <div className="flex items-center gap-1">
                        <div className="h-2 w-2 animate-pulse rounded-full bg-red-500"></div>
                        <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                          LIVE
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </form>
          </Form>
        </div>
      </div>
      <div className="pointer-events-none absolute inset-0 -top-24 bottom-10 mx-2 bg-gradient-to-b from-transparent via-background/50 to-background to-100%" />

      {isMobile && isKeyboardVisible && (
        <div className="fixed bottom-0 left-0 right-0 z-40 h-1 bg-gradient-to-r from-primary/20 via-primary/40 to-primary/20" />
      )}

      <LazyModal
        open={showAgentSettings}
        onOpenChange={(open) => {
          setShowAgentSettings(open);
        }}
      >
        <AgentSettingModalContent
          projectId={projectId}
          setModal={(modal) => {
            setShowAgentSettings(modal);
          }}
          setFormContent={setFormContent}
          formContent={form.watch("content") || ""}
          availableFiles={availableFiles}
        />
      </LazyModal>

      <LazyModal open={showFileUploadModal} onOpenChange={setShowFileUploadModal}>
        <FileUploadModalContent
          file={fileQueue[0]}
          onConfirm={handleFileUploadConfirm}
          uploadProgress={uploadProgress}
          fileQueue={fileQueue}
          totalAttachments={selectedImages.length}
        />
      </LazyModal>
    </>
  );
};

function BalanceInfo({
  authState,
  tokenInfo,
  animateKey,
  contextLimitReached,
  walletBalance,
}: {
  authState: AuthState;
  tokenInfo: { remainingTokens: string };
  animateKey: number;
  contextLimitReached?: boolean;
  walletBalance?: number;
}) {
  const [isDismissed, setIsDismissed] = useState(false);

  return (
    <AnimatePresence>
      {!isDismissed && (
        <motion.div
          key={animateKey}
          className="absolute inset-0 z-0 w-full"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.3 }}
        >
          <div
            className={cn(
              "group absolute inset-0 -top-6 z-0 mx-auto h-10 w-[96%] rounded-t-2xl bg-sidebar-ring/20 px-4",
            )}
          >
            <div
              className={cn(
                "flex h-[60%] w-full items-center justify-between",
                contextLimitReached && "h-[140%] items-start pt-[0.14rem]",
              )}
            >
              {contextLimitReached ? (
                <Label className="text-sm text-background">
                  Context window is full. Please start a new thread/task.
                </Label>
              ) : (
                <Label className="text-xs text-primary">
                  {authState.wholesalePlan
                    ? `${formatCurrency({ amount: walletBalance ? walletBalance : authState.userFromDb?.wallet_balance, digits: 2 })} remaining`
                    : `${tokenInfo.remainingTokens} Tokens left`}
                </Label>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="hidden h-fit w-fit bg-transparent p-1 pb-0.5 hover:bg-transparent group-hover:block"
                onClick={() => setIsDismissed(true)}
              >
                <X className="size-4 text-primary" />
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default MessageInput;
