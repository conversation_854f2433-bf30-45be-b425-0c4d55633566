import asyncio
from sqlalchemy import inspect
from sqlalchemy.ext.asyncio import create_async_engine
from core.db import Base
from core.config import settings
import ssl

# Import all models to ensure they're registered with Base

async def sync_models():
    print("Syncing database schema with models...")
    
    # Get MySQL connection info from settings
    mysql_url = settings.database_url
    
    # Configure SSL context for PlanetScale
    connect_args = {}
    if mysql_url.startswith('mysql'):
        ssl_context = ssl.create_default_context(cafile="/etc/ssl/certs/ca-certificates.crt")
        ssl_context.verify_mode = ssl.CERT_REQUIRED
        connect_args = {"ssl": ssl_context}

    engine = create_async_engine(
        mysql_url,
        connect_args=connect_args
    )
    
    # Get existing tables
    async with engine.connect() as conn:
        inspector = inspect(engine)
        existing_tables = await conn.run_sync(lambda sync_conn: inspector.get_table_names())
    
    # Create missing tables
    async with engine.begin() as conn:
        # Get all tables defined in models
        model_tables = Base.metadata.tables.keys()
        
        # Find tables that exist in models but not in database
        missing_tables = set(model_tables) - set(existing_tables)
        
        if missing_tables:
            print(f"Creating missing tables: {', '.join(missing_tables)}")
            # Create only the missing tables
            tables_to_create = [Base.metadata.tables[table_name] for table_name in missing_tables]
            await conn.run_sync(lambda sync_conn: Base.metadata.create_all(
                sync_conn, tables=tables_to_create
            ))
        else:
            print("No missing tables found")
    
    await engine.dispose()
    print("Schema sync completed!")

if __name__ == "__main__":
    asyncio.run(sync_models()) 