import json
import logging
from fastapi import APIRouter, HTTPException, Depends, Form, File, UploadFile
from typing import Optional, List
from core.db import Database
from core.platform.user import User, get_current_active_user
from sqlalchemy.future import select as future_select
from sqlalchemy import select
from core.models import MemoryModule, ProjectThread, Project, ProjectAgentRun
from core.agent.thread_manager import ThreadService
from core.utils.image_processor import process_images
from core.utils.error_store import error_store
import datetime
import asyncio
from sqlalchemy.exc import IntegrityError
from core.platform.is_admin import (
    is_admin,
    is_admin_or_owner_or_team
)

# Initialize APIRouter
router = APIRouter()

db = Database()

async def get_thread_service(db, thread_id: int, current_user: User):
    async with db.get_async_session() as session:
        stmt = future_select(ProjectThread).where(ProjectThread.thread_id == thread_id)
        result = await session.execute(stmt)
        thread = result.scalar_one_or_none()
        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        # Get the project and check access
        stmt = future_select(Project).where(Project.project_id == thread.project_id)
        result = await session.execute(stmt)
        project = result.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Check if user has access (admin, owner, or public project)
        if is_admin_or_owner_or_team(project, current_user.id, current_user.email):
            # If admin, use current user
            if is_admin(current_user) or project.isPublic:
                user = current_user
            else:
                # For non-admins, fetch the project owner's details
                stmt = future_select(User).where(User.id == project.owner_id)
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                
                if not user:
                    raise HTTPException(status_code=404, detail="Project owner not found")
        else:
            raise HTTPException(status_code=403, detail="Not authorized to access this thread")
        
        messages = json.loads(thread.messages)

        # Get the latest agent run for this thread
        stmt = (
            select(ProjectAgentRun)
            .where(ProjectAgentRun.thread_id == thread_id)
            .order_by(ProjectAgentRun.creation_date.desc())
        )
        result = await session.execute(stmt)
        latest_run = result.scalar_one_or_none()
        
        latest_session_status = None
        if latest_run:
            latest_session_status = {
                "project_agent_run": latest_run.run_id,
                "status": latest_run.status,
                "creation_date": latest_run.creation_date,
                "objective": latest_run.objective
            }

        error = error_store.get_error(thread_id)
        if error:
            error_store.clear_error(thread_id)  # Clear the error after sending it

        return {
            "thread_id": thread.thread_id,
            "project_id": thread.project_id,
            "creation_date": thread.creation_date,
            "last_updated_date": thread.last_updated_date,
            "total_messages": len(messages),
            "messages": messages,
            "latest_session_status": latest_session_status,
            "error": error,
            "total_free_requests": user.total_free_request,
            "free_total_token": user.free_total_token,
            "name": thread.name or "New Thread"  # Include the thread name
        }

async def create_thread_service(db, project_id: str, page_route: Optional[str], current_user: User):
    async with db.get_async_session() as session:
        # Check if the project exists and the user has access to it
        stmt = future_select(Project).where(Project.project_id == project_id)
        result = await session.execute(stmt)
        project = result.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Use the utility function to check access
        if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
            raise HTTPException(status_code=403, detail="Not authorized to create a thread for this project")
        
        # Create a new thread and commit it
        new_thread = ProjectThread(
            project_id=project_id,
            messages=json.dumps([]),
            creation_date=datetime.datetime.now().isoformat(),
            last_updated_date=datetime.datetime.now().isoformat(),
            page_route=page_route,
            name="New Thread"  # Default name for new threads
        )
        session.add(new_thread)
        await session.commit()
        
        # Initialize empty memory module in background without waiting
        async def init_memory():
            try:
                async with db.get_async_session() as new_session:
                    # Check if memory module already exists
                    memory_query = select(MemoryModule).where(
                        (MemoryModule.project_id == project_id) & 
                        (MemoryModule.module_name == 'OpenFilesInEditor')
                    )
                    result = await new_session.execute(memory_query)
                    existing_module = result.scalar_one_or_none()

                    if existing_module:
                        # Update existing module
                        existing_module.data = json.dumps([])
                    else:
                        # Create new module
                        memory_module = MemoryModule(
                            project_id=project_id,
                            module_name="OpenFilesInEditor",
                            data=json.dumps([])
                        )
                        new_session.add(memory_module)
                    
                    try:
                        await new_session.commit()
                    except IntegrityError:
                        # If we hit a race condition where another process created the module
                        # between our check and commit, just update the existing one
                        await new_session.rollback()
                        memory_query = select(MemoryModule).where(
                            (MemoryModule.project_id == project_id) & 
                            (MemoryModule.module_name == 'OpenFilesInEditor')
                        )
                        result = await new_session.execute(memory_query)
                        existing_module = result.scalar_one_or_none()
                        if existing_module:
                            existing_module.data = json.dumps([])
                            await new_session.commit()
            except Exception as e:
                logging.error(f"Error initializing memory module: {str(e)}")

        try:
            # Fire and forget
            asyncio.create_task(init_memory())
        except Exception as e:
            logging.error(f"Error creating init_memory task: {str(e)}")
        
        return {
            "thread_id": new_thread.thread_id,
            "project_id": new_thread.project_id,
            "creation_date": new_thread.creation_date,
            "last_updated_date": new_thread.last_updated_date,
            "page_route": new_thread.page_route,
            "name": new_thread.name
        }

async def add_message_to_thread_service(db, thread_id: int, content: str, images: List[UploadFile], current_user: User):
    try:
        logging.info(f"Received request to add message to thread {thread_id}")
        thread_manager = ThreadService(db)
        
        # Check if the thread exists and if the user has permission
        thread = await thread_manager.get_thread(thread_id)
        if not thread:
            logging.error(f"Thread {thread_id} not found")
            raise HTTPException(status_code=404, detail="Thread not found")
        
        # Check if the user owns the project associated with this thread
        async with db.get_async_session() as session:
            stmt = future_select(Project).where(Project.project_id == thread.project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()
            
            # Use the utility function to check access
            if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
                raise HTTPException(status_code=403, detail="Not authorized to add message to this thread")
        
        # Process images
        processed_images = process_images(images) if images else []
        
        # Add the message to the thread
        logging.info(f"Adding message to thread {thread_id}")
        await thread_manager.add_message(
            thread_id,
            {"role": "user", "content": content},
            images=processed_images if processed_images else None
        )
        
        logging.info(f"Message added successfully to thread {thread_id}")

        return {"message": "Message added successfully", "thread_id": thread_id}
    except Exception as e:
        logging.error(f"Error adding message to thread {thread_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error adding message to thread: {str(e)}")
    
async def delete_thread_service(db, thread_id: int, project_id: str, current_user: User):
    async with db.get_async_session() as session:
        # Check if the thread exists
        stmt = future_select(ProjectThread).where(
            ProjectThread.thread_id == thread_id,
            ProjectThread.project_id == project_id
        )
        result = await session.execute(stmt)
        thread = result.scalar_one_or_none()
        
        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        # Check if the project exists and user has permission
        stmt = future_select(Project).where(Project.project_id == project_id)
        result = await session.execute(stmt)
        project = result.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Check if user has permission to delete the thread
        if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
            raise HTTPException(status_code=403, detail="Not authorized to delete this thread")
        
        # Delete the thread
        await session.delete(thread)
        await session.commit()
        
        return {"message": "Thread deleted successfully"} 
    
async def rename_thread_service(db, thread_id: int, project_id: str, new_name: str, current_user: User):
    async with db.get_async_session() as session:
        # Check if the thread exists
        stmt = future_select(ProjectThread).where(
            ProjectThread.thread_id == thread_id,
            ProjectThread.project_id == project_id
        )
        result = await session.execute(stmt)
        thread = result.scalar_one_or_none()
        
        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        # Check if the project exists and user has permission
        stmt = future_select(Project).where(Project.project_id == project_id)
        result = await session.execute(stmt)
        project = result.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Check if user has permission to rename the thread
        if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
            raise HTTPException(status_code=403, detail="Not authorized to rename this thread")
        
        # Update the thread name
        thread.name = new_name
        thread.last_updated_date = datetime.datetime.now().isoformat()
        await session.commit()
        
        return {
            "thread_id": thread.thread_id,
            "project_id": thread.project_id,
            "name": thread.name,
            "message": "Thread renamed successfully"
        }

@router.get("/thread/{thread_id}")
async def get_thread(thread_id: int, current_user: User = Depends(get_current_active_user)):
    return await get_thread_service(db, thread_id, current_user)

@router.post("/thread")
async def create_thread(
    project_id: str,
    page_route: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    return await create_thread_service(db, project_id, page_route, current_user)

@router.post("/thread/{thread_id}/add_message")
async def add_message_to_thread(
    thread_id: int,
    content: str = Form(...),
    images: List[UploadFile] = File(None),
    current_user: User = Depends(get_current_active_user)
):
    return await add_message_to_thread_service(db, thread_id, content, images, current_user)    

@router.delete("/thread/{project_id}/{thread_id}")
async def delete_thread(
    project_id: str,
    thread_id: int,
    current_user: User = Depends(get_current_active_user)
):
    return await delete_thread_service(db, thread_id, project_id, current_user)    

@router.patch("/thread/{project_id}/{thread_id}/rename")
async def rename_thread(
    project_id: str,
    thread_id: int,
    new_name: str = Form(...),
    current_user: User = Depends(get_current_active_user)
):
    return await rename_thread_service(db, thread_id, project_id, new_name, current_user)    
   