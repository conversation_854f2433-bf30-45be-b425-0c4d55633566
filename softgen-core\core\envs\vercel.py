import logging
import re
from typing import Optional, Dict, Any, List
import json
from core.config import settings
import base64
from core.utils.tmux_utils import execute_in_tmux_2
import uuid
import asyncio
from core.envs.env_ops import env_ops
import aiohttp

# Create a module-specific logger
logger = logging.getLogger("vercel")
logger.setLevel(logging.INFO)

# Prevent propagation to the root logger to avoid duplicate logs
logger.propagate = False

# Only add handler if not already added to avoid duplicate logs
if not logger.handlers:
    # Create handler with timestamp format
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s.%(msecs)03d %(levelname)s [%(name)s]: %(message)s',
                                 datefmt='%Y-%m-%d %H:%M:%S')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class VercelManager:
    async def setup_vercel_cli(self, workspace_id: str) -> Dict[str, Any]:
        """
        Set up Vercel CLI in the container.
        """
        try:
            command = "npm install vercel"  # Install locally instead of globally
            result = await env_ops.execute_command(workspace_id, command)

            return {
                "success": result["success"],
                "output": result["stdout"]
            }
        except Exception as e:
            logger.exception("Error setting up Vercel CLI")
            return {"error": str(e)}

    async def vercel_login(self, workspace_id: str, token: str, use_default_scope: bool = False) -> Dict[str, Any]:
        """
        Set up Vercel CLI to use the provided token.
        """
        try:
            sandbox = await env_ops.get_started_sandbox(workspace_id)

            # First, try to clear any existing login
            clear_command = "rm -rf /app/.vercel"
            await sandbox.process.exec(clear_command)

            # Construct login command
            command = f"cd /app && npx vercel logout && npx vercel login --token {token}"
            if use_default_scope:
                command += f" --scope {settings.vercel_scope}"
            command += " --yes"

            result = await sandbox.process.exec(command)

            # Access the dictionary correctly
            stderr = result.result
            if "Not authorized" in stderr or "You must re-authenticate" in stderr:
                return {
                    "error": "Authorization failed",
                    "message": "Please check your Vercel token and scope permissions"
                }


            return {"success": True, "message": "Vercel login completed"}

        except Exception as e:
            return {"error": f"An error occurred during Vercel login: {str(e)}"}

    async def vercel_link(self, workspace_id: str, token: str, use_default_scope: bool = False, project_id: str = None) -> Dict[str, Any]:
        """
        Link the current directory to a Vercel project.
        """
        try:
            sandbox = await env_ops.get_started_sandbox(workspace_id)

            # Create a unique project name using project_id
            unique_project_name = f"sg-{project_id}"

            command = f"cd /app && npx vercel link --token {token} --project {unique_project_name}"
            if use_default_scope:
                command += f" --scope {settings.vercel_scope}"
            command += " --yes"

            result = await sandbox.process.exec(command)

            # Get the project ID from .vercel/project.json
            project_info_cmd = "cat /app/.vercel/project.json 2>/dev/null || echo '{}'"
            project_info_result = await sandbox.process.exec(project_info_cmd)
            project_info = json.loads(project_info_result.result)
            vercel_project_id = project_info.get('projectId')

            if vercel_project_id:
                # Unpause the project using direct API call
                async with aiohttp.ClientSession() as session:
                    url = f"https://api.vercel.com/v1/projects/{vercel_project_id}/unpause"
                    params = {"teamId": settings.vercel_scope} if use_default_scope else {}
                    headers = {
                        "Authorization": f"Bearer {token}",
                        "Content-Type": "application/json"
                    }
                    async with session.post(url, params=params, headers=headers) as response:
                        if response.status == 200:
                            logger.info(f"Successfully unpaused project {vercel_project_id}")
                        else:
                            logger.warning(f"Failed to unpause project {vercel_project_id}: {await response.text()}")

                # Enable firewall and add basic protection
                firewall_results = await self._setup_basic_firewall(workspace_id, token, use_default_scope, vercel_project_id)
                
                return {
                    "success": result.exit_code == 0,
                    "output": result.result,
                    "project_name": unique_project_name,
                    "project_id": vercel_project_id,
                    "firewall_setup": firewall_results
                }
            else:
                return {
                    "success": result.exit_code == 0,
                    "output": result.result,
                    "project_name": unique_project_name,
                    "warning": "Could not determine project ID"
                }
        except Exception as e:
            logger.exception("Error linking Vercel project")
            return {"error": str(e)}

    async def _setup_basic_firewall(self, workspace_id: str, token: str, use_default_scope: bool, project_id: str) -> Dict[str, Any]:
        """
        Setup basic firewall protection for a Vercel project.
        """
        try:
            firewall_results = {
                "enabled": False,
                "rate_limiting": False,
                "errors": []
            }

            # Get project scope dynamically using the existing method
            project_scope = await self._get_vercel_project_scope(workspace_id)
            if not project_scope:
                # Fallback to default scope if not found
                project_scope = settings.vercel_scope

            # 1. Enable firewall
            try:
                enable_result = await self._enable_firewall(project_id, token, use_default_scope, project_scope)
                if enable_result.get("success"):
                    firewall_results["enabled"] = True
                    logger.info(f"Firewall enabled for project {project_id}")
                else:
                    firewall_results["errors"].append(f"Failed to enable firewall: {enable_result.get('error')}")
            except Exception as e:
                firewall_results["errors"].append(f"Error enabling firewall: {str(e)}")

            # 2. Add rate limiting rule
            try:
                rate_limit_result = await self._add_rate_limiting_rule(project_id, token, use_default_scope, project_scope)
                if rate_limit_result.get("success"):
                    firewall_results["rate_limiting"] = True
                    logger.info(f"Rate limiting rule added for project {project_id}")
                else:
                    firewall_results["errors"].append(f"Failed to add rate limiting: {rate_limit_result.get('error')}")
            except Exception as e:
                firewall_results["errors"].append(f"Error adding rate limiting: {str(e)}")



            return firewall_results

        except Exception as e:
            logger.exception("Error setting up basic firewall")
            return {
                "enabled": False,
                "rate_limiting": False,
                "errors": [f"Firewall setup failed: {str(e)}"]
            }

    async def _enable_firewall(self, project_id: str, token: str, use_default_scope: bool, project_scope: str = None) -> Dict[str, Any]:
        """
        Enable firewall for a Vercel project using the correct PUT endpoint.
        """
        try:
            # Use provided project scope or fallback to default
            if not project_scope:
                project_scope = settings.vercel_scope

            # Enable firewall using the PUT /v1/security/firewall/config endpoint
            url = "https://api.vercel.com/v1/security/firewall/config"
            params = {
                "projectId": project_id,
                "teamId": project_scope
            } if project_scope else {"projectId": project_id}
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            request_body = {
                "firewallEnabled": True
            }

            async with aiohttp.ClientSession() as session:
                async with session.put(url, params=params, headers=headers, json=request_body) as response:
                    if response.status == 200:
                        return {"success": True, "message": "Firewall enabled successfully"}
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to enable firewall: {response.status} - {error_text}")
                        return {"error": f"Failed to enable firewall: {response.status}", "details": error_text}

        except Exception as e:
            logger.exception("Error enabling firewall")
            return {"error": str(e)}

    async def _add_rate_limiting_rule(self, project_id: str, token: str, use_default_scope: bool, project_scope: str = None) -> Dict[str, Any]:
        """
        Add rate limiting WAF rule to a Vercel project.
        """
        try:
            # Use the provided project scope (should be passed from _setup_basic_firewall)
            if not project_scope:
                project_scope = settings.vercel_scope

            # Get current firewall config first
            url = "https://api.vercel.com/v1/security/firewall/config"
            params = {
                "projectId": project_id,
                "teamId": project_scope
            } if project_scope else {"projectId": project_id}
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                # Get current config
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        current_config = await response.json()
                        
                        # Add rate limiting rule to existing rules
                        rules = current_config.get("active", {}).get("rules", [])
                        
                        # Rate limiting rule: Limits requests to 100 per minute per IP address
                        # When exceeded, users get a CAPTCHA challenge instead of being blocked (429 error)
                        # This prevents DDoS attacks while keeping legitimate users happy
                        # The "rate_limit_api_id" type tracks requests per unique IP address
                        rate_limit_rule = {
                            "name": "Rate Limiting Protection",
                            "description": "Basic rate limiting to protect against traffic spikes and abuse",
                            "active": True,
                            "conditionGroup": [
                                {
                                    "conditions": [
                                        {
                                            "op": "gt",
                                            "type": "rate_limit_api_id",
                                            "value": 100  # Max 100 requests per minute per IP
                                        }
                                    ]
                                }
                            ],
                            "action": {
                                "mitigate": {
                                    "action": "challenge",  # Show CAPTCHA instead of blocking
                                    "rateLimit": None,
                                    "redirect": None,
                                    "actionDuration": None
                                }
                            }
                        }
                        rules.append(rate_limit_rule)

                        # Update firewall config with new rules
                        update_body = {
                            "firewallEnabled": True,
                            "managedRules": current_config.get("active", {}).get("managedRules", {}),
                            "rules": rules
                        }

                        async with session.put(url, params=params, headers=headers, json=update_body) as update_response:
                            if update_response.status == 200:
                                return {
                                    "success": True,
                                    "message": "Rate limiting protection added successfully"
                                }
                            else:
                                error_text = await update_response.text()
                                logger.error(f"Failed to add rate limiting WAF rule: {update_response.status} - {error_text}")
                                return {
                                    "error": f"Failed to add rate limiting WAF rule: {update_response.status}",
                                    "details": error_text
                                }
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get current firewall config: {response.status} - {error_text}")
                        return {
                            "error": f"Failed to get current firewall config: {response.status}",
                            "details": error_text
                        }

        except Exception as e:
            logger.exception("Error adding rate limiting WAF rule")
            return {"error": str(e)}



    async def vercel_env_push(self, workspace_id: str, token: str, use_default_scope: bool = False) -> Dict[str, Any]:
        """
        Push environment variables to Vercel from the local .env file.
        Properly handles environment variables with special characters in their values.
        """
        try:
            max_retries = 2
            env_content = None
            for attempt in range(max_retries + 1):
                try:
                    env_content = await env_ops.read_file_contents(workspace_id, "/app/.env.local")
                    if env_content:
                        break
                except Exception:
                    logger.exception("Attempt {attempt+1}: Failed to read .env.local")
                await asyncio.sleep(1)  # small delay between retries

            if not env_content:
                warning_msg = "Failed to read .env.local file after retries. Continuing without pushing env vars."
                logger.warning(warning_msg)
                return {
                    "warning": warning_msg,
                    "env_push_results": [],
                    "success": True
                }

            logger.info(f"Successfully read .env.local file. Found content length: {len(env_content)}")

            # Parse environment variables
            env_vars = self.parse_env_content(env_content)
            if not env_vars:
                logger.warning("No environment variables found to process")
                return {
                    "warning": "No environment variables found to process",
                    "env_push_results": []
                }

            results = []
            logger.info(f"Starting env push for workspace: {workspace_id}")

            sandbox = await env_ops.get_started_sandbox(workspace_id)

            for key, value in env_vars.items():
                logger.info(f"Processing env var: {key} (value length: {len(value)})")

                # First, try to remove existing env var
                remove_command = f"cd /app && npx vercel env rm {key} production --token {token}"
                if use_default_scope:
                    remove_command += f" --scope {settings.vercel_scope}"
                remove_command += " -y"  # Force yes for removal

                await sandbox.process.exec(remove_command)

                # Now add the new env var
                value_b64 = base64.b64encode(value.encode()).decode()
                add_command = f"""cd /app && echo "{value_b64}" | base64 -d | npx vercel env add {key} production"""
                if use_default_scope:
                    add_command += f" --scope {settings.vercel_scope}"
                add_command += f" --token {token}"

                # Log the command with masked token
                masked_command = add_command.replace(token, "****")
                logger.info(f"Executing command: {masked_command}")

                add_result = await sandbox.process.exec(add_command)

                success = add_result.exit_code == 0
                logger.info(f"Env var {key} push result - Success: {success}")

                if not success:
                    logger.error(f"Failed to add env var {key}. Output: {add_result.result}")

                results.append({
                    "action": "update",
                    "key": key,
                    "success": success,
                    "output": add_result.result
                })

            logger.info(f"Completed env push. Total vars processed: {len(results)}")
            return {
                "success": True,
                "env_push_results": results
            }

        except Exception as e:
            logger.exception(f"Error pushing env variables: {str(e)}")
            return {"error": str(e)}

    async def update_vercel_config(self, workspace_id: str) -> Optional[Dict[str, Any]]:
        """
        Update the vercel.json file in the workspace with the specified configuration.
        """
        try:
            vercel_config = {
                "framework": "nextjs",
                "redirects": [
                    {"source": "/old-path", "destination": "/new-path", "permanent": True}
                ],
                "headers": [
                    {
                        "source": "/(.*)",
                        "headers": [
                            {"key": "X-Content-Type-Options", "value": "nosniff"}
                        ]
                    }
                ],
                "cleanUrls": True,
                "trailingSlash": False
            }

            # Convert config to JSON string
            config_content = json.dumps(vercel_config, indent=2)

            # Encode content to base64
            content_bytes = config_content.encode('utf-8')
            base64_content = base64.b64encode(content_bytes).decode('utf-8')

            # Path for vercel.json
            path = '/app/vercel.json'
            escaped_path = path.replace('"', '\\"')

            sandbox = await env_ops.get_started_sandbox(workspace_id)

            # Update file using base64
            cmd = f"sh -c 'echo {base64_content} | base64 -d > \"{escaped_path}\"'"
            result = await sandbox.process.exec(cmd)

            if result.exit_code != 0:
                return {
                    "success": False,
                    "message": "Failed to update vercel.json",
                    "error": result.result
                }

            # Read back the file to verify
            read_cmd = f"cat \"{escaped_path}\""
            read_result = await sandbox.process.exec(read_cmd)

            if read_result.exit_code != 0:
                return {
                    "success": False,
                    "message": "Failed to verify vercel.json content",
                    "error": read_result.result
                }

            return {
                "success": True,
                "message": "Vercel config updated successfully",
                "config": json.loads(read_result.result)
            }

        except Exception as e:
            logger.exception("Error updating Vercel config")
            return {"error": str(e)}

    async def get_vercel_build_logs(self, workspace_id: str, deployment_url: str, token: str, use_default_scope: bool = False) -> str:
        """
        Fetch detailed build logs for a failed deployment using vercel inspect --logs.
        Returns the raw logs without any parsing or modification.
        """
        try:
            logger.info(f"Fetching detailed build logs for deployment: {deployment_url}")

            if not deployment_url:
                logger.error("Cannot fetch logs: No deployment URL provided")
                return "No deployment URL available to fetch logs"

            # Construct the command to fetch logs
            logs_command = f"cd /app && npx vercel inspect --logs {deployment_url} --token {token}"
            if use_default_scope:
                logs_command += f" --scope {settings.vercel_scope}"

            # Execute the command
            logs_result = await env_ops.execute_command(workspace_id, logs_command)

            # Return the raw logs without any parsing
            return logs_result.get("stdout", "No logs available")

        except Exception as e:
            logger.exception("Error fetching build logs")
            return f"Error fetching build logs: {str(e)}"

    async def vercel_deploy(self, workspace_id: str, token: str, use_default_scope: bool = False) -> Dict[str, Any]:
        try:
            logger.info("Starting Vercel deployment...")
            custom_domain = None

            await self.update_vercel_config(workspace_id)
            deploy_command = "npx vercel deploy --prod"
            if use_default_scope:
                deploy_command += f" --scope {settings.vercel_scope}"
            deploy_command += f" --token {token} --yes"

            # Log the command (make sure to mask the token)
            # masked_command = deploy_command.replace(token, "****")
            logger.info(f"Executing deploy command: {deploy_command}")

            session_name = f"deploy-{uuid.uuid4().hex[:8]}"
            deploy_result = await execute_in_tmux_2(deploy_command, session_name, workspace_id)

            logger.info(f"Deploy command execution completed. Result code: {deploy_result['code']}")

            # Replace line breaks in the output with empty string
            combined_output = deploy_result.get("result", "").replace("\n", "")
            logger.info(f"Raw deployment output (cleaned): {combined_output}")

            # Try multiple patterns for URL extraction
            url_patterns = [
                r'Production:\s*(https://[a-zA-Z0-9][a-zA-Z0-9-]*\.vercel\.app)',  # Match after "Production:" label
                r'https://[a-zA-Z0-9][a-zA-Z0-9-]*\.vercel\.app',                  # Standard format
                r'https://[^\s]+\.vercel\.app'                                      # Fallback pattern
            ]

            deployment_url = None
            for pattern in url_patterns:
                url_match = re.search(pattern, combined_output)
                if url_match:
                    deployment_url = url_match.group(1) if 'Production:' in pattern else url_match.group(0)
                    deployment_url = deployment_url.strip()
                    logger.info(f"Found deployment URL using pattern {pattern}: {deployment_url}")
                    break
                else:
                    logger.info(f"No match found with pattern: {pattern}")

            # If the command failed (code != 0), treat it as a build failure
            if deploy_result.get("code") != 0:
                # Updated regex pattern to capture URLs with numbers and hyphens
                url_match = re.search(r'https://[a-zA-Z0-9-]+\.vercel\.app', combined_output)
                deployment_url = url_match.group(0) if url_match else None

                # Extract initial specific error from build logs
                specific_error = self._extract_most_relevant_error(combined_output)

                # Extract configuration warnings
                config_warnings = re.findall(r'⚠.*?(?:\n.*?)*?$', combined_output, re.MULTILINE)

                # Fetch detailed build logs if we have a deployment URL
                detailed_logs = ""
                if deployment_url:
                    detailed_logs = await self.get_vercel_build_logs(workspace_id, deployment_url, token, use_default_scope)


                clean_output = remove_ansi_escape(detailed_logs)

                return {
                    "success": False,
                    "error": "Build failed",
                    "specific_error": specific_error,
                    "deployment_url": deployment_url,
                    "config_warnings": config_warnings,
                    "logs": combined_output,
                    "detailed_logs": clean_output
                }


             # Check for other deployment errors
            elif "Error:" in combined_output:
                error_match = re.search(r'Error: (.*)', combined_output)
                specific_error = error_match.group(1) if error_match else "Unknown deployment error"

                # Fetch detailed logs if we have a deployment URL
                detailed_logs = ""
                if deployment_url:
                    detailed_logs = await self.get_vercel_build_logs(workspace_id, deployment_url, token, use_default_scope)

                return {
                    "success": False,
                    "error": "Deployment failed",
                    "specific_error": specific_error,
                    "logs": combined_output,
                    "detailed_logs": detailed_logs
                }

            sandbox = await env_ops.get_started_sandbox(workspace_id)

            # Fetch all aliases and select the shortest one
            if deployment_url:
                inspect_command = f"npx vercel inspect {deployment_url} --token {token}"
                if use_default_scope:
                    inspect_command += f" --scope {settings.vercel_scope}"
                inspect_result = await sandbox.process.exec(inspect_command, cwd="/app")
                inspect_output = inspect_result.result

                # Extract all aliases and find the shortest one
                alias_matches = re.findall(r'╶\s+(https://[^\s\n]+)(?:\n|$)', inspect_output)
                if alias_matches:
                    # Find the shortest URL from all matches
                    deployment_url = min(alias_matches, key=len)

            # If deployment was successful and we have a URL
            if "success" in deploy_result and deployment_url:
                # Get all domains for the project
                domains_command = f"npx vercel domains ls --token {token}"
                if use_default_scope:
                    domains_command += f" --scope {settings.vercel_scope}"

                domains_result = await sandbox.process.exec(domains_command, cwd="/app")

                # Look for custom domains in the output
                if domains_result.exit_code == 0:
                    domain_matches = re.findall(r'(?:│|┊)\s+([a-zA-Z0-9.-]+)\s+(?:│|┊)', domains_result.result)
                    if domain_matches:
                        # Filter out vercel.app domains
                        custom_domains = [d for d in domain_matches if not d.endswith('.vercel.app')]
                        if custom_domains:
                            custom_domain = custom_domains[0]  # Use the first custom domain

            return {
                "success": True,
                "message": "Deployment successful",
                "deployment_url": deployment_url,
                "production_url": custom_domain,  # Use custom domain if available
                "logs": combined_output
            }

        except Exception as e:
            logger.exception("Error deploying to Vercel")
            return {"success": False, "error": str(e)}

    async def vercel_unlink(self, workspace_id: str, token: str, use_default_scope: bool = False) -> Dict[str, Any]:
        """
        Pause the Vercel project and clean up .vercel directory.
        """
        try:
            sandbox = await env_ops.get_started_sandbox(workspace_id)

            # First, get the project ID from the .vercel/project.json file
            project_info_cmd = "cat /app/.vercel/project.json 2>/dev/null || echo '{}'"
            project_info_result = await sandbox.process.exec(project_info_cmd)
            project_info = json.loads(project_info_result.result)
            project_id = project_info.get('projectId')

            if project_id:
                # Pause the project using direct API call
                async with aiohttp.ClientSession() as session:
                    url = f"https://api.vercel.com/v1/projects/{project_id}/pause"
                    params = {"teamId": settings.vercel_scope} if use_default_scope else {}
                    headers = {
                        "Authorization": f"Bearer {token}",
                        "Content-Type": "application/json"
                    }
                    async with session.post(url, params=params, headers=headers) as response:
                        if response.status != 200:
                            logger.warning(f"Failed to pause project {project_id}: {await response.text()}")

            # Remove .vercel directory if it exists
            rm_command = "rm -rf /app/.vercel"
            rm_result = await sandbox.process.exec(rm_command)
            if rm_result.exit_code != 0:
                logger.warning(f"Failed to remove .vercel directory: {rm_result.result}")

            return {
                "success": True,
                "message": "Project successfully paused and .vercel directory cleaned up",
                "project_id": project_id
            }

        except Exception as e:
            logger.exception("Error in vercel_unlink")
            return {"error": f"An error occurred while unlinking from Vercel: {str(e)}"}

    @staticmethod
    def parse_env_content(env_content: str) -> Dict[str, str]:
        """
        Parse environment variables from a string content.
        Returns a dictionary of key-value pairs.

        Example input:
        DATABASE_URL="******************************"
        NEXT_PUBLIC_APP_URL="http://localhost:3000"
        NODE_ENV=development
        """
        try:
            env_vars = {}
            env_pattern = re.compile(r'^([A-Za-z_][A-Za-z0-9_]*)=["]?(.*?)["]?\s*$', re.MULTILINE)

            for match in env_pattern.finditer(env_content):
                key, value = match.groups()
                value = value.strip('"')  # Remove any remaining quotes
                env_vars[key] = value

            return env_vars

        except Exception:
            logger.exception("Error parsing env content")
            return {}

    async def vercel_connect_domain(self, workspace_id: str, domain: str, token: str, use_default_scope: bool = False) -> Dict[str, Any]:
        """
        Connect a custom domain to the current Vercel project.
        """
        try:
            # Clean the domain - remove protocol and trailing slashes
            cleaned_domain = domain.replace('https://', '').replace('http://', '').rstrip('/')
            logger.info(f"Connecting domain {cleaned_domain} to Vercel project...")

            # Force add domain to the project with --force flag
            command = f"cd /app && npx vercel domains add {cleaned_domain} --force --token {token}"
            if use_default_scope:
                command += f" --scope {settings.vercel_scope}"

            result = await env_ops.execute_session(workspace_id, command)
            output = result.get("stdout", "")

            # Check for successful domain connection
            if ("Success! Domain" in output and "added to project" in output) or "is now assigned to" in output:
                return {
                    "success": True,
                    "message": f"Domain {cleaned_domain} successfully connected to project",
                    "domain": cleaned_domain
                }

            # If we get here, there was an error adding the domain
            # Check if "Error:" exists in the output
            if "Error:" in output:
                # Extract error message after "Error:"
                error_match = re.search(r'Error:(.+?)(?=\n|$)', output)
                error_message = error_match.group(1).strip() if error_match else output
                return {
                    "error": "Failed to connect domain to project",
                    "detail": error_message,
                    "message": output
                }
            else:
                return {
                    "error": "Failed to connect domain to project",
                    "detail": output,
                    "message": output
                }


        except Exception as e:
            logger.exception("Error connecting domain to Vercel project")
            return {"error": str(e)}

    async def _get_vercel_project_scope(self, workspace_id: str) -> Optional[str]:
        """
        Read the scope from .vercel/project.json file.
        Returns the orgId (scope) from the project configuration.
        """
        try:
            # Read the .vercel/project.json file
            project_json_content = await env_ops.read_file_contents(workspace_id, "/app/.vercel/project.json")
            if project_json_content:
                import json
                project_data = json.loads(project_json_content)
                org_id = project_data.get("orgId")
                if org_id:
                    logger.info(f"Found scope from .vercel/project.json: {org_id}")
                    return org_id
                else:
                    logger.warning("No orgId found in .vercel/project.json")
            else:
                logger.warning(".vercel/project.json file not found or empty")
        except Exception as e:
            logger.warning(f"Error reading .vercel/project.json: {str(e)}")
        
        return None

    async def vercel_domain_status(self, workspace_id: str, domain: str, token: str, use_default_scope: bool = False) -> Dict[str, Any]:
        """
        Check the status of a domain using vercel domains inspect command.
        Returns detailed information about the domain including verification status, DNS configuration, etc.
        """
        try:
            # Clean the domain - remove protocol and trailing slashes
            cleaned_domain = domain.replace('https://', '').replace('http://', '').rstrip('/')

            # Use vercel domains inspect command
            command = f"cd /app && npx vercel domains inspect {cleaned_domain}"

            
            if use_default_scope:
                command += f" --scope {settings.vercel_scope}"
                logger.info(f"Using default scope: {settings.vercel_scope}")
            else:
                # Try to get scope from .vercel/project.json for custom tokens
                project_scope = await self._get_vercel_project_scope(workspace_id)
                if project_scope:
                    command += f" --scope {project_scope}"
                    logger.info(f"Using project scope: {project_scope}")
                else:
                    logger.info("No project scope found, using user-provided scope (no default scope)")
            
            command += f" --token {token}"     

            # Log the command (mask the token for security)
            masked_command = command.replace(token, "****") if token else command
            logger.info(f"Executing command: {masked_command}")

            result = await env_ops.execute_command(workspace_id, command)
            output = result.get("stdout", "")
            success = result.get("success", False)

            print("result", result)

            logger.info(f"Command execution result - Success: {success}")
            logger.info(f"Output length: {len(output)}")
            logger.info(f"Raw output: {output[:200]}..." if output else "No output")

            # Check if the command was successful
            if success and output:
                logger.info("Domain inspection successful, parsing results...")
                # Parse the domain information from the output
                domain_info = self._parse_domain_inspect_output(output)
                
                logger.info(f"Parsed domain info: {domain_info}")
                
                return {
                    "success": True,
                    "domain": cleaned_domain,
                    "status": "found",
                    "message": f"Domain {cleaned_domain} found and accessible",
                    "info": domain_info,
                    "raw_output": output,
                    "summary": {
                        "domain": cleaned_domain,
                        "verification_status": domain_info.get("verification_status", "Unknown"),
                        "project": domain_info.get("project", "Not assigned"),
                        "dns_records_count": len(domain_info.get("dns_records", [])),
                        "nameservers_count": len(domain_info.get("nameservers", []))
                    }
                }
            else:
                # Check for specific error cases
                logger.info(f"Command failed. Combined output: {output}")
                
                # Check for scope/authentication errors first (most common issue)
                if any(auth_error in output.lower() for auth_error in [
                    "not authorized",
                    "authentication failed", 
                    "invalid token",
                    "unauthorized",
                    "must re-authenticate",
                    "trying to access resource under scope"
                ]):
                    logger.error("Authentication/Authorization failed")
                    
                    # Extract scope information if available
                    scope_info = ""
                    scope_match = None
                    
                    # Look for scope information in the error
                    if "scope" in output.lower():
                        # Try different patterns for scope extraction
                        scope_patterns = [
                            r'scope "([^"]+)"',
                            r'under scope "([^"]+)"',
                            r'under ([^\s]+)',
                            r'scope ([^\s]+)'
                        ]
                        
                        for pattern in scope_patterns:
                            scope_match = re.search(pattern, output)
                            if scope_match:
                                scope_info = f" for scope '{scope_match.group(1)}'"
                                break
                    
                    return {
                        "success": False,
                        "domain": cleaned_domain,
                        "status": "auth_error",
                        "message": f"Authentication failed{scope_info}. Please check your Vercel token and scope permissions.",
                        "error_details": output,
                        "summary": {
                            "domain": cleaned_domain,
                            "status": "auth_error",
                            "scope": scope_match.group(1) if scope_match else "unknown",
                            "suggestion": "Verify your Vercel token is valid and has access to the correct scope. Try running 'vercel whoami' to check your current scope."
                        }
                    }
                
                # Check if domain doesn't exist or access denied
                elif any(error_msg in output.lower() for error_msg in [
                    "domain not found",
                    "you don't have access to the domain",
                    "doesn't exist",
                    "not found"
                ]):
                    logger.info("Domain not found or access denied")
                    
                    # Extract scope information if available
                    scope_info = ""
                    if "under" in output:
                        scope_match = re.search(r'under ([^\s]+)', output)
                        if scope_match:
                            scope_info = f" in scope '{scope_match.group(1)}'"
                    
                    return {
                        "success": True,
                        "domain": cleaned_domain,
                        "status": "not_found",
                        "message": f"Domain {cleaned_domain} not found or you don't have access to it{scope_info}",
                        "error_details": output,
                        "summary": {
                            "domain": cleaned_domain,
                            "status": "not_found",
                            "scope": scope_match.group(1) if 'scope_match' in locals() else "unknown",
                            "suggestion": "Try running 'vercel domains ls' to see available domains in this scope"
                        }
                    }
                
                # Check for scope/permission errors
                elif "scope" in output.lower() or "permission" in output.lower():
                    logger.error("Permission denied")
                    return {
                        "success": False,
                        "domain": cleaned_domain,
                        "status": "permission_error",
                        "message": "Permission denied. You may not have access to this domain in the current scope.",
                        "error_details": output,
                        "summary": {
                            "domain": cleaned_domain,
                            "status": "permission_error",
                            "suggestion": "Check if you have the correct scope and permissions for this domain"
                        }
                    }
                
                # Generic error
                else:
                    logger.error(f"Generic error occurred: {output}")
                    return {
                        "success": False,
                        "domain": cleaned_domain,
                        "status": "error",
                        "message": "Failed to check domain status. Please try again or contact support.",
                        "error_details": output,
                        "summary": {
                            "domain": cleaned_domain,
                            "status": "error",
                            "suggestion": "Check the error details and try again"
                        }
                    }

        except Exception as e:
            logger.exception(f"Error checking domain status for {domain}")
            return {
                "success": False,
                "domain": domain,
                "status": "error",
                "error": str(e)
            }

    def _parse_domain_inspect_output(self, output: str) -> Dict[str, Any]:
        """
        Parse the output of vercel domains inspect command to extract relevant information.
        """
        try:
            domain_info = {
                "name": None,
                "verification_status": None,
                "dns_records": [],
                "nameservers": [],
                "created_at": None,
                "updated_at": None,
                "project": None
            }

            lines = output.split('\n')
            current_section = None

            for line in lines:
                line = line.strip()
                
                # Skip empty lines
                if not line:
                    continue

                # Parse domain name
                if line.startswith('Domain:'):
                    domain_info["name"] = line.split(':', 1)[1].strip()
                
                # Parse verification status
                elif line.startswith('Verification Status:'):
                    domain_info["verification_status"] = line.split(':', 1)[1].strip()
                
                # Parse creation date
                elif line.startswith('Created:'):
                    domain_info["created_at"] = line.split(':', 1)[1].strip()
                
                # Parse last updated
                elif line.startswith('Updated:'):
                    domain_info["updated_at"] = line.split(':', 1)[1].strip()
                
                # Parse project assignment
                elif line.startswith('Project:'):
                    domain_info["project"] = line.split(':', 1)[1].strip()
                
                # Parse DNS records section
                elif 'DNS Records' in line:
                    current_section = 'dns_records'
                    continue
                
                # Parse Nameservers section
                elif 'Nameservers' in line:
                    current_section = 'nameservers'
                    continue
                
                # Parse DNS records
                elif current_section == 'dns_records' and line and not line.startswith('─'):
                    # Try to parse DNS record line
                    parts = line.split()
                    if len(parts) >= 3:
                        dns_record = {
                            "type": parts[0],
                            "name": parts[1],
                            "value": parts[2]
                        }
                        domain_info["dns_records"].append(dns_record)
                
                # Parse nameservers
                elif current_section == 'nameservers' and line and not line.startswith('─'):
                    nameserver = line.strip()
                    if nameserver:
                        domain_info["nameservers"].append(nameserver)

            return domain_info

        except Exception as e:
            logger.exception("Error parsing domain inspect output")
            return {
                "error": f"Failed to parse domain information: {str(e)}",
                "raw_output": output
            }


    def _parse_domains_list_output(self, output: str) -> List[Dict[str, Any]]:
        """
        Parse the output of vercel domains ls command to extract domain information.
        """
        try:
            domains = []
            lines = output.split('\n')
            
            # Skip header lines and find the data section
            data_started = False
            
            for line in lines:
                line = line.strip()
                
                # Skip empty lines
                if not line:
                    continue
                
                # Look for the table header to know when data starts
                if 'Domain' in line and 'Status' in line and 'Created' in line:
                    data_started = True
                    continue
                
                # Skip separator lines
                if line.startswith('─') or line.startswith('│'):
                    continue
                
                # Parse domain data lines
                if data_started and line:
                    # Try to parse the domain information
                    # Format is typically: Domain | Status | Created | Updated
                    parts = [part.strip() for part in line.split('│')]
                    
                    if len(parts) >= 4:
                        domain_info = {
                            "domain": parts[0],
                            "status": parts[1],
                            "created": parts[2],
                            "updated": parts[3] if len(parts) > 3 else None
                        }
                        domains.append(domain_info)
                    elif len(parts) >= 2:
                        # Fallback for simpler format
                        domain_info = {
                            "domain": parts[0],
                            "status": parts[1],
                            "created": None,
                            "updated": None
                        }
                        domains.append(domain_info)

            return domains

        except Exception:
            logger.exception("Error parsing domains list output")
            return []

    def _extract_most_relevant_error(self, logs: str) -> str:
        """
        Extract the most relevant error from the logs based on priority.

        Args:
            logs (str): The logs to extract errors from

        Returns:
            str: The most relevant error message
        """
        # Define error patterns in order of priority
        error_patterns = [
            # npm errors
            (r'npm ERR!(.+?)(?=\n\n|\Z)', lambda m: f"npm install error: {m.group(1).strip()}"),

            # Missing dependencies
            (r'Cannot find (module|package) \'([^\']+)\'', lambda m: f"Missing dependency: {m.group(2)}"),

            # TypeScript errors
            (r'TS\d+:(.+?)(?=\n\n|\Z)', lambda m: f"TypeScript error: {m.group(1).strip()}"),

            # Syntax errors
            (r'SyntaxError:(.+?)(?=\n\n|\Z)', lambda m: f"Syntax error: {m.group(1).strip()}"),

            # Build errors
            (r'Build error occurred(.+?)(?=\n\n|\Z)', lambda m: f"Build error: {m.group(1).strip()}"),

            # General errors
            (r'Error:(.+?)(?=\n\n|\Z)', lambda m: f"Error: {m.group(1).strip()}")
        ]

        # Check each pattern in order of priority
        for pattern, formatter in error_patterns:
            match = re.search(pattern, logs, re.DOTALL)
            if match:
                return formatter(match)

        return "Unknown error occurred during deployment"

if __name__ == "__main__":
    import asyncio

    async def test_vercel_config():
        try:
            print("\n=== Testing VercelManager Config Update ===")

            # Initialize manager
            manager = VercelManager()

            # Test workspace ID - replace with your actual workspace ID
            workspace_id = "sandbox-2b0b5810"

            # Check if vercel.json exists before update
            print("\nChecking existing vercel.json:")
            check_command = "test -f /app/vercel.json && cat /app/vercel.json || echo 'No existing vercel.json'"
            check_result = env_ops.execute_session(workspace_id, check_command)
            print(f"Before update: {check_result.stdout}")

            print("\nTesting update_vercel_config:")
            config_result = await manager.update_vercel_config(workspace_id)
            print(f"Config update result: {json.dumps(config_result, indent=2)}")

            # Check actual file content after update
            print("\nVerifying file content directly:")
            verify_command = "cat /app/vercel.json"
            verify_result = env_ops.execute_session(workspace_id, verify_command)
            print(f"Direct file content: {verify_result.stdout}")

            if config_result.get('success'):
                print("\n✅ Config update successful!")
                print("\nVercel.json content:")
                print("-" * 50)
                print(json.dumps(config_result['config'], indent=2))
                print("-" * 50)
            else:
                print("\n❌ Config update failed!")
                print(f"Error: {config_result.get('error') or config_result.get('message')}")

        except Exception as e:
            print(f"\nError during testing: {str(e)}")
            import traceback
            traceback.print_exc()

    async def test_vercel_deploy():
        try:
            print("\n=== Testing Vercel Deployment ===")

            # Initialize manager
            manager = VercelManager()

            # Test parameters - replace these with your actual values
            workspace_id = "sandbox-247c2c40"
            vercel_token = "************************"

            print("\nInitiating deployment...")
            deploy_result = await manager.vercel_deploy(workspace_id, vercel_token)

            print("\nDeployment Result:")
            print("-" * 50)
            if deploy_result.get('success'):
                print("✅ Deployment successful!")
                print(f"Deployment URL: {deploy_result.get('deployment_url')}")
            else:
                print("❌ Deployment failed!")
                print(f"Error: {deploy_result.get('error')}")
                print("\nSpecific Error:")
                print(deploy_result.get('specific_error', 'No specific error provided'))
                print("\nConfig Warnings:")
                print(deploy_result.get('config_warnings', 'No warnings'))

            print("\nFull Logs:")
            print("-" * 50)
            print(deploy_result.get('logs', 'No logs available'))

        except Exception as e:
            print(f"\nError during testing: {str(e)}")
            import traceback
            traceback.print_exc()

    async def test_env_parsing_from_workspace():
        print("\n=== Testing Environment Variable Parsing from Workspace ===")

        try:
            # Initialize manager
            manager = VercelManager()
            workspace_id = "sandbox-34954fab"

            # Read the .env.local file directly using env_ops
            env_content = await env_ops.read_file_contents(workspace_id, "/app/.env.local")

            if not env_content:
                print("❌ Failed to read .env.local file")
                return

            print(f"📄 Successfully read .env.local file. Found content length: {len(env_content)}")
            print(f"📄 Raw env content:\n{env_content}")

            # Parse the environment variables
            result = manager.parse_env_content(env_content)

            print("\n📋 Parsed Environment Variables:")
            print("-" * 50)
            for key, value in result.items():
                print(f"{key}: {value}")

            print(f"\n✅ Total variables parsed: {len(result)}")

        except Exception as e:
            print(f"\n❌ Error during testing: {str(e)}")
            import traceback
            traceback.print_exc()

    # Run the test
    asyncio.run(test_vercel_deploy())

    async def test_vercel_build_logs():
        try:
            print("\n=== Testing Vercel Build Logs Fetching ===")

            # Initialize manager
            manager = VercelManager()

            # Test parameters - replace these with your actual values
            workspace_id = "sandbox-247c2c40"
            deployment_url = "your-project-name.vercel.app"  # Replace with an actual deployment URL
            vercel_token = "your-vercel-token"  # Replace with your actual token

            print(f"\nFetching build logs for deployment: {deployment_url}")
            logs = await manager.get_vercel_build_logs(workspace_id, deployment_url, vercel_token)

            print("\nBuild Logs:")
            print("-" * 50)
            print(logs)
            print("-" * 50)

            print("\n✅ Test completed!")

        except Exception as e:
            print(f"\n❌ Error during testing: {str(e)}")
            import traceback
            traceback.print_exc()

    # Uncomment to run the build logs test
    # asyncio.run(test_vercel_build_logs())



def remove_ansi_escape(text: str) -> str:
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    return ansi_escape.sub('', text)
