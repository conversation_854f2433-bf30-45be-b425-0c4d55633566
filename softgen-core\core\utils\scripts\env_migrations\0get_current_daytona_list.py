import json
import requests
from collections import defaultdict
from pathlib import Path

def extract_workspace_ids():
    # API endpoint and headers
    url = 'https://stage.daytona.work/api/workspace'
    headers = {
        'accept': 'application/json',
        'Authorization': 'Bearer dtn_f092546b75b789012215f4e6956de8016d52926eb31388c40aa4427972519b2a'
    }
    
    # Make API request
    response = requests.get(url, headers=headers)
    print(response.json())
    workspaces = response.json()
    
    # Extract workspace info with running status and state
    workspace_info = []
    for workspace in workspaces:
        workspace_id = workspace['id']
        is_running = False
        provider_state = None
        node_domain = None
        
        # Check running status from projects
        for project in workspace.get('projects', []):
            if project.get('info', {}).get('isRunning', False):
                is_running = True
                break
        
        # Get state and nodeDomain from providerMetadata
        try:
            metadata = json.loads(workspace['info']['providerMetadata'])
            provider_state = metadata.get('state')
            node_domain = metadata.get('nodeDomain')
        except (<PERSON><PERSON><PERSON><PERSON>, json.JSONDecodeError):
            continue
            
        # Only include workspaces with provider_state "started"
        if provider_state != "started":
            continue
            
        # Construct terminal URL and web URL
        terminal_url = f"https://22222-{workspace_id}.{node_domain}/" if node_domain else None
        web_url = f"https://3000-{workspace_id}.{node_domain}/" if node_domain else None
            
        workspace_info.append({
            'id': workspace_id,
            'is_running': is_running,
            'provider_state': provider_state,
            'node_domain': node_domain,
            'terminal_url': terminal_url,
            'web_url': web_url
        })
    
    # Group IDs by their number after the hyphen
    number_groups = defaultdict(list)
    for info in workspace_info:
        number = info['id'].split('-')[1]
        number_groups[number].append(info)
    
    # Filter out groups that have more than one ID
    duplicate_groups = {num: infos for num, infos in number_groups.items() if len(infos) > 1}
    single_infos = [infos[0] for num, infos in number_groups.items() if len(infos) == 1]
    
    return single_infos, duplicate_groups

def get_duplicate_workspace_ids():
    """
    Returns a list of workspace IDs that are duplicates (have the same number after the hyphen).
    """
    single_infos, duplicate_groups = extract_workspace_ids()
    
    # Collect all workspace IDs from duplicate groups
    duplicate_workspace_ids = []
    for infos in duplicate_groups.values():
        # Sort by ID to consistently get workspace2-* versions
        sorted_infos = sorted(infos, key=lambda x: x['id'])
        # Add all except the first one (keeping workspace1-* and removing workspace2-*)
        for info in sorted_infos[1:]:
            duplicate_workspace_ids.append(info['id'])
    
    return duplicate_workspace_ids

if __name__ == "__main__":
    single_infos, duplicate_groups = extract_workspace_ids()
    
    # Calculate counts
    total_single = len(single_infos)
    total_duplicate_workspaces = sum(len(infos) for infos in duplicate_groups.values())
    total_workspaces = total_single + total_duplicate_workspaces
    
    # Create a dictionary structure for JSON output
    output_data = {
        "counts": {
            "total_workspaces": total_workspaces,
            "single_workspaces": total_single,
            "duplicate_workspaces": total_duplicate_workspaces,
            "duplicate_groups": len(duplicate_groups)
        },
        "single_workspaces": single_infos,
        "duplicate_groups": duplicate_groups
    }
    
    # Get the script's directory and create output path
    script_dir = Path(__file__).parent
    output_dir = script_dir / "workspace_ids_current.json"
    
    # Write JSON to output file
    with open(output_dir, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    # # Add this to also print the duplicate IDs:
    # duplicate_ids = get_duplicate_workspace_ids()
    # print("\nDuplicate workspace IDs to remove:")
    # print(duplicate_ids)