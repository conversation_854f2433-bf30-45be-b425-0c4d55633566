import {
  clearAllC<PERSON><PERSON>,
  getC<PERSON>ie,
  getItemWithExpiry,
  remove<PERSON><PERSON>ie,
  removeItemWithExpiry,
  setCookieWithExpiry,
} from "@/utils/auth-utils";
import { useMutation } from "@tanstack/react-query";
import axios, { AxiosInstance } from "axios";
import { debug } from "./debug";

export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

export const getToken = () => {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    const cookies = document.cookie.split("; ");
    const tokenCookie = cookies.find((row) => row.startsWith("access_token="));

    if (!tokenCookie) {
      console.warn("No access_token cookie found");
      return null;
    }

    const token = tokenCookie.split("=")[1];
    if (!token) {
      console.warn("Access token is empty");
      return null;
    }

    return token;
  } catch (error) {
    console.error("Error parsing access token from cookies:", error);
    return null;
  }
};

const api = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use(
  async (config) => {
    let token = getCookie("access_token");

    if (!token) {
      token = getItemWithExpiry("access_token");
      if (token) {
        debug("Token found in localStorage but not in cookie, restoring cookie");
        setCookieWithExpiry("access_token", token, 600 * 60 * 1000);
      }
    }

    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    } else {
      console.warn("No valid token found for API request");

      const cookieStatus = document.cookie ? "Cookies exist" : "No cookies found";
      const tokenCookie = document.cookie
        .split("; ")
        .find((row) => row.startsWith("access_token="));
      console.warn(
        `Cookie status: ${cookieStatus}, Access token cookie: ${tokenCookie ? "present" : "missing"}`,
      );

      if (
        !config.url?.includes("/logout") &&
        !config.url?.includes("/kinde_user") &&
        typeof window !== "undefined"
      ) {
        debug("Attempting to refresh auth state before proceeding with request");
        try {
          const authData = localStorage.getItem("auth-storage");
          if (authData) {
            const parsedData = JSON.parse(authData);
            if (parsedData?.state?.user?.access_token) {
              token = parsedData.state.user.access_token;
              debug("Found token in auth store, applying to request");
              setCookieWithExpiry("access_token", token!, 600 * 60 * 1000);
              config.headers["Authorization"] = `Bearer ${token}`;
            } else {
              debug("No valid token in auth store, clearing persisted state");
              localStorage.removeItem("auth-storage");
            }
          }
        } catch (e) {
          console.error("Error trying to restore auth state:", e);
          localStorage.removeItem("auth-storage");
        }
      }
    }
    return config;
  },
  (error) => {
    console.error("Error in request interceptor:", error);
    return Promise.reject(error);
  },
);

const addErrorHandling = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      console.error("API Error:", error.response);

      if (error.response && error.response.status === 401) {
        if (typeof window !== "undefined") {
          localStorage.clear();
          localStorage.removeItem("auth-storage");
        }

        clearAllCookies();

        removeCookie("access_token");
        removeCookie("kinde_id");

        removeItemWithExpiry("access_token");
        removeItemWithExpiry("kinde_id");

        if (typeof window !== "undefined") {
          window.location.href = "/";
          window.location.reload();
        }
      }

      if (error.response && error.response.data) {
        throw error.response.data;
      }

      throw error;
    },
  );
};

addErrorHandling(api);

const handleApiError = (error: unknown, customMessage: string) => {
  console.error(customMessage, error);
  throw error;
};

export const createProject = async (name: string, stack?: string) => {
  try {
    const response = await api.post("/project", null, { params: { name, stack } });
    return response.data;
  } catch (error) {
    handleApiError(error, "Create project error:");
  }
};

export const getProject = async (projectId: string) => {
  try {
    const response = await api.get(`/project/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get project error:");
  }
};

export const deleteProject = async (projectId: string) => {
  try {
    const response = await api.delete(`/project/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Delete project error:");
  }
};

export const listProjects = async () => {
  try {
    const response = await api.get("/projects");
    return response.data;
  } catch (error) {
    handleApiError(error, "List projects error:");
  }
};
export const updateProject = async (
  projectId: string,
  updateData: {
    name?: string | null;
    onboarding_completed?: boolean | null;
    isPublic?: boolean | null;
    preview_image_url?: string | null;
    custom_instructions?: string | null;
  },
) => {
  try {
    const response = await api.put(
      `/project/${projectId}`,
      {},
      {
        params: updateData,
      },
    );
    return response.data;
  } catch (error) {
    handleApiError(error, "Update project error:");
  }
};

export const startAgent = async ({
  threadId,
  workspaceId,
  projectId,
  maxIterations = 5,
  userInput,
  agentType = "frontend",
  selectedPaths = [],
  objectiveImages = [],
  token,
}: {
  threadId: string;
  workspaceId: string;
  projectId: string;
  maxIterations?: number;
  userInput: string;
  agentType?: string;
  selectedPaths?: string[];
  objectiveImages?: string[];
  token?: string;
}) => {
  try {
    const response = await api.post("/agent/start", {
      thread_id: threadId,
      workspace_id: workspaceId,
      project_id: projectId,
      max_iterations: maxIterations,
      user_input: userInput,
      agent_type: agentType,
      selected_paths: selectedPaths,
      objective_images: objectiveImages,
      token: token,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Start agent error:");
  }
};

export const stopAgent = async (threadId: string) => {
  try {
    const response = await api.post(`/agent/stop/${threadId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Stop session error:");
  }
};

export const getProjectThreads = async (projectId: string) => {
  try {
    const response = await api.get(`/project/${projectId}/threads`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get project threads error:");
  }
};

export const getThread = async (threadId: number) => {
  try {
    const response = await api.get(`/thread/${threadId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get thread error:");
  }
};

export const createThread = async (projectId: string, pageRoute?: string) => {
  try {
    const response = await api.post("/thread", null, {
      params: {
        project_id: projectId,
        page_route: pageRoute,
      },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Create thread error:");
  }
};

export const startSessionRun = async (
  projectId: string,
  threadId: string | null,
  objective: string,
  objectiveImages: File[],
  forceCommunicationInterval: boolean,
  mode: string = "interactive",
  promptSet: string = "1",
  autonomousIterations: number = 20,
  selectedPaths: string[] = [],
  model_id: string = "1",
) => {
  try {
    const formData = new FormData();
    formData.append("project_id", projectId);
    if (threadId) formData.append("thread_id", threadId);
    formData.append("objective", objective);
    formData.append("force_communication_interval", forceCommunicationInterval.toString());
    formData.append("mode", mode);
    formData.append("prompt_set", promptSet);
    formData.append("autonomous_iterations", autonomousIterations.toString());
    formData.append("selected_paths", selectedPaths.join(","));
    formData.append("model_id", model_id);

    if (objectiveImages && objectiveImages.length > 0) {
      objectiveImages.forEach((image) => {
        formData.append("objective_images", image);
      });
    }

    const response = await api.post("/start_session_run", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Start session run error:");
  }
};

export const addMessageToThread = async (
  threadId: string,
  content: string,
  images: File[] = [],
) => {
  try {
    const formData = new FormData();
    formData.append("content", content);

    // Validate and append each image
    images.forEach((image, index) => {
      if (!(image instanceof File) || !image.type.startsWith("image/")) {
        throw new Error(`Invalid image file at index ${index}`);
      }
      formData.append("images", image);
    });

    debug("Sending message with form data:", {
      content,
      imageCount: images.length,
      imageTypes: images.map((img) => img.type),
    });

    const response = await api.post(`/thread/${threadId}/add_message`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      // Add timeout for large file uploads
      timeout: 30000,
    });

    if (!response.data) {
      throw new Error("No response data received from server");
    }

    debug("File uploaded successfully:", response.data);
    return response.data;
  } catch (error: unknown) {
    const err = error as {
      detail?: string;
      message?: string;
      response?: {
        data?: unknown;
      };
    };

    if (err.detail && err.detail.includes("Incomplete tool responses")) {
      throw new Error(
        "Wait a short moment. You have to wait for the Agent to complete his actions, then you can send the message.",
      );
    }

    console.error("Error adding message to thread:", err);
    throw new Error(err.detail || err.message || "Failed to add message to thread");
  }
};

export const getAgentStatus = async (threadId: string) => {
  try {
    const response = await api.get(`/agent/status/${threadId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get agent status error:");
  }
};

export const getThreadLlmMessages = async (
  threadId: string,
  {
    hideToolMsgs = false,
    onlyLatestAssistant = false,
    lastNLlmMessages,
  }: {
    hideToolMsgs?: boolean;
    onlyLatestAssistant?: boolean;
    lastNLlmMessages?: number;
  } = {},
) => {
  try {
    const params = new URLSearchParams({
      ...(hideToolMsgs && { hide_tool_msgs: hideToolMsgs.toString() }),
      ...(onlyLatestAssistant && { only_latest_assistant: onlyLatestAssistant.toString() }),
      ...(lastNLlmMessages !== undefined && { last_n_llm_messages: lastNLlmMessages.toString() }),
    });

    const response = await api.get(`/threads/${threadId}/llm_history_messages?${params}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get thread LLM messages error:");
  }
};

export const getThreadSessionStatus = async (threadId: string) => {
  try {
    const response = await api.get(`/thread_session_status/${threadId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get thread session status error:");
  }
};

export const getProjectActiveSessionStatus = async (projectId: string) => {
  try {
    const response = await api.get(`/project_active_session_status/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get project active session status error:");
  }
};

export const getKindeUser = async (kindeId: string, logout?: () => void) => {
  try {
    const response = await api.get(`/kinde_user/${kindeId}`);
    return response.data;
  } catch (error) {
    if (logout) {
      logout();
    }
    handleApiError(error, "Get Kinde user error:");
  }
};

export type PlanType =
  | "entry"
  | "boost"
  | "fly"
  | "pro_enterprise"
  | "elite_enterprise"
  | "wholesale";

export const subscriptionCheckout = async (
  plan: PlanType,
  kindeId: string | null = null,
  isUpgrade: boolean = false,
  toltReferral: string | null = null,
  discountCode: string | null = null,
) => {
  try {
    const params: Record<string, unknown> = {
      plan,
      kinde_id: kindeId,
      is_upgrade: isUpgrade,
    };

    debug("subscriptionCheckout", params);

    if (toltReferral) {
      params.tolt_referral = toltReferral;
    }

    if (discountCode) {
      params.discount_code = discountCode;
    }

    const response = await api.post("/subscription_checkout", null, { params });
    return response.data;
  } catch (error) {
    handleApiError(error, "Subscription checkout error:");
  }
};

export const oneTimePaymentCheckout = async (plan: string, kindeId: string | null = null) => {
  try {
    const response = await api.post("/one_time_payment_checkout", null, {
      params: { plan, kinde_id: kindeId },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "One-time payment checkout error:");
  }
};

export const freeRequestCheckout = async (email: string, package_total_free_request: number) => {
  try {
    const response = await api.post("/free_request_checkout", null, {
      params: { email, package_total_free_request },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Free request checkout error:");
  }
};

export const createCustomerPortalSession = async (customerId: string) => {
  try {
    const response = await api.post("/customer_portal_session", null, {
      params: { customer_id: customerId },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Create customer portal session error:");
  }
};

export const submitFeedback = async (feedbackData: unknown) => {
  try {
    const response = await api.post("/feedback", feedbackData);
    return response.data;
  } catch (error) {
    handleApiError(error, "Submit feedback error:");
  }
};

export const submitAnswer = async (threadId: string, promptId: string, answer: string) => {
  try {
    const formData = new FormData();
    formData.append("prompt_id", promptId);
    formData.append("answer", answer);
    const response = await api.post(`/thread/${threadId}/submit_answer`, formData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Submit answer error:");
  }
};

export const useCheckAndStart = () => {
  return useMutation({
    mutationFn: async (project_id: string) => {
      try {
        const response = await api.post("/check_and_start_env", { project_id });
        return response.data;
      } catch (error) {
        handleApiError(error, "Check and start error:");
      }
    },
  });
};

export const getGithubInfo = async (projectId: string) => {
  try {
    const response = await api.get(`/github-info/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get GitHub info error:");
  }
};

export const addGithubCollaborator = async (
  projectId: string,
  username: string,
  github_repo: string,
) => {
  try {
    const response = await api.post(
      `/git-add-collaborator/${projectId}`,
      JSON.stringify({ username, github_repo }),
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    return response.data;
  } catch (error) {
    handleApiError(error, "Add GitHub collaborator error:");
  }
};

export const revertGithubCommit = async (
  projectId: string,
  commitHash: string,
  revertAndFullReset = false,
) => {
  try {
    const response = await api.post(
      `/git-revert-commit/${projectId}`,
      JSON.stringify({
        commit: { commit_hash: commitHash },
        revert_and_full_reset: revertAndFullReset,
      }),
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    return response.data;
  } catch (error) {
    handleApiError(error, "Revert GitHub commit error:");
  }
};

// ----------------- CODE EDITOR ROUTES -----------------
export const getFiles = async (project_id: string, path = "/") => {
  try {
    const response = await api.get(`/get-files/${project_id}`, { params: { path } });
    return response.data;
  } catch (error) {
    console.error("Error fetching files:", error);
    const err = error as {
      response?: {
        status: number;
        headers: unknown;
        data?: { message?: string };
      };
      request?: unknown;
      message?: string;
    };

    if (err.response) {
      if (err.response.status === 404) {
        console.warn(`No files found for project ${project_id} at path ${path}`);
        return { files: [] };
      }
      console.error("Response status:", err.response.status);
      console.error("Response headers:", err.response.headers);
      throw new Error(
        `Server error: ${err.response.status} - ${err.response.data?.message || "Unknown error"}`,
      );
    } else if (err.request) {
      throw new Error("No response received from server. Please check your network connection.");
    } else {
      throw new Error(`Error setting up request: ${err.message}`);
    }
  }
};

export const getFileContent = async (project_id: string, filePath: string) => {
  try {
    const response = await api.get(`/get-file-content/${project_id}`, {
      params: { file_path: filePath },
      responseType: "arraybuffer",
    });

    const contentType = response.headers["content-type"];
    const fileName = filePath.split("/").pop() || "";

    if (
      contentType.startsWith("text/") ||
      contentType === "application/json" ||
      fileName.startsWith(".")
    ) {
      const textContent = new TextDecoder().decode(response.data);

      if (contentType === "application/json") {
        return JSON.parse(textContent);
      }
      return textContent;
    } else {
      const blob = new Blob([response.data], { type: contentType });
      return URL.createObjectURL(blob);
    }
  } catch (error) {
    console.error("Error fetching file content:", error);
    const err = error as {
      response?: { status: number };
      request?: unknown;
      message?: string;
    };

    if (err.response) {
      throw new Error(`Failed to fetch file content. Server returned ${err.response.status}`);
    } else if (err.request) {
      throw new Error("No response received from server. Please check your network connection.");
    } else {
      throw new Error(`Error setting up request: ${err.message}`);
    }
  }
};

export const createFile = async (
  project_id: string,
  filePath: string,
  content = "",
  isDirectory = false,
) => {
  try {
    const response = await api.post("/create-file", {
      project_id,
      path: filePath,
      content,
      isDirectory,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Create file error:");
  }
};

export const updateFile = async (project_id: string, filePath: string, content: string) => {
  try {
    const response = await api.put("/update-file", { project_id, path: filePath, content });
    return response.data;
  } catch (error) {
    console.error("Update file error:", error);
    throw error;
  }
};

export const deleteFile = async (project_id: string, filePath: string) => {
  try {
    const response = await api.delete("/delete-file", {
      params: { project_id, path: filePath },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Delete file error:");
  }
};

export const executeCommand = async (project_id: string, command: string) => {
  try {
    const response = await api.post("/execute-command", { project_id, command });
    return response.data;
  } catch (error) {
    const err = error as {
      response?: {
        status?: number;
        data?: unknown;
      };
      message?: string;
    };

    if (err.response?.status === 422) {
      console.error("Validation error during command execution:", err.response.data);
      const errorDetail =
        err.response.data && typeof err.response.data === "object" && "detail" in err.response.data
          ? Array.isArray(err.response.data.detail)
            ? (err.response.data.detail as { msg: string }[]).map((e) => e.msg).join(", ")
            : String(err.response.data.detail)
          : "Invalid command parameters";
      throw new Error(`Command execution validation failed: ${errorDetail}`);
    }

    handleApiError(error, "Execute command error:");
  }
};

export const gitCommit = async (project_id: string, commit_message: string) => {
  try {
    const response = await api.post("/git-commit", { project_id, commit_message });
    return response.data;
  } catch (error) {
    handleApiError(error, "Git commit error:");
  }
};

export async function uploadFile({
  projectId,
  file,
  relativePath,
  onProgress,
}: {
  projectId: string;
  file: File;
  relativePath: string;
  onProgress?: (progress: number) => void;
}) {
  try {
    if (!(file instanceof File)) {
      throw new Error("Invalid file object provided");
    }

    const formData = new FormData();
    formData.append("file", file);
    formData.append("relativePath", relativePath || "");

    debug("Uploading file:", {
      name: file.name,
      type: file.type,
      size: file.size,
      path: relativePath,
    });

    const response = await api.post(`/upload-file`, formData, {
      params: { project_id: projectId },
      headers: {
        "Content-Type": "multipart/form-data",
      },
      timeout: 60000,
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        }
      },
    });

    if (!response.data) {
      throw new Error("No data received from server");
    }

    debug("File uploaded successfully:", response.data);
    return response.data;
  } catch (error) {
    const err = error as {
      response?: {
        data?: unknown;
        status?: number;
      };
      message?: string;
    };

    console.error("Error uploading file:", {
      error: err.response?.data || err.message,
      status: err.response?.status,
    });

    if (err.response?.status === 422) {
      const validationError = err.response.data as { detail?: Array<{ msg: string }> | string };
      const errorMessage = validationError.detail
        ? Array.isArray(validationError.detail)
          ? validationError.detail.map((e) => e.msg).join(", ")
          : String(validationError.detail)
        : "Validation error during file upload";
      throw new Error(errorMessage);
    }

    if (err.response?.status === 413) {
      throw new Error("File size too large. Please try a smaller file.");
    } else if (err.response?.status === 415) {
      throw new Error("Unsupported file type. Please check the file format.");
    } else if (err.response?.status === 408) {
      throw new Error("Upload timeout. Please try again.");
    }

    throw new Error(err.message || "Failed to upload file. Please try again.");
  }
}

export const moveFile = async (project_id: string, sourcePath: string, targetPath: string) => {
  try {
    const response = await api.post("/move-file", { project_id, sourcePath, targetPath });
    return response.data;
  } catch (error) {
    handleApiError(error, "Move file error:");
  }
};

{
  /*
    ===========================
        Code Editor Route
    ===========================
*/
}

export const getTemplateByIdentifier = async (identifier: string) => {
  try {
    const response = await api.get(`/templates/${identifier}`);
    return response.data;
  } catch (error) {
    const err = error as {
      response?: { status: number };
      message?: string;
    };
    console.error(`Error fetching template with id ${identifier}:`, err);
    if (err.response?.status === 404) {
      throw new Error("Template not found");
    }
    throw new Error("Failed to fetch template");
  }
};

export const setupVercelCLI = async (
  projectId: string,
  projectName: string,
  vercelToken: string,
) => {
  try {
    const response = await api.post("/vercel/setup-cli", {
      project_id: projectId,
      project_name: projectName,
      vercel_token: vercelToken,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Setup Vercel CLI error:");
  }
};

export const vercelLogin = async (projectId: string, projectName: string, vercelToken: string) => {
  try {
    const response = await api.post("/vercel/login", {
      project_id: projectId,
      project_name: projectName,
      vercel_token: vercelToken,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Vercel login error:");
  }
};

export const vercelLink = async (projectId: string, projectName: string, vercelToken: string) => {
  try {
    const response = await api.post("/vercel/link", {
      project_id: projectId,
      project_name: projectName,
      vercel_token: vercelToken,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Vercel link error:");
  }
};

export const vercelEnvPush = async (
  projectId: string,
  projectName: string,
  vercelToken: string,
) => {
  try {
    const response = await api.post("/vercel/env-push", {
      project_id: projectId,
      project_name: projectName,
      vercel_token: vercelToken,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Vercel env push error:");
  }
};

export const vercelDeploy = async (projectId: string, projectName: string, vercelToken: string) => {
  try {
    const response = await api.post("/vercel/deploy", {
      project_id: projectId,
      project_name: projectName,
      vercel_token: vercelToken,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Vercel deploy error:");
  }
};

export const activateUser = async (kindeId: string) => {
  try {
    const response = await api.post(`/activate_user/${kindeId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Activate user error:");
  }
};

export const startSessionRunWithWebSocket = async (
  projectId: string,
  threadId: string | null,
  objective: string,
  objectiveImages: File[],
  forceCommunicationInterval: boolean,
  mode: string = "interactive",
  promptSet: string = "1",
  autonomousIterations: number = 20,
  selectedPaths: string[] = [],
  model_id: string = "1",
) => {
  try {
    const token = getToken();
    const wsUrl = `${API_BASE_URL.replace("http", "ws")}/start_session_run?token=${token}`;
    const socket = new WebSocket(wsUrl);

    socket.onopen = () => {
      debug("WebSocket connection established for session run");

      const encodeImageAsBase64 = (file: File) => {
        return new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => resolve((reader.result as string).split(",")[1]);
          reader.onerror = (error) => reject(error);
        });
      };

      Promise.all(objectiveImages.map((file) => encodeImageAsBase64(file)))
        .then((encodedImages) => {
          const message = JSON.stringify({
            project_id: projectId,
            thread_id: threadId,
            objective: objective,
            objective_images: encodedImages,
            force_communication_interval: forceCommunicationInterval,
            mode: mode,
            prompt_set: parseInt(promptSet, 10),
            autonomous_iterations: autonomousIterations,
            selected_paths: selectedPaths,
            model_id: model_id,
          });
          socket.send(message);
        })
        .catch((error) => {
          console.error("Error encoding images:", error);
          handleApiError(error, "Start session run with WebSocket error:");
        });
    };

    socket.onerror = (error) => {
      console.error("WebSocket error:", error);
    };

    socket.onclose = (event) => {
      if (!event.wasClean) {
        console.warn(`WebSocket closed unexpectedly, code: ${event.code}`);
      } else {
        debug("WebSocket connection closed");
      }
    };

    return socket;
  } catch (error) {
    handleApiError(error, "Start session run with WebSocket error:");
  }
};

export const connectTerminalSession = (project_id: string) => {
  if (!project_id) {
    console.error("No project_id provided to connectTerminalSession");
    return null;
  }

  const token = getToken();
  const wsUrl = `${API_BASE_URL.replace("http", "ws")}/terminal_session?token=${token}&project_id=${project_id}&session_id=${project_id || "2"}`;
  const socket = new WebSocket(wsUrl);

  socket.onopen = () => {
    debug("Terminal WebSocket connection established");
  };

  socket.onerror = (error) => {
    console.error("Terminal WebSocket error:", error);
  };

  socket.onclose = (event) => {
    if (!event.wasClean) {
      console.warn(`Terminal WebSocket closed unexpectedly, code: ${event.code}`);
    } else {
      debug("Terminal WebSocket connection closed");
    }
  };

  return socket;
};

export const getFilePaths = async (projectId: string, fullPath: string, depth = 4) => {
  try {
    const response = await api.post("/get-file-paths", {
      project_id: projectId,
      full_path: fullPath,
      depth: depth,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Get file paths error:");
  }
};

export const enhancePrompt = async (prompt: string) => {
  try {
    const response = await api.post("/project/enhance-prompt", { prompt });
    return response.data;
  } catch (error) {
    handleApiError(error, "Enhance prompt error:");
  }
};

export const vercelUnlink = async (projectId: string) => {
  try {
    const response = await api.post("/vercel/unlink", { project_id: projectId });
    return response.data;
  } catch (error) {
    handleApiError(error, "Vercel unlink error:");
  }
};

export const createBlog = async (
  headline: string,
  metaDescription: string,
  image: string,
  categoryTitle: string,
  tags: string[],
  mdx: string,
) => {
  try {
    const blogData = {
      headline,
      meta_description: metaDescription,
      image,
      category_title: categoryTitle,
      tags,
      mdx,
    };
    const response = await api.post("/blogs", blogData);
    return response.data;
  } catch (error) {
    handleApiError(error, "Create blog error:");
  }
};

export const updateBlog = async (
  blogId: string,
  headline: string,
  metaDescription: string,
  image: string,
  categoryTitle: string,
  tags: string[],
  mdx: string,
) => {
  try {
    const blogData = {
      headline,
      meta_description: metaDescription,
      image,
      category_title: categoryTitle,
      tags,
      mdx,
    };
    const updateData = Object.fromEntries(
      Object.entries(blogData).filter(([, value]) => value !== undefined),
    );

    const response = await api.put(`/blogs/${blogId}`, updateData);
    return response.data;
  } catch (error) {
    handleApiError(error, "Update blog error:");
  }
};

export const listBlogs = async (limit = null, offset = null) => {
  try {
    const response = await api.get("/blogs");

    const allBlogs = Array.isArray(response.data) ? response.data : response.data?.items || [];
    const total = allBlogs.length;

    let paginatedBlogs = allBlogs;
    if (limit !== null && offset !== null) {
      paginatedBlogs = allBlogs.slice(offset, offset + limit);
    }

    return {
      items: paginatedBlogs,
      total: total,
    };
  } catch (error) {
    console.error("List blogs error:", error);
    return {
      items: [],
      total: 0,
    };
  }
};

export const getBlog = async (blogId: string) => {
  try {
    const response = await api.get(`/blogs/${blogId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get blog error:");
  }
};

export const getBlogBySlug = async (slug: string) => {
  try {
    const response = await api.get(`/blogs/slug/${slug}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get blog by slug error:");
  }
};

export const deleteBlog = async (blogId: string) => {
  try {
    const response = await api.delete(`/blogs/${blogId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Delete blog error:");
  }
};

export const getTokenUsage = async (customerId: string, tokenEventName: string) => {
  try {
    const response = await api.get(`/token_usage/${customerId}`, {
      params: { token_event_name: tokenEventName },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Get token usage error:");
  }
};

export const remixProject = async (projectId: string, envValues: Record<string, string>) => {
  try {
    const response = await api.post(`/project/${projectId}/remix`, {
      env_values: envValues,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Remix project error:");
  }
};

export const addTeamMember = async (projectId: string, teamEmail: string) => {
  try {
    const response = await api.post(`/project/${projectId}/team`, {
      team_email: teamEmail,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Add team member error:");
  }
};

export const removeTeamMember = async (projectId: string, teamEmail: string) => {
  try {
    const response = await api.delete(`/project/${projectId}/team/${teamEmail}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Remove team member error:");
  }
};

export const getEnvKeys = async (projectId: string) => {
  try {
    const response = await api.get(`/get-env-keys/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get environment keys error:");
  }
};

export const submitPrize = async (
  url: string,
  projectId: string,
  userId: string,
  platform: string,
) => {
  try {
    const response = await api.post("/prize-submission", {
      url,
      project_id: projectId,
      user_id: userId,
      description: "project-social-share",
      prize_amount: 500000,
      platform,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Prize submission error:");
  }
};

export const renameThread = async (projectId: string, threadId: number, newName: string) => {
  try {
    const formData = new FormData();
    formData.append("new_name", newName);

    const response = await api.patch(`/thread/${projectId}/${threadId}/rename`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    return response.data;
  } catch (error) {
    handleApiError(error, "Rename thread error:");
  }
};

export const deleteThread = async (projectId: string, threadId: string) => {
  try {
    const response = await api.delete(`/thread/${projectId}/${threadId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Delete thread error:");
  }
};

export const tokenPackageCheckout = async (packageSize: string, kindeId: string) => {
  try {
    const response = await api.post("/token_package_checkout", null, {
      params: {
        package_size: packageSize,
        kinde_id: kindeId,
      },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Token package checkout error:");
  }
};

export const gitForcePull = async (projectId: string) => {
  try {
    const response = await api.post(`/git-force-pull/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Git force pull error:");
  }
};

export const updateTechStack = async (
  projectId: string,
  techStackArray: Array<{ key: string; value: string }>,
) => {
  try {
    const response = await api.post(`/project/${projectId}/tech-stack`, {
      tech_stack_array: techStackArray,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Update tech stack error:");
  }
};

export const updateCustomInstructions = async (projectId: string, customInstructions: string) => {
  try {
    const response = await api.post(`/project/${projectId}/custom-instructions`, {
      custom_instructions: customInstructions,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Update custom instructions error:");
  }
};

export const vercelConnectDomain = async (
  projectId: string,
  domain: string,
  vercelToken: string | null,
) => {
  try {
    const response = await api.post("/vercel-domain-connect", {
      project_id: projectId,
      domain,
      vercel_token: vercelToken,
    });
    return response.data;
  } catch (error) {
    console.error("Vercel domain connect error:", error);

    handleApiError(error, "Vercel domain connect error:");
  }
};

export const vercelDomainStatus = async (
  projectId: string,
  domain: string,
  vercelToken: string | null,
) => {
  try {
    const response = await api.post("/vercel/domain-status", {
      project_id: projectId,
      domain,
      vercel_token: vercelToken,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Vercel domain status check error:");
  }
};

export const getTeamMembers = async (projectId: string) => {
  try {
    const response = await api.get(`/project/${projectId}/team`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get team members error:");
  }
};

// Admin API functions
export const getUsersByAdmin = async ({
  skip = 0,
  limit = 100,
  search = "",
}: {
  skip?: number;
  limit?: number;
  search?: string;
}) => {
  try {
    const params: {
      skip: number;
      limit: number;
      search?: string;
    } = {
      skip,
      limit,
    };

    if (search) params.search = search;

    debug("Requesting users with params:", params);

    const response = await api.get("/admin/users", { params });

    debug("API Response structure:", {
      hasUsers: !!response.data.users,
      usersCount: response.data.users?.length || 0,
      total: response.data.total || 0,
      isArray: Array.isArray(response.data),
    });

    // Handle array response (list of users without pagination info)
    if (Array.isArray(response.data)) {
      debug("Converting array response to paginated format");
      return {
        users: response.data,
        total: response.data.length,
      };
    }

    // If the API already returns the structured format with pagination, use it
    return response.data;
  } catch (error) {
    console.error("Get users by admin error:", error);
    // Return empty result instead of throwing to prevent UI from breaking
    return { users: [], total: 0 };
  }
};

export const getUserByEmailAdmin = async (email: string) => {
  try {
    const response = await api.get(`/admin/users/${email}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get user by email admin error:");
  }
};

export const updateUserByAdmin = async (kindeId: string, userData: unknown) => {
  try {
    debug("Updating user with ID:", kindeId);
    debug("Update data:", userData);

    const response = await api.put(`/admin/users/${kindeId}`, userData);
    return response.data;
  } catch (error) {
    handleApiError(error, "Update user by admin error:");
  }
};

export const getUserProjectsByAdmin = async (email: string) => {
  try {
    const response = await api.get(`/admin/users/${email}/projects`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get user projects by admin error:");
  }
};

export const getProjectByAdmin = async (projectId: string) => {
  try {
    const response = await api.get(`/admin/projects/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get project by admin error:");
  }
};

export const updateProjectByAdmin = async (projectId: string, projectData: unknown) => {
  try {
    const response = await api.put(`/admin/projects/${projectId}`, projectData);
    return response.data;
  } catch (error) {
    handleApiError(error, "Update project by admin error:");
  }
};

export const updateDeploymentByAdmin = async (projectId: string, deploymentData: unknown) => {
  try {
    const response = await api.put(`/admin/projects/${projectId}/deployment`, deploymentData);
    return response.data;
  } catch (error) {
    handleApiError(error, "Update deployment by admin error:");
  }
};

export const getBillingDetailsByAdmin = async (customerId: string) => {
  try {
    const response = await api.get(`/admin/billing/customer/${customerId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get billing details by admin error:");
  }
};

export const downloadStripeAnalyticsByAdmin = async () => {
  try {
    // This will return a blob for download
    const response = await api.get("/admin/billing/analytics/download", {
      responseType: "blob",
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Download stripe analytics by admin error:");
  }
};

export const authorizeSupabase = async (projectId: string) => {
  try {
    const response = await api.post("/supabase/authorize", { project_id: projectId });
    return response.data;
  } catch (error) {
    handleApiError(error, "Supabase authorization error:");
  }
};

export const deleteSupabaseOrganization = async (organizationId: string) => {
  try {
    const response = await api.delete(`/supabase/organizations/${organizationId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Delete Supabase organization error:");
  }
};

export const getSupabaseOrganizations = async (projectId?: string | null) => {
  try {
    const params = projectId ? { project_id: projectId } : {};
    const response = await api.get("/supabase/organizations", { params });
    return response.data;
  } catch (error: unknown) {
    console.error(
      "Get Supabase organizations error:",
      error instanceof Error
        ? error.message
        : // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (error as any)?.response?.data || "Unknown error",
    );
    // Return a structured error response instead of throwing
    return { success: false, organizations: [] };
  }
};

export const getSupabaseProjects = async (projectId: string, organizationId: string) => {
  try {
    const response = await api.get(`/supabase/organization/${organizationId}/projects`);
    return response.data;
  } catch (error: unknown) {
    console.error(
      "Get Supabase projects error:",
      error instanceof Error
        ? error.message
        : // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (error as any)?.response?.data || "Unknown error",
    );
    // Return a structured error response instead of throwing
    return { success: false, projects: [] };
  }
};

export const testSqlQuery = async (projectId: string, query: string) => {
  try {
    const response = await api.post(`/supabase/project/${projectId}/sql`, { query });
    return response.data;
  } catch (error) {
    handleApiError(error, "Test SQL query error:");
  }
};

export const connectSupabaseProject = async (
  projectId: string,
  supabaseProjectId: string,
  organizationId: string,
  organizationName: string,
  accessToken: string | null,
  refreshToken: string | null,
  apiKey: string,
  apiUrl: string,
  databasePassword: string | null,
) => {
  try {
    const response = await api.post("/supabase/project/connect", {
      project_id: projectId,
      supabase_project_id: supabaseProjectId,
      organization_id: organizationId,
      organization_name: organizationName,
      access_token: accessToken,
      refresh_token: refreshToken,
      api_key: apiKey,
      api_url: apiUrl,
      database_password: databasePassword,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Connect Supabase project error:");
  }
};

export const disconnectSupabase = async (projectId: string) => {
  try {
    const response = await api.delete(`/supabase/disconnect/${projectId}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Disconnect Supabase error:");
  }
};

export const deleteUserByAdmin = async (email: string, confirm: boolean = false) => {
  try {
    const response = await api.post("/admin/users/delete", {
      email,
      confirm,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Delete user error:");
  }
};

export const updateProjectPrompts = async (
  projectId: string,
  promptsArray: Array<{ key: string; value: string }>,
) => {
  try {
    const response = await api.post(`/project/${projectId}/prompts`, {
      prompts_array: promptsArray,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Update project prompts error:");
  }
};

export const getProjectPrompts = async (projectId: string) => {
  try {
    const response = await api.get(`/project/${projectId}/prompts`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get project prompts error:");
  }
};

export const deleteProjectPrompt = async (projectId: string, promptKey: string) => {
  try {
    const response = await api.delete(`/project/${projectId}/prompts/${promptKey}`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Delete project prompt error:");
  }
};

export const updateProjectPrompt = async (
  projectId: string,
  promptKey: string,
  promptValue: string,
) => {
  try {
    const response = await api.put(`/project/${projectId}/prompts/${promptKey}`, {
      key: promptKey,
      value: promptValue,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Update project prompt error:");
  }
};

export const connectVercelDeployWebSocket = ({
  projectId,
  vercelToken,
  startStep,
}: {
  projectId: string;
  vercelToken: string | null;
  startStep: number;
}) => {
  try {
    const token = getToken();
    const wsUrl = `${API_BASE_URL.replace("http", "ws")}/vercel-deploy?token=${token}&project_id=${projectId}&start_step=${startStep}${vercelToken ? `&vercel_token=${vercelToken}` : ""}`;
    const socket = new WebSocket(wsUrl);

    socket.onopen = () => {
      debug("Vercel deployment WebSocket connection established");
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        return data;
      } catch (error) {
        console.error("Error handling WebSocket message:", error);
        throw error;
      }
    };

    socket.onerror = (error) => {
      console.error("Vercel deployment WebSocket error:", error);
    };

    socket.onclose = (event) => {
      debug(`Vercel deployment WebSocket closed: ${event.code} ${event.reason}`);
    };

    return socket;
  } catch (error) {
    handleApiError(error, "Vercel deployment WebSocket connection error:");
    throw error;
  }
};

export const getOpenFiles = async (project_id: string) => {
  try {
    const response = await api.get(`/get-open-files/${project_id}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching open files:", error);
    throw error;
  }
};

export const openFilesInEditor = async (project_id: string, files: string[]) => {
  try {
    const response = await api.post(`/open-files-in-editor/${project_id}`, { files });
    return response.data;
  } catch (error) {
    console.error("Error opening files:", error);
    throw error;
  }
};

export const closeFilesInEditor = async (project_id: string, files: string[]) => {
  try {
    const response = await api.post(`/close-files-in-editor/${project_id}`, { files });
    return response.data;
  } catch (error) {
    console.error("Error closing files:", error);
    throw error;
  }
};

export const connectRemixProjectWebSocket = ({
  projectId,
  envValues = null,
}: {
  projectId: string;
  envValues: Record<string, string> | null;
}) => {
  try {
    const token = getToken();
    let wsUrl = `${API_BASE_URL.replace("http", "ws")}/project/${projectId}/remix?token=${token}`;

    // Add env_values as query parameter if provided
    if (envValues) {
      const envValuesString = JSON.stringify(envValues);
      wsUrl += `&env_values=${encodeURIComponent(envValuesString)}`;
    }

    const socket = new WebSocket(wsUrl);

    socket.onopen = () => {
      console.log("Remix project WebSocket connection established");
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        return data;
      } catch (error) {
        console.error("Error handling WebSocket message:", error);
        throw error;
      }
    };

    socket.onerror = (error) => {
      console.error("Remix project WebSocket error:", error);
    };

    socket.onclose = (event) => {
      console.log(`Remix project WebSocket closed: ${event.code} ${event.reason}`);
    };

    return socket;
  } catch (error) {
    handleApiError(error, "Remix project WebSocket connection error:");
    throw error;
  }
};

export const banUserByAdmin = async ({ email }: { email: string }) => {
  try {
    const response = await api.post("/admin/users/ban", {
      email,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Ban user error:");
  }
};

export const unbanUserByAdmin = async ({ email }: { email: string }) => {
  try {
    const response = await api.post("/admin/users/unban", {
      email,
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Unban user error:");
  }
};

export const getSupabaseProjectStatus = async ({
  supabaseProjectId,
}: {
  supabaseProjectId: string;
}) => {
  try {
    const response = await api.post("/supabase/project/status", {
      supabase_project_id: supabaseProjectId,
    });

    return response.data;
  } catch (error) {
    handleApiError(error, "Get Supabase project status error:");
  }
};

export const restoreSupabaseProject = async ({
  supabaseProjectId,
}: {
  supabaseProjectId: string;
}) => {
  try {
    const response = await api.post("/supabase/project/restore", {
      supabase_project_id: supabaseProjectId,
    });

    return response.data;
  } catch (error) {
    handleApiError(error, "Restore Supabase project error:");
  }
};

// Missing Admin Functions for parity with sg-web
export const searchProjectByAdmin = async (searchTerm: string) => {
  try {
    const response = await api.get(`/admin/projects/search`, {
      params: { search_term: searchTerm },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Search project by admin error:");
  }
};

export const getUserTransactionsByAdmin = async (email: string) => {
  try {
    const response = await api.get(`/admin/users/${email}/transactions`);
    return response.data;
  } catch (error) {
    handleApiError(error, "Get user transactions by admin error:");
  }
};

// GitHub Clone WebSocket connection for admin functionality
export const connectGitHubCloneWebSocket = (projectId: string) => {
  try {
    const token = getToken();
    const wsUrl = `${API_BASE_URL.replace("http", "ws")}/admin/github-clone/${projectId}?token=${token}`;
    const socket = new WebSocket(wsUrl);

    socket.onopen = () => {
      console.log("GitHub clone WebSocket connection established");
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        return data;
      } catch (error) {
        console.error("Error handling GitHub clone WebSocket message:", error);
        throw error;
      }
    };

    socket.onerror = (error) => {
      console.error("GitHub clone WebSocket error:", error);
    };

    socket.onclose = (event) => {
      console.log(`GitHub clone WebSocket closed: ${event.code} ${event.reason}`);
    };

    return socket;
  } catch (error) {
    handleApiError(error, "GitHub clone WebSocket connection error:");
    throw error;
  }
};

// ----------------- WALLET ROUTES -----------------

export const getWallet = async () => {
  try {
    const response = await api.get("/wallet");
    return response.data;
  } catch (error) {
    handleApiError(error, "Get wallet error:");
  }
};

export const getWalletTransactions = async ({ pageParam = undefined }: { pageParam?: number }) => {
  try {
    const response = await api.get("/wallet/transactions", {
      params: {
        cursor: pageParam,
        limit: 10,
      },
    });
    return response.data;
  } catch (error) {
    handleApiError(error, "Get wallet transactions error:");
  }
};

export const topUpWallet = async (amount: number) => {
  try {
    const response = await api.post("/wallet/topup", { amount });
    return response.data;
  } catch (error) {
    handleApiError(error, "Top up wallet error:");
  }
};

export default api;
