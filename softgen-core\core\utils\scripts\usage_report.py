# usage_report.py

from datetime import datetime
import stripe
import json
from core.db import Database
from core.config import settings
import logging
from typing import Dict, List
from sqlalchemy import select
from core.models import User

stripe.api_key = settings.stripe_api_key
db = Database()

class UsageReport:
    def __init__(self):
        self.db = Database()
        self.report_data = []
        self.total_stats = {
            "total_users": 0,
            "active_users": 0,
            "total_usage": 0,
            "total_base_cost": 0,
            "total_usage_cost": 0,
            "total_cost": 0,
            "users_by_plan": {},
            "revenue_by_plan": {}
        }

    async def get_all_users(self):
        """Get all users from the database"""
        async with self.db.get_async_session() as session:
            query = select(User)
            result = await session.execute(query)
            return result.scalars().all()

    async def calculate_user_usage(self, user) -> Dict:
        """Calculate usage and billing for a single user"""
        try:
            if not user.stripe_customer_id:
                return {
                    "user_id": user.id,
                    "email": user.email,
                    "status": "no_billing_info",
                    "error": "Missing stripe customer ID or token event name"
                }

            # Get customer from Stripe
            customer = stripe.Customer.retrieve(
                user.stripe_customer_id,
                expand=['subscriptions']
            )

            # Check for active subscription
            if not customer.subscriptions or not customer.subscriptions.data:
                return {
                    "user_id": user.id,
                    "email": user.email,
                    "status": "no_active_subscription"
                }

            # Get active subscription
            subscription = customer.subscriptions.data[0]
            
            # Get upcoming invoice
            try:
                upcoming_invoice = stripe.Invoice.upcoming(
                    customer=user.stripe_customer_id,
                    subscription=subscription.id
                )

                # Find usage-based line items
                usage_items = [
                    item for item in upcoming_invoice.lines.data 
                    if item.price.recurring and item.price.recurring.usage_type == 'metered'
                ]

                # Calculate total usage and cost
                total_usage = sum(item.quantity for item in usage_items)
                usage_cost = sum(item.amount for item in usage_items) / 100  # Convert cents to dollars

                # Get base subscription cost
                base_items = [
                    item for item in upcoming_invoice.lines.data 
                    if not (item.price.recurring and item.price.recurring.usage_type == 'metered')
                ]
                base_cost = sum(item.amount for item in base_items) / 100
                total_cost = usage_cost + base_cost

                # Update total statistics
                self.total_stats["total_usage"] += total_usage
                self.total_stats["total_base_cost"] += base_cost
                self.total_stats["total_usage_cost"] += usage_cost
                self.total_stats["total_cost"] += total_cost

                # Update plan statistics
                plan = user.plan or "unknown"
                self.total_stats["users_by_plan"][plan] = self.total_stats["users_by_plan"].get(plan, 0) + 1
                self.total_stats["revenue_by_plan"][plan] = self.total_stats["revenue_by_plan"].get(plan, 0) + total_cost

                return {
                    "user_id": user.id,
                    "email": user.email,
                    "stripe_customer_id": user.stripe_customer_id,
                    "plan": user.plan,
                    "subscription_id": subscription.id,
                    "subscription_status": subscription.status,
                    "current_period_start": datetime.fromtimestamp(subscription.current_period_start).isoformat(),
                    "current_period_end": datetime.fromtimestamp(subscription.current_period_end).isoformat(),
                    "total_usage": total_usage,
                    "usage_cost": usage_cost,
                    "base_cost": base_cost,
                    "total_cost": total_cost,
                    "currency": upcoming_invoice.currency,
                    "status": "active"
                }

            except stripe.error.InvalidRequestError:
                return {
                    "user_id": user.id,
                    "email": user.email,
                    "status": "no_upcoming_invoice",
                    "error": "No upcoming invoice found"
                }

        except Exception as e:
            logging.error(f"Error calculating usage for user {user.email}: {str(e)}")
            return {
                "user_id": user.id,
                "email": user.email,
                "status": "error",
                "error": str(e)
            }

    def print_summary(self, summary):
        """Print a formatted summary of the usage report"""
        print("\n=== Usage Report Summary ===")
        print(f"Total Users: {summary['total_users']}")
        print(f"Active Users: {summary['active_users']}")
        print("\nUsage Statistics:")
        print(f"Total Token Usage: {summary['total_usage']:,}")
        print("\nCost Breakdown:")
        print(f"Total Base Cost: ${summary['total_base_cost']:,.2f}")
        print(f"Total Usage Cost: ${summary['total_usage_cost']:,.2f}")
        print(f"Total Revenue: ${summary['total_cost']:,.2f}")
        
        print("\nUsers by Plan:")
        for plan, count in summary['users_by_plan'].items():
            revenue = summary['revenue_by_plan'].get(plan, 0)
            print(f"{plan}: {count} users (${revenue:,.2f} revenue)")

        print(f"\nReport saved to: {summary['report_file']}")

    async def generate_usage_report(self):
        """Generate usage report for all users"""
        try:
            # Get all users using the direct database query
            users = await self.get_all_users()
            self.total_stats["total_users"] = len(users)
            
            for user in users:
                usage_data = await self.calculate_user_usage(user)
                self.report_data.append(usage_data)
                if usage_data.get("status") == "active":
                    self.total_stats["active_users"] += 1

            # Save report to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"usage_report_{timestamp}.json"
            
            report_data = {
                "generated_at": datetime.now().isoformat(),
                "summary": self.total_stats,
                "users": self.report_data
            }

            with open(filename, 'w') as f:
                json.dump(report_data, f, indent=2)

            self.total_stats["report_file"] = filename
            return self.total_stats

        except Exception as e:
            logging.error(f"Error generating usage report: {str(e)}")
            raise

    async def get_high_usage_alerts(self, threshold_percentage: float = 80.0) -> List[Dict]:
        """Get alerts for users approaching their plan limits"""
        alerts = []
        
        for usage_data in self.report_data:
            if usage_data.get("status") != "active":
                continue

            plan = usage_data.get("plan")
            total_usage = usage_data.get("total_usage", 0)

            # Get included tokens based on plan
            included_tokens = {
                "launch": 3_000_000,
                "elite": 7_000_000,
                "business": 16_000_000
            }.get(plan, 0)

            if included_tokens > 0:
                usage_percentage = (total_usage / included_tokens) * 100
                if usage_percentage >= threshold_percentage:
                    alerts.append({
                        "user_id": usage_data["user_id"],
                        "email": usage_data["email"],
                        "plan": plan,
                        "usage_percentage": usage_percentage,
                        "total_usage": total_usage,
                        "included_tokens": included_tokens
                    })

        return alerts

async def main():
    try:
        report_generator = UsageReport()
        summary = await report_generator.generate_usage_report()
        
        # Print formatted summary
        report_generator.print_summary(summary)

        # Get high usage alerts
        alerts = await report_generator.get_high_usage_alerts(threshold_percentage=80.0)
        if alerts:
            print("\n=== High Usage Alerts ===")
            for alert in alerts:
                print(f"User {alert['email']} has used {alert['usage_percentage']:.1f}% of their {alert['plan']} plan limit")

        # Close the database connection
        await report_generator.db.close()

    except Exception as e:
        print(f"Error running usage report: {str(e)}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())