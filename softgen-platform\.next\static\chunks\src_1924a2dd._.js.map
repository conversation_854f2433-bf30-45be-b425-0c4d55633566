{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white  hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n        \"outline-primary\":\r\n          \"border border-primary/20 dark:border-primary/15 bg-accent/80 hover:bg-accent/90 text-primary/80 hover:text-primary\",\r\n        success:\r\n          \"border border-emerald-800 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-800\",\r\n        info: \"border border-blue-800 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800\",\r\n        invert:\r\n          \"w-fit dark:bg-[#0a0a0a] dark:hover:bg-[#0a0a0a]/95 dark:text-[#fafafa] bg-[#fafafa] hover:bg-[#fafafa]/95 text-[#0a0a0a]\",\r\n        \"invert-outline-primary\":\r\n          \"border border-background/10 bg-primary/90 hover:bg-primary text-background hover:text-background\",\r\n        transparent: \"bg-transparent hover:bg-transparent text-primary\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-lg px-6 has-[>svg]:px-4\",\r\n        icon: \"size-8\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;YACN,mBACE;YACF,SACE;YACF,MAAM;YACN,QACE;YACF,0BACE;YACF,aAAa;QACf;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\ntype Props = {\r\n  className?: string;\r\n};\r\n\r\nconst Loading = ({ className }: Props) => {\r\n  return (\r\n    <svg\r\n      className={cn(\"size-5 animate-spin text-primary\", className)}\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill=\"none\"\r\n      viewBox=\"0 0 24 24\"\r\n    >\r\n      <circle\r\n        className=\"opacity-25\"\r\n        cx=\"12\"\r\n        cy=\"12\"\r\n        r=\"10\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"4\"\r\n      ></circle>\r\n      <path\r\n        className=\"opacity-75\"\r\n        fill=\"currentColor\"\r\n        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n      ></path>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default Loading;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAMA,MAAM,UAAU,CAAC,EAAE,SAAS,EAAS;IACnC,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAClD,OAAM;QACN,MAAK;QACL,SAAQ;;0BAER,4SAAC;gBACC,WAAU;gBACV,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,4SAAC;gBACC,WAAU;gBACV,MAAK;gBACL,GAAE;;;;;;;;;;;;AAIV;KAvBM;uCAyBS", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/modal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { X } from \"@mynaui/icons-react\";\r\nimport { Suspense, useEffect, useState } from \"react\";\r\nimport { Button } from \"./button\";\r\nimport Loading from \"./loading\";\r\n\r\nconst Modal = ({ onOpenChange, ...props }: DialogPrimitive.DialogProps) => {\r\n  return <DialogPrimitive.Root onOpenChange={onOpenChange} {...props} />;\r\n};\r\n\r\nconst ModalTrigger = DialogPrimitive.Trigger;\r\n\r\nconst ModalClose = DialogPrimitive.Close;\r\n\r\nconst ModalPortal = DialogPrimitive.Portal;\r\n\r\nconst ModalOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-overlay/90 backdrop-blur-[1px] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n));\r\nModalOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst ModalVariants = cva(\r\n  cn(\r\n    \"fixed z-50 gap-0 bg-background border border-primary/10 p-0 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 overflow-y-auto\",\r\n    \"lg:left-[50%] lg:top-[50%] lg:grid lg:w-full lg:max-w-lg lg:translate-x-[-50%] lg:translate-y-[-50%] lg:border lg:duration-200 lg:data-[state=open]:animate-in lg:data-[state=closed]:animate-out lg:data-[state=closed]:fade-out-0 lg:data-[state=open]:fade-in-0 lg:data-[state=closed]:zoom-out-95 lg:data-[state=open]:zoom-in-95 lg:data-[state=closed]:slide-out-to-left-1/2 lg:data-[state=closed]:slide-out-to-top-[48%] lg:data-[state=open]:slide-in-from-left-1/2 lg:data-[state=open]:slide-in-from-top-[48%] lg:rounded-2xl\",\r\n  ),\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b rounded-b-2xl max-h-[90%] lg:h-fit data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 lg:bottom-auto border-t lg:h-auto max-h-[90%] rounded-t-2xl data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full lg:h-fit w-3/4 border-r rounded-r-2xl data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full lg:h-fit w-3/4 border-l rounded-l-2xl data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"bottom\",\r\n    },\r\n  },\r\n);\r\n\r\ninterface ModalContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>,\r\n    VariantProps<typeof ModalVariants> {\r\n  closeClassName?: string;\r\n  modalClassName?: string;\r\n  border?: boolean;\r\n  showCloseButton?: boolean;\r\n  closeOnOutsideClick?: boolean;\r\n}\r\n\r\nconst ModalContentInner = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  ModalContentProps\r\n>(\r\n  (\r\n    {\r\n      side = \"bottom\",\r\n      className,\r\n      modalClassName,\r\n      closeClassName,\r\n      children,\r\n      border = false,\r\n      showCloseButton = true,\r\n      closeOnOutsideClick = true,\r\n      ...props\r\n    },\r\n    ref,\r\n  ) => (\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        ModalVariants({ side, className: modalClassName }),\r\n        className,\r\n        \"rounded-2xl rounded-b-none lg:rounded-b-2xl\",\r\n      )}\r\n      onPointerDownOutside={closeOnOutsideClick ? undefined : (e) => e.preventDefault()}\r\n      {...props}\r\n    >\r\n      {children}\r\n\r\n      {showCloseButton && (\r\n        <ModalClose asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            className={cn(\r\n              \"absolute right-4 top-4 h-8 w-8 rounded-xl p-0 text-xs font-semibold text-primary transition-colors hover:text-primary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n              closeClassName,\r\n            )}\r\n            tabIndex={-1}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n            <span className=\"sr-only\">Close</span>\r\n          </Button>\r\n        </ModalClose>\r\n      )}\r\n\r\n      {border && (\r\n        <div className=\"pointer-events-none absolute inset-0 z-0 rounded-2xl rounded-b-none border-2 border-primary/20 lg:rounded-b-2xl\" />\r\n      )}\r\n    </DialogPrimitive.Content>\r\n  ),\r\n);\r\nModalContentInner.displayName = \"ModalContentInner\";\r\n\r\nconst ModalContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  ModalContentProps\r\n>((props, ref) => (\r\n  <ModalPortal>\r\n    <ModalOverlay />\r\n    <ModalContentInner {...props} ref={ref} />\r\n  </ModalPortal>\r\n));\r\nModalContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst ModalHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div className={cn(\"flex flex-col space-y-2 text-left\", \"px-6 pt-5\", className)} {...props} />\r\n);\r\nModalHeader.displayName = \"ModalHeader\";\r\n\r\nconst ModalFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse items-center gap-y-2 rounded-b-none px-6 pb-5 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-2 md:rounded-b-2xl md:px-6 lg:rounded-b-2xl\",\r\n\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nModalFooter.displayName = \"ModalFooter\";\r\n\r\nconst ModalTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-base font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nModalTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst ModalDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nModalDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nconst ModalLoadingContent = () => {\r\n  return (\r\n    <ModalContentInner className=\"flex min-h-[300px] items-center justify-center\" autoFocus={false}>\r\n      <div className=\"flex flex-col items-center gap-4\">\r\n        <Loading className=\"h-12 w-12\" />\r\n        <p className=\"text-muted-foreground\">Loading content...</p>\r\n      </div>\r\n    </ModalContentInner>\r\n  );\r\n};\r\n\r\n// TODO: implement passing props directly to ModalContent\r\n// NOTE: consider moving portal+overlay inside Suspense\r\nconst LazyModal = ({\r\n  children,\r\n  open,\r\n  forceMount,\r\n  ...props\r\n}: DialogPrimitive.DialogProps & { forceMount?: boolean }) => {\r\n  const [hasOpened, setHasOpened] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (open) {\r\n      setHasOpened(true);\r\n    }\r\n  }, [open]);\r\n\r\n  if (!hasOpened && !forceMount) return null;\r\n\r\n  return (\r\n    <Modal open={open} {...props}>\r\n      <ModalPortal>\r\n        <ModalOverlay />\r\n        <Suspense fallback={<ModalLoadingContent />}>{children}</Suspense>\r\n      </ModalPortal>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport {\r\n  LazyModal,\r\n  Modal,\r\n  ModalClose,\r\n  ModalContent,\r\n  ModalContentInner,\r\n  ModalDescription,\r\n  ModalFooter,\r\n  ModalHeader,\r\n  ModalOverlay,\r\n  ModalPortal,\r\n  ModalTitle,\r\n  ModalTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;;;AAVA;;;;;;;;;AAYA,MAAM,QAAQ,CAAC,EAAE,YAAY,EAAE,GAAG,OAAoC;IACpE,qBAAO,4SAAC,kRAAA,CAAA,OAAoB;QAAC,cAAc;QAAe,GAAG,KAAK;;;;;;AACpE;KAFM;AAIN,MAAM,eAAe,kRAAA,CAAA,UAAuB;AAE5C,MAAM,aAAa,kRAAA,CAAA,QAAqB;AAExC,MAAM,cAAc,kRAAA,CAAA,SAAsB;AAE1C,MAAM,6BAAe,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,UAAuB;QACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gLACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;MAVH;AAaN,aAAa,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE9D,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACC,6OACA,6gBAEF;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAaF,MAAM,kCAAoB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAIvC,CACE,EACE,OAAO,QAAQ,EACf,SAAS,EACT,cAAc,EACd,cAAc,EACd,QAAQ,EACR,SAAS,KAAK,EACd,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EAC1B,GAAG,OACJ,EACD,oBAEA,4SAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;YAAE;YAAM,WAAW;QAAe,IAChD,WACA;QAEF,sBAAsB,sBAAsB,YAAY,CAAC,IAAM,EAAE,cAAc;QAC9E,GAAG,KAAK;;YAER;YAEA,iCACC,4SAAC;gBAAW,OAAO;0BACjB,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6LACA;oBAEF,UAAU,CAAC;;sCAEX,4SAAC,uSAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;sCACb,4SAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAK/B,wBACC,4SAAC;gBAAI,WAAU;;;;;;;;;;;;MA/CjB;AAoDN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,6BAAe,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGlC,CAAC,OAAO,oBACR,4SAAC;;0BACC,4SAAC;;;;;0BACD,4SAAC;gBAAmB,GAAG,KAAK;gBAAE,KAAK;;;;;;;;;;;;;AAGvC,aAAa,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE9D,MAAM,cAAc,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBAChF,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC,aAAa;QAAa,GAAG,KAAK;;;;;;MADtF;AAGN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBAChF,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oKAEA;QAED,GAAG,KAAK;;;;;;MAPP;AAUN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,kRAAA,CAAA,cAA2B,CAAC,WAAW;AAEtE,MAAM,sBAAsB;IAC1B,qBACE,4SAAC;QAAkB,WAAU;QAAiD,WAAW;kBACvF,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC,sIAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,4SAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAI7C;OATM;AAWN,yDAAyD;AACzD,uDAAuD;AACvD,MAAM,YAAY,CAAC,EACjB,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,GAAG,OACoD;;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR,aAAa;YACf;QACF;8BAAG;QAAC;KAAK;IAET,IAAI,CAAC,aAAa,CAAC,YAAY,OAAO;IAEtC,qBACE,4SAAC;QAAM,MAAM;QAAO,GAAG,KAAK;kBAC1B,cAAA,4SAAC;;8BACC,4SAAC;;;;;8BACD,4SAAC,4QAAA,CAAA,WAAQ;oBAAC,wBAAU,4SAAC;;;;;8BAAyB;;;;;;;;;;;;;;;;;AAItD;GAxBM;OAAA", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & { border?: boolean }\r\n>(({ className, border = true, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-30 rounded-xl bg-card text-card-foreground shadow-sm\",\r\n      border && \"relative\",\r\n      \"border border-primary/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {border && (\r\n      <div className=\"pointer-events-none absolute inset-0 z-0 rounded-md ring-[1.2px] ring-inset ring-primary/20\" />\r\n    )}\r\n    {props.children}\r\n  </div>\r\n));\r\nCard.displayName = \"Card\";\r\n\r\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => (\r\n    <div\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex flex-col space-y-1.5 p-6\",\r\n        \"border-b border-primary/10 px-6 py-5\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  ),\r\n);\r\nCardHeader.displayName = \"CardHeader\";\r\n\r\nconst CardTitle = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => (\r\n    <div\r\n      ref={ref}\r\n      className={cn(\"text-2xl font-semibold leading-none tracking-tight\", className)}\r\n      {...props}\r\n    />\r\n  ),\r\n);\r\nCardTitle.displayName = \"CardTitle\";\r\n\r\nconst CardDescription = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => (\r\n    <div ref={ref} className={cn(\"text-sm text-muted-foreground\", className)} {...props} />\r\n  ),\r\n);\r\nCardDescription.displayName = \"CardDescription\";\r\n\r\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => (\r\n    <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n  ),\r\n);\r\nCardContent.displayName = \"CardContent\";\r\n\r\nconst CardSeparator = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => (\r\n    <div ref={ref} className={cn(\"-mx-6 h-px bg-muted\", className)} {...props} />\r\n  ),\r\n);\r\nCardSeparator.displayName = \"CardSeparator\";\r\n\r\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => (\r\n    <div\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex items-center rounded-b-xl border-t border-primary/10 bg-sidebar-accent/60 px-6 py-4 dark:bg-sidebar-accent/15\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  ),\r\n);\r\nCardFooter.displayName = \"CardFooter\";\r\n\r\nexport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardSeparator, CardTitle };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE,GAAG,OAAO,EAAE,oBACzC,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA,UAAU,YACV,0GACA;QAED,GAAG,KAAK;;YAER,wBACC,4SAAC;gBAAI,WAAU;;;;;;YAEhB,MAAM,QAAQ;;;;;;;;AAGnB,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAChC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iCACA,wCACA;QAED,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QACrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,4SAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAGvF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QACjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,4SAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,8BAAgB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,SACnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,4SAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QAAa,GAAG,KAAK;;;;;;;AAG7E,cAAc,WAAW,GAAG;AAE5B,MAAM,2BAAa,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,SAChC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sHACA;QAED,GAAG,KAAK;;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/banned-user.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\r\nimport { DangerTriangle } from \"@mynaui/icons-react\";\r\n\r\nconst BannedUser = () => {\r\n  return (\r\n    <main className=\"flex min-h-screen flex-col items-center justify-center\">\r\n      <div className=\"mt-10 flex h-[85vh] w-full max-w-md flex-col items-center justify-center rounded-xl p-4\">\r\n        <div className=\"flex flex-col items-start gap-6\">\r\n          <div className=\"flex flex-col items-start gap-4\">\r\n            <DangerTriangle className=\"size-12 text-destructive\" />\r\n\r\n            <CardHeader className=\"space-y-3 border-b-0 p-0\">\r\n              <CardTitle className=\"text-3xl font-bold text-destructive\">Account Banned</CardTitle>\r\n              <CardDescription className=\"text-base\">\r\n                Your account has been banned from accessing the platform.\r\n              </CardDescription>\r\n            </CardHeader>\r\n          </div>\r\n          <div className=\"w-full space-y-6\">\r\n            <Button\r\n              variant=\"destructive\"\r\n              size=\"lg\"\r\n              className=\"w-full\"\r\n              onClick={() =>\r\n                window.open(\"https://softgen.zendesk.com/hc/en-us/requests/new\", \"_blank\")\r\n              }\r\n            >\r\n              Contact Support\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"mx-auto my-10 mt-auto w-full max-w-md\">\r\n        <p className=\"text-balance text-center text-sm text-muted-foreground\">\r\n          If you believe this is an error or would like to appeal this decision, please contact our\r\n          support team for assistance.\r\n        </p>\r\n      </div>\r\n    </main>\r\n  );\r\n};\r\n\r\nexport default BannedUser;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,aAAa;IACjB,qBACE,4SAAC;QAAK,WAAU;;0BACd,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,iUAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;8CAE1B,4SAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,4SAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsC;;;;;;sDAC3D,4SAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAY;;;;;;;;;;;;;;;;;;sCAK3C,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IACP,OAAO,IAAI,CAAC,qDAAqD;0CAEpE;;;;;;;;;;;;;;;;;;;;;;0BAOP,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAE,WAAU;8BAAyD;;;;;;;;;;;;;;;;;AAO9E;KAtCM;uCAwCS", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined);\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    };\r\n    mql.addEventListener(\"change\", onChange);\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    return () => mql.removeEventListener(\"change\", onChange);\r\n  }, []);\r\n\r\n  return !!isMobile;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarFallback, AvatarImage };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,kRAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Check, ChevronRight, Circle } from \"@mynaui/icons-react\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-[999] min-w-[8rem] overflow-hidden rounded-md border bg-background p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-[200] min-w-[14rem] overflow-hidden rounded-2xl border border-primary/10 bg-background p-2 text-popover-foreground hover:text-primary data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-[0.4rem] px-2 py-1.5 text-sm outline-none transition-colors focus:bg-foreground/10 focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      \"w-full items-center justify-between gap-2 text-sm font-normal text-primary/80 transition-all duration-500 hover:bg-primary/10 hover:text-primary\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold tracking-normal\", inset && \"pl-8\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-primary/10\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)} {...props} />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuPortal,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AANA;;;;;;AAQA,MAAM,eAAe,sRAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,sRAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,sRAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,sRAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,sRAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,sRAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,4SAAC,sRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,4SAAC,6TAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,sRAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,sRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+bACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAAG,sRAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,4SAAC,sRAAA,CAAA,SAA4B;kBAC3B,cAAA,4SAAC,sRAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4dACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,sRAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,4SAAC,sRAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA,oJACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,sRAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,4SAAC,sRAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8NACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,4SAAC;gBAAK,WAAU;0BACd,cAAA,4SAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,4SAAC,+SAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAAG,sRAAA,CAAA,eAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4SAAC,sRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8NACA;QAED,GAAG,KAAK;;0BAET,4SAAC;gBAAK,WAAU;0BACd,cAAA,4SAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,4SAAC,iTAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,sRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,4SAAC,sRAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD,SAAS,QAAQ;QACnF,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sRAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,sRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,sRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAAE,SAAS,EAAE,GAAG,OAA8C;IAC1F,qBACE,4SAAC;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAAa,GAAG,KAAK;;;;;;AAE3F;OAJM;AAKN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n);\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> & VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root ref={ref} className={cn(labelVariants(), className)} {...props} />\r\n));\r\nLabel.displayName = LabelPrimitive.Root.displayName;\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,iRAAA,CAAA,OAAmB;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAAa,GAAG,KAAK;;;;;;;AAErF,MAAM,WAAW,GAAG,iRAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1055, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-token-info.ts"], "sourcesContent": ["import { getTokenUsage } from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { format } from \"date-fns\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\n\r\ninterface TokenUsage {\r\n  total_usage: number;\r\n  period_start?: number;\r\n  period_end?: number;\r\n}\r\n\r\nexport const useTokenInfo = () => {\r\n  const [tokenUsage, setTokenUsage] = useState<TokenUsage | null>(null);\r\n  const [isExcessUsageModalOpen, setIsExcessUsageModalOpen] = useState(false);\r\n  const [excessUsageMessage, setExcessUsageMessage] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const { user } = useAuth();\r\n\r\n  const fetchTokenUsage = useCallback(async () => {\r\n    if (!user?.userFromDb?.stripe_customer_id || !user?.userFromDb?.token_event_name) {\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const usage = await getTokenUsage(\r\n        user.userFromDb.stripe_customer_id,\r\n        user.userFromDb.token_event_name,\r\n      );\r\n      setTokenUsage(usage);\r\n    } catch (error) {\r\n      console.error(\"Error fetching token usage:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [user?.userFromDb?.stripe_customer_id, user?.userFromDb?.token_event_name]);\r\n\r\n  useEffect(() => {\r\n    fetchTokenUsage();\r\n    const intervalId = setInterval(fetchTokenUsage, 30000);\r\n    return () => clearInterval(intervalId);\r\n  }, [fetchTokenUsage]);\r\n\r\n  const formatTokens = (tokens: number) => {\r\n    if (tokens >= 1000000) {\r\n      return `${(tokens / 1000000).toFixed(2)}M`;\r\n    } else {\r\n      return `${Math.round(tokens / 1000)}k`;\r\n    }\r\n  };\r\n\r\n  const getPlanTokens = () => {\r\n    const plan = user?.userFromDb?.plan?.toLowerCase() || \"launch\";\r\n    switch (plan) {\r\n      case \"launch\":\r\n        return 3;\r\n      case \"elite\":\r\n        return 7;\r\n      case \"business\":\r\n        return 16;\r\n      case \"entry\":\r\n        return 2.5;\r\n      case \"boost\":\r\n        return 6;\r\n      case \"fly\":\r\n        return 14;\r\n      case \"pro_enterprise\":\r\n        return 30;\r\n      case \"elite_enterprise\":\r\n        return 85;\r\n      case \"free-tier\":\r\n        return 0.25;\r\n      default:\r\n        return 3;\r\n    }\r\n  };\r\n\r\n  const getRemainingTokens = () => {\r\n    return user?.userFromDb?.free_total_token || 0;\r\n  };\r\n\r\n  const getTokenDisplayInfo = () => {\r\n    const remaining = getRemainingTokens();\r\n    const total = getPlanTokens() * 1000000; // Convert to tokens\r\n    const isExcess = tokenUsage?.total_usage && tokenUsage.total_usage > 0;\r\n    const usedTokens = tokenUsage?.total_usage || 0;\r\n    const effectiveTotal = Math.max(remaining, total);\r\n\r\n    return {\r\n      usedText: usedTokens > 0 ? formatTokens(usedTokens) : \"\",\r\n      remainingTokens: formatTokens(remaining),\r\n      effectiveTotalTokens: formatTokens(effectiveTotal),\r\n      remainingText: `${formatTokens(remaining)}/${formatTokens(effectiveTotal)}`,\r\n      isWarning: isExcess,\r\n      tooltipContent: isExcess\r\n        ? `You've used ${formatTokens(usedTokens)} tokens this billing period. Excess usage charges may apply.`\r\n        : `You have ${formatTokens(remaining)} tokens remaining out of ${formatTokens(effectiveTotal)} tokens.`,\r\n      totalUsage: usedTokens,\r\n      remaining,\r\n      total: total / 1000000, // Keep in millions for calculations\r\n    };\r\n  };\r\n\r\n  const handleExcessUsageClick = () => {\r\n    const info = getTokenDisplayInfo();\r\n    const periodStart = tokenUsage?.period_start\r\n      ? format(new Date(tokenUsage.period_start * 1000), \"MMMM dd, yyyy\")\r\n      : \"N/A\";\r\n    const periodEnd = tokenUsage?.period_end\r\n      ? format(new Date(tokenUsage.period_end * 1000), \"MMMM dd, yyyy\")\r\n      : \"N/A\";\r\n    const userPlan = user?.userFromDb?.plan || \"launch\";\r\n    const isNewPlan = [\"entry\", \"boost\", \"fly\"].includes(userPlan.toLowerCase());\r\n\r\n    if (isNewPlan) {\r\n      const excessTokensIn100k = Math.ceil(info.totalUsage / 100000);\r\n      const message = `\r\n        You've exceeded your monthly token limit by ${formatTokens(info.totalUsage)} tokens.\r\n\r\n        Your next billing amount will be $${excessTokensIn100k} for the period ${periodStart} to ${periodEnd}.\r\n\r\n        Billing breakdown:\r\n        • Base plan includes ${formatTokens(info.total * 1000000)} tokens\r\n        • Additional usage is charged at $1 per 100k tokens\r\n        • You are using ${excessTokensIn100k} additional block(s) of 100k tokens\r\n      `;\r\n      setExcessUsageMessage(message);\r\n    } else {\r\n      const usageAmount =\r\n        {\r\n          launch: 25,\r\n          elite: 50,\r\n          business: 100,\r\n          entry: 25,\r\n          boost: 50,\r\n          fly: 100,\r\n          pro_enterprise: 200,\r\n          elite_enterprise: 500,\r\n          free_tier: 0.25,\r\n        }[userPlan.toLowerCase()] || 25;\r\n\r\n      const additionalBlocksUsed = Math.ceil(info.totalUsage / (info.total * 1000000));\r\n      const nextBillingAmount = usageAmount * additionalBlocksUsed;\r\n\r\n      const message = `\r\n        You've exceeded your monthly token limit by ${formatTokens(info.totalUsage)} tokens.\r\n\r\n        Your next billing amount will be $${nextBillingAmount} for the period ${periodStart} to ${periodEnd}.\r\n\r\n        Billing breakdown:\r\n        • Base plan includes ${formatTokens(info.total * 1000000)} tokens\r\n        • Additional usage is charged at $${usageAmount} per ${formatTokens(info.total * 1000000)} tokens\r\n        • You are using ${additionalBlocksUsed} additional block(s) of ${formatTokens(info.total * 1000000)} tokens\r\n      `;\r\n      setExcessUsageMessage(message);\r\n    }\r\n\r\n    setIsExcessUsageModalOpen(true);\r\n  };\r\n\r\n  return {\r\n    tokenUsage,\r\n    isLoading,\r\n    getTokenDisplayInfo,\r\n    formatTokens,\r\n    handleExcessUsageClick,\r\n    isExcessUsageModalOpen,\r\n    setIsExcessUsageModalOpen,\r\n    excessUsageMessage,\r\n    refreshTokenUsage: fetchTokenUsage,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;;AAQO,MAAM,eAAe;;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;qDAAE;YAClC,IAAI,CAAC,MAAM,YAAY,sBAAsB,CAAC,MAAM,YAAY,kBAAkB;gBAChF,aAAa;gBACb;YACF;YAEA,IAAI;gBACF,aAAa;gBACb,MAAM,QAAQ,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAC9B,KAAK,UAAU,CAAC,kBAAkB,EAClC,KAAK,UAAU,CAAC,gBAAgB;gBAElC,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,SAAU;gBACR,aAAa;YACf;QACF;oDAAG;QAAC,MAAM,YAAY;QAAoB,MAAM,YAAY;KAAiB;IAE7E,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;kCAAE;YACR;YACA,MAAM,aAAa,YAAY,iBAAiB;YAChD;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,SAAS;YACrB,OAAO,GAAG,CAAC,SAAS,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO;YACL,OAAO,GAAG,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC;QACxC;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,OAAO,MAAM,YAAY,MAAM,iBAAiB;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,MAAM,YAAY,oBAAoB;IAC/C;IAEA,MAAM,sBAAsB;QAC1B,MAAM,YAAY;QAClB,MAAM,QAAQ,kBAAkB,SAAS,oBAAoB;QAC7D,MAAM,WAAW,YAAY,eAAe,WAAW,WAAW,GAAG;QACrE,MAAM,aAAa,YAAY,eAAe;QAC9C,MAAM,iBAAiB,KAAK,GAAG,CAAC,WAAW;QAE3C,OAAO;YACL,UAAU,aAAa,IAAI,aAAa,cAAc;YACtD,iBAAiB,aAAa;YAC9B,sBAAsB,aAAa;YACnC,eAAe,GAAG,aAAa,WAAW,CAAC,EAAE,aAAa,iBAAiB;YAC3E,WAAW;YACX,gBAAgB,WACZ,CAAC,YAAY,EAAE,aAAa,YAAY,4DAA4D,CAAC,GACrG,CAAC,SAAS,EAAE,aAAa,WAAW,yBAAyB,EAAE,aAAa,gBAAgB,QAAQ,CAAC;YACzG,YAAY;YACZ;YACA,OAAO,QAAQ;QACjB;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,OAAO;QACb,MAAM,cAAc,YAAY,eAC5B,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,YAAY,GAAG,OAAO,mBACjD;QACJ,MAAM,YAAY,YAAY,aAC1B,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,UAAU,GAAG,OAAO,mBAC/C;QACJ,MAAM,WAAW,MAAM,YAAY,QAAQ;QAC3C,MAAM,YAAY;YAAC;YAAS;YAAS;SAAM,CAAC,QAAQ,CAAC,SAAS,WAAW;QAEzE,IAAI,WAAW;YACb,MAAM,qBAAqB,KAAK,IAAI,CAAC,KAAK,UAAU,GAAG;YACvD,MAAM,UAAU,CAAC;oDAC6B,EAAE,aAAa,KAAK,UAAU,EAAE;;0CAE1C,EAAE,mBAAmB,gBAAgB,EAAE,YAAY,IAAI,EAAE,UAAU;;;6BAGhF,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;;wBAE1C,EAAE,mBAAmB;MACvC,CAAC;YACD,sBAAsB;QACxB,OAAO;YACL,MAAM,cACJ;gBACE,QAAQ;gBACR,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,KAAK;gBACL,gBAAgB;gBAChB,kBAAkB;gBAClB,WAAW;YACb,CAAC,CAAC,SAAS,WAAW,GAAG,IAAI;YAE/B,MAAM,uBAAuB,KAAK,IAAI,CAAC,KAAK,UAAU,GAAG,CAAC,KAAK,KAAK,GAAG,OAAO;YAC9E,MAAM,oBAAoB,cAAc;YAExC,MAAM,UAAU,CAAC;oDAC6B,EAAE,aAAa,KAAK,UAAU,EAAE;;0CAE1C,EAAE,kBAAkB,gBAAgB,EAAE,YAAY,IAAI,EAAE,UAAU;;;6BAG/E,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;0CACxB,EAAE,YAAY,KAAK,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;wBAC1E,EAAE,qBAAqB,wBAAwB,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;MACtG,CAAC;YACD,sBAAsB;QACxB;QAEA,0BAA0B;IAC5B;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,mBAAmB;IACrB;AACF;GAjKa;;QAKM,wIAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/global/toast.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { <PERSON>, <PERSON>, <PERSON> } from \"@mynaui/icons-react\";\r\nimport { toast } from \"sonner\";\r\n\r\ntype ToastOptions = {\r\n  description?: string;\r\n  duration?: number;\r\n  position?:\r\n    | \"bottom-right\"\r\n    | \"bottom-left\"\r\n    | \"top-right\"\r\n    | \"top-left\"\r\n    | \"top-center\"\r\n    | \"bottom-center\";\r\n  action?: {\r\n    label: string;\r\n    onClick: () => void;\r\n  };\r\n  button?: React.ReactNode;\r\n};\r\n\r\nconst DEFAULT_DURATION = 3000;\r\nconst DEFAULT_POSITION = \"bottom-right\";\r\n\r\nexport const successToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-[#56eda1]\"\r\n              strokeWidth={1.2}\r\n              aria-hidden=\"true\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0\" />\r\n              <path d=\"m8.667 12.633 1.505 1.721a1 1 0 0 0 1.564-.073L15.333 9.3\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const loadingToast = (\r\n  message: string,\r\n  promise: Promise<unknown>,\r\n  options?: ToastOptions,\r\n  successMessage?: string,\r\n) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  const toastId = Math.random().toString(36).substring(2, 9);\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <Loading className=\"mt-0.5 size-4 animate-spin text-primary\" />\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      id: toastId,\r\n      duration: Infinity,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n\r\n  return promise.then(\r\n    (data) => {\r\n      toast.dismiss(toastId);\r\n      successToast(successMessage || \"Completed\", options);\r\n      return data;\r\n    },\r\n    (error) => {\r\n      toast.dismiss(toastId);\r\n      errorToast(error?.message || \"An error occurred\", options);\r\n      throw error;\r\n    },\r\n  );\r\n};\r\n\r\nexport const errorToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div\r\n        className={cn(\r\n          \"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\",\r\n        )}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-red-500\"\r\n              strokeWidth={1.2}\r\n              aria-hidden=\"true\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0\" />\r\n              <path d=\"m12 8 .01 4\" />\r\n              <path d=\"M12 16h.01\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n              {options?.button && options.button}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const infoToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-blue-500\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"1.5\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0\" />\r\n              <path d=\"M12 16v-5h-.5m0 5h1M12 8.5V8\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-muted-foreground\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-muted-foreground\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const warningToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n\r\n  toast.custom(\r\n    (t) => (\r\n      <div className=\"w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex grow items-center gap-3\">\r\n            <svg\r\n              className=\"mt-0.5 text-yellow-500\"\r\n              width=\"24\"\r\n              height=\"24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"1.5\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path d=\"M12 8.5V14m0 3.247v-.5m-6.02-5.985C8.608 5.587 9.92 3 12 3s3.393 2.587 6.02 7.762l.327.644c2.182 4.3 3.274 6.45 2.287 8.022C19.648 21 17.208 21 12.327 21h-.654c-4.88 0-7.321 0-8.307-1.572s.105-3.722 2.287-8.022z\" />\r\n            </svg>\r\n            <div className=\"flex grow items-center justify-between gap-12\">\r\n              <div>\r\n                <p className=\"text-base font-medium text-primary/80\">{message}</p>\r\n                {options?.description && (\r\n                  <p className=\"text-sm text-primary/70\">{options.description}</p>\r\n                )}\r\n              </div>\r\n              {options?.action && (\r\n                <div className=\"whitespace-nowrap text-sm\">\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={options.action.onClick}\r\n                  >\r\n                    {options.action.label}\r\n                  </button>\r\n                  <span className=\"mx-1 text-primary/80\">·</span>\r\n                  <button\r\n                    className=\"text-sm font-medium text-primary hover:underline\"\r\n                    onClick={() => toast.dismiss(t)}\r\n                  >\r\n                    Dismiss\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0\"\r\n            onClick={() => toast.dismiss(t)}\r\n            aria-label=\"Close notification\"\r\n          >\r\n            <X size={16} aria-hidden=\"true\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ),\r\n    {\r\n      duration: options?.duration || DEFAULT_DURATION,\r\n      position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    },\r\n  );\r\n};\r\n\r\nexport const copyToast = (message: string = \"Copied to clipboard\", options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  toast(message, {\r\n    description: options?.description,\r\n    duration: options?.duration || DEFAULT_DURATION,\r\n    position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    icon: (\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        width=\"16\"\r\n        height=\"16\"\r\n        fill=\"none\"\r\n        aria-hidden=\"true\"\r\n        viewBox=\"0 0 16 16\"\r\n        className=\"h-5 w-5 font-semibold\"\r\n        strokeWidth={1.2}\r\n      >\r\n        <path\r\n          fill=\"#10B981\"\r\n          d=\"M14.548 3.488a.75.75 0 0 1-.036 1.06l-8.572 8a.75.75 0 0 1-1.023 0l-3.429-3.2a.75.75 0 0 1 1.024-1.096l2.917 2.722 8.06-7.522a.75.75 0 0 1 1.06.036Z\"\r\n        />\r\n      </svg>\r\n    ),\r\n    style: {\r\n      backgroundColor: \"hsl(var(--background))\",\r\n      color: \"hsl(var(--primary))\",\r\n      border: \"2px solid hsl(var(--border) / 0.7)\",\r\n    },\r\n    className:\r\n      \"rounded-2xl border-2 border-border/80 py-2.5  shadow-lg flex items-center gap-2 text-sm font-medium\",\r\n    action: options?.action,\r\n  });\r\n};\r\n\r\nexport const notificationToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  toast(message, {\r\n    description: options?.description,\r\n    duration: options?.duration || DEFAULT_DURATION,\r\n    position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    icon: (\r\n      <Bell\r\n        className={cn(\"h-5 w-5 font-semibold\", options?.description && \"mt-3\")}\r\n        size={16}\r\n        strokeWidth={1.2}\r\n      />\r\n    ),\r\n    style: {\r\n      backgroundColor: \"rgb(88, 28, 135)\",\r\n      color: \"hsl(var(--primary))\",\r\n      border: \"2px solid hsl(var(--border) / 0.7)\",\r\n    },\r\n    className: cn(\r\n      \"rounded-2xl shadow-lg flex items-center gap-3 py-2 text-sm font-medium justify-start\",\r\n      options?.description && \"items-start justify-start\",\r\n    ),\r\n    action: options?.action,\r\n  });\r\n};\r\n\r\nexport const congratsToast = (message: string, options?: ToastOptions) => {\r\n  const isMobile = window.innerWidth <= 768;\r\n  toast(message, {\r\n    description: options?.description,\r\n    duration: options?.duration || DEFAULT_DURATION,\r\n    position: isMobile ? \"top-center\" : options?.position || DEFAULT_POSITION,\r\n    icon: (\r\n      <Confetti\r\n        className={cn(\"h-5 w-5 font-semibold\", options?.description && \"mt-3\")}\r\n        size={16}\r\n        strokeWidth={1.2}\r\n      />\r\n    ),\r\n    style: {\r\n      backgroundColor: \"#004014\",\r\n      color: \"#56eda1\",\r\n      border: \"none\",\r\n    },\r\n    className: cn(\r\n      \"rounded-2xl py-3 px-4  shadow-lg flex items-center gap-2 text-sm font-medium\",\r\n      options?.description && \"items-start justify-start\",\r\n    ),\r\n    action: options?.action,\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;AAmBA,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AAElB,MAAM,eAAe,CAAC,SAAiB;IAC5C,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,aAAa;gCACb,eAAY;gCACZ,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;;kDAEN,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,eAAe,CAC1B,SACA,SACA,SACA;IAEA,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,MAAM,UAAU,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAExD,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,sIAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,IAAI;QACJ,UAAU;QACV,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;IAGF,OAAO,QAAQ,IAAI,CACjB,CAAC;QACC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,aAAa,kBAAkB,aAAa;QAC5C,OAAO;IACT,GACA,CAAC;QACC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,WAAW,OAAO,WAAW,qBAAqB;QAClD,MAAM;IACR;AAEJ;AAEO,MAAM,aAAa,CAAC,SAAiB;IAC1C,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;sBAGF,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,aAAa;gCACb,eAAY;gCACZ,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;;kDAEN,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;oCAKJ,SAAS,UAAU,QAAQ,MAAM;;;;;;;;;;;;;kCAGtC,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,YAAY,CAAC,SAAiB;IACzC,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;;kDAEN,4SAAC;wCAAK,GAAE;;;;;;kDACR,4SAAC;wCAAK,GAAE;;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAA+C;;;;;;4CAC3D,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,eAAe,CAAC,SAAiB;IAC5C,MAAM,WAAW,OAAO,UAAU,IAAI;IAEtC,2QAAA,CAAA,QAAK,CAAC,MAAM,CACV,CAAC,kBACC,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAU;gCACV,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,SAAQ;gCACR,eAAc;gCACd,gBAAe;gCACf,OAAM;0CAEN,cAAA,4SAAC;oCAAK,GAAE;;;;;;;;;;;0CAEV,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAE,WAAU;0DAAyC;;;;;;4CACrD,SAAS,6BACR,4SAAC;gDAAE,WAAU;0DAA2B,QAAQ,WAAW;;;;;;;;;;;;oCAG9D,SAAS,wBACR,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,WAAU;gDACV,SAAS,QAAQ,MAAM,CAAC,OAAO;0DAE9B,QAAQ,MAAM,CAAC,KAAK;;;;;;0DAEvB,4SAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,4SAAC;gDACC,WAAU;gDACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;0DAC9B;;;;;;;;;;;;;;;;;;;;;;;;kCAOT,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBAC7B,cAAW;kCAEX,cAAA,4SAAC,uSAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;;;;;;;;;;;kBAKjC;QACE,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;IAC3D;AAEJ;AAEO,MAAM,YAAY,CAAC,UAAkB,qBAAqB,EAAE;IACjE,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,SAAS;QACb,aAAa,SAAS;QACtB,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;QACzD,oBACE,4SAAC;YACC,OAAM;YACN,OAAM;YACN,QAAO;YACP,MAAK;YACL,eAAY;YACZ,SAAQ;YACR,WAAU;YACV,aAAa;sBAEb,cAAA,4SAAC;gBACC,MAAK;gBACL,GAAE;;;;;;;;;;;QAIR,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,QAAQ;QACV;QACA,WACE;QACF,QAAQ,SAAS;IACnB;AACF;AAEO,MAAM,oBAAoB,CAAC,SAAiB;IACjD,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,SAAS;QACb,aAAa,SAAS;QACtB,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;QACzD,oBACE,4SAAC,6SAAA,CAAA,OAAI;YACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,SAAS,eAAe;YAC/D,MAAM;YACN,aAAa;;;;;;QAGjB,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,QAAQ;QACV;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA,SAAS,eAAe;QAE1B,QAAQ,SAAS;IACnB;AACF;AAEO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,MAAM,WAAW,OAAO,UAAU,IAAI;IACtC,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,SAAS;QACb,aAAa,SAAS;QACtB,UAAU,SAAS,YAAY;QAC/B,UAAU,WAAW,eAAe,SAAS,YAAY;QACzD,oBACE,4SAAC,qTAAA,CAAA,WAAQ;YACP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,SAAS,eAAe;YAC/D,MAAM;YACN,aAAa;;;;;;QAGjB,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,QAAQ;QACV;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,SAAS,eAAe;QAE1B,QAAQ,SAAS;IACnB;AACF", "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/global/account-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { createCustomerPortalSession } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { AuthState } from \"@/types\";\r\nimport { CartSolid, CreditCardSolid, Logout, Moon, Sun, UserSolid } from \"@mynaui/icons-react\";\r\nimport { Coins } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useTokenInfo } from \"../../hooks/use-token-info\";\r\nimport { errorToast } from \"./toast\";\r\n\r\nexport default function AccountDropdown({\r\n  align = \"center\",\r\n  side = \"right\",\r\n  sideOffset = 5,\r\n  alignOffset = 0,\r\n  className = \"\",\r\n  buttonClassName = \"\",\r\n  openProfileModal,\r\n  openTokenModal,\r\n}: {\r\n  user?: AuthState;\r\n  align?: \"center\" | \"end\" | \"start\";\r\n  side?: \"left\" | \"right\" | \"top\" | \"bottom\";\r\n  buttonClassName?: string;\r\n  className?: string;\r\n  sideOffset?: number;\r\n  alignOffset?: number;\r\n  openProfileModal?: () => void;\r\n  openTokenModal?: () => void;\r\n}) {\r\n  const { logout, user } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const { getTokenDisplayInfo } = useTokenInfo();\r\n  const tokenInfo = getTokenDisplayInfo();\r\n  const createCustomerPortal = async () => {\r\n    if (user.userFromDb?.stripe_customer_id) {\r\n      const customerPortalSession = await createCustomerPortalSession(\r\n        user.userFromDb.stripe_customer_id,\r\n      );\r\n      window.open(customerPortalSession.url, \"_blank\");\r\n    } else {\r\n      errorToast(\"Failed to create customer portal session. Please try again.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger\r\n        asChild\r\n        className={cn(\"w-full focus:outline-none sm:w-full md:w-full lg:w-full\", className)}\r\n      >\r\n        <Button\r\n          variant=\"ghost\"\r\n          className={cn(\r\n            \"group flex cursor-pointer items-center justify-start gap-3 px-2.5 text-sm text-primary/90 transition-all duration-500 hover:bg-sidebar-accent/80 hover:text-primary\",\r\n            buttonClassName,\r\n          )}\r\n        >\r\n          <Avatar className=\"h-6 w-6\">\r\n            <AvatarImage src={`https://avatar.vercel.sh/${user?.email}.png`} />\r\n            <AvatarFallback>{user?.email?.charAt(0).toUpperCase()}</AvatarFallback>\r\n          </Avatar>\r\n          <Label className=\"cursor-pointer text-sm\">{user.email}</Label>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n\r\n      <DropdownMenuContent\r\n        align={align}\r\n        side={side}\r\n        alignOffset={alignOffset}\r\n        sideOffset={sideOffset}\r\n        className=\"w-full p-0\"\r\n      >\r\n        <div className=\"p-2 pb-0.5\">\r\n          <DropdownMenuLabel className=\"mb-1\">My Account</DropdownMenuLabel>\r\n\r\n          {openProfileModal && (\r\n            <DropdownMenuItem onClick={openProfileModal} className=\"justify-start gap-3\">\r\n              <UserSolid className=\"h-4 w-4\" strokeWidth={2} />\r\n              Profile\r\n            </DropdownMenuItem>\r\n          )}\r\n          <DropdownMenuItem onClick={() => createCustomerPortal()} className=\"justify-start gap-3\">\r\n            <CreditCardSolid className=\"h-4 w-4\" strokeWidth={2} />\r\n            Billing\r\n          </DropdownMenuItem>\r\n\r\n          {tokenInfo.remainingTokens && (\r\n            <DropdownMenuItem className=\"justify-start gap-3\">\r\n              <Coins className=\"h-4 w-4\" strokeWidth={2} />\r\n              {tokenInfo.remainingTokens} Tokens\r\n            </DropdownMenuItem>\r\n          )}\r\n\r\n          <DropdownMenuItem onSelect={openTokenModal} className=\"justify-start gap-3\">\r\n            <CartSolid strokeWidth={2} className=\"h-4 w-4\" />\r\n            Purchase Tokens\r\n          </DropdownMenuItem>\r\n\r\n          <DropdownMenuItem\r\n            onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n            className=\"justify-start gap-3\"\r\n          >\r\n            {theme === \"light\" ? (\r\n              <Moon className=\"h-4 w-4\" strokeWidth={2} />\r\n            ) : (\r\n              <Sun className=\"h-4 w-4\" strokeWidth={2} />\r\n            )}\r\n            {theme === \"light\" ? \"Dark Mode\" : \"Light Mode\"}\r\n          </DropdownMenuItem>\r\n        </div>\r\n\r\n        <DropdownMenuSeparator />\r\n\r\n        <div className=\"p-2 pt-0.5\">\r\n          <DropdownMenuItem onClick={() => logout()}>\r\n            <span className=\"flex w-full items-center justify-start gap-3 text-red-500 hover:text-red-600\">\r\n              <Logout className=\"h-4 w-4\" strokeWidth={2} />\r\n              Log out\r\n            </span>\r\n          </DropdownMenuItem>\r\n        </div>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;;;AAuBe,SAAS,gBAAgB,EACtC,QAAQ,QAAQ,EAChB,OAAO,OAAO,EACd,aAAa,CAAC,EACd,cAAc,CAAC,EACf,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,gBAAgB,EAChB,cAAc,EAWf;;IACC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IAC3C,MAAM,YAAY;IAClB,MAAM,uBAAuB;QAC3B,IAAI,KAAK,UAAU,EAAE,oBAAoB;YACvC,MAAM,wBAAwB,MAAM,CAAA,GAAA,oHAAA,CAAA,8BAA2B,AAAD,EAC5D,KAAK,UAAU,CAAC,kBAAkB;YAEpC,OAAO,IAAI,CAAC,sBAAsB,GAAG,EAAE;QACzC,OAAO;YACL,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,qBACE,4SAAC,+IAAA,CAAA,eAAY;;0BACX,4SAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAO;gBACP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;0BAEzE,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uKACA;;sCAGF,4SAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,4SAAC,qIAAA,CAAA,cAAW;oCAAC,KAAK,CAAC,yBAAyB,EAAE,MAAM,MAAM,IAAI,CAAC;;;;;;8CAC/D,4SAAC,qIAAA,CAAA,iBAAc;8CAAE,MAAM,OAAO,OAAO,GAAG;;;;;;;;;;;;sCAE1C,4SAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA0B,KAAK,KAAK;;;;;;;;;;;;;;;;;0BAIzD,4SAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAO;gBACP,MAAM;gBACN,aAAa;gBACb,YAAY;gBACZ,WAAU;;kCAEV,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAAO;;;;;;4BAEnC,kCACC,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAkB,WAAU;;kDACrD,4SAAC,gUAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCAAK;;;;;;;0CAIrD,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS,IAAM;gCAAwB,WAAU;;kDACjE,4SAAC,4UAAA,CAAA,kBAAe;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCAAK;;;;;;;4BAIxD,UAAU,eAAe,kBACxB,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,WAAU;;kDAC1B,4SAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCACvC,UAAU,eAAe;oCAAC;;;;;;;0CAI/B,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,UAAU;gCAAgB,WAAU;;kDACpD,4SAAC,gUAAA,CAAA,YAAS;wCAAC,aAAa;wCAAG,WAAU;;;;;;oCAAY;;;;;;;0CAInD,4SAAC,+IAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;gCACrD,WAAU;;oCAET,UAAU,wBACT,4SAAC,6SAAA,CAAA,OAAI;wCAAC,WAAU;wCAAU,aAAa;;;;;6DAEvC,4SAAC,2SAAA,CAAA,MAAG;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCAEvC,UAAU,UAAU,cAAc;;;;;;;;;;;;;kCAIvC,4SAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,+IAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM;sCAC/B,cAAA,4SAAC;gCAAK,WAAU;;kDACd,4SAAC,iTAAA,CAAA,SAAM;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;GAnHwB;;QAoBG,wIAAA,CAAA,UAAO;QACJ,4PAAA,CAAA,WAAQ;QACJ,uIAAA,CAAA,eAAY;;;KAtBtB", "debugId": null}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/global/logo.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport Link from \"next/link\";\r\n\r\nconst Logo = ({ className }: { className?: string }) => {\r\n  const { user } = useAuth();\r\n\r\n  return (\r\n    <>\r\n      <Link\r\n        href={user ? \"/app\" : \"/\"}\r\n        className={cn(\r\n          \"relative flex items-center rounded-xl focus:outline-none focus:ring-0\",\r\n          className,\r\n        )}\r\n        tabIndex={0}\r\n      >\r\n        <div className={cn(\"relative flex h-auto w-fit items-center justify-center\")}>\r\n          <div className=\"h-[38px] w-[120px]\">\r\n            <svg\r\n              width=\"120\"\r\n              height=\"38\"\r\n              viewBox=\"0 0 482 109\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                d=\"M156.645 81.4936C150.337 81.4936 145.103 80.0845 140.943 77.2661C136.782 74.4477 134.232 70.1866 133.293 64.4827L142.956 62.1676C143.493 64.9189 144.399 67.0998 145.674 68.7103C146.949 70.3208 148.526 71.4616 150.405 72.1326C152.351 72.8036 154.431 73.1392 156.645 73.1392C159.933 73.1392 162.45 72.5017 164.194 71.2267C166.006 69.9517 166.912 68.3077 166.912 66.2945C166.912 64.2814 166.073 62.8051 164.396 61.8657C162.718 60.9262 160.235 60.1545 156.947 59.5506L153.525 58.9466C150.17 58.3427 147.116 57.4703 144.365 56.3296C141.614 55.1888 139.433 53.6118 137.823 51.5987C136.212 49.5856 135.407 47.0021 135.407 43.8482C135.407 39.1509 137.151 35.5272 140.641 32.9773C144.13 30.3602 148.76 29.0517 154.531 29.0517C160.101 29.0517 164.664 30.3267 168.221 32.8766C171.844 35.3595 174.193 38.7482 175.267 43.0429L165.604 45.7606C165 42.7409 163.725 40.6272 161.779 39.4193C159.833 38.1443 157.417 37.5068 154.531 37.5068C151.713 37.5068 149.499 38.0436 147.888 39.1173C146.278 40.1239 145.472 41.5666 145.472 43.4455C145.472 45.4587 146.244 46.935 147.787 47.8744C149.398 48.8139 151.545 49.5185 154.229 49.9882L157.752 50.5921C161.309 51.1961 164.564 52.0349 167.516 53.1086C170.469 54.1822 172.784 55.7256 174.461 57.7387C176.206 59.7519 177.078 62.436 177.078 65.7912C177.078 70.757 175.233 74.6155 171.542 77.3667C167.852 80.118 162.886 81.4936 156.645 81.4936Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M207.415 81.4936C202.45 81.4936 198.021 80.4871 194.129 78.474C190.304 76.3937 187.284 73.4411 185.07 69.6162C182.855 65.7913 181.748 61.2617 181.748 56.0276V54.5177C181.748 49.2836 182.855 44.7876 185.07 41.0298C187.284 37.2048 190.304 34.2523 194.129 32.172C198.021 30.0918 202.45 29.0517 207.415 29.0517C212.381 29.0517 216.81 30.0918 220.702 32.172C224.594 34.2523 227.647 37.2048 229.862 41.0298C232.076 44.7876 233.183 49.2836 233.183 54.5177V56.0276C233.183 61.2617 232.076 65.7913 229.862 69.6162C227.647 73.4411 224.594 76.3937 220.702 78.474C216.81 80.4871 212.381 81.4936 207.415 81.4936ZM207.415 72.2333C211.978 72.2333 215.669 70.7905 218.488 67.905C221.373 64.9524 222.816 60.8926 222.816 55.7256V54.8197C222.816 49.6527 221.407 45.6264 218.588 42.7409C215.77 39.7884 212.046 38.3121 207.415 38.3121C202.919 38.3121 199.229 39.7884 196.343 42.7409C193.525 45.6264 192.116 49.6527 192.116 54.8197V55.7256C192.116 60.8926 193.525 64.9524 196.343 67.905C199.229 70.7905 202.919 72.2333 207.415 72.2333Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M248.149 80.0845V39.218H234.258V30.4609H248.149V19.59C248.149 16.5703 249.055 14.1545 250.867 12.3427C252.678 10.5309 255.094 9.625 258.114 9.625H270.897V18.3821H261.436C259.49 18.3821 258.517 19.3887 258.517 21.4018V30.4609H272.81V39.218H258.517V80.0845H248.149Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M295.735 80.0845C292.715 80.0845 290.3 79.1785 288.488 77.3667C286.743 75.5549 285.871 73.1392 285.871 70.1195V39.218H272.181V30.4609H285.871V14.0539H296.238V30.4609H311.035V39.218H296.238V68.3077C296.238 70.3208 297.178 71.3274 299.057 71.3274H309.424V80.0845H295.735Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M313.997 55.5243V54.0145C313.997 48.7803 315.037 44.3179 317.117 40.6272C319.265 36.9364 322.083 34.0845 325.573 32.0714C329.062 30.0582 332.887 29.0517 337.047 29.0517C341.879 29.0517 345.57 29.9576 348.12 31.7694C350.737 33.5812 352.649 35.5272 353.857 37.6075H355.467V30.4609H365.533V90.2507C365.533 93.2704 364.627 95.6862 362.815 97.498C361.071 99.3098 358.655 100.216 355.568 100.216H322.15V91.1567H352.347C354.293 91.1567 355.266 90.1501 355.266 88.137V72.3339H353.656C352.918 73.5418 351.877 74.7832 350.535 76.0582C349.193 77.3332 347.415 78.3733 345.201 79.1785C343.053 79.9838 340.335 80.3864 337.047 80.3864C332.887 80.3864 329.028 79.4134 325.472 77.4674C321.982 75.4543 319.198 72.6023 317.117 68.9116C315.037 65.1538 313.997 60.6913 313.997 55.5243ZM339.866 71.3274C344.362 71.3274 348.052 69.9182 350.938 67.0998C353.891 64.2143 355.367 60.2552 355.367 55.2223V54.3164C355.367 49.1494 353.924 45.1903 351.039 42.439C348.153 39.6206 344.429 38.2114 339.866 38.2114C335.437 38.2114 331.746 39.6206 328.794 42.439C325.908 45.1903 324.465 49.1494 324.465 54.3164V55.2223C324.465 60.2552 325.908 64.2143 328.794 67.0998C331.746 69.9182 335.437 71.3274 339.866 71.3274Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M398.513 81.4936C393.48 81.4936 389.085 80.4535 385.327 78.3733C381.569 76.226 378.617 73.2398 376.469 69.4149C374.389 65.5228 373.349 61.0269 373.349 55.9269V54.7191C373.349 49.552 374.389 45.056 376.469 41.2311C378.549 37.339 381.435 34.3529 385.126 32.2727C388.884 30.1253 393.212 29.0517 398.11 29.0517C402.875 29.0517 407.035 30.1253 410.592 32.2727C414.215 34.3529 417.034 37.2719 419.047 41.0298C421.06 44.7876 422.067 49.183 422.067 54.2158V58.1414H383.918C384.052 62.5031 385.495 65.9926 388.246 68.6096C391.064 71.1596 394.554 72.4346 398.714 72.4346C402.606 72.4346 405.525 71.5622 407.471 69.8175C409.485 68.0728 411.028 66.0597 412.102 63.7781L420.657 68.207C419.718 70.0859 418.342 72.0655 416.53 74.1457C414.786 76.226 412.471 77.9707 409.585 79.3799C406.7 80.789 403.009 81.4936 398.513 81.4936ZM384.018 50.1895H411.498C411.229 46.4317 409.887 43.5126 407.471 41.4324C405.056 39.2851 401.902 38.2114 398.01 38.2114C394.118 38.2114 390.93 39.2851 388.447 41.4324C386.032 43.5126 384.555 46.4317 384.018 50.1895Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <path\r\n                d=\"M429.514 80.0845V30.4609H439.68V37.9094H441.29C442.23 35.8963 443.907 34.0174 446.323 32.2727C448.739 30.528 452.329 29.6556 457.093 29.6556C460.851 29.6556 464.173 30.4944 467.058 32.172C470.011 33.8496 472.326 36.2318 474.004 39.3186C475.681 42.3383 476.52 45.9955 476.52 50.2902V80.0845H466.153V51.0954C466.153 46.8008 465.079 43.6469 462.932 41.6337C460.784 39.5535 457.832 38.5134 454.074 38.5134C449.779 38.5134 446.323 39.9226 443.706 42.7409C441.156 45.5593 439.881 49.6527 439.881 55.021V80.0845H429.514Z\"\r\n                fill=\"currentColor\"\r\n              />\r\n              <g clipPath=\"url(#clip0_1503_289)\">\r\n                800%\r\n                <path\r\n                  d=\"M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z\"\r\n                  fill=\"currentColor\"\r\n                />\r\n                <path\r\n                  d=\"M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z\"\r\n                  fill=\"currentColor\"\r\n                />\r\n                <path\r\n                  d=\"M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z\"\r\n                  fill=\"currentColor\"\r\n                />\r\n                <path\r\n                  d=\"M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z\"\r\n                  fill=\"currentColor\"\r\n                />\r\n              </g>\r\n              <defs>\r\n                <clipPath id=\"clip0_1503_289\">\r\n                  <rect width=\"109\" height=\"109\" fill=\"currentColor\" />\r\n                </clipPath>\r\n              </defs>\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </Link>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Logo;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,OAAO,CAAC,EAAE,SAAS,EAA0B;;IACjD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE;kBACE,cAAA,4SAAC,8QAAA,CAAA,UAAI;YACH,MAAM,OAAO,SAAS;YACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yEACA;YAEF,UAAU;sBAEV,cAAA,4SAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;0BACjB,cAAA,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBACC,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,OAAM;;0CAEN,4SAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,4SAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,4SAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,4SAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,4SAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,4SAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,4SAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,4SAAC;gCAAE,UAAS;;oCAAuB;kDAEjC,4SAAC;wCACC,GAAE;wCACF,MAAK;;;;;;kDAEP,4SAAC;wCACC,GAAE;wCACF,MAAK;;;;;;kDAEP,4SAAC;wCACC,GAAE;wCACF,MAAK;;;;;;kDAEP,4SAAC;wCACC,GAAE;wCACF,MAAK;;;;;;;;;;;;0CAGT,4SAAC;0CACC,cAAA,4SAAC;oCAAS,IAAG;8CACX,cAAA,4SAAC;wCAAK,OAAM;wCAAM,QAAO;wCAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;GAhFM;;QACa,wIAAA,CAAA,UAAO;;;KADpB;uCAkFS", "debugId": null}}, {"offset": {"line": 2581, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport Link from \"next/link\";\r\nimport AccountDropdown from \"../global/account-dropdown\";\r\nimport Logo from \"../global/logo\";\r\nimport { SettingsAction } from \"./type\";\r\nconst navLinks = [\r\n  { label: \"Academy\", href: \"https://academy.softgen.ai\" },\r\n  { label: \"Give feedback\", href: \"https://softgen.zendesk.com/hc/en-us/requests/new\" },\r\n];\r\n\r\nconst Header = ({ setSettingsAction }: { setSettingsAction: (action: SettingsAction) => void }) => {\r\n  const { user } = useAuth();\r\n  const isMobile = useIsMobile();\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <nav className=\"relative z-50 mx-auto flex w-full max-w-7xl max-w-full items-center justify-between border-b p-4 md:hidden\">\r\n        <div className=\"flex items-center justify-center gap-4 pl-0 md:gap-8 md:pl-0\">\r\n          <Logo />\r\n        </div>\r\n\r\n        <AccountDropdown\r\n          user={user}\r\n          side=\"top\"\r\n          sideOffset={10}\r\n          className=\"w-fit\"\r\n          openProfileModal={() => setSettingsAction(\"profile\")}\r\n          openTokenModal={() => setSettingsAction(\"purchase\")}\r\n        />\r\n      </nav>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <nav className=\"relative mx-auto flex hidden w-full max-w-full flex-col items-center justify-between border-b md:flex\">\r\n      <div className=\"w-full p-4 py-[0.8rem]\">\r\n        <div className=\"flex w-full items-center justify-end gap-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {navLinks.map((link) => (\r\n              <Button variant=\"ghost\" asChild className=\"px-3\" key={link.href}>\r\n                <Link\r\n                  href={link.href}\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"flex h-8 items-center gap-2 px-2 py-0 text-base text-primary/80 transition-all duration-500 hover:text-primary\"\r\n                >\r\n                  {link.label}\r\n                </Link>\r\n              </Button>\r\n            ))}\r\n\r\n            <AccountDropdown\r\n              user={user}\r\n              side=\"top\"\r\n              className=\"flex-1\"\r\n              openProfileModal={() => setSettingsAction(\"profile\")}\r\n              openTokenModal={() => setSettingsAction(\"purchase\")}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,WAAW;IACf;QAAE,OAAO;QAAW,MAAM;IAA6B;IACvD;QAAE,OAAO;QAAiB,MAAM;IAAoD;CACrF;AAED,MAAM,SAAS,CAAC,EAAE,iBAAiB,EAA2D;;IAC5F,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAE3B,IAAI,UAAU;QACZ,qBACE,4SAAC;YAAI,WAAU;;8BACb,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,qIAAA,CAAA,UAAI;;;;;;;;;;8BAGP,4SAAC,oJAAA,CAAA,UAAe;oBACd,MAAM;oBACN,MAAK;oBACL,YAAY;oBACZ,WAAU;oBACV,kBAAkB,IAAM,kBAAkB;oBAC1C,gBAAgB,IAAM,kBAAkB;;;;;;;;;;;;IAIhD;IAEA,qBACE,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,4SAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,OAAO;gCAAC,WAAU;0CACxC,cAAA,4SAAC,8QAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,QAAO;oCACP,KAAI;oCACJ,WAAU;8CAET,KAAK,KAAK;;;;;;+BAPuC,KAAK,IAAI;;;;;sCAYjE,4SAAC,oJAAA,CAAA,UAAe;4BACd,MAAM;4BACN,MAAK;4BACL,WAAU;4BACV,kBAAkB,IAAM,kBAAkB;4BAC1C,gBAAgB,IAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD;GArDM;;QACa,wIAAA,CAAA,UAAO;QACP,iIAAA,CAAA,cAAW;;;KAFxB;uCAuDS", "debugId": null}}, {"offset": {"line": 2730, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/colors.ts"], "sourcesContent": ["export const colors = [\r\n  {\r\n    from: \"from-blue-600/80\",\r\n    via: \"dark:via-blue-500/30 via-blue-500/60\",\r\n    to: \"to-sky-400/70\",\r\n  },\r\n  {\r\n    from: \"from-rose-500/80\",\r\n    via: \"dark:via-orange-400/30 via-orange-400/60\",\r\n    to: \"to-amber-300/70\",\r\n  },\r\n  {\r\n    from: \"from-emerald-600/80\",\r\n    via: \"dark:via-emerald-500/30 via-emerald-500/60\",\r\n    to: \"to-teal-400/70\",\r\n  },\r\n  {\r\n    from: \"from-violet-600/80\",\r\n    via: \"dark:via-purple-500/30 via-purple-500/60\",\r\n    to: \"to-fuchsia-400/70\",\r\n  },\r\n  {\r\n    from: \"from-pink-600/80\",\r\n    via: \"dark:via-pink-500/30 via-pink-500/60\",\r\n    to: \"to-rose-400/70\",\r\n  },\r\n  {\r\n    from: \"from-amber-500/80\",\r\n    via: \"dark:via-yellow-400/30 via-yellow-400/60\",\r\n    to: \"to-orange-300/70\",\r\n  },\r\n  {\r\n    from: \"from-cyan-600/80\",\r\n    via: \"dark:via-blue-500/30 via-blue-500/60\",\r\n    to: \"to-indigo-400/70\",\r\n  },\r\n  {\r\n    from: \"from-lime-500/80\",\r\n    via: \"dark:via-green-500/30 via-green-500/60\",\r\n    to: \"to-emerald-400/70\",\r\n  },\r\n  {\r\n    from: \"from-red-600/80\",\r\n    via: \"dark:via-red-500/30 via-red-500/60\",\r\n    to: \"to-pink-400/70\",\r\n  },\r\n  {\r\n    from: \"from-indigo-600/80\",\r\n    via: \"dark:via-indigo-500/30 via-indigo-500/60\",\r\n    to: \"to-violet-400/70\",\r\n  },\r\n  {\r\n    from: \"from-teal-600/80\",\r\n    via: \"dark:via-teal-500/30 via-teal-500/60\",\r\n    to: \"to-cyan-400/70\",\r\n  },\r\n  {\r\n    from: \"from-orange-600/80\",\r\n    via: \"dark:via-orange-500/30 via-orange-500/60\",\r\n    to: \"to-amber-400/70\",\r\n  },\r\n  {\r\n    from: \"from-yellow-600/80\",\r\n    via: \"dark:via-yellow-500/30 via-yellow-500/60\",\r\n    to: \"to-lime-400/70\",\r\n  },\r\n  {\r\n    from: \"from-green-600/80\",\r\n    via: \"dark:via-green-500/30 via-green-500/60\",\r\n    to: \"to-emerald-400/70\",\r\n  },\r\n  {\r\n    from: \"from-purple-600/80\",\r\n    via: \"dark:via-purple-500/30 via-purple-500/60\",\r\n    to: \"to-fuchsia-400/70\",\r\n  },\r\n  {\r\n    from: \"from-fuchsia-600/80\",\r\n    via: \"dark:via-fuchsia-500/30 via-fuchsia-500/60\",\r\n    to: \"to-pink-400/70\",\r\n  },\r\n  {\r\n    from: \"from-sky-600/80\",\r\n    via: \"dark:via-sky-500/30 via-sky-500/60\",\r\n    to: \"to-blue-400/70\",\r\n  },\r\n  {\r\n    from: \"from-rose-600/80\",\r\n    via: \"dark:via-rose-500/30 via-rose-500/60\",\r\n    to: \"to-red-400/70\",\r\n  },\r\n  {\r\n    from: \"from-amber-600/80\",\r\n    via: \"dark:via-amber-500/30 via-amber-500/60\",\r\n    to: \"to-yellow-400/70\",\r\n  },\r\n  {\r\n    from: \"from-lime-600/80\",\r\n    via: \"dark:via-lime-500/30 via-lime-500/60\",\r\n    to: \"to-green-400/70\",\r\n  },\r\n  {\r\n    from: \"from-emerald-600/80\",\r\n    via: \"dark:via-emerald-500/30 via-emerald-500/60\",\r\n    to: \"to-teal-400/70\",\r\n  },\r\n  {\r\n    from: \"from-teal-600/80\",\r\n    via: \"dark:via-teal-500/30 via-teal-500/60\",\r\n    to: \"to-cyan-400/70\",\r\n  },\r\n  {\r\n    from: \"from-cyan-600/80\",\r\n    via: \"dark:via-cyan-500/30 via-cyan-500/60\",\r\n    to: \"to-sky-400/70\",\r\n  },\r\n  {\r\n    from: \"from-sky-600/80\",\r\n    via: \"dark:via-sky-500/30 via-sky-500/60\",\r\n    to: \"to-blue-400/70\",\r\n  },\r\n  {\r\n    from: \"from-blue-600/80\",\r\n    via: \"dark:via-blue-500/30 via-blue-500/60\",\r\n    to: \"to-indigo-400/70\",\r\n  },\r\n  {\r\n    from: \"from-indigo-600/80\",\r\n    via: \"dark:via-indigo-500/30 via-indigo-500/60\",\r\n    to: \"to-violet-400/70\",\r\n  },\r\n  {\r\n    from: \"from-violet-600/80\",\r\n    via: \"dark:via-violet-500/30 via-violet-500/60\",\r\n    to: \"to-purple-400/70\",\r\n  },\r\n  {\r\n    from: \"from-purple-600/80\",\r\n    via: \"dark:via-purple-500/30 via-purple-500/60\",\r\n    to: \"to-fuchsia-400/70\",\r\n  },\r\n  {\r\n    from: \"from-fuchsia-600/80\",\r\n    via: \"dark:via-fuchsia-500/30 via-fuchsia-500/60\",\r\n    to: \"to-pink-400/70\",\r\n  },\r\n  {\r\n    from: \"from-pink-600/80\",\r\n    via: \"dark:via-pink-500/30 via-pink-500/60\",\r\n    to: \"to-rose-400/70\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,SAAS;IACpB;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;IACA;QACE,MAAM;QACN,KAAK;QACL,IAAI;IACN;CACD", "debugId": null}}, {"offset": {"line": 2894, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/format-timestamp.ts"], "sourcesContent": ["import { format, formatDistanceToNow, isToday, isYesterday } from \"date-fns\";\r\n\r\nexport function formatTimestamp(\r\n  isoString: string,\r\n  type:\r\n    | \"edited\"\r\n    | \"lastChat\"\r\n    | \"message-timestamp\"\r\n    | \"deployed\"\r\n    | \"default\"\r\n    | \"commit-history\" = \"default\",\r\n  year: boolean = false,\r\n): string {\r\n  const utcString = isoString.endsWith(\"Z\") ? isoString : isoString + \"Z\";\r\n  const date = new Date(utcString);\r\n  const currentYear = new Date().getFullYear();\r\n  const dateYear = date.getFullYear();\r\n\r\n  switch (type) {\r\n    case \"edited\":\r\n      return `Edited on ${format(date, dateYear === currentYear && !year ? \"d MMMM\" : \"d MMMM yyyy\")}`;\r\n    case \"message-timestamp\":\r\n      return format(\r\n        date,\r\n        dateYear === currentYear && !year ? \"h:mm a d MMMM\" : \"h:mm a d MMMM yyyy\",\r\n      );\r\n    case \"lastChat\":\r\n      if (isToday(date)) {\r\n        return `Last chat today at ${format(date, \"h:mm a\")}`;\r\n      } else if (isYesterday(date)) {\r\n        return \"Last chat yesterday\";\r\n      } else {\r\n        return `Last chat ${formatDistanceToNow(date)} ago`;\r\n      }\r\n    case \"deployed\":\r\n      return `Deployed on ${format(date, dateYear === currentYear && !year ? \"h:mm a d MMMM\" : \"h:mm a d MMMM yyyy\")}`;\r\n    case \"commit-history\":\r\n      if (isToday(date)) {\r\n        return `Last commit today at ${format(date, \"h:mm a\")}`;\r\n      } else if (isYesterday(date)) {\r\n        return \"Last commit yesterday\";\r\n      } else {\r\n        return `Last commit ${formatDistanceToNow(date)} ago`;\r\n      }\r\n    default:\r\n      return format(\r\n        date,\r\n        dateYear === currentYear && !year ? \"h:mm a d MMMM\" : \"h:mm a d MMMM yyyy\",\r\n      );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;;AAEO,SAAS,gBACd,SAAiB,EACjB,OAMuB,SAAS,EAChC,OAAgB,KAAK;IAErB,MAAM,YAAY,UAAU,QAAQ,CAAC,OAAO,YAAY,YAAY;IACpE,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,WAAW,KAAK,WAAW;IAEjC,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,UAAU,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,aAAa,eAAe,CAAC,OAAO,WAAW,gBAAgB;QAClG,KAAK;YACH,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EACV,MACA,aAAa,eAAe,CAAC,OAAO,kBAAkB;QAE1D,KAAK;YACH,IAAI,CAAA,GAAA,iMAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACjB,OAAO,CAAC,mBAAmB,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,WAAW;YACvD,OAAO,IAAI,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;gBAC5B,OAAO;YACT,OAAO;gBACL,OAAO,CAAC,UAAU,EAAE,CAAA,GAAA,6MAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,IAAI,CAAC;YACrD;QACF,KAAK;YACH,OAAO,CAAC,YAAY,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,aAAa,eAAe,CAAC,OAAO,kBAAkB,uBAAuB;QAClH,KAAK;YACH,IAAI,CAAA,GAAA,iMAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACjB,OAAO,CAAC,qBAAqB,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,WAAW;YACzD,OAAO,IAAI,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;gBAC5B,OAAO;YACT,OAAO;gBACL,OAAO,CAAC,YAAY,EAAE,CAAA,GAAA,6MAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,IAAI,CAAC;YACvD;QACF;YACE,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EACV,MACA,aAAa,eAAe,CAAC,OAAO,kBAAkB;IAE5D;AACF", "debugId": null}}, {"offset": {"line": 2943, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/projects/project-card.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card<PERSON><PERSON>nt, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { colors } from \"@/features/app/colors\";\r\nimport { formatTimestamp } from \"@/lib/format-timestamp\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Project } from \"@/providers/project-provider\";\r\nimport { Calendar } from \"@mynaui/icons-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { memo, useCallback, useMemo } from \"react\";\r\n\r\nconst ProjectCard = ({\r\n  project,\r\n  setSelectedProject,\r\n}: {\r\n  project: Project;\r\n  setSelectedProject: (project: Project) => void;\r\n}) => {\r\n  const gradientColors = useMemo(() => {\r\n    // Create a hash from the project ID to ensure consistent colors\r\n    const hashCode = (str: string) => {\r\n      let hash = 0;\r\n      for (let i = 0; i < str.length; i++) {\r\n        hash = str.charCodeAt(i) + ((hash << 5) - hash);\r\n      }\r\n      return Math.abs(hash);\r\n    };\r\n\r\n    const projectHash = hashCode(project.project_id);\r\n    const colorSet = colors[projectHash % colors.length];\r\n\r\n    return `${colorSet.from} ${colorSet.via} ${colorSet.to}`;\r\n  }, [project.project_id]);\r\n\r\n  const onProjectSettingClick = useCallback(() => {\r\n    setSelectedProject(project);\r\n  }, [project]);\r\n\r\n  // const projectDateInUTC = project.last_updated_date.endsWith(\"Z\")\r\n  //   ? project.last_updated_date\r\n  //   : `${project.last_updated_date}Z`;\r\n\r\n  return (\r\n    <div className=\"group relative overflow-hidden rounded-md border-none bg-sidebar-border/70 p-0 shadow-none outline-none transition-all duration-300 focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2\">\r\n      <Link\r\n        href={`/project/${project.project_id}`}\r\n        className=\"relative w-full rounded-md bg-sidebar-border/70 outline-none transition-all\"\r\n      >\r\n        <CardContent className=\"z-20 rounded-md rounded-b-none border-none bg-sidebar-border/70 p-0 shadow-none\">\r\n          <div className=\"relative aspect-video w-full overflow-hidden rounded-md rounded-b-none bg-sidebar-border/70\">\r\n            {project.preview_image_url ? (\r\n              <Image\r\n                fill\r\n                src={project.preview_image_url}\r\n                alt={project.name}\r\n                className=\"absolute inset-0 left-0 right-0 top-1/2 z-30 mx-auto h-full max-w-[90%] translate-x-0.5 translate-y-[15%] rotate-3 rounded-md object-cover transition-all duration-300 group-hover:translate-x-0 group-hover:translate-y-[9%] group-hover:rotate-0\"\r\n                loading=\"lazy\"\r\n              />\r\n            ) : (\r\n              <div className=\"flex h-full items-center justify-center text-sm text-muted-foreground\">\r\n                No Preview\r\n              </div>\r\n            )}\r\n            <div\r\n              className={cn(\r\n                gradientColors,\r\n                \"absolute inset-0 z-20 h-full w-full rounded-md rounded-b-none bg-gradient-to-br opacity-65\",\r\n              )}\r\n            />\r\n          </div>\r\n        </CardContent>\r\n      </Link>\r\n\r\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 rounded-md rounded-t-none border-b-0 bg-muted p-3 px-4 pb-4\">\r\n        <div className=\"flex w-full flex-col items-start justify-start gap-0.5\">\r\n          <CardTitle className=\"max-w-xs truncate text-lg font-medium lg:text-lg\">\r\n            {project.name}\r\n          </CardTitle>\r\n          <CardDescription className=\"flex items-center gap-1 text-sm\">\r\n            <Calendar className=\"size-4\" strokeWidth={2} />\r\n            <span>{formatTimestamp(project.last_updated_date || \"\", \"edited\", true)}</span>\r\n          </CardDescription>\r\n        </div>\r\n\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"z-50 h-8 w-8 rounded-md p-1\"\r\n          onClick={onProjectSettingClick}\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"24\"\r\n            height=\"24\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n          >\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              clipRule=\"evenodd\"\r\n              d=\"M9.78522 3.18531C10.2789 2.44479 11.11 2 12 2C12.89 2 13.7211 2.44479 14.2148 3.18531L14.5924 3.75182C14.925 4.25061 15.5315 4.49147 16.1157 4.35667L16.4882 4.2707C17.3962 4.06117 18.348 4.33416 19.0069 4.99307C19.6658 5.65197 19.9388 6.60383 19.7293 7.5118L19.6433 7.88434C19.5085 8.46846 19.7494 9.07503 20.2482 9.40755L20.8147 9.78522C21.5552 10.2789 22 11.11 22 12C22 12.89 21.5552 13.7211 20.8147 14.2148L20.2482 14.5924C19.7494 14.925 19.5085 15.5315 19.6433 16.1157L19.7293 16.4882C19.9388 17.3962 19.6658 18.348 19.0069 19.0069C18.348 19.6658 17.3962 19.9388 16.4882 19.7293L16.1157 19.6433C15.5315 19.5085 14.925 19.7494 14.5924 20.2482L14.2148 20.8147C13.7211 21.5552 12.89 22 12 22C11.11 22 10.2789 21.5552 9.78522 20.8147L9.40755 20.2482C9.07503 19.7494 8.46846 19.5085 7.88434 19.6433L7.5118 19.7293C6.60383 19.9388 5.65197 19.6658 4.99307 19.0069C4.33416 18.348 4.06117 17.3962 4.2707 16.4882L4.35667 16.1157C4.49147 15.5315 4.25061 14.925 3.75182 14.5924L3.18531 14.2148C2.44479 13.7211 2 12.89 2 12C2 11.11 2.4448 10.2789 3.18531 9.78522L3.75182 9.40755C4.25061 9.07503 4.49147 8.46846 4.35667 7.88434L4.2707 7.5118C4.06117 6.60383 4.33416 5.65197 4.99307 4.99307C5.65197 4.33416 6.60383 4.06117 7.5118 4.2707L7.88434 4.35667C8.46846 4.49147 9.07503 4.25061 9.40755 3.75182L9.78522 3.18531ZM8.5 12C8.5 10.067 10.067 8.5 12 8.5C13.933 8.5 15.5 10.067 15.5 12C15.5 13.933 13.933 15.5 12 15.5C10.067 15.5 8.5 13.933 8.5 12Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n          </svg>\r\n        </Button>\r\n      </CardHeader>\r\n    </div>\r\n  );\r\n};\r\n\r\nProjectCard.displayName = \"ProjectCard\";\r\n\r\nexport default memo(ProjectCard);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,cAAc,CAAC,EACnB,OAAO,EACP,kBAAkB,EAInB;;IACC,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;+CAAE;YAC7B,gEAAgE;YAChE,MAAM;gEAAW,CAAC;oBAChB,IAAI,OAAO;oBACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;wBACnC,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI;oBAChD;oBACA,OAAO,KAAK,GAAG,CAAC;gBAClB;;YAEA,MAAM,cAAc,SAAS,QAAQ,UAAU;YAC/C,MAAM,WAAW,mIAAA,CAAA,SAAM,CAAC,cAAc,mIAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAEpD,OAAO,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE;QAC1D;8CAAG;QAAC,QAAQ,UAAU;KAAC;IAEvB,MAAM,wBAAwB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;0DAAE;YACxC,mBAAmB;QACrB;yDAAG;QAAC;KAAQ;IAEZ,mEAAmE;IACnE,gCAAgC;IAChC,uCAAuC;IAEvC,qBACE,4SAAC;QAAI,WAAU;;0BACb,4SAAC,8QAAA,CAAA,UAAI;gBACH,MAAM,CAAC,SAAS,EAAE,QAAQ,UAAU,EAAE;gBACtC,WAAU;0BAEV,cAAA,4SAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,4SAAC;wBAAI,WAAU;;4BACZ,QAAQ,iBAAiB,iBACxB,4SAAC,+OAAA,CAAA,UAAK;gCACJ,IAAI;gCACJ,KAAK,QAAQ,iBAAiB;gCAC9B,KAAK,QAAQ,IAAI;gCACjB,WAAU;gCACV,SAAQ;;;;;qDAGV,4SAAC;gCAAI,WAAU;0CAAwE;;;;;;0CAIzF,4SAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA;;;;;;;;;;;;;;;;;;;;;;0BAOV,4SAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAClB,QAAQ,IAAI;;;;;;0CAEf,4SAAC,mIAAA,CAAA,kBAAe;gCAAC,WAAU;;kDACzB,4SAAC,qTAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAS,aAAa;;;;;;kDAC1C,4SAAC;kDAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,iBAAiB,IAAI,IAAI,UAAU;;;;;;;;;;;;;;;;;;kCAItE,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,4SAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;sCAEL,cAAA,4SAAC;gCACC,UAAS;gCACT,UAAS;gCACT,GAAE;gCACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnB;GAjGM;KAAA;AAmGN,YAAY,WAAW,GAAG;2DAEX,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3154, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  border = true,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement> & { border?: boolean }) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"relative animate-pulse overflow-hidden rounded-md bg-accent/60 py-4\",\r\n        className,\r\n      )}\r\n    >\r\n      {border && (\r\n        <div className=\"pointer-events-none absolute inset-0 z-0 rounded-xl border border-primary/10\" />\r\n      )}\r\n      {props.children}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,SAAS,IAAI,EACb,GAAG,OACyD;IAC5D,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uEACA;;YAGD,wBACC,4SAAC;gBAAI,WAAU;;;;;;YAEhB,MAAM,QAAQ;;;;;;;AAGrB;KAlBS", "debugId": null}}, {"offset": {"line": 3193, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/projects/projects-loading.tsx"], "sourcesContent": ["import { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nconst ProjectsLoading = () => {\r\n  return (\r\n    <div className=\"grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3\">\r\n      {Array.from({ length: 3 }).map((_, index) => (\r\n        <div\r\n          className=\"group relative overflow-hidden border-none p-0 transition-all\"\r\n          key={`skeleton-${index}`}\r\n        >\r\n          <div className=\"p-0\">\r\n            <Skeleton\r\n              className=\"aspect-video h-full w-full overflow-hidden rounded-xl\"\r\n              border={false}\r\n            />\r\n          </div>\r\n          <div className=\"flex flex-row items-center justify-between space-y-0 p-4\">\r\n            <div className=\"flex w-full flex-col items-start justify-start gap-2\">\r\n              <Skeleton className=\"h-5 w-32\" border={false} />\r\n              <Skeleton className=\"h-4 w-24\" border={false} />\r\n            </div>\r\n            <Skeleton className=\"h-8 w-8 rounded-lg bg-muted\" border={false} />\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProjectsLoading;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,kBAAkB;IACtB,qBACE,4SAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,4SAAC;gBACC,WAAU;;kCAGV,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,uIAAA,CAAA,WAAQ;4BACP,WAAU;4BACV,QAAQ;;;;;;;;;;;kCAGZ,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAW,QAAQ;;;;;;kDACvC,4SAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAW,QAAQ;;;;;;;;;;;;0CAEzC,4SAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;gCAA8B,QAAQ;;;;;;;;;;;;;eAbvD,CAAC,SAAS,EAAE,OAAO;;;;;;;;;;AAmBlC;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 3290, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/input.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\nimport * as React from \"react\";\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"shadow-xs flex h-9 w-full min-w-0 rounded-lg border border-input bg-transparent px-3 py-1 text-sm outline-none transition-[color,box-shadow] file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\r\n        \"border-ring/40\",\r\n        \"focus-visible:border-ring/60 focus-visible:ring-[3px] focus-visible:ring-primary/20\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        type === \"search\" &&\r\n          \"[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none\",\r\n        type === \"file\" &&\r\n          \"p-0 pr-3 italic text-muted-foreground/70 file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:border-input file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic file:text-foreground\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,4SAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iXACA,kBACA,uFACA,0GACA,SAAS,YACP,8MACF,SAAS,UACP,mOACF;QAED,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 3322, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { CheckSolid, ChevronDown, ChevronUp } from \"@mynaui/icons-react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {\r\n    variant?: \"default\" | \"outline\";\r\n  }\r\n>(({ className, children, variant = \"default\", ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-xl border border-input bg-background px-3 py-5 text-sm ring-offset-background placeholder:text-muted-foreground hover:border-primary/10 hover:bg-muted/40 focus:outline-none focus:ring-2 focus:ring-ring focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/10 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      variant === \"outline\" &&\r\n        \"h-9 border-input bg-transparent px-3 hover:bg-sidebar-accent/80 hover:text-accent-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName = SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 w-full overflow-hidden rounded-3xl border border-primary/10 bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className,\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[calc(var(--radix-select-trigger-width)-8px)]\",\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-md px-4 py-2.5 text-sm outline-none focus:bg-accent/50 focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-3 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <CheckSolid className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAKnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE,oBACzD,4SAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uZACA,YAAY,aACV,gGACF;QAED,GAAG,KAAK;;YAER;0BACD,4SAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4SAAC,2TAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,4SAAC,uTAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MATnB;AAYN,qBAAqB,WAAW,GAAG,kRAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,4SAAC,2TAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MATrB;AAYN,uBAAuB,WAAW,GAAG,kRAAA,CAAA,mBAAgC,CAAC,WAAW;AAEjF,MAAM,8BAAgB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,4SAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4SAAC,kRAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wdACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4SAAC;;;;;8BACD,4SAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4SAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4SAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2NACA;QAED,GAAG,KAAK;;0BAET,4SAAC;gBAAK,WAAU;0BACd,cAAA,4SAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4SAAC,kUAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAI1B,4SAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,kRAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kRAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3537, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/projects/search-form.tsx"], "sourcesContent": ["import { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Dispatch, SetStateAction } from \"react\";\r\n\r\nconst SearchForm = ({\r\n  searchTerm,\r\n  setSearchTerm,\r\n  sortBy,\r\n  setSortBy,\r\n}: {\r\n  searchTerm: string;\r\n  setSearchTerm: Dispatch<SetStateAction<string>>;\r\n  sortBy: \"sort-by-name\" | \"sort-by-activity\";\r\n  setSortBy: Dispatch<SetStateAction<\"sort-by-name\" | \"sort-by-activity\">>;\r\n}) => {\r\n  return (\r\n    <>\r\n      <div className=\"relative w-full\">\r\n        <Input\r\n          value={searchTerm}\r\n          onChange={(e) => setSearchTerm(e.target.value)}\r\n          placeholder=\"Search projects...\"\r\n          className=\"w-full min-w-[256px] rounded-md border-input py-5 pl-10 pr-4 text-base\"\r\n        />\r\n\r\n        <div className=\"absolute left-3 top-1/2 -translate-y-1/2\">\r\n          <svg\r\n            className=\"size-5 text-primary/75\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"24\"\r\n            height=\"24\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n          >\r\n            <path\r\n              d=\"M20 20L16.05 16.05M18 11C18 14.866 14.866 18 11 18C7.13401 18 4 14.866 4 11C4 7.13401 7.13401 4 11 4C14.866 4 18 7.13401 18 11Z\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"2\"\r\n              strokeLinecap=\"round\"\r\n            />\r\n          </svg>\r\n        </div>\r\n      </div>\r\n\r\n      <Select\r\n        value={sortBy}\r\n        onValueChange={(value: \"sort-by-name\" | \"sort-by-activity\") => setSortBy(value)}\r\n      >\r\n        <SelectTrigger className=\"w-full min-w-48 px-4\">\r\n          <SelectValue placeholder=\"Sort By\" className=\"mr-8\" />\r\n        </SelectTrigger>\r\n        <SelectContent align=\"end\">\r\n          <SelectItem value=\"sort-by-name\">Sort by Name</SelectItem>\r\n          <SelectItem value=\"sort-by-activity\">Sort by Activity</SelectItem>\r\n        </SelectContent>\r\n      </Select>\r\n    </>\r\n  );\r\n};\r\n\r\nSearchForm.displayName = \"SearchForm\";\r\n\r\nexport default SearchForm;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,aAAa,CAAC,EAClB,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EAMV;IACC,qBACE;;0BACE,4SAAC;gBAAI,WAAU;;kCACb,4SAAC,oIAAA,CAAA,QAAK;wBACJ,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,aAAY;wBACZ,WAAU;;;;;;kCAGZ,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BACC,WAAU;4BACV,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;sCAEL,cAAA,4SAAC;gCACC,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,eAAc;;;;;;;;;;;;;;;;;;;;;;0BAMtB,4SAAC,qIAAA,CAAA,SAAM;gBACL,OAAO;gBACP,eAAe,CAAC,QAA+C,UAAU;;kCAEzE,4SAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACvB,cAAA,4SAAC,qIAAA,CAAA,cAAW;4BAAC,aAAY;4BAAU,WAAU;;;;;;;;;;;kCAE/C,4SAAC,qIAAA,CAAA,gBAAa;wBAAC,OAAM;;0CACnB,4SAAC,qIAAA,CAAA,aAAU;gCAAC,OAAM;0CAAe;;;;;;0CACjC,4SAAC,qIAAA,CAAA,aAAU;gCAAC,OAAM;0CAAmB;;;;;;;;;;;;;;;;;;;;AAK/C;KAtDM;AAwDN,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 3664, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\";\r\n\r\nconst Collapsible = CollapsiblePrimitive.Root;\r\n\r\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger;\r\n\r\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent;\r\n\r\nexport { Collapsible, CollapsibleContent, CollapsibleTrigger };\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAIA,MAAM,cAAc,8QAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8QAAA,CAAA,qBAAuC;AAElE,MAAM,qBAAqB,8QAAA,CAAA,qBAAuC", "debugId": null}}, {"offset": {"line": 3685, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/rainbow-button.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\nimport React from \"react\";\r\nimport { <PERSON><PERSON> } from \"./button\";\r\n\r\ninterface RainbowButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  variant?: \"primary\" | \"secondary\" | \"tertiary\";\r\n  asChild?: boolean;\r\n  rainbowHeight?: string;\r\n  buttonClassName?: string;\r\n}\r\n\r\nexport const RainbowButton = React.forwardRef<HTMLButtonElement, RainbowButtonProps>(\r\n  ({ children, className, asChild, rainbowHeight = \"h-6 \", buttonClassName }, ref) => {\r\n    return (\r\n      <div className={cn(\"relative flex flex-col gap-2\", className)}>\r\n        <Button\r\n          ref={ref}\r\n          size=\"lg\"\r\n          className={cn(\"z-[50] rounded-xl text-sm font-semibold\", buttonClassName)}\r\n          variant=\"default\"\r\n          asChild={asChild ? true : false}\r\n        >\r\n          {children}\r\n        </Button>\r\n\r\n        <div\r\n          className={cn(\r\n            \"pointer-events-none absolute inset-0 bottom-0 grid h-6 select-none grid-cols-4 blur-xl\",\r\n            rainbowHeight,\r\n          )}\r\n          aria-hidden\r\n        >\r\n          <div className=\"bg-red-300/70\"></div>\r\n          <div className=\"bg-yellow-300/70\"></div>\r\n          <div className=\"bg-green-300/70\"></div>\r\n          <div className=\"bg-blue-300/70\"></div>\r\n        </div>\r\n      </div>\r\n    );\r\n  },\r\n);\r\n\r\nRainbowButton.displayName = \"RainbowButton\";\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AASO,MAAM,8BAAgB,4QAAA,CAAA,UAAK,CAAC,UAAU,MAC3C,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,MAAM,EAAE,eAAe,EAAE,EAAE;IAC1E,qBACE,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;;0BACjD,4SAAC,qIAAA,CAAA,SAAM;gBACL,KAAK;gBACL,MAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;gBACzD,SAAQ;gBACR,SAAS,UAAU,OAAO;0BAEzB;;;;;;0BAGH,4SAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0FACA;gBAEF,aAAW;;kCAEX,4SAAC;wBAAI,WAAU;;;;;;kCACf,4SAAC;wBAAI,WAAU;;;;;;kCACf,4SAAC;wBAAI,WAAU;;;;;;kCACf,4SAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3771, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit] bg-transparent ring-offset-background focus:rounded-xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n));\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName;\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" && \"h-0.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n));\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName;\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4SAAC,oRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,4SAAC,oRAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,4SAAC;;;;;0BACD,4SAAC,oRAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,oRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,4SAAC,oRAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cAAc,sDAC9B,gBAAgB,gBAAgB,wDAChC;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC,oRAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAf7C;AAkBN,UAAU,WAAW,GAAG,oRAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3847, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/features/font/geist_71a83cc0.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_71a83cc0-module__GcBWLa__className\",\n  \"variable\": \"geist_71a83cc0-module__GcBWLa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 3857, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/font/geist_71a83cc0.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22index.ts%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/GeistVF.woff%22,%22variable%22:%22--font-geist-sans%22,%22weight%22:%22100%20200%20300%20400%20500%20600%20700%20800%20900%22}],%22variableName%22:%22geist%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'geist', 'geist Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "debugId": null}}, {"offset": {"line": 3880, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/features/font/geistmono_cb8291bf.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geistmono_cb8291bf-module__CIEVNa__className\",\n  \"variable\": \"geistmono_cb8291bf-module__CIEVNa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 3890, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/font/geistmono_cb8291bf.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22index.ts%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/GeistMono.ttf%22,%22variable%22:%22--font-geist-mono%22,%22weight%22:%22100%20200%20300%20400%20500%20600%20700%20800%20900%22}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'geistMono', 'geistMono Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,4JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,4JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,4JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "debugId": null}}, {"offset": {"line": 3913, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/features/font/inter_f772a68f.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_f772a68f-module__ZWZ_iG__className\",\n  \"variable\": \"inter_f772a68f-module__ZWZ_iG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 3923, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/font/inter_f772a68f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22index.ts%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/inter.woff2%22,%22variable%22:%22--font-inter%22,%22weight%22:%22100%20200%20300%20400%20500%20600%20700%20800%20900%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'inter', 'inter Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "debugId": null}}, {"offset": {"line": 3947, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3985, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/typography.tsx"], "sourcesContent": ["import { geist } from \"@/features/font\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { HTMLAttributes } from \"react\";\r\n\r\nconst Typography = {\r\n  BlockQuote: ({ children }: { children: React.ReactNode }) => {\r\n    return <blockquote className=\"mt-6 border-l-2 pl-6\">{children}</blockquote>;\r\n  },\r\n\r\n  H1: ({\r\n    children,\r\n    className,\r\n    style,\r\n  }: {\r\n    children: React.ReactNode;\r\n    className?: string;\r\n    style?: React.CSSProperties;\r\n  }) => {\r\n    return (\r\n      <h1\r\n        className={cn(\"scroll-m-20 text-3xl font-extrabold tracking-normal\", className)}\r\n        style={style}\r\n      >\r\n        {children}\r\n      </h1>\r\n    );\r\n  },\r\n\r\n  H2: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return (\r\n      <h2 className={cn(className, \"scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0\")}>\r\n        {children}\r\n      </h2>\r\n    );\r\n  },\r\n\r\n  H3: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return (\r\n      <h3 className={cn(\"scroll-m-20 text-xl font-semibold tracking-tight\", className)}>\r\n        {children}\r\n      </h3>\r\n    );\r\n  },\r\n\r\n  H4: ({\r\n    children,\r\n    className,\r\n    style,\r\n  }: {\r\n    children: React.ReactNode;\r\n    className?: string;\r\n    style?: React.CSSProperties;\r\n  }) => {\r\n    return (\r\n      <h4\r\n        className={cn(\"scroll-m-20 text-lg font-semibold tracking-tight\", className)}\r\n        style={style}\r\n      >\r\n        {children}\r\n      </h4>\r\n    );\r\n  },\r\n\r\n  H5: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return (\r\n      <h5 className={cn(\"scroll-m-20 text-base font-semibold tracking-tight\", className)}>\r\n        {children}\r\n      </h5>\r\n    );\r\n  },\r\n\r\n  H6: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return (\r\n      <h6 className={cn(\"scroll-m-20 text-sm font-semibold tracking-tight\", className)}>\r\n        {children}\r\n      </h6>\r\n    );\r\n  },\r\n  InlineCode: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return (\r\n      <code className={cn(\"relative rounded px-1 py-0.5 text-sm text-primary\", className)}>\r\n        {children}\r\n      </code>\r\n    );\r\n  },\r\n\r\n  Lead: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return (\r\n      <p className={cn(\"ml-1 mt-4 text-lg tracking-normal text-muted-foreground\", className)}>\r\n        {children}\r\n      </p>\r\n    );\r\n  },\r\n\r\n  P: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return (\r\n      <p\r\n        className={cn(\"mt-1 text-base leading-7 text-muted-foreground\", geist.className, className)}\r\n      >\r\n        {children}\r\n      </p>\r\n    );\r\n  },\r\n\r\n  Bold: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return <span className={cn(\"font-semibold\", className)}>{children}</span>;\r\n  },\r\n\r\n  Large: ({ children, className }: HTMLAttributes<HTMLDivElement>) => {\r\n    return <div className={cn(\"text-lg font-semibold\", className)}>{children}</div>;\r\n  },\r\n\r\n  Small: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return <small className={cn(\"text-sm font-medium leading-none\", className)}>{children}</small>;\r\n  },\r\n\r\n  Muted: ({ children, className }: { children: React.ReactNode; className?: string }) => {\r\n    return <p className={cn(\"text-sm text-muted-foreground\", className)}>{children}</p>;\r\n  },\r\n};\r\n\r\nexport default Typography;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;;AAGA,MAAM,aAAa;IACjB,YAAY,CAAC,EAAE,QAAQ,EAAiC;QACtD,qBAAO,4SAAC;YAAW,WAAU;sBAAwB;;;;;;IACvD;IAEA,IAAI,CAAC,EACH,QAAQ,EACR,SAAS,EACT,KAAK,EAKN;QACC,qBACE,4SAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;YACrE,OAAO;sBAEN;;;;;;IAGP;IAEA,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAC7E,qBACE,4SAAC;YAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;sBAC1B;;;;;;IAGP;IAEA,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAC7E,qBACE,4SAAC;YAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;sBACnE;;;;;;IAGP;IAEA,IAAI,CAAC,EACH,QAAQ,EACR,SAAS,EACT,KAAK,EAKN;QACC,qBACE,4SAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;YAClE,OAAO;sBAEN;;;;;;IAGP;IAEA,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAC7E,qBACE,4SAAC;YAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;sBACrE;;;;;;IAGP;IAEA,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAC7E,qBACE,4SAAC;YAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;sBACnE;;;;;;IAGP;IACA,YAAY,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QACrF,qBACE,4SAAC;YAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;sBACtE;;;;;;IAGP;IAEA,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAC/E,qBACE,4SAAC;YAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;sBACzE;;;;;;IAGP;IAEA,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAC5E,qBACE,4SAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD,gLAAA,CAAA,QAAK,CAAC,SAAS,EAAE;sBAEhF;;;;;;IAGP;IAEA,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAC/E,qBAAO,4SAAC;YAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;sBAAa;;;;;;IAC3D;IAEA,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAkC;QAC7D,qBAAO,4SAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;sBAAa;;;;;;IAClE;IAEA,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAChF,qBAAO,4SAAC;YAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;sBAAa;;;;;;IAC/E;IAEA,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAqD;QAChF,qBAAO,4SAAC;YAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;sBAAa;;;;;;IACxE;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 4149, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-sidebar-navigation.tsx"], "sourcesContent": ["import { listProjects } from \"@/lib/api\";\r\nimport { Project } from \"@/providers/project-provider\";\r\nimport { CACHE_PARAMS } from \"@/providers/query-provider\";\r\nimport { AuthState } from \"@/types\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { ReactNode } from \"react\";\r\n\r\nexport type NavItem = {\r\n  title: string;\r\n  url?: string;\r\n  icon: ReactNode;\r\n  items?: NavItem[];\r\n  projectLimit?: number;\r\n  mobileUrl?: string;\r\n  modal?: boolean;\r\n};\r\n\r\nexport function useSidebarNavigation(user: AuthState) {\r\n  const pathname = usePathname();\r\n  const currentProjectId = pathname?.split(\"/\")[2];\r\n\r\n  const { data: rawProjects = [], isLoading } = useQuery<Project[]>({\r\n    queryKey: [\"projects\"],\r\n    queryFn: listProjects,\r\n    enabled: !!user?.access_token,\r\n    retry: 2, // Retry failed requests twice\r\n    refetchOnWindowFocus: true,\r\n    ...CACHE_PARAMS\r\n  });\r\n\r\n  const projects = [...rawProjects].sort((a, b) => {\r\n    return new Date(b.last_updated_date).getTime() - new Date(a.last_updated_date).getTime();\r\n  });\r\n\r\n  const baseNavItems: NavItem[] = [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/app\",\r\n      icon: (\r\n        <svg\r\n          width=\"20\"\r\n          height=\"20\"\r\n          viewBox=\"0 0 23 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            fillRule=\"evenodd\"\r\n            clipRule=\"evenodd\"\r\n            d=\"M0.834039 7.30058C0.249939 8.36768 0.249939 9.65493 0.249939 12.2294V13.9406C0.249939 18.3291 0.249939 20.5233 1.56796 21.8867C2.88598 23.25 5.0073 23.25 9.24994 23.25H13.7499C17.9926 23.25 20.1139 23.25 21.4319 21.8867C22.7499 20.5233 22.7499 18.3291 22.7499 13.9406V12.2294C22.7499 9.65493 22.7499 8.36768 22.1658 7.30058C21.5817 6.23348 20.5146 5.5712 18.3804 4.24664L16.1304 2.85023C13.8744 1.45008 12.7464 0.75 11.4999 0.75C10.2535 0.75 9.1255 1.45008 6.86947 2.85023L4.61948 4.24664C2.48525 5.5712 1.41814 6.23348 0.834039 7.30058ZM8.62738 15.8222C8.25302 15.5447 7.72459 15.6232 7.4471 15.9976C7.16961 16.3719 7.24814 16.9004 7.6225 17.1778C8.71646 17.9887 10.0543 18.4687 11.4999 18.4687C12.9455 18.4687 14.2834 17.9887 15.3774 17.1778C15.7517 16.9004 15.8303 16.3719 15.5528 15.9976C15.2753 15.6232 14.7469 15.5447 14.3725 15.8222C13.5531 16.4295 12.5641 16.7812 11.4999 16.7812C10.4358 16.7812 9.44676 16.4295 8.62738 15.8222Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Your Projects\",\r\n      projectLimit: user?.userFromDb?.project_limit,\r\n      url: \"/app\",\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          width=\"20\"\r\n          height=\"20\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n        >\r\n          <path\r\n            fillRule=\"evenodd\"\r\n            clipRule=\"evenodd\"\r\n            d=\"M4.55592 5.51158C5.09004 4.57686 6.08407 4 7.16065 4H16.8394C17.9159 4 18.91 4.57686 19.4441 5.51158L22.6047 11.0427C22.8638 11.496 23 12.009 23 12.5311V17C23 18.6569 21.6569 20 20 20H4C2.34315 20 1 18.6569 1 17V12.5311C1 12.009 1.13625 11.496 1.39527 11.0427L4.55592 5.51158ZM21 13H16.5C16.1852 13 15.8889 13.1482 15.7 13.4L15.4 13.8C14.8334 14.5554 13.9443 15 13 15H11C10.0557 15 9.16656 14.5554 8.6 13.8L8.3 13.4C8.11115 13.1482 7.81476 13 7.5 13H3V17C3 17.5523 3.44772 18 4 18H20C20.5523 18 21 17.5523 21 17V13Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n        </svg>\r\n      ),\r\n      items: projects.map((project) => ({\r\n        title: project.name,\r\n        url: `/project/${project.project_id}`,\r\n        icon: (\r\n          <svg\r\n            width=\"24\"\r\n            height=\"24\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <path\r\n              d=\"M9.72796 3H7.5C6.25736 3 5.25 4.00736 5.25 5.25V18.75C5.25 19.9926 6.25736 21 7.5 21H16.5C17.7426 21 18.75 19.9926 18.75 18.75V12M9.72796 3C10.9706 3 12 4.00736 12 5.25V7.5C12 8.74264 13.0074 9.75 14.25 9.75H16.5C17.7426 9.75 18.75 10.7574 18.75 12M9.72796 3C13.4179 3 18.75 8.3597 18.75 12\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"1.5\"\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n            />\r\n          </svg>\r\n        ),\r\n      })),\r\n    },\r\n    {\r\n      title: \"Settings\",\r\n      modal: true,\r\n      icon: (\r\n        <span className=\"group-hover:animate-spin-once\">\r\n          <svg\r\n            width=\"20\"\r\n            height=\"20\"\r\n            fill=\"currentColor\"\r\n            viewBox=\"0 0 24 24\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <path d=\"M10.565 2.075c-.394.189-.755.497-1.26.928l-.079.066a2.56 2.56 0 0 1-1.58.655l-.102.008c-.662.053-1.135.09-1.547.236a3.33 3.33 0 0 0-2.03 2.029c-.145.412-.182.885-.235 1.547l-.008.102a2.56 2.56 0 0 1-.655 1.58l-.066.078c-.431.506-.74.867-.928 1.261a3.33 3.33 0 0 0 0 2.87c.189.394.497.755.928 1.26l.066.079c.41.48.604.939.655 1.58l.008.102c.053.662.09 1.135.236 1.547a3.33 3.33 0 0 0 2.029 2.03c.412.145.885.182 1.547.235l.102.008c.629.05 1.09.238 1.58.655l.079.066c.505.431.866.74 1.26.928a3.33 3.33 0 0 0 2.87 0c.394-.189.755-.497 1.26-.928l.079-.066c.48-.41.939-.604 1.58-.655l.102-.008c.662-.053 1.135-.09 1.547-.236a3.33 3.33 0 0 0 2.03-2.029c.145-.412.182-.885.235-1.547l.008-.102c.05-.629.238-1.09.655-1.58l.066-.079c.431-.505.74-.866.928-1.26a3.33 3.33 0 0 0 0-2.87c-.189-.394-.497-.755-.928-1.26l-.066-.079a2.56 2.56 0 0 1-.655-1.58l-.008-.102c-.053-.662-.09-1.135-.236-1.547a3.33 3.33 0 0 0-2.029-2.03c-.412-.145-.885-.182-1.547-.235l-.102-.008a2.56 2.56 0 0 1-1.58-.655l-.079-.066c-.505-.431-.866-.74-1.26-.928a3.33 3.33 0 0 0-2.87 0M8.75 12a3.25 3.25 0 1 1 6.5 0 3.25 3.25 0 0 1-6.5 0\" />\r\n          </svg>\r\n        </span>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const navItems = baseNavItems;\r\n\r\n  const integrations: NavItem[] = [\r\n    {\r\n      title: \"Firebase\",\r\n      url: `/integration/firebase?id=${currentProjectId}`,\r\n      icon: (\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 175 205\">\r\n          <path\r\n            d=\"M51.2275 191.109C59.4031 194.399 68.2727 196.352 77.5857 196.677C90.1896 197.117 102.175 194.511 112.89 189.561C100.041 184.513 88.4043 177.131 78.4729 167.952C72.0352 178.26 62.5135 186.401 51.2275 191.109Z\"\r\n            fill=\"#FF9100\"\r\n          ></path>\r\n          <path\r\n            d=\"M78.4711 167.955C55.8006 146.988 42.0477 116.641 43.21 83.3558C43.2478 82.2749 43.3048 81.1947 43.3715 80.1148C39.3113 79.0647 35.0725 78.4239 30.7103 78.2716C24.4663 78.0535 18.4195 78.8279 12.7108 80.4355C6.659 91.0365 3.00696 103.2 2.552 116.228C1.37785 149.852 21.7254 179.231 51.2257 191.112C62.5117 186.404 72.0331 178.272 78.4711 167.955Z\"\r\n            fill=\"#FFC400\"\r\n          ></path>\r\n          <path\r\n            d=\"M78.472 167.954C83.7428 159.519 86.9386 149.63 87.3106 138.976C88.2893 110.95 69.4486 86.8412 43.3725 80.1137C43.3058 81.1936 43.2487 82.2738 43.211 83.3547C42.0486 116.64 55.8015 146.987 78.472 167.954Z\"\r\n            fill=\"#FF9100\"\r\n          ></path>\r\n          <path\r\n            d=\"M84.3945 1.33524C69.5432 13.233 57.8158 28.9212 50.7205 46.9936C46.6583 57.3453 44.1056 68.4647 43.3603 80.1206C69.4365 86.8481 88.2772 110.957 87.2985 138.983C86.9264 149.637 83.7213 159.516 78.4599 167.961C88.391 177.149 100.028 184.522 112.877 189.57C138.667 177.649 156.965 151.996 158.025 121.654C158.711 101.995 151.158 84.4744 140.485 69.6852C129.214 54.0442 84.3945 1.33524 84.3945 1.33524Z\"\r\n            fill=\"#DD2C00\"\r\n          ></path>\r\n        </svg>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const isNavItemActive = (url: string) => pathname === url;\r\n\r\n  const shouldShowIntegrations = pathname?.startsWith(\"/app/\");\r\n\r\n  return {\r\n    navItems,\r\n    integrations,\r\n    isNavItemActive,\r\n    shouldShowIntegrations,\r\n    isLoading,\r\n    projects,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA;AACA;;;;;;;AAaO,SAAS,qBAAqB,IAAe;;IAClD,MAAM,WAAW,CAAA,GAAA,oPAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,mBAAmB,UAAU,MAAM,IAAI,CAAC,EAAE;IAEhD,MAAM,EAAE,MAAM,cAAc,EAAE,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAa;QAChE,UAAU;YAAC;SAAW;QACtB,SAAS,oHAAA,CAAA,eAAY;QACrB,SAAS,CAAC,CAAC,MAAM;QACjB,OAAO;QACP,sBAAsB;QACtB,GAAG,yIAAA,CAAA,eAAY;IACjB;IAEA,MAAM,WAAW;WAAI;KAAY,CAAC,IAAI,CAAC,CAAC,GAAG;QACzC,OAAO,IAAI,KAAK,EAAE,iBAAiB,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,iBAAiB,EAAE,OAAO;IACxF;IAEA,MAAM,eAA0B;QAC9B;YACE,OAAO;YACP,KAAK;YACL,oBACE,4SAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,4SAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;;;;;;;;;;;QAIb;QACA;YACE,OAAO;YACP,cAAc,MAAM,YAAY;YAChC,KAAK;YACL,oBACE,4SAAC;gBACC,OAAM;gBACN,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;0BAEL,cAAA,4SAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAK;;;;;;;;;;;YAIX,OAAO,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;oBAChC,OAAO,QAAQ,IAAI;oBACnB,KAAK,CAAC,SAAS,EAAE,QAAQ,UAAU,EAAE;oBACrC,oBACE,4SAAC;wBACC,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,OAAM;kCAEN,cAAA,4SAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;;;;;;;;;;;gBAIvB,CAAC;QACH;QACA;YACE,OAAO;YACP,OAAO;YACP,oBACE,4SAAC;gBAAK,WAAU;0BACd,cAAA,4SAAC;oBACC,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,SAAQ;oBACR,OAAM;8BAEN,cAAA,4SAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;QAIhB;KACD;IAED,MAAM,WAAW;IAEjB,MAAM,eAA0B;QAC9B;YACE,OAAO;YACP,KAAK,CAAC,yBAAyB,EAAE,kBAAkB;YACnD,oBACE,4SAAC;gBAAI,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;;kCAC1D,4SAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,4SAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,4SAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,4SAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;QAIb;KACD;IAED,MAAM,kBAAkB,CAAC,MAAgB,aAAa;IAEtD,MAAM,yBAAyB,UAAU,WAAW;IAEpD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA5IgB;;QACG,oPAAA,CAAA,cAAW;QAGkB,8QAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 4366, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"@/components/ui/collapsible\";\r\nimport { RainbowButton } from \"@/components/ui/rainbow-button\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { useSidebarNavigation } from \"@/hooks/use-sidebar-navigation\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport Link from \"next/link\";\r\nimport Logo from \"../global/logo\";\r\nimport { SettingsAction } from \"./type\";\r\n\r\nconst Sidebar = ({\r\n  setSettingsAction,\r\n}: {\r\n  setSettingsAction: (action: SettingsAction) => void;\r\n}) => {\r\n  const { user } = useAuth();\r\n  const { navItems, isNavItemActive } = useSidebarNavigation(user);\r\n\r\n  return (\r\n    <nav>\r\n      <aside className=\"hidden h-screen w-[250px] flex-shrink-0 flex-col justify-between border-r bg-background py-4 text-primary md:flex\">\r\n        <div className=\"flex h-[30px] items-center gap-2 px-4\">\r\n          <div className=\"px-2\">\r\n            <Logo />\r\n          </div>\r\n        </div>\r\n\r\n        <ScrollArea className=\"relative mb-auto flex w-full flex-col\">\r\n          <nav className=\"my-8 flex-1 px-4\">\r\n            <ul className=\"flex flex-col gap-3\">\r\n              {navItems.map((item) => (\r\n                <li key={`${item.url}-${item.title}`}>\r\n                  {item.items && item.items.length > 0 ? (\r\n                    <Collapsible>\r\n                      <CollapsibleTrigger className=\"w-full rounded-md outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2\">\r\n                        <div\r\n                          className={cn(\r\n                            \"group flex h-8 w-full items-center justify-between gap-2 rounded-md px-2 text-sm outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2\",\r\n                            isNavItemActive(item.url ?? \"\")\r\n                              ? \"bg-background text-primary\"\r\n                              : \"text-primary/80 hover:bg-background hover:text-primary\",\r\n                          )}\r\n                        >\r\n                          <div className=\"flex items-center gap-2\">\r\n                            {item.icon}\r\n                            {item.title}\r\n                          </div>\r\n                          <div className=\"flex items-center\">\r\n                            {item.projectLimit ? (\r\n                              <Typography.P className=\"text-xs\">\r\n                                {item.items.length} / {item.projectLimit}\r\n                              </Typography.P>\r\n                            ) : (\r\n                              <svg\r\n                                width=\"16\"\r\n                                height=\"16\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                fill=\"none\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                className=\"text-primary/70\"\r\n                              >\r\n                                <path\r\n                                  d=\"M6 9l6 6 6-6\"\r\n                                  stroke=\"currentColor\"\r\n                                  strokeWidth=\"1.5\"\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                />\r\n                              </svg>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </CollapsibleTrigger>\r\n                      <CollapsibleContent className=\"transform-gpu duration-300 animate-in fade-in-0\">\r\n                        <ul className=\"ml-3 mt-2 flex flex-col gap-2\">\r\n                          {item.items.map((subItem) => (\r\n                            <li key={`${subItem.url}-${subItem.title}`}>\r\n                              <Link\r\n                                className={cn(\r\n                                  \"group flex h-7 items-center gap-2 rounded-md px-2 text-xs outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2\",\r\n                                  isNavItemActive(subItem.url ?? \"\")\r\n                                    ? \"bg-background text-primary\"\r\n                                    : \"text-primary/80 hover:bg-background hover:text-primary\",\r\n                                )}\r\n                                href={subItem.url ?? \"\"}\r\n                              >\r\n                                {subItem.icon}\r\n                                <span className=\"max-w-40 truncate\">{subItem.title}</span>\r\n                              </Link>\r\n                            </li>\r\n                          ))}\r\n                        </ul>\r\n                      </CollapsibleContent>\r\n                    </Collapsible>\r\n                  ) : item.modal ? (\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className={cn(\r\n                        \"group flex h-8 w-full items-center justify-between gap-2 rounded-md px-2 text-sm outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2\",\r\n                        \"border-none text-primary/80 hover:bg-background hover:text-primary\",\r\n                      )}\r\n                      onClick={() => setSettingsAction(\"profile\")}\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {item.icon}\r\n                        {item.title}\r\n                      </div>\r\n                      {item.projectLimit && (\r\n                        <Typography.P className=\"text-xs\">\r\n                          {item.items?.length} / {item.projectLimit}\r\n                        </Typography.P>\r\n                      )}\r\n                    </Button>\r\n                  ) : (\r\n                    <Link\r\n                      className={cn(\r\n                        \"group flex h-8 items-center justify-between gap-2 rounded-md px-2 text-sm outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2\",\r\n                        isNavItemActive(item.url ?? \"\")\r\n                          ? \"bg-background text-primary\"\r\n                          : \"text-primary/80 hover:bg-background hover:text-primary\",\r\n                      )}\r\n                      href={item.url ?? \"\"}\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {item.icon}\r\n                        {item.title}\r\n                      </div>\r\n                      {item.projectLimit && (\r\n                        <Typography.P className=\"text-xs\">\r\n                          {item.items?.length} / {item.projectLimit}\r\n                        </Typography.P>\r\n                      )}\r\n                    </Link>\r\n                  )}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </nav>\r\n\r\n          <div className=\"absolute inset-0 top-0 h-10 bg-gradient-to-b from-background to-transparent\" />\r\n        </ScrollArea>\r\n\r\n        <div className=\"flex flex-col gap-5 px-4\">\r\n          {user.userFromDb && user.userFromDb?.free_total_token === 0 ? (\r\n            <div className=\"relative rounded-xl border p-4 shadow-sm\">\r\n              <h3 className=\"text-base font-medium\">Token Limit Reached</h3>\r\n              <Typography.P className=\"mt-1 text-sm text-muted-foreground\">\r\n                You have reached your free tier limit. Please upgrade your plan to continue using\r\n                our services.\r\n              </Typography.P>\r\n\r\n              <div className=\"w-full rounded-2xl border bg-foreground/10 p-0.5\">\r\n                <Button\r\n                  className=\"h-fit w-full py-2 text-sm\"\r\n                  onClick={() => setSettingsAction(\"purchase\")}\r\n                >\r\n                  Upgrade Now\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <RainbowButton\r\n              className=\"relative rounded-lg\"\r\n              buttonClassName=\"h-10 w-full rounded-lg text-sm\"\r\n              asChild\r\n            >\r\n              <Link href=\"/pricing\" target=\"_blank\">\r\n                Upgrade Now\r\n              </Link>\r\n            </RainbowButton>\r\n          )}\r\n        </div>\r\n      </aside>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAcA,MAAM,UAAU,CAAC,EACf,iBAAiB,EAGlB;;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD,EAAE;IAE3D,qBACE,4SAAC;kBACC,cAAA,4SAAC;YAAM,WAAU;;8BACf,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,qIAAA,CAAA,UAAI;;;;;;;;;;;;;;;8BAIT,4SAAC,6IAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAG,WAAU;0CACX,SAAS,GAAG,CAAC,CAAC,qBACb,4SAAC;kDACE,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,kBACjC,4SAAC,0IAAA,CAAA,cAAW;;8DACV,4SAAC,0IAAA,CAAA,qBAAkB;oDAAC,WAAU;8DAC5B,cAAA,4SAAC;wDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6KACA,gBAAgB,KAAK,GAAG,IAAI,MACxB,+BACA;;0EAGN,4SAAC;gEAAI,WAAU;;oEACZ,KAAK,IAAI;oEACT,KAAK,KAAK;;;;;;;0EAEb,4SAAC;gEAAI,WAAU;0EACZ,KAAK,YAAY,iBAChB,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;oEAAC,WAAU;;wEACrB,KAAK,KAAK,CAAC,MAAM;wEAAC;wEAAI,KAAK,YAAY;;;;;;yFAG1C,4SAAC;oEACC,OAAM;oEACN,QAAO;oEACP,SAAQ;oEACR,MAAK;oEACL,OAAM;oEACN,WAAU;8EAEV,cAAA,4SAAC;wEACC,GAAE;wEACF,QAAO;wEACP,aAAY;wEACZ,eAAc;wEACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAO3B,4SAAC,0IAAA,CAAA,qBAAkB;oDAAC,WAAU;8DAC5B,cAAA,4SAAC;wDAAG,WAAU;kEACX,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,wBACf,4SAAC;0EACC,cAAA,4SAAC,8QAAA,CAAA,UAAI;oEACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA,gBAAgB,QAAQ,GAAG,IAAI,MAC3B,+BACA;oEAEN,MAAM,QAAQ,GAAG,IAAI;;wEAEpB,QAAQ,IAAI;sFACb,4SAAC;4EAAK,WAAU;sFAAqB,QAAQ,KAAK;;;;;;;;;;;;+DAX7C,GAAG,QAAQ,GAAG,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;mDAkBhD,KAAK,KAAK,iBACZ,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6KACA;4CAEF,SAAS,IAAM,kBAAkB;;8DAEjC,4SAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;wDACT,KAAK,KAAK;;;;;;;gDAEZ,KAAK,YAAY,kBAChB,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;oDAAC,WAAU;;wDACrB,KAAK,KAAK,EAAE;wDAAO;wDAAI,KAAK,YAAY;;;;;;;;;;;;iEAK/C,4SAAC,8QAAA,CAAA,UAAI;4CACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sKACA,gBAAgB,KAAK,GAAG,IAAI,MACxB,+BACA;4CAEN,MAAM,KAAK,GAAG,IAAI;;8DAElB,4SAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;wDACT,KAAK,KAAK;;;;;;;gDAEZ,KAAK,YAAY,kBAChB,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;oDAAC,WAAU;;wDACrB,KAAK,KAAK,EAAE;wDAAO;wDAAI,KAAK,YAAY;;;;;;;;;;;;;uCAlG1C,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;sCA4G1C,4SAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,4SAAC;oBAAI,WAAU;8BACZ,KAAK,UAAU,IAAI,KAAK,UAAU,EAAE,qBAAqB,kBACxD,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;gCAAC,WAAU;0CAAqC;;;;;;0CAK7D,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB;8CAClC;;;;;;;;;;;;;;;;6CAML,4SAAC,gJAAA,CAAA,gBAAa;wBACZ,WAAU;wBACV,iBAAgB;wBAChB,OAAO;kCAEP,cAAA,4SAAC,8QAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,QAAO;sCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD;GArKM;;QAKa,wIAAA,CAAA,UAAO;QACc,gJAAA,CAAA,uBAAoB;;;KANtD;uCAuKS", "debugId": null}}, {"offset": {"line": 4732, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/zendesk-help-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport Script from \"next/script\";\r\nimport { useEffect } from \"react\";\r\n\r\nexport function ZendeskScript({\r\n  project = false,\r\n  reset = false,\r\n}: {\r\n  project?: boolean;\r\n  reset?: boolean;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n\r\n  useEffect(() => {\r\n    if (!isMobile) return;\r\n\r\n    const interval = setInterval(() => {\r\n      if (reset) {\r\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n        // @ts-expect-error\r\n        window.zE.hide();\r\n      }\r\n\r\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n      // @ts-expect-error\r\n      if (window.zE && window.zE.activate) {\r\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n        // @ts-expect-error\r\n        window.zE(\"webWidget\", \"updateSettings\", {\r\n          webWidget: {\r\n            position: { horizontal: \"right\", vertical: \"bottom\" },\r\n            offset: {\r\n              horizontal: \"0px\",\r\n              vertical: project ? \"135px\" : \"0px\",\r\n            },\r\n          },\r\n        });\r\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n        // @ts-expect-error\r\n        window.zE.show();\r\n        clearInterval(interval);\r\n      }\r\n    }, 200);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isMobile, project, reset]);\r\n\r\n  return (\r\n    <Script\r\n      id=\"ze-snippet\"\r\n      strategy=\"afterInteractive\"\r\n      src=\"https://static.zdassets.com/ekr/snippet.js?key=477b3640-e135-4ab8-8c84-37a00aaaa405\"\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS,cAAc,EAC5B,UAAU,KAAK,EACf,QAAQ,KAAK,EAId;;IACC,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW;oDAAY;oBAC3B,IAAI,OAAO;wBACT,6DAA6D;wBAC7D,mBAAmB;wBACnB,OAAO,EAAE,CAAC,IAAI;oBAChB;oBAEA,6DAA6D;oBAC7D,mBAAmB;oBACnB,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC,QAAQ,EAAE;wBACnC,6DAA6D;wBAC7D,mBAAmB;wBACnB,OAAO,EAAE,CAAC,aAAa,kBAAkB;4BACvC,WAAW;gCACT,UAAU;oCAAE,YAAY;oCAAS,UAAU;gCAAS;gCACpD,QAAQ;oCACN,YAAY;oCACZ,UAAU,UAAU,UAAU;gCAChC;4BACF;wBACF;wBACA,6DAA6D;wBAC7D,mBAAmB;wBACnB,OAAO,EAAE,CAAC,IAAI;wBACd,cAAc;oBAChB;gBACF;mDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;QAAU;QAAS;KAAM;IAE7B,qBACE,4SAAC,gPAAA,CAAA,UAAM;QACL,IAAG;QACH,UAAS;QACT,KAAI;;;;;;AAGV;GAlDgB;;QAOG,iIAAA,CAAA,cAAW;;;KAPd", "debugId": null}}, {"offset": {"line": 4818, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/flag/index.ts"], "sourcesContent": ["import { useFeatureFlagPayload } from \"posthog-js/react\";\r\n\r\ntype flagData = {\r\n  daytonaStatusBanner: {\r\n    slug: \"daytona-banner\";\r\n    active: boolean;\r\n    value: {\r\n      status: \"error\" | \"info\" | \"default\" | \"maintenance\";\r\n      message: string;\r\n      link: string;\r\n      buttonText: string;\r\n    };\r\n  };\r\n  newFeatureLaunch: {\r\n    slug: \"new-feature\";\r\n    active: boolean;\r\n    value: {\r\n      status: \"error\" | \"info\" | \"default\" | \"maintenance\";\r\n      title: string;\r\n      message: string;\r\n      link: string;\r\n      buttonText: string;\r\n    };\r\n  };\r\n  maintenanceMode: {\r\n    slug: \"maintenance-mode\";\r\n    active: boolean;\r\n    value: {\r\n      status: \"error\" | \"info\" | \"default\" | \"maintenance\";\r\n      title: string;\r\n      message: string;\r\n      link: string;\r\n      buttonText: string;\r\n    };\r\n  };\r\n  errorBanner: {\r\n    slug: \"error-banner\";\r\n    active: boolean;\r\n    value: {\r\n      status: \"error\" | \"info\" | \"default\" | \"maintenance\";\r\n      title: string;\r\n      message: string;\r\n      link: string;\r\n      buttonText: string;\r\n    };\r\n  };\r\n};\r\n\r\nexport function useTypedFeatureFlagPayload<T = unknown>(slug: string): T | null {\r\n  return useFeatureFlagPayload(slug) as T | null;\r\n}\r\n\r\nexport function useBannerPayload(slug: \"daytona-banner\"): flagData[\"daytonaStatusBanner\"] | null;\r\nexport function useBannerPayload(slug: \"new-feature\"): flagData[\"newFeatureLaunch\"] | null;\r\nexport function useBannerPayload(slug: \"maintenance-mode\"): flagData[\"maintenanceMode\"] | null;\r\nexport function useBannerPayload(slug: \"error-banner\"): flagData[\"errorBanner\"] | null;\r\nexport function useBannerPayload(slug: string): unknown | null;\r\nexport function useBannerPayload(slug: string) {\r\n  return useTypedFeatureFlagPayload(slug);\r\n}\r\n\r\nexport const getBannerPayload = useBannerPayload;\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAgDO,SAAS,2BAAwC,IAAY;;IAClE,OAAO,CAAA,GAAA,6NAAA,CAAA,wBAAqB,AAAD,EAAE;AAC/B;GAFgB;;QACP,6NAAA,CAAA,wBAAqB;;;AAQvB,SAAS,iBAAiB,IAAY;;IAC3C,OAAO,2BAA2B;AACpC;IAFgB;;QACP;;;AAGF,MAAM,mBAAmB", "debugId": null}}, {"offset": {"line": 4854, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/flag/banner.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport { X } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport * as React from \"react\";\r\n\r\nconst bannerVariants = cva(\r\n  \"relative z-10 w-full flex items-center justify-between gap-x-3 px-4 py-3 text-sm leading-none transition-all duration-300\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-background \",\r\n        maintenance: \"bg-amber-500 text-background\",\r\n        info: \" bg-blue-800 text-blue-200\",\r\n        error: \"bg-red-800 text-red-200\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BannerProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof bannerVariants> {\r\n  message?: string;\r\n  link?: string;\r\n  buttonText?: string;\r\n  acctionClassName?: string;\r\n  onClose?: () => void;\r\n  showCloseButton?: boolean;\r\n}\r\n\r\nconst Banner = React.forwardRef<HTMLDivElement, BannerProps>(\r\n  (\r\n    {\r\n      className,\r\n      variant = \"default\",\r\n      message,\r\n      link,\r\n      buttonText,\r\n      acctionClassName,\r\n      onClose,\r\n      showCloseButton = false,\r\n      ...props\r\n    },\r\n    ref,\r\n  ) => {\r\n    return (\r\n      <div\r\n        ref={ref}\r\n        className={cn(bannerVariants({ variant }), className, \"overflow-hidden\")}\r\n        role=\"alert\"\r\n        {...props}\r\n      >\r\n        <div className=\"flex w-full items-center justify-between gap-3 md:justify-center\">\r\n          <div className=\"flex flex-1 flex-row items-center justify-between gap-2 md:justify-center md:gap-3\">\r\n            {message && (\r\n              <Typography.P\r\n                className={cn(\r\n                  \"mt-0 text-left text-base font-medium tracking-tight text-background\",\r\n                  variant === \"info\" && \"text-background dark:text-primary\",\r\n                  variant === \"error\" && \"text-background dark:text-primary\",\r\n                  variant === \"maintenance\" && \"text-primary dark:text-background\",\r\n                )}\r\n              >\r\n                {message}\r\n              </Typography.P>\r\n            )}\r\n\r\n            <div className=\"flex items-center gap-2\">\r\n              {link && (\r\n                <Button\r\n                  asChild\r\n                  variant=\"secondary\"\r\n                  size={buttonText ? \"sm\" : \"icon\"}\r\n                  className={cn(\r\n                    !buttonText && \"size-7\",\r\n                    acctionClassName,\r\n                    variant === \"default\"\r\n                      ? \"bg-background/90 text-primary hover:bg-background\"\r\n                      : \"bg-primary/90 text-background hover:bg-primary\",\r\n                    \"group inline-flex items-center gap-1 whitespace-nowrap rounded-lg px-3 py-1.5 text-xs font-medium transition-all duration-200\",\r\n                  )}\r\n                >\r\n                  <Link\r\n                    href={link}\r\n                    className={cn(buttonText && \"flex items-center gap-2\")}\r\n                    target=\"_blank\"\r\n                  >\r\n                    {buttonText && (\r\n                      <span className=\"hidden font-medium md:block\">{buttonText}</span>\r\n                    )}\r\n                    <svg\r\n                      className=\"size-3\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      stroke=\"currentColor\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={2}\r\n                        d=\"M14 5l7 7m0 0l-7 7m7-7H3\"\r\n                      />\r\n                    </svg>\r\n                  </Link>\r\n                </Button>\r\n              )}\r\n\r\n              {showCloseButton && onClose && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  onClick={onClose}\r\n                  className={cn(\r\n                    \"size-7 rounded-full\",\r\n                    variant === \"default\"\r\n                      ? \"text-background hover:bg-background/20\"\r\n                      : \"text-background hover:bg-background/20\",\r\n                  )}\r\n                >\r\n                  <X className=\"size-4\" />\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  },\r\n);\r\nBanner.displayName = \"Banner\";\r\n\r\nexport { Banner };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,6HACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAcF,MAAM,uBAAS,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAC5B,CACE,EACE,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,EACP,IAAI,EACJ,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,kBAAkB,KAAK,EACvB,GAAG,OACJ,EACD;IAEA,qBACE,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;QAAQ,IAAI,WAAW;QACtD,MAAK;QACJ,GAAG,KAAK;kBAET,cAAA,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;oBACZ,yBACC,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;wBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uEACA,YAAY,UAAU,qCACtB,YAAY,WAAW,qCACvB,YAAY,iBAAiB;kCAG9B;;;;;;kCAIL,4SAAC;wBAAI,WAAU;;4BACZ,sBACC,4SAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,SAAQ;gCACR,MAAM,aAAa,OAAO;gCAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAC,cAAc,UACf,kBACA,YAAY,YACR,sDACA,kDACJ;0CAGF,cAAA,4SAAC,8QAAA,CAAA,UAAI;oCACH,MAAM;oCACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oCAC5B,QAAO;;wCAEN,4BACC,4SAAC;4CAAK,WAAU;sDAA+B;;;;;;sDAEjD,4SAAC;4CACC,WAAU;4CACV,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,4SAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;4BAOX,mBAAmB,yBAClB,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uBACA,YAAY,YACR,2CACA;0CAGN,cAAA,4SAAC,mRAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7B;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5016, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/flag/banner-container.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { memo, useState } from \"react\";\r\nimport { getBannerPayload } from \".\";\r\nimport { Banner } from \"./banner\";\r\n\r\nconst BannerContainer = ({ noFeatureBanner }: { noFeatureBanner?: boolean }) => {\r\n  const [isNewFeatureBannerClosed, setIsNewFeatureBannerClosed] = useState(false);\r\n\r\n  const daytonaFlagPayload = getBannerPayload(\"daytona-banner\");\r\n  const newFeatureLaunchFlagPayload = getBannerPayload(\"new-feature\");\r\n  const maintenanceModeFlagPayload = getBannerPayload(\"maintenance-mode\");\r\n  const errorBannerFlagPayload = getBannerPayload(\"error-banner\");\r\n\r\n  return (\r\n    <>\r\n      <div className=\"sticky top-0 z-[999] w-full\">\r\n        {newFeatureLaunchFlagPayload &&\r\n          newFeatureLaunchFlagPayload.active &&\r\n          !noFeatureBanner &&\r\n          !isNewFeatureBannerClosed && (\r\n            <Banner\r\n              message={newFeatureLaunchFlagPayload.value.message}\r\n              variant={newFeatureLaunchFlagPayload.value.status}\r\n              link={newFeatureLaunchFlagPayload.value.link}\r\n              buttonText={newFeatureLaunchFlagPayload.value.buttonText}\r\n              acctionClassName=\"rounded-lg\"\r\n              showCloseButton={true}\r\n              onClose={() => setIsNewFeatureBannerClosed(true)}\r\n            />\r\n          )}\r\n\r\n        {errorBannerFlagPayload && errorBannerFlagPayload.active && (\r\n          <Banner\r\n            message={errorBannerFlagPayload.value.message}\r\n            variant=\"error\"\r\n            link={errorBannerFlagPayload.value.link}\r\n            buttonText={errorBannerFlagPayload.value.buttonText}\r\n          />\r\n        )}\r\n\r\n        {daytonaFlagPayload && daytonaFlagPayload.active && (\r\n          <Banner\r\n            message={daytonaFlagPayload.value.message}\r\n            variant={daytonaFlagPayload.value.status}\r\n            link={daytonaFlagPayload.value.link}\r\n            buttonText={daytonaFlagPayload.value.buttonText}\r\n            acctionClassName=\"rounded-lg\"\r\n          />\r\n        )}\r\n\r\n        {maintenanceModeFlagPayload && maintenanceModeFlagPayload.active && (\r\n          <Banner\r\n            message={maintenanceModeFlagPayload.value.message}\r\n            variant=\"maintenance\"\r\n            link={maintenanceModeFlagPayload.value.link}\r\n            buttonText={maintenanceModeFlagPayload.value.buttonText}\r\n            acctionClassName=\"rounded-lg\"\r\n          />\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default memo(BannerContainer);\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,eAAe,EAAiC;;IACzE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,MAAM,qBAAqB,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5C,MAAM,8BAA8B,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;IACrD,MAAM,6BAA6B,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;IACpD,MAAM,yBAAyB,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;IAEhD,qBACE;kBACE,cAAA,4SAAC;YAAI,WAAU;;gBACZ,+BACC,4BAA4B,MAAM,IAClC,CAAC,mBACD,CAAC,0CACC,4SAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,4BAA4B,KAAK,CAAC,OAAO;oBAClD,SAAS,4BAA4B,KAAK,CAAC,MAAM;oBACjD,MAAM,4BAA4B,KAAK,CAAC,IAAI;oBAC5C,YAAY,4BAA4B,KAAK,CAAC,UAAU;oBACxD,kBAAiB;oBACjB,iBAAiB;oBACjB,SAAS,IAAM,4BAA4B;;;;;;gBAIhD,0BAA0B,uBAAuB,MAAM,kBACtD,4SAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,uBAAuB,KAAK,CAAC,OAAO;oBAC7C,SAAQ;oBACR,MAAM,uBAAuB,KAAK,CAAC,IAAI;oBACvC,YAAY,uBAAuB,KAAK,CAAC,UAAU;;;;;;gBAItD,sBAAsB,mBAAmB,MAAM,kBAC9C,4SAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,mBAAmB,KAAK,CAAC,OAAO;oBACzC,SAAS,mBAAmB,KAAK,CAAC,MAAM;oBACxC,MAAM,mBAAmB,KAAK,CAAC,IAAI;oBACnC,YAAY,mBAAmB,KAAK,CAAC,UAAU;oBAC/C,kBAAiB;;;;;;gBAIpB,8BAA8B,2BAA2B,MAAM,kBAC9D,4SAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,2BAA2B,KAAK,CAAC,OAAO;oBACjD,SAAQ;oBACR,MAAM,2BAA2B,KAAK,CAAC,IAAI;oBAC3C,YAAY,2BAA2B,KAAK,CAAC,UAAU;oBACvD,kBAAiB;;;;;;;;;;;;;AAM7B;GAzDM;KAAA;2DA2DS,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 5108, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/global/empty-state.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport React from \"react\";\r\n\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { LinkOne } from \"@mynaui/icons-react\";\r\n\r\ninterface EmptyStateProps {\r\n  icon?: React.ElementType;\r\n  iconSize?: number;\r\n  title?: string;\r\n  titleSize?: \"small\" | \"medium\" | \"large\";\r\n  description?: string;\r\n  action?: React.ReactNode;\r\n  learnMore?: {\r\n    href: string;\r\n    label: string;\r\n  };\r\n  className?: string;\r\n  border?: boolean;\r\n  titleClassName?: string;\r\n  descriptionClassName?: string;\r\n  mainClassName?: string;\r\n}\r\n\r\nexport function EmptyState({\r\n  icon,\r\n  iconSize = 32,\r\n  title = \"Title\",\r\n  description = \"This should detail the actions you can take on this screen, as well as why it's valuable.\",\r\n  action,\r\n  learnMore,\r\n  className,\r\n  border = true,\r\n  titleClassName,\r\n  descriptionClassName,\r\n  mainClassName,\r\n}: EmptyStateProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"rounded-lg bg-background\",\r\n        border &&\r\n          \"bg-gradient-to-br from-primary/40 from-5% via-transparent via-60% to-transparent to-50% bg-clip-padding p-[1px]\",\r\n        className,\r\n      )}\r\n    >\r\n      <div className=\"h-full rounded-lg bg-background p-4\">\r\n        <main\r\n          className={cn(\r\n            \"container mx-auto flex h-full flex-1 flex-col items-center justify-center gap-6 py-40 text-center\",\r\n            mainClassName,\r\n          )}\r\n        >\r\n          {icon && (\r\n            <div className=\"text-foreground/80\">\r\n              <div className=\"flex items-center justify-center\">\r\n                {React.createElement(icon, { size: iconSize })}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"flex flex-col items-center gap-1\">\r\n            <h1\r\n              className={cn(\r\n                \"tracking-4 max-w-3xl text-xl font-bold leading-[34px] md:text-2xl\",\r\n                titleClassName,\r\n              )}\r\n            >\r\n              {title}\r\n            </h1>\r\n            <Typography.P className={cn(\"text-base text-muted-foreground\", descriptionClassName)}>\r\n              {description}\r\n            </Typography.P>\r\n          </div>\r\n\r\n          {action}\r\n\r\n          {learnMore && (\r\n            <Link\r\n              href={learnMore.href}\r\n              className=\"inline-flex items-center gap-1 text-sm text-muted-foreground transition-colors hover:text-foreground\"\r\n              aria-label={`Learn more about ${title}`}\r\n            >\r\n              {learnMore.label}\r\n              <LinkOne className=\"h-4 w-4\" />\r\n            </Link>\r\n          )}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA;;;;;;;AAoBO,SAAS,WAAW,EACzB,IAAI,EACJ,WAAW,EAAE,EACb,QAAQ,OAAO,EACf,cAAc,2FAA2F,EACzG,MAAM,EACN,SAAS,EACT,SAAS,EACT,SAAS,IAAI,EACb,cAAc,EACd,oBAAoB,EACpB,aAAa,EACG;IAChB,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA,UACE,mHACF;kBAGF,cAAA,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qGACA;;oBAGD,sBACC,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;sCACZ,cAAA,4QAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;gCAAE,MAAM;4BAAS;;;;;;;;;;;kCAKlD,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qEACA;0CAGD;;;;;;0CAEH,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;gCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;0CAC5D;;;;;;;;;;;;oBAIJ;oBAEA,2BACC,4SAAC,8QAAA,CAAA,UAAI;wBACH,MAAM,UAAU,IAAI;wBACpB,WAAU;wBACV,cAAY,CAAC,iBAAiB,EAAE,OAAO;;4BAEtC,UAAU,KAAK;0CAChB,4SAAC,mTAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjC;KAnEgB", "debugId": null}}, {"offset": {"line": 5222, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/global/page-header.tsx"], "sourcesContent": ["import Typography from \"@/components/ui/typography\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { HTMLAttributes } from \"react\";\r\n\r\ninterface PageHeaderProps {\r\n  title: string;\r\n  description?: React.ReactNode;\r\n  descriptionClassName?: string;\r\n  titleClassName?: string;\r\n  className?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<HTMLAttributes<HTMLDivElement> & PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  className,\r\n  descriptionClassName,\r\n  titleClassName,\r\n}: PageHeaderProps) => {\r\n  return (\r\n    <div className={cn(\"flex w-full items-center\", description && \"justify-between\", className)}>\r\n      <h1\r\n        className={cn(\r\n          \"tracking-4 text-marketing-text-dark dark:text-marketing-text-light max-w-3xl text-3xl font-bold leading-[34px]\",\r\n          titleClassName,\r\n        )}\r\n      >\r\n        {title}\r\n      </h1>\r\n      {description && (\r\n        <Typography.P className={cn(\"text-marketing-text-light/60 text-lg\", descriptionClassName)}>\r\n          {description}\r\n        </Typography.P>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageHeader;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAWA,MAAM,aAAyE,CAAC,EAC9E,KAAK,EACL,WAAW,EACX,SAAS,EACT,oBAAoB,EACpB,cAAc,EACE;IAChB,qBACE,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B,eAAe,mBAAmB;;0BAC/E,4SAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kHACA;0BAGD;;;;;;YAEF,6BACC,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;gBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;0BACjE;;;;;;;;;;;;AAKX;KAxBM;uCA0BS", "debugId": null}}, {"offset": {"line": 5271, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/input-prompt.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { createJSONStorage, persist } from \"zustand/middleware\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\n\r\ninterface InputPromptState {\r\n  prompt: string;\r\n  setPrompt: (prompt: string) => void;\r\n  isNewUser: boolean;\r\n  setIsNewUser: (isNewUser: boolean) => void;\r\n}\r\n\r\nexport const useInputPromptStore = create<InputPromptState>()(\r\n  persist(\r\n    (set) => ({\r\n      prompt: \"\",\r\n      setPrompt: (prompt) => set({ prompt }),\r\n      isNewUser: false,\r\n      setIsNewUser: (isNewUser) => set({ isNewUser }),\r\n    }),\r\n    {\r\n      name: \"input-prompt\",\r\n      storage: createJSONStorage(() => localStorage),\r\n    },\r\n  ),\r\n);\r\n\r\nexport const useInputPrompt = () =>\r\n  useInputPromptStore(\r\n    useShallow((state) => ({\r\n      prompt: state.prompt,\r\n      setPrompt: state.setPrompt,\r\n    })),\r\n  );\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AASO,MAAM,sBAAsB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IACtC,CAAA,GAAA,4PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QACpC,WAAW;QACX,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC/C,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,4PAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;AACnC;AAIG,MAAM,iBAAiB;;IAC5B,OAAA,oBACE,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;yDAAE,CAAC,QAAU,CAAC;gBACrB,QAAQ,MAAM,MAAM;gBACpB,WAAW,MAAM,SAAS;YAC5B,CAAC;;AACH;GANW;;QAET,kQAAA,CAAA,aAAU;QADZ", "debugId": null}}, {"offset": {"line": 5319, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/app/%28main%29/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { LazyModal } from \"@/components/ui/modal\";\r\nimport BannedUser from \"@/features/app/banned-user\";\r\nimport Header from \"@/features/app/header\";\r\nimport { CreateProjectResponse } from \"@/features/app/modal/create-project-modal\";\r\nimport ProjectCard from \"@/features/app/projects/project-card\";\r\nimport ProjectsLoading from \"@/features/app/projects/projects-loading\";\r\nimport SearchForm from \"@/features/app/projects/search-form\";\r\nimport Sidebar from \"@/features/app/sidebar\";\r\nimport { SettingsAction } from \"@/features/app/type\";\r\nimport { ZendeskScript } from \"@/features/app/zendesk-help-button\";\r\nimport BannerContainer from \"@/features/flag/banner-container\";\r\nimport { EmptyState } from \"@/features/global/empty-state\";\r\nimport PageHeader from \"@/features/global/page-header\";\r\nimport { errorToast, loadingToast } from \"@/features/global/toast\";\r\nimport { createProject, listProjects } from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { Project } from \"@/providers/project-provider\";\r\nimport { CACHE_PARAMS } from \"@/providers/query-provider\";\r\nimport { useInputPromptStore } from \"@/stores/input-prompt\";\r\nimport { Plus } from \"@mynaui/icons-react\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { ApiError } from \"next/dist/server/api-utils\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { lazy, Suspense, useCallback, useEffect, useMemo, useRef, useState } from \"react\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\n\r\nconst WelcomeModal = lazy(() => import(\"@/features/app/welcome-modal\"));\r\nconst CreateProjectModalContent = lazy(() => import(\"@/features/app/modal/create-project-modal\"));\r\nconst ProjectCardSettingsModal = lazy(\r\n  () => import(\"@/features/app/projects/project-card-setting-modal\"),\r\n);\r\nconst SettingsModalContent = lazy(() => import(\"@/features/app/settings\"));\r\n\r\nexport default function Dashboard() {\r\n  const { user } = useAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const { prompt, isNewUser, setIsNewUser } = useInputPromptStore(\r\n    useShallow((state) => ({\r\n      prompt: state.prompt,\r\n      isNewUser: state.isNewUser,\r\n      setIsNewUser: state.setIsNewUser,\r\n    })),\r\n  );\r\n  const queryClient = useQueryClient();\r\n\r\n  const [showCreateProjectModal, setShowCreateProjectModal] = useState(false);\r\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null);\r\n  const [settingsAction, setSettingsAction] = useState<SettingsAction>(undefined);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortBy, setSortBy] = useState<\"sort-by-name\" | \"sort-by-activity\">(\"sort-by-activity\");\r\n\r\n  const attemptedPromptsRef = useRef<Set<string>>(new Set());\r\n\r\n  const { mutate: createProjectMutation, isPending: isCreatingProject } = useMutation<\r\n    CreateProjectResponse,\r\n    ApiError,\r\n    { name: string }\r\n  >({\r\n    mutationKey: [\"create-project\"],\r\n    mutationFn: async (data: { name: string }) => {\r\n      const response = await loadingToast(\"Creating project...\", createProject(data.name));\r\n      return response as CreateProjectResponse;\r\n    },\r\n    onSuccess: (data: CreateProjectResponse) => {\r\n      if (data && data.project_data) {\r\n        queryClient.invalidateQueries({ queryKey: [\"projects\"] });\r\n        router.push(`/project/${data.project_data.project_id}?action=chat`);\r\n      } else {\r\n        errorToast(\"Error creating project\");\r\n      }\r\n    },\r\n    onError: () => {\r\n      errorToast(\"Error creating project\");\r\n    },\r\n  });\r\n\r\n  // Nav component has a link to create project\r\n  useEffect(() => {\r\n    const shouldCreateProject = searchParams.get(\"action\") === \"create-project\";\r\n    if (shouldCreateProject) {\r\n      setShowCreateProjectModal(true);\r\n      const newUrl = new URL(window.location.href);\r\n      newUrl.searchParams.delete(\"action\");\r\n      router.replace(newUrl.toString(), { scroll: false });\r\n    }\r\n  }, [searchParams]);\r\n\r\n  const { data: projects = [], isLoading } = useQuery<Project[]>({\r\n    queryKey: [\"projects\"],\r\n    queryFn: listProjects,\r\n    enabled: !!user?.access_token,\r\n    refetchOnWindowFocus: true,\r\n    ...CACHE_PARAMS\r\n  });\r\n\r\n  const filteredProjects = useMemo(() => {\r\n    if (!projects.length) return [];\r\n\r\n    return [...projects]\r\n      .filter(\r\n        (project) =>\r\n          searchTerm === \"\" || project.name.toLowerCase().includes(searchTerm.toLowerCase()),\r\n      )\r\n      .sort((a, b) => {\r\n        if (sortBy === \"sort-by-name\") {\r\n          return a.name.localeCompare(b.name);\r\n        }\r\n        return new Date(b.last_updated_date).getTime() - new Date(a.last_updated_date).getTime();\r\n      });\r\n  }, [projects, searchTerm, sortBy]);\r\n\r\n  const { mutate } = useMutation<\r\n    CreateProjectResponse,\r\n    { detail: string },\r\n    { name: string; type: \"frontend\" | \"fullstack\" }\r\n  >({\r\n    mutationKey: [\"create-project\"],\r\n    mutationFn: async (data: { name: string; type: \"frontend\" | \"fullstack\" }) => {\r\n      const response = await loadingToast(\"Creating project...\", createProject(data.name));\r\n      return response as CreateProjectResponse;\r\n    },\r\n    onSuccess: (data: CreateProjectResponse) => {\r\n      if (data && data.project_data) {\r\n        queryClient.invalidateQueries({ queryKey: [\"projects\"] });\r\n        router.push(`/project/${data.project_data.project_id}`);\r\n        attemptedPromptsRef.current.delete(prompt);\r\n      } else {\r\n        errorToast(\"Error creating project\");\r\n      }\r\n    },\r\n    onError: (error: { detail: string }) => {\r\n      errorToast(error.detail || \"Error creating project\");\r\n    },\r\n  });\r\n\r\n  // create project when new user is redirected here from landing prompt\r\n  const handleClaimTokens = useCallback(() => {\r\n    setIsNewUser(false);\r\n    if (prompt && !attemptedPromptsRef.current.has(prompt)) {\r\n      attemptedPromptsRef.current.add(prompt);\r\n      mutate({ name: \"New Project\", type: \"frontend\" });\r\n    }\r\n  }, [prompt]);\r\n\r\n  const wholesaleUser = user.userFromDb?.plan?.startsWith(\"wholesale\");\r\n\r\n  // create project when existing user is redirected here from landing prompt\r\n  useEffect(() => {\r\n    if (!isNewUser && prompt && !attemptedPromptsRef.current.has(prompt)) {\r\n      attemptedPromptsRef.current.add(prompt);\r\n      mutate({ name: \"New Project\", type: \"frontend\" });\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isNewUser && wholesaleUser) {\r\n      setIsNewUser(false);\r\n      window.location.href = \"/pricing/wholesale\";\r\n    }\r\n  }, [wholesaleUser]);\r\n\r\n  if (isNewUser && wholesaleUser) {\r\n    return null;\r\n  }\r\n\r\n  const isLoadingProjects = !user?.access_token || isLoading;\r\n\r\n  if (user?.userFromDb?.plan === \"banned\") {\r\n    return <BannedUser />;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Zendesk Widget - Only for paid users */}\r\n      {user?.userFromDb?.plan && user.userFromDb.plan !== \"free-tier\" && (\r\n        <ZendeskScript reset={true} />\r\n      )}\r\n      <main className=\"flex h-screen\">\r\n        <Sidebar setSettingsAction={setSettingsAction} />\r\n\r\n        <div className=\"relative flex w-full flex-1 flex-col overflow-y-auto\">\r\n          <BannerContainer />\r\n          <Header setSettingsAction={setSettingsAction} />\r\n\r\n          <div className=\"flex-1 bg-background\">\r\n            <div className=\"mx-auto flex w-full flex-col px-4 lg:max-w-7xl\">\r\n              <div className=\"h-full w-full\">\r\n                <div className=\"flex flex-col items-start justify-between py-8 lg:flex-row lg:items-center\">\r\n                  <PageHeader title=\"Your Projects\" />\r\n\r\n                  <div className=\"mt-4 flex w-full flex-col items-center gap-3 lg:mt-0 lg:w-fit lg:flex-row lg:gap-2\">\r\n                    {projects.length > 0 && (\r\n                      <SearchForm\r\n                        searchTerm={searchTerm}\r\n                        setSearchTerm={setSearchTerm}\r\n                        sortBy={sortBy}\r\n                        setSortBy={setSortBy}\r\n                      />\r\n                    )}\r\n\r\n                    <div className=\"shadow-xs group inline-flex w-full rounded-md rtl:space-x-reverse\">\r\n                      <Button\r\n                        className=\"h-10 w-full rounded-none bg-[#0a0a0a]/95 pr-4 font-medium shadow-none first:rounded-s-lg last:rounded-e-lg hover:bg-[#0a0a0a]/90 focus-visible:z-10 dark:bg-[#fafafa] dark:hover:bg-[#fafafa]/80\"\r\n                        onClick={() => createProjectMutation({ name: \"New Project\" })}\r\n                        disabled={isCreatingProject}\r\n                      >\r\n                        {isCreatingProject && (\r\n                          <Loading className=\"size-4 animate-spin text-background\" />\r\n                        )}\r\n                        Add New...\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {isLoadingProjects ? (\r\n                  <ProjectsLoading />\r\n                ) : filteredProjects.length > 0 ? (\r\n                  <div className=\"grid gap-6 pt-4 lg:grid-cols-3\">\r\n                    {filteredProjects.map((project, index) => (\r\n                      <ProjectCard\r\n                        key={`project-${index}`}\r\n                        project={project}\r\n                        setSelectedProject={setSelectedProject}\r\n                      />\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <EmptyState\r\n                    title=\"No projects found\"\r\n                    description=\"Create a new project to get started.\"\r\n                    action={\r\n                      <Button\r\n                        className=\"mt-4 h-9 w-fit bg-[#0a0a0a]/95 pr-4 font-medium shadow-none hover:bg-[#0a0a0a]/90 focus-visible:z-10 dark:bg-[#fafafa] dark:hover:bg-[#fafafa]/80 lg:mt-0\"\r\n                        size=\"sm\"\r\n                        onClick={() => setShowCreateProjectModal(true)}\r\n                      >\r\n                        <Plus strokeWidth={2} />\r\n                        Add New Project\r\n                      </Button>\r\n                    }\r\n                  />\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isNewUser && !wholesaleUser && (\r\n          <Suspense fallback={null}>\r\n            <WelcomeModal handleClaimTokens={handleClaimTokens} />\r\n          </Suspense>\r\n        )}\r\n\r\n        <LazyModal open={!!settingsAction} onOpenChange={() => setSettingsAction(undefined)}>\r\n          <SettingsModalContent tab={settingsAction} />\r\n        </LazyModal>\r\n\r\n        <LazyModal open={showCreateProjectModal} onOpenChange={setShowCreateProjectModal}>\r\n          <CreateProjectModalContent onOpenChange={setShowCreateProjectModal} />\r\n        </LazyModal>\r\n\r\n        <LazyModal open={!!selectedProject} onOpenChange={() => setSelectedProject(null)}>\r\n          <ProjectCardSettingsModal\r\n            project={selectedProject}\r\n            setShowModal={() => setSelectedProject(null)}\r\n          />\r\n        </LazyModal>\r\n      </main>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,MAAM,6BAAe,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE;KAApB;AACN,MAAM,0CAA4B,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE;MAAjC;AACN,MAAM,yCAA2B,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAClC;MADI;AAGN,MAAM,qCAAuB,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE;MAA5B;AAES,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,oPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,oPAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAC5D,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;oDAAE,CAAC,QAAU,CAAC;gBACrB,QAAQ,MAAM,MAAM;gBACpB,WAAW,MAAM,SAAS;gBAC1B,cAAc,MAAM,YAAY;YAClC,CAAC;;IAEH,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1E,MAAM,sBAAsB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IAEpD,MAAM,EAAE,QAAQ,qBAAqB,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAIhF;QACA,aAAa;YAAC;SAAiB;QAC/B,UAAU;qCAAE,OAAO;gBACjB,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE,uBAAuB,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI;gBAClF,OAAO;YACT;;QACA,SAAS;qCAAE,CAAC;gBACV,IAAI,QAAQ,KAAK,YAAY,EAAE;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAW;oBAAC;oBACvD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC;gBACpE,OAAO;oBACL,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;gBACb;YACF;;QACA,OAAO;qCAAE;gBACP,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;YACb;;IACF;IAEA,6CAA6C;IAC7C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,sBAAsB,aAAa,GAAG,CAAC,cAAc;YAC3D,IAAI,qBAAqB;gBACvB,0BAA0B;gBAC1B,MAAM,SAAS,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;gBAC3C,OAAO,YAAY,CAAC,MAAM,CAAC;gBAC3B,OAAO,OAAO,CAAC,OAAO,QAAQ,IAAI;oBAAE,QAAQ;gBAAM;YACpD;QACF;8BAAG;QAAC;KAAa;IAEjB,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAa;QAC7D,UAAU;YAAC;SAAW;QACtB,SAAS,oHAAA,CAAA,eAAY;QACrB,SAAS,CAAC,CAAC,MAAM;QACjB,sBAAsB;QACtB,GAAG,yIAAA,CAAA,eAAY;IACjB;IAEA,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;+CAAE;YAC/B,IAAI,CAAC,SAAS,MAAM,EAAE,OAAO,EAAE;YAE/B,OAAO;mBAAI;aAAS,CACjB,MAAM;uDACL,CAAC,UACC,eAAe,MAAM,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;sDAElF,IAAI;uDAAC,CAAC,GAAG;oBACR,IAAI,WAAW,gBAAgB;wBAC7B,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;oBACpC;oBACA,OAAO,IAAI,KAAK,EAAE,iBAAiB,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,iBAAiB,EAAE,OAAO;gBACxF;;QACJ;8CAAG;QAAC;QAAU;QAAY;KAAO;IAEjC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAI3B;QACA,aAAa;YAAC;SAAiB;QAC/B,UAAU;qCAAE,OAAO;gBACjB,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE,uBAAuB,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI;gBAClF,OAAO;YACT;;QACA,SAAS;qCAAE,CAAC;gBACV,IAAI,QAAQ,KAAK,YAAY,EAAE;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;yBAAW;oBAAC;oBACvD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,UAAU,EAAE;oBACtD,oBAAoB,OAAO,CAAC,MAAM,CAAC;gBACrC,OAAO;oBACL,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;gBACb;YACF;;QACA,OAAO;qCAAE,CAAC;gBACR,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,MAAM,IAAI;YAC7B;;IACF;IAEA,sEAAsE;IACtE,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;oDAAE;YACpC,aAAa;YACb,IAAI,UAAU,CAAC,oBAAoB,OAAO,CAAC,GAAG,CAAC,SAAS;gBACtD,oBAAoB,OAAO,CAAC,GAAG,CAAC;gBAChC,OAAO;oBAAE,MAAM;oBAAe,MAAM;gBAAW;YACjD;QACF;mDAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB,KAAK,UAAU,EAAE,MAAM,WAAW;IAExD,2EAA2E;IAC3E,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,aAAa,UAAU,CAAC,oBAAoB,OAAO,CAAC,GAAG,CAAC,SAAS;gBACpE,oBAAoB,OAAO,CAAC,GAAG,CAAC;gBAChC,OAAO;oBAAE,MAAM;oBAAe,MAAM;gBAAW;YACjD;QACF;8BAAG,EAAE;IAEL,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,aAAa,eAAe;gBAC9B,aAAa;gBACb,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;8BAAG;QAAC;KAAc;IAElB,IAAI,aAAa,eAAe;QAC9B,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC,MAAM,gBAAgB;IAEjD,IAAI,MAAM,YAAY,SAAS,UAAU;QACvC,qBAAO,4SAAC,4IAAA,CAAA,UAAU;;;;;IACpB;IAEA,qBACE;;YAEG,MAAM,YAAY,QAAQ,KAAK,UAAU,CAAC,IAAI,KAAK,6BAClD,4SAAC,uJAAA,CAAA,gBAAa;gBAAC,OAAO;;;;;;0BAExB,4SAAC;gBAAK,WAAU;;kCACd,4SAAC,qIAAA,CAAA,UAAO;wBAAC,mBAAmB;;;;;;kCAE5B,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,kJAAA,CAAA,UAAe;;;;;0CAChB,4SAAC,oIAAA,CAAA,UAAM;gCAAC,mBAAmB;;;;;;0CAE3B,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;;kEACb,4SAAC,+IAAA,CAAA,UAAU;wDAAC,OAAM;;;;;;kEAElB,4SAAC;wDAAI,WAAU;;4DACZ,SAAS,MAAM,GAAG,mBACjB,4SAAC,wJAAA,CAAA,UAAU;gEACT,YAAY;gEACZ,eAAe;gEACf,QAAQ;gEACR,WAAW;;;;;;0EAIf,4SAAC;gEAAI,WAAU;0EACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;oEACL,WAAU;oEACV,SAAS,IAAM,sBAAsB;4EAAE,MAAM;wEAAc;oEAC3D,UAAU;;wEAET,mCACC,4SAAC,sIAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEACnB;;;;;;;;;;;;;;;;;;;;;;;;4CAOT,kCACC,4SAAC,6JAAA,CAAA,UAAe;;;;uDACd,iBAAiB,MAAM,GAAG,kBAC5B,4SAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,4SAAC,yJAAA,CAAA,UAAW;wDAEV,SAAS;wDACT,oBAAoB;uDAFf,CAAC,QAAQ,EAAE,OAAO;;;;;;;;;qEAO7B,4SAAC,+IAAA,CAAA,aAAU;gDACT,OAAM;gDACN,aAAY;gDACZ,sBACE,4SAAC,qIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,MAAK;oDACL,SAAS,IAAM,0BAA0B;;sEAEzC,4SAAC,6SAAA,CAAA,OAAI;4DAAC,aAAa;;;;;;wDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWvC,aAAa,CAAC,+BACb,4SAAC,4QAAA,CAAA,WAAQ;wBAAC,UAAU;kCAClB,cAAA,4SAAC;4BAAa,mBAAmB;;;;;;;;;;;kCAIrC,4SAAC,oIAAA,CAAA,YAAS;wBAAC,MAAM,CAAC,CAAC;wBAAgB,cAAc,IAAM,kBAAkB;kCACvE,cAAA,4SAAC;4BAAqB,KAAK;;;;;;;;;;;kCAG7B,4SAAC,oIAAA,CAAA,YAAS;wBAAC,MAAM;wBAAwB,cAAc;kCACrD,cAAA,4SAAC;4BAA0B,cAAc;;;;;;;;;;;kCAG3C,4SAAC,oIAAA,CAAA,YAAS;wBAAC,MAAM,CAAC,CAAC;wBAAiB,cAAc,IAAM,mBAAmB;kCACzE,cAAA,4SAAC;4BACC,SAAS;4BACT,cAAc,IAAM,mBAAmB;;;;;;;;;;;;;;;;;;;AAMnD;GA/OwB;;QACL,wIAAA,CAAA,UAAO;QACT,oPAAA,CAAA,YAAS;QACH,oPAAA,CAAA,kBAAe;QACQ,mIAAA,CAAA,sBAAmB;QAO3C,yRAAA,CAAA,iBAAc;QAUsC,iRAAA,CAAA,cAAW;QAkCxC,8QAAA,CAAA,WAAQ;QAwBhC,iRAAA,CAAA,cAAW;;;MA/ER", "debugId": null}}]}