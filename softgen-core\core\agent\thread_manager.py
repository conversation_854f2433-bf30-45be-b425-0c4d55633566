from sqlalchemy import select
from core.db import Database
from core.models import ProjectThread, LLMRecord
from core.platform.billing import record_usage, update_request_limit
from core.agent.tools import TerminalTool, FilesTool, SessionTool, SupabaseTool
from core.utils.unit import ToolResult
from core.agent.working_memory import WorkingMemory
from core.platform.project import ProjectService
from core.utils.llm import make_llm_api_call
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import json
import logging
import asyncio
from core.utils.error_store import error_store
import re
from fastapi import WebSocket
from starlette.websockets import WebSocketState
import uuid
from core.utils.websocket_utils import send_thread_update

class ThreadService:
    def __init__(self, db: Database):
        self.db = db
        self.working_memory = WorkingMemory(db)
        self.project_manager = ProjectService(db)
        self.user = None
        self.project = None
        self.stop_executing_tool_calls = False
        self.websocket = None
        logging.info("ThreadService initialized")  # Add debug logging

    async def set_user_and_project(self, user, project):
        self.user = user
        self.project = project

    async def create_thread(self, project_id: str) -> int:
        async with self.db.get_async_session() as session:
            creation_date = datetime.now().isoformat()
            new_thread = ProjectThread(
                project_id=project_id,
                messages=json.dumps([]),
                creation_date=creation_date,
                last_updated_date=creation_date,
                name="New Thread"  # Default name for new threads
            )
            session.add(new_thread)
            await session.commit()
            await session.refresh(new_thread)
            return new_thread.thread_id

    async def add_message(self, thread_id: int, message_data: Dict[str, Any], images: Optional[List[Dict[str, Any]]] = None):
        # logging.info(f"Adding message to thread {thread_id} with images: {images}")
        print("adding message to thread")
        async with self.db.get_async_session() as session:
            thread = await session.get(ProjectThread, thread_id)
            if not thread:
                raise ValueError(f"Thread with id {thread_id} not found")

            try:
                messages = json.loads(thread.messages)

                # Add timestamp to message
                message_data['timestamp'] = datetime.now().isoformat()

                # Convert ToolResult objects to strings
                for key, value in message_data.items():
                    if isinstance(value, ToolResult):
                        message_data[key] = str(value)

                # Process images if present
                if images:
                    if isinstance(message_data['content'], str):
                        message_data['content'] = [{"type": "text", "text": message_data['content']}]
                    elif not isinstance(message_data['content'], list):
                        message_data['content'] = []

                    for image in images:
                        image_content = {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{image['content_type']};base64,{image['base64']}",
                                "detail": "high"
                            }
                        }
                        message_data['content'].append(image_content)

                messages.append(message_data)
                thread.messages = json.dumps(messages)
                thread.last_updated_date = datetime.now().isoformat()
                await session.commit()
            except Exception as e:
                await session.rollback()
                raise e

        # After adding message, send websocket update if websocket exists
        if self.websocket and self.websocket.client_state == WebSocketState.CONNECTED:
            print("sending websocket update from adding message")
            try:
                update_task = send_thread_update(self.db, self.websocket, thread_id, self.user)
                await update_task  # Wait for the update to complete
            except Exception as e:
                logging.error(f"Error sending websocket update: {str(e)}")

    async def get_message(self, thread_id: int, message_index: int) -> Optional[Dict[str, Any]]:
        async with self.db.get_async_session() as session:
            thread = await session.get(ProjectThread, thread_id)
            if not thread:
                return None
            messages = json.loads(thread.messages)
            if message_index < len(messages):
                return messages[message_index]
            return None

    async def modify_message(self, thread_id: int, message_index: int, new_message_data: Dict[str, Any]):
        async with self.db.get_async_session() as session:
            thread = await session.get(ProjectThread, thread_id)
            if not thread:
                raise ValueError(f"Thread with id {thread_id} not found")

            try:
                messages = json.loads(thread.messages)
                if message_index < len(messages):
                    messages[message_index] = new_message_data
                    thread.messages = json.dumps(messages)
                    thread.last_updated_date = datetime.now().isoformat()
                    await session.commit()
            except Exception as e:
                await session.rollback()
                raise e

        # After modifying message, send websocket update if websocket exists
        if self.websocket:
            print("sending websocket update from modifying message")
            await send_thread_update(self.db, self.websocket, thread_id, self.user)

    async def remove_message(self, thread_id: int, message_index: int):
        async with self.db.get_async_session() as session:
            thread = await session.get(ProjectThread, thread_id)
            if not thread:
                raise ValueError(f"Thread with id {thread_id} not found")

            try:
                messages = json.loads(thread.messages)
                if message_index < len(messages):
                    del messages[message_index]
                    thread.messages = json.dumps(messages)
                    thread.last_updated_date = datetime.now().isoformat()
                    await session.commit()
            except Exception as e:
                await session.rollback()
                raise e

    def _normalize_error_message(self, error_message: str) -> dict:
        """
        Normalize error message by parsing the dictionary string and cleaning up whitespace.
        """
        try:
            logging.info(f"Raw error message to normalize: {error_message}")

            # First try to extract the dictionary part if it's wrapped in something
            if 'output=' in error_message:
                start_idx = error_message.find('output=') + len('output=')
                end_idx = error_message.rfind(')')
                if end_idx > start_idx:
                    error_message = error_message[start_idx:end_idx]

            # Clean up the string for JSON parsing
            # Replace single quotes with double quotes
            json_str = error_message.replace("'", '"')

            # Handle escaped quotes and newlines
            json_str = json_str.replace('\\"', '"')
            json_str = json_str.replace('\\n', ' ')
            json_str = json_str.replace('\n', ' ')

            # Remove any trailing commas before closing braces
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            try:
                error_dict = json.loads(json_str)
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract key-value pairs manually
                error_dict = {}
                # Look for lint_output
                lint_match = re.search(r'"lint_output"\s*:\s*"([^"]*)"', json_str)
                if lint_match:
                    error_dict['lint_output'] = lint_match.group(1)
                # Look for typescript_output
                ts_match = re.search(r'"typescript_output"\s*:\s*"([^"]*)"', json_str)
                if ts_match:
                    error_dict['typescript_output'] = ts_match.group(1)

            # Ensure we have both fields, even if empty
            normalized = {
                'lint_output': error_dict.get('lint_output', '').strip().replace('  ', ' '),
                'typescript_output': error_dict.get('typescript_output', '').strip().replace('  ', ' ')
            }

            # If both fields are empty, use the original message
            if not normalized['lint_output'] and not normalized['typescript_output']:
                normalized['lint_output'] = error_message.strip()
                normalized['typescript_output'] = error_message.strip()

            return normalized

        except Exception as e:
            logging.error(f"Error in _normalize_error_message: {str(e)}")
            logging.error(f"Failed to parse message: {error_message}")
            return {
                'lint_output': error_message.strip(),
                'typescript_output': error_message.strip()
            }

    def _are_errors_identical(self, error1: dict, error2: dict) -> bool:
        """
        Compare two error messages to check if they are identical.

        Args:
            error1: First error message dict
            error2: Second error message dict

        Returns:
            bool: True if errors are identical, False otherwise
        """
        lint_match = error1.get('lint_output') == error2.get('lint_output')
        ts_match = error1.get('typescript_output') == error2.get('typescript_output')


        return lint_match and ts_match

    async def _get_last_two_error_messages(self, messages: List[Dict[str, Any]]) -> List[dict]:
        """
        Extract and normalize the last two error messages from the message history.
        """
        last_two_errors = []

        for msg in reversed(messages):
            if msg.get('role') == 'assistant' and isinstance(msg.get('content'), str):
                content = msg.get('content', '')
                if 'check_for_errors: ToolResult(success=False' in content:
                    # Extract the dictionary part of the error message
                    start_marker = "output="
                    error_start = content.find(start_marker)
                    if error_start != -1:
                        error_start += len(start_marker)
                        # Find the end of the dictionary
                        error_end = content.find(')', error_start)
                        if error_end != -1:
                            error_message = content[error_start:error_end]

                            try:
                                normalized_error = self._normalize_error_message(error_message)
                                last_two_errors.append(normalized_error)
                            except Exception as e:
                                logging.error(f"Error during normalization: {str(e)}")
                                logging.error(f"Failed to normalize message: {error_message}")
                        else:
                            logging.warning("Could not find end of error message")
                    else:
                        logging.warning("Could not find start of error message")

                if len(last_two_errors) >= 2:
                    break

        logging.info(f"Final error count: {len(last_two_errors)}")
        if len(last_two_errors) > 0:
            logging.info(f"First error: {last_two_errors[0]}")
        return last_two_errors

    async def list_messages(self, thread_id: int, hide_tool_msgs: bool = False, only_latest_assistant: bool = False, regular_list: bool = True, last_message_with_string_filter: Optional[str] = None, limit_assistant_message: Optional[int] = None, chat_mode: bool = False) -> Dict[str, Any]:
        async with self.db.get_async_session() as session:
            thread = await session.get(ProjectThread, thread_id)
            if not thread:
                logging.warning(f"Thread {thread_id} not found")
                return {"messages": [], "is_last_build_failed": False, "has_consecutive_identical_errors": False, "last_check_errors_result": None}

            messages = json.loads(thread.messages)
            is_last_build_failed = False
            last_check_errors_result = None

            # Get last two error messages and check if they're identical
            try:
                last_two_errors = await self._get_last_two_error_messages(messages)
                has_consecutive_identical_errors = (
                    len(last_two_errors) == 2 and
                    self._are_errors_identical(last_two_errors[0], last_two_errors[1])
                )
            except Exception as e:
                logging.error(f"Error checking consecutive errors: {str(e)}")
                has_consecutive_identical_errors = False
                last_two_errors = []

            # Check for build failure in all messages first
            try:
                is_last_build_failed = False
                for msg in reversed(messages):
                    if msg.get('role') == 'assistant' and isinstance(msg.get('content'), str):
                        content = msg.get('content', '')
                        if ('check_for_errors: ToolResult' in content or 'run_npm_build: ToolResult' in content):
                            if ('check_for_errors: ToolResult(success=False' in content or
                                'run_npm_build: ToolResult(success=False' in content):
                                is_last_build_failed = True
                                last_check_errors_result = content

                            else :
                                last_check_errors_result = "No errors found"
                            break
            except Exception as e:
                logging.error(f"Error checking build failure: {str(e)}")
                is_last_build_failed = False

            filtered_messages = messages  # Initialize filtered_messages with all messages

            # Filter out messages with <actions> tag when in chat mode
            if chat_mode:
                filtered_messages = [
                    msg for msg in filtered_messages
                    if not (isinstance(msg.get('content'), str) and '<actions>' in msg.get('content', ''))
                ]

            if only_latest_assistant:
                for msg in reversed(messages):
                    if msg.get('role') == 'assistant':
                        return {"messages": [msg], "is_last_build_failed": is_last_build_failed, "has_consecutive_identical_errors": has_consecutive_identical_errors, "last_check_errors_result": last_check_errors_result}
                return {"messages": [], "is_last_build_failed": is_last_build_failed, "has_consecutive_identical_errors": has_consecutive_identical_errors, "last_check_errors_result": last_check_errors_result}

            # New: Limit assistant messages if specified
            if limit_assistant_message is not None:
                assistant_messages = [msg for msg in filtered_messages if msg.get('role') == 'assistant']
                if len(assistant_messages) > limit_assistant_message:
                    # Get indices of assistant messages to keep
                    assistant_indices = [i for i, msg in enumerate(filtered_messages)
                                      if msg.get('role') == 'assistant'][-limit_assistant_message:]
                    # Keep non-assistant messages and last N assistant messages
                    filtered_messages = [msg for i, msg in enumerate(filtered_messages)
                                      if msg.get('role') != 'assistant' or i in assistant_indices]

            # Apply string filter if provided
            if last_message_with_string_filter is not None:
                for msg in reversed(filtered_messages):
                    content = msg.get('content', '')
                    if isinstance(content, str) and last_message_with_string_filter in content:
                        return {"messages": [msg], "is_last_build_failed": is_last_build_failed, "has_consecutive_identical_errors": has_consecutive_identical_errors, "last_check_errors_result": last_check_errors_result}
                return {"messages": [], "is_last_build_failed": is_last_build_failed, "has_consecutive_identical_errors": has_consecutive_identical_errors, "last_check_errors_result": last_check_errors_result}

            if hide_tool_msgs:
                filtered_messages = [
                    {k: v for k, v in msg.items() if k != 'tool_calls'}
                    for msg in filtered_messages
                    if msg.get('role') != 'tool'
                ]

            if regular_list:
                filtered_messages = [
                    msg for msg in filtered_messages
                    if msg.get('role') in ['system', 'assistant', 'tool', 'user']
                ]

            # Add a "go on" message if the first message is an assistant message
            if filtered_messages and filtered_messages[0].get('role') == 'assistant':
                filtered_messages.insert(0, {
                    "role": "user",
                    "content": "go on"
                })

            return {
                "messages": filtered_messages,
                "is_last_build_failed": is_last_build_failed,
                "has_consecutive_identical_errors": has_consecutive_identical_errors,
                "last_check_errors_result": last_check_errors_result
            }

    async def cleanup_incomplete_tool_calls(self, thread_id: int):
        messages_data = await self.list_messages(thread_id)
        messages = messages_data["messages"]
        last_assistant_message = next((m for m in reversed(messages) if m['role'] == 'assistant' and 'tool_calls' in m), None)

        if last_assistant_message:
            tool_calls = last_assistant_message.get('tool_calls', [])
            tool_responses = [m for m in messages[messages.index(last_assistant_message)+1:] if m['role'] == 'tool']

            if len(tool_calls) != len(tool_responses):
                # Create failed ToolResults for incomplete tool calls
                failed_tool_results = []
                for tool_call in tool_calls[len(tool_responses):]:
                    failed_tool_result = {
                        "role": "tool",
                        "tool_call_id": tool_call['id'],
                        "name": tool_call['function']['name'],
                        "content": "UnitResult(success=False, output='Execution interrupted. Session was stopped.')"
                    }
                    failed_tool_results.append(failed_tool_result)

                # Insert failed tool results after the last assistant message
                assistant_index = messages.index(last_assistant_message)
                messages[assistant_index+1:assistant_index+1] = failed_tool_results

                async with self.db.get_async_session() as session:
                    thread = await session.get(ProjectThread, thread_id)
                    if thread and thread.messages:
                        thread.messages = json.dumps(messages)
                        await session.commit()

                return True
        return False

    async def handle_action_loops_and_duplicates(self, thread_id: int, messages: List[Dict[str, Any]]) -> tuple[List[Dict[str, Any]], Optional[str]]:
        """
        Handle action loops and duplicates in thread messages.
        Returns: (filtered_messages, loop_warning_message)
        """
        try:
            # First check for loops in recent messages
            assistant_messages_with_actions = [
                msg for msg in messages[-11:]  # Look at last 5 messages
                if msg['role'] == 'assistant' and '<actions>' in msg['content']
            ][-2:]  # Get last 2 assistant messages with actions

            loop_warning = None
            loop_detected = False

            if len(assistant_messages_with_actions) == 2:
                def extract_actions_block(content):
                    start = content.find('<actions>')
                    end = content.find('</actions>') + len('</actions>')
                    return content[start:end].strip() if start > -1 and end > -1 else ''

                last_actions = extract_actions_block(assistant_messages_with_actions[-1]['content'])
                previous_actions = extract_actions_block(assistant_messages_with_actions[-2]['content'])

                if last_actions == previous_actions:
                    loop_detected = True  # noqa: F841
                    loop_warning = f"""
🚨 ATTENTION: YOU ARE STUCK IN A LOOP! 🚨

You have repeated these exact actions twice:
{previous_actions}

Your recent behavior:
1. You've been checking the same files
2. Making the same suggestions
3. Not implementing actual changes

To break this loop:
1. ⚡ STOP investigating - START implementing
2. 🔨 Make concrete code changes NOW
3. 🔄 Try a completely different approach:
   - Add new features
   - Improve existing code
   - Enhance UX/UI
   - Optimize performance
4. 💡 If truly stuck, ask the user for clarification

I'm now removing duplicate actions to help you focus.
Remember: Progress over repetition!
"""
                    # If loop detected, now remove duplicates
                    seen_actions = {}
                    indices_to_remove = []

                    # Identify duplicates
                    for i, msg in enumerate(messages):
                        if msg['role'] == 'assistant' and '<actions>' in msg.get('content', ''):
                            actions_block = extract_actions_block(msg['content'])
                            if actions_block in seen_actions:
                                indices_to_remove.append(seen_actions[actions_block])
                                seen_actions[actions_block] = i
                            else:
                                seen_actions[actions_block] = i

                    # Update thread in database with duplicates removed
                    async with self.db.get_async_session() as session:
                        thread = await session.get(ProjectThread, thread_id)
                        if thread and thread.messages:
                            filtered_messages = [msg for i, msg in enumerate(messages) if i not in indices_to_remove]
                            thread.messages = json.dumps(filtered_messages)
                            await session.commit()

                            duplicate_count = len(indices_to_remove)
                            if duplicate_count > 0:
                                loop_warning += f"\n\n🧹 Cleaned up {duplicate_count} duplicate action messages to help you focus on new solutions."

                            # logging.info(f"Removed {duplicate_count} duplicate action messages from thread {thread_id}")
                            return filtered_messages, loop_warning

            return messages, loop_warning

        except Exception as e:
            logging.error(f"Error in handle_action_loops_and_duplicates for thread {thread_id}: {str(e)}")
            return messages, None


    async def run_thread(self, thread_id: int, system_message: List[Dict[str, Any]], model_name: Any, max_tokens: Optional[Any] = None, iteration_count: int = 0, mode: str = "interactive", websocket: Optional[WebSocket] = None, messages: Optional[List[Dict[str, Any]]] = None, is_build_failed: bool = False, chat_mode: bool = False, model_mode: str = "creative", reasoning_effort: Optional[str] = None) -> Any:
        if websocket and not websocket.running_session:
            self.websocket = websocket
            await self.send_status_message(websocket, "stopped")
            return {"status": "stopped", "message": "Session cancelled"}

        project_id = await self.get_project_id_by_thread(thread_id)

        # Get editor context
        files_tool = FilesTool(self.db, project_id)
        editor_context = await self.get_editor_context(files_tool)

        # Get messages
        if messages is None:
            messages_data = await self.list_messages(
                thread_id,
                limit_assistant_message=11
            )
            messages = messages_data["messages"]
        # Handle loops and duplicates
        # messages, loop_warning = await self.handle_action_loops_and_duplicates(thread_id, messages)
        loop_warning = None

        # Update additional instructions with loop warning if needed
        if loop_warning:
            additional_instructions = loop_warning + "\n" + editor_context
        else:
            additional_instructions = editor_context

        messages = self.clean_messages(messages)  # Clean the messages
        prepared_messages = system_message + messages

        if additional_instructions:
            additional_instruction_message = {
                "role": "user",
                "content": additional_instructions
            }
            prepared_messages.append(additional_instruction_message)

        try:
            response = await make_llm_api_call(
                messages=prepared_messages,
                model_name=model_name,
                max_tokens=max_tokens,
                websocket=websocket,
                user_id=self.user.id if self.user else None,
                free_fix=is_build_failed,
                trace_id=f"thread_{thread_id}",
                reasoning_effort=reasoning_effort
            )
        except Exception as e:
            logging.error(f"Error in API call: {str(e)}")
            if websocket:
                await self.send_error_message(websocket, str(e))

            if "Context window is full" in str(e):
                error_message = "Context window is full. Please start a new thread/task."
                if websocket:
                    await self.send_error_message(websocket, error_message)
                error_store.set_error(thread_id, error_message)
                return {"status": "context_exceeded", "message": error_message}

            if "once a close message has been sent" in str(e):
                self.stop_executing_tool_calls = True
                logging.error("WebSocket has been closed, cannot send messages.")
                mock_response = {
                    'usage': {
                        'prompt_tokens': 10000,
                        'completion_tokens': 5000
                    }
                }
                asyncio.create_task(self.process_token_usage(mock_response, model_name, is_build_failed))
                return {"status": "error", "message": "WebSocket closed, cannot send messages."}

            error_message = f"An error occurred: {str(e)}"
            if websocket:
                await self.send_error_message(websocket, error_message)
            error_store.set_error(thread_id, error_message)
            return {"status": "error", "message": error_message}
        # Create a message_id for the response
        message_id = str(uuid.uuid4())

        # Calculate total tokens
        try:
            # If build failed, set tokens to 0
            if is_build_failed:
                total_tokens = 0
            else:
                input_tokens = response['usage']['prompt_tokens']
                output_tokens = response['usage']['completion_tokens']
                total_tokens = input_tokens + output_tokens

                # Add git commit tokens if not a file opening response
                response_content = response.choices[0].message.content if hasattr(response, 'choices') else ''
                if '<open_files_in_editor' not in response_content:
                    git_commit_tokens = 3000
                    total_tokens = total_tokens + git_commit_tokens

                # Adjust token count for deepseek models
                if model_name and ('deepseek' in model_name or 'openai' in model_name):
                    total_tokens = total_tokens // 15  # Integer division
                if chat_mode:
                    total_tokens = total_tokens // 2
        except (KeyError, TypeError):
            total_tokens = 0 if is_build_failed else 15000  # Default value based on build status

        result = await self.handle_response_with_tools(
            thread_id=thread_id,
            response=response,
            project_id=project_id,
            message_id=message_id,
            iteration_count=iteration_count,
            mode=mode,
            websocket=websocket,
            total_tokens=total_tokens,
            model_name=model_name,
            model_mode= model_mode
        )
        run_tools_failed = result.get("run_tools_failed", False)
        has_tool_calls = result.get("has_tool_calls", True)

        if (run_tools_failed) and not is_build_failed:
            # Update the message with zero tokens and claude_free model
            messages_data = await self.list_messages(thread_id)
            messages = messages_data["messages"]
            for i, msg in enumerate(messages):
                if msg.get('role') == 'assistant' and msg.get('message_id') == message_id:
                    updated_msg = msg.copy()
                    updated_msg['total_tokens'] = 0
                    updated_msg['model_name'] = 'claude_free'
                    updated_msg['mode'] = model_mode
                    await self.modify_message(thread_id, i, updated_msg)
                    break

        asyncio.create_task(self.process_token_usage(response, model_name, is_build_failed, run_tools_failed, chat_mode ,has_tool_calls, model_mode))

        return {
            "response": response,
            "message_id": message_id,
            "current_build_failed": result.get("build_failed", False),
            "run_tools_failed": run_tools_failed,
            "has_tool_calls": has_tool_calls
        }

    async def handle_response_with_tools(self, thread_id: int, response: Any, project_id: str, message_id: str, iteration_count: int = 0, mode: str = "interactive", websocket: Optional[WebSocket] = None, total_tokens: int = 0, model_name: str = None, model_mode: str = "creative"):
        try:
            # Get the response content directly
            content = response.choices[0].message.content

            # Split content at <actions> tag
            parts = content.split('<actions>')
            content_before_actions = parts[0].strip()
            actions_content = parts[1].split('</actions>')[0].strip() if len(parts) > 1 else ''

            # Parse tool calls from actions content
            tool_calls = self.parse_tool_calls(actions_content)

            has_tool_calls = True

            # If no tool calls, return early
            if not tool_calls:
                has_tool_calls = False
                total_tokens = total_tokens // 2

            # Create assistant message with content before actions
            response_with_actions = content_before_actions + "<actions>" + actions_content + "</actions>"
            reasoning_content = response.choices[0].message.reasoning_content if hasattr(response.choices[0].message, 'reasoning_content') else ''
            assistant_message = {
                "role": "assistant",
                "content": response_with_actions,
                "reasoning_content": reasoning_content,
                "actions": actions_content,
                "message_id": message_id,
                "commit_hash": "pending",
                "total_tokens": total_tokens,
                "model_name": model_name,
                "mode": model_mode
            }

            await self.add_message(thread_id, assistant_message)

            if websocket and not websocket.running_session:
                    return {"status": "stopped", "message": "Session cancelled"}

            result = await self.execute_tools_sync(tool_calls, self.get_available_functions(project_id, thread_id, websocket), thread_id, message_id, iteration_count, mode)
            build_failed = result.get("build_failed", False)
            run_tools_failed = result.get("run_tools_failed", False)

            return {"build_failed": build_failed, "run_tools_failed": run_tools_failed, "has_tool_calls": has_tool_calls}

        except Exception as e:
            logging.error(f"Error in handle_response_with_tools: {str(e)}")
            reasoning_content = response.choices[0].message.reasoning_content if hasattr(response.choices[0].message, 'reasoning_content') else ''
            await self.add_message(thread_id, {
                "role": "assistant",
                "content": response.choices[0].message.content,
                "reasoning_content": reasoning_content,
            })
            return {"build_failed": False}  # Default to False on error

    def create_assistant_message_with_tools(self, response_message: Any) -> Dict[str, Any]:
        # Ensure tool_calls is a list, even if it's None
        tool_calls = response_message.get('tool_calls', [])
        if tool_calls is None:
            tool_calls = []

        return {
            "role": response_message['role'],
            "content": response_message['content'],
            "tool_calls": [
                {
                    "tool_name": tool_call["tool_name"],
                    "parameters": tool_call["parameters"]
                }
                for tool_call in tool_calls
            ]
        }

    def get_available_functions(self, project_id: str, thread_id: int, websocket: Optional[WebSocket] = None) -> Dict[str, Callable]:
        terminal_tool = TerminalTool(self.db, project_id)
        files_tool = FilesTool(self.db, project_id)
        supabase_tool = SupabaseTool(self.db, project_id)
        # web_tool = WebTool(self.db, project_id)
        session_tool = SessionTool(self.db, thread_id, websocket)

        available_functions = {}
        for tool in [terminal_tool, files_tool, session_tool, supabase_tool]:
            for func_name in dir(tool):
                if callable(getattr(tool, func_name)) and not func_name.startswith("__"):
                    available_functions[func_name] = getattr(tool, func_name)
        return available_functions

    async def execute_tools_sync(self, tool_calls, available_functions, thread_id, message_id, iteration_count: int = 0, mode: str = "interactive", websocket: Optional[WebSocket] = None):
        failed_actions = []  # noqa: F841
        success_actions = []  # noqa: F841

        # Set is_executing to true at the start
        messages_data = await self.list_messages(thread_id)
        messages = messages_data["messages"]
        for i, msg in enumerate(messages):
            if msg.get('role') == 'assistant' and msg.get('message_id') == message_id:
                updated_msg = msg.copy()
                updated_msg['is_executing'] = True
                await self.modify_message(thread_id, i, updated_msg)
                break

        run_build_failed = False
        run_tools_failed = False

        try:
            # Separate stop_session, close_files_in_editor from other tool calls
            stop_session_call = None
            close_files_in_editor_call = None
            other_tool_calls = []

            for tool_call in tool_calls:
                if isinstance(tool_call, dict):
                    function_name = tool_call['function']['name']
                    if function_name == 'stop_session':
                        stop_session_call = tool_call
                    elif function_name == 'close_files_in_editor':
                        close_files_in_editor_call = tool_call
                    else:
                        other_tool_calls.append(tool_call)
                else:
                    function_name = tool_call.function.name
                    if function_name == 'stop_session':
                        stop_session_call = tool_call
                    elif function_name == 'close_files_in_editor':
                        close_files_in_editor_call = tool_call
                    else:
                        other_tool_calls.append(tool_call)

            # Check if only close_files_in_editor was called

            # Execute other tool calls
            for tool_call in other_tool_calls:
                if websocket and not websocket.running_session:
                    return {"status": "stopped", "message": "Session cancelled"}

                if isinstance(tool_call, dict):
                    function_name = tool_call['function']['name']
                    function_args = json.loads(tool_call['function']['arguments'])
                    tool_call_id = tool_call['id']
                else:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    tool_call_id = tool_call.id

                function_to_call = available_functions.get(function_name)
                if function_to_call:
                    result = await self.execute_tool(function_to_call, function_args, function_name, tool_call_id)

                    if function_name != 'stop_session' and (websocket and not websocket.running_session):
                        return {"status": "stopped", "message": "Session cancelled"}

                    await self.add_message(thread_id, result['tool_message'])

                    # Update message after tool execution
                    if isinstance(result.get('tool_message', {}).get('content'), str):
                        content = result['tool_message']['content']
                        messages_data = await self.list_messages(thread_id)
                        messages = messages_data["messages"]
                        for i, msg in enumerate(messages):
                            if msg.get('role') == 'assistant' and msg.get('message_id') == message_id:
                                updated_msg = msg.copy()
                                if 'ToolResult(success=False' in content:
                                    updated_failed_actions = updated_msg.get('failed_actions', []).copy()
                                    updated_failed_actions.append({
                                        'function': function_name,
                                        'args': function_args,
                                        'error': content
                                    })
                                    updated_msg['failed_actions'] = updated_failed_actions
                                    print("function_name", function_name)

                                    if function_name == 'check_for_errors' or function_name == 'run_npm_build':
                                        print("check_for_errors", function_name)
                                        run_build_failed = True
                                    else:
                                        run_tools_failed = True

                                elif 'ToolResult(success=True' in content:
                                    updated_success_actions = updated_msg.get('success_actions', []).copy()
                                    updated_success_actions.append({
                                        'function': function_name,
                                        'args': function_args,
                                        'result': content
                                    })
                                    updated_msg['success_actions'] = updated_success_actions
                                await self.modify_message(thread_id, i, updated_msg)
                                break

                    if result.get('communication_failed'):
                        return [result]

            # Execute close_files_in_editor BEFORE stop_session
            if close_files_in_editor_call:
                if isinstance(close_files_in_editor_call, dict):
                    function_name = close_files_in_editor_call['function']['name']
                    function_args = json.loads(close_files_in_editor_call['function']['arguments'])
                    tool_call_id = close_files_in_editor_call['id']
                else:
                    function_name = close_files_in_editor_call.function.name
                    function_args = json.loads(close_files_in_editor_call.function.arguments)
                    tool_call_id = close_files_in_editor_call.id

                function_to_call = available_functions.get(function_name)
                if function_to_call:
                    result = await self.execute_tool(function_to_call, function_args, function_name, tool_call_id)
                    await self.add_message(thread_id, result['tool_message'])

                    # Track the result of close_files_in_editor
                    if isinstance(result.get('tool_message', {}).get('content'), str):
                        content = result['tool_message']['content']
                        messages_data = await self.list_messages(thread_id)
                        messages = messages_data["messages"]
                        for i, msg in enumerate(messages):
                            if msg.get('role') == 'assistant' and msg.get('message_id') == message_id:
                                updated_msg = msg.copy()
                                if 'ToolResult(success=False' in content:
                                    updated_failed_actions = updated_msg.get('failed_actions', []).copy()
                                    updated_failed_actions.append({
                                        'function': function_name,
                                        'args': function_args,
                                        'error': content
                                    })
                                    updated_msg['failed_actions'] = updated_failed_actions
                                    run_tools_failed = True
                                elif 'ToolResult(success=True' in content:
                                    updated_success_actions = updated_msg.get('success_actions', []).copy()
                                    updated_success_actions.append({
                                        'function': function_name,
                                        'args': function_args,
                                        'result': content
                                    })
                                    updated_msg['success_actions'] = updated_success_actions
                                await self.modify_message(thread_id, i, updated_msg)

                    if result.get('communication_failed'):
                        return [result]

            # Execute stop_session last if it exists and conditions are met
            if stop_session_call and not (run_build_failed and iteration_count < 18 and mode == 'autonomous'):
                if isinstance(stop_session_call, dict):
                    function_name = stop_session_call['function']['name']
                    function_args = json.loads(stop_session_call['function']['arguments'])
                    tool_call_id = stop_session_call['id']
                else:
                    function_name = stop_session_call.function.name
                    function_args = json.loads(stop_session_call.function.arguments)
                    tool_call_id = stop_session_call.id

                function_to_call = available_functions.get(function_name)
                if function_to_call:
                    result = await self.execute_tool(function_to_call, function_args, function_name, tool_call_id)
                    await self.add_message(thread_id, result['tool_message'])
                    if result.get('communication_failed'):
                        return [result]

            # After adding messages or modifying them
            if websocket:
                send_thread_update(self.db, websocket, thread_id, websocket.current_user)

            return {
                "status": "success",
                "build_failed": run_build_failed,
                "run_tools_failed": run_tools_failed,
            }

        finally:
            # Set is_executing to false at the end
            messages_data = await self.list_messages(thread_id)
            messages = messages_data["messages"]
            for i, msg in enumerate(messages):
                if msg.get('role') == 'assistant' and msg.get('message_id') == message_id:
                    updated_msg = msg.copy()
                    updated_msg['is_executing'] = False
                    await self.modify_message(thread_id, i, updated_msg)
                    break

    async def execute_tool(self, function_to_call, function_args, function_name, tool_call_id):
        try:
            function_response = await function_to_call(**function_args)

            # Handle None response
            if function_response is None:
                error_message = f"Tool {function_name} returned None"
                logging.error(error_message)
                function_response = ToolResult(success=False, output=error_message)

            # Check if the function is communicate_with_user and the response is a failure
            communication_failed = function_name == "communicate_with_user" and isinstance(function_response, ToolResult) and not function_response.success

        except Exception as e:
            error_message = f"Error in {function_name}: {str(e)}"
            logging.error(error_message, exc_info=True)
            function_response = ToolResult(success=False, output=error_message)
            communication_failed = False

        # tool_message = {
        #     "role": "tool",
        #     "tool_call_id": tool_call_id,
        #     "name": function_name,
        #     "content": str(function_response),
        # }

        tool_message = {
            "role": "assistant",
            "content": f"{function_name}: {str(function_response)}"
        }

        return {
            "tool_message": tool_message,
            "communication_failed": communication_failed
        }


    async def get_project_id_by_thread(self, thread_id: int) -> str:
        async with self.db.get_async_session() as session:
            thread = await session.get(ProjectThread, thread_id)
            if not thread:
                raise ValueError(f"Thread with id {thread_id} not found")
            return thread.project_id

    async def get_thread(self, thread_id: int) -> Optional[ProjectThread]:
        async with self.db.get_async_session() as session:
            return await session.get(ProjectThread, thread_id)

    # matches:
    # <update_file_sections file_path="path/to/file">file contents</update_file_sections>
    # <update_file_sections file_path="path/to/file" file_updates="file updates"></update_file_sections>
    # <update_file_sections file_path="path/to/file" file_updates="file updates" />
    UPDATE_FILE_SECTIONS_PATTERN = r'<update_file_sections\s+.*?(?:>.*?</update_file_sections>|/>)'
    UPDATE_FILE_SECTIONS_CONTENT_PATTERN = re.compile(
        r'<update_file_sections[^>]*>([\s\S]*?)(?=</update_file_sections>)',
        re.DOTALL
    )

    # Generic attribute pattern for extracting attribute values
    ATTRIBUTE_PATTERN = r'{}=["\'](.*?)["\']'

    def parse_tool_calls(self, content: str) -> List[Dict[str, Any]]:
        """Parse XML-style tool calls from content string."""
        # Initialize tool_calls list at the start
        tool_calls = []

        # Updated regex pattern for create_file, update_file, full_file_rewrite
        file_pattern = r'<(create_file|update_file|full_file_rewrite)\s+file_path="([^"]+)">([\s\S]*?)(?=<\/\1>)'

        # Find all matches
        matches = re.finditer(file_pattern, content)
        for match in matches:
            action_type = match.group(1)  # create_file or update_file or full_file_rewrite
            file_path = match.group(2)    # file path
            file_contents = match.group(3) # exact content between tags

            tool_calls.append({
                "id": str(uuid.uuid4()),
                "type": "function",
                "function": {
                    "name": action_type,
                    "arguments": json.dumps({
                        "file_path": file_path,
                        "file_contents": file_contents
                    })
                }
            })

        patterns = {
            # 'create_file': (
            #     r'<create_file\s+file_path="([^"]+)">(.*?)</create_file>',
            #     lambda m: {
            #         "file_path": m.group(1),
            #         "file_contents": m.group(2).strip()
            #     }
            # ),
            # 'update_file': (
            #     r'<update_file\s+file_path="([^"]+)">(.*?)</update_file>',
            #     lambda m: {
            #         "file_path": m.group(1),
            #         "file_contents": m.group(2).strip()
            #     }
            # ),
            'replace_string': (
                r'<replace_string\s+file_path="([^"]+)"\s+old_str=[\'"](.*?)[\'"]'
                r'\s+new_str=[\'"](.*?)[\'"]\s*>(.*?)</replace_string>',
                lambda m: {
                    "file_path": m.group(1),
                    "old_str": m.group(2).replace('\\n', '\n'),
                    "new_str": m.group(3).replace('\\n', '\n')
                }
            ),
            'update_file_sections': (
                self.UPDATE_FILE_SECTIONS_PATTERN,
                lambda m: self._parse_update_file_sections(m.group(0))
            ),
            'execute_sql_query': (
                r'<execute_sql_query(?:\s+query="([^"]+)")?\s*/?>(.*?)</execute_sql_query>',
                lambda m: {
                    "sql_query": m.group(1) if m.group(1) else m.group(2).strip()
                }
            ),
            'get_database_schema': (
                r'<get_database_schema\s*/?>(.*?)</get_database_schema>|<get_database_schema\s*/?>',
                lambda m: {}
            ),
            'visit_and_check_page_errors': (
                r'<visit_and_check_page_errors\s+page_routes="([^"]+)"\s*/?>(.*?)</visit_and_check_page_errors>',
                lambda m: {
                    "page_routes": [route.strip() for route in m.group(1).split(",")]
                }
            ),
            'communicate_with_user': (
                r'<communicate_with_user\s+prompt="([^"]+)"\s*/?>(.*?)</communicate_with_user>',
                lambda m: {
                    "prompt": m.group(1)
                }
            ),
            'delete_file': (
                r'<delete_file\s+file_path="([^"]+)"\s*/?>(.*?)</delete_file>',
                lambda m: {
                    "file_path": m.group(1)
                }
            ),
           'send_terminal_command': (
                r'<send_terminal_command(?:\s+command="([^"]+)")?\s*/?>(.*?)</send_terminal_command>',
                lambda m: {
                    "command": m.group(1) if m.group(1) else m.group(2).strip()
                }
            ),
            'visit_page_and_evaluate_visually': (
                r'<visit_page_and_evaluate_visually\s+page_evaluations="([^"]+)"\s*/?>(.*?)</visit_page_and_evaluate_visually>',
                lambda m: {
                    "page_evaluations": m.group(1)
                }
            ),
            'stop_session': (
                r'<stop_session\s+message="([^"]+)"\s*/?>(.*?)</stop_session>',
                lambda m: {
                    "message": m.group(1)
                }
            ),
            'run_npm_build': (
                r'<run_npm_build\s*/?>(.*?)</run_npm_build>|<run_npm_build\s*/?>',
                lambda m: {}
            ),
            'check_for_errors': (
                r'<check_for_errors(?:\s+with_build="(true|false)")?\s*/?>(.*?)</check_for_errors>|<check_for_errors(?:\s+with_build="(true|false)")?\s*/?>',
                lambda m: {
                    "with_build": m.group(1) == "true" if m.group(1) else False
                }
            ),
            'reset_next_server': (
                r'<reset_next_server\s*/?>(.*?)</reset_next_server>|<reset_next_server\s*/?>',
                lambda m: {}
            ),
            'go_to_browser_page': (
                r'<go_to_browser_page\s+page_route="([^"]+)"\s*/?>(.*?)</go_to_browser_page>',
                lambda m: {
                    "page_route": m.group(1)
                }
            ),
            'act_on_current_browser_page': (
                r'<act_on_current_browser_page\s+action="([^"]+)"\s*/?>(.*?)</act_on_current_browser_page>',
                lambda m: {
                    "action": m.group(1)
                }
            ),

            'observe_page_visually': (
                r'<observe_page_visually\s+page_route="([^"]+)"\s+observation_instruction="([^"]+)"\s*/?>(.*?)</observe_page_visually>',
                lambda m: {
                    "page_route": m.group(1),
                    "observation_instruction": m.group(2)
                }
            ),
            'open_files_in_editor': (
                r'<open_files_in_editor\s+files=\[(.*?)\]\s*/?>(.*?)</open_files_in_editor>',
                lambda m: {
                    "files": [
                            f.strip().strip('"').strip("'")
                            for f in m.group(1).split(",")
                        ]
                }
            ),
            'close_files_in_editor': (
                r'<close_files_in_editor\s+files=\[(.*?)\]\s*/?>(.*?)</close_files_in_editor>',
                lambda m: {
                    "files": [
                            f.strip().strip('"').strip("'")
                            for f in m.group(1).split(",")
                        ]
                }
            )
        }

        # First, normalize newlines and escape sequences
        content = content.replace('\\n', '\n').replace('\\"', '"')

        # Track the current position in the content
        pos = 0
        while pos < len(content):
            # Find the next opening tag
            next_tag_start = content.find('<', pos)
            if next_tag_start == -1:
                break

            # Find the corresponding closing tag
            tag_end = content.find('>', next_tag_start)
            if tag_end == -1:
                break

            # Extract the tag name
            tag_content = content[next_tag_start:tag_end+1]
            tag_name = tag_content.split()[0][1:].rstrip('>').rstrip('/')

            # print(f"\nFound tag: {tag_name}")  # Debug output

            # Skip closing tags
            if tag_name.startswith('/'):
                pos = tag_end + 1
                continue

            if tag_name in patterns:
                pattern, args_processor = patterns[tag_name]
                # Look for the complete tag pattern starting from this position
                match = re.search(pattern, content[next_tag_start:], re.DOTALL)
                if match:
                    try:
                        args = args_processor(match)
                        # Clean up the content
                        if 'file_contents' in args:
                            args['file_contents'] = args['file_contents'].strip()

                        tool_calls.append({
                            "id": str(uuid.uuid4()),
                            "type": "function",
                            "function": {
                                "name": tag_name,
                                "arguments": json.dumps(args)
                            }
                        })
                        # print(f"Added tool call: {tag_name}")  # Debug output
                        # Move position past this tag
                        pos = next_tag_start + len(match.group(0))
                        continue
                    except Exception as e:
                        logging.error(f"Error processing {tag_name} match: {str(e)}")

            # If we didn't find a valid tag, move past this position
            pos = tag_end + 1

        return tool_calls

    def _parse_update_file_sections(self, tag_str: str) -> dict:
        """Parse content from a single update_file_sections tag and return a dict.

        Returns a dict with file_path and file_updates keys.

        Possible tag formats:
        - <update_file_sections file_path="path/to/file">file contents</update_file_sections>
        - <update_file_sections file_path="path/to/file" file_updates="file updates"></update_file_sections>
        - <update_file_sections file_path="path/to/file" file_updates="file updates" />

        Returns:
            A dict with file_path and file_updates keys

        Raises:
            ValueError: If parsing fails or if either file_path or file_updates are empty
        """
        try:
            result = {}

            # Extract file_path attribute
            file_path_pattern = self.ATTRIBUTE_PATTERN.format('file_path')
            file_path_match = re.search(file_path_pattern, tag_str)
            if file_path_match:
                result['file_path'] = file_path_match.group(1)
            else:
                raise ValueError("Missing required file_path attribute in update_file_sections tag")

            # First check for file_updates attribute
            file_updates_pattern = self.ATTRIBUTE_PATTERN.format('file_updates')
            file_updates_match = re.search(file_updates_pattern, tag_str, re.DOTALL)

            if file_updates_match:
                # If file_updates attribute exists, use it
                result['file_updates'] = file_updates_match.group(1)
            else:
                # Otherwise, try to extract content between tags
                content_match = self.UPDATE_FILE_SECTIONS_CONTENT_PATTERN.search(tag_str)
                if content_match:
                    result['file_updates'] = content_match.group(1)

            if not result.get('file_updates'):
                raise ValueError("Missing required file_updates content in update_file_sections tag")

            return result
        except Exception as e:
            raise ValueError(f"Failed to parse update_file_sections tag: {str(e)}")

    def clean_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove metadata fields from messages."""
        for msg in messages:
            msg.pop('success_actions', None)
            msg.pop('failed_actions', None)
            msg.pop('message_id', None)
            msg.pop('commit_hash', None)
            msg.pop('is_executing', None)
            msg.pop('total_tokens', None)
            msg.pop('model_name', None)
            msg.pop('timestamp', None)
            msg.pop('mode', None)
        return messages

    async def process_token_usage(self, response: Dict[str, Any], model_name: Optional[str] = None, is_build_failed: bool = False, run_tools_failed: bool = False, chat_mode: bool = False, has_tool_calls: bool = True, model_mode: str = "creative") -> None:
        """Process token usage for billing in the background and save LLM usage record."""
        try:
            if is_build_failed:
                return
            # if (not tools_executed or run_tools_failed) and not chat_mode:
            #     return
            if (run_tools_failed) and not chat_mode:
                return
            try:
                input_tokens = response['usage']['prompt_tokens']
                output_tokens = response['usage']['completion_tokens']
                total_tokens = input_tokens + output_tokens
            except (KeyError, TypeError):
                logging.warning("Could not extract token usage from response, using default value")
                input_tokens = 0
                output_tokens = 0
                total_tokens = 15000  # Default to 15k tokens on error

            # Only add git commit tokens if not a file opening response
            response_content = response.choices[0].message.content if hasattr(response, 'choices') else ''
            if '<open_files_in_editor' not in response_content:
                git_commit_tokens = 3000
                total_tokens = total_tokens + git_commit_tokens

            # Adjust token count for deepseek models
            if model_name and ('deepseek' in model_name or 'openai' in model_name):
                total_tokens = total_tokens // 15  # Integer division to avoid decimals

            if chat_mode or not has_tool_calls:
                total_tokens = total_tokens // 2

            # Save LLM usage record
            if total_tokens > 0 and self.user and self.project:
                async with self.db.get_async_session() as session:
                    llm_record = LLMRecord(
                        user_id=self.user.id,
                        project_id=self.project.project_id,
                        model=model_name,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        mode="chat" if chat_mode else model_mode,
                    )
                    session.add(llm_record)
                    await session.commit()

            # logging.info(f"Token usage - Total: {total_tokens} for model: {model_name}")

            # Process token usage for billing
            if total_tokens > 0:
                # Fetch the latest user details by email before processing
                if self.user and self.user.email:
                    updated_user = await self.db.user_service.get_user_by_email(self.user.email)
                    if updated_user:
                        self.user = updated_user

                if self.user.isRequestBased:
                    if '<open_files_in_editor' not in response_content:
                        await update_request_limit(self.user, self.project)
                else:
                    if self.user.free_total_token > 0:
                        if self.user.free_total_token >= total_tokens:
                            # If we have enough free tokens, just use them
                            self.user.free_total_token -= total_tokens
                            await self.db.user_service.update_user_free_tokens(self.user.kinde_id, self.user.free_total_token)
                        else:
                            # Use all remaining free tokens and record usage for the rest if token_event_name exists
                            remaining_tokens = total_tokens - self.user.free_total_token
                            self.user.free_total_token = 0
                            await self.db.user_service.update_user_free_tokens(self.user.kinde_id, 0)
                            if self.user.token_event_name:
                                await record_usage(self.user.stripe_customer_id, remaining_tokens, self.user.token_event_name)
                    else:
                        # No free tokens, record usage only if token_event_name exists
                        if self.user.token_event_name:
                            await record_usage(self.user.stripe_customer_id, total_tokens, self.user.token_event_name)
        except Exception as e:
            logging.error(f"Error processing token usage: {str(e)}")

    async def get_previous_thread_messages(self, project_id: str, current_thread_id: int, limit: int = 4):
        """
        Fetch only assistant messages from previous threads until we have enough or reach 2nd last thread.
        Includes success_actions, failed_actions, and tool_calls from messages.
        """
        async with self.db.get_async_session() as session:
            collected_messages = []
            threads_checked = 0

            stmt = (
                select(ProjectThread)
                .where(
                    ProjectThread.project_id == project_id,
                    ProjectThread.thread_id < current_thread_id
                )
                .order_by(ProjectThread.thread_id.desc())
                .limit(3)
            )
            result = await session.execute(stmt)
            previous_threads = result.scalars().all()

            for thread in previous_threads:
                if len(collected_messages) >= limit or threads_checked >= 3:
                    break

                try:
                    messages = json.loads(thread.messages)

                    # Format messages similar to list_messages but include success/failed actions and tool_calls
                    formatted_messages = [
                        {
                            "role": msg.get('role'),
                            "content": msg.get('content'),
                            "success_actions": msg.get('success_actions', []),
                            "failed_actions": msg.get('failed_actions', []),
                            "actions": msg.get('actions', ''),
                            "tool_calls": msg.get('tool_calls', []) if msg.get('tool_calls') else []
                        }
                        for msg in messages
                        if msg.get('role') == 'assistant'
                        and not 'ToolResult' in str(msg.get('content', ''))  # noqa: E713
                    ]

                    # Calculate how many more messages we need
                    remaining_needed = limit - len(collected_messages)

                    # Add only the needed number of messages
                    if remaining_needed > 0:
                        collected_messages.extend(formatted_messages[-remaining_needed:])

                except Exception as e:
                    logging.error(f"Error processing thread {thread.thread_id}: {str(e)}")

                threads_checked += 1

            # logging.info(f"Found {len(collected_messages)} formatted messages")
            return collected_messages[-limit:]  # Return only up to limit messages

    async def get_editor_context(self, files_tool: FilesTool) -> str:
        """Get context about the current state of the code editor including file contents"""
        try:
            open_files = await files_tool.get_open_files()

            if not open_files:
                return "\nCode Editor Status: No files are currently open in the Visual Studio Code editor workspace."

            context = "\nCode Editor Workspace Status:"
            context += "\n----------------------------------------"
            context += f"\nActive Editor Tabs ({len(open_files)} files currently open in VS Code):"
            if len(open_files) > 4:
                context += "\nNote: Multiple files are open. Consider closing unused files to maintain a focused workspace."
            context += "\n----------------------------------------\n"

            for file in open_files:
                if isinstance(file, dict):
                    path = file.get("path", "unknown")
                    last_modified = file.get("last_modified", "unknown")
                    content = file.get("content", "")

                    context += f"\n=== VS Code Tab: {path} ==="
                    context += f"\nLast Modified: {last_modified}"
                    context += "\nFile Contents:"
                    context += "\n```"
                    # Language-specific syntax highlighting
                    if path.endswith(('.ts', '.tsx', '.js', '.jsx')):
                        context += "typescript"
                    elif path.endswith(('.css', '.scss')):
                        context += "css"
                    elif path.endswith('.html'):
                        context += "html"
                    elif path.endswith('.py'):
                        context += "python"
                    context += "\n"
                    context += content
                    context += "\n```\n"
                    context += "----------------------------------------\n"

            return context

        except Exception as e:
            logging.error(f"Error getting editor context: {str(e)}")
            return "\nCode Editor Status: Unable to retrieve information about open files in VS Code workspace."

    async def format_previous_context(self, messages: List[Dict[str, Any]]) -> str:
        """
        Format previous messages into a readable context string.

        Args:
            messages: List of previous message dictionaries

        Returns:
            Formatted context string
        """
        if not messages:
            return ""

        context = "\nContext from previous actions:\n"

        for msg in messages:
            # Add tool calls
            if msg.get('tool_calls'):
                context += "\nTool calls:\n"
                for tool_call in msg['tool_calls']:
                    context += (
                        f"- Function: {tool_call['function']['name']}\n"
                        f"  Arguments: {tool_call['function']['arguments']}\n"
                    )

            # Add successful actions
            if msg.get('success_actions'):
                context += "\nSuccessful actions:\n"
                for action in msg['success_actions']:
                    context += (
                        f"- {action['function']} with args {action['args']}\n"
                        f"  Result: {action['result']}\n"
                    )

            # Add failed actions
            if msg.get('failed_actions'):
                context += "\nFailed actions:\n"
                for action in msg['failed_actions']:
                    context += (
                        f"- {action['function']} with args {action['args']}\n"
                        f"  Error: {action['error']}\n"
                    )

        return context




    async def send_websocket_message(self, websocket: WebSocket, message_type: str, data: str = None,
                                   is_complete: bool = True, termination: bool = True):
        """
        Utility function to send formatted websocket messages.
        """
        if websocket:
            try:
                message = {
                    "type": message_type,
                    "isCompleteMessage": is_complete,
                    "termination": termination
                }

                # Add optional fields based on message type
                if message_type == "error":
                    message["data"] = f"Error: {str(data)}"
                    websocket.running_session = False  # Reset running flag on error
                elif message_type == "session_status":
                    message["status"] = data if data else "stopped"

                await websocket.send_json(message)
            except Exception as e:
                logging.error(f"Error sending websocket message: {str(e)}")
                if websocket:
                    websocket.running_session = False  # Ensure running_session is False on error

    async def send_error_message(self, websocket: WebSocket, error: str):
        """Utility function to send error messages."""
        if websocket:
            websocket.running_session = False  # Reset running flag on error
        await self.send_websocket_message(websocket, "error", error)

    async def send_status_message(self, websocket: WebSocket, status: str = "stopped",
                                is_complete: bool = True, termination: bool = True):
        """Utility function to send status messages."""
        if status == "stopped" and websocket:
            websocket.running_session = False  # Reset running flag when stopping
        await self.send_websocket_message(websocket, "session_status", status, is_complete, termination)
