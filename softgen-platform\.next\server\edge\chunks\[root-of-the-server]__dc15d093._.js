(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
async function middleware(request) {
    const path = request.nextUrl.pathname;
    const access_token = request.cookies.get("access_token")?.value;
    const kinde_id = request.cookies.get("kinde_id")?.value;
    // Detect country from Vercel geolocation headers
    const detectedCountry = request.headers.get("x-vercel-ip-country") || "US";
    const currentCountryCookie = request.cookies.get("detected_country")?.value;
    const privatePathPatterns = [
        /^\/app($|\/.*)/,
        /^\/project($|\/.*)/,
        /^\/settings($|\/.*)/,
        /^\/profile($|\/.*)/,
        /^\/integration($|\/.*)/,
        /^\/test\/[^\/]+$/,
        /^\/admin($|\/.*)/
    ];
    const isPrivatePath = privatePathPatterns.some((pattern)=>pattern.test(path));
    if (!access_token && isPrivatePath) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL("/", request.url));
    }
    if (access_token && [
        "/signin",
        "/register"
    ].includes(path)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL("/app", request.url));
    }
    if (access_token && isPrivatePath) {
        if (!kinde_id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL("/pricing", request.url));
        }
        try {
            const apiUrl = `${("TURBOPACK compile-time value", "http://localhost:8000")}/kinde_user/${kinde_id}`;
            const response = await fetch(apiUrl, {
                cache: "no-store",
                headers: {
                    Authorization: `Bearer ${access_token}`,
                    "Content-Type": "application/json"
                },
                mode: "cors",
                credentials: "include"
            });
            if (!response.ok) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL("/pricing", request.url));
            }
            const userData = await response.json();
            if (!userData.plan && userData.total_free_request <= 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL("/pricing", request.url));
            }
        } catch (error) {
            console.error("error", error);
        // return NextResponse.redirect(new URL("/pricing", request.url));
        }
    }
    // Set country cookie if not already set or if country has changed
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    if (!currentCountryCookie || currentCountryCookie !== detectedCountry) {
        response.cookies.set("detected_country", detectedCountry, {
            httpOnly: false,
            secure: ("TURBOPACK compile-time value", "development") === "production",
            sameSite: "lax",
            maxAge: 60 * 60 * 24 * 7
        });
    }
    return response;
}
const config = {
    matcher: [
        "/((?!_next|api|static|favicon.ico).*)"
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map