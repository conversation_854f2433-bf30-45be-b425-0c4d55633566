import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Loading from "@/components/ui/loading";
import { TextShimmer } from "@/components/ui/text-shimmer";
import Typography from "@/components/ui/typography";
import { getFileContent, gitCommit, updateFile } from "@/lib/api";
import { cn } from "@/lib/utils";
import { useNavigateFile } from "@/stores/navigate-file";
import type { BeforeMount, EditorProps, OnMount } from "@monaco-editor/react";
import { File } from "@mynaui/icons-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { editor } from "monaco-editor";
import { useTheme } from "next-themes";
import dynamic from "next/dynamic";
import path from "path";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import { TbLayoutSidebar, TbLayoutSidebarFilled } from "react-icons/tb";
import { useShallow } from "zustand/react/shallow";
import Hint from "../../../components/ui/hint";
import { errorToast, loadingToast } from "../../global/toast";
import FileUpdateModal from "./file-update-modal";
import { FileStatus } from "./types";

const Editor = dynamic(() => import("@monaco-editor/react"), { ssr: false });

const darkThemeData = {
  base: "vs-dark" as const,
  inherit: true,
  rules: [
    { token: "comment", foreground: "eef0f98f" },
    { token: "keyword", foreground: "54b9ff" },
    { token: "string", foreground: "ffd493" },
    { token: "number", foreground: "ffd493" },
    { token: "operator", foreground: "eef0f9" },
    { token: "identifier", foreground: "4bf3c8" },
    { token: "type", foreground: "acafff" },
    { token: "function", foreground: "00daef" },
  ],
  colors: {
    "editor.background": "#111115",
    "editor.foreground": "#eef0f9",
    "editorCursor.foreground": "#aeafad",
    "editor.lineHighlightBackground": "#23262d",
    "editorLineNumber.foreground": "#545864",
    "editor.selectionBackground": "#ad5dca44",
    "editor.inactiveSelectionBackground": "#2a2d34",
    "editorIndentGuide.background": "#343841",
    "editor.selectionHighlightBackground": "#add6ff34",
    "editor.wordHighlightBackground": "#494949b8",
    "editorBracketMatch.background": "#545864",
    "editorBracketMatch.border": "#ffffff00",
  },
};

const lightThemeData = {
  base: "vs" as const,
  inherit: true,
  rules: [
    { token: "comment", foreground: "6a7280" },
    { token: "keyword", foreground: "0077cc" },
    { token: "string", foreground: "d97706" },
    { token: "number", foreground: "d97706" },
    { token: "operator", foreground: "111827" },
    { token: "identifier", foreground: "059669" },
    { token: "type", foreground: "6366f1" },
    { token: "function", foreground: "0891b2" },
    { token: "variable", foreground: "111827" },
    { token: "constant", foreground: "0369a1" },
    { token: "regexp", foreground: "be123c" },
    { token: "entity.name.tag", foreground: "7f1d1d" },
    { token: "entity.other.attribute-name", foreground: "b91c1c" },
    { token: "storage", foreground: "1e40af" },
    { token: "keyword.control", foreground: "7e22ce" },
  ],
  colors: {
    "editor.background": "#fafafa",
    "editor.foreground": "#000000",
    "editorCursor.foreground": "#000000",
    "editor.lineHighlightBackground": "#F8F8F8",
    "editorLineNumber.foreground": "#767676",
    "editor.selectionBackground": "#ADD6FF",
    "editor.inactiveSelectionBackground": "#E5EBF1",
    "editorIndentGuide.background1": "#D3D3D3",
    "editorIndentGuide.activeBackground1": "#939393",
    "editor.selectionHighlightBackground": "#ADD6FF80",
    "editor.wordHighlightBackground": "#E8E8E8",
    "editorBracketMatch.background": "#C9DDFC",
    "editorBracketMatch.border": "#2760BB",
    "sideBarSectionHeader.background": "#0000",
    "sideBarSectionHeader.border": "#61616130",
    "sideBarTitle.foreground": "#6F6F6F",
  },
};

const EDITOR_OPTIONS: EditorProps["options"] = {
  minimap: { enabled: false, showSlider: "always", size: "proportional" },
  fontFamily: "monospace",
  fontSize: 13,
  tabCompletion: "on",
  readOnlyMessage: { value: "Agent is running... Please wait" },
  lineNumbers: "on",
  padding: { top: 10, bottom: 10 },
  scrollBeyondLastLine: false,
  automaticLayout: true,
  formatOnPaste: true,
  formatOnType: true,
  folding: true,
  foldingHighlight: true,
  renderLineHighlight: "all",
  cursorBlinking: "blink",
  cursorSmoothCaretAnimation: "on",
  cursorWidth: 1,
  smoothScrolling: true,
  mouseWheelScrollSensitivity: 3,
  fastScrollSensitivity: 20,
  wordWrap: "on",
  bracketPairColorization: {
    enabled: true,
  },
};

const LANGUAGES_BY_FILE_EXTENSION = {
  ts: "typescript",
  tsx: "typescript",
  js: "javascript",
  jsx: "javascript",
  cjs: "javascript",
  mjs: "javascript",
  css: "css",
  json: "json",
  yml: "yaml",
  yaml: "yaml",
  md: "markdown",
  sql: "sql",
};

const configureMonaco: BeforeMount = (monaco) => {
  monaco.editor.defineTheme("custom-dark", darkThemeData);
  monaco.editor.defineTheme("custom-light", lightThemeData);

  try {
    monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: true,
      noSyntaxValidation: true,
      noSuggestionDiagnostics: true,
    });

    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      allowNonTsExtensions: true,
      allowJs: true,
      checkJs: false,
      noUnusedLocals: false,
      noUnusedParameters: false,
      strict: false,
      target: monaco.languages.typescript.ScriptTarget.Latest,
    });
  } catch (error) {
    console.warn("Error configuring Monaco editor:", error);
  }
};

const Breadcrumbs = memo(({ file, isMobile }: { file: string; isMobile?: boolean }) => {
  return (
    <Breadcrumb className="truncate px-4 pb-2 pt-1.5 text-sm font-normal text-muted-foreground md:py-1.5">
      <BreadcrumbList>
        {file.split("/").map((part, index, array) => {
          // If mobile and there are more than 3 parts, show only the last part
          if (isMobile && array.length > 3) {
            if (index === array.length - 1) {
              return (
                <React.Fragment key={index}>
                  <BreadcrumbItem>
                    <BreadcrumbPage className="font-medium text-foreground">{part}</BreadcrumbPage>
                  </BreadcrumbItem>
                </React.Fragment>
              );
            }

            if (index === 0) {
              return (
                <React.Fragment key={index}>
                  <BreadcrumbItem>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-6 px-1 text-xs">
                          ...
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start" className="w-56">
                        {array.slice(0, array.length - 1).map((middlePart, middleIndex) => (
                          <DropdownMenuItem key={middleIndex}>{middlePart}</DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="mt-0.5" />
                </React.Fragment>
              );
            }
            return null;
          }

          return (
            <React.Fragment key={index}>
              {index > 0 && <BreadcrumbSeparator className="mt-0.5" />}
              <BreadcrumbItem>
                {index === array.length - 1 ? (
                  <BreadcrumbPage className="font-medium text-foreground">{part}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink>{part}</BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
});

Breadcrumbs.displayName = "TextEditorBreadcrumbs";

const TextEditor = (props: {
  isAgentRunning: boolean;
  projectId: string;
  file: string;
  isMobile?: boolean;
  className?: string;
}) => {
  const { theme } = useTheme();
  const queryClient = useQueryClient();
  const { showFileTree, setShowFileTree } = useNavigateFile(
    useShallow((state) => ({
      showFileTree: state.showFileTree,
      setShowFileTree: state.setShowFileTree,
    })),
  );

  const [showGitDialog, setShowGitDialog] = useState(false);
  const [fileStatus, setFileStatus] = useState<FileStatus>("no-changes");

  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const currentFileRef = useRef<string | null>(null);
  const contentRef = useRef<string | undefined>(undefined);

  const { data: content, isLoading } = useQuery({
    queryKey: ["fileContent", props.projectId, props.file],
    queryFn: () => getFileContent(props.projectId, props.file),
    enabled: !!props.projectId && !!props.file,
    staleTime: 1000 * 30,
    gcTime: 1000 * 60,
  });

  // Handle content and file changes
  useEffect(() => {
    contentRef.current = content;

    if (!editorRef.current) return;

    // Clear editor when loading or no content
    if (isLoading || !content) {
      editorRef.current.setValue("");
      return;
    }

    const isNewFile = props.file !== currentFileRef.current;

    if (isNewFile) {
      currentFileRef.current = props.file;
      setFileStatus("no-changes");
    }

    const currentValue = editorRef.current.getValue();

    if (isNewFile || currentValue !== content) {
      editorRef.current.setValue(content || "");
    }
  }, [content, props.file, isLoading]);

  // Handle editor mount
  const handleEditorDidMount: OnMount = useCallback((editor) => {
    editorRef.current = editor;

    if (contentRef.current) {
      editor.setValue(contentRef.current);
    }
  }, []);

  // Handle save content mutation
  const { mutate: saveContent, isPending: isSaving } = useMutation({
    mutationFn: (content: string) => {
      return updateFile(props.projectId, props.file, content);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["fileContent", props.projectId, props.file],
      });
      setFileStatus("saved");
      setShowGitDialog(true);
    },
    onError: (error) => {
      errorToast(
        `Failed to save file: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    },
  });

  // Handle commit changes mutation
  const { mutate: commitChanges, isPending: isCommitting } = useMutation({
    mutationFn: () => {
      return loadingToast(
        "Committing changes...",
        gitCommit(props.projectId, `Edit ${props.file}`),
      );
    },
    onError: (error) => {
      errorToast(
        `Failed to commit changes: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    },
  });

  const handleSave = useCallback(() => {
    if (editorRef.current) {
      const content = editorRef.current.getValue();
      saveContent(content);
    }
  }, [saveContent]);

  const handleGitCommit = useCallback(() => {
    commitChanges();
  }, [commitChanges]);

  const handleDiscard = useCallback(() => {
    if (content !== undefined) {
      editorRef.current?.setValue(content);
      setFileStatus("no-changes");
    }
  }, [content, editorRef, setFileStatus]);

  const handleEditorChange = useCallback(() => {
    if (!editorRef.current) return;
    const currentValue = editorRef.current.getValue();
    const isChanged = currentValue !== (content || "");
    setFileStatus(isChanged ? "unsaved" : "no-changes");
  }, [content]);

  // cleanup on unmount
  useEffect(() => {
    return () => {
      if (editorRef.current) {
        editorRef.current.dispose();
        editorRef.current = null;
      }
    };
  }, []);

  if (!props.file) {
    return (
      <Card
        className={cn(
          "flex h-full w-full flex-col items-center justify-center rounded-none border-none bg-transparent shadow-none",
        )}
        border={false}
      >
        <CardContent className="flex w-full max-w-xl flex-col items-center justify-center space-y-6 p-6">
          <div className="flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2">
            <File className="size-8 text-muted-foreground" />
          </div>

          <div className="flex items-center gap-4">
            <div className="space-y-1.5 text-center">
              <Typography.H4>No file selected</Typography.H4>
              <Typography.P className="mt-0 text-balance leading-normal">
                Please select a file to edit.
              </Typography.P>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const fileExtension = path.extname(props.file || "").slice(1);
  const language =
    LANGUAGES_BY_FILE_EXTENSION[fileExtension as keyof typeof LANGUAGES_BY_FILE_EXTENSION] ||
    "plaintext";
  const editorTheme = theme === "dark" ? "custom-dark" : "custom-light";
  const readOnlyEditor = props.isAgentRunning || isLoading;

  return (
    <div className={cn("relative flex h-full w-full flex-col overflow-hidden", props.className)}>
      <div
        className={cn(
          "flex items-center justify-between py-1.5 text-sm font-medium md:border-b md:border-t-0",
        )}
      >
        <div className="flex w-full items-center justify-between md:pl-2">
          <Hint label={showFileTree ? "Hide File Tree" : "Show File Tree"} side="bottom">
            <Button
              size="icon"
              className="hidden rounded-lg md:flex"
              variant="ghost"
              onClick={() => setShowFileTree(!showFileTree)}
            >
              {showFileTree ? (
                <TbLayoutSidebarFilled className="h-4 w-4" />
              ) : (
                <TbLayoutSidebar className="h-4 w-4" />
              )}
            </Button>
          </Hint>

          <Breadcrumbs file={props.file} isMobile={props.isMobile} />

          <div className="flex items-center gap-2 py-1 pr-2 md:px-0 md:py-0">
            {props.isAgentRunning && (
              <Badge variant="terminal" className="flex items-center gap-2">
                <Loading className="size-3 animate-spin" />
                Agent is running...
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 tracking-wider">
        {isLoading && (
          <div className="flex h-full w-full items-center justify-center bg-background text-muted-foreground">
            <div className="flex items-center gap-2">
              <TextShimmer className="text-base font-normal">Fetching file content...</TextShimmer>
              <Loading className="size-4 animate-spin" />
            </div>
          </div>
        )}

        <Editor
          height="100%"
          language={language}
          className={cn("h-full pb-5", isLoading && "hidden")}
          theme={editorTheme}
          onMount={handleEditorDidMount}
          beforeMount={configureMonaco}
          onChange={handleEditorChange}
          options={{
            ...EDITOR_OPTIONS,
            readOnly: readOnlyEditor,
          }}
        />
      </div>

      {!props.isAgentRunning && (
        <FileUpdateModal
          side="right"
          fileStatus={fileStatus ?? "saved"}
          onReset={handleDiscard}
          onSave={handleSave}
          isSaving={isSaving}
          isMobile={props.isMobile}
        />
      )}

      <AlertDialog open={showGitDialog} onOpenChange={setShowGitDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Commit Changes to Git?</AlertDialogTitle>
            <AlertDialogDescription>
              Do you want to commit these changes to the Git version history?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex items-center justify-end">
            <AlertDialogCancel className="w-full md:w-fit" onClick={() => setShowGitDialog(false)}>
              No
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleGitCommit}
              disabled={isCommitting}
              className="h-8 w-full md:w-fit"
            >
              {isCommitting ? (
                <div className="flex items-center gap-2">
                  <Loading className="size-4 animate-spin" />
                  <span>Committing...</span>
                </div>
              ) : (
                "Yes, Commit"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

TextEditor.displayName = "TextEditor";

export default TextEditor;
