"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loading from "@/components/ui/loading";
import {
  ModalContentInner,
  ModalDescription,
  ModalFooter,
  ModalHeader,
  ModalTitle,
} from "@/components/ui/modal";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import SupabaseIcon from "@/features/icon/supabase";
import { ProjectTypeContent } from "@/features/new/project-tabs";
import { createProject } from "@/lib/api";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { errorToast, loadingToast } from "../../global/toast";

type Props = {
  onOpenChange: (isOpen: boolean) => void;
};

const formSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Project name is required" })
    .transform((value) => value.trim()),
  type: z.enum(["frontend", "fullstack"]),
  stack: z.enum(["next.js", "vite"]),
});

type FormValues = z.infer<typeof formSchema>;

interface ProjectData {
  project_id: string;
  name: string;
  creation_date: string;
  last_updated_date: string;
  env_id: string;
  agent_instructions: string | null;
  agent_continue_instructions: string | null;
  agent_rules_to_follow: string | null;
  onboarding_completed: boolean;
  initial_thread_id: number;
}

export interface CreateProjectResponse {
  message: string;
  project_data: ProjectData;
}

interface ApiError {
  detail: string;
}

const CreateProjectModalContent = ({ onOpenChange }: Props) => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      type: "frontend",
      stack: "next.js",
    },
  });

  const selectedType = form.watch("type");
  const selectedStack = form.watch("stack");

  const { mutate, isPending } = useMutation<CreateProjectResponse, ApiError, FormValues>({
    mutationKey: ["create-project"],
    mutationFn: async (data: FormValues) => {
      const response = await loadingToast(
        "Creating project...",
        createProject(data.name, data.stack),
      );
      return response as CreateProjectResponse;
    },
    onSuccess: (data: CreateProjectResponse) => {
      if (data && data.project_data) {
        queryClient.invalidateQueries({ queryKey: ["projects"] });
        router.push(`/project/${data.project_data.project_id}?action=chat`);
        onOpenChange(false);
      } else {
        errorToast("Error creating project");
      }
    },
    onError: (error: ApiError) => {
      errorToast(error.detail || "Error creating project");
    },
  });

  const onSubmit = (data: FormValues) => {
    try {
      mutate(data);
    } catch (error) {
      const apiError = error as ApiError;
      errorToast(apiError.detail || "Error submitting project");
    }
  };

  return (
    <ModalContentInner className="lg:max-w-3xl">
      <ModalHeader className="px-4 md:px-6">
        <ModalTitle>Create New Project</ModalTitle>
        <ModalDescription>Fill in the details to create your project.</ModalDescription>
      </ModalHeader>
      <div className="space-y-6 px-4 py-6 md:p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Project Name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="stack"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Framework</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="grid grid-cols-2 gap-4"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="next.js" />
                        </FormControl>
                        <FormLabel className="font-normal">Next.js</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="vite" />
                        </FormControl>
                        <FormLabel className="font-normal">Vite</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedStack === "next.js" && (
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Type</FormLabel>
                    <FormControl>
                      <div className="flex w-full flex-col gap-2">
                        <RadioGroup
                          className="gap-2"
                          defaultValue="frontend"
                          onValueChange={field.onChange}
                        >
                          <div className="grid w-full grid-cols-1 gap-2 md:grid-cols-2">
                            <div
                              className={cn(
                                "shadow-xs relative flex w-full items-start gap-2 rounded-md border border-border p-4 outline-none",
                                selectedType === "frontend" && "bg-foreground/5",
                              )}
                            >
                              <RadioGroupItem
                                value="frontend"
                                id={`${field.name}-frontend`}
                                aria-describedby={`${field.name}-frontend-description`}
                                className="order-1 after:absolute after:inset-0"
                              />
                              <div className="flex w-full grow flex-col items-start gap-3">
                                <div className="flex w-full items-start gap-2">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="50"
                                    height="50"
                                    viewBox="0 0 100 100"
                                    className="size-6"
                                    fill="none"
                                  >
                                    <path
                                      fill="#000"
                                      d="M50 100c27.614 0 50-22.386 50-50S77.614 0 50 0 0 22.385 0 50s22.386 50 50 50"
                                    ></path>
                                    <path
                                      fill="url(#nextJs__a)"
                                      d="M83.06 87.511 38.412 30H30v39.983h6.73V38.546L77.777 91.58a50 50 0 0 0 5.283-4.069"
                                    ></path>
                                    <path
                                      fill="url(#nextJs__b)"
                                      d="M70.556 30h-6.667v40h6.667z"
                                    ></path>
                                    <defs>
                                      <linearGradient
                                        id="nextJs__a"
                                        x1="60.556"
                                        x2="80.278"
                                        y1="64.722"
                                        y2="89.166"
                                        gradientUnits="userSpaceOnUse"
                                      >
                                        <stop stop-color="#fff"></stop>
                                        <stop offset="1" stop-color="#fff" stop-opacity="0"></stop>
                                      </linearGradient>
                                      <linearGradient
                                        id="nextJs__b"
                                        x1="67.222"
                                        x2="67.111"
                                        y1="30"
                                        y2="59.375"
                                        gradientUnits="userSpaceOnUse"
                                      >
                                        <stop stop-color="#fff"></stop>
                                        <stop offset="1" stop-color="#fff" stop-opacity="0"></stop>
                                      </linearGradient>
                                    </defs>
                                  </svg>
                                  <div className="grid w-full grow gap-2">
                                    <Label htmlFor={`${field.name}-frontend`}>
                                      Frontend (Next.js)
                                    </Label>
                                    <p
                                      id={`${field.name}-frontend-description`}
                                      className="text-xs text-muted-foreground"
                                    >
                                      Build fast, modern web apps with Next.js. Perfect for
                                      frontend-focused projects.
                                    </p>
                                  </div>
                                </div>
                                <div className="w-full md:hidden">
                                  {selectedType === "frontend" && (
                                    <ProjectTypeContent type={selectedType} />
                                  )}
                                </div>
                              </div>
                            </div>

                            <div
                              className={cn(
                                "shadow-xs relative flex w-full items-start gap-2 rounded-md border border-border p-4 outline-none",
                                selectedType === "fullstack" && "bg-foreground/5",
                              )}
                            >
                              <RadioGroupItem
                                value="fullstack"
                                id={`${field.name}-fullstack`}
                                aria-describedby={`${field.name}-fullstack-description`}
                                className="order-1 after:absolute after:inset-0"
                              />
                              <div className="flex w-full grow flex-col items-start gap-3">
                                <div className="flex w-full items-start gap-2">
                                  <SupabaseIcon className="size-6" />
                                  <div className="grid w-full grow gap-2">
                                    <Label htmlFor={`${field.name}-fullstack`}>Full Stack</Label>
                                    <p
                                      id={`${field.name}-fullstack-description`}
                                      className="text-xs text-muted-foreground"
                                    >
                                      Build complete web apps with Next.js frontend and Supabase
                                      backend. Includes database, auth and storage.
                                    </p>
                                  </div>
                                </div>
                                <div className="md:hidden">
                                  {selectedType === "fullstack" && (
                                    <ProjectTypeContent type={selectedType} />
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </RadioGroup>
                        <div className="hidden md:block">
                          <ProjectTypeContent type={selectedType} />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </form>
        </Form>
      </div>
      <ModalFooter className="px-4 pb-20 md:px-6 md:pb-6">
        <div className="flex w-full items-center md:justify-end">
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isPending || !form.formState.isDirty}
            className="flex h-9 w-full items-center gap-2 md:w-auto"
          >
            {isPending && <Loading className="size-4 animate-spin text-background" />}
            Create Project
          </Button>
        </div>
      </ModalFooter>
    </ModalContentInner>
  );
};

export default CreateProjectModalContent;
