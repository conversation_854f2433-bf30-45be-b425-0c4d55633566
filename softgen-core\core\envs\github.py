import logging
import re
import aiohttp
import asyncio
import shlex
from datetime import datetime
from typing import Optional, Dict, Any
from core.config import settings
from core.envs.env_ops import env_ops, AsyncSandbox

# Configure logging for this module
logger = logging.getLogger(__name__)

class GitHubManager:
    def __init__(self):
        self.github_token = settings.github_token
        logger.info("GitHubManager initialized")

    async def _wait_for_workspace(self, workspace_id: str, max_retries: int = 10) -> AsyncSandbox | None:
        """Wait for workspace to be ready"""
        logger.info(f"Waiting for workspace {workspace_id} to be ready")
        for i in range(max_retries):
            try:
                sandbox = await env_ops.get_started_sandbox(workspace_id)
                # Try a simple command to test if workspace is responsive
                result = await sandbox.process.exec("ls")

                if result.exit_code == 0:
                    logger.info(f"Workspace {workspace_id} is ready after {i+1} attempts")
                    return sandbox
            except Exception as e:
                logger.info(f"Waiting for workspace... attempt {i+1}/{max_retries}, error: {e}")
                await asyncio.sleep(2)

        logger.error(f"Workspace {workspace_id} not ready after {max_retries} attempts")
        return None

    async def create_and_setup_repo(self, workspace_id: str) -> Optional[Dict[str, str]]:
        """
        Creates a GitHub repository and sets it up in the workspace.

        Args:
            workspace_id (str): The workspace ID to set up Git for

        Returns:
            Optional[Dict[str, str]]: Repository details if successful, None otherwise
        """
        try:
            # Wait for workspace to be ready
            sandbox = await self._wait_for_workspace(workspace_id)

            if not sandbox:
                logger.error("Workspace not ready after maximum retries")
                return None

            repo_name = f"sg-{workspace_id}-{int(datetime.now().timestamp())}"

            # Create GitHub repository
            repo_info = await self._create_github_repo(repo_name)
            if not repo_info:
                return None

            # Initialize Git in workspace
            if not await self._init_git_in_workspace(sandbox):
                return None

            # Setup remote and push
            if not await self._setup_remote_and_push(sandbox, repo_info["clone_url"]):
                return None

            return {
                "repo_name": repo_name,
                "clone_url": repo_info["clone_url"],
                "html_url": repo_info["html_url"]
            }

        except Exception as e:
            logger.error(f"Error in create_and_setup_repo: {str(e)}")
            return None

    async def _create_github_repo(self, repo_name: str) -> Optional[Dict]:
        """Creates a new GitHub repository"""
        try:
            headers = {
                "Authorization": f"token {self.github_token}",
                "Accept": "application/vnd.github.v3+json"
            }
            repo_data = {
                "name": repo_name,
                "private": True,
                "auto_init": False
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://api.github.com/user/repos",
                    headers=headers,
                    json=repo_data
                ) as response:
                    if response.status != 201:
                        logger.error(f"Failed to create GitHub repository: {await response.text()}")
                        return None
                    return await response.json()

        except Exception as e:
            logger.error(f"Error creating GitHub repository: {str(e)}")
            return None

    async def _init_git_in_workspace(self, sandbox: AsyncSandbox) -> bool:
        """Initializes Git in the workspace"""
        try:
            # Check if git is installed
            git_check = await sandbox.process.exec("which git")
            if "git" not in git_check.result:
                logger.error("Git not found")
                return False

            # Git config commands
            git_commands = [
                "git config --global user.email '<EMAIL>'",
                "git config --global user.name 'Softgen'",
                "git config --global init.defaultBranch 'main'",
                "git -C /app init",
                "git -C /app add .",
                "git -C /app status",
                "git -C /app commit -m 'Initial commit'",
                "git -C /app branch -M main"
            ]

            for cmd in git_commands:
                logger.info(f"Executing: {cmd}")
                result = await sandbox.process.exec(cmd)
                logger.info(f"Command output: {result.result}")

                if "status" in cmd:
                    # If nothing to commit, create a new change
                    if "nothing to commit" in result.result:
                        logger.info("No changes detected, creating new file...")
                        await sandbox.process.exec("touch /app/update.txt")
                        await sandbox.process.exec("git -C /app add update.txt")
                        status = await sandbox.process.exec("git -C /app status")
                        logger.info(f"New status after creating file: {status.result}")
                elif "error:" in result.result.lower() or "fatal:" in result.result.lower():
                    logger.error(f"Git command failed: {cmd}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error initializing Git in workspace: {str(e)}")
            logger.exception("Full traceback:")
            return False

    async def _setup_remote_and_push(self, sandbox: AsyncSandbox, clone_url: str) -> bool:
        """Sets up Git remote and pushes initial commit"""
        try:
            auth_url = clone_url.replace("https://", f"https://softgenprojects:{self.github_token}@")

            # Setup commands
            git_commands = [
                f"git -C /app remote add origin {auth_url}",
                "git -C /app push -f origin main"
            ]

            for cmd in git_commands:
                logger.info(f"Executing: {cmd}")
                result = await sandbox.process.exec(cmd)
                logger.info(f"Command output: {result.result}")

                if "error:" in result.result.lower() or "fatal:" in result.result.lower():
                    logger.error(f"Git command failed: {cmd}")
                    logger.error(f"Output: {result.result}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error setting up Git remote: {str(e)}")
            return False

    async def get_git_info(self, workspace_id: str) -> Optional[Dict[str, Any]]:
        """Gets Git repository information from the workspace using direct commands."""
        try:
            sandbox = await self._wait_for_workspace(workspace_id)

            if not sandbox:
                logger.error("Workspace not ready")
                return None

            # Get branch
            branch_output = await sandbox.process.exec("git -C /app rev-parse --abbrev-ref HEAD")
            branch = branch_output.result.strip() if branch_output.result else None

            # Get remote URL
            remote_output = await sandbox.process.exec("git -C /app config --get remote.origin.url")
            remote_url = remote_output.result.strip() if remote_output.result else None

            if remote_url and "softgenprojects:" in remote_url:
                # Remove token from URL
                remote_url = re.sub(r'softgenprojects:[^@]+@', '', remote_url)

            # Get commits (increased from 5 to 100 commits)
            commits_output = await sandbox.process.exec('git -C /app log -5000 --pretty=format:"%H|||%aI|||%s"')
            commits = []
            if commits_output.result:
                for line in commits_output.result.split('\n'):
                    if '|||' in line:
                        try:
                            hash, date, message = line.strip('"').split('|||')
                            commits.append({
                                "hash": hash.strip(),
                                "date": date.strip(),
                                "message": message.strip()
                            })
                        except ValueError:
                            continue

            result = {
                "remote_url": remote_url,
                "github_repo": remote_url,
                "branch": branch,
                "commits": commits
            }

            # logging.info(f"Git info: {result}")
            return result

        except Exception as e:
            logger.error(f"Error getting Git info: {str(e)}")
            logger.exception("Full traceback:")
            return {
                "remote_url": None,
                "github_repo": None,
                "branch": None,
                "commits": []
            }

    async def git_add_and_push(self, workspace_id: str, commit_message: str) -> Optional[Dict[str, Any]]:
        """
        Add all changes, commit with message, and push to remote repository.
        Returns information about whether there were any file changes.
        Retries up to 3 times if authentication fails.
        """
        try:
            logger.info(f"Starting git_add_and_push for workspace {workspace_id}")

            sandbox = await self._wait_for_workspace(workspace_id)

            if not sandbox:
                raise Exception("Workspace not ready after maximum retries")

            # Set up git credentials
            git_setup_commands = [
                "git config --global credential.helper store",
                "git config --global user.name 'softgenai'",
                "git config --global user.email '<EMAIL>'",
                f"echo 'https://softgenprojects:{self.github_token}@github.com' > ~/.git-credentials",
                "chmod 600 ~/.git-credentials"
            ]

            for cmd in git_setup_commands:
                result = await sandbox.process.exec(cmd)
                if "error:" in result.result.lower() or "fatal:" in result.result.lower():
                    logger.error(f"Git setup failed: {cmd}")
                    continue

            # Extract first line of commit message for the subject
            first_line = commit_message.split('\n')[0].strip()
            # If first line is too long or empty, use "Updates"
            subject = first_line if 0 < len(first_line) <= 72 else "Updates"

            # Check if there are any changes before adding
            status_result = await sandbox.process.exec("git -C /app status --porcelain")
            has_changes = bool(status_result.result.strip())

            logger.info(f"Has changes to commit: {has_changes}")

            # Git commands sequence
            git_commands = [
                "git -C /app add --all",  # Use --all instead of -A for clarity
            ]

            # Only add commit and push commands if there are changes
            if has_changes:
                safe_subject = shlex.quote(subject)
                git_commands.extend([
                    f"git -C /app commit -m {safe_subject}",
                    "git -C /app push -f origin main"
                ])

            logger.info("=== EXECUTING GIT ADD AND PUSH COMMANDS ===")
            result = await self._execute_git_commands_with_retry(sandbox, workspace_id, git_commands, max_retries=3)

            if not result["success"]:
                raise Exception(f"Git add and push failed: {result['error']}")

            # Get commit hash only if we committed
            commit_hash = None
            if has_changes:
                hash_result = await sandbox.process.exec("git -C /app rev-parse HEAD")
                commit_hash = hash_result.result.strip()
                logger.info(f"Successfully pushed changes for workspace {workspace_id}, commit: {commit_hash}")
            else:
                logger.info(f"No changes to push for workspace {workspace_id}")

            return {
                "success": True,
                "commit": {
                    "hash": commit_hash,
                    "message": subject
                },
                "has_file_changes": has_changes,
                "logs": result["logs"]
            }

        except Exception:
            logger.exception("Error in git_add_and_push")
            return None

    async def revert_git_commit(self, workspace_id: str, commit_hash: str, revert_and_full_reset: bool = False) -> Dict[str, Any]:
        """
        Reverts to a specific commit.

        Args:
            workspace_id (str): The workspace ID
            commit_hash (str): The commit hash to revert to
            revert_and_full_reset (bool): Whether to do a full reset

        Returns:
            Dict[str, Any]: Result of the revert operation
        """
        logs = []

        try:
            sandbox = await self._wait_for_workspace(workspace_id)

            if not sandbox:
                raise Exception("Workspace not ready after maximum retries")

            # Clean working directory
            clean_commands = [
                "git -C /app reset --hard HEAD",
                "git -C /app clean -fd"
            ]

            for cmd in clean_commands:
                result = await sandbox.process.exec(cmd)
                logs.append(f"Clean command output: {result.result}")

            # Set up git credentials
            git_setup_commands = [
                "git config --global credential.helper store",
                "git config --global user.name 'softgenai'",
                "git config --global user.email '<EMAIL>'",
                "mkdir -p ~/.git",
                f"echo 'https://softgenprojects:{self.github_token}@github.com' > ~/.git/.git-credentials",
                "chmod 600 ~/.git/.git-credentials",
                "git config --global credential.helper 'store --file ~/.git/.git-credentials'"
            ]

            for cmd in git_setup_commands:
                result = await sandbox.process.exec(cmd)
                logs.append(f"Git setup command output: {result.result}")

            # Fetch and checkout using helper function
            fetch_commands = [
                "git -C /app fetch --all --prune --tags --force",
                "git -C /app checkout main",
                "git -C /app pull --force origin main"
            ]

            logger.info("=== EXECUTING FETCH COMMANDS ===")
            fetch_result = await self._execute_git_commands_with_retry(sandbox, workspace_id, fetch_commands, max_retries=3)

            if not fetch_result["success"]:
                raise Exception(f"Fetch commands failed: {fetch_result['error']}")

            logs.extend(fetch_result["logs"])

            # Check if commit exists
            commit_check = await sandbox.process.exec(f"git -C /app cat-file -t {commit_hash}")
            if not commit_check.result or commit_check.result.strip() != "commit":
                raise Exception(f"Commit {commit_hash} not found")

            if revert_and_full_reset:
                # Full reset using helper function
                reset_commands = [
                    f"git -C /app reset --hard {commit_hash}",
                    "git -C /app push --force origin main"
                ]

                logger.info("=== EXECUTING RESET COMMANDS ===")
                reset_result = await self._execute_git_commands_with_retry(sandbox, workspace_id, reset_commands, max_retries=3)

                if not reset_result["success"]:
                    raise Exception(f"Reset commands failed: {reset_result['error']}")

                logs.extend(reset_result["logs"])
            else:
                # Revert changes
                revert_result = await sandbox.process.exec(f"git -C /app revert --no-commit {commit_hash}..HEAD")
                logs.append(f"Revert result: {revert_result.result}")
                if "error:" in revert_result.result.lower() or "fatal:" in revert_result.result.lower():
                    raise Exception(f"Revert command failed: {revert_result.result}")

                # Check for changes
                status_result = await sandbox.process.exec("git -C /app status --porcelain")
                if not status_result.result.strip():
                    return {
                        "success": True,
                        "message": f"No changes needed. Repository is already at the state of commit {commit_hash}",
                        "logs": logs
                    }

                # Commit and push changes using helper function
                commit_commands = [
                    "git -C /app add -A",
                    f"git -C /app commit -m 'Revert to state at {commit_hash}'",
                    "git -C /app push --force origin main"
                ]

                logger.info("=== EXECUTING COMMIT COMMANDS ===")
                commit_result = await self._execute_git_commands_with_retry(sandbox, workspace_id, commit_commands, max_retries=3)

                if not commit_result["success"]:
                    raise Exception(f"Commit commands failed: {commit_result['error']}")

                logs.extend(commit_result["logs"])

            # Restart PM2
            restart_result = await sandbox.process.exec("pm2 restart all")
            logs.append(f"PM2 restart result: {restart_result.result}")

            return {
                "success": True,
                "message": f"Successfully {'reset' if revert_and_full_reset else 'reverted'} to state at commit {commit_hash}",
                "logs": logs
            }

        except Exception as e:
            logger.error(f"Error reverting git commit: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "logs": logs
            }

    async def add_collaborator(self, repo_url: str, username: str) -> bool:
        """
        Adds a collaborator to a GitHub repository.

        Args:
            repo_url (str): The repository URL
            username (str): The GitHub username to add

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Extract owner and repo from URL
            import re
            match = re.match(r"https?://github\.com/([^/]+)/([^/\.]+)(?:\.git)?", repo_url)
            if not match:
                logger.error(f"Invalid GitHub URL: {repo_url}")
                return False

            owner, repo = match.groups()
            logger.info(f"Extracted owner: {owner}, repo: {repo}")

            # Create GitHub API request
            headers = {
                "Authorization": f"token {self.github_token}",
                "Accept": "application/vnd.github.v3+json"
            }

            async with aiohttp.ClientSession() as session:
                url = f"https://api.github.com/repos/{owner}/{repo}/collaborators/{username}"
                async with session.put(url, headers=headers, json={"permission": "push"}) as response:
                    # 204 means success for this endpoint
                    if response.status in [201, 204]:
                        logger.info(f"Successfully added {username} as collaborator to {repo_url}")
                        return True
                    else:
                        logger.error(f"Failed to add collaborator. Status: {response.status}")
                        return False

        except Exception as e:
            logger.error(f"Error adding collaborator: {str(e)}")
            return False

    async def force_pull(self, workspace_id: str) -> Dict[str, Any]:
        """
        Force pulls the latest changes from the main branch.

        Args:
            workspace_id (str): The workspace ID

        Returns:
            Dict[str, Any]: Result of the pull operation
        """
        logs = []

        try:
            sandbox = await self._wait_for_workspace(workspace_id)

            if not sandbox:
                raise Exception("Workspace not ready after maximum retries")

            # Clean working directory first
            clean_commands = [
                "git -C /app reset --hard HEAD",
                "git -C /app clean -fd"
            ]

            for cmd in clean_commands:
                result = await sandbox.process.exec(cmd)
                logs.append(f"Clean command output: {result.result}")

            # Set up git credentials
            git_setup_commands = [
                "git config --global credential.helper store",
                "git config --global user.name 'softgenai'",
                "git config --global user.email '<EMAIL>'",
                f"echo 'https://softgenprojects:{self.github_token}@github.com' > ~/.git-credentials",
                "chmod 600 ~/.git-credentials"
            ]

            for cmd in git_setup_commands:
                result = await sandbox.process.exec(cmd)
                logs.append(f"Git setup command output: {result.result}")

            # Debug: Check git status and remote configuration
            debug_commands = [
                "git -C /app status",
                "git -C /app remote -v",
                "git -C /app config --get remote.origin.url"
            ]

            logger.info("=== DEBUGGING GIT STATE ===")
            for debug_cmd in debug_commands:
                debug_result = await sandbox.process.exec(debug_cmd)
                logger.info(f"Debug {debug_cmd}: {debug_result.result}")
                logger.info(f"Debug exit code: {debug_result.exit_code}")
                logs.append(f"Debug {debug_cmd}: {debug_result.result}")

            # Check if remote exists and is properly configured
            remote_check = await sandbox.process.exec("git -C /app config --get remote.origin.url")
            if not remote_check.result.strip():
                logger.error("No remote origin configured")
                raise Exception("No remote origin configured")

            logger.info(f"Remote URL: {remote_check.result.strip()}")

            # Force pull commands using the helper function
            pull_commands = [
                "git -C /app fetch --all --prune",
                "git -C /app checkout main",
                "git -C /app reset --hard origin/main"
            ]

            logger.info("=== EXECUTING PULL COMMANDS ===")
            result = await self._execute_git_commands_with_retry(sandbox, workspace_id, pull_commands, max_retries=3)

            if not result["success"]:
                raise Exception(f"Force pull failed: {result['error']}")

            # Add the logs from the helper function
            logs.extend(result["logs"])

            # Restart PM2 after pull
            restart_result = await sandbox.process.exec("pm2 restart all")
            logs.append(f"PM2 restart result: {restart_result.result}")

            return {
                "success": True,
                "message": "Successfully pulled latest changes",
                "logs": logs
            }

        except Exception as e:
            logger.error(f"Error force pulling changes: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "logs": logs
            }

    async def _handle_git_auth_retry(self, sandbox: AsyncSandbox, workspace_id: str, cmd: str, result, attempt: int, max_retries: int) -> bool:
        """
        Helper function to handle Git authentication retry logic.

        Args:
            sandbox: The sandbox instance
            workspace_id: The workspace ID
            cmd: The command that failed
            result: The command result
            attempt: Current attempt number
            max_retries: Maximum number of retries

        Returns:
            bool: True if should retry, False if should fail
        """
        # Check for authentication issues
        auth_error_patterns = [
            "Authentication failed",
            "Invalid username or password",
            "remote: Invalid credentials",
            "fatal: Authentication failed",
            "fatal: remote error: Invalid username or password"
        ]

        is_auth_error = any(pattern in result.result for pattern in auth_error_patterns)

        if is_auth_error:
            logger.warning(f"Authentication failed on attempt {attempt + 1} for workspace {workspace_id}")
            logger.error(f"Auth error details: {result.result}")

            # If this is not the last attempt, set remote URL with token and retry
            if attempt < max_retries - 1:
                logger.info(f"Setting remote URL with token and retrying for workspace {workspace_id}...")

                # Get current remote URL
                remote_check = await sandbox.process.exec("git -C /app config --get remote.origin.url")
                current_remote = remote_check.result.strip()
                logger.info(f"Current remote URL: {current_remote}")

                if current_remote:
                    # Update remote URL with token - handle existing auth tokens
                    if "softgenprojects:" in current_remote:
                        # Remove existing token first - preserve https://
                        clean_url = current_remote.replace("https://softgenprojects:", "https://")
                        # Remove username@ part but keep the rest
                        clean_url = re.sub(r'https://[^@]+@', 'https://', clean_url)
                        auth_url = clean_url.replace("https://", f"https://softgenprojects:{self.github_token}@")
                        logger.info("Cleaned existing auth token and set new one")
                    else:
                        # No existing token, just add it
                        auth_url = current_remote.replace("https://", f"https://softgenprojects:{self.github_token}@")
                        logger.info("Added auth token to remote URL")

                    logger.info(f"Setting new auth URL for workspace {workspace_id}")
                    set_url_result = await sandbox.process.exec(f"git -C /app remote set-url origin {auth_url}")
                    if set_url_result.exit_code != 0:
                        logger.error(f"Failed to set remote URL: {set_url_result.result}")
                        return False

                    return True  # Should retry
                else:
                    logger.error("No remote URL found to update")
                    return False
            else:
                logger.error(f"Git command failed after {max_retries} attempts for workspace {workspace_id}: {cmd}")
                logger.error(f"Final authentication failure output: {result.result}")
                return False  # Should fail

        return False  # Not an auth error, should fail

    async def _execute_git_commands_with_retry(self, sandbox: AsyncSandbox, workspace_id: str, commands: list, max_retries: int = 3) -> Dict[str, Any]:
        """
        Execute Git commands with authentication retry logic.

        Args:
            sandbox: The sandbox instance
            workspace_id: The workspace ID
            commands: List of commands to execute
            max_retries: Maximum number of retries

        Returns:
            Dict[str, Any]: Result with success status and logs
        """
        logs = []

        for attempt in range(max_retries):
            try:
                logger.info(f"Attempt {attempt + 1}/{max_retries} for git commands")

                for cmd in commands:
                    logger.info(f"Executing: {cmd}")
                    result = await sandbox.process.exec(cmd)
                    logger.info(f"Command output: {result.result}")
                    logger.info(f"Exit code: {result.exit_code}")
                    logs.append(f"Command: {cmd}")
                    logs.append(f"Output: {result.result}")
                    logs.append(f"Exit code: {result.exit_code}")

                    # Check for errors
                    if result.exit_code != 0 or "error:" in result.result.lower() or "fatal:" in result.result.lower():
                        logger.warning(f"Git command failed: {cmd}")
                        logger.warning(f"Full output: {result.result}")

                        # Try to handle authentication retry
                        should_retry = await self._handle_git_auth_retry(sandbox, workspace_id, cmd, result, attempt, max_retries)

                        if should_retry:
                            logger.info(f"Retrying entire command sequence (attempt {attempt + 2}/{max_retries})")
                            break  # Break out of command loop to retry entire sequence
                        else:
                            # Either not an auth error or max retries reached
                            raise Exception(f"Git command failed: {cmd} - {result.result}")

                # If we get here without breaking, all commands succeeded
                logger.info(f"All git commands completed successfully for workspace {workspace_id}")
                return {
                    "success": True,
                    "logs": logs
                }

            except Exception as e:
                logger.exception("Error in git command")
                if attempt == max_retries - 1:
                    return {
                        "success": False,
                        "error": str(e),
                        "logs": logs
                    }
                # Continue to next attempt
                continue

        return {
            "success": False,
            "error": "Git commands failed after maximum retries",
            "logs": logs
        }

github_manager = GitHubManager()

if __name__ == "__main__":
    import asyncio
    from core.config import settings

    async def test():
        # Test workspace ID
        test_workspace_id = "sandbox-6de3bdf9"

        try:
            print("\nTesting get_git_info...")
            git_info = await github_manager.get_git_info(test_workspace_id)

            if git_info:
                print("\nGit Info Results:")
                print(f"Remote URL: {git_info['remote_url']}")
                print(f"Branch: {git_info['branch']}")
                print("\nRecent Commits:")
                for commit in git_info['commits']:
                    print(f"\nHash: {commit['hash']}")
                    print(f"Date: {commit['date']}")
                    print(f"Message: {commit['message']}")
            else:
                print("Failed to get git info")

        except Exception as e:
            print(f"Error during test: {str(e)}")
            import traceback
            traceback.print_exc()

    # Run the test
    asyncio.run(test())
