{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport * as React from \"react\";\r\nimport {\r\n  Controller,\r\n  ControllerProps,\r\n  FieldPath,\r\n  FieldValues,\r\n  FormProvider,\r\n  useFormContext,\r\n} from \"react-hook-form\";\r\n\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState, formState } = useFormContext();\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\");\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nconst FormItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => {\r\n    const id = React.useId();\r\n\r\n    return (\r\n      <FormItemContext.Provider value={{ id }}>\r\n        <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n      </FormItemContext.Provider>\r\n    );\r\n  },\r\n);\r\nFormItem.displayName = \"FormItem\";\r\n\r\nconst FormLabel = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", \"text-base\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nFormLabel.displayName = \"FormLabel\";\r\n\r\nconst FormControl = React.forwardRef<\r\n  React.ElementRef<typeof Slot>,\r\n  React.ComponentPropsWithoutRef<typeof Slot>\r\n>(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nFormControl.displayName = \"FormControl\";\r\n\r\nconst FormDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-sm text-muted-foreground\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nFormDescription.displayName = \"FormDescription\";\r\n\r\nconst FormMessage = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message) : children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-sm font-medium text-destructive\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n});\r\nFormMessage.displayName = \"FormMessage\";\r\n\r\nexport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n  useFormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AACA;AASA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,uPAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6VAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6VAAC,uPAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uPAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,MAAM,yBAAW,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,MAAM,KAAK,CAAA,GAAA,oTAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6VAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6VAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AAEF,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6VAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB,aAAa;QACxD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6VAAC,oSAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6VAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW;IAE9C,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6VAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\";\r\nimport { Circle } from \"lucide-react\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return <RadioGroupPrimitive.Root className={cn(\"grid gap-2\", className)} {...props} ref={ref} />;\r\n});\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName;\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  );\r\n});\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;\r\n\r\nexport { RadioGroup, RadioGroupItem };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBAAO,6VAAC,iRAAA,CAAA,OAAwB;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAAa,GAAG,KAAK;QAAE,KAAK;;;;;;AAC3F;AACA,WAAW,WAAW,GAAG,iRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6VAAC,iRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,iRAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6VAAC,0RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,iRAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/icon/supabase.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\nconst SupabaseIcon = ({ className }: { className?: string }) => {\r\n  return (\r\n    <svg\r\n      width=\"113\"\r\n      height=\"113\"\r\n      viewBox=\"0 0 113 113\"\r\n      className={cn(\"flex h-8 w-8 items-center justify-center\", className)}\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z\"\r\n        fill=\"#3ECF8E\"\r\n      ></path>\r\n      <path\r\n        d=\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\"\r\n        fill=\"#3ECF8E\"\r\n      ></path>\r\n      <defs>\r\n        <linearGradient\r\n          id=\"paint0_linear\"\r\n          x1=\"53.9738\"\r\n          y1=\"54.974\"\r\n          x2=\"94.1635\"\r\n          y2=\"71.8295\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stop-color=\"#249361\"></stop>\r\n          <stop offset=\"1\" stop-color=\"#3ECF8E\"></stop>\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear\"\r\n          x1=\"36.1558\"\r\n          y1=\"30.578\"\r\n          x2=\"54.4844\"\r\n          y2=\"65.0806\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop></stop>\r\n          <stop offset=\"1\" stop-opacity=\"0\"></stop>\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default SupabaseIcon;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,eAAe,CAAC,EAAE,SAAS,EAA0B;IACzD,qBACE,6VAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,MAAK;QACL,OAAM;;0BAEN,6VAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6VAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,6VAAC;;kCACC,6VAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,6VAAC;gCAAK,cAAW;;;;;;0CACjB,6VAAC;gCAAK,QAAO;gCAAI,cAAW;;;;;;;;;;;;kCAE9B,6VAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,eAAc;;0CAEd,6VAAC;;;;;0CACD,6VAAC;gCAAK,QAAO;gCAAI,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;AAKxC;uCAEe", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-lg px-3 py-1 text-xs font-medium transition-all focus:outline-none cursor-pointer \",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border border-primary/10 bg-primary text-primary-foreground\",\r\n        secondary: \"border border-secondary/10 bg-secondary text-secondary-foreground\",\r\n        destructive:\r\n          \"border-transparent bg-red-100 border border-red-600/30 text-red-800   dark:bg-red-900/30 dark:text-red-400\",\r\n        success:\r\n          \"border-transparent bg-emerald-200 border border-emerald-400 text-emerald-800 dark:border-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-500\",\r\n        badgeSuccess:\r\n          \"border-transparent bg-emerald-200   text-emerald-800   dark:bg-emerald-900/50 dark:text-emerald-500 disabled:border-alpha-300 focus-visible:ring-offset-background outline-hidden has-focus-visible:ring-2 pointer-events-none inline-flex shrink-0 cursor-pointer items-center justify-center gap-1.5 whitespace-nowrap rounded-full   ring-blue-600 transition-all focus-visible:ring-2 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-400 disabled:ring-0 [&>svg]:pointer-events-none   bg-teal-100 text-teal-700 hover:bg-teal-100  focus:bg-teal-100   focus-visible:bg-teal-100 has-[>svg]:pl-[10px] [&>svg]:size-3 h-5 px-1.5 text-[11px] font-medium\",\r\n        update:\r\n          \"border-transparent bg-blue-200 border border-blue-400 text-blue-800 dark:border-blue-800 dark:bg-blue-900/50 dark:text-blue-500\",\r\n        warning:\r\n          // \"border-transparent bg-yellow-100 border border-yellow-600/40  text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400\",\r\n          \"border-transparent bg-yellow-200 border border-yellow-400 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-500\",\r\n        // info: \"border-transparent bg-neutral-100 border border-neutral-600/40 text-muted-foreground dark:bg-neutral-900/30\",\r\n        info: \"border-transparent bg-neutral-200 border border-neutral-400 text-neutral-800 dark:border-neutral-800 dark:bg-neutral-900/50 dark:text-neutral-500\",\r\n        terminal:\r\n          // \"border-transparent bg-purple-100 border border-purple-600/40 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400\",\r\n          \"border-transparent bg-purple-200 border border-purple-400 text-purple-800 dark:border-purple-800 dark:bg-purple-900/50 dark:text-purple-500\",\r\n        reset:\r\n          // \"border-transparent bg-amber-100 border border-amber-600/40   text-amber-600 dark:bg-amber-900/30\",\r\n          \"border-transparent bg-amber-200 border border-amber-400 text-amber-800 dark:border-amber-800 dark:bg-amber-900/50 dark:text-amber-500\",\r\n        outline: \"bg-background text-foreground border border-foreground/20\",\r\n        opened:\r\n          \"flex-shrink-0 bg-neutral-100 border border-neutral-600/40 text-muted-foreground dark:bg-neutral-900/30\",\r\n        closed:\r\n          \"flex-shrink-0 bg-neutral-100 border border-neutral-600/40 text-muted-foreground dark:bg-neutral-900/30\",\r\n        loading:\r\n          \"border-transparent bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300\",\r\n        passed:\r\n          \"flex-shrink-0 rounded-lg px-3 py-1 text-xs font-medium bg-emerald-200 border border-emerald-400 text-emerald-800 dark:border-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-500\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return <div className={cn(badgeVariants({ variant }), className)} {...props} />;\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAGA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,uHACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aACE;YACF,SACE;YACF,cACE;YACF,QACE;YACF,SACE,8HAA8H;YAC9H;YACF,uHAAuH;YACvH,MAAM;YACN,UACE,6HAA6H;YAC7H;YACF,OACE,sGAAsG;YACtG;YACF,SAAS;YACT,QACE;YACF,QACE;YACF,SACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBAAO,6VAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAC7E", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/new/project-tabs.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport SupabaseIcon from \"../icon/supabase\";\r\n\r\nconst content = {\r\n  frontend: {\r\n    title: \"Frontend (Next.js)\",\r\n    description: \"Build fast, modern web apps with Next.js. Perfect for frontend-focused projects.\",\r\n    features: [\r\n      {\r\n        icon: (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"50\"\r\n            height=\"50\"\r\n            viewBox=\"0 0 100 100\"\r\n            className=\"size-4\"\r\n            fill=\"none\"\r\n          >\r\n            <path\r\n              fill=\"#000\"\r\n              d=\"M50 100c27.614 0 50-22.386 50-50S77.614 0 50 0 0 22.385 0 50s22.386 50 50 50\"\r\n            ></path>\r\n            <path\r\n              fill=\"url(#nextJs__a)\"\r\n              d=\"M83.06 87.511 38.412 30H30v39.983h6.73V38.546L77.777 91.58a50 50 0 0 0 5.283-4.069\"\r\n            ></path>\r\n            <path fill=\"url(#nextJs__b)\" d=\"M70.556 30h-6.667v40h6.667z\"></path>\r\n            <defs>\r\n              <linearGradient\r\n                id=\"nextJs__a\"\r\n                x1=\"60.556\"\r\n                x2=\"80.278\"\r\n                y1=\"64.722\"\r\n                y2=\"89.166\"\r\n                gradientUnits=\"userSpaceOnUse\"\r\n              >\r\n                <stop stop-color=\"#fff\"></stop>\r\n                <stop offset=\"1\" stop-color=\"#fff\" stop-opacity=\"0\"></stop>\r\n              </linearGradient>\r\n              <linearGradient\r\n                id=\"nextJs__b\"\r\n                x1=\"67.222\"\r\n                x2=\"67.111\"\r\n                y1=\"30\"\r\n                y2=\"59.375\"\r\n                gradientUnits=\"userSpaceOnUse\"\r\n              >\r\n                <stop stop-color=\"#fff\"></stop>\r\n                <stop offset=\"1\" stop-color=\"#fff\" stop-opacity=\"0\"></stop>\r\n              </linearGradient>\r\n            </defs>\r\n          </svg>\r\n        ),\r\n        label: \"NextJS\",\r\n      },\r\n      {\r\n        icon: (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"50\"\r\n            height=\"50\"\r\n            viewBox=\"0 0 100 100\"\r\n            className=\"size-4\"\r\n            fill=\"none\"\r\n          >\r\n            <path\r\n              fill=\"#61DAFB\"\r\n              d=\"M50.307 58.817a8.816 8.816 0 1 0 0-17.632 8.816 8.816 0 0 0 0 17.632\"\r\n            ></path>\r\n            <path\r\n              stroke=\"#61DAFB\"\r\n              stroke-width=\"5\"\r\n              d=\"M50.307 68.063c26.126 0 47.306-8.087 47.306-18.062s-21.18-18.063-47.306-18.063C24.18 31.938 3 40.025 3 50.001s21.18 18.062 47.307 18.062Z\"\r\n            ></path>\r\n            <path\r\n              stroke=\"#61DAFB\"\r\n              stroke-width=\"5\"\r\n              d=\"M34.664 59.032C47.727 81.658 65.321 95.957 73.96 90.969c8.64-4.988 5.053-27.373-8.01-50C52.885 18.343 35.291 4.044 26.652 9.032s-5.052 27.373 8.011 50Z\"\r\n            ></path>\r\n            <path\r\n              stroke=\"#61DAFB\"\r\n              stroke-width=\"5\"\r\n              d=\"M34.664 40.97c-13.063 22.626-16.65 45.011-8.01 50 8.638 4.987 26.232-9.312 39.295-31.938 13.064-22.627 16.65-45.012 8.01-50-8.638-4.988-26.232 9.31-39.295 31.937Z\"\r\n            ></path>\r\n          </svg>\r\n        ),\r\n        label: \"React Components\",\r\n      },\r\n      {\r\n        icon: (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"50\"\r\n            height=\"50\"\r\n            viewBox=\"0 0 100 100\"\r\n            className=\"size-4\"\r\n            fill=\"none\"\r\n          >\r\n            <path\r\n              fill=\"#06B6D4\"\r\n              d=\"M50 20q-20 .001-25 19.994 7.5-9.996 17.5-7.498c3.804.95 6.522 3.71 9.532 6.764 4.902 4.974 10.576 10.732 22.969 10.732q20 0 24.999-19.995-7.5 9.997-17.5 7.499c-3.803-.95-6.521-3.71-9.531-6.764C68.067 25.758 62.392 20.001 50 20.001M25 49.993q-20 0-25 19.995 7.5-9.998 17.5-7.498c3.803.952 6.522 3.71 9.532 6.762C31.933 74.226 37.608 79.984 50 79.984q20 0 25-19.995-7.5 9.998-17.5 7.498c-3.803-.95-6.522-3.71-9.532-6.763C43.066 55.75 37.393 49.992 25 49.992\"\r\n            ></path>\r\n          </svg>\r\n        ),\r\n        label: \"Tailwind CSS Styling\",\r\n      },\r\n      {\r\n        icon: (\r\n          <svg\r\n            width=\"40\"\r\n            height=\"40\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            stroke-width=\"1.5\"\r\n            viewBox=\"0 0 24 24\"\r\n            stroke-linecap=\"round\"\r\n            stroke-linejoin=\"round\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"size-4\"\r\n          >\r\n            <path d=\"m7.63 7.63 3.006 3.006M3.042 4.34l2.616 9.157c.778 2.723 2.933 4.6 5.764 4.6a5.6 5.6 0 0 0 1.264-.148c.392-.09.812-.001 1.097.283l2.46 2.46c.41.41 1.075.41 1.485 0l2.964-2.965a1.05 1.05 0 0 0 0-1.486l-2.455-2.455c-.284-.284-.373-.703-.284-1.094q.143-.624.145-1.27c0-2.832-1.877-4.987-4.6-5.765L4.341 3.042a1.05 1.05 0 0 0-1.3 1.299\"></path>\r\n          </svg>\r\n        ),\r\n        label: \"Responsive Design\",\r\n      },\r\n    ],\r\n    idealFor: [\r\n      \"Corporate Websites\",\r\n      \"Portfolio Showcases\",\r\n      \"Landing Pages\",\r\n      \"Static Directories\",\r\n      \"Simple Project Trackers\",\r\n      \"Content-focused Blogs\",\r\n    ],\r\n    note: \"You can easily upgrade to Full Stack (Next.js + Supabase) later by completing a simple onboarding setup within your project.\",\r\n  },\r\n  fullstack: {\r\n    title: \"Full Stack (Next.js + Supabase)\",\r\n    description:\r\n      \"Build complete web apps with Next.js frontend and Supabase backend. Includes database, auth and storage.\",\r\n    features: [\r\n      {\r\n        icon: <SupabaseIcon className=\"size-4\" />,\r\n        label: \"Secure Authentication\",\r\n      },\r\n      {\r\n        icon: <SupabaseIcon className=\"size-4\" />,\r\n        label: \"Scalable Database\",\r\n      },\r\n      {\r\n        icon: <SupabaseIcon className=\"size-4\" />,\r\n        label: \"Real-time Data Sync\",\r\n      },\r\n      {\r\n        icon: <SupabaseIcon className=\"size-4\" />,\r\n        label: \"Cloud File Storage\",\r\n      },\r\n      {\r\n        icon: (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"50\"\r\n            height=\"50\"\r\n            viewBox=\"0 0 100 100\"\r\n            className=\"size-4\"\r\n            fill=\"none\"\r\n          >\r\n            <path\r\n              fill=\"#000\"\r\n              d=\"M50 100c27.614 0 50-22.386 50-50S77.614 0 50 0 0 22.385 0 50s22.386 50 50 50\"\r\n            ></path>\r\n            <path\r\n              fill=\"url(#nextJs__a)\"\r\n              d=\"M83.06 87.511 38.412 30H30v39.983h6.73V38.546L77.777 91.58a50 50 0 0 0 5.283-4.069\"\r\n            ></path>\r\n            <path fill=\"url(#nextJs__b)\" d=\"M70.556 30h-6.667v40h6.667z\"></path>\r\n            <defs>\r\n              <linearGradient\r\n                id=\"nextJs__a\"\r\n                x1=\"60.556\"\r\n                x2=\"80.278\"\r\n                y1=\"64.722\"\r\n                y2=\"89.166\"\r\n                gradientUnits=\"userSpaceOnUse\"\r\n              >\r\n                <stop stop-color=\"#fff\"></stop>\r\n                <stop offset=\"1\" stop-color=\"#fff\" stop-opacity=\"0\"></stop>\r\n              </linearGradient>\r\n              <linearGradient\r\n                id=\"nextJs__b\"\r\n                x1=\"67.222\"\r\n                x2=\"67.111\"\r\n                y1=\"30\"\r\n                y2=\"59.375\"\r\n                gradientUnits=\"userSpaceOnUse\"\r\n              >\r\n                <stop stop-color=\"#fff\"></stop>\r\n                <stop offset=\"1\" stop-color=\"#fff\" stop-opacity=\"0\"></stop>\r\n              </linearGradient>\r\n            </defs>\r\n          </svg>\r\n        ),\r\n        label: \"API Integration\",\r\n      },\r\n    ],\r\n    idealFor: [\r\n      \"Social Networks\",\r\n      \"Business Directories\",\r\n      \"Project Management Tools\",\r\n      \"Community Platforms\",\r\n      \"E-commerce Solutions\",\r\n      \"Messaging Applications\",\r\n    ],\r\n    note: \"Full Stack projects come with a powerful Next.js frontend and Supabase backend, enabling you to build complex, scalable applications with ease.\",\r\n  },\r\n};\r\n\r\nexport const ProjectTypeContent = ({ type }: { type: \"frontend\" | \"fullstack\" }) => {\r\n  const data = content[type];\r\n\r\n  return (\r\n    <div className=\"mt-2 grid w-full grid-cols-1 gap-6\">\r\n      <div className=\"w-full\">\r\n        <Typography.H4 className=\"mb-3 text-base font-semibold\">Key Features</Typography.H4>\r\n        <ul className=\"flex flex-wrap gap-2 overflow-auto text-sm\">\r\n          {data.features.map((feature, index) => (\r\n            <Badge key={index} variant=\"closed\" className=\"flex cursor-default items-center gap-2\">\r\n              {typeof feature === \"object\" && feature.icon}\r\n              <span className=\"text-primary\">{typeof feature === \"object\" && feature.label}</span>\r\n            </Badge>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n\r\n      <div className=\"w-full\">\r\n        <Typography.H4 className=\"mb-3 text-base font-semibold\">Ideal For</Typography.H4>\r\n        <ul className=\"flex flex-wrap gap-2 overflow-auto text-sm\">\r\n          {data.idealFor.map((item, index) => (\r\n            <Badge\r\n              key={index}\r\n              variant=\"closed\"\r\n              className=\"flex cursor-default items-center gap-2 text-primary dark:text-primary\"\r\n            >\r\n              {item}\r\n            </Badge>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,UAAU;IACd,UAAU;QACR,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBACE,oBACE,6VAAC;oBACC,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,WAAU;oBACV,MAAK;;sCAEL,6VAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6VAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6VAAC;4BAAK,MAAK;4BAAkB,GAAE;;;;;;sCAC/B,6VAAC;;8CACC,6VAAC;oCACC,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,eAAc;;sDAEd,6VAAC;4CAAK,cAAW;;;;;;sDACjB,6VAAC;4CAAK,QAAO;4CAAI,cAAW;4CAAO,gBAAa;;;;;;;;;;;;8CAElD,6VAAC;oCACC,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,eAAc;;sDAEd,6VAAC;4CAAK,cAAW;;;;;;sDACjB,6VAAC;4CAAK,QAAO;4CAAI,cAAW;4CAAO,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;gBAKxD,OAAO;YACT;YACA;gBACE,oBACE,6VAAC;oBACC,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,WAAU;oBACV,MAAK;;sCAEL,6VAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6VAAC;4BACC,QAAO;4BACP,gBAAa;4BACb,GAAE;;;;;;sCAEJ,6VAAC;4BACC,QAAO;4BACP,gBAAa;4BACb,GAAE;;;;;;sCAEJ,6VAAC;4BACC,QAAO;4BACP,gBAAa;4BACb,GAAE;;;;;;;;;;;;gBAIR,OAAO;YACT;YACA;gBACE,oBACE,6VAAC;oBACC,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,WAAU;oBACV,MAAK;8BAEL,cAAA,6VAAC;wBACC,MAAK;wBACL,GAAE;;;;;;;;;;;gBAIR,OAAO;YACT;YACA;gBACE,oBACE,6VAAC;oBACC,OAAM;oBACN,QAAO;oBACP,MAAK;oBACL,QAAO;oBACP,gBAAa;oBACb,SAAQ;oBACR,kBAAe;oBACf,mBAAgB;oBAChB,OAAM;oBACN,WAAU;8BAEV,cAAA,6VAAC;wBAAK,GAAE;;;;;;;;;;;gBAGZ,OAAO;YACT;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;IACR;IACA,WAAW;QACT,OAAO;QACP,aACE;QACF,UAAU;YACR;gBACE,oBAAM,6VAAC,oIAAA,CAAA,UAAY;oBAAC,WAAU;;;;;;gBAC9B,OAAO;YACT;YACA;gBACE,oBAAM,6VAAC,oIAAA,CAAA,UAAY;oBAAC,WAAU;;;;;;gBAC9B,OAAO;YACT;YACA;gBACE,oBAAM,6VAAC,oIAAA,CAAA,UAAY;oBAAC,WAAU;;;;;;gBAC9B,OAAO;YACT;YACA;gBACE,oBAAM,6VAAC,oIAAA,CAAA,UAAY;oBAAC,WAAU;;;;;;gBAC9B,OAAO;YACT;YACA;gBACE,oBACE,6VAAC;oBACC,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,WAAU;oBACV,MAAK;;sCAEL,6VAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6VAAC;4BACC,MAAK;4BACL,GAAE;;;;;;sCAEJ,6VAAC;4BAAK,MAAK;4BAAkB,GAAE;;;;;;sCAC/B,6VAAC;;8CACC,6VAAC;oCACC,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,eAAc;;sDAEd,6VAAC;4CAAK,cAAW;;;;;;sDACjB,6VAAC;4CAAK,QAAO;4CAAI,cAAW;4CAAO,gBAAa;;;;;;;;;;;;8CAElD,6VAAC;oCACC,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,eAAc;;sDAEd,6VAAC;4CAAK,cAAW;;;;;;sDACjB,6VAAC;4CAAK,QAAO;4CAAI,cAAW;4CAAO,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;gBAKxD,OAAO;YACT;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB,CAAC,EAAE,IAAI,EAAsC;IAC7E,MAAM,OAAO,OAAO,CAAC,KAAK;IAE1B,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;wBAAC,WAAU;kCAA+B;;;;;;kCACxD,6VAAC;wBAAG,WAAU;kCACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,6VAAC,iIAAA,CAAA,QAAK;gCAAa,SAAQ;gCAAS,WAAU;;oCAC3C,OAAO,YAAY,YAAY,QAAQ,IAAI;kDAC5C,6VAAC;wCAAK,WAAU;kDAAgB,OAAO,YAAY,YAAY,QAAQ,KAAK;;;;;;;+BAFlE;;;;;;;;;;;;;;;;0BAQlB,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;wBAAC,WAAU;kCAA+B;;;;;;kCACxD,6VAAC;wBAAG,WAAU;kCACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6VAAC,iIAAA,CAAA,QAAK;gCAEJ,SAAQ;gCACR,WAAU;0CAET;+BAJI;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/modal/create-project-modal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport {\r\n  ModalContentInner,\r\n  ModalDescription,\r\n  ModalFooter,\r\n  ModalHeader,\r\n  ModalTitle,\r\n} from \"@/components/ui/modal\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport SupabaseIcon from \"@/features/icon/supabase\";\r\nimport { ProjectTypeContent } from \"@/features/new/project-tabs\";\r\nimport { createProject } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { errorToast, loadingToast } from \"../../global/toast\";\r\n\r\ntype Props = {\r\n  onOpenChange: (isOpen: boolean) => void;\r\n};\r\n\r\nconst formSchema = z.object({\r\n  name: z\r\n    .string()\r\n    .min(1, { message: \"Project name is required\" })\r\n    .transform((value) => value.trim()),\r\n  type: z.enum([\"frontend\", \"fullstack\"]),\r\n  stack: z.enum([\"next.js\", \"vite\"]),\r\n});\r\n\r\ntype FormValues = z.infer<typeof formSchema>;\r\n\r\ninterface ProjectData {\r\n  project_id: string;\r\n  name: string;\r\n  creation_date: string;\r\n  last_updated_date: string;\r\n  env_id: string;\r\n  agent_instructions: string | null;\r\n  agent_continue_instructions: string | null;\r\n  agent_rules_to_follow: string | null;\r\n  onboarding_completed: boolean;\r\n  initial_thread_id: number;\r\n}\r\n\r\nexport interface CreateProjectResponse {\r\n  message: string;\r\n  project_data: ProjectData;\r\n}\r\n\r\ninterface ApiError {\r\n  detail: string;\r\n}\r\n\r\nconst CreateProjectModalContent = ({ onOpenChange }: Props) => {\r\n  const router = useRouter();\r\n  const queryClient = useQueryClient();\r\n\r\n  const form = useForm<FormValues>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      name: \"\",\r\n      type: \"frontend\",\r\n      stack: \"next.js\",\r\n    },\r\n  });\r\n\r\n  const selectedType = form.watch(\"type\");\r\n  const selectedStack = form.watch(\"stack\");\r\n\r\n  const { mutate, isPending } = useMutation<CreateProjectResponse, ApiError, FormValues>({\r\n    mutationKey: [\"create-project\"],\r\n    mutationFn: async (data: FormValues) => {\r\n      const response = await loadingToast(\r\n        \"Creating project...\",\r\n        createProject(data.name, data.stack),\r\n      );\r\n      return response as CreateProjectResponse;\r\n    },\r\n    onSuccess: (data: CreateProjectResponse) => {\r\n      if (data && data.project_data) {\r\n        queryClient.invalidateQueries({ queryKey: [\"projects\"] });\r\n        router.push(`/project/${data.project_data.project_id}?action=chat`);\r\n        onOpenChange(false);\r\n      } else {\r\n        errorToast(\"Error creating project\");\r\n      }\r\n    },\r\n    onError: (error: ApiError) => {\r\n      errorToast(error.detail || \"Error creating project\");\r\n    },\r\n  });\r\n\r\n  const onSubmit = (data: FormValues) => {\r\n    try {\r\n      mutate(data);\r\n    } catch (error) {\r\n      const apiError = error as ApiError;\r\n      errorToast(apiError.detail || \"Error submitting project\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <ModalContentInner className=\"lg:max-w-3xl\">\r\n      <ModalHeader className=\"px-4 md:px-6\">\r\n        <ModalTitle>Create New Project</ModalTitle>\r\n        <ModalDescription>Fill in the details to create your project.</ModalDescription>\r\n      </ModalHeader>\r\n      <div className=\"space-y-6 px-4 py-6 md:p-6\">\r\n        <Form {...form}>\r\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\r\n            <FormField\r\n              control={form.control}\r\n              name=\"name\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Project Name</FormLabel>\r\n                  <FormControl>\r\n                    <Input placeholder=\"Project Name\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n\r\n            <FormField\r\n              control={form.control}\r\n              name=\"stack\"\r\n              render={({ field }) => (\r\n                <FormItem className=\"space-y-3\">\r\n                  <FormLabel>Framework</FormLabel>\r\n                  <FormControl>\r\n                    <RadioGroup\r\n                      onValueChange={field.onChange}\r\n                      defaultValue={field.value}\r\n                      className=\"grid grid-cols-2 gap-4\"\r\n                    >\r\n                      <FormItem className=\"flex items-center space-x-3 space-y-0\">\r\n                        <FormControl>\r\n                          <RadioGroupItem value=\"next.js\" />\r\n                        </FormControl>\r\n                        <FormLabel className=\"font-normal\">Next.js</FormLabel>\r\n                      </FormItem>\r\n                      <FormItem className=\"flex items-center space-x-3 space-y-0\">\r\n                        <FormControl>\r\n                          <RadioGroupItem value=\"vite\" />\r\n                        </FormControl>\r\n                        <FormLabel className=\"font-normal\">Vite</FormLabel>\r\n                      </FormItem>\r\n                    </RadioGroup>\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n\r\n            {selectedStack === \"next.js\" && (\r\n              <FormField\r\n                control={form.control}\r\n                name=\"type\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Project Type</FormLabel>\r\n                    <FormControl>\r\n                      <div className=\"flex w-full flex-col gap-2\">\r\n                        <RadioGroup\r\n                          className=\"gap-2\"\r\n                          defaultValue=\"frontend\"\r\n                          onValueChange={field.onChange}\r\n                        >\r\n                          <div className=\"grid w-full grid-cols-1 gap-2 md:grid-cols-2\">\r\n                            <div\r\n                              className={cn(\r\n                                \"shadow-xs relative flex w-full items-start gap-2 rounded-md border border-border p-4 outline-none\",\r\n                                selectedType === \"frontend\" && \"bg-foreground/5\",\r\n                              )}\r\n                            >\r\n                              <RadioGroupItem\r\n                                value=\"frontend\"\r\n                                id={`${field.name}-frontend`}\r\n                                aria-describedby={`${field.name}-frontend-description`}\r\n                                className=\"order-1 after:absolute after:inset-0\"\r\n                              />\r\n                              <div className=\"flex w-full grow flex-col items-start gap-3\">\r\n                                <div className=\"flex w-full items-start gap-2\">\r\n                                  <svg\r\n                                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                                    width=\"50\"\r\n                                    height=\"50\"\r\n                                    viewBox=\"0 0 100 100\"\r\n                                    className=\"size-6\"\r\n                                    fill=\"none\"\r\n                                  >\r\n                                    <path\r\n                                      fill=\"#000\"\r\n                                      d=\"M50 100c27.614 0 50-22.386 50-50S77.614 0 50 0 0 22.385 0 50s22.386 50 50 50\"\r\n                                    ></path>\r\n                                    <path\r\n                                      fill=\"url(#nextJs__a)\"\r\n                                      d=\"M83.06 87.511 38.412 30H30v39.983h6.73V38.546L77.777 91.58a50 50 0 0 0 5.283-4.069\"\r\n                                    ></path>\r\n                                    <path\r\n                                      fill=\"url(#nextJs__b)\"\r\n                                      d=\"M70.556 30h-6.667v40h6.667z\"\r\n                                    ></path>\r\n                                    <defs>\r\n                                      <linearGradient\r\n                                        id=\"nextJs__a\"\r\n                                        x1=\"60.556\"\r\n                                        x2=\"80.278\"\r\n                                        y1=\"64.722\"\r\n                                        y2=\"89.166\"\r\n                                        gradientUnits=\"userSpaceOnUse\"\r\n                                      >\r\n                                        <stop stop-color=\"#fff\"></stop>\r\n                                        <stop offset=\"1\" stop-color=\"#fff\" stop-opacity=\"0\"></stop>\r\n                                      </linearGradient>\r\n                                      <linearGradient\r\n                                        id=\"nextJs__b\"\r\n                                        x1=\"67.222\"\r\n                                        x2=\"67.111\"\r\n                                        y1=\"30\"\r\n                                        y2=\"59.375\"\r\n                                        gradientUnits=\"userSpaceOnUse\"\r\n                                      >\r\n                                        <stop stop-color=\"#fff\"></stop>\r\n                                        <stop offset=\"1\" stop-color=\"#fff\" stop-opacity=\"0\"></stop>\r\n                                      </linearGradient>\r\n                                    </defs>\r\n                                  </svg>\r\n                                  <div className=\"grid w-full grow gap-2\">\r\n                                    <Label htmlFor={`${field.name}-frontend`}>\r\n                                      Frontend (Next.js)\r\n                                    </Label>\r\n                                    <p\r\n                                      id={`${field.name}-frontend-description`}\r\n                                      className=\"text-xs text-muted-foreground\"\r\n                                    >\r\n                                      Build fast, modern web apps with Next.js. Perfect for\r\n                                      frontend-focused projects.\r\n                                    </p>\r\n                                  </div>\r\n                                </div>\r\n                                <div className=\"w-full md:hidden\">\r\n                                  {selectedType === \"frontend\" && (\r\n                                    <ProjectTypeContent type={selectedType} />\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n\r\n                            <div\r\n                              className={cn(\r\n                                \"shadow-xs relative flex w-full items-start gap-2 rounded-md border border-border p-4 outline-none\",\r\n                                selectedType === \"fullstack\" && \"bg-foreground/5\",\r\n                              )}\r\n                            >\r\n                              <RadioGroupItem\r\n                                value=\"fullstack\"\r\n                                id={`${field.name}-fullstack`}\r\n                                aria-describedby={`${field.name}-fullstack-description`}\r\n                                className=\"order-1 after:absolute after:inset-0\"\r\n                              />\r\n                              <div className=\"flex w-full grow flex-col items-start gap-3\">\r\n                                <div className=\"flex w-full items-start gap-2\">\r\n                                  <SupabaseIcon className=\"size-6\" />\r\n                                  <div className=\"grid w-full grow gap-2\">\r\n                                    <Label htmlFor={`${field.name}-fullstack`}>Full Stack</Label>\r\n                                    <p\r\n                                      id={`${field.name}-fullstack-description`}\r\n                                      className=\"text-xs text-muted-foreground\"\r\n                                    >\r\n                                      Build complete web apps with Next.js frontend and Supabase\r\n                                      backend. Includes database, auth and storage.\r\n                                    </p>\r\n                                  </div>\r\n                                </div>\r\n                                <div className=\"md:hidden\">\r\n                                  {selectedType === \"fullstack\" && (\r\n                                    <ProjectTypeContent type={selectedType} />\r\n                                  )}\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </RadioGroup>\r\n                        <div className=\"hidden md:block\">\r\n                          <ProjectTypeContent type={selectedType} />\r\n                        </div>\r\n                      </div>\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            )}\r\n          </form>\r\n        </Form>\r\n      </div>\r\n      <ModalFooter className=\"px-4 pb-20 md:px-6 md:pb-6\">\r\n        <div className=\"flex w-full items-center md:justify-end\">\r\n          <Button\r\n            onClick={form.handleSubmit(onSubmit)}\r\n            disabled={isPending || !form.formState.isDirty}\r\n            className=\"flex h-9 w-full items-center gap-2 md:w-auto\"\r\n          >\r\n            {isPending && <Loading className=\"size-4 animate-spin text-background\" />}\r\n            Create Project\r\n          </Button>\r\n        </div>\r\n      </ModalFooter>\r\n    </ModalContentInner>\r\n  );\r\n};\r\n\r\nexport default CreateProjectModalContent;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AA/BA;;;;;;;;;;;;;;;;;;;AAqCA,MAAM,aAAa,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,MAAM,mOAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAC7C,SAAS,CAAC,CAAC,QAAU,MAAM,IAAI;IAClC,MAAM,mOAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;KAAY;IACtC,OAAO,mOAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;KAAO;AACnC;AA0BA,MAAM,4BAA4B,CAAC,EAAE,YAAY,EAAS;IACxD,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAc;QAC/B,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,MAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IAEjC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAA+C;QACrF,aAAa;YAAC;SAAiB;QAC/B,YAAY,OAAO;YACjB,MAAM,WAAW,MAAM,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAChC,uBACA,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK;YAErC,OAAO;QACT;QACA,WAAW,CAAC;YACV,IAAI,QAAQ,KAAK,YAAY,EAAE;gBAC7B,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC;gBAClE,aAAa;YACf,OAAO;gBACL,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACb;QACF;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,MAAM,IAAI;QAC7B;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI;YACF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,WAAW;YACjB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,SAAS,MAAM,IAAI;QAChC;IACF;IAEA,qBACE,6VAAC,iIAAA,CAAA,oBAAiB;QAAC,WAAU;;0BAC3B,6VAAC,iIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6VAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,6VAAC,iIAAA,CAAA,mBAAgB;kCAAC;;;;;;;;;;;;0BAEpB,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,6VAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAW,WAAU;;0CACrD,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0DACP,6VAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oDAAC,aAAY;oDAAgB,GAAG,KAAK;;;;;;;;;;;0DAE7C,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAKlB,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6VAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC,0IAAA,CAAA,aAAU;oDACT,eAAe,MAAM,QAAQ;oDAC7B,cAAc,MAAM,KAAK;oDACzB,WAAU;;sEAEV,6VAAC,gIAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,6VAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,6VAAC,0IAAA,CAAA,iBAAc;wEAAC,OAAM;;;;;;;;;;;8EAExB,6VAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;;;;;;;sEAErC,6VAAC,gIAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,6VAAC,gIAAA,CAAA,cAAW;8EACV,cAAA,6VAAC,0IAAA,CAAA,iBAAc;wEAAC,OAAM;;;;;;;;;;;8EAExB,6VAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;0DAIzC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;4BAKjB,kBAAkB,2BACjB,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0DACP,6VAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC;oDAAI,WAAU;;sEACb,6VAAC,0IAAA,CAAA,aAAU;4DACT,WAAU;4DACV,cAAa;4DACb,eAAe,MAAM,QAAQ;sEAE7B,cAAA,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qGACA,iBAAiB,cAAc;;0FAGjC,6VAAC,0IAAA,CAAA,iBAAc;gFACb,OAAM;gFACN,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;gFAC5B,oBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC;gFACtD,WAAU;;;;;;0FAEZ,6VAAC;gFAAI,WAAU;;kGACb,6VAAC;wFAAI,WAAU;;0GACb,6VAAC;gGACC,OAAM;gGACN,OAAM;gGACN,QAAO;gGACP,SAAQ;gGACR,WAAU;gGACV,MAAK;;kHAEL,6VAAC;wGACC,MAAK;wGACL,GAAE;;;;;;kHAEJ,6VAAC;wGACC,MAAK;wGACL,GAAE;;;;;;kHAEJ,6VAAC;wGACC,MAAK;wGACL,GAAE;;;;;;kHAEJ,6VAAC;;0HACC,6VAAC;gHACC,IAAG;gHACH,IAAG;gHACH,IAAG;gHACH,IAAG;gHACH,IAAG;gHACH,eAAc;;kIAEd,6VAAC;wHAAK,cAAW;;;;;;kIACjB,6VAAC;wHAAK,QAAO;wHAAI,cAAW;wHAAO,gBAAa;;;;;;;;;;;;0HAElD,6VAAC;gHACC,IAAG;gHACH,IAAG;gHACH,IAAG;gHACH,IAAG;gHACH,IAAG;gHACH,eAAc;;kIAEd,6VAAC;wHAAK,cAAW;;;;;;kIACjB,6VAAC;wHAAK,QAAO;wHAAI,cAAW;wHAAO,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;0GAItD,6VAAC;gGAAI,WAAU;;kHACb,6VAAC,iIAAA,CAAA,QAAK;wGAAC,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;kHAAE;;;;;;kHAG1C,6VAAC;wGACC,IAAI,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC;wGACxC,WAAU;kHACX;;;;;;;;;;;;;;;;;;kGAML,6VAAC;wFAAI,WAAU;kGACZ,iBAAiB,4BAChB,6VAAC,0IAAA,CAAA,qBAAkB;4FAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kFAMlC,6VAAC;wEACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qGACA,iBAAiB,eAAe;;0FAGlC,6VAAC,0IAAA,CAAA,iBAAc;gFACb,OAAM;gFACN,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;gFAC7B,oBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC;gFACvD,WAAU;;;;;;0FAEZ,6VAAC;gFAAI,WAAU;;kGACb,6VAAC;wFAAI,WAAU;;0GACb,6VAAC,oIAAA,CAAA,UAAY;gGAAC,WAAU;;;;;;0GACxB,6VAAC;gGAAI,WAAU;;kHACb,6VAAC,iIAAA,CAAA,QAAK;wGAAC,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;kHAAE;;;;;;kHAC3C,6VAAC;wGACC,IAAI,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC;wGACzC,WAAU;kHACX;;;;;;;;;;;;;;;;;;kGAML,6VAAC;wFAAI,WAAU;kGACZ,iBAAiB,6BAChB,6VAAC,0IAAA,CAAA,qBAAkB;4FAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAOtC,6VAAC;4DAAI,WAAU;sEACb,cAAA,6VAAC,0IAAA,CAAA,qBAAkB;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0DAIhC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,6VAAC,iIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,KAAK,YAAY,CAAC;wBAC3B,UAAU,aAAa,CAAC,KAAK,SAAS,CAAC,OAAO;wBAC9C,WAAU;;4BAET,2BAAa,6VAAC,mIAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAyC;;;;;;;;;;;;;;;;;;;;;;;AAOtF;uCAEe", "debugId": null}}]}