export type UserFromDb = {
  kinde_id: string;
  is_active: boolean;
  id: number;
  stripe_customer_id: string;
  subscription_id: string;
  subscription_end_date: Date;
  free_total_token: number;
  project_limit: number;
  total_free_request: number;
  is_creating_project: boolean;
  email: string;
  is_superuser: boolean;
  plan: "free-tier" | string;
  isSubscribed: boolean;
  token_event_name: string;
  isRequestBased: boolean;
  isFreeTrial: boolean;
  wallet_balance: number;
};

export type AuthState = {
  access_token: string | null;
  email: string | null;
  kinde_id: string | null;
  userFromDb: UserFromDb | null;
  wholesalePlan?: "free" | "paid";
  customer_id: string | null;
  isSubscribed: boolean;
  isLoading: boolean;
  showPricingDialog: boolean;
  cookie?: {
    access_token: string | null;
    kinde_id: string | null;
  };
};
