"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/classic/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loading from "@/components/ui/loading";
import { ModalContentInner, ModalHeader, ModalTitle } from "@/components/ui/modal";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import Typography from "@/components/ui/typography";
import {
  closeFilesInEditor,
  getOpenFiles,
  getProjectPrompts,
  openFilesInEditor,
  updateCustomInstructions,
  updateProjectPrompts,
  updateTechStack,
} from "@/lib/api";
import { cn } from "@/lib/utils";
import { Project, useProject } from "@/providers/project-provider";
import { examplePrompts } from "@/utils/example-prompts";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AcademicHatSolid,
  Command,
  DotsVertical,
  Edit,
  File,
  Tool,
  Trash,
} from "@mynaui/icons-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { errorToast, loadingToast, successToast } from "../global/toast";
import SoftgenIcon from "../thread/thread-message/softgen-icon";
import { CACHE_PARAMS } from "@/providers/query-provider";

type Props = {
  projectId: string;
  setModal: (modal: boolean) => void;
  setFormContent: (content: string) => void;
  formContent: string;
  availableFiles: string[];
};

type SettingsTab = "prompt" | "custom-knowledge" | "tech-stack" | "file-context";

type TechStackEntry = { key: string; value: string };

const techStackEntrySchema = z.object({
  key: z.string().min(1, "Key is required"),
  value: z.string().min(1, "Value is required"),
});

type TechStackEntryFormData = z.infer<typeof techStackEntrySchema>;

const customInstructionsFormSchema = z.object({
  customInstructions: z.string(),
});

type CustomInstructionsFormData = z.infer<typeof customInstructionsFormSchema>;

const promptSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
});

type PromptFormValues = z.infer<typeof promptSchema>;

const AgentSettingModalContent = ({
  projectId,
  setModal,
  setFormContent,
  formContent,
  availableFiles,
}: Props) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>("prompt");
  const { project } = useProject();

  const handleTabChange = (tab: SettingsTab) => {
    setActiveTab(tab);
  };

  const tabs: { id: SettingsTab; label: string; icon: React.ReactNode; pro?: boolean }[] = [
    { id: "prompt", label: "Prompts", icon: <Command /> },
    { id: "custom-knowledge", label: "Knowledge", icon: <AcademicHatSolid /> },
    { id: "tech-stack", label: "Tech Stack", icon: <Tool /> },
    { id: "file-context", label: "File Context", icon: <File /> },
  ];

  const handleUsePrompt = (prompt: { key: string; value: string }) => {
    const content = `${formContent}\n\n${prompt.key}\n${prompt.value}`;

    setFormContent(content);
  };

  const handleUseExamplePrompt = (prompt: { title: string; content: string }) => {
    const rawContent = `${formContent}\n\n${prompt.title}\n${prompt.content}`;

    setFormContent(rawContent);
    setModal(false);
  };

  return (
    <ModalContentInner className="w-full lg:max-w-5xl">
      <div className="grid h-[600px] grid-cols-1 md:grid-cols-12">
        <div className="col-span-1 hidden flex-col border-r border-ring/15 bg-sidebar-ring/10 px-3 md:col-span-3 md:flex">
          <div className="flex flex-col gap-4 space-y-2 px-2 py-5 text-left">
            <SoftgenIcon />
          </div>

          <div className="flex flex-1 flex-col gap-1">
            {tabs
              .sort((a, b) => (a.pro ? 1 : b.pro ? -1 : 0))
              .map((tab) => (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? "default" : "ghost"}
                  className="h-fit w-full justify-between rounded-md px-3 py-2"
                  onClick={() => handleTabChange(tab.id)}
                >
                  <div className="flex items-center gap-2">
                    {tab.icon}
                    {tab.id === "prompt" ? "Prompts" : tab.label}
                  </div>
                  {tab.pro && (
                    <Badge variant="terminal" className="px-2 py-0.5">
                      Pro
                    </Badge>
                  )}
                </Button>
              ))}
          </div>
        </div>

        <div className="col-span-1 w-full md:col-span-9">
          <ModalHeader>
            <ModalTitle>
              {tabs.find((tab) => tab.id === activeTab)?.label ||
                activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </ModalTitle>
          </ModalHeader>

          <ScrollArea>
            <div className="flex flex-row gap-2 overflow-x-auto p-6 py-3 md:hidden">
              {tabs
                .sort((a, b) => (a.pro ? 1 : b.pro ? -1 : 0))
                .map((tab) => (
                  <Button
                    key={tab.id}
                    variant={activeTab === tab.id ? "default" : "ghost"}
                    className="h-fit justify-between whitespace-nowrap rounded-md px-3 py-2"
                    onClick={() => {
                      handleTabChange(tab.id);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {tab.icon}
                      {tab.label}
                    </div>
                  </Button>
                ))}
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>

          <div className="col-span-1 md:col-span-9">
            <div className="p-4 px-6">
              {activeTab === "custom-knowledge" && (
                <SettingsCustomKnowledge
                  project={project || ({} as Project)}
                  projectId={projectId}
                  setModal={setModal}
                />
              )}
              {activeTab === "tech-stack" && <SettingsTechStack projectId={projectId} />}
              {activeTab === "prompt" && (
                <SettingsPrompt
                  projectId={projectId}
                  handleUsePrompt={handleUsePrompt}
                  handleUseExamplePrompt={handleUseExamplePrompt}
                />
              )}
              {activeTab === "file-context" && (
                <SettingsFileContext projectId={projectId} availableFiles={availableFiles} />
              )}
            </div>
          </div>
        </div>
      </div>
    </ModalContentInner>
  );
};

const SettingsCustomKnowledge = ({
  project,
  projectId,
  setModal,
}: {
  project: Project;
  projectId: string;
  setModal: (modal: boolean) => void;
}) => {
  const queryClient = useQueryClient();

  const customInstructionsForm = useForm<CustomInstructionsFormData>({
    resolver: zodResolver(customInstructionsFormSchema),
    defaultValues: {
      customInstructions: project.custom_instructions || "",
    },
  });

  const updateCustomInstructionsMutation = useMutation({
    mutationFn: (customInstructions: string) =>
      updateCustomInstructions(projectId, customInstructions),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-project", projectId] });
      successToast("Custom instructions updated successfully");
    },
    onError: (error) => {
      console.error("Error updating custom instructions:", error);
      errorToast("Failed to update custom instructions. Please try again.");
    },
  });

  const onSubmitCustomInstructions = async (data: CustomInstructionsFormData) => {
    try {
      await updateCustomInstructionsMutation.mutateAsync(data.customInstructions);
      customInstructionsForm.reset(data);
    } catch (error) {
      console.error("Error updating custom instructions:", error);
      errorToast("Failed to update custom instructions. Please try again.");
    }
  };

  return (
    <Form {...customInstructionsForm}>
      <form
        onSubmit={customInstructionsForm.handleSubmit(onSubmitCustomInstructions)}
        className="flex flex-col gap-2"
      >
        <FormField
          control={customInstructionsForm.control}
          name="customInstructions"
          render={({ field }) => (
            <FormItem className="space-y-4">
              <div className="flex flex-col gap-2">
                <div className="flex flex-col gap-2">
                  <Typography.H5>Best Practices</Typography.H5>
                  <Typography.P>
                    Add any specific instructions, conventions, or knowledge that the AI should
                    consider when working with your project.
                  </Typography.P>
                </div>
                <FormControl>
                  {/* <Textarea
                    {...field}
                    defaultValue={project.custom_instructions || ""}
                    placeholder="Add custom knowledge or instructions..."
                    className="h-[25rem]"
                  /> */}
                  <textarea
                    id="custom-instructions"
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                    className={cn(
                      "h-[20rem] w-full resize-none rounded-md border-0 bg-muted p-4 text-sm text-primary",
                      "ring-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0",
                    )}
                    placeholder={`Add custom knowledge or instructions...`}
                  />
                </FormControl>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />

        <div className="flex w-full items-center justify-end">
          <div className="flex items-center gap-2">
            <Button type="button" variant="ghost" onClick={() => setModal(false)}>
              Cancel
            </Button>
            <Button type="submit" className="flex h-8 items-center gap-2">
              {updateCustomInstructionsMutation.isPending ? (
                <Loading className="size-4 animate-spin text-background" />
              ) : null}
              Save Changes
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

const SettingsTechStack = ({ projectId }: { projectId: string }) => {
  const queryClient = useQueryClient();
  const { project, refetch } = useProject();
  const techStackEntries: TechStackEntry[] = (() => {
    if (!project?.tech_stack_prompt) return [];

    if (typeof project.tech_stack_prompt === "string") {
      try {
        const parsed = JSON.parse(project.tech_stack_prompt);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        console.error("Error parsing tech stack prompt:", e);
        return [];
      }
    }

    if (Array.isArray(project.tech_stack_prompt)) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return project.tech_stack_prompt.map((item: any) => {
        if (typeof item === "string") {
          return { key: item, value: item };
        }
        return item as TechStackEntry;
      });
    }

    return [];
  })();

  const techStackForm = useForm<TechStackEntryFormData>({
    resolver: zodResolver(techStackEntrySchema),
    defaultValues: {
      key: "",
      value: "",
    },
  });

  const onSubmit = async (data: TechStackEntryFormData) => {
    const newEntries = [...techStackEntries, { key: data.key, value: data.value }];

    loadingToast(
      "Adding",
      updateTechStack(projectId, newEntries).then(() => {
        queryClient.invalidateQueries({ queryKey: ["get-project", projectId] });
        refetch();
        techStackForm.reset();
        return { message: "Tech stack updated successfully" };
      }),
    );
  };

  const handleRemoveTechStackEntry = (indexToRemove: number) => {
    const newEntries = techStackEntries.filter((_, index) => index !== indexToRemove);

    loadingToast(
      "Removing tech stack entry",
      updateTechStack(projectId, newEntries).then(() => {
        queryClient.invalidateQueries({ queryKey: ["get-project", projectId] });
        refetch();
        return { message: "Tech stack entry removed successfully" };
      }),
    );
  };

  return (
    <Form {...techStackForm}>
      <div className="flex flex-col gap-4">
        <form onSubmit={techStackForm.handleSubmit(onSubmit)} className="flex flex-col gap-4">
          <FormField
            control={techStackForm.control}
            name="key"
            render={({ field }) => (
              <FormItem>
                <Typography.H5>Name</Typography.H5>
                <FormControl>
                  <Input placeholder="Enter tech stack name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={techStackForm.control}
            name="value"
            render={({ field }) => (
              <FormItem>
                <Typography.H5>Description</Typography.H5>
                <FormControl>
                  <Textarea placeholder="Enter tech stack description" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit">Add</Button>
        </form>
        <div className="mt-6">
          <h3 className="mb-2 font-semibold">Current Tech Stack</h3>
          <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
            {techStackEntries.map((entry, index) => (
              <div
                key={index}
                className={cn(
                  "group flex flex-col justify-between gap-1 rounded-xl bg-sidebar-ring/10 p-3 px-4",
                )}
              >
                <div className="flex w-full items-center justify-between gap-2">
                  <Label className="text-base font-semibold">{entry.key}</Label>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="destructive"
                      size="icon"
                      className="m-0 hidden h-6 w-6 transition-all duration-700 ease-in group-hover:flex"
                      onClick={() => handleRemoveTechStackEntry(index)}
                    >
                      <Trash />
                    </Button>
                  </div>
                </div>
                <div className="text-sm">{entry.value}</div>
              </div>
            ))}
            {techStackEntries.length === 0 && (
              <div className="col-span-2 rounded-xl bg-sidebar-ring/10 p-3 px-4 text-sm text-sidebar-foreground">
                No tech stack entries yet. Add some above.
              </div>
            )}
          </div>
        </div>
      </div>
    </Form>
  );
};

const SettingsPrompt = ({
  projectId,
  handleUsePrompt,
  handleUseExamplePrompt,
}: {
  projectId: string;
  handleUsePrompt: (prompt: { key: string; value: string }) => void;
  handleUseExamplePrompt: (prompt: { title: string; content: string }) => void;
}) => {
  const [editingPromptIndex, setEditingPromptIndex] = useState<number>(-1);

  const form = useForm<PromptFormValues>({
    resolver: zodResolver(promptSchema),
    defaultValues: {
      title: "",
      content: "",
    },
  });

  const {
    data: projectPrompts,
    isLoading: isLoadingPrompts,
    refetch: refetchPrompts,
  } = useQuery<{
    project_id: string;
    prompts: Array<{ key: string; value: string }>;
  }>({
    queryKey: ["prompts", projectId],
    queryFn: async () => {
      const prompts = await getProjectPrompts(projectId);
      return prompts;
    },
    ...CACHE_PARAMS
  });

  const addPromptMutation = useMutation({
    mutationFn: async (
      values: PromptFormValues,
    ): Promise<{
      message: string;
      project_id: string;
      prompts: Array<{ key: string; value: string }>;
    }> => {
      const newPrompt = { key: values.title, value: values.content };
      const currentPrompts = projectPrompts?.prompts || [];
      const updatedPrompts = [...currentPrompts, newPrompt];
      const apiPrompts = updatedPrompts.map((p) => ({ key: p.key, value: p.value }));
      return await updateProjectPrompts(projectId, apiPrompts);
    },
    onSuccess: () => {
      refetchPrompts();
      form.reset();
    },
  });

  const updatePromptMutation = useMutation({
    mutationFn: async (values: PromptFormValues & { index: number }) => {
      const updatedPrompts = [...(projectPrompts?.prompts || [])];
      updatedPrompts[values.index] = { key: values.title, value: values.content };
      const apiPrompts = updatedPrompts.map((p) => ({ key: p.key, value: p.value }));
      return await updateProjectPrompts(projectId, apiPrompts);
    },
    onSuccess: () => {
      refetchPrompts();
      form.reset();
      setEditingPromptIndex(-1);
    },
  });

  const deletePromptMutation = useMutation({
    mutationFn: async (index: number) => {
      const apiPrompts = projectPrompts?.prompts
        .filter((_, i) => i !== index)
        .map((p) => ({ key: p.key, value: p.value }));
      if (!apiPrompts) return;
      return await updateProjectPrompts(projectId, apiPrompts);
    },
    onSuccess: () => {
      refetchPrompts();
    },
  });

  const onSubmit = (values: PromptFormValues) => {
    if (editingPromptIndex >= 0) {
      updatePromptMutation.mutate({ ...values, index: editingPromptIndex });
    } else {
      addPromptMutation.mutate(values);
    }
  };

  const handleEditPrompt = (index: number) => {
    const prompt = projectPrompts?.prompts[index];
    form.setValue("title", prompt?.key || "");
    form.setValue("content", prompt?.value || "");
    setEditingPromptIndex(index);
  };

  const handleDeletePrompt = (index: number) => {
    loadingToast(
      "Deleting prompt",
      deletePromptMutation.mutateAsync(index).then(() => {
        refetchPrompts();
        return { message: "Prompt deleted successfully" };
      }),
      {},
      "Prompt deleted",
    );
  };

  return (
    <div className="space-y-4">
      <div className="space-y-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <Typography.H5>Title</Typography.H5>
                  <FormControl>
                    <Input placeholder="Prompt Title" className="h-10" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <Typography.H5>Content</Typography.H5>
                  <FormControl>
                    <Textarea placeholder="Prompt Content" className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              disabled={addPromptMutation.isPending || updatePromptMutation.isPending}
              className="w-full"
            >
              {addPromptMutation.isPending || updatePromptMutation.isPending ? (
                <>
                  <Loading className="size-4 text-background" />
                  {editingPromptIndex >= 0 ? "Updating..." : "Adding..."}
                </>
              ) : editingPromptIndex >= 0 ? (
                "Update Prompt"
              ) : (
                "Add Prompt"
              )}
            </Button>
          </form>
        </Form>

        <Tabs defaultValue="prompts">
          <TabsList>
            <TabsTrigger value="prompts">Prompts</TabsTrigger>
            <TabsTrigger value="example-prompts">Example Prompts</TabsTrigger>
          </TabsList>

          <TabsContent value="prompts">
            <div className="space-y-4">
              <h3 className="mb-2 font-semibold">Your Prompts</h3>
              {isLoadingPrompts ? (
                <div className="flex items-center justify-center py-4">
                  <Loading className="size-6" />
                </div>
              ) : projectPrompts?.prompts.length === 0 ? (
                <div className="rounded-xl bg-sidebar-ring/10 p-3 px-4 text-sm text-sidebar-foreground">
                  No custom prompts found. Add some above.
                </div>
              ) : (
                <div className="relative columns-1 gap-3 space-y-3 md:columns-2">
                  {projectPrompts?.prompts.map((prompt, index) => (
                    <div
                      key={index}
                      className={cn(
                        "group flex flex-col justify-between gap-1 rounded-xl bg-sidebar-ring/10 p-3 px-4",
                        "h-fit w-full break-inside-avoid",
                      )}
                    >
                      <div className="flex w-full items-center justify-between gap-2">
                        <Label className="text-base font-semibold">{prompt.key}</Label>
                        <div className="flex items-center gap-1">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className={cn(
                                  "group flex cursor-pointer items-center justify-start gap-3 rounded-lg px-2.5 text-sm text-primary transition-all duration-500 hover:bg-sidebar-accent/80",
                                )}
                              >
                                <DotsVertical />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleUsePrompt(prompt)}
                                className="flex items-center justify-between gap-3"
                              >
                                Use
                                <Tool className="size-4" strokeWidth={2} />
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleEditPrompt(index)}
                                className="flex items-center justify-between gap-3"
                              >
                                Edit
                                <Edit className="size-4" strokeWidth={2} />
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeletePrompt(index)}
                                className="flex items-center justify-between gap-3"
                              >
                                Delete
                                <Trash className="size-4" strokeWidth={2} />
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      <div className="text-sm">{prompt.value}</div>
                    </div>
                  ))}
                  {projectPrompts?.prompts.length === 0 && (
                    <div className="col-span-2 rounded-xl bg-sidebar-ring/10 p-3 px-4 text-sm text-sidebar-foreground">
                      No custom prompts found. Add some above.
                    </div>
                  )}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="example-prompts">
            <div className="space-y-4">
              <h3 className="font-semibold">Example Prompts</h3>
              <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                {examplePrompts.map((category, index) => (
                  <div
                    key={index}
                    className="flex flex-col items-start justify-start gap-3 rounded-xl border bg-sidebar-ring/10 p-3 px-4"
                  >
                    <div className="flex flex-col gap-1">
                      <Label className="text-base font-medium">{category.title}</Label>
                      <p className="text-sm text-muted-foreground">{category.description}</p>
                    </div>
                    <div className="flex flex-col gap-1">
                      {category.prompts.map((prompt, promptIndex) => (
                        <Button
                          key={promptIndex}
                          size="sm"
                          className="w-full md:w-fit"
                          onClick={() => handleUseExamplePrompt(prompt)}
                        >
                          Try out
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

const SettingsFileContext = ({
  projectId,
  availableFiles,
}: {
  projectId: string;
  availableFiles: string[];
}) => {
  // State declarations
  const [openFiles, setOpenFiles] = useState<string[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(true);
  const [selectedFilesToClose, setSelectedFilesToClose] = useState<string[]>([]);
  const [selectedFilesToOpen, setSelectedFilesToOpen] = useState<string[]>([]);
  const [isClosingFiles, setIsClosingFiles] = useState(false);
  const [isOpeningFiles, setIsOpeningFiles] = useState(false);
  const [fileContextSearchQuery, setFileContextSearchQuery] = useState("");
  const [fileContextFilteredFiles, setFileContextFilteredFiles] = useState<string[]>([]);
  const [fileContextShowSelector, setFileContextShowSelector] = useState(false);
  const [fileContextSelectedIndex, setFileContextSelectedIndex] = useState(0);

  // Function declarations
  const fetchOpenFiles = useCallback(async () => {
    try {
      setIsLoadingFiles(true);
      const response = await getOpenFiles(projectId);
      if (response?.data) {
        setOpenFiles(
          response.data.map((file: { path: string } | string) =>
            typeof file === "string" ? file : file.path,
          ),
        );
      }
    } catch (error) {
      console.error("Error fetching open files:", error);
      errorToast("Failed to fetch open files");
    } finally {
      setIsLoadingFiles(false);
    }
  }, [projectId]);

  const handleFileContextInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFileContextSearchQuery(value);

    if (value.trim()) {
      const filtered = availableFiles.filter((file) =>
        file.toLowerCase().includes(value.toLowerCase()),
      );
      setFileContextFilteredFiles(filtered);
      setFileContextShowSelector(true);
      setFileContextSelectedIndex(0);
    } else {
      setFileContextShowSelector(false);
    }
  };

  const handleFileContextKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (fileContextShowSelector && fileContextFilteredFiles.length > 0) {
      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setFileContextSelectedIndex((prev) =>
            prev < fileContextFilteredFiles.length - 1 ? prev + 1 : prev,
          );
          break;
        case "ArrowUp":
          e.preventDefault();
          setFileContextSelectedIndex((prev) => (prev > 0 ? prev - 1 : prev));
          break;
        case "Enter":
          e.preventDefault();
          const selectedFile = fileContextFilteredFiles[fileContextSelectedIndex];
          setSelectedFilesToOpen((prev) => [...prev, selectedFile]);
          setFileContextSearchQuery("");
          setFileContextShowSelector(false);
          break;
        case "Escape":
          e.preventDefault();
          setFileContextShowSelector(false);
          break;
        case "Tab":
          e.preventDefault();
          const tabSelectedFile = fileContextFilteredFiles[fileContextSelectedIndex];
          setSelectedFilesToOpen((prev) => [...prev, tabSelectedFile]);
          setFileContextSearchQuery("");
          setFileContextShowSelector(false);
          break;
        default:
          break;
      }
    }
  };

  const handleCloseFiles = async () => {
    if (selectedFilesToClose.length === 0) return;

    try {
      setIsClosingFiles(true);
      await closeFilesInEditor(projectId, selectedFilesToClose);
      await fetchOpenFiles();
      setSelectedFilesToClose([]);
      successToast("Files closed successfully");
    } catch (error) {
      console.error("Error closing files:", error);
      errorToast("Failed to close files");
    } finally {
      setIsClosingFiles(false);
    }
  };

  const handleOpenFiles = async () => {
    if (selectedFilesToOpen.length === 0) return;

    try {
      setIsOpeningFiles(true);
      await openFilesInEditor(projectId, selectedFilesToOpen);
      await fetchOpenFiles();
      setSelectedFilesToOpen([]);
      successToast("Files opened successfully");
    } catch (error) {
      console.error("Error opening files:", error);
      errorToast("Failed to open files");
    } finally {
      setIsOpeningFiles(false);
    }
  };

  // Effects
  useEffect(() => {
    if (projectId) {
      fetchOpenFiles();
    }
  }, [projectId, fetchOpenFiles]);

  // Render
  return (
    <div className="space-y-4">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold">Open Files</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCloseFiles}
            disabled={selectedFilesToClose.length === 0 || isClosingFiles}
            className="h-9 px-4"
          >
            {isClosingFiles ? (
              <>
                <Loading className="mr-2 h-4 w-4 animate-spin" />
                Closing...
              </>
            ) : (
              <>
                <X className="mr-2 h-4 w-4" />
                Close Selected
              </>
            )}
          </Button>
        </div>
      </div>

      {isLoadingFiles ? (
        <div className="flex items-center justify-center py-4">
          <Loading className="h-6 w-6 animate-spin" />
        </div>
      ) : openFiles.length === 0 ? (
        <p className="text-sm text-muted-foreground">No files currently open</p>
      ) : (
        <div className="space-y-2">
          <div className="flex items-center gap-2 rounded-t-lg border-b bg-muted/30 px-3 py-2">
            <Checkbox
              checked={selectedFilesToClose.length === openFiles.length && openFiles.length > 0}
              onCheckedChange={(checked) => {
                if (checked) {
                  setSelectedFilesToClose(openFiles);
                } else {
                  setSelectedFilesToClose([]);
                }
              }}
              aria-label="Select all files"
            />
            <span className="text-xs font-medium">Select All</span>
          </div>
          {openFiles.map((file, index) => (
            <div
              key={index}
              className="flex items-center justify-between rounded-lg border bg-card p-3"
            >
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedFilesToClose.includes(file)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedFilesToClose((prev) => [...prev, file]);
                    } else {
                      setSelectedFilesToClose((prev) => prev.filter((f) => f !== file));
                    }
                  }}
                  aria-label={`Select file ${file}`}
                />
                <File className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">{file}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-6">
        <h4 className="mb-4 text-sm font-medium">Open New Files</h4>
        <div className="space-y-4">
          <div className="relative">
            <Input
              placeholder="Type to search files..."
              value={fileContextSearchQuery}
              onChange={handleFileContextInput}
              onKeyDown={handleFileContextKeyDown}
              className="h-10"
            />
            {fileContextShowSelector && fileContextFilteredFiles.length > 0 && (
              <div className="absolute z-20 mt-1 max-h-40 w-full overflow-y-auto rounded-md border bg-background shadow-lg">
                {fileContextFilteredFiles.map((file, index) => (
                  <div
                    key={file}
                    className={cn(
                      "flex cursor-pointer items-center gap-2 px-3 py-2 text-sm hover:bg-secondary/50",
                      fileContextSelectedIndex === index && "bg-secondary",
                    )}
                    onClick={() => {
                      setSelectedFilesToOpen((prev) => [...prev, file]);
                      setFileContextSearchQuery("");
                      setFileContextShowSelector(false);
                    }}
                  >
                    <File className="h-4 w-4 text-yellow-500" />
                    <div className="flex flex-col">
                      <span className="truncate font-medium">{file.split("/").pop()}</span>
                      <span className="truncate text-xs text-muted-foreground">{file}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedFilesToOpen.map((file, index) => (
              <div
                key={index}
                className="flex items-center gap-1 rounded-md bg-secondary/20 px-2 py-1 text-xs"
              >
                <File className="h-3 w-3 text-yellow-500" />
                <span>{file}</span>
                <button
                  type="button"
                  onClick={() =>
                    setSelectedFilesToOpen((prev) => prev.filter((_, i) => i !== index))
                  }
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
          <Button
            onClick={handleOpenFiles}
            disabled={selectedFilesToOpen.length === 0 || isOpeningFiles}
            className="w-full"
          >
            {isOpeningFiles ? (
              <>
                <Loading className="mr-2 h-4 w-4 animate-spin" />
                Opening...
              </>
            ) : (
              "Open Files"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AgentSettingModalContent;
