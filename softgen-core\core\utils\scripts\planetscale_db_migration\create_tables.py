import asyncio
import ssl
from sqlalchemy import MetaData, Table, Column, Integer, String, Float, Boolean, Text, create_engine
from core.config import settings

# Define tables manually without foreign key constraints
metadata = MetaData()

users = Table(
    'users', metadata,
    Column('id', Integer, primary_key=True),
    Column('kinde_id', String(255), unique=True, nullable=False),
    Column('email', String(255), unique=True, nullable=False),
    <PERSON>umn('is_active', Boolean, default=True),
    <PERSON>umn('is_superuser', Boolean, default=False),
    Column('stripe_customer_id', String(255), nullable=True, unique=True),
    <PERSON>umn('plan', String(50), nullable=True),
    <PERSON>umn('subscription_id', String(255), nullable=True),
    <PERSON>umn('isSubscribed', Boolean, default=False),
    Column('subscription_end_date', String(50), nullable=True),
    <PERSON>umn('free_total_token', Integer, default=0),
    <PERSON>umn('token_event_name', String(100), nullable=True),
    <PERSON>umn('project_limit', Integer, default=0),
    <PERSON>umn('isRequestBased', <PERSON>olean, default=False, nullable=True),
    Column('total_free_request', Integer, default=0, nullable=True),
    Column('isFreeTrial', Boolean, default=False, nullable=True),
    Column('is_creating_project', Boolean, default=False)
)

transactions = Table(
    'transactions', metadata,
    Column('id', Integer, primary_key=True),
    Column('kinde_id', String(255)),  # No foreign key constraint
    Column('stripe_payment_id', String(255), unique=True, nullable=False),
    Column('amount', Float, nullable=False),
    Column('currency', String(10), nullable=False),
    Column('status', String(50), nullable=False),
    Column('created_at', String(50))
)

projects = Table(
    'projects', metadata,
    Column('project_id', String(36), primary_key=True),
    Column('name', String(255)),
    Column('creation_date', String(50)),
    Column('last_updated_date', String(50)),
    Column('env_id', String(50)),
    Column('owner_id', Integer, nullable=False),  # No foreign key constraint
    Column('preview_image_url', String(255), nullable=True),
    Column('total_input_token', Integer, default=0),
    Column('total_output_token', Integer, default=0),
    Column('total_request', Integer, default=0),
    Column('agent_instructions', Text, nullable=True),
    Column('agent_continue_instructions', Text, nullable=True),
    Column('agent_rules_to_follow', Text, nullable=True),
    Column('tech_stack_prompt', Text, nullable=True),
    Column('custom_instructions', Text, nullable=True),
    Column('onboarding_completed', Boolean, default=False),
    Column('browser_session_id', String(255)),
    Column('isPublic', Boolean, default=False),
    Column('team_emails', Text)
)

deployments = Table(
    'deployments', metadata,
    Column('id', Integer, primary_key=True),
    Column('project_id', String(36), unique=True),  # No foreign key constraint
    Column('vercel_token', String(255), nullable=True),
    Column('deployment_url', String(255), nullable=True),
    Column('production_url', String(255), nullable=True),
    Column('last_deployed', String(50), nullable=True),
    Column('is_deployed', Boolean, default=False)
)

project_threads = Table(
    'project_threads', metadata,
    Column('thread_id', Integer, primary_key=True),
    Column('project_id', String(36)),  # No foreign key constraint
    Column('messages', Text(length=4294967295)),  # LONGTEXT
    Column('creation_date', String(50)),
    Column('last_updated_date', String(50)),
    Column('page_route', String(255), nullable=True)
)

memory_modules = Table(
    'memory_modules', metadata,
    Column('id', Integer, primary_key=True),
    Column('project_id', String(36)),  # No foreign key constraint
    Column('module_name', String(100)),
    Column('data', Text(length=4294967295))  # LONGTEXT
)

project_thread_runs = Table(
    'project_thread_runs', metadata,
    Column('run_id', Integer, primary_key=True),
    Column('thread_id', Integer),  # No foreign key constraint
    Column('messages', Text(length=4294967295)),  # LONGTEXT
    Column('creation_date', String(50)),
    Column('working_memory', Text(length=4294967295)),  # LONGTEXT
    Column('project_agent_run', Integer)  # No foreign key constraint
)

project_agent_run = Table(
    'project_agent_run', metadata,
    Column('run_id', Integer, primary_key=True),
    Column('project_id', String(36)),  # No foreign key constraint
    Column('thread_id', Integer, unique=True),  # No foreign key constraint
    Column('creation_date', String(50)),
    Column('objective', Text),
    Column('status', String(50))
)

blogs = Table(
    'blogs', metadata,
    Column('id', String(36), primary_key=True),
    Column('slug', String(255), unique=True, nullable=False),
    Column('headline', String(255), nullable=False),
    Column('meta_description', Text, nullable=False),
    Column('image', String(255), nullable=False),
    Column('created_at', String(50), nullable=False),
    Column('updated_at', String(50), nullable=True),
    Column('category_title', String(100), nullable=False),
    Column('category_slug', String(100), nullable=False),
    Column('tags', Text, nullable=True),
    Column('mdx', Text, nullable=True)
)

prize_submissions = Table(
    'prize_submissions', metadata,
    Column('id', Integer, primary_key=True),
    Column('url', String(255), nullable=False),
    Column('project_id', String(36), nullable=False),  # No foreign key constraint
    Column('user_id', Integer, nullable=False),  # No foreign key constraint
    Column('description', Text, nullable=True),
    Column('prize_granted', Boolean, default=False),
    Column('prize_amount', Float, nullable=True),
    Column('created_at', String(50))
)

environments = Table(
    'environments', metadata,
    Column('id', Integer, primary_key=True),
    Column('env_id', String(50), unique=True, nullable=False),
    Column('assigned', Boolean, default=False),
    Column('status', String(20)),  # Using String instead of Enum
    Column('project_id', String(36), unique=True, nullable=True),  # No foreign key constraint
    Column('github_repo', String(255), nullable=True),
    Column('template', String(255))
)

async def create_tables():
    print("Creating tables in MySQL without foreign key constraints...")
    
    # Get MySQL connection info from settings
    mysql_url = settings.database_url
    
    # Only add SSL context for MySQL connection
    connect_args = {}
    if 'mysql' in mysql_url:
        ssl_context = ssl.create_default_context(cafile="/etc/ssl/certs/ca-certificates.crt")
        ssl_context.verify_mode = ssl.CERT_REQUIRED
        connect_args = {"ssl": ssl_context}

    # Create a synchronous engine for table creation
    # Replace aiomysql with pymysql for synchronous operations
    sync_url = mysql_url.replace('mysql+aiomysql', 'mysql+pymysql')
    engine = create_engine(sync_url, connect_args=connect_args)
    
    # Create all tables
    metadata.create_all(engine)
    
    print("Tables created successfully!")

if __name__ == "__main__":
    asyncio.run(create_tables()) 