import logging
from typing import Dict, Any, List
from dataclasses import dataclass
from core.utils import Unit, ToolResult
from core.db import Database

@dataclass
class SupabaseTool(Unit):
    db: Database
    project_id: str

    def __init__(self, db: Database, project_id: str):
        super().__init__()
        self.db = db
        self.project_id = project_id

    async def execute_sql_query(self, sql_query: str) -> ToolResult:
        """
        Execute a SQL query on the connected Supabase project.
        
        Args:
            sql_query (str): The SQL query to execute
            
        Returns:
            ToolResult: Result of the SQL query execution
        """
        try:
            logging.info(f"Executing SQL query on Supabase for project {self.project_id}")
            
            # Use the shared SQL executor utility
            from core.utils import supabase_sql_executor
            
            async with self.db.get_async_session() as session:
                result = await supabase_sql_executor.execute_with_retry(
                    session=session,
                    project_id=self.project_id,
                    sql_query=sql_query,
                    supabase_service=None  # Will be created if needed
                )
                
                if result.get("success"):
                    # Format the result data for better readability
                    result_data = result.get("data", {})
                    return ToolResult(
                        success=True, 
                        output=f"SQL query executed successfully. Result: {result_data}"
                    )
                else:
                    error_msg = result.get("message", "Unknown error")
                    return ToolResult(
                        success=False,
                        output=error_msg
                    )
                
        except Exception as e:
            logging.error(f"Error executing SQL query: {str(e)}")
            logging.exception("Full exception details:")
            return ToolResult(success=False, output=f"Error executing SQL query: {str(e)}")

    async def get_database_schema(self) -> ToolResult:
        """
        Get the database schema from the connected Supabase project.
        
        Returns:
            ToolResult: Database schema information
        """
        try:
            # SQL query to get schema information
            schema_query = """
            SELECT 
                table_schema,
                table_name,
                column_name,
                data_type,
                column_default,
                is_nullable
            FROM 
                information_schema.columns
            WHERE 
                table_schema = 'public'
            ORDER BY 
                table_name, ordinal_position;
            """
            
            return await self.execute_sql_query(schema_query)
            
        except Exception as e:
            logging.error(f"Error getting database schema: {str(e)}")
            logging.exception("Full exception details:")
            return ToolResult(success=False, output=f"Error getting database schema: {str(e)}")

    @staticmethod
    def schema() -> List[Dict[str, Any]]:
        """
        Returns the OpenAPI JSON schema for function calls in the Supabase Tool.
        """
        return [
            {
                "type": "function",
                "function": {
                    "name": SupabaseTool.execute_sql_query.__name__,
                    "description": "Execute a SQL query on the connected Supabase database",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "sql_query": {
                                "type": "string",
                                "description": "The SQL query to execute on the Supabase database",
                            }
                        },
                        "required": ["sql_query"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": SupabaseTool.get_database_schema.__name__,
                    "description": "Get the database schema from the connected Supabase project",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": [],
                    },
                },
            },
        ] 