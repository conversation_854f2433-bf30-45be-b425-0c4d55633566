import asyncio
import sqlite3
import ssl
import json
import os
import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Configure logging
log_file = "./db_migration.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),  # Save to file
        logging.StreamHandler()  # Output to console for real-time viewing
    ]
)
logger = logging.getLogger("db-migration")
logger.info(f"Logging to file: {os.path.abspath(log_file)}")

async def migrate_data():
    logger.info("Starting database migration from SQLite to MySQL...")
    
    # Create a directory for JSON backups if it doesn't exist
    backup_dir = "./db_migration_backups"
    os.makedirs(backup_dir, exist_ok=True)
    
    # Initialize migration status tracking
    migration_status = {
        "started_at": datetime.now().isoformat(),
        "completed_at": None,
        "tables": {},
        "summary": {
            "total_tables": 0,
            "migrated_tables": 0,
            "skipped_tables": 0,
            "failed_tables": 0,
            "total_rows": 0,
            "migrated_rows": 0,
            "failed_rows": 0
        }
    }
    
    # Connect to SQLite database
    sqlite_db_path = "/mnt/HC_Volume_102202153/softgen/main.db"
    sqlite_conn = sqlite3.connect(sqlite_db_path)
    sqlite_cursor = sqlite_conn.cursor()
    
    # Get MySQL connection info from settings
    mysql_url = "mysql+aiomysql://w2u6np05gg58j5isrudt:<EMAIL>/softgenai?ssl=true"
    
    # Only add SSL context for MySQL connection
    connect_args = {}
    if 'mysql' in mysql_url:
        ssl_context = ssl.create_default_context(cafile="/etc/ssl/certs/ca-certificates.crt")
        ssl_context.verify_mode = ssl.CERT_REQUIRED
        connect_args = {"ssl": ssl_context}

    mysql_engine = create_async_engine(
        mysql_url,
        connect_args=connect_args
    )
    
    # Create session factories
    mysql_session = sessionmaker(
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
        bind=mysql_engine
    )
    
    # Get all tables from SQLite
    sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
    all_tables = [table[0] for table in sqlite_cursor.fetchall()]
    
    logger.info(f"Found {len(all_tables)} tables in database: {', '.join(all_tables)}")
    migration_status["summary"]["total_tables"] = len(all_tables)
    
    # Define the correct order for migration based on dependencies
    ordered_tables = [
        'users',
        'transactions',
        'projects',
        'deployments',
        'project_threads',
        'memory_modules',
        'project_agent_run',
        'blogs',
        'prize_submissions',
        'environments'
    ]
    
    # Add any tables that might be in the database but not in our ordered list
    for table in all_tables:
        if table not in ordered_tables and table != 'project_thread_runs':
            ordered_tables.append(table)
    
    logger.info(f"Will migrate tables in this order: {', '.join(ordered_tables)}")
    
    # For each table, transfer data in the correct order
    for table_name in ordered_tables:
        # Initialize table status
        migration_status["tables"][table_name] = {
            "status": "pending",
            "started_at": datetime.now().isoformat(),
            "completed_at": None,
            "total_rows": 0,
            "migrated_rows": 0,
            "failed_rows": 0,
            "skipped": False,
            "error": None,
            "failed_row_ids": []
        }
        
        # Skip tables that don't exist in the source database
        if table_name not in all_tables:
            logger.warning(f"Table {table_name} not found in source database, skipping")
            migration_status["tables"][table_name]["status"] = "skipped"
            migration_status["tables"][table_name]["skipped"] = True
            migration_status["tables"][table_name]["error"] = "Table not found in source database"
            migration_status["tables"][table_name]["completed_at"] = datetime.now().isoformat()
            migration_status["summary"]["skipped_tables"] += 1
            continue
            
        # Skip project_thread_runs table as requested
        if table_name == 'project_thread_runs':
            logger.info(f"Skipping table: {table_name} as requested")
            migration_status["tables"][table_name]["status"] = "skipped"
            migration_status["tables"][table_name]["skipped"] = True
            migration_status["tables"][table_name]["error"] = "Table skipped as requested"
            migration_status["tables"][table_name]["completed_at"] = datetime.now().isoformat()
            migration_status["summary"]["skipped_tables"] += 1
            continue
            
        # Ask for confirmation before migrating each table
        confirmation = input(f"Migrate table '{table_name}'? (yes/no): ").lower().strip()
        if confirmation != 'yes':
            logger.info(f"Skipping table: {table_name}")
            migration_status["tables"][table_name]["status"] = "skipped"
            migration_status["tables"][table_name]["skipped"] = True
            migration_status["tables"][table_name]["error"] = "User chose to skip"
            migration_status["tables"][table_name]["completed_at"] = datetime.now().isoformat()
            migration_status["summary"]["skipped_tables"] += 1
            continue
            
        logger.info(f"Migrating table: {table_name}")
        
        # Get column info from SQLite
        sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
        columns_info = sqlite_cursor.fetchall()
        column_names = [col[1] for col in columns_info]
        
        # Get data from SQLite
        sqlite_cursor.execute(f"SELECT * FROM {table_name}")
        rows = sqlite_cursor.fetchall()
        
        if not rows:
            logger.info(f"  Table {table_name} is empty, skipping")
            migration_status["tables"][table_name]["status"] = "skipped"
            migration_status["tables"][table_name]["skipped"] = True
            migration_status["tables"][table_name]["error"] = "Table is empty"
            migration_status["tables"][table_name]["completed_at"] = datetime.now().isoformat()
            migration_status["summary"]["skipped_tables"] += 1
            continue
            
        logger.info(f"  Found {len(rows)} rows to migrate")
        migration_status["tables"][table_name]["total_rows"] = len(rows)
        migration_status["summary"]["total_rows"] += len(rows)
        
        # Prepare data for MySQL
        data_to_insert = []
        for row in rows:
            row_dict = {column_names[i]: row[i] for i in range(len(column_names))}
            
            # Handle specific tables with data size issues
            if table_name == 'projects' and 'project_id' in row_dict:
                # Ensure project_id is exactly 36 characters
                if row_dict['project_id'] and len(row_dict['project_id']) > 36:
                    logger.warning(f"  Truncating project_id: {row_dict['project_id']} to 36 chars")
                    row_dict['project_id'] = row_dict['project_id'][:36]
            
            # No need to truncate messages or data fields anymore since we're using LONGTEXT
            
            data_to_insert.append(row_dict)
        
        # Save table data to JSON file
        json_file_path = os.path.join(backup_dir, f"{table_name}.json")
        with open(json_file_path, 'w') as f:
            json.dump(data_to_insert, f, indent=2, default=str)
        logger.info(f"  Saved table data to {json_file_path}")
        
        # Check if table exists in MySQL before attempting to insert
        async with mysql_session() as session:
            try:
                # Check if table exists
                check_table = await session.execute(
                    text("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = :table_name"),
                    {"table_name": table_name}
                )
                table_exists = check_table.scalar() > 0
                
                if not table_exists:
                    logger.warning(f"  Table {table_name} does not exist in MySQL, skipping")
                    migration_status["tables"][table_name]["status"] = "failed"
                    migration_status["tables"][table_name]["error"] = "Table does not exist in MySQL"
                    migration_status["tables"][table_name]["completed_at"] = datetime.now().isoformat()
                    migration_status["summary"]["failed_tables"] += 1
                    continue
                
                # Get MySQL column names to ensure we only insert valid columns
                columns_result = await session.execute(
                    text("SHOW COLUMNS FROM `{}`".format(table_name))
                )
                mysql_columns = [row[0] for row in columns_result.fetchall()]
                
                # Insert data in batches to avoid memory issues
                batch_size = 10  # Smaller batch size for large data
                for i in range(0, len(data_to_insert), batch_size):
                    batch = data_to_insert[i:i+batch_size]
                    
                    for row_dict in batch:
                        # Filter out columns that don't exist in MySQL
                        filtered_row = {k: v for k, v in row_dict.items() if k in mysql_columns}
                        
                        if not filtered_row:
                            logger.warning(f"  Warning: No matching columns for a row in {table_name}")
                            migration_status["tables"][table_name]["failed_rows"] += 1
                            migration_status["summary"]["failed_rows"] += 1
                            
                            # Add row ID to failed rows list if available
                            primary_key = None
                            if 'id' in row_dict:
                                primary_key = row_dict['id']
                            elif f'{table_name[:-1]}_id' in row_dict:
                                primary_key = row_dict[f'{table_name[:-1]}_id']
                            elif 'project_id' in row_dict:
                                primary_key = row_dict['project_id']
                                
                            if primary_key:
                                migration_status["tables"][table_name]["failed_row_ids"].append(str(primary_key))
                            continue
                            
                        # Build REPLACE INTO statement manually to handle duplicates
                        columns = ", ".join([f"`{k}`" for k in filtered_row.keys()])
                        placeholders = ", ".join([":"+k for k in filtered_row.keys()])
                        
                        sql = f"REPLACE INTO `{table_name}` ({columns}) VALUES ({placeholders})"
                        try:
                            await session.execute(text(sql), filtered_row)
                            await session.commit()
                            migration_status["tables"][table_name]["migrated_rows"] += 1
                            migration_status["summary"]["migrated_rows"] += 1
                        except Exception as e:
                            await session.rollback()
                            error_msg = str(e)[:200]
                            logger.error(f"  Error inserting row in {table_name}: {error_msg}...")
                            migration_status["tables"][table_name]["failed_rows"] += 1
                            migration_status["summary"]["failed_rows"] += 1
                            
                            # Add row ID to failed rows list if available
                            primary_key = None
                            if 'id' in row_dict:
                                primary_key = row_dict['id']
                            elif f'{table_name[:-1]}_id' in row_dict:
                                primary_key = row_dict[f'{table_name[:-1]}_id']
                            elif 'project_id' in row_dict:
                                primary_key = row_dict['project_id']
                                
                            if primary_key:
                                migration_status["tables"][table_name]["failed_row_ids"].append(str(primary_key))
                            
                            # Skip this row and continue with others
                            continue
                    
                    logger.info(f"  Inserted batch {i//batch_size + 1}/{(len(data_to_insert)-1)//batch_size + 1}")
                
                # Update table status based on results
                if migration_status["tables"][table_name]["failed_rows"] == migration_status["tables"][table_name]["total_rows"]:
                    migration_status["tables"][table_name]["status"] = "failed"
                    migration_status["summary"]["failed_tables"] += 1
                elif migration_status["tables"][table_name]["failed_rows"] > 0:
                    migration_status["tables"][table_name]["status"] = "partial"
                    migration_status["summary"]["migrated_tables"] += 1
                else:
                    migration_status["tables"][table_name]["status"] = "success"
                    migration_status["summary"]["migrated_tables"] += 1
                
                migration_status["tables"][table_name]["completed_at"] = datetime.now().isoformat()
                logger.info(f"  Successfully migrated table {table_name}: {migration_status['tables'][table_name]['migrated_rows']} of {migration_status['tables'][table_name]['total_rows']} rows")
                
            except Exception as e:
                await session.rollback()
                error_msg = str(e)[:200]
                logger.error(f"  Error migrating table {table_name}: {error_msg}...")
                migration_status["tables"][table_name]["status"] = "failed"
                migration_status["tables"][table_name]["error"] = error_msg
                migration_status["tables"][table_name]["completed_at"] = datetime.now().isoformat()
                migration_status["summary"]["failed_tables"] += 1
    
    # Close connections
    sqlite_conn.close()
    await mysql_engine.dispose()
    
    # Update final migration status
    migration_status["completed_at"] = datetime.now().isoformat()
    
    # Save migration status to JSON file
    status_file_path = os.path.join(backup_dir, "migration_status.json")
    with open(status_file_path, 'w') as f:
        json.dump(migration_status, f, indent=2, default=str)
    
    # Print summary
    logger.info("Migration completed!")
    logger.info(f"Summary: {migration_status['summary']['migrated_tables']} tables migrated successfully, {migration_status['summary']['failed_tables']} failed, {migration_status['summary']['skipped_tables']} skipped")
    logger.info(f"Rows: {migration_status['summary']['migrated_rows']} migrated successfully, {migration_status['summary']['failed_rows']} failed")
    logger.info(f"Detailed status saved to {status_file_path}")
    
    # Print tables with issues
    tables_with_issues = [name for name, info in migration_status["tables"].items() 
                         if info["status"] in ["failed", "partial"]]
    if tables_with_issues:
        logger.info("Tables with issues:")
        for table_name in tables_with_issues:
            table_info = migration_status["tables"][table_name]
            logger.info(f"  - {table_name}: {table_info['status']}, {table_info['migrated_rows']}/{table_info['total_rows']} rows migrated")
            if table_info["error"]:
                logger.info(f"    Error: {table_info['error']}")

if __name__ == "__main__":
    asyncio.run(migrate_data())