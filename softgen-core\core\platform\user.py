from fastapi import Depends, HTTPException, status
from fastapi.security import O<PERSON><PERSON>2<PERSON><PERSON><PERSON>Bearer
from jose import JW<PERSON>rror, jwt
from datetime import datetime, timedelta, timezone
from typing import Optional
from pydantic import BaseModel
from core.db import Database
from core.models import User
from core.config import settings
from kinde_sdk import Configuration
from kinde_sdk.kinde_api_client import KindeApiClient, GrantType
import logging
import aiohttp
import asyncio
import functools
from sqlalchemy.future import select
from sqlalchemy.exc import OperationalError, DisconnectionError
from sqlalchemy import or_, update

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

def db_retry(max_retries=3, delay=1.0, backoff=2.0):
    """
    Decorator for database operations with exponential backoff retry logic.
    Handles connection resets, disconnections, and operational errors.
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except (OperationalError, DisconnectionError, ConnectionResetError) as e:
                    last_exception = e
                    error_msg = str(e).lower()

                    # Log the retry attempt
                    logging.warning(f"🔄 DB RETRY [{attempt + 1}/{max_retries}] for {func.__name__}: {str(e)[:100]}...")

                    # Check if it's a retryable error
                    retryable_errors = [
                        'connection reset by peer',
                        'lost connection to mysql server',
                        'mysql server has gone away',
                        'connection was killed',
                        'connection timed out'
                    ]

                    if not any(err in error_msg for err in retryable_errors):
                        logging.error(f"❌ Non-retryable DB error in {func.__name__}: {str(e)}")
                        raise e

                    if attempt < max_retries - 1:
                        wait_time = delay * (backoff ** attempt)
                        logging.info(f"⏳ Waiting {wait_time:.1f}s before retry...")
                        await asyncio.sleep(wait_time)
                    else:
                        logging.error(f"🚨 DB RETRY EXHAUSTED for {func.__name__} after {max_retries} attempts")

            # If all retries failed, raise a user-friendly error
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "error": "Database temporarily unavailable",
                    "message": "Our database is experiencing connectivity issues. Please try again in a few moments.",
                    "action": "Refresh the page or try again in 30 seconds",
                    "technical_details": str(last_exception) if last_exception else "Connection failed"
                }
            )
        return wrapper
    return decorator

configuration = Configuration(host=settings.kinde_host)
kinde_client = KindeApiClient(
    configuration=configuration,
    domain=settings.kinde_host,
    client_id=settings.kinde_client_id,
    client_secret=settings.kinde_client_secret,
    grant_type=GrantType.AUTHORIZATION_CODE,
    callback_url=settings.kinde_redirect_url
)

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme), db: Database = Depends()):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        token_data = TokenData(email=email)
    except JWTError:
        raise credentials_exception
    user = await db.user_service.get_user_by_email(email=token_data.email)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    # if not current_user.is_active:
    #     raise HTTPException(status_code=400, detail="Inactive user")
    if current_user.plan == "banned":
        raise HTTPException(status_code=403, detail="Banned user")
    return current_user




class UserService:
    def __init__(self, db: Database):
        self.db = db

    @db_retry(max_retries=3, delay=0.5, backoff=2.0)
    async def get_user_by_email(self, email: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.email == email))
            return result.scalar_one_or_none()

    @db_retry(max_retries=3, delay=0.5, backoff=2.0)
    async def get_user_by_kinde_id(self, kinde_id: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
            return result.scalar_one_or_none()

    async def create_user(self, email: str, kinde_id: str, is_superuser: bool, stripe_customer_id: str):
        async with self.db.get_async_session() as session:
            new_user = User(
                email=email,
                kinde_id=kinde_id,
                is_active=True,
                is_superuser=is_superuser,
                stripe_customer_id=stripe_customer_id,
                isSubscribed=False,
                project_limit=1,
                isRequestBased=False,
                total_free_request=0,
                free_total_token=50000,  # Added: 50k free tokens
                plan="free-tier"  # Added: free-tier plan
            )
            session.add(new_user)
            await session.commit()

            # Send Slack notification
            webhook_url = settings.slack_webhook_url
            async with aiohttp.ClientSession() as slack_session:
                payload = {
                    "text": f"🎉 New user signed up!\nEmail: {email}\nPlan: free-tier\nFree Tokens: 100,000"
                }
                try:
                    async with slack_session.post(webhook_url, json=payload) as response:
                        if response.status != 200:
                            logging.error(f"Failed to send Slack notification: {response.status}")
                except Exception as e:
                    logging.error(f"Error sending Slack notification: {str(e)}")

            return new_user

    async def updateUserBySubscriptionId(self, subscription_id: str, isSubscribed: bool, plan: str, requests_to_add: int, subscription_end_date: Optional[int]):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.subscription_id == subscription_id))
            user = result.scalar_one_or_none()
            if user:
                user.isSubscribed = isSubscribed
                user.total_free_request += requests_to_add
                # user.plan = plan
                if subscription_end_date is not None:
                    user.subscription_end_date = datetime.datetime.fromtimestamp(subscription_end_date).isoformat()
                else:
                    user.subscription_end_date = None
                await session.commit()

    async def updateUserTokenBySubscriptionId(self, subscription_id: str, isSubscribed: bool, plan: str, subscription_end_date: Optional[int]):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.subscription_id == subscription_id))
            user = result.scalar_one_or_none()
            free_total_token = 3000000
            if plan == "launch":
                free_total_token = 3000000
            elif plan == "elite":
                free_total_token = 7000000
            elif plan == "business":
                free_total_token = 16000000
            elif plan == "entry":
                free_total_token = 2500000
            elif plan == "boost":
                free_total_token = 6000000  # 6M tokens
            elif plan == "fly":
                free_total_token = 14000000  # 14M tokens
            elif plan == "pro_enterprise":
                free_total_token = 30000000  # 30M tokens
            elif plan == "elite_enterprise":
                free_total_token = 85000000  # 85M tokens
            else:
                free_total_token = 3000000
            if user:
                user.isSubscribed = isSubscribed
                user.free_total_token += free_total_token
                if subscription_end_date is not None:
                    user.subscription_end_date = datetime.datetime.fromtimestamp(subscription_end_date).isoformat()
                else:
                    user.subscription_end_date = None
                await session.commit()

    async def update_user_total_free_request(self, kinde_id: str, total_free_request: int):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
            user = result.scalar_one_or_none()
            if user:
                user.total_free_request = total_free_request
                session.add(user)
                await session.commit()

    async def update_user_isFreeTrial(self, customer_id: str, subscription_id: str, isFreeTrial: bool):
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(User).where(
                    or_(
                        User.stripe_customer_id == customer_id,
                        User.subscription_id == subscription_id
                    )
                )
            )
            user = result.scalar_one_or_none()
            if user:
                user.isFreeTrial = isFreeTrial
                session.add(user)
                await session.commit()

    async def update_user_kinde_id(self, user_id: int, new_kinde_id: str, customer_id: str = None):
        print(user_id, new_kinde_id, customer_id)
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.id == user_id))
            user = result.scalar_one_or_none()
            if user:
                user.kinde_id = new_kinde_id
                if customer_id:
                    user.stripe_customer_id = customer_id
                await session.commit()

    async def update_user_after_payment(self, email: str, kinde_id: str, plan: str, stripe_customer_id: str, subscription_id: str, token_event_name: str | None, included_tokens: int):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
            user = result.scalar_one_or_none()
            if user:
                # if plan == "standard":
                #     project_limit = 4
                #     total_free_request = 200

                # elif plan == "basic":
                #     project_limit = 3
                #     total_free_request = 50
                # elif plan == "plus":
                #     project_limit = 5
                #     total_free_request = 200
                # elif plan == "ultimate":
                #     project_limit = 5
                #     total_free_request = 450

                project_limit = 25
                free_total_token = int(included_tokens) if isinstance(included_tokens, str) else included_tokens

                user.stripe_customer_id = stripe_customer_id
                user.plan = plan
                user.subscription_id = subscription_id
                user.isSubscribed = True
                user.project_limit = project_limit
                user.free_total_token += free_total_token
                user.token_event_name = token_event_name
                user.isRequestBased = False
                await session.commit()

    async def create_user_after_payment(self, email: str, kinde_id: str, plan: str, stripe_customer_id: str, subscription_id: str):
        async with self.db.get_async_session() as session:
            if plan == "standard":
                project_limit = 5
                total_free_request = 200

            elif plan == "basic":
                project_limit = 3
                total_free_request = 50
            elif plan == "plus":
                project_limit = 5
                total_free_request = 200
            elif plan == "ultimate":
                project_limit = 5
                total_free_request = 450

            new_user = User(
                email=email,
                kinde_id=kinde_id,
                is_active=False,
                is_superuser=False,
                stripe_customer_id=stripe_customer_id,
                plan=plan,
                project_limit=project_limit,
                total_free_request=total_free_request,
                isRequestBased=False,
                subscription_id=subscription_id,
                isSubscribed=True
            )
            session.add(new_user)
            await session.commit()
            return new_user

    async def update_user_free_requests(self, email: str, total_free_request: int):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.email == email))
            user = result.scalar_one_or_none()
            if user:
                user.total_free_request += total_free_request
                user.isRequestBased = True
                await session.commit()

    async def update_user_free_tokens(self, kinde_id: str, free_total_token: int):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
            user = result.scalar_one_or_none()
            if user:
                user.free_total_token = free_total_token
                session.add(user)
                await session.commit()

    async def activate_user(self, kinde_id: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
            user = result.scalar_one_or_none()
            if user:
                user.is_active = True
                await session.commit()
                return user
            return None

    async def get_user_by_subscription_id(self, subscription_id: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.subscription_id == subscription_id))
            return result.scalar_one_or_none()

    async def get_by_customer_id(self, customer_id: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.stripe_customer_id == customer_id))
            return result.scalar_one_or_none()

    async def update_user_token_balance(self, kinde_id: str, tokens_to_add: int):
        """Update user's token balance with atomic addition."""
        async with self.db.get_async_session() as session:
            try:
                # First update the user
                stmt = (
                    update(User)
                    .where(User.kinde_id == kinde_id)
                    .values(
                        free_total_token=User.free_total_token + tokens_to_add,
                        isRequestBased=False
                    )
                )
                await session.execute(stmt)

                # Then fetch the updated user to return
                result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
                user = result.scalar_one_or_none()

                if user:
                    await session.commit()
                    logging.info(f"Added {tokens_to_add:,} tokens to user {kinde_id}. New balance: {user.free_total_token:,}")
                    return user
                else:
                    logging.error(f"No user found with kinde_id {kinde_id}")
                    return None
            except Exception as e:
                await session.rollback()
                logging.error(f"Error updating token balance: {str(e)}")
                raise

    async def update_user_free_tier(self, user_id: int):
        """Update user's plan to free tier and set initial token balance if not set."""
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.id == user_id))
            user = result.scalar_one_or_none()
            if user and user.plan is None and user.free_total_token == 0:
                user.plan = "free-tier"
                user.free_total_token = 100000
                user.is_active = True
                user.project_limit = 1
                await session.commit()
                logging.info(f"Updated user {user.email} to free tier with initial tokens")
                return user
            return None
