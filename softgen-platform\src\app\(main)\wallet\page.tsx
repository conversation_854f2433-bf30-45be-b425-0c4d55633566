"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Layout } from "@/features/global/layout";
import { getWallet, getWalletTransactions, topUpWallet } from "@/lib/api";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { formatCurrency } from "@/utils/currency";
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  AlertCircle,
  Clock,
  CreditCard,
  Loader2,
  Plus,
  TrendingUp,
  Wallet,
  XCircle,
  Zap,
} from "lucide-react";
import { useRout<PERSON>, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Wallet {
  id: number;
  user_id: number;
  balance: number;
  created_at: string;
  updated_at: string;
}

const getTransactionIcon = (type: string, status: string) => {
  if (status === "failed") return <XCircle className="h-5 w-5 text-red-500" />;
  if (status === "pending") return <Clock className="h-5 w-5 text-yellow-500" />;

  switch (type.toLowerCase()) {
    case "deposit":
      return <TrendingUp className="h-5 w-5 text-green-500" />;
    case "withdrawal":
      return <TrendingUp className="h-5 w-5 rotate-180 text-red-500" />;
    default:
      return <Wallet className="h-5 w-5 text-gray-500" />;
  }
};

const getStatusBadge = (status: string) => {
  const variants = {
    completed: "default",
    pending: "secondary",
    failed: "destructive",
  } as const;

  return (
    <Badge variant={variants[status as keyof typeof variants] || "outline"} className="text-xs">
      {status}
    </Badge>
  );
};

const quickAmounts = [5, 10, 25, 50, 100];

const formatRelativeTime = (dateString: string) => {
  const utcDateString = dateString.endsWith("Z") ? dateString : `${dateString}Z`;
  const date = new Date(utcDateString);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) return "Just now";
  if (diffInHours < 24) return `${diffInHours}h ago`;
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;

  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: date.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
  });
};

const LoadingSkeleton = () => (
  <div className="space-y-3">
    {[...Array(3)].map((_, i) => (
      <div
        key={i}
        className="flex animate-pulse items-center justify-between rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50"
      >
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700"></div>
          <div className="space-y-2">
            <div className="h-4 w-20 rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-3 w-16 rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
        </div>
        <div className="h-4 w-16 rounded bg-gray-200 dark:bg-gray-700"></div>
      </div>
    ))}
  </div>
);

const EmptyState = () => (
  <div className="py-12 text-center">
    <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20">
      <Wallet className="h-12 w-12 text-blue-500" />
    </div>
    <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
      No transactions yet
    </h3>
    <p className="mb-6 text-gray-500 dark:text-gray-400">
      Your transaction history will appear here once you make your first top-up.
    </p>
    <Button
      onClick={() => document.getElementById("amount-input")?.focus()}
      variant="outline"
      className="gap-2"
    >
      <Plus className="h-4 w-4" />
      Add funds to get started
    </Button>
  </div>
);

const WalletPage = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [amount, setAmount] = useState("");

  useEffect(() => {
    if (!user.isLoading && !user.access_token) {
      router.replace("/");
    }
  }, [user.isLoading, user.access_token, router]);

  const queryClient = useQueryClient();

  const { data: wallet, isLoading: isLoadingWallet } = useQuery<Wallet>({
    queryKey: ["wallet"],
    queryFn: getWallet,
    enabled: !!user.access_token, // Only fetch if authenticated
  });

  const {
    data: transactionsData,
    isLoading: isLoadingTransactions,
    error: transactionsError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ["walletTransactions"],
    queryFn: getWalletTransactions,
    getNextPageParam: (lastPage) => lastPage.next_cursor,
    initialPageParam: undefined,
    enabled: !!user.access_token, // Only fetch if authenticated
  });

  const transactions = transactionsData?.pages.flatMap((page) => page.transactions) ?? [];

  const topUpMutation = useMutation({
    mutationFn: topUpWallet,
    onSuccess: (data) => {
      console.log("data", data);
      if (data.checkout_url) {
        window.location.href = data.checkout_url;
      } else {
        toast.error("Could not retrieve checkout URL.");
      }
    },
    onError: (error: unknown) => {
      const detail = (error as { detail?: string })?.detail || "An error occurred during top-up.";
      toast.error(detail);
    },
  });

  const searchParams = useSearchParams();
  useEffect(() => {
    if (searchParams.get("success")) {
      toast.success("Top-up successful!");
      queryClient.invalidateQueries({ queryKey: ["wallet"] });
      queryClient.invalidateQueries({ queryKey: ["walletTransactions"] });
    }

    if (searchParams.get("canceled")) {
      toast.info("Your top-up was canceled.");
    }
  }, [searchParams, queryClient]);

  const handleTopUp = async () => {
    const amountValue = Number(amount);

    if (!amountValue || amountValue <= 0) {
      toast.error("Please enter a valid amount.");
      return;
    }

    if (amountValue < 1) {
      // $1 minimum
      toast.error("Minimum top-up amount is $1.00");
      return;
    }

    topUpMutation.mutate(amountValue);
  };

  if (user.isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  if (!user.access_token) {
    return null;
  }

  return (
    <Layout>
      <div className="container mx-auto max-w-4xl px-6 pt-24">
        <div className="mb-8">
          <h1 className="mb-2 text-3xl font-bold text-gray-900 dark:text-gray-100">Wallet</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your account balance and view transaction history
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Balance Card */}
          <Card className="relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 dark:from-blue-500/5 dark:to-purple-500/5"></div>
            <CardHeader className="relative">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5" />
                  Current Balance
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="relative">
              {isLoadingWallet ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="text-2xl font-bold">Loading...</span>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="py-4 text-4xl font-bold text-gray-900 dark:text-gray-100">
                    {formatCurrency({ amount: wallet?.balance || 0 })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Top-up Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Add Funds
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="amount-input" className="text-sm font-medium">
                  Amount (USD)
                </label>
                <Input
                  id="amount-input"
                  type="number"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  min="1"
                  step="0.01"
                  className="text-lg"
                />
              </div>

              {/* Quick Amount Buttons */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Quick amounts</label>
                <div className="flex flex-wrap gap-2">
                  {quickAmounts.map((quickAmount) => (
                    <Button
                      key={quickAmount}
                      variant="outline"
                      size="sm"
                      onClick={() => setAmount(quickAmount.toString())}
                      className="text-xs"
                    >
                      ${quickAmount}
                    </Button>
                  ))}
                </div>
              </div>

              <Button
                onClick={handleTopUp}
                disabled={topUpMutation.isPending || !amount}
                className="w-full gap-2"
              >
                {topUpMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4" />
                    Add Funds
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Transactions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Transaction History
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            {isLoadingTransactions ? (
              <LoadingSkeleton />
            ) : transactionsError ? (
              <div className="py-8 text-center">
                <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
                <p className="font-medium text-red-500">Error loading transactions</p>
                <p className="mt-1 text-sm text-gray-500">Please try refreshing the page</p>
              </div>
            ) : transactions.length > 0 ? (
              <div className="space-y-2">
                {transactions.map((transaction) => {
                  return (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between rounded-lg border border-gray-100 p-4 transition-colors hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800/50"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          {getTransactionIcon(transaction.transaction_type, transaction.status)}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="mb-1 flex items-center gap-2">
                            <p className="font-medium capitalize text-gray-900 dark:text-gray-100">
                              {transaction.transaction_type}
                            </p>
                            {getStatusBadge(transaction.status)}
                          </div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {formatRelativeTime(transaction.created_at)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div
                          className={cn(
                            "text-lg font-semibold",
                            transaction.status === "completed"
                              ? "text-gray-900 dark:text-gray-100"
                              : transaction.status === "failed"
                                ? "text-red-500"
                                : "text-gray-500",
                          )}
                        >
                          {formatCurrency({ amount: transaction.amount })}
                        </div>

                        {transaction.transaction_type === "deposit" && transaction.fee_amount && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-help text-xs text-gray-500 dark:text-gray-400">
                                  +{formatCurrency({ amount: transaction.fee_amount })} fee
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  Softgen fee: {formatCurrency({ amount: transaction.fee_amount })}
                                </p>
                                <p>
                                  Total charged:{" "}
                                  {formatCurrency({
                                    amount: transaction.amount + transaction.fee_amount,
                                  })}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </div>
                  );
                })}

                {hasNextPage && (
                  <div className="pt-4">
                    <Button
                      onClick={() => fetchNextPage()}
                      disabled={isFetchingNextPage}
                      variant="outline"
                      className="w-full gap-2"
                    >
                      {isFetchingNextPage ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading more...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4" />
                          Load More
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <EmptyState />
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default WalletPage;
