module.exports = {

"[project]/src/hooks/use-mobile.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useIsMobile": (()=>useIsMobile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
const MOBILE_BREAKPOINT = 768;
function useIsMobile() {
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(undefined);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
        const onChange = ()=>{
            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
        };
        mql.addEventListener("change", onChange);
        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
        return ()=>mql.removeEventListener("change", onChange);
    }, []);
    return !!isMobile;
}
}}),
"[project]/src/hooks/use-threads.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useThread": (()=>useThread)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$thread$2d$provider$2f$index$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/thread-provider/index.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
const useThread = ()=>{
    const { currentThread, isLoading, createThreadFn, sendMessageFn: contextSendMessage, selectThreadId, stopSession, selectFirstThread, contextLimitReached, setContextLimitReached } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$thread$2d$provider$2f$index$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useThreadContext"])();
    const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        try {
            let attempts = 0;
            const maxAttempts = 3;
            const backoffMs = 500;
            const attemptSend = async ()=>{
                try {
                    attempts++;
                    await contextSendMessage.mutateAsync({
                        content: data.content,
                        images: data.options?.images,
                        selectedPaths: data.options?.selectedPaths,
                        model: data.options?.model || "creative"
                    });
                } catch (error) {
                    console.error(`Attempt ${attempts} failed:`, error);
                    if (attempts < maxAttempts) {
                        const delay = backoffMs * Math.pow(2, attempts - 1);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])(`Retrying in ${delay}ms...`);
                        await new Promise((resolve)=>setTimeout(resolve, delay));
                        return attemptSend();
                    }
                    throw error;
                }
            };
            await attemptSend();
        } catch (error) {
            console.warn("error found", error);
            console.error("Failed to send message after multiple attempts:", error);
            throw error;
        }
    }, [
        contextSendMessage
    ]);
    return {
        currentThread,
        isLoading,
        createThreadFn,
        sendMessage,
        selectThreadId,
        stopSession,
        selectFirstThread,
        contextLimitReached,
        setContextLimitReached
    };
};
}}),
"[project]/src/hooks/use-copy.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCopy": (()=>useCopy)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function useCopy(duration = 1500) {
    const [copied, setCopied] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const copy = async (text)=>{
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(()=>setCopied(false), duration);
            return true;
        } catch (err) {
            console.error("Failed to copy text: ", err);
            return false;
        }
    };
    return {
        copied,
        copy
    };
}
}}),
"[project]/src/hooks/use-token-info.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTokenInfo": (()=>useTokenInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/auth-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
;
const useTokenInfo = ()=>{
    const [tokenUsage, setTokenUsage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isExcessUsageModalOpen, setIsExcessUsageModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [excessUsageMessage, setExcessUsageMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const fetchTokenUsage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!user?.userFromDb?.stripe_customer_id || !user?.userFromDb?.token_event_name) {
            setIsLoading(false);
            return;
        }
        try {
            setIsLoading(true);
            const usage = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenUsage"])(user.userFromDb.stripe_customer_id, user.userFromDb.token_event_name);
            setTokenUsage(usage);
        } catch (error) {
            console.error("Error fetching token usage:", error);
        } finally{
            setIsLoading(false);
        }
    }, [
        user?.userFromDb?.stripe_customer_id,
        user?.userFromDb?.token_event_name
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        fetchTokenUsage();
        const intervalId = setInterval(fetchTokenUsage, 30000);
        return ()=>clearInterval(intervalId);
    }, [
        fetchTokenUsage
    ]);
    const formatTokens = (tokens)=>{
        if (tokens >= 1000000) {
            return `${(tokens / 1000000).toFixed(2)}M`;
        } else {
            return `${Math.round(tokens / 1000)}k`;
        }
    };
    const getPlanTokens = ()=>{
        const plan = user?.userFromDb?.plan?.toLowerCase() || "launch";
        switch(plan){
            case "launch":
                return 3;
            case "elite":
                return 7;
            case "business":
                return 16;
            case "entry":
                return 2.5;
            case "boost":
                return 6;
            case "fly":
                return 14;
            case "pro_enterprise":
                return 30;
            case "elite_enterprise":
                return 85;
            case "free-tier":
                return 0.25;
            default:
                return 3;
        }
    };
    const getRemainingTokens = ()=>{
        return user?.userFromDb?.free_total_token || 0;
    };
    const getTokenDisplayInfo = ()=>{
        const remaining = getRemainingTokens();
        const total = getPlanTokens() * 1000000; // Convert to tokens
        const isExcess = tokenUsage?.total_usage && tokenUsage.total_usage > 0;
        const usedTokens = tokenUsage?.total_usage || 0;
        const effectiveTotal = Math.max(remaining, total);
        return {
            usedText: usedTokens > 0 ? formatTokens(usedTokens) : "",
            remainingTokens: formatTokens(remaining),
            effectiveTotalTokens: formatTokens(effectiveTotal),
            remainingText: `${formatTokens(remaining)}/${formatTokens(effectiveTotal)}`,
            isWarning: isExcess,
            tooltipContent: isExcess ? `You've used ${formatTokens(usedTokens)} tokens this billing period. Excess usage charges may apply.` : `You have ${formatTokens(remaining)} tokens remaining out of ${formatTokens(effectiveTotal)} tokens.`,
            totalUsage: usedTokens,
            remaining,
            total: total / 1000000
        };
    };
    const handleExcessUsageClick = ()=>{
        const info = getTokenDisplayInfo();
        const periodStart = tokenUsage?.period_start ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(tokenUsage.period_start * 1000), "MMMM dd, yyyy") : "N/A";
        const periodEnd = tokenUsage?.period_end ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(tokenUsage.period_end * 1000), "MMMM dd, yyyy") : "N/A";
        const userPlan = user?.userFromDb?.plan || "launch";
        const isNewPlan = [
            "entry",
            "boost",
            "fly"
        ].includes(userPlan.toLowerCase());
        if (isNewPlan) {
            const excessTokensIn100k = Math.ceil(info.totalUsage / 100000);
            const message = `
        You've exceeded your monthly token limit by ${formatTokens(info.totalUsage)} tokens.

        Your next billing amount will be $${excessTokensIn100k} for the period ${periodStart} to ${periodEnd}.

        Billing breakdown:
        • Base plan includes ${formatTokens(info.total * 1000000)} tokens
        • Additional usage is charged at $1 per 100k tokens
        • You are using ${excessTokensIn100k} additional block(s) of 100k tokens
      `;
            setExcessUsageMessage(message);
        } else {
            const usageAmount = {
                launch: 25,
                elite: 50,
                business: 100,
                entry: 25,
                boost: 50,
                fly: 100,
                pro_enterprise: 200,
                elite_enterprise: 500,
                free_tier: 0.25
            }[userPlan.toLowerCase()] || 25;
            const additionalBlocksUsed = Math.ceil(info.totalUsage / (info.total * 1000000));
            const nextBillingAmount = usageAmount * additionalBlocksUsed;
            const message = `
        You've exceeded your monthly token limit by ${formatTokens(info.totalUsage)} tokens.

        Your next billing amount will be $${nextBillingAmount} for the period ${periodStart} to ${periodEnd}.

        Billing breakdown:
        • Base plan includes ${formatTokens(info.total * 1000000)} tokens
        • Additional usage is charged at $${usageAmount} per ${formatTokens(info.total * 1000000)} tokens
        • You are using ${additionalBlocksUsed} additional block(s) of ${formatTokens(info.total * 1000000)} tokens
      `;
            setExcessUsageMessage(message);
        }
        setIsExcessUsageModalOpen(true);
    };
    return {
        tokenUsage,
        isLoading,
        getTokenDisplayInfo,
        formatTokens,
        handleExcessUsageClick,
        isExcessUsageModalOpen,
        setIsExcessUsageModalOpen,
        excessUsageMessage,
        refreshTokenUsage: fetchTokenUsage
    };
};
}}),
"[project]/src/stores/settings.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAdvancedMode": (()=>useAdvancedMode),
    "useResizablePanelConfig": (()=>useResizablePanelConfig),
    "useSettings": (()=>useSettings)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react/shallow.mjs [app-ssr] (ecmascript)");
;
;
;
const useSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        resizablePanelConfig: {
            width: 28,
            previewWidth: 72,
            tasks: true
        },
        setResizablePanelConfig: (config)=>set({
                resizablePanelConfig: {
                    ...get().resizablePanelConfig,
                    ...config
                }
            }),
        isAdvancedMode: false,
        toggleAdvancedMode: ()=>set((state)=>({
                    isAdvancedMode: !state.isAdvancedMode
                }))
    }), {
    name: "settings-storage",
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage)
}));
const useResizablePanelConfig = ()=>{
    return useSettings((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            resizablePanelConfig: state.resizablePanelConfig,
            setResizablePanelConfig: state.setResizablePanelConfig
        })));
};
const useAdvancedMode = ()=>{
    return useSettings((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            isAdvancedMode: state.isAdvancedMode,
            toggleAdvancedMode: state.toggleAdvancedMode
        })));
};
}}),
"[project]/src/stores/settings-tab.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useSettingsStore": (()=>useSettingsStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const initialState = {
    settingsTab: null
};
const useSettingsStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subscribeWithSelector"])((set)=>({
        ...initialState,
        setSettingsTab: (tab)=>set({
                settingsTab: tab
            }),
        closeModal: ()=>set({
                settingsTab: null
            }),
        reset: ()=>set(initialState)
    })));
}}),
"[project]/src/stores/agent-input.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAgentInput": (()=>useAgentInput),
    "useSubscribeToAgentInput": (()=>useSubscribeToAgentInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
;
const useAgentInput = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subscribeWithSelector"])((set)=>({
        inputContent: null,
        mode: "creative",
        setInputContent: (content)=>set({
                inputContent: content
            }),
        setMode: (mode)=>set({
                mode
            }),
        reset: ()=>set({
                inputContent: null,
                mode: "creative"
            })
    })));
const useSubscribeToAgentInput = (callback)=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const unsubscribe = useAgentInput.subscribe((state)=>state.inputContent, callback);
        return ()=>unsubscribe();
    }, [
        callback
    ]);
};
}}),
"[project]/src/stores/input-prompt.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useInputPrompt": (()=>useInputPrompt),
    "useInputPromptStore": (()=>useInputPromptStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react/shallow.mjs [app-ssr] (ecmascript)");
;
;
;
const useInputPromptStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        prompt: "",
        setPrompt: (prompt)=>set({
                prompt
            }),
        isNewUser: false,
        setIsNewUser: (isNewUser)=>set({
                isNewUser
            })
    }), {
    name: "input-prompt",
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage)
}));
const useInputPrompt = ()=>useInputPromptStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            prompt: state.prompt,
            setPrompt: state.setPrompt
        })));
}}),
"[project]/src/lib/shiki.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// https://shiki.style/guide/bundles#fine-grained-bundle
__turbopack_context__.s({
    "getLanguageFromFilename": (()=>getLanguageFromFilename),
    "useHighlightedCode": (()=>useHighlightedCode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@sentry+nextjs@9.29.0_@open_1e1fe85ff8ebead5e28d8250bbc32024/node_modules/@sentry/nextjs/build/cjs/index.server.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$core__$5b$external$5d$__$28$shiki$2f$core$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/shiki/core [external] (shiki/core, esm_import)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$engine$2f$javascript__$5b$external$5d$__$28$shiki$2f$engine$2f$javascript$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/shiki/engine/javascript [external] (shiki/engine/javascript, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$core__$5b$external$5d$__$28$shiki$2f$core$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$engine$2f$javascript__$5b$external$5d$__$28$shiki$2f$engine$2f$javascript$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$core__$5b$external$5d$__$28$shiki$2f$core$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$engine$2f$javascript__$5b$external$5d$__$28$shiki$2f$engine$2f$javascript$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
const BundledLanguage = {
    typescript: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/typescript.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    javascript: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/javascript.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    jsx: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/jsx.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    tsx: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/tsx.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    css: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/css.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    scss: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/scss.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    html: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/html.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    sql: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/sql.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    json: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/json.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    markdown: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/markdown.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    yaml: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/yaml.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    bash: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+langs@3.6.0/node_modules/@shikijs/langs/dist/bash.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)
};
const BundledTheme = {
    "light-plus": ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+themes@3.6.0/node_modules/@shikijs/themes/dist/light-plus.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i),
    houston: ()=>__turbopack_context__.r("[project]/node_modules/.pnpm/@shikijs+themes@3.6.0/node_modules/@shikijs/themes/dist/houston.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)
};
const createHighlighter = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$core__$5b$external$5d$__$28$shiki$2f$core$2c$__esm_import$29$__["createdBundledHighlighter"])({
    langs: BundledLanguage,
    themes: BundledTheme,
    engine: ()=>(0, __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$engine$2f$javascript__$5b$external$5d$__$28$shiki$2f$engine$2f$javascript$2c$__esm_import$29$__["createJavaScriptRegexEngine"])()
});
const { codeToHtml: codeToHtmlSingleton } = /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$shiki$2f$core__$5b$external$5d$__$28$shiki$2f$core$2c$__esm_import$29$__["createSingletonShorthands"])(createHighlighter);
const codeToHtml = async (code, language)=>{
    return codeToHtmlSingleton(code, {
        lang: language,
        themes: {
            light: "light-plus",
            dark: "houston"
        }
    });
};
const LANGUAGE_MAP = {
    js: "javascript",
    cjs: "javascript",
    mjs: "javascript",
    jsx: "jsx",
    ts: "typescript",
    tsx: "tsx",
    css: "css",
    scss: "scss",
    html: "html",
    json: "json",
    md: "markdown",
    sh: "bash",
    sql: "sql",
    yml: "yaml",
    yaml: "yaml"
};
function getLanguageFromFilename(filename) {
    if (!filename) return "text";
    const extension = filename.split(".").pop()?.toLowerCase() || "";
    return LANGUAGE_MAP[extension] || "text";
}
const useHighlightedCode = (code, language)=>{
    const [highlightedHtml, setHighlightedHtml] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let isMounted = true;
        const highlightCode = async ()=>{
            if (!code || !isMounted) return;
            try {
                const html = await codeToHtml(code, language);
                if (isMounted) {
                    setHighlightedHtml(html);
                }
            } catch (error) {
                console.error("Failed to highlight code:", error);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["captureException"])(error);
                if (isMounted) {
                    setHighlightedHtml(code);
                }
            }
        };
        highlightCode();
        return ()=>{
            isMounted = false;
        };
    }, [
        code,
        language
    ]);
    return highlightedHtml;
};
;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/lib/xml-parser.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACTIONS_REGEX": (()=>ACTIONS_REGEX),
    "COMMUNICATION_REGEX": (()=>COMMUNICATION_REGEX),
    "extractActions": (()=>extractActions),
    "parseCommunication": (()=>parseCommunication),
    "parseFileOperations": (()=>parseFileOperations),
    "parseStreamContent": (()=>parseStreamContent)
});
const ACTION_TAG_REGEX = /<([a-z_]+)([^>]*?)(?:\/>|>([\s\S]*?)<\/\1\s*>|>([\s\S]*)?$)/gi;
const ATTR_REGEX = /(\w+)=(?:"([^"]*)"|\[([^\]]*)\])/g;
const FILE_SPLIT_REGEX = /\s*,\s*/;
const QUOTE_TRIM_REGEX = /^["']|["']$/g;
const ERROR_REGEX = /(error|failed)/i;
// file operations are handled in file-operations.ts
const ACTION_TAGS = new Set([
    "open_files_in_editor",
    "close_files_in_editor",
    "check_for_errors",
    "send_terminal_command",
    "reset_next_server",
    "execute_sql_query"
]);
const COMMUNICATION_START = "<communication>";
const COMMUNICATION_END = "</communication>";
const ACTIONS_START = "<actions>";
const ACTIONS_END = "</actions>";
function extractSection(content, startTag, endTag) {
    const startIdx = content.indexOf(startTag);
    if (startIdx === -1) return "";
    const contentStart = startIdx + startTag.length;
    const endIdx = content.indexOf(endTag, contentStart);
    // Stop at the first end tag found
    if (endIdx === -1) return content.slice(contentStart).trim();
    return content.slice(contentStart, endIdx).trim();
}
function extractActions(content) {
    return extractSection(content, ACTIONS_START, ACTIONS_END);
}
function parseStreamContent(content) {
    if (!content?.trim()) return {
        actions: []
    };
    const result = {
        actions: []
    };
    // Extract communication section if present
    const communicationContent = extractSection(content, COMMUNICATION_START, COMMUNICATION_END);
    if (communicationContent) {
        result.communication = communicationContent;
    }
    // Extract actions section
    const actionsContent = extractActions(content);
    if (!actionsContent) {
        return result;
    }
    // Reset lastIndex for safety with global regex
    ACTION_TAG_REGEX.lastIndex = 0;
    let match;
    // Use pre-compiled regex for action tags
    while((match = ACTION_TAG_REGEX.exec(actionsContent)) !== null){
        const [fullMatch, type, attrs, content, incompleteContent] = match;
        if (!ACTION_TAGS.has(type)) continue;
        // Skip if this is a partial match at the end of the string
        if (match.index + fullMatch.length > actionsContent.length) {
            break;
        }
        // TODO: add with_build and failed to the command
        const command = {
            type: type,
            status: incompleteContent !== undefined ? "pending" : "completed"
        };
        // Parse attributes using pre-compiled regex
        ATTR_REGEX.lastIndex = 0; // Reset lastIndex for global regex
        let attrMatch;
        while((attrMatch = ATTR_REGEX.exec(attrs)) !== null){
            const [, key, dqValue, sqValue] = attrMatch;
            const value = dqValue !== undefined ? dqValue : sqValue;
            if (!key) continue;
            if (key === "file_path") command.path = value;
            if (key === "message") command.message = value;
            // if (key === "with_build") command.with_build = value === "true";
            if (key === "command") command.command = value;
            // Special handling for files array
            if (key === "files" && value) {
                // More efficient file array parsing
                command.files = value.split(FILE_SPLIT_REGEX).map((s)=>s.replace(QUOTE_TRIM_REGEX, "")).filter(Boolean);
            }
        }
        // Handle content for non-file operations
        const actualContent = content || incompleteContent;
        if (actualContent && ![
            "create_file",
            "update_file",
            "delete_file"
        ].includes(type)) {
            command.content = actualContent.trim();
            // Check for errors in content using pre-compiled regex
            if (command.content && ERROR_REGEX.test(command.content)) {
                command.status = "failed";
                command.result = command.content;
            }
        }
        result.actions.push(command);
    }
    return result;
}
const TAG_TYPES = [
    "create_file",
    "update_file",
    "full_file_rewrite",
    "update_file_sections",
    "check_for_errors"
];
const FILE_OPERATION_PATTERN = new RegExp(`<(${TAG_TYPES.join("|")})\\s+file_path="([^"]+)"[^>]*>([\\s\\S]*?)(?=<(?:${TAG_TYPES.join("|")}|/)|$)`, "g");
const parseFileOperations = (content)=>{
    const fileOperations = [];
    if (!content) return fileOperations;
    const actionsContent = extractActions(content);
    if (!actionsContent) return fileOperations;
    FILE_OPERATION_PATTERN.lastIndex = 0;
    let match;
    while((match = FILE_OPERATION_PATTERN.exec(actionsContent)) !== null){
        const [, type, filePath, content] = match;
        // Check if this operation has a closing tag
        const hasClosingTag = actionsContent.includes(`</${type}>`);
        fileOperations.push({
            type: type,
            status: hasClosingTag ? "completed" : "pending",
            filePath,
            failed: content.trim() === "true" ? true : false,
            ...hasClosingTag ? {} : {
                length: content.length
            }
        });
    }
    return fileOperations;
};
const COMMUNICATION_REGEX = /<communication>([\s\S]*?)<\/communication>/i;
const ACTIONS_REGEX = /<actions>/i;
const parseCommunication = (content)=>{
    const newContent = typeof content === "string" ? content : String(content || "");
    const communicationMatch = newContent.match(COMMUNICATION_REGEX);
    if (communicationMatch?.[1]) {
        return {
            communication: communicationMatch[1].trim().replace(/<\/?communication>/gi, "")
        };
    }
    if (newContent.includes("<communication>")) {
        const startIndex = newContent.indexOf("<communication>") + "<communication>".length;
        const actionsIndex = newContent.indexOf("<actions>");
        const endIndex = actionsIndex !== -1 ? actionsIndex : newContent.length;
        return {
            communication: newContent.slice(startIndex, endIndex).trim()
        };
    }
    if (newContent.includes("</communication>")) {
        const endIndex = newContent.indexOf("</communication>");
        return {
            communication: newContent.slice(0, endIndex).trim()
        };
    }
    const actionsIndex = newContent.indexOf("<actions>");
    return {
        communication: actionsIndex !== -1 ? newContent.slice(0, actionsIndex).trim() : newContent.trim()
    };
};
}}),
"[project]/src/lib/format-timestamp.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatTimestamp": (()=>formatTimestamp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$formatDistanceToNow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/formatDistanceToNow.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isToday$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isToday.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isYesterday$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isYesterday.js [app-ssr] (ecmascript)");
;
function formatTimestamp(isoString, type = "default", year = false) {
    const utcString = isoString.endsWith("Z") ? isoString : isoString + "Z";
    const date = new Date(utcString);
    const currentYear = new Date().getFullYear();
    const dateYear = date.getFullYear();
    switch(type){
        case "edited":
            return `Edited on ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, dateYear === currentYear && !year ? "d MMMM" : "d MMMM yyyy")}`;
        case "message-timestamp":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, dateYear === currentYear && !year ? "h:mm a d MMMM" : "h:mm a d MMMM yyyy");
        case "lastChat":
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isToday$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isToday"])(date)) {
                return `Last chat today at ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "h:mm a")}`;
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isYesterday$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isYesterday"])(date)) {
                return "Last chat yesterday";
            } else {
                return `Last chat ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$formatDistanceToNow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDistanceToNow"])(date)} ago`;
            }
        case "deployed":
            return `Deployed on ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, dateYear === currentYear && !year ? "h:mm a d MMMM" : "h:mm a d MMMM yyyy")}`;
        case "commit-history":
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isToday$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isToday"])(date)) {
                return `Last commit today at ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "h:mm a")}`;
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$isYesterday$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isYesterday"])(date)) {
                return "Last commit yesterday";
            } else {
                return `Last commit ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$formatDistanceToNow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDistanceToNow"])(date)} ago`;
            }
        default:
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$date$2d$fns$40$4$2e$1$2e$0$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, dateYear === currentYear && !year ? "h:mm a d MMMM" : "h:mm a d MMMM yyyy");
    }
}
}}),
"[project]/src/constants/image-validation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ALLOWED_IMAGE_TYPES": (()=>ALLOWED_IMAGE_TYPES),
    "MAX_IMAGES_PER_MESSAGE": (()=>MAX_IMAGES_PER_MESSAGE),
    "MAX_IMAGE_SIZE": (()=>MAX_IMAGE_SIZE),
    "validateImage": (()=>validateImage),
    "validateImageCount": (()=>validateImageCount)
});
const MAX_IMAGES_PER_MESSAGE = 5;
const MAX_IMAGE_SIZE = 10 * 1024 * 1024;
const ALLOWED_IMAGE_TYPES = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp"
];
const validateImage = (file)=>{
    if (file.size > MAX_IMAGE_SIZE) {
        throw new Error(`Image too large: ${file.name}. Maximum size is 10MB per image.`);
    }
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
        throw new Error(`Invalid image type: ${file.name}. Allowed types: JPEG, PNG, GIF, WebP.`);
    }
};
const validateImageCount = (currentCount, additionalCount = 0)=>{
    if (currentCount + additionalCount > MAX_IMAGES_PER_MESSAGE) {
        throw new Error(`Maximum ${MAX_IMAGES_PER_MESSAGE} are allowed per message.`);
    }
};
}}),
"[project]/src/utils/error-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Filters out webpack hot-update related errors that should be ignored
 * @param {Array<unknown>} errors - Array of error objects
 * @returns {Array<unknown>} - Filtered array of errors
 */ __turbopack_context__.s({
    "deduplicateErrors": (()=>deduplicateErrors),
    "filterIgnorableErrors": (()=>filterIgnorableErrors)
});
const filterIgnorableErrors = (errors)=>{
    if (!errors || !errors.length) return [];
    return errors.filter((error)=>{
        // Skip empty error objects
        if (!error || typeof error !== "object" || !("message" in error) || !error.message || error.message === "{}" || error.message === "undefined" || error.message === "[object Object]" || typeof error.message === "string" && error.message.trim() === "") {
            return false;
        }
        // Skip cancelled errors
        if (typeof error.message === "string" && error.message.includes('"cancelled": true')) {
            return false;
        }
        // Skip Next.js router navigation errors
        if (typeof error.message === "string" && error.message.includes("Invariant: attempted to hard navigate to the same URL") && "stack" in error && typeof error.stack === "string" && error.stack.includes("handleHardNavigation") && error.stack.includes("Router.change")) {
            return false;
        }
        // Skip Next.js hydration errors with specific stack trace pattern
        if ("stack" in error && typeof error.stack === "string" && error.stack.includes("hydration-error-info.js") && error.stack.includes("setup-hydration-warning.js") && error.stack.includes("handleRouteInfoError") && error.stack.includes("getRouteInfo") && error.stack.includes("Router.change")) {
            return false;
        }
        // Skip Next.js internal errors with "{}" at the beginning of the message and stack trace
        if (typeof error.message === "string" && error.message.startsWith("{}") && (error.message.includes("webpack-internal") || error.message.includes("next/dist/client") || error.message.includes("hydration-error-info.js") || error.message.includes("setup-hydration-warning.js"))) {
            return false;
        }
        // Skip "Page not found" errors
        if (typeof error.message === "string" && (error.message.includes("Page not found") || error.message.includes("page not found") || error.message.includes("Cannot find page") || error.message.includes("cannot find page") || error.message.includes("page does not exist") || error.message.includes("Page does not exist") || error.message.includes("at /") || error.message.includes("Path: /_next/static/chunks/pages/"))) {
            return false;
        }
        // Skip 404 Not Found errors
        if (typeof error.message === "string" && (error.message.includes("404 Not Found") || error.message.includes("not found") || error.message.includes("Failed to load script") || error.message.toLowerCase().includes("404") || error.message.includes(".html") || "status" in error && error.status === 404 || "statusCode" in error && error.statusCode === 404 || "code" in error && error.code === 404)) {
            return false;
        }
        // Skip Next.js hydration error with [object Object] and specific stack trace
        if (typeof error.message === "string" && error.message === "[object Object]" && "stack" in error && typeof error.stack === "string" && error.stack.includes("hydration-error-info.js") && error.stack.includes("setup-hydration-warning.js") && error.stack.includes("handleRouteInfoError")) {
            return false;
        }
        // Skip Next.js hydration errors with [object Object] and hydration-error-info
        if (typeof error.message === "string" && error.message.includes("[object Object]") && "stack" in error && typeof error.stack === "string" && error.stack.includes("hydration-error-info") && error.stack.includes("handleRouteInfoError")) {
            return false;
        }
        // Skip hydration-related errors
        if (typeof error.message === "string" && (error.message.includes("Hydration failed") || error.message.includes("hydration error") || error.message.includes("An error occurred during hydration") || error.message.includes("react-hydration-error") || error.message.includes("validateDOMNesting"))) {
            return false;
        }
        // Skip webpack hot-update errors
        if (typeof error.message === "string" && (error.message.includes("webpack.hot-update.json") || error.message.includes("hot-update.js") || error.message.includes("webpack-hmr"))) {
            return false;
        }
        // Skip unknown errors with no details
        if (typeof error.message === "string" && error.message === "Unknown error" && (!("stack" in error) || !error.stack)) {
            return false;
        }
        // Skip SoftGen script related errors
        if (typeof error.message === "string" && // error.message.includes('cdn.softgen.ai/script.js') ||
        (error.message.includes("Failed to send message") || error.message.includes("SoftGen"))) {
            return false;
        }
        // Skip "Abort fetching component for route" errors
        // TODO: Consider refactoring agent to handle route fetching/cancellation more gracefully -> router.replace, router.replace
        if (typeof error.message === "string" && error.message.includes("Abort fetching component for route:")) {
            return false;
        }
        // Skip Next.js resource errors
        if (typeof error.message === "string" && error.message.includes("/_next/static/")) {
            // Keep only page-specific errors that might be relevant
            const isPageError = error.message.includes("/_next/static/chunks/pages/") && !error.message.includes("/_next/static/chunks/pages/_app.js") && !error.message.includes("/_next/static/chunks/pages/_error.js");
            return isPageError;
        }
        // Skip any errors containing _next/static (comprehensive filtering)
        if (typeof error.message === "string" && error.message.toLowerCase().includes("_next/static")) {
            return false;
        }
        // Skip specific Firebase/Firestore 404 errors that are expected or configuration-related
        if (typeof error.message === "string" && error.message.includes("404 Not Found") && (error.message.includes("identitytoolkit.googleapis.com/v1/accounts:lookup") || error.message.includes("identitytoolkit.googleapis.com/v1/accounts:update") || error.message.includes("identitytoolkit.googleapis.com/v1/accounts:signInWithPassword") || error.message.includes("securetoken.googleapis.com/v1/token") || error.message.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel") || error.message.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel"))) {
            return false;
        }
        // Skip Firestore channel errors and other Firebase API errors
        if (typeof error.message === "string" && (error.message.includes("Failed to fetch") || error.message === "Error: Failed to fetch") && "stack" in error && typeof error.stack === "string" && (error.stack.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel") || error.stack.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:lookup") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:update") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:signInWithPassword") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:signUp") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:sendOobCode") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:resetPassword") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:delete") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:sendVerificationCode") || error.stack.includes("identitytoolkit.googleapis.com/v1/accounts:verifyPassword") || error.stack.includes("securetoken.googleapis.com/v1/token") || error.stack.includes("firebaseinstallations.googleapis.com") || error.stack.includes("fcmregistrations.googleapis.com") || error.stack.includes("firebasedatabase.app") || error.stack.includes("firestore.googleapis.com/google.firestore.v1.Firestore/BatchGet") || error.stack.includes("firestore.googleapis.com/google.firestore.v1.Firestore/RunQuery") || error.stack.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Commit"))) {
            return false;
        }
        // Skip network errors related to Firestore channels
        if ("type" in error && error.type === "network" && "details" in error && error.details && typeof error.details === "object" && "url" in error.details && typeof error.details.url === "string" && (error.details.url.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel") || error.details.url.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel"))) {
            return false;
        }
        // Skip console errors related to Firestore channels
        if ("type" in error && error.type === "console" && typeof error.message === "string" && error.message.includes("Fetch error for") && (error.message.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel") || error.message.includes("firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel"))) {
            return false;
        }
        // Skip Next.js stack frame errors
        if (typeof error.message === "string" && error.message.includes("/__nextjs_original-stack-frame") || "details" in error && error.details && typeof error.details === "object" && "url" in error.details && typeof error.details.url === "string" && error.details.url.includes("/__nextjs_original-stack-frame")) {
            return false;
        }
        // Skip "Fetch error for /__nextjs_original-stack-frame" errors
        if (typeof error.message === "string" && error.message.includes("Fetch error for /__nextjs_original-stack-frame")) {
            return false;
        }
        // Skip "404 Not Found" errors for Next.js stack frames
        if (typeof error.message === "string" && error.message.includes("404 Not Found") && error.message.includes("/__nextjs_original-stack-frame")) {
            return false;
        }
        // Skip network errors related to Next.js resources
        if ("type" in error && error.type === "network" && "details" in error && error.details && typeof error.details === "object" && "url" in error.details && typeof error.details.url === "string" && error.details.url.includes("/_next/")) {
            return false;
        }
        // Skip hydration mismatch errors for Next.js links with query parameters
        if (typeof error.message === "string" && (error.message.includes("Prop `%s` did not match") || error.message.includes("Warning: Prop")) && error.message.includes("href") && error.message.includes("?")) {
            return false;
        }
        // Skip font and image loading errors
        if (typeof error.message === "string" && (error.message.toLowerCase().includes("fonts") || error.message.toLowerCase().includes("images") || error.message.toLowerCase().includes(".png") || error.message.toLowerCase().includes(".jpeg") || error.message.toLowerCase().includes(".jpg") || error.message.toLowerCase().includes(".webp") || error.message.toLowerCase().includes("font") || error.message.toLowerCase().includes("woff") || error.message.toLowerCase().includes("ttf") || error.message.toLowerCase().includes("otf") || error.message.toLowerCase().includes("eot") || error.message.toLowerCase().includes("_next"))) {
            return false;
        }
        return true;
    });
};
const deduplicateErrors = (errors)=>{
    if (!errors || !errors.length) return [];
    // First filter out ignorable errors
    const filteredErrors = filterIgnorableErrors(errors);
    // Create a map to deduplicate errors
    const errorMap = new Map();
    filteredErrors.forEach((error)=>{
        if (!error || typeof error !== "object") return;
        // Skip network errors related to images
        if ("type" in error && error.type === "network" && "details" in error && error.details && typeof error.details === "object" && "url" in error.details && typeof error.details.url === "string" && (error.details.url.includes("images") || error.details.url.includes("__nextjs") || error.details.url.includes("fonts"))) {
            return;
        }
        if ("type" in error && error.type === "runtime" && "message" in error && typeof error.message === "string" && error.message.includes("Unknown runtime error")) {
            return;
        }
        // For Next.js resource errors, group them by the type of resource
        if ("message" in error && typeof error.message === "string" && error.message.includes("/_next/static/chunks/")) {
            // Extract the resource type (e.g., pages, webpack, main)
            const resourceTypeMatch = error.message.match(/\/_next\/static\/chunks\/([^\/\.]+)/);
            const resourceType = resourceTypeMatch ? resourceTypeMatch[1] : "unknown";
            // For pages, extract the actual page path
            if (resourceType === "pages") {
                const pagePathMatch = error.message.match(/\/_next\/static\/chunks\/pages(\/[^\.]+)\.js/);
                if (pagePathMatch && pagePathMatch[1]) {
                    const key = `next-page-${pagePathMatch[1]}`;
                    if (!errorMap.has(key)) {
                        errorMap.set(key, {
                            ...error,
                            id: "id" in error && error.id ? error.id : `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                            simplifiedMessage: `Page not found: ${pagePathMatch[1]}`
                        });
                    }
                    return;
                }
            }
            // Create a key based on the resource type
            const key = `next-resource-${resourceType}`;
            // Only keep one error per resource type
            if (!errorMap.has(key)) {
                errorMap.set(key, {
                    ...error,
                    id: "id" in error && error.id ? error.id : `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    simplifiedMessage: `Failed to load Next.js ${resourceType} resources`
                });
            }
        } else {
            // For other errors, use the first line of the message as the key
            const messageFirstLine = "message" in error && typeof error.message === "string" ? error.message.split("\n")[0] : "unknown";
            const errorType = "type" in error && error.type ? String(error.type) : "unknown";
            const key = `${errorType}-${messageFirstLine}`;
            // Only keep the first occurrence of each error
            if (!errorMap.has(key)) {
                errorMap.set(key, {
                    ...error,
                    id: "id" in error && error.id ? error.id : `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
                });
            }
        }
    });
    // Convert map values back to array
    return Array.from(errorMap.values());
};
}}),
"[project]/src/app/(main)/project/[id]/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/modal.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$app$2f$banned$2d$user$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/app/banned-user.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$app$2f$zendesk$2d$help$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/app/zendesk-help-button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$desktop$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/project/desktop-view.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$mobile$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/project/mobile-view.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$mobile$2f$header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/project/mobile/header.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$modal$2f$no$2d$token$2d$modal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/project/modal/no-token-modal.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$modal$2f$restore$2d$supabase$2d$paused$2d$project$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/project/modal/restore-supabase-paused-project.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-mobile.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$threads$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-threads.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/auth-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/current-thread.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/navigate-file.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$settings$2d$tab$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/settings-tab.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$desktop$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$mobile$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$desktop$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$mobile$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const SettingsModalContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/src/features/project/project-settings-modal.tsx [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i));
const RemixSuccessModal = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["lazy"])(()=>__turbopack_context__.r("[project]/src/features/project/remix-success-modal.tsx [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i));
const ProjectPage = ({ params })=>{
    const { id } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["use"])(params);
    const resetNavigateFileStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNavigateFile"])((state)=>state.reset);
    const resetSettingsStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$settings$2d$tab$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSettingsStore"])((state)=>state.reset);
    const setCurrentThreadId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCurrentThreadStore"])((state)=>state.setId);
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const isRemix = searchParams.get("remix") === "true";
    const [isRemixSuccessModalOpen, setIsRemixSuccessModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(isRemix);
    const isMobile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$mobile$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsMobile"])();
    const { selectFirstThread } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$threads$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useThread"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const closeModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$settings$2d$tab$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSettingsStore"])((state)=>state.closeModal);
    const open = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$settings$2d$tab$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSettingsStore"])((state)=>state.settingsTab !== null);
    const handleClose = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$settings$2d$tab$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSettingsStore"])((state)=>state.closeModal);
    // reset stores on project change and on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        resetNavigateFileStore();
        setCurrentThreadId(null);
        resetSettingsStore();
        return ()=>{
            resetNavigateFileStore();
            setCurrentThreadId(null);
            resetSettingsStore();
        };
    }, [
        id,
        resetNavigateFileStore,
        setCurrentThreadId,
        resetSettingsStore
    ]);
    const handleStartBuilding = ()=>{
        const threadId = selectFirstThread();
        if (threadId) {
            setCurrentThreadId(threadId);
            setIsRemixSuccessModalOpen(false);
            const newSearchParams = new URLSearchParams(Array.from(searchParams.entries()));
            newSearchParams.delete("remix");
            const base = `/project/${id}`;
            const query = newSearchParams.toString();
            const url = query ? `${base}?${query}` : base;
            router.replace(url);
        }
    };
    if (user?.userFromDb?.plan === "banned") {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$app$2f$banned$2d$user$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
            lineNumber: 72,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            isMobile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$mobile$2f$header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                lineNumber: 77,
                columnNumber: 20
            }, this),
            user?.userFromDb?.plan && user.userFromDb.plan !== "free-tier" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$app$2f$zendesk$2d$help$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ZendeskScript"], {
                project: true
            }, void 0, false, {
                fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                lineNumber: 80,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-1 flex-col gap-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1 md:min-h-min",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "z-20 flex flex-1 flex-col gap-4 bg-background md:h-screen",
                            style: {
                                scrollBehavior: "smooth",
                                scrollbarWidth: "none"
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-1 bg-background",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mx-auto flex w-full flex-1 flex-col md:h-screen",
                                        children: isMobile ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$mobile$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            projectId: id
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                            lineNumber: 94,
                                            columnNumber: 29
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$desktop$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            projectId: id
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                            lineNumber: 94,
                                            columnNumber: 61
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                        lineNumber: 93,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$modal$2f$no$2d$token$2d$modal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                        lineNumber: 97,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$modal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LazyModal"], {
                                        open: open,
                                        onOpenChange: handleClose,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SettingsModalContent, {
                                            setShowModal: closeModal
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                            lineNumber: 100,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                        lineNumber: 99,
                                        columnNumber: 15
                                    }, this),
                                    isRemix && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(RemixSuccessModal, {
                                        open: isRemixSuccessModalOpen,
                                        onOpenChange: setIsRemixSuccessModalOpen,
                                        onStartBuilding: handleStartBuilding
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                        lineNumber: 104,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                                lineNumber: 92,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                            lineNumber: 85,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$project$2f$modal$2f$restore$2d$supabase$2d$paused$2d$project$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                    lineNumber: 84,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/(main)/project/[id]/page.tsx",
                lineNumber: 83,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = ProjectPage;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=src_1b9c866b._.js.map