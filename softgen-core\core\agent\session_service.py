import logging
import asyncio
import datetime
from fastapi import APIRouter, HTTPException,UploadFile, WebSocket, Query
from starlette.websockets import WebSocketState, WebSocketDisconnect
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select
from sqlalchemy.future import select as future_select
from core.envs.env_ops import env_ops
from core.models import Project, ProjectThread
from core.agent.session import Session
from core.utils.image_processor import process_images
from typing import List, Dict, Any, Optional
import json
from core.db import Database
from core.platform.user import User
import jwt
from core.config import settings
import uuid
import base64
from core.utils.model_mapping import DEFAULT_MODEL_ID
import io
from core.platform.is_admin import (
    is_admin,
    is_admin_or_owner_or_team
)
from core.utils.websocket_utils import send_thread_update

# Initialize APIRouter
router = APIRouter()

running_tasks = {}
db = Database()

# Add this at the top with other global variables
session_instances = {}  # Store Session instances by run_id

PING_INTERVAL = 30  # Send ping every 30 seconds
PING_TIMEOUT = 10   # Wait 10 seconds for pong response
EXTRA_TIMEOUT = 20

async def send_websocket_message(websocket: WebSocket, message_type: str, data: str = None, 
                               is_complete: bool = True, termination: bool = True, 
                               thread_id: int = None, project_id: str = None):
    """
    Utility function to send formatted websocket messages with improved state handling.
    """
    if not websocket or not hasattr(websocket, 'client_state'):
        return
        
    try:
        # Check if we're already in a closing state
        if getattr(websocket, 'is_closing', False):
            return
            
        # Only send if the connection is still active
        if websocket.client_state == WebSocketState.CONNECTED:
            message = {
                "type": message_type,
                "isCompleteMessage": is_complete,
                "termination": termination
            }
            
            # Add optional fields based on message type
            if message_type == "error" and data:
                message["data"] = f"Error: {str(data)}"
                websocket.running_session = False
            elif message_type == "session_status":
                message["status"] = data if data else "stopped"
                if thread_id:
                    message["thread_id"] = thread_id
                if project_id:
                    message["project_id"] = project_id
                if data == "stopped":
                    websocket.running_session = False
            
            await websocket.send_json(message)
    except Exception as e:
        logging.error(f"Error sending websocket message: {str(e)}")
        if websocket and not getattr(websocket, 'is_closing', False):
            websocket.running_session = False

async def send_error_message(websocket: WebSocket, error: str):
    """Utility function to send error messages."""
    await send_websocket_message(websocket, "error", error)

async def send_status_message(websocket: WebSocket, status: str = "stopped", 
                            thread_id: int = None, project_id: str = None,
                            is_complete: bool = True, termination: bool = True):
    """Utility function to send status messages."""
    await send_websocket_message(websocket, "session_status", status, 
                               is_complete, termination, thread_id, project_id)

async def start_session_run_service(
    db, project_id: str, thread_id: Optional[int], objective: str, 
    autonomous_iterations: int, mode: str, objective_images: List[Any], 
    selectedPaths: List[str], prompt_set: int, current_user: User, 
    websocket: WebSocket = None, model_id: str = DEFAULT_MODEL_ID, agent: Session = None, user_is_admin: bool = False,
    base_url: Optional[str] = None, chat_mode: bool = False
):
    try:
        async with db.get_async_session() as session:
            async with session.begin():
                # Check if the user owns the project or is an admin
                stmt = future_select(Project).where(Project.project_id == project_id)
                result = await session.execute(stmt)
                project = result.scalar_one_or_none()
                
                if not project:
                    raise HTTPException(status_code=404, detail="Project not found")
                
                # Use the utility function to check access
                if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
                    raise HTTPException(status_code=403, detail="Not authorized to start a session for this project")
                
                if thread_id is None:
                    # Create a new thread
                    new_thread = ProjectThread(
                        project_id=project_id,
                        messages=json.dumps([]),
                        creation_date=datetime.datetime.now().isoformat(),
                        last_updated_date=datetime.datetime.now().isoformat()
                    )
                    session.add(new_thread)
                    await session.flush()
                    thread_id = new_thread.thread_id

        # First process all images
        processed_images = process_images(objective_images) if objective_images else []
        
        # Then upload each processed image
        for img in processed_images:
            if img and img.get('base64'):
                try:
                    # Generate unique filename
                    filename = f"image_{uuid.uuid4()}.png"
                    filepath = f"/uploads/{filename}"
                    
                    # Convert base64 to bytes
                    image_data = base64.b64decode(img['base64'])
                    
                    # Create file-like object
                    file_obj = io.BytesIO(image_data)
                    file_obj.name = filename  # Add name attribute
                    
                    # Create UploadFile without content_type
                    upload_file = UploadFile(filename=filename, file=file_obj)
                    
                    # Upload using FileOperationsManager with is_image=True
                    asyncio.create_task(env_ops.upload_file(project.env_id, upload_file, filepath))
                    img['url'] = filepath
                except Exception as e:
                    logging.error(f"Error uploading image: {str(e)}")
                    if websocket:
                        await send_error_message(websocket, f"Error uploading image: {str(e)}")
                        await send_status_message(websocket, "stopped")
                    raise HTTPException(status_code=500, detail=f"Error uploading image: {str(e)}")
        
        asyncio.create_task(run_session_background(
            project_id, 
            thread_id, 
            objective, 
            autonomous_iterations,
            mode,
            processed_images,
            selectedPaths,
            prompt_set,
            websocket,
            agent,  # Pass the agent instance
            user_is_admin,  # Pass the admin status
            current_user,
            model_id,  # Pass model_id
            base_url,  # Pass base_url
            chat_mode  # Pass chat_mode
        ))
        
        return {"message": "Session initialization and run started", "thread_id": thread_id}
    except IntegrityError as e:
        logging.error(f"Database integrity error in start_session_run: {str(e)}")
        if websocket:
            await send_error_message(websocket, "Conflict: Unable to start session due to database constraint")
            await send_status_message(websocket, "stopped")
        await session.rollback()
        raise HTTPException(status_code=409, detail="Conflict: Unable to start session due to database constraint")
    except Exception as e:
        logging.error(f"Error in start_session_run: {str(e)}")
        if websocket:
            await send_error_message(websocket, str(e))
            await send_status_message(websocket, "stopped")
        raise HTTPException(status_code=500, detail=f"Error starting session run: {str(e)}")

async def run_session_background(project_id: str, thread_id: int, objective: str, 
                               autonomous_iterations: int = 10, mode: str = "interactive", 
                               processed_images: List[Dict[str, Any]] = None, selectedPaths: List[str] = None, prompt_set: int = 1, 
                               websocket: WebSocket = None, agent: Session = None, user_is_admin: bool = False, current_user: User = None,
                               model_id: str = DEFAULT_MODEL_ID, base_url: Optional[str] = None, chat_mode: bool = False):
    try:
        project, user = await agent.db.project_service.get_project_and_user_by_thread_id(thread_id)
        # Pre-fetch project and user information
        if not project or not user:
            raise HTTPException(status_code=404, detail="Project or user not found")
            
            if is_admin:
                user = current_user

            # Determine prompt set based on project settings
            if project.agent_instructions and project.agent_instructions.strip():
                agent.agent_instructions = project.agent_instructions if project.agent_instructions else ""
                prompt_set = None  # Use project-specific instructions
            elif prompt_set == 3:
                prompt_set = 3  # Keep explicit prompt_set 3 if specified
            else:
                # Set default prompt based on onboarding status
                prompt_set = 1 if project.onboarding_completed else 2

        # Add the user's objective and images to the thread
        await agent.thread_manager.add_message(
            thread_id,
            {"role": "user", "content": objective},
            images=processed_images if processed_images else None
        )


        # Start run_session and handle any errors
        try:
            await agent.run_session(prompt_set=prompt_set, selectedPaths=selectedPaths, mode=mode, model_id=model_id, 
                                   autonomous_iterations=autonomous_iterations, is_admin=is_admin, base_url=base_url,chat_mode=chat_mode)
        except Exception as run_error:
            logging.error(f"Error in run_session: {str(run_error)}")
            if agent:
                await agent.stop_session()
            if websocket:
                await send_error_message(websocket, f"Error in run_session: {str(run_error)}")
                await send_status_message(websocket, "stopped")
            raise

    except asyncio.CancelledError:
        if agent:
            await agent.stop_session()
        if websocket:
            await send_status_message(websocket, "stopped")
    except Exception as e:
        logging.error(f"Critical error in run_session_background: {str(e)}", exc_info=True)
        if agent:
            await agent.stop_session()
        if websocket:
            await send_error_message(websocket, str(e))
            await send_status_message(websocket, "stopped")

async def keepalive_ping(websocket: WebSocket, thread_id: int, current_user: User):
    """
    Keepalive ping mechanism with improved connection state handling.
    """
    try:
        last_streaming_state = False
        next_update_time = 0
        last_ping_time = 0
        
        while websocket.client_state == WebSocketState.CONNECTED and not getattr(websocket, 'is_closing', False):
            current_time = asyncio.get_event_loop().time()
            current_streaming_state = getattr(websocket, 'is_streaming', False)
            running_session = getattr(websocket, 'running_session', False)
            
            # Only send ping if we're not streaming and not running a session
            if not current_streaming_state and not running_session:
                if current_time - last_ping_time >= PING_INTERVAL:
                    try:
                        if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                            await websocket.send_json({"type": "ping"})
                            last_ping_time = current_time
                    except Exception as e:
                        logging.error(f"Error sending ping: {str(e)}")
                        break
            
            # Handle thread updates
            update_interval = 8 if running_session else 15
            
            if current_streaming_state:
                next_update_time = current_time + update_interval
                last_streaming_state = True
            else:
                if last_streaming_state:
                    try:
                        if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                            update_task = send_thread_update(db, websocket, thread_id, current_user)
                            await update_task
                            next_update_time = current_time + update_interval
                    except Exception as e:
                        logging.error(f"Error sending thread update after streaming: {str(e)}")
                    last_streaming_state = False
                elif current_time >= next_update_time:
                    try:
                        if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                            update_task = send_thread_update(db, websocket, thread_id, current_user)
                            await update_task
                            next_update_time = current_time + update_interval
                    except Exception as e:
                        logging.error(f"Error sending regular thread update: {str(e)}")
            
            await asyncio.sleep(0.1)
                
    except Exception as e:
        logging.error(f"Keepalive task error: {str(e)}")
    finally:
        # Don't try to close the websocket here - let the main handler do it
        pass

@router.websocket("/start_thread_websocket/{project_id}/{thread_id}")
async def start_thread_websocket(
    websocket: WebSocket,
    project_id: str,
    thread_id: int,
    token: str = Query(...),
):
    ping_task = None
    agent = None  # Initialize agent variable
    
    try:
        # Initialize WebSocket state before accepting connection
        websocket.running_session = False
        websocket.connected = True
        websocket.is_streaming = False
        websocket.is_closing = False
        
        await websocket.accept()
        
        try:
            # Authenticate user using the token from query parameter
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            email = payload.get("sub")
            logging.info(f"Authentication successful for user: {email}")
            
            if not email:
                raise ValueError("No email found in token")

            # Get the user manually
            async with db.get_async_session() as session:
                result = await session.execute(select(User).where(User.email == email))
                current_user = result.scalar_one_or_none()
                
                if not current_user:
                    raise ValueError(f"User not found for email: {email}")

                if not current_user.is_active:
                    raise ValueError(f"Inactive user: {email}")

                # Verify project and thread access
                thread = await session.execute(select(ProjectThread).where(ProjectThread.thread_id == thread_id))
                thread = thread.scalar_one_or_none()
                if not thread:
                    raise ValueError(f"Thread not found: {thread_id}")

                project = await session.execute(select(Project).where(Project.project_id == project_id))
                project = project.scalar_one_or_none()
                if not project:
                    raise ValueError(f"Project not found: {project_id}")

                # Check user access
                if not is_admin_or_owner_or_team(project, current_user.id, current_user.email):
                    raise ValueError(f"User {email} not authorized for project {project_id}")

                # Create the Session instance early
                agent = Session(project_id)    

                project, user = await agent.db.project_service.get_project_and_user_by_thread_id(thread_id)    

                agent.websocket = websocket
                agent.thread_manager.websocket = websocket
                agent.thread_manager.user = current_user
                mode = "autonomous"
                user_is_admin = is_admin(current_user)
                if user_is_admin:
                    user = current_user
                await agent.init_session(thread_id, mode, websocket, user_is_admin, current_user, user, project)

            # Start keepalive ping task
            ping_task = asyncio.create_task(keepalive_ping(websocket, thread_id, current_user))
            
            while websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                try:
                    data = await websocket.receive_json()
                    
                    if data.get("type") == "pong":
                        continue
                    
                    if data.get("action") == "new_message":
                        if websocket.running_session:
                            await send_error_message(websocket, "A session is already running")
                            continue
                        
                        websocket.running_session = True
                        await send_status_message(websocket, "running", thread_id=thread_id, project_id=project_id)
                        
                        try:
                            result = await start_session_run_service(
                                db, project_id, thread_id, data.get("message", ""),
                                data.get("autonomous_iterations", 10),
                                data.get("mode", "autonomous"),
                                data.get("objective_images", []),
                                data.get("selected_paths", []),
                                data.get("prompt_set", 1),
                                current_user,
                                websocket,
                                data.get("model_id", DEFAULT_MODEL_ID),
                                agent,
                                user_is_admin,
                                data.get("base_url"),  # Extract base_url from the message data
                                data.get("chat_mode", False)
                            )
                            
                            if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                                await websocket.send_json(result)
                                
                        except Exception as e:
                            logging.error(f"Session run error: {str(e)}")
                            if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                                websocket.running_session = False
                                await send_status_message(websocket, "stopped", thread_id=thread_id, project_id=project_id)
                    
                    elif data.get("action") == "stop":
                        websocket.running_session = False
                        if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                            await send_status_message(websocket, "stopped", thread_id=thread_id, project_id=project_id)
                
                except WebSocketDisconnect:
                    logging.info(f"WebSocket disconnected for thread {thread_id}")
                    break
                except Exception as e:
                    logging.error(f"Error in WebSocket message loop: {str(e)}")
                    if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                        websocket.running_session = False
                        await send_error_message(websocket, f"Error processing message: {str(e)}")
        
        except Exception as e:
            error_msg = f"Fatal error in websocket handler: {str(e)}"
            logging.error(error_msg)
            if websocket.client_state == WebSocketState.CONNECTED and not websocket.is_closing:
                await send_error_message(websocket, error_msg)
                await send_status_message(websocket, "stopped")
    
    finally:
        # Clean up
        if ping_task and not ping_task.done():
            ping_task.cancel()
            try:
                await ping_task
            except asyncio.CancelledError:
                pass
        
        # Mark as closing to prevent further send attempts
        websocket.is_closing = True
        websocket.running_session = False
        
        if websocket.client_state != WebSocketState.DISCONNECTED:
            try:
                await websocket.close()
            except Exception as e:
                logging.error(f"Error closing websocket: {str(e)}")