"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { createCustomerPortalSession } from "@/lib/api";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { AuthState } from "@/types";
import { CartSolid, CreditCardSolid, Logout, Moon, Sun, UserSolid } from "@mynaui/icons-react";
import { Coins } from "lucide-react";
import { useTheme } from "next-themes";
import Link from "next/link";
import { useTokenInfo } from "../../hooks/use-token-info";
import { errorToast } from "./toast";

export default function AccountDropdown({
  align = "center",
  side = "right",
  sideOffset = 5,
  alignOffset = 0,
  className = "",
  buttonClassName = "",
  openProfileModal,
  openTokenModal,
}: {
  user?: AuthState;
  align?: "center" | "end" | "start";
  side?: "left" | "right" | "top" | "bottom";
  buttonClassName?: string;
  className?: string;
  sideOffset?: number;
  alignOffset?: number;
  openProfileModal?: () => void;
  openTokenModal?: () => void;
}) {
  const { logout, user } = useAuth();
  const { theme, setTheme } = useTheme();
  const { getTokenDisplayInfo } = useTokenInfo();
  const tokenInfo = getTokenDisplayInfo();
  const createCustomerPortal = async () => {
    if (user.userFromDb?.stripe_customer_id) {
      const customerPortalSession = await createCustomerPortalSession(
        user.userFromDb.stripe_customer_id,
      );
      window.open(customerPortalSession.url, "_blank");
    } else {
      errorToast("Failed to create customer portal session. Please try again.");
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        asChild
        className={cn("w-full focus:outline-none sm:w-full md:w-full lg:w-full", className)}
      >
        <Button
          variant="ghost"
          className={cn(
            "group flex cursor-pointer items-center justify-start gap-3 px-2.5 text-sm text-primary/90 transition-all duration-500 hover:bg-sidebar-accent/80 hover:text-primary",
            buttonClassName,
          )}
        >
          <Avatar className="h-6 w-6">
            <AvatarImage src={`https://avatar.vercel.sh/${user?.email}.png`} />
            <AvatarFallback>{user?.email?.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
          <Label className="cursor-pointer text-sm">{user.email}</Label>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align={align}
        side={side}
        alignOffset={alignOffset}
        sideOffset={sideOffset}
        className="w-full p-0"
      >
        <div className="p-2 pb-0.5">
          <DropdownMenuLabel className="mb-1">My Account</DropdownMenuLabel>

          {openProfileModal && (
            <DropdownMenuItem onClick={openProfileModal} className="justify-start gap-3">
              <UserSolid className="h-4 w-4" strokeWidth={2} />
              Profile
            </DropdownMenuItem>
          )}
          <DropdownMenuItem onClick={() => createCustomerPortal()} className="justify-start gap-3">
            <CreditCardSolid className="h-4 w-4" strokeWidth={2} />
            Billing
          </DropdownMenuItem>

          {user.wholesalePlan ? (
            <DropdownMenuItem className="justify-start gap-3" asChild>
              <Link href="/wallet">
                <CartSolid strokeWidth={2} className="h-4 w-4" />
                Wallet
              </Link>
            </DropdownMenuItem>
          ) : (
            <>
              {tokenInfo.remainingTokens && (
                <DropdownMenuItem className="justify-start gap-3">
                  <Coins className="h-4 w-4" strokeWidth={2} />
                  {tokenInfo.remainingTokens} Tokens
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onSelect={openTokenModal} className="justify-start gap-3">
                <CartSolid strokeWidth={2} className="h-4 w-4" />
                Purchase Tokens
              </DropdownMenuItem>
            </>
          )}

          <DropdownMenuItem
            onClick={() => setTheme(theme === "light" ? "dark" : "light")}
            className="justify-start gap-3"
          >
            {theme === "light" ? (
              <Moon className="h-4 w-4" strokeWidth={2} />
            ) : (
              <Sun className="h-4 w-4" strokeWidth={2} />
            )}
            {theme === "light" ? "Dark Mode" : "Light Mode"}
          </DropdownMenuItem>
        </div>

        <DropdownMenuSeparator />

        <div className="p-2 pt-0.5">
          <DropdownMenuItem onClick={() => logout()}>
            <span className="flex w-full items-center justify-start gap-3 text-red-500 hover:text-red-600">
              <Logout className="h-4 w-4" strokeWidth={2} />
              Log out
            </span>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
