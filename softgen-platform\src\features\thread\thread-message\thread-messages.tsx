"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { LazyModal } from "@/components/ui/modal";
import { TextShimmer } from "@/components/ui/text-shimmer";
import Typography from "@/components/ui/typography";
import { useThread } from "@/hooks/use-threads";
import { cn } from "@/lib/utils";
import { useWebSocketMessage } from "@/providers/thread-provider/hooks";
import { WrapUpSummaryMessage } from "@/providers/thread-provider/types";
import { useAgentInput } from "@/stores/agent-input";
import {
  useCurrentThreadStore,
  useSelectedSectionAction,
  useSubscribeToAgentRunning,
} from "@/stores/current-thread";
import { ThreadMessage } from "@/types/thread-message";
import { ChevronDown } from "@mynaui/icons-react";
import { useVirtualizer, VirtualItem } from "@tanstack/react-virtual";
import { AnimatePresence, motion } from "motion/react";
import { lazy, memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useShallow } from "zustand/react/shallow";
import { ContinueMessage } from "./continue-message";
import { Message } from "./messages";
import { WrapUpSummary } from "./messages/wrap-up-summary";
import StreamingMessage from "./streaming-message";

const SectionModalContent = lazy(
  () => import("./messages/collapsible-content/modal/section-modal"),
);

type PhaseLabel = {
  label: string;
  time: number;
};

type PhaseLabels = {
  [key: string]: PhaseLabel;
};

const duringStreamingLabels: PhaseLabels = {
  generating: { label: "generating…", time: 7000 },
  thinking: { label: "thinking…", time: 5000 },
  working: { label: "working…", time: 4000 },
  creating: { label: "creating…", time: 3000 },
  crafting: { label: "crafting…", time: 3000 },
  writing: { label: "writing…", time: 3000 },
};

const postStreamingLabels: PhaseLabels = {
  processing: { label: "processing actions…", time: 3000 },
  ready: { label: "ready to process…", time: 5000 },
  updating: { label: "updating files…", time: 3000 },
  analyzing: { label: "analyzing…", time: 2000 },
  linting: { label: "linting…", time: 5000 },
  formatting: { label: "formatting…", time: 4000 },
  refactoring: { label: "refactoring…", time: 4000 },
  optimizing: { label: "optimizing code…", time: 5000 },
  checking: { label: "checking syntax…", time: 8000 },
  reviewing: { label: "reviewing code…", time: 10000 },
  cleaning: { label: "cleaning up…", time: 5000 },
  building: { label: "building…", time: 10000 },
};

function SelectedSectionActionModal() {
  const { selectedSectionAction, setSelectedSectionAction } = useSelectedSectionAction();

  return (
    <LazyModal
      open={!!selectedSectionAction?.section || !!selectedSectionAction?.action}
      onOpenChange={(open) => {
        if (!open) setSelectedSectionAction({ section: null, action: null });
      }}
    >
      <SectionModalContent />
    </LazyModal>
  );
}

const VirtualMessageItem = memo(
  ({
    virtualItem,
    message,
    measureElement,
  }: {
    virtualItem: VirtualItem;
    message: ThreadMessage;
    measureElement: (element: Element | null) => void;
  }) => (
    <div
      data-index={virtualItem.index}
      ref={measureElement}
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        transform: `translateY(${virtualItem.start}px)`,
      }}
    >
      <Message message={message} />
    </div>
  ),
);

VirtualMessageItem.displayName = "VirtualMessageItem";

const AgentStatusMessage = () => {
  const { isSoftgenProcessing } = useCurrentThreadStore(
    useShallow((state) => ({
      isSoftgenProcessing: state.isSoftgenProcessing,
    })),
  );

  const currentPhaseLabels = isSoftgenProcessing ? postStreamingLabels : duringStreamingLabels;
  const phaseKeys = Object.keys(currentPhaseLabels);

  const [currentIndex, setCurrentIndex] = useState(0);
  const phaseState = phaseKeys[currentIndex];

  const updateBadge = useCallback(() => {
    setCurrentIndex((prevIndex: number) => (prevIndex + 1) % phaseKeys.length);
  }, [phaseKeys.length]);

  useEffect(() => {
    setCurrentIndex(0);
  }, [isSoftgenProcessing]);

  useEffect(() => {
    const timer = setInterval(updateBadge, currentPhaseLabels[phaseState]?.time || 1000);
    return () => clearInterval(timer);
  }, [updateBadge]);

  return (
    <div className="sticky top-0 flex items-center justify-start rounded-lg bg-transparent pb-8 text-sm">
      <TextShimmer className="font-medium">
        softgen is{" "}
        <AnimatePresence mode="popLayout" initial={false}>
          <motion.span
            key={`${isSoftgenProcessing}-${phaseState}`}
            transition={{
              bounce: 0,
              duration: 0.3,
              type: "spring",
            }}
            initial={{ y: 25, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 25, opacity: 0 }}
          >
            {currentPhaseLabels[phaseState]?.label || "working…"}
          </motion.span>
        </AnimatePresence>
      </TextShimmer>
    </div>
  );
};

const ScrollToBottomButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <Card
      className={cn(
        "my-1.5 flex w-fit items-center justify-center rounded-4xl bg-primary shadow-sm",
        "absolute bottom-4 left-1/2 z-50 flex -translate-x-1/2",
      )}
      border={false}
    >
      <CardContent className="flex w-full items-center justify-center gap-2 p-0 text-sm">
        <Button
          onClick={onClick}
          size="sm"
          className="flex items-center justify-center rounded-4xl text-primary-foreground transition-all"
          aria-label="Scroll to bottom"
        >
          <div className="flex items-center gap-1.5">
            <span className="text-sm font-medium text-background">Scroll</span>
            <ChevronDown className="size-5 font-medium text-background" />
          </div>
        </Button>
      </CardContent>
    </Card>
  );
};

const ThreadMessages = () => {
  const { isAgentRunning, threadMessages } = useCurrentThreadStore(
    useShallow((state) => ({
      isAgentRunning: state.isAgentRunning,
      threadMessages: state.threadMessages,
    })),
  );

  const { isLoading, sendMessage } = useThread();

  const handleContinueMessage = (mode: "creative" | "standard") => {
    sendMessage({
      content: "continue",
      options: {
        model: mode,
      },
    });
    setWrapUpSummary(null);
  };

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [userScrolledUp, setUserScrolledUp] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isScrollingProgrammaticallyRef = useRef(false);
  const [wrapUpSummary, setWrapUpSummary] = useState<WrapUpSummaryMessage["data"] | null>(null);

  const virtualizer = useVirtualizer({
    count: threadMessages.length,
    getScrollElement: () => scrollContainerRef.current,
    estimateSize: useCallback(
      (index) => {
        const message = threadMessages[index];
        if (!message) return 100;

        if (typeof message.content === "string") {
          const contentLength = message.content.length;
          return Math.max(80, Math.min(400, 80 + Math.floor(contentLength / 100) * 20));
        }

        return 120;
      },
      [threadMessages],
    ),
    overscan: 5,
    measureElement: (element) => element?.getBoundingClientRect().height,
  });

  const isNearBottom = useCallback(() => {
    if (!scrollContainerRef.current) return true;
    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
    return scrollHeight - scrollTop - clientHeight <= 30;
  }, []);

  const scrollToBottom = useCallback(() => {
    if (!scrollContainerRef.current) return;

    scrollContainerRef.current.scrollTo({
      top: scrollContainerRef.current.scrollHeight,
      behavior: "smooth",
    });

    setUserScrolledUp(false);
  }, []);

  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;

    const nearBottom = isNearBottom();

    const shouldShowButton = !nearBottom && (isAgentRunning || threadMessages.length > 0);
    setShowScrollButton(shouldShowButton);

    setUserScrolledUp(!nearBottom);
  }, [isNearBottom, isAgentRunning, threadMessages.length]);

  useEffect(() => {
    if (isAgentRunning && !userScrolledUp) {
      const interval = setInterval(() => {
        if (
          scrollContainerRef.current &&
          !userScrolledUp &&
          !isScrollingProgrammaticallyRef.current
        ) {
          isScrollingProgrammaticallyRef.current = true;
          scrollContainerRef.current.scrollTo({
            top: scrollContainerRef.current.scrollHeight,
            behavior: "auto",
          });
          setTimeout(() => {
            isScrollingProgrammaticallyRef.current = false;
          }, 50);
        }
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isAgentRunning, userScrolledUp]);

  useEffect(() => {
    if (threadMessages.length > 0 && !userScrolledUp) {
      setTimeout(() => scrollToBottom(), 50);
    }
  }, [threadMessages.length, userScrolledUp, scrollToBottom]);

  useSubscribeToAgentRunning((isRunning, wasRunning) => {
    if (isRunning && !wasRunning) {
      setUserScrolledUp(false);
      setShowScrollButton(false);
      setTimeout(() => scrollToBottom(), 100);
    }
  });

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      container.removeEventListener("scroll", handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [handleScroll]);

  const virtualItems = virtualizer.getVirtualItems();

  const handleWrapUpSummary = useCallback((message: WrapUpSummaryMessage) => {
    setWrapUpSummary(message.data);
  }, []);

  useWebSocketMessage<WrapUpSummaryMessage>("wrap_up_summary", handleWrapUpSummary);

  if (isLoading) {
    return (
      <div className="relative flex h-full flex-col">
        <div className="flex h-full items-center justify-center">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            <p className="text-sm text-muted-foreground">Loading messages...</p>
          </div>
        </div>
      </div>
    );
  }

  const handleSuggestionClick = (suggestion: string) => {
    sendMessage({
      content: suggestion,
      options: {
        model: "standard",
      },
    });
    setWrapUpSummary(null);
  };

  return (
    <div className="relative flex h-full flex-col">
      <div
        ref={scrollContainerRef}
        className="mb-0 w-full px-2 py-0 pt-2 md:pt-0"
        style={{
          height: "100%",
          overflow: "auto",
        }}
      >
        <div className="w-full py-2 md:py-0">
          {threadMessages.length === 0 && !isAgentRunning ? (
            <div className="flex flex-col items-center justify-center py-8 text-primary">
              <Typography.P className="leading-1 text-center">No messages yet.</Typography.P>
              <Typography.P className="mt-0 text-center leading-none">
                Start a conversation!
              </Typography.P>
            </div>
          ) : (
            <>
              <div
                style={{
                  height: virtualizer.getTotalSize(),
                  width: "100%",
                  position: "relative",
                }}
              >
                {virtualItems.map((virtualItem) => {
                  const message = threadMessages[virtualItem.index];
                  return (
                    <VirtualMessageItem
                      key={virtualItem.key}
                      virtualItem={virtualItem}
                      message={message}
                      measureElement={virtualizer.measureElement}
                    />
                  );
                })}
              </div>

              <StreamingMessage />

              {!wrapUpSummary && !isAgentRunning && (
                <ContinueMessageWrapper handleContinueMessage={handleContinueMessage} />
              )}

              {wrapUpSummary && !isAgentRunning && (
                <WrapUpSummary
                  wrapUpSummary={wrapUpSummary}
                  handleSuggestionClick={handleSuggestionClick}
                />
              )}
            </>
          )}
        </div>
      </div>

      {isAgentRunning && <AgentStatusMessage />}
      {showScrollButton && <ScrollToBottomButton onClick={scrollToBottom} />}

      <SelectedSectionActionModal />
    </div>
  );
};

function ContinueMessageWrapper({
  handleContinueMessage,
}: {
  handleContinueMessage: (mode: "creative" | "standard") => void;
}) {
  const { setMode } = useAgentInput();
  const { isAgentRunning, threadMessages } = useCurrentThreadStore(
    useShallow((state) => ({
      isAgentRunning: state.isAgentRunning,
      threadMessages: state.threadMessages,
    })),
  );

  const continueProps = useMemo(() => {
    if (!isAgentRunning && threadMessages.length > 0) {
      const latestMessage = threadMessages[threadMessages.length - 1];

      if (latestMessage.role === "assistant" && latestMessage.content) {
        const switchModeMatch = latestMessage.content.match(/(creative|standard) mode/i);

        if (switchModeMatch && switchModeMatch[1]) {
          const modeToSwitch = switchModeMatch[1].toLowerCase() as "creative" | "standard";
          return {
            text: `Switch to ${
              modeToSwitch.charAt(0).toUpperCase() + modeToSwitch.slice(1)
            } and Continue`,
            onContinue: () => {
              setMode(modeToSwitch);
              handleContinueMessage(modeToSwitch);
            },
          };
        }

        const showStandardContinue =
          latestMessage.content.includes("<continue>") ||
          latestMessage.content.includes("send_terminal_command: ToolResult") ||
          latestMessage.content.includes("close_files_in_editor: ToolResult");

        if (showStandardContinue) {
          return { onContinue: () => handleContinueMessage("creative") };
        }
      }
    }
    return undefined;
  }, [isAgentRunning, threadMessages, handleContinueMessage, setMode]);

  if (!continueProps) return null;

  return <ContinueMessage {...continueProps} />;
}

ThreadMessages.displayName = "ThreadMessages";

export default ThreadMessages;
