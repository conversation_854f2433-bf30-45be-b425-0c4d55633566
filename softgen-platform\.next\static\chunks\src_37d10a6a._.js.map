{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Check } from \"@mynaui/icons-react\";\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator className={cn(\"flex items-center justify-center text-current\")}>\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n));\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName;\r\n\r\nexport { Checkbox };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC,iRAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC,iRAAA,CAAA,YAA2B;YAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBACzC,cAAA,4SAAC,+SAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,iRAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit] bg-transparent ring-offset-background focus:rounded-xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n));\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName;\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" && \"h-0.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n));\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName;\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4SAAC,oRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,4SAAC,oRAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,4SAAC;;;;;0BACD,4SAAC,oRAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,oRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,4SAAC,oRAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cAAc,sDAC9B,gBAAgB,gBAAgB,wDAChC;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC,oRAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAf7C;AAkBN,UAAU,WAAW,GAAG,oRAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/utils/example-prompts.ts"], "sourcesContent": ["export const examplePrompts = [\r\n  {\r\n    title: \"Brainstorm Core App Features\",\r\n    description: \"Plan and structure your app's core features\",\r\n    prompts: [\r\n      {\r\n        title: \"Brainstorm Core App Features\",\r\n        content: `I want to build a [describe your app idea in a few words, e.g., 'task management tool for small teams,' 'recipe sharing platform,' 'local events discovery app'].\r\n  Before I start designing specific screens or functions:\r\n  1. Output a 1-100 confidence level for your understanding of the app concept.\r\n  2. Based on common patterns and user expectations for a [reiterate app type or core function] application, list 3-5 essential core features that are critical for an MVP (Minimum Viable Product).\r\n  3. For each core feature, provide a brief 1-2 sentence description of its primary purpose and the main user benefit.\r\n  4. Suggest 1-2 'next-step' or advanced features that could be considered after the MVP is established.\r\n  5. Output this as a prioritized list, with MVP features first.`,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Design Basic Data Structure\",\r\n    description: \"Plan your app's data organization\",\r\n    prompts: [\r\n      {\r\n        title: \"Design Basic Data Structure\",\r\n        content: `My application will need to store information about [list the main things, e.g., 'users, products, and orders' or 'projects and tasks'].\r\n  Before creating the database structure:\r\n  1. Output a 1-100 confidence level for the proposed data organization.\r\n  2. For each main information type I listed ([reiterate main things]), suggest a simple structure (like a table or collection) and list 3-5 key pieces of information (fields) you think it should store, along with a basic data type for each (e.g., Text, Number, Yes/No, Date).\r\n  3. If any of these information types are related (e.g., an 'order' is related to a 'user' and 'products'), briefly explain the basic relationship.\r\n  4. Await my confirmation before setting up this initial data structure.`,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Implement Simple User Signup & Login\",\r\n    description: \"Set up basic authentication\",\r\n    prompts: [\r\n      {\r\n        title: \"Implement Simple User Signup & Login\",\r\n        content: `I need a basic way for users to sign up with an email and password, and then log in to my application.\r\n  Before generating the components or logic:\r\n  1. Output a 1-100 confidence level for the proposed authentication flow.\r\n  2. Outline the necessary UI components for signup (e.g., email field, password field, signup button) and login (e.g., email field, password field, login button).\r\n  3. Describe the basic steps for:\r\n      a. User registration (including storing email and a secure password).\r\n      b. User login (checking credentials and handling incorrect attempts).\r\n  4. Briefly mention how you'll handle user sessions after login.\r\n  5. Await my review before implementation.`,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Quick Error Check\",\r\n    description: \"Quick troubleshooting for errors\",\r\n    prompts: [\r\n      {\r\n        title: \"Quick Error Check\",\r\n        content: `Something went wrong with [describe the specific action you were trying or part of the app that's not working].\r\n  Before you try to fix it automatically:\r\n  1. Output a 1-100 confidence level in identifying the likely problem.\r\n  2. Briefly state what you think the immediate error is based on the latest actions and current app state.\r\n  3. Propose one direct action you will take to attempt a fix.\r\n  4. If this first attempt doesn't resolve it, I will use a more detailed troubleshooting prompt.`,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Troubleshoot Persistent Error Loops\",\r\n    description: \"Deep dive into recurring errors\",\r\n    prompts: [\r\n      {\r\n        title: \"Troubleshoot Persistent Error Loops\",\r\n        content: `Your attempts to solve the current error seem to be stuck in a loop, focusing too narrowly on the immediate symptom without fully considering the broader context or downstream effects.\r\n  For the rest of this troubleshooting session, output a 1-100 confidence level along with all decisions and recommendations.\r\n  \r\n  Before making any more changes:\r\n  1. Review and analyze the entire recent context window and app state for the underlying blocker.\r\n  2. Output a concise debug report including:\r\n      a. A complete description of the recurring error and its context.\r\n      b. All solution paths you've attempted so far for this specific issue (include an attempt count for each to identify patterns to avoid).\r\n  3. Cross-reference this information for applicability with the current error.\r\n  4. Assess until you have a confidence level of 99%+ in the correct solution path OR clearly state why you cannot reach that confidence and what information is missing.\r\n  5. If a high-confidence solution is found, describe it before proceeding.`,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Make Page Section Mobile-Friendly\",\r\n    description: \"Improve mobile responsiveness\",\r\n    prompts: [\r\n      {\r\n        title: \"Make Page Section Mobile-Friendly\",\r\n        content: `The [describe specific section or elements on the current page, e.g., 'header,' 'product grid,' 'contact form'] doesn't look right on mobile devices.\r\n  Before applying responsive design changes:\r\n  1. Output a 1-100 confidence level that your proposed changes will improve mobile display without negatively impacting the desktop view significantly.\r\n  2. Analyze the specified section: [reiterate section].\r\n  3. Briefly describe the main issue you see on smaller screens (e.g., 'elements overlap,' 'text is too small,' 'it requires horizontal scrolling').\r\n  4. Propose 1-2 specific adjustments you will make to improve its mobile responsiveness (e.g., 'stack elements vertically,' 'reduce font size for mobile,' 'convert to a single column layout').\r\n  5. Show me the proposed changes before making them permanent if possible, or proceed if confident.`,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Help Me Clarify My Goal for the AI\",\r\n    description: \"Get better AI assistance\",\r\n    prompts: [\r\n      {\r\n        title: \"Help Me Clarify My Goal for the AI\",\r\n        content: `I want to [describe your goal, e.g., 'add a search bar,' 'change the button color,' 'connect to an external data source'], but I'm not sure how to best instruct you (Softgen AI).\r\n  Based on my goal: [reiterate goal].\r\n  1. Output a 1-100 confidence level in understanding my objective.\r\n  2. Ask me 1-3 specific questions that would help you understand exactly what I want to achieve and how it should function or look.\r\n  3. Based on my answers, suggest a more precise prompt I could use to get the best results from you.`,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Optimize My Instructions for Token Efficiency\",\r\n    description: \"Make your prompts more efficient\",\r\n    prompts: [\r\n      {\r\n        title: \"Optimize My Instructions for Token Efficiency\",\r\n        content: `Before we continue, please review my previous [3-5] instructions in this current session.\r\n  1. Output a 1-100 confidence level for your analysis.\r\n  2. Identify if any of my recent prompts could have been more concise or direct to achieve the same outcome, potentially using fewer tokens, while still being clear for Softgen AI.\r\n  3. If so, provide 1-2 examples of how I could have phrased them more efficiently.\r\n  4. Offer one general tip for me to reduce token consumption when instructing you to build or modify this application, keeping in mind Softgen's ability to take appropriate next actions once a clear goal is set.`,\r\n      },\r\n    ],\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,iBAAiB;IAC5B;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;;;gEAM8C,CAAC;YAC3D;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;;yEAKuD,CAAC;YACpE;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;;;;;2CAQyB,CAAC;YACtC;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;;iGAK+E,CAAC;YAC5F;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;;;;;;;2EAUyD,CAAC;YACtE;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;;;oGAMkF,CAAC;YAC/F;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;qGAImF,CAAC;YAChG;SACD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,OAAO;gBACP,SAAS,CAAC;;;;oNAIkM,CAAC;YAC/M;SACD;IACH;CACD", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/agent-setting-modal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/classic/dropdown-menu\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { Form, FormControl, FormField, FormItem, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { ModalContentInner, ModalHeader, ModalTitle } from \"@/components/ui/modal\";\r\nimport { ScrollArea, ScrollBar } from \"@/components/ui/scroll-area\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport {\r\n  closeFilesInEditor,\r\n  getOpenFiles,\r\n  getProjectPrompts,\r\n  openFilesInEditor,\r\n  updateCustomInstructions,\r\n  updateProjectPrompts,\r\n  updateTechStack,\r\n} from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Project, useProject } from \"@/providers/project-provider\";\r\nimport { examplePrompts } from \"@/utils/example-prompts\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport {\r\n  AcademicHatSolid,\r\n  Command,\r\n  DotsVertical,\r\n  Edit,\r\n  File,\r\n  Tool,\r\n  Trash,\r\n} from \"@mynaui/icons-react\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { X } from \"lucide-react\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { errorToast, loadingToast, successToast } from \"../global/toast\";\r\nimport SoftgenIcon from \"../thread/thread-message/softgen-icon\";\r\nimport { CACHE_PARAMS } from \"@/providers/query-provider\";\r\n\r\ntype Props = {\r\n  projectId: string;\r\n  setModal: (modal: boolean) => void;\r\n  setFormContent: (content: string) => void;\r\n  formContent: string;\r\n  availableFiles: string[];\r\n};\r\n\r\ntype SettingsTab = \"prompt\" | \"custom-knowledge\" | \"tech-stack\" | \"file-context\";\r\n\r\ntype TechStackEntry = { key: string; value: string };\r\n\r\nconst techStackEntrySchema = z.object({\r\n  key: z.string().min(1, \"Key is required\"),\r\n  value: z.string().min(1, \"Value is required\"),\r\n});\r\n\r\ntype TechStackEntryFormData = z.infer<typeof techStackEntrySchema>;\r\n\r\nconst customInstructionsFormSchema = z.object({\r\n  customInstructions: z.string(),\r\n});\r\n\r\ntype CustomInstructionsFormData = z.infer<typeof customInstructionsFormSchema>;\r\n\r\nconst promptSchema = z.object({\r\n  title: z.string().min(1, \"Title is required\"),\r\n  content: z.string().min(1, \"Content is required\"),\r\n});\r\n\r\ntype PromptFormValues = z.infer<typeof promptSchema>;\r\n\r\nconst AgentSettingModalContent = ({\r\n  projectId,\r\n  setModal,\r\n  setFormContent,\r\n  formContent,\r\n  availableFiles,\r\n}: Props) => {\r\n  const [activeTab, setActiveTab] = useState<SettingsTab>(\"prompt\");\r\n  const { project } = useProject();\r\n\r\n  const handleTabChange = (tab: SettingsTab) => {\r\n    setActiveTab(tab);\r\n  };\r\n\r\n  const tabs: { id: SettingsTab; label: string; icon: React.ReactNode; pro?: boolean }[] = [\r\n    { id: \"prompt\", label: \"Prompts\", icon: <Command /> },\r\n    { id: \"custom-knowledge\", label: \"Knowledge\", icon: <AcademicHatSolid /> },\r\n    { id: \"tech-stack\", label: \"Tech Stack\", icon: <Tool /> },\r\n    { id: \"file-context\", label: \"File Context\", icon: <File /> },\r\n  ];\r\n\r\n  const handleUsePrompt = (prompt: { key: string; value: string }) => {\r\n    const content = `${formContent}\\n\\n${prompt.key}\\n${prompt.value}`;\r\n\r\n    setFormContent(content);\r\n  };\r\n\r\n  const handleUseExamplePrompt = (prompt: { title: string; content: string }) => {\r\n    const rawContent = `${formContent}\\n\\n${prompt.title}\\n${prompt.content}`;\r\n\r\n    setFormContent(rawContent);\r\n    setModal(false);\r\n  };\r\n\r\n  return (\r\n    <ModalContentInner className=\"w-full lg:max-w-5xl\">\r\n      <div className=\"grid h-[600px] grid-cols-1 md:grid-cols-12\">\r\n        <div className=\"col-span-1 hidden flex-col border-r border-ring/15 bg-sidebar-ring/10 px-3 md:col-span-3 md:flex\">\r\n          <div className=\"flex flex-col gap-4 space-y-2 px-2 py-5 text-left\">\r\n            <SoftgenIcon />\r\n          </div>\r\n\r\n          <div className=\"flex flex-1 flex-col gap-1\">\r\n            {tabs\r\n              .sort((a, b) => (a.pro ? 1 : b.pro ? -1 : 0))\r\n              .map((tab) => (\r\n                <Button\r\n                  key={tab.id}\r\n                  variant={activeTab === tab.id ? \"default\" : \"ghost\"}\r\n                  className=\"h-fit w-full justify-between rounded-md px-3 py-2\"\r\n                  onClick={() => handleTabChange(tab.id)}\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    {tab.icon}\r\n                    {tab.id === \"prompt\" ? \"Prompts\" : tab.label}\r\n                  </div>\r\n                  {tab.pro && (\r\n                    <Badge variant=\"terminal\" className=\"px-2 py-0.5\">\r\n                      Pro\r\n                    </Badge>\r\n                  )}\r\n                </Button>\r\n              ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"col-span-1 w-full md:col-span-9\">\r\n          <ModalHeader>\r\n            <ModalTitle>\r\n              {tabs.find((tab) => tab.id === activeTab)?.label ||\r\n                activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}\r\n            </ModalTitle>\r\n          </ModalHeader>\r\n\r\n          <ScrollArea>\r\n            <div className=\"flex flex-row gap-2 overflow-x-auto p-6 py-3 md:hidden\">\r\n              {tabs\r\n                .sort((a, b) => (a.pro ? 1 : b.pro ? -1 : 0))\r\n                .map((tab) => (\r\n                  <Button\r\n                    key={tab.id}\r\n                    variant={activeTab === tab.id ? \"default\" : \"ghost\"}\r\n                    className=\"h-fit justify-between whitespace-nowrap rounded-md px-3 py-2\"\r\n                    onClick={() => {\r\n                      handleTabChange(tab.id);\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {tab.icon}\r\n                      {tab.label}\r\n                    </div>\r\n                  </Button>\r\n                ))}\r\n            </div>\r\n            <ScrollBar orientation=\"horizontal\" />\r\n          </ScrollArea>\r\n\r\n          <div className=\"col-span-1 md:col-span-9\">\r\n            <div className=\"p-4 px-6\">\r\n              {activeTab === \"custom-knowledge\" && (\r\n                <SettingsCustomKnowledge\r\n                  project={project || ({} as Project)}\r\n                  projectId={projectId}\r\n                  setModal={setModal}\r\n                />\r\n              )}\r\n              {activeTab === \"tech-stack\" && <SettingsTechStack projectId={projectId} />}\r\n              {activeTab === \"prompt\" && (\r\n                <SettingsPrompt\r\n                  projectId={projectId}\r\n                  handleUsePrompt={handleUsePrompt}\r\n                  handleUseExamplePrompt={handleUseExamplePrompt}\r\n                />\r\n              )}\r\n              {activeTab === \"file-context\" && (\r\n                <SettingsFileContext projectId={projectId} availableFiles={availableFiles} />\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ModalContentInner>\r\n  );\r\n};\r\n\r\nconst SettingsCustomKnowledge = ({\r\n  project,\r\n  projectId,\r\n  setModal,\r\n}: {\r\n  project: Project;\r\n  projectId: string;\r\n  setModal: (modal: boolean) => void;\r\n}) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  const customInstructionsForm = useForm<CustomInstructionsFormData>({\r\n    resolver: zodResolver(customInstructionsFormSchema),\r\n    defaultValues: {\r\n      customInstructions: project.custom_instructions || \"\",\r\n    },\r\n  });\r\n\r\n  const updateCustomInstructionsMutation = useMutation({\r\n    mutationFn: (customInstructions: string) =>\r\n      updateCustomInstructions(projectId, customInstructions),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"get-project\", projectId] });\r\n      successToast(\"Custom instructions updated successfully\");\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error updating custom instructions:\", error);\r\n      errorToast(\"Failed to update custom instructions. Please try again.\");\r\n    },\r\n  });\r\n\r\n  const onSubmitCustomInstructions = async (data: CustomInstructionsFormData) => {\r\n    try {\r\n      await updateCustomInstructionsMutation.mutateAsync(data.customInstructions);\r\n      customInstructionsForm.reset(data);\r\n    } catch (error) {\r\n      console.error(\"Error updating custom instructions:\", error);\r\n      errorToast(\"Failed to update custom instructions. Please try again.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Form {...customInstructionsForm}>\r\n      <form\r\n        onSubmit={customInstructionsForm.handleSubmit(onSubmitCustomInstructions)}\r\n        className=\"flex flex-col gap-2\"\r\n      >\r\n        <FormField\r\n          control={customInstructionsForm.control}\r\n          name=\"customInstructions\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-4\">\r\n              <div className=\"flex flex-col gap-2\">\r\n                <div className=\"flex flex-col gap-2\">\r\n                  <Typography.H5>Best Practices</Typography.H5>\r\n                  <Typography.P>\r\n                    Add any specific instructions, conventions, or knowledge that the AI should\r\n                    consider when working with your project.\r\n                  </Typography.P>\r\n                </div>\r\n                <FormControl>\r\n                  {/* <Textarea\r\n                    {...field}\r\n                    defaultValue={project.custom_instructions || \"\"}\r\n                    placeholder=\"Add custom knowledge or instructions...\"\r\n                    className=\"h-[25rem]\"\r\n                  /> */}\r\n                  <textarea\r\n                    id=\"custom-instructions\"\r\n                    value={field.value}\r\n                    onChange={(e) => field.onChange(e.target.value)}\r\n                    className={cn(\r\n                      \"h-[20rem] w-full resize-none rounded-md border-0 bg-muted p-4 text-sm text-primary\",\r\n                      \"ring-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0\",\r\n                    )}\r\n                    placeholder={`Add custom knowledge or instructions...`}\r\n                  />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </div>\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        <div className=\"flex w-full items-center justify-end\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button type=\"button\" variant=\"ghost\" onClick={() => setModal(false)}>\r\n              Cancel\r\n            </Button>\r\n            <Button type=\"submit\" className=\"flex h-8 items-center gap-2\">\r\n              {updateCustomInstructionsMutation.isPending ? (\r\n                <Loading className=\"size-4 animate-spin text-background\" />\r\n              ) : null}\r\n              Save Changes\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst SettingsTechStack = ({ projectId }: { projectId: string }) => {\r\n  const queryClient = useQueryClient();\r\n  const { project, refetch } = useProject();\r\n  const techStackEntries: TechStackEntry[] = (() => {\r\n    if (!project?.tech_stack_prompt) return [];\r\n\r\n    if (typeof project.tech_stack_prompt === \"string\") {\r\n      try {\r\n        const parsed = JSON.parse(project.tech_stack_prompt);\r\n        return Array.isArray(parsed) ? parsed : [];\r\n      } catch (e) {\r\n        console.error(\"Error parsing tech stack prompt:\", e);\r\n        return [];\r\n      }\r\n    }\r\n\r\n    if (Array.isArray(project.tech_stack_prompt)) {\r\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n      return project.tech_stack_prompt.map((item: any) => {\r\n        if (typeof item === \"string\") {\r\n          return { key: item, value: item };\r\n        }\r\n        return item as TechStackEntry;\r\n      });\r\n    }\r\n\r\n    return [];\r\n  })();\r\n\r\n  const techStackForm = useForm<TechStackEntryFormData>({\r\n    resolver: zodResolver(techStackEntrySchema),\r\n    defaultValues: {\r\n      key: \"\",\r\n      value: \"\",\r\n    },\r\n  });\r\n\r\n  const onSubmit = async (data: TechStackEntryFormData) => {\r\n    const newEntries = [...techStackEntries, { key: data.key, value: data.value }];\r\n\r\n    loadingToast(\r\n      \"Adding\",\r\n      updateTechStack(projectId, newEntries).then(() => {\r\n        queryClient.invalidateQueries({ queryKey: [\"get-project\", projectId] });\r\n        refetch();\r\n        techStackForm.reset();\r\n        return { message: \"Tech stack updated successfully\" };\r\n      }),\r\n    );\r\n  };\r\n\r\n  const handleRemoveTechStackEntry = (indexToRemove: number) => {\r\n    const newEntries = techStackEntries.filter((_, index) => index !== indexToRemove);\r\n\r\n    loadingToast(\r\n      \"Removing tech stack entry\",\r\n      updateTechStack(projectId, newEntries).then(() => {\r\n        queryClient.invalidateQueries({ queryKey: [\"get-project\", projectId] });\r\n        refetch();\r\n        return { message: \"Tech stack entry removed successfully\" };\r\n      }),\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Form {...techStackForm}>\r\n      <div className=\"flex flex-col gap-4\">\r\n        <form onSubmit={techStackForm.handleSubmit(onSubmit)} className=\"flex flex-col gap-4\">\r\n          <FormField\r\n            control={techStackForm.control}\r\n            name=\"key\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <Typography.H5>Name</Typography.H5>\r\n                <FormControl>\r\n                  <Input placeholder=\"Enter tech stack name\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          <FormField\r\n            control={techStackForm.control}\r\n            name=\"value\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <Typography.H5>Description</Typography.H5>\r\n                <FormControl>\r\n                  <Textarea placeholder=\"Enter tech stack description\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          <Button type=\"submit\">Add</Button>\r\n        </form>\r\n        <div className=\"mt-6\">\r\n          <h3 className=\"mb-2 font-semibold\">Current Tech Stack</h3>\r\n          <div className=\"grid grid-cols-1 gap-2 md:grid-cols-2\">\r\n            {techStackEntries.map((entry, index) => (\r\n              <div\r\n                key={index}\r\n                className={cn(\r\n                  \"group flex flex-col justify-between gap-1 rounded-xl bg-sidebar-ring/10 p-3 px-4\",\r\n                )}\r\n              >\r\n                <div className=\"flex w-full items-center justify-between gap-2\">\r\n                  <Label className=\"text-base font-semibold\">{entry.key}</Label>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Button\r\n                      variant=\"destructive\"\r\n                      size=\"icon\"\r\n                      className=\"m-0 hidden h-6 w-6 transition-all duration-700 ease-in group-hover:flex\"\r\n                      onClick={() => handleRemoveTechStackEntry(index)}\r\n                    >\r\n                      <Trash />\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-sm\">{entry.value}</div>\r\n              </div>\r\n            ))}\r\n            {techStackEntries.length === 0 && (\r\n              <div className=\"col-span-2 rounded-xl bg-sidebar-ring/10 p-3 px-4 text-sm text-sidebar-foreground\">\r\n                No tech stack entries yet. Add some above.\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Form>\r\n  );\r\n};\r\n\r\nconst SettingsPrompt = ({\r\n  projectId,\r\n  handleUsePrompt,\r\n  handleUseExamplePrompt,\r\n}: {\r\n  projectId: string;\r\n  handleUsePrompt: (prompt: { key: string; value: string }) => void;\r\n  handleUseExamplePrompt: (prompt: { title: string; content: string }) => void;\r\n}) => {\r\n  const [editingPromptIndex, setEditingPromptIndex] = useState<number>(-1);\r\n\r\n  const form = useForm<PromptFormValues>({\r\n    resolver: zodResolver(promptSchema),\r\n    defaultValues: {\r\n      title: \"\",\r\n      content: \"\",\r\n    },\r\n  });\r\n\r\n  const {\r\n    data: projectPrompts,\r\n    isLoading: isLoadingPrompts,\r\n    refetch: refetchPrompts,\r\n  } = useQuery<{\r\n    project_id: string;\r\n    prompts: Array<{ key: string; value: string }>;\r\n  }>({\r\n    queryKey: [\"prompts\", projectId],\r\n    queryFn: async () => {\r\n      const prompts = await getProjectPrompts(projectId);\r\n      return prompts;\r\n    },\r\n    ...CACHE_PARAMS\r\n  });\r\n\r\n  const addPromptMutation = useMutation({\r\n    mutationFn: async (\r\n      values: PromptFormValues,\r\n    ): Promise<{\r\n      message: string;\r\n      project_id: string;\r\n      prompts: Array<{ key: string; value: string }>;\r\n    }> => {\r\n      const newPrompt = { key: values.title, value: values.content };\r\n      const currentPrompts = projectPrompts?.prompts || [];\r\n      const updatedPrompts = [...currentPrompts, newPrompt];\r\n      const apiPrompts = updatedPrompts.map((p) => ({ key: p.key, value: p.value }));\r\n      return await updateProjectPrompts(projectId, apiPrompts);\r\n    },\r\n    onSuccess: () => {\r\n      refetchPrompts();\r\n      form.reset();\r\n    },\r\n  });\r\n\r\n  const updatePromptMutation = useMutation({\r\n    mutationFn: async (values: PromptFormValues & { index: number }) => {\r\n      const updatedPrompts = [...(projectPrompts?.prompts || [])];\r\n      updatedPrompts[values.index] = { key: values.title, value: values.content };\r\n      const apiPrompts = updatedPrompts.map((p) => ({ key: p.key, value: p.value }));\r\n      return await updateProjectPrompts(projectId, apiPrompts);\r\n    },\r\n    onSuccess: () => {\r\n      refetchPrompts();\r\n      form.reset();\r\n      setEditingPromptIndex(-1);\r\n    },\r\n  });\r\n\r\n  const deletePromptMutation = useMutation({\r\n    mutationFn: async (index: number) => {\r\n      const apiPrompts = projectPrompts?.prompts\r\n        .filter((_, i) => i !== index)\r\n        .map((p) => ({ key: p.key, value: p.value }));\r\n      if (!apiPrompts) return;\r\n      return await updateProjectPrompts(projectId, apiPrompts);\r\n    },\r\n    onSuccess: () => {\r\n      refetchPrompts();\r\n    },\r\n  });\r\n\r\n  const onSubmit = (values: PromptFormValues) => {\r\n    if (editingPromptIndex >= 0) {\r\n      updatePromptMutation.mutate({ ...values, index: editingPromptIndex });\r\n    } else {\r\n      addPromptMutation.mutate(values);\r\n    }\r\n  };\r\n\r\n  const handleEditPrompt = (index: number) => {\r\n    const prompt = projectPrompts?.prompts[index];\r\n    form.setValue(\"title\", prompt?.key || \"\");\r\n    form.setValue(\"content\", prompt?.value || \"\");\r\n    setEditingPromptIndex(index);\r\n  };\r\n\r\n  const handleDeletePrompt = (index: number) => {\r\n    loadingToast(\r\n      \"Deleting prompt\",\r\n      deletePromptMutation.mutateAsync(index).then(() => {\r\n        refetchPrompts();\r\n        return { message: \"Prompt deleted successfully\" };\r\n      }),\r\n      {},\r\n      \"Prompt deleted\",\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"space-y-8\">\r\n        <Form {...form}>\r\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"grid gap-4\">\r\n            <FormField\r\n              control={form.control}\r\n              name=\"title\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <Typography.H5>Title</Typography.H5>\r\n                  <FormControl>\r\n                    <Input placeholder=\"Prompt Title\" className=\"h-10\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            <FormField\r\n              control={form.control}\r\n              name=\"content\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <Typography.H5>Content</Typography.H5>\r\n                  <FormControl>\r\n                    <Textarea placeholder=\"Prompt Content\" className=\"min-h-[100px]\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={addPromptMutation.isPending || updatePromptMutation.isPending}\r\n              className=\"w-full\"\r\n            >\r\n              {addPromptMutation.isPending || updatePromptMutation.isPending ? (\r\n                <>\r\n                  <Loading className=\"size-4 text-background\" />\r\n                  {editingPromptIndex >= 0 ? \"Updating...\" : \"Adding...\"}\r\n                </>\r\n              ) : editingPromptIndex >= 0 ? (\r\n                \"Update Prompt\"\r\n              ) : (\r\n                \"Add Prompt\"\r\n              )}\r\n            </Button>\r\n          </form>\r\n        </Form>\r\n\r\n        <Tabs defaultValue=\"prompts\">\r\n          <TabsList>\r\n            <TabsTrigger value=\"prompts\">Prompts</TabsTrigger>\r\n            <TabsTrigger value=\"example-prompts\">Example Prompts</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"prompts\">\r\n            <div className=\"space-y-4\">\r\n              <h3 className=\"mb-2 font-semibold\">Your Prompts</h3>\r\n              {isLoadingPrompts ? (\r\n                <div className=\"flex items-center justify-center py-4\">\r\n                  <Loading className=\"size-6\" />\r\n                </div>\r\n              ) : projectPrompts?.prompts.length === 0 ? (\r\n                <div className=\"rounded-xl bg-sidebar-ring/10 p-3 px-4 text-sm text-sidebar-foreground\">\r\n                  No custom prompts found. Add some above.\r\n                </div>\r\n              ) : (\r\n                <div className=\"relative columns-1 gap-3 space-y-3 md:columns-2\">\r\n                  {projectPrompts?.prompts.map((prompt, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className={cn(\r\n                        \"group flex flex-col justify-between gap-1 rounded-xl bg-sidebar-ring/10 p-3 px-4\",\r\n                        \"h-fit w-full break-inside-avoid\",\r\n                      )}\r\n                    >\r\n                      <div className=\"flex w-full items-center justify-between gap-2\">\r\n                        <Label className=\"text-base font-semibold\">{prompt.key}</Label>\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <DropdownMenu>\r\n                            <DropdownMenuTrigger asChild>\r\n                              <Button\r\n                                variant=\"ghost\"\r\n                                size=\"icon\"\r\n                                className={cn(\r\n                                  \"group flex cursor-pointer items-center justify-start gap-3 rounded-lg px-2.5 text-sm text-primary transition-all duration-500 hover:bg-sidebar-accent/80\",\r\n                                )}\r\n                              >\r\n                                <DotsVertical />\r\n                              </Button>\r\n                            </DropdownMenuTrigger>\r\n                            <DropdownMenuContent align=\"end\">\r\n                              <DropdownMenuItem\r\n                                onClick={() => handleUsePrompt(prompt)}\r\n                                className=\"flex items-center justify-between gap-3\"\r\n                              >\r\n                                Use\r\n                                <Tool className=\"size-4\" strokeWidth={2} />\r\n                              </DropdownMenuItem>\r\n                              <DropdownMenuItem\r\n                                onClick={() => handleEditPrompt(index)}\r\n                                className=\"flex items-center justify-between gap-3\"\r\n                              >\r\n                                Edit\r\n                                <Edit className=\"size-4\" strokeWidth={2} />\r\n                              </DropdownMenuItem>\r\n                              <DropdownMenuItem\r\n                                onClick={() => handleDeletePrompt(index)}\r\n                                className=\"flex items-center justify-between gap-3\"\r\n                              >\r\n                                Delete\r\n                                <Trash className=\"size-4\" strokeWidth={2} />\r\n                              </DropdownMenuItem>\r\n                            </DropdownMenuContent>\r\n                          </DropdownMenu>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"text-sm\">{prompt.value}</div>\r\n                    </div>\r\n                  ))}\r\n                  {projectPrompts?.prompts.length === 0 && (\r\n                    <div className=\"col-span-2 rounded-xl bg-sidebar-ring/10 p-3 px-4 text-sm text-sidebar-foreground\">\r\n                      No custom prompts found. Add some above.\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"example-prompts\">\r\n            <div className=\"space-y-4\">\r\n              <h3 className=\"font-semibold\">Example Prompts</h3>\r\n              <div className=\"grid grid-cols-1 gap-2 md:grid-cols-2\">\r\n                {examplePrompts.map((category, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"flex flex-col items-start justify-start gap-3 rounded-xl border bg-sidebar-ring/10 p-3 px-4\"\r\n                  >\r\n                    <div className=\"flex flex-col gap-1\">\r\n                      <Label className=\"text-base font-medium\">{category.title}</Label>\r\n                      <p className=\"text-sm text-muted-foreground\">{category.description}</p>\r\n                    </div>\r\n                    <div className=\"flex flex-col gap-1\">\r\n                      {category.prompts.map((prompt, promptIndex) => (\r\n                        <Button\r\n                          key={promptIndex}\r\n                          size=\"sm\"\r\n                          className=\"w-full md:w-fit\"\r\n                          onClick={() => handleUseExamplePrompt(prompt)}\r\n                        >\r\n                          Try out\r\n                        </Button>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst SettingsFileContext = ({\r\n  projectId,\r\n  availableFiles,\r\n}: {\r\n  projectId: string;\r\n  availableFiles: string[];\r\n}) => {\r\n  // State declarations\r\n  const [openFiles, setOpenFiles] = useState<string[]>([]);\r\n  const [isLoadingFiles, setIsLoadingFiles] = useState(true);\r\n  const [selectedFilesToClose, setSelectedFilesToClose] = useState<string[]>([]);\r\n  const [selectedFilesToOpen, setSelectedFilesToOpen] = useState<string[]>([]);\r\n  const [isClosingFiles, setIsClosingFiles] = useState(false);\r\n  const [isOpeningFiles, setIsOpeningFiles] = useState(false);\r\n  const [fileContextSearchQuery, setFileContextSearchQuery] = useState(\"\");\r\n  const [fileContextFilteredFiles, setFileContextFilteredFiles] = useState<string[]>([]);\r\n  const [fileContextShowSelector, setFileContextShowSelector] = useState(false);\r\n  const [fileContextSelectedIndex, setFileContextSelectedIndex] = useState(0);\r\n\r\n  // Function declarations\r\n  const fetchOpenFiles = useCallback(async () => {\r\n    try {\r\n      setIsLoadingFiles(true);\r\n      const response = await getOpenFiles(projectId);\r\n      if (response?.data) {\r\n        setOpenFiles(\r\n          response.data.map((file: { path: string } | string) =>\r\n            typeof file === \"string\" ? file : file.path,\r\n          ),\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching open files:\", error);\r\n      errorToast(\"Failed to fetch open files\");\r\n    } finally {\r\n      setIsLoadingFiles(false);\r\n    }\r\n  }, [projectId]);\r\n\r\n  const handleFileContextInput = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setFileContextSearchQuery(value);\r\n\r\n    if (value.trim()) {\r\n      const filtered = availableFiles.filter((file) =>\r\n        file.toLowerCase().includes(value.toLowerCase()),\r\n      );\r\n      setFileContextFilteredFiles(filtered);\r\n      setFileContextShowSelector(true);\r\n      setFileContextSelectedIndex(0);\r\n    } else {\r\n      setFileContextShowSelector(false);\r\n    }\r\n  };\r\n\r\n  const handleFileContextKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (fileContextShowSelector && fileContextFilteredFiles.length > 0) {\r\n      switch (e.key) {\r\n        case \"ArrowDown\":\r\n          e.preventDefault();\r\n          setFileContextSelectedIndex((prev) =>\r\n            prev < fileContextFilteredFiles.length - 1 ? prev + 1 : prev,\r\n          );\r\n          break;\r\n        case \"ArrowUp\":\r\n          e.preventDefault();\r\n          setFileContextSelectedIndex((prev) => (prev > 0 ? prev - 1 : prev));\r\n          break;\r\n        case \"Enter\":\r\n          e.preventDefault();\r\n          const selectedFile = fileContextFilteredFiles[fileContextSelectedIndex];\r\n          setSelectedFilesToOpen((prev) => [...prev, selectedFile]);\r\n          setFileContextSearchQuery(\"\");\r\n          setFileContextShowSelector(false);\r\n          break;\r\n        case \"Escape\":\r\n          e.preventDefault();\r\n          setFileContextShowSelector(false);\r\n          break;\r\n        case \"Tab\":\r\n          e.preventDefault();\r\n          const tabSelectedFile = fileContextFilteredFiles[fileContextSelectedIndex];\r\n          setSelectedFilesToOpen((prev) => [...prev, tabSelectedFile]);\r\n          setFileContextSearchQuery(\"\");\r\n          setFileContextShowSelector(false);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleCloseFiles = async () => {\r\n    if (selectedFilesToClose.length === 0) return;\r\n\r\n    try {\r\n      setIsClosingFiles(true);\r\n      await closeFilesInEditor(projectId, selectedFilesToClose);\r\n      await fetchOpenFiles();\r\n      setSelectedFilesToClose([]);\r\n      successToast(\"Files closed successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error closing files:\", error);\r\n      errorToast(\"Failed to close files\");\r\n    } finally {\r\n      setIsClosingFiles(false);\r\n    }\r\n  };\r\n\r\n  const handleOpenFiles = async () => {\r\n    if (selectedFilesToOpen.length === 0) return;\r\n\r\n    try {\r\n      setIsOpeningFiles(true);\r\n      await openFilesInEditor(projectId, selectedFilesToOpen);\r\n      await fetchOpenFiles();\r\n      setSelectedFilesToOpen([]);\r\n      successToast(\"Files opened successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error opening files:\", error);\r\n      errorToast(\"Failed to open files\");\r\n    } finally {\r\n      setIsOpeningFiles(false);\r\n    }\r\n  };\r\n\r\n  // Effects\r\n  useEffect(() => {\r\n    if (projectId) {\r\n      fetchOpenFiles();\r\n    }\r\n  }, [projectId, fetchOpenFiles]);\r\n\r\n  // Render\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"mb-6 flex items-center justify-between\">\r\n        <h3 className=\"text-lg font-semibold\">Open Files</h3>\r\n        <div className=\"flex gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleCloseFiles}\r\n            disabled={selectedFilesToClose.length === 0 || isClosingFiles}\r\n            className=\"h-9 px-4\"\r\n          >\r\n            {isClosingFiles ? (\r\n              <>\r\n                <Loading className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                Closing...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <X className=\"mr-2 h-4 w-4\" />\r\n                Close Selected\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {isLoadingFiles ? (\r\n        <div className=\"flex items-center justify-center py-4\">\r\n          <Loading className=\"h-6 w-6 animate-spin\" />\r\n        </div>\r\n      ) : openFiles.length === 0 ? (\r\n        <p className=\"text-sm text-muted-foreground\">No files currently open</p>\r\n      ) : (\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center gap-2 rounded-t-lg border-b bg-muted/30 px-3 py-2\">\r\n            <Checkbox\r\n              checked={selectedFilesToClose.length === openFiles.length && openFiles.length > 0}\r\n              onCheckedChange={(checked) => {\r\n                if (checked) {\r\n                  setSelectedFilesToClose(openFiles);\r\n                } else {\r\n                  setSelectedFilesToClose([]);\r\n                }\r\n              }}\r\n              aria-label=\"Select all files\"\r\n            />\r\n            <span className=\"text-xs font-medium\">Select All</span>\r\n          </div>\r\n          {openFiles.map((file, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"flex items-center justify-between rounded-lg border bg-card p-3\"\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <Checkbox\r\n                  checked={selectedFilesToClose.includes(file)}\r\n                  onCheckedChange={(checked) => {\r\n                    if (checked) {\r\n                      setSelectedFilesToClose((prev) => [...prev, file]);\r\n                    } else {\r\n                      setSelectedFilesToClose((prev) => prev.filter((f) => f !== file));\r\n                    }\r\n                  }}\r\n                  aria-label={`Select file ${file}`}\r\n                />\r\n                <File className=\"h-4 w-4 text-yellow-500\" />\r\n                <span className=\"text-sm\">{file}</span>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"mt-6\">\r\n        <h4 className=\"mb-4 text-sm font-medium\">Open New Files</h4>\r\n        <div className=\"space-y-4\">\r\n          <div className=\"relative\">\r\n            <Input\r\n              placeholder=\"Type to search files...\"\r\n              value={fileContextSearchQuery}\r\n              onChange={handleFileContextInput}\r\n              onKeyDown={handleFileContextKeyDown}\r\n              className=\"h-10\"\r\n            />\r\n            {fileContextShowSelector && fileContextFilteredFiles.length > 0 && (\r\n              <div className=\"absolute z-20 mt-1 max-h-40 w-full overflow-y-auto rounded-md border bg-background shadow-lg\">\r\n                {fileContextFilteredFiles.map((file, index) => (\r\n                  <div\r\n                    key={file}\r\n                    className={cn(\r\n                      \"flex cursor-pointer items-center gap-2 px-3 py-2 text-sm hover:bg-secondary/50\",\r\n                      fileContextSelectedIndex === index && \"bg-secondary\",\r\n                    )}\r\n                    onClick={() => {\r\n                      setSelectedFilesToOpen((prev) => [...prev, file]);\r\n                      setFileContextSearchQuery(\"\");\r\n                      setFileContextShowSelector(false);\r\n                    }}\r\n                  >\r\n                    <File className=\"h-4 w-4 text-yellow-500\" />\r\n                    <div className=\"flex flex-col\">\r\n                      <span className=\"truncate font-medium\">{file.split(\"/\").pop()}</span>\r\n                      <span className=\"truncate text-xs text-muted-foreground\">{file}</span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {selectedFilesToOpen.map((file, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex items-center gap-1 rounded-md bg-secondary/20 px-2 py-1 text-xs\"\r\n              >\r\n                <File className=\"h-3 w-3 text-yellow-500\" />\r\n                <span>{file}</span>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() =>\r\n                    setSelectedFilesToOpen((prev) => prev.filter((_, i) => i !== index))\r\n                  }\r\n                  className=\"ml-1 hover:text-destructive\"\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </button>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <Button\r\n            onClick={handleOpenFiles}\r\n            disabled={selectedFilesToOpen.length === 0 || isOpeningFiles}\r\n            className=\"w-full\"\r\n          >\r\n            {isOpeningFiles ? (\r\n              <>\r\n                <Loading className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                Opening...\r\n              </>\r\n            ) : (\r\n              \"Open Files\"\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AgentSettingModalContent;\r\n"], "names": [], "mappings": ";;;;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAjDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA,MAAM,uBAAuB,sOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,KAAK,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACvB,OAAO,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC3B;AAIA,MAAM,+BAA+B,sOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,oBAAoB,sOAAA,CAAA,IAAC,CAAC,MAAM;AAC9B;AAIA,MAAM,eAAe,sOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,SAAS,sOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAIA,MAAM,2BAA2B,CAAC,EAChC,SAAS,EACT,QAAQ,EACR,cAAc,EACd,WAAW,EACX,cAAc,EACR;;IACN,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IAE7B,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,MAAM,OAAmF;QACvF;YAAE,IAAI;YAAU,OAAO;YAAW,oBAAM,4SAAC,mTAAA,CAAA,UAAO;;;;;QAAI;QACpD;YAAE,IAAI;YAAoB,OAAO;YAAa,oBAAM,4SAAC,8UAAA,CAAA,mBAAgB;;;;;QAAI;QACzE;YAAE,IAAI;YAAc,OAAO;YAAc,oBAAM,4SAAC,6SAAA,CAAA,OAAI;;;;;QAAI;QACxD;YAAE,IAAI;YAAgB,OAAO;YAAgB,oBAAM,4SAAC,6SAAA,CAAA,OAAI;;;;;QAAI;KAC7D;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,GAAG,YAAY,IAAI,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE,OAAO,KAAK,EAAE;QAElE,eAAe;IACjB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,aAAa,GAAG,YAAY,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,OAAO,EAAE;QAEzE,eAAe;QACf,SAAS;IACX;IAEA,qBACE,4SAAC,oIAAA,CAAA,oBAAiB;QAAC,WAAU;kBAC3B,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC,qKAAA,CAAA,UAAW;;;;;;;;;;sCAGd,4SAAC;4BAAI,WAAU;sCACZ,KACE,IAAI,CAAC,CAAC,GAAG,IAAO,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,GACzC,GAAG,CAAC,CAAC,oBACJ,4SAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,cAAc,IAAI,EAAE,GAAG,YAAY;oCAC5C,WAAU;oCACV,SAAS,IAAM,gBAAgB,IAAI,EAAE;;sDAErC,4SAAC;4CAAI,WAAU;;gDACZ,IAAI,IAAI;gDACR,IAAI,EAAE,KAAK,WAAW,YAAY,IAAI,KAAK;;;;;;;wCAE7C,IAAI,GAAG,kBACN,4SAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAW,WAAU;sDAAc;;;;;;;mCAV/C,IAAI,EAAE;;;;;;;;;;;;;;;;8BAmBrB,4SAAC;oBAAI,WAAU;;sCACb,4SAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,4SAAC,oIAAA,CAAA,aAAU;0CACR,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,YAAY,SACzC,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;;;;;;;;;;;sCAI1D,4SAAC,6IAAA,CAAA,aAAU;;8CACT,4SAAC;oCAAI,WAAU;8CACZ,KACE,IAAI,CAAC,CAAC,GAAG,IAAO,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,GACzC,GAAG,CAAC,CAAC,oBACJ,4SAAC,qIAAA,CAAA,SAAM;4CAEL,SAAS,cAAc,IAAI,EAAE,GAAG,YAAY;4CAC5C,WAAU;4CACV,SAAS;gDACP,gBAAgB,IAAI,EAAE;4CACxB;sDAEA,cAAA,4SAAC;gDAAI,WAAU;;oDACZ,IAAI,IAAI;oDACR,IAAI,KAAK;;;;;;;2CATP,IAAI,EAAE;;;;;;;;;;8CAcnB,4SAAC,6IAAA,CAAA,YAAS;oCAAC,aAAY;;;;;;;;;;;;sCAGzB,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;oCACZ,cAAc,oCACb,4SAAC;wCACC,SAAS,WAAY,CAAC;wCACtB,WAAW;wCACX,UAAU;;;;;;oCAGb,cAAc,8BAAgB,4SAAC;wCAAkB,WAAW;;;;;;oCAC5D,cAAc,0BACb,4SAAC;wCACC,WAAW;wCACX,iBAAiB;wCACjB,wBAAwB;;;;;;oCAG3B,cAAc,gCACb,4SAAC;wCAAoB,WAAW;wCAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E;GA3HM;;QAQgB,2IAAA,CAAA,aAAU;;;KAR1B;AA6HN,MAAM,0BAA0B,CAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EAKT;;IACC,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,yBAAyB,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAA8B;QACjE,UAAU,CAAA,GAAA,2QAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,oBAAoB,QAAQ,mBAAmB,IAAI;QACrD;IACF;IAEA,MAAM,mCAAmC,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACnD,UAAU;qFAAE,CAAC,qBACX,CAAA,GAAA,oHAAA,CAAA,2BAAwB,AAAD,EAAE,WAAW;;QACtC,SAAS;qFAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAe;qBAAU;gBAAC;gBACrE,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE;YACf;;QACA,OAAO;qFAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;YACb;;IACF;IAEA,MAAM,6BAA6B,OAAO;QACxC,IAAI;YACF,MAAM,iCAAiC,WAAW,CAAC,KAAK,kBAAkB;YAC1E,uBAAuB,KAAK,CAAC;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,qBACE,4SAAC,mIAAA,CAAA,OAAI;QAAE,GAAG,sBAAsB;kBAC9B,cAAA,4SAAC;YACC,UAAU,uBAAuB,YAAY,CAAC;YAC9C,WAAU;;8BAEV,4SAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,uBAAuB,OAAO;oBACvC,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;sCAClB,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,yIAAA,CAAA,UAAU,CAAC,EAAE;0DAAC;;;;;;0DACf,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;0DAAC;;;;;;;;;;;;kDAKhB,4SAAC,mIAAA,CAAA,cAAW;kDAOV,cAAA,4SAAC;4CACC,IAAG;4CACH,OAAO,MAAM,KAAK;4CAClB,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA;4CAEF,aAAa,CAAC,uCAAuC,CAAC;;;;;;;;;;;kDAG1D,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;8BAMpB,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;gCAAQ,SAAS,IAAM,SAAS;0CAAQ;;;;;;0CAGtE,4SAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,WAAU;;oCAC7B,iCAAiC,SAAS,iBACzC,4SAAC,sIAAA,CAAA,UAAO;wCAAC,WAAU;;;;;+CACjB;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvB;IApGM;;QASgB,yRAAA,CAAA,iBAAc;QAEH,0PAAA,CAAA,UAAO;QAOG,iRAAA,CAAA,cAAW;;;MAlBhD;AAsGN,MAAM,oBAAoB,CAAC,EAAE,SAAS,EAAyB;;IAC7D,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IACtC,MAAM,mBAAqC,CAAC;QAC1C,IAAI,CAAC,SAAS,mBAAmB,OAAO,EAAE;QAE1C,IAAI,OAAO,QAAQ,iBAAiB,KAAK,UAAU;YACjD,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC,QAAQ,iBAAiB;gBACnD,OAAO,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YAC5C,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO,EAAE;YACX;QACF;QAEA,IAAI,MAAM,OAAO,CAAC,QAAQ,iBAAiB,GAAG;YAC5C,8DAA8D;YAC9D,OAAO,QAAQ,iBAAiB,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,OAAO,SAAS,UAAU;oBAC5B,OAAO;wBAAE,KAAK;wBAAM,OAAO;oBAAK;gBAClC;gBACA,OAAO;YACT;QACF;QAEA,OAAO,EAAE;IACX,CAAC;IAED,MAAM,gBAAgB,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAA0B;QACpD,UAAU,CAAA,GAAA,2QAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,KAAK;YACL,OAAO;QACT;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,aAAa;eAAI;YAAkB;gBAAE,KAAK,KAAK,GAAG;gBAAE,OAAO,KAAK,KAAK;YAAC;SAAE;QAE9E,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EACT,UACA,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,YAAY,IAAI,CAAC;YAC1C,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAe;iBAAU;YAAC;YACrE;YACA,cAAc,KAAK;YACnB,OAAO;gBAAE,SAAS;YAAkC;QACtD;IAEJ;IAEA,MAAM,6BAA6B,CAAC;QAClC,MAAM,aAAa,iBAAiB,MAAM,CAAC,CAAC,GAAG,QAAU,UAAU;QAEnE,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EACT,6BACA,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,YAAY,IAAI,CAAC;YAC1C,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAe;iBAAU;YAAC;YACrE;YACA,OAAO;gBAAE,SAAS;YAAwC;QAC5D;IAEJ;IAEA,qBACE,4SAAC,mIAAA,CAAA,OAAI;QAAE,GAAG,aAAa;kBACrB,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC;oBAAK,UAAU,cAAc,YAAY,CAAC;oBAAW,WAAU;;sCAC9D,4SAAC,mIAAA,CAAA,YAAS;4BACR,SAAS,cAAc,OAAO;4BAC9B,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;sDACP,4SAAC,yIAAA,CAAA,UAAU,CAAC,EAAE;sDAAC;;;;;;sDACf,4SAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,4SAAC,oIAAA,CAAA,QAAK;gDAAC,aAAY;gDAAyB,GAAG,KAAK;;;;;;;;;;;sDAEtD,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAKlB,4SAAC,mIAAA,CAAA,YAAS;4BACR,SAAS,cAAc,OAAO;4BAC9B,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;sDACP,4SAAC,yIAAA,CAAA,UAAU,CAAC,EAAE;sDAAC;;;;;;sDACf,4SAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,4SAAC,uIAAA,CAAA,WAAQ;gDAAC,aAAY;gDAAgC,GAAG,KAAK;;;;;;;;;;;sDAEhE,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAKlB,4SAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;sCAAS;;;;;;;;;;;;8BAExB,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,4SAAC;4BAAI,WAAU;;gCACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,4SAAC;wCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;0DAGF,4SAAC;gDAAI,WAAU;;kEACb,4SAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAA2B,MAAM,GAAG;;;;;;kEACrD,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,2BAA2B;sEAE1C,cAAA,4SAAC,+SAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;;;;;0DAIZ,4SAAC;gDAAI,WAAU;0DAAW,MAAM,KAAK;;;;;;;uCAlBhC;;;;;gCAqBR,iBAAiB,MAAM,KAAK,mBAC3B,4SAAC;oCAAI,WAAU;8CAAoF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjH;IAtIM;;QACgB,yRAAA,CAAA,iBAAc;QACL,2IAAA,CAAA,aAAU;QA2BjB,0PAAA,CAAA,UAAO;;;MA7BzB;AAwIN,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,eAAe,EACf,sBAAsB,EAKvB;;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAU,CAAC;IAEtE,MAAM,OAAO,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,2QAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,SAAS;QACX;IACF;IAEA,MAAM,EACJ,MAAM,cAAc,EACpB,WAAW,gBAAgB,EAC3B,SAAS,cAAc,EACxB,GAAG,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAGR;QACD,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;uCAAE;gBACP,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE;gBACxC,OAAO;YACT;;QACA,GAAG,yIAAA,CAAA,eAAY;IACjB;IAEA,MAAM,oBAAoB,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACpC,UAAU;6DAAE,OACV;gBAMA,MAAM,YAAY;oBAAE,KAAK,OAAO,KAAK;oBAAE,OAAO,OAAO,OAAO;gBAAC;gBAC7D,MAAM,iBAAiB,gBAAgB,WAAW,EAAE;gBACpD,MAAM,iBAAiB;uBAAI;oBAAgB;iBAAU;gBACrD,MAAM,aAAa,eAAe,GAAG;gFAAC,CAAC,IAAM,CAAC;4BAAE,KAAK,EAAE,GAAG;4BAAE,OAAO,EAAE,KAAK;wBAAC,CAAC;;gBAC5E,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW;YAC/C;;QACA,SAAS;6DAAE;gBACT;gBACA,KAAK,KAAK;YACZ;;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACvC,UAAU;gEAAE,OAAO;gBACjB,MAAM,iBAAiB;uBAAK,gBAAgB,WAAW,EAAE;iBAAE;gBAC3D,cAAc,CAAC,OAAO,KAAK,CAAC,GAAG;oBAAE,KAAK,OAAO,KAAK;oBAAE,OAAO,OAAO,OAAO;gBAAC;gBAC1E,MAAM,aAAa,eAAe,GAAG;mFAAC,CAAC,IAAM,CAAC;4BAAE,KAAK,EAAE,GAAG;4BAAE,OAAO,EAAE,KAAK;wBAAC,CAAC;;gBAC5E,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW;YAC/C;;QACA,SAAS;gEAAE;gBACT;gBACA,KAAK,KAAK;gBACV,sBAAsB,CAAC;YACzB;;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACvC,UAAU;gEAAE,OAAO;gBACjB,MAAM,aAAa,gBAAgB,QAChC;wEAAO,CAAC,GAAG,IAAM,MAAM;uEACvB;wEAAI,CAAC,IAAM,CAAC;4BAAE,KAAK,EAAE,GAAG;4BAAE,OAAO,EAAE,KAAK;wBAAC,CAAC;;gBAC7C,IAAI,CAAC,YAAY;gBACjB,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW;YAC/C;;QACA,SAAS;gEAAE;gBACT;YACF;;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,sBAAsB,GAAG;YAC3B,qBAAqB,MAAM,CAAC;gBAAE,GAAG,MAAM;gBAAE,OAAO;YAAmB;QACrE,OAAO;YACL,kBAAkB,MAAM,CAAC;QAC3B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,gBAAgB,OAAO,CAAC,MAAM;QAC7C,KAAK,QAAQ,CAAC,SAAS,QAAQ,OAAO;QACtC,KAAK,QAAQ,CAAC,WAAW,QAAQ,SAAS;QAC1C,sBAAsB;IACxB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EACT,mBACA,qBAAqB,WAAW,CAAC,OAAO,IAAI,CAAC;YAC3C;YACA,OAAO;gBAAE,SAAS;YAA8B;QAClD,IACA,CAAC,GACD;IAEJ;IAEA,qBACE,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC,mIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,4SAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAW,WAAU;;0CACrD,4SAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;0DACP,4SAAC,yIAAA,CAAA,UAAU,CAAC,EAAE;0DAAC;;;;;;0DACf,4SAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,4SAAC,oIAAA,CAAA,QAAK;oDAAC,aAAY;oDAAe,WAAU;oDAAQ,GAAG,KAAK;;;;;;;;;;;0DAE9D,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,4SAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;0DACP,4SAAC,yIAAA,CAAA,UAAU,CAAC,EAAE;0DAAC;;;;;;0DACf,4SAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,4SAAC,uIAAA,CAAA,WAAQ;oDAAC,aAAY;oDAAiB,WAAU;oDAAiB,GAAG,KAAK;;;;;;;;;;;0DAE5E,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,4SAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,kBAAkB,SAAS,IAAI,qBAAqB,SAAS;gCACvE,WAAU;0CAET,kBAAkB,SAAS,IAAI,qBAAqB,SAAS,iBAC5D;;sDACE,4SAAC,sIAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAClB,sBAAsB,IAAI,gBAAgB;;mDAE3C,sBAAsB,IACxB,kBAEA;;;;;;;;;;;;;;;;;8BAMR,4SAAC,mIAAA,CAAA,OAAI;oBAAC,cAAa;;sCACjB,4SAAC,mIAAA,CAAA,WAAQ;;8CACP,4SAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAU;;;;;;8CAC7B,4SAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAkB;;;;;;;;;;;;sCAGvC,4SAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAG,WAAU;kDAAqB;;;;;;oCAClC,iCACC,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC,sIAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;+CAEnB,gBAAgB,QAAQ,WAAW,kBACrC,4SAAC;wCAAI,WAAU;kDAAyE;;;;;6DAIxF,4SAAC;wCAAI,WAAU;;4CACZ,gBAAgB,QAAQ,IAAI,CAAC,QAAQ,sBACpC,4SAAC;oDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;;sEAGF,4SAAC;4DAAI,WAAU;;8EACb,4SAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EAA2B,OAAO,GAAG;;;;;;8EACtD,4SAAC;oEAAI,WAAU;8EACb,cAAA,4SAAC,oJAAA,CAAA,eAAY;;0FACX,4SAAC,oJAAA,CAAA,sBAAmB;gFAAC,OAAO;0FAC1B,cAAA,4SAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;8FAGF,cAAA,4SAAC,6TAAA,CAAA,eAAY;;;;;;;;;;;;;;;0FAGjB,4SAAC,oJAAA,CAAA,sBAAmB;gFAAC,OAAM;;kGACzB,4SAAC,oJAAA,CAAA,mBAAgB;wFACf,SAAS,IAAM,gBAAgB;wFAC/B,WAAU;;4FACX;0GAEC,4SAAC,6SAAA,CAAA,OAAI;gGAAC,WAAU;gGAAS,aAAa;;;;;;;;;;;;kGAExC,4SAAC,oJAAA,CAAA,mBAAgB;wFACf,SAAS,IAAM,iBAAiB;wFAChC,WAAU;;4FACX;0GAEC,4SAAC,6SAAA,CAAA,OAAI;gGAAC,WAAU;gGAAS,aAAa;;;;;;;;;;;;kGAExC,4SAAC,oJAAA,CAAA,mBAAgB;wFACf,SAAS,IAAM,mBAAmB;wFAClC,WAAU;;4FACX;0GAEC,4SAAC,+SAAA,CAAA,QAAK;gGAAC,WAAU;gGAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAMjD,4SAAC;4DAAI,WAAU;sEAAW,OAAO,KAAK;;;;;;;mDA/CjC;;;;;4CAkDR,gBAAgB,QAAQ,WAAW,mBAClC,4SAAC;gDAAI,WAAU;0DAAoF;;;;;;;;;;;;;;;;;;;;;;;sCAS7G,4SAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;sCACjB,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAG,WAAU;kDAAgB;;;;;;kDAC9B,4SAAC;wCAAI,WAAU;kDACZ,qIAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC7B,4SAAC;gDAEC,WAAU;;kEAEV,4SAAC;wDAAI,WAAU;;0EACb,4SAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAyB,SAAS,KAAK;;;;;;0EACxD,4SAAC;gEAAE,WAAU;0EAAiC,SAAS,WAAW;;;;;;;;;;;;kEAEpE,4SAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC7B,4SAAC,qIAAA,CAAA,SAAM;gEAEL,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,uBAAuB;0EACvC;+DAJM;;;;;;;;;;;+CAVN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BzB;IAlRM;;QAWS,0PAAA,CAAA,UAAO;QAYhB,8QAAA,CAAA,WAAQ;QAYc,iRAAA,CAAA,cAAW;QAoBR,iRAAA,CAAA,cAAW;QAcX,iRAAA,CAAA,cAAW;;;MArEpC;AAoRN,MAAM,sBAAsB,CAAC,EAC3B,SAAS,EACT,cAAc,EAIf;;IACC,qBAAqB;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrF,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,wBAAwB;IACxB,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;2DAAE;YACjC,IAAI;gBACF,kBAAkB;gBAClB,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;gBACpC,IAAI,UAAU,MAAM;oBAClB,aACE,SAAS,IAAI,CAAC,GAAG;2EAAC,CAAC,OACjB,OAAO,SAAS,WAAW,OAAO,KAAK,IAAI;;gBAGjD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;YACb,SAAU;gBACR,kBAAkB;YACpB;QACF;0DAAG;QAAC;KAAU;IAEd,MAAM,yBAAyB,CAAC;QAC9B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,0BAA0B;QAE1B,IAAI,MAAM,IAAI,IAAI;YAChB,MAAM,WAAW,eAAe,MAAM,CAAC,CAAC,OACtC,KAAK,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;YAE/C,4BAA4B;YAC5B,2BAA2B;YAC3B,4BAA4B;QAC9B,OAAO;YACL,2BAA2B;QAC7B;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,IAAI,2BAA2B,yBAAyB,MAAM,GAAG,GAAG;YAClE,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,EAAE,cAAc;oBAChB,4BAA4B,CAAC,OAC3B,OAAO,yBAAyB,MAAM,GAAG,IAAI,OAAO,IAAI;oBAE1D;gBACF,KAAK;oBACH,EAAE,cAAc;oBAChB,4BAA4B,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI;oBAC7D;gBACF,KAAK;oBACH,EAAE,cAAc;oBAChB,MAAM,eAAe,wBAAwB,CAAC,yBAAyB;oBACvE,uBAAuB,CAAC,OAAS;+BAAI;4BAAM;yBAAa;oBACxD,0BAA0B;oBAC1B,2BAA2B;oBAC3B;gBACF,KAAK;oBACH,EAAE,cAAc;oBAChB,2BAA2B;oBAC3B;gBACF,KAAK;oBACH,EAAE,cAAc;oBAChB,MAAM,kBAAkB,wBAAwB,CAAC,yBAAyB;oBAC1E,uBAAuB,CAAC,OAAS;+BAAI;4BAAM;yBAAgB;oBAC3D,0BAA0B;oBAC1B,2BAA2B;oBAC3B;gBACF;oBACE;YACJ;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,qBAAqB,MAAM,KAAK,GAAG;QAEvC,IAAI;YACF,kBAAkB;YAClB,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;YACpC,MAAM;YACN,wBAAwB,EAAE;YAC1B,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;QACb,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,oBAAoB,MAAM,KAAK,GAAG;QAEtC,IAAI;YACF,kBAAkB;YAClB,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;YACnC,MAAM;YACN,uBAAuB,EAAE;YACzB,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;QACb,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,UAAU;IACV,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,WAAW;gBACb;YACF;QACF;wCAAG;QAAC;QAAW;KAAe;IAE9B,SAAS;IACT,qBACE,4SAAC;QAAI,WAAU;;0BACb,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,qBAAqB,MAAM,KAAK,KAAK;4BAC/C,WAAU;sCAET,+BACC;;kDACE,4SAAC,sIAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,4SAAC,mRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAQvC,+BACC,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC,sIAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;uBAEnB,UAAU,MAAM,KAAK,kBACvB,4SAAC;gBAAE,WAAU;0BAAgC;;;;;qCAE7C,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,uIAAA,CAAA,WAAQ;gCACP,SAAS,qBAAqB,MAAM,KAAK,UAAU,MAAM,IAAI,UAAU,MAAM,GAAG;gCAChF,iBAAiB,CAAC;oCAChB,IAAI,SAAS;wCACX,wBAAwB;oCAC1B,OAAO;wCACL,wBAAwB,EAAE;oCAC5B;gCACF;gCACA,cAAW;;;;;;0CAEb,4SAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;oBAEvC,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,4SAAC;4BAEC,WAAU;sCAEV,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,uIAAA,CAAA,WAAQ;wCACP,SAAS,qBAAqB,QAAQ,CAAC;wCACvC,iBAAiB,CAAC;4CAChB,IAAI,SAAS;gDACX,wBAAwB,CAAC,OAAS;2DAAI;wDAAM;qDAAK;4CACnD,OAAO;gDACL,wBAAwB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;4CAC7D;wCACF;wCACA,cAAY,CAAC,YAAY,EAAE,MAAM;;;;;;kDAEnC,4SAAC,6SAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,4SAAC;wCAAK,WAAU;kDAAW;;;;;;;;;;;;2BAhBxB;;;;;;;;;;;0BAuBb,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU;wCACV,WAAW;wCACX,WAAU;;;;;;oCAEX,2BAA2B,yBAAyB,MAAM,GAAG,mBAC5D,4SAAC;wCAAI,WAAU;kDACZ,yBAAyB,GAAG,CAAC,CAAC,MAAM,sBACnC,4SAAC;gDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kFACA,6BAA6B,SAAS;gDAExC,SAAS;oDACP,uBAAuB,CAAC,OAAS;+DAAI;4DAAM;yDAAK;oDAChD,0BAA0B;oDAC1B,2BAA2B;gDAC7B;;kEAEA,4SAAC,6SAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAK,WAAU;0EAAwB,KAAK,KAAK,CAAC,KAAK,GAAG;;;;;;0EAC3D,4SAAC;gEAAK,WAAU;0EAA0C;;;;;;;;;;;;;+CAdvD;;;;;;;;;;;;;;;;0CAqBf,4SAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAC,MAAM,sBAC9B,4SAAC;wCAEC,WAAU;;0DAEV,4SAAC,6SAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,4SAAC;0DAAM;;;;;;0DACP,4SAAC;gDACC,MAAK;gDACL,SAAS,IACP,uBAAuB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gDAE/D,WAAU;0DAEV,cAAA,4SAAC,mRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAZV;;;;;;;;;;0CAiBX,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,oBAAoB,MAAM,KAAK,KAAK;gCAC9C,WAAU;0CAET,+BACC;;sDACE,4SAAC,sIAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;mDAInD;;;;;;;;;;;;;;;;;;;;;;;;AAOd;IA1RM;MAAA;uCA4RS", "debugId": null}}]}