import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { useIsMobile } from "@/hooks/use-mobile";
import { getFiles } from "@/lib/api";
import { debug } from "@/lib/debug";
import { useIsAgentRunning, useSubscribeToAgentRunning } from "@/stores/current-thread";
import { useNavigateFile } from "@/stores/navigate-file";
import { useQuery } from "@tanstack/react-query";
import { memo, useCallback, useMemo } from "react";
import { useShallow } from "zustand/react/shallow";
import FileTree, { FileNode } from "./file-tree";
import TextEditor from "./text-editor";

type Props = {
  projectId: string;
  envId: string;
  isIframeLoading?: boolean;
};

const CodeEditor = ({ projectId, envId }: Props) => {
  const isMobile = useIsMobile();

  const { file, setFile, showFileTree } = useNavigateFile(
    useShallow((state) => ({
      file: state.file,
      setFile: state.setFile,
      showFileTree: state.showFileTree,
    })),
  );

  const isAgentRunning = useIsAgentRunning();

  const {
    data: filesData,
    isLoading: isLoadingFiles,
    refetch,
  } = useQuery<FileNode[]>({
    queryKey: ["files", projectId, envId],
    queryFn: async () => {
      const result = await getFiles(projectId, "/");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const sortedFiles = (result?.files || []).sort((a: any, b: any) => {
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.localeCompare(b.name);
      });
      return sortedFiles;
    },
    enabled: !!projectId,
    placeholderData: (previousData) => previousData,
  });

  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {
    if (!isAgentRunning && wasRunning) {
      debug("Agent streaming complete, refetching files");
      refetch();
    }
  });

  const filesAndFolders = useMemo(() => {
    return filesData || [];
  }, [filesData]);

  const handleFileSelect = useCallback((filepath: string) => {
    setFile(filepath);
  }, []);

  if (isMobile) {
    return (
      <div className="relative flex h-full grid-cols-12 flex-col overflow-hidden md:grid">
        <FileTree
          className="md:col-span-2 md:border-r"
          projectId={projectId}
          onFileSelect={handleFileSelect}
          selectedFile={file}
          filesAndFolders={filesAndFolders}
          isLoadingFilePaths={isLoadingFiles}
          allFilePaths={filesData || []}
          refetchFileTree={refetch}
        />

        <TextEditor
          isMobile
          className={showFileTree ? "md:col-span-10" : "md:col-span-12"}
          file={file}
          isAgentRunning={isAgentRunning}
          projectId={projectId}
        />
      </div>
    );
  }

  return (
    <ResizablePanelGroup
      direction="horizontal"
      className="relative flex h-full flex-col overflow-hidden md:flex-row"
    >
      {showFileTree && (
        <>
          <ResizablePanel id="file-tree" order={1} defaultSize={20} minSize={20} maxSize={45}>
            <FileTree
              className="h-full border-r"
              projectId={projectId}
              onFileSelect={handleFileSelect}
              selectedFile={file}
              filesAndFolders={filesAndFolders}
              isLoadingFilePaths={isLoadingFiles}
              allFilePaths={filesData || []}
              refetchFileTree={refetch}
            />
          </ResizablePanel>

          <ResizableHandle />
        </>
      )}

      <ResizablePanel id="code-editor" order={2} defaultSize={showFileTree ? 80 : 100}>
        <TextEditor
          isAgentRunning={isAgentRunning}
          projectId={projectId}
          className="h-full"
          file={file}
        />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};

export default memo(CodeEditor);
