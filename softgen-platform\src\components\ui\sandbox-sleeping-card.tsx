import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useCheckAndStart } from "@/lib/api";
import { cn } from "@/lib/utils";
import { useProject } from "@/providers/project-provider";
import { Play } from "@mynaui/icons-react";
import { useState } from "react";
import Typography from "./typography";

interface SandboxSleepingCardProps {
  className?: string;
}

export const SandboxSleepingCard = ({ className }: SandboxSleepingCardProps) => {
  const { project } = useProject();
  const startEnvironment = useCheckAndStart();
  const [isWakingUp, setIsWakingUp] = useState(false);

  const handleWakeUp = async () => {
    if (!project?.project_id) return;

    setIsWakingUp(true);
    try {
      await startEnvironment.mutateAsync(project.project_id);
    } catch (error) {
      console.error("Failed to wake up sandbox:", error);
    } finally {
      setIsWakingUp(false);
    }
  };

  return (
    <Card className={cn("max-w-md shadow-lg", className)}>
      <CardContent className="space-y-6 p-6">
        <div className="flex items-start gap-4">
          <div className="space-y-1.5">
            <Typography.H4>Sandbox is sleeping</Typography.H4>
            <Typography.P className="mt-0 leading-normal">
              Your development environment is currently paused to conserve resources
            </Typography.P>
          </div>
        </div>

        <Button onClick={handleWakeUp} disabled={isWakingUp} className="w-full" size="lg">
          {isWakingUp ? (
            <>
              <div className="mr-2 size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              Waking up...
            </>
          ) : (
            <>
              <Play className="size-5" />
              Wake up sandbox
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
