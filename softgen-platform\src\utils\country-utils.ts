/**
 * Utility functions for country detection and management
 */

// List of OECD countries
export const oecdCountries = [
  "AU",
  "AT",
  "BE",
  "CA",
  "CL",
  "CZ",
  "DK",
  "EE",
  "FI",
  "FR",
  "DE",
  "GR",
  "HU",
  "IS",
  "IE",
  "IL",
  "IT",
  "JP",
  "KR",
  "LV",
  "LT",
  "LU",
  "MX",
  "NL",
  "NZ",
  "NO",
  "PL",
  "PT",
  "SK",
  "SI",
  "ES",
  "SE",
  "CH",
  "TR",
  "GB",
  "US",
];

/**
 * Check if a country code is an OECD member
 */
export const isOecdCountry = (countryCode: string): boolean => {
  return oecdCountries.some((country) => country === countryCode);
};

/**
 * Get detected country from cookies (client-side)
 */
export const getDetectedCountry = (): string => {
  if (typeof document === "undefined") return "US"; // Server-side fallback

  const cookies = document.cookie.split(";");
  const countryCookie = cookies.find((cookie) => cookie.trim().startsWith("detected_country="));

  if (countryCookie) {
    const country = countryCookie.split("=")[1].trim();
    return country;
  }
  return "US";
};

/**
 * Pricing constants
 */
export const OECD_MEMBERSHIP_PRICE = 50; // $50/year for OECD countries
export const NON_OECD_MEMBERSHIP_PRICE = 25; // $25/year for non-OECD countries

/**
 * Get membership price based on country
 */
export const getMembershipPrice = (countryCode: string): number => {
  return isOecdCountry(countryCode) ? OECD_MEMBERSHIP_PRICE : NON_OECD_MEMBERSHIP_PRICE;
};
