"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Loading from "@/components/ui/loading";
import { LazyModal } from "@/components/ui/modal";
import BannedUser from "@/features/app/banned-user";
import Header from "@/features/app/header";
import { CreateProjectResponse } from "@/features/app/modal/create-project-modal";
import ProjectCard from "@/features/app/projects/project-card";
import ProjectsLoading from "@/features/app/projects/projects-loading";
import SearchForm from "@/features/app/projects/search-form";
import Sidebar from "@/features/app/sidebar";
import { SettingsAction } from "@/features/app/type";
import { ZendeskScript } from "@/features/app/zendesk-help-button";
import BannerContainer from "@/features/flag/banner-container";
import { EmptyState } from "@/features/global/empty-state";
import PageHeader from "@/features/global/page-header";
import { errorToast, loadingToast } from "@/features/global/toast";
import { createProject, listProjects } from "@/lib/api";
import { useAuth } from "@/providers/auth-provider";
import { Project } from "@/providers/project-provider";
import { CACHE_PARAMS } from "@/providers/query-provider";
import { useInputPromptStore } from "@/stores/input-prompt";
import { Plus } from "@mynaui/icons-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ApiError } from "next/dist/server/api-utils";
import { useRouter, useSearchParams } from "next/navigation";
import { lazy, Suspense, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useShallow } from "zustand/react/shallow";

const WelcomeModal = lazy(() => import("@/features/app/welcome-modal"));
const CreateProjectModalContent = lazy(() => import("@/features/app/modal/create-project-modal"));
const ProjectCardSettingsModal = lazy(
  () => import("@/features/app/projects/project-card-setting-modal"),
);
const SettingsModalContent = lazy(() => import("@/features/app/settings"));

export default function Dashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { prompt, isNewUser, setIsNewUser } = useInputPromptStore(
    useShallow((state) => ({
      prompt: state.prompt,
      isNewUser: state.isNewUser,
      setIsNewUser: state.setIsNewUser,
    })),
  );
  const queryClient = useQueryClient();

  const [showCreateProjectModal, setShowCreateProjectModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [settingsAction, setSettingsAction] = useState<SettingsAction>(undefined);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"sort-by-name" | "sort-by-activity">("sort-by-activity");

  const attemptedPromptsRef = useRef<Set<string>>(new Set());

  const { mutate: createProjectMutation, isPending: isCreatingProject } = useMutation<
    CreateProjectResponse,
    ApiError,
    { name: string }
  >({
    mutationKey: ["create-project"],
    mutationFn: async (data: { name: string }) => {
      const response = await loadingToast("Creating project...", createProject(data.name));
      return response as CreateProjectResponse;
    },
    onSuccess: (data: CreateProjectResponse) => {
      if (data && data.project_data) {
        queryClient.invalidateQueries({ queryKey: ["projects"] });
        router.push(`/project/${data.project_data.project_id}?action=chat`);
      } else {
        errorToast("Error creating project");
      }
    },
    onError: () => {
      errorToast("Error creating project");
    },
  });

  // Nav component has a link to create project
  useEffect(() => {
    const shouldCreateProject = searchParams.get("action") === "create-project";
    if (shouldCreateProject) {
      setShowCreateProjectModal(true);
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("action");
      router.replace(newUrl.toString(), { scroll: false });
    }
  }, [searchParams]);

  const { data: projects = [], isLoading } = useQuery<Project[]>({
    queryKey: ["projects"],
    queryFn: listProjects,
    enabled: !!user?.access_token,
    refetchOnWindowFocus: true,
    ...CACHE_PARAMS
  });

  const filteredProjects = useMemo(() => {
    if (!projects.length) return [];

    return [...projects]
      .filter(
        (project) =>
          searchTerm === "" || project.name.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      .sort((a, b) => {
        if (sortBy === "sort-by-name") {
          return a.name.localeCompare(b.name);
        }
        return new Date(b.last_updated_date).getTime() - new Date(a.last_updated_date).getTime();
      });
  }, [projects, searchTerm, sortBy]);

  const { mutate } = useMutation<
    CreateProjectResponse,
    { detail: string },
    { name: string; type: "frontend" | "fullstack" }
  >({
    mutationKey: ["create-project"],
    mutationFn: async (data: { name: string; type: "frontend" | "fullstack" }) => {
      const response = await loadingToast("Creating project...", createProject(data.name));
      return response as CreateProjectResponse;
    },
    onSuccess: (data: CreateProjectResponse) => {
      if (data && data.project_data) {
        queryClient.invalidateQueries({ queryKey: ["projects"] });
        router.push(`/project/${data.project_data.project_id}`);
        attemptedPromptsRef.current.delete(prompt);
      } else {
        errorToast("Error creating project");
      }
    },
    onError: (error: { detail: string }) => {
      errorToast(error.detail || "Error creating project");
    },
  });

  // create project when new user is redirected here from landing prompt
  const handleClaimTokens = useCallback(() => {
    setIsNewUser(false);
    if (prompt && !attemptedPromptsRef.current.has(prompt)) {
      attemptedPromptsRef.current.add(prompt);
      mutate({ name: "New Project", type: "frontend" });
    }
  }, [prompt]);

  const wholesaleUser = user.userFromDb?.plan?.startsWith("wholesale");

  // create project when existing user is redirected here from landing prompt
  useEffect(() => {
    if (!isNewUser && prompt && !attemptedPromptsRef.current.has(prompt)) {
      attemptedPromptsRef.current.add(prompt);
      mutate({ name: "New Project", type: "frontend" });
    }
  }, []);

  useEffect(() => {
    if (isNewUser && wholesaleUser) {
      setIsNewUser(false);
      window.location.href = "/pricing/wholesale";
    }
  }, [wholesaleUser]);

  if (isNewUser && wholesaleUser) {
    return null;
  }

  const isLoadingProjects = !user?.access_token || isLoading;

  if (user?.userFromDb?.plan === "banned") {
    return <BannedUser />;
  }

  return (
    <>
      {/* Zendesk Widget - Only for paid users */}
      {user?.userFromDb?.plan && user.userFromDb.plan !== "free-tier" && (
        <ZendeskScript reset={true} />
      )}
      <main className="flex h-screen">
        <Sidebar setSettingsAction={setSettingsAction} />

        <div className="relative flex w-full flex-1 flex-col overflow-y-auto">
          <BannerContainer />
          <Header setSettingsAction={setSettingsAction} />

          <div className="flex-1 bg-background">
            <div className="mx-auto flex w-full flex-col px-4 lg:max-w-7xl">
              <div className="h-full w-full">
                <div className="flex flex-col items-start justify-between py-8 lg:flex-row lg:items-center">
                  <PageHeader title="Your Projects" />

                  <div className="mt-4 flex w-full flex-col items-center gap-3 lg:mt-0 lg:w-fit lg:flex-row lg:gap-2">
                    {projects.length > 0 && (
                      <SearchForm
                        searchTerm={searchTerm}
                        setSearchTerm={setSearchTerm}
                        sortBy={sortBy}
                        setSortBy={setSortBy}
                      />
                    )}

                    <div className="shadow-xs group inline-flex w-full rounded-md rtl:space-x-reverse">
                      <Button
                        className="h-10 w-full rounded-none bg-[#0a0a0a]/95 pr-4 font-medium shadow-none first:rounded-s-lg last:rounded-e-lg hover:bg-[#0a0a0a]/90 focus-visible:z-10 dark:bg-[#fafafa] dark:hover:bg-[#fafafa]/80"
                        onClick={() => createProjectMutation({ name: "New Project" })}
                        disabled={isCreatingProject}
                      >
                        {isCreatingProject && (
                          <Loading className="size-4 animate-spin text-background" />
                        )}
                        Add New...
                      </Button>
                    </div>
                  </div>
                </div>

                {isLoadingProjects ? (
                  <ProjectsLoading />
                ) : filteredProjects.length > 0 ? (
                  <div className="grid gap-6 pt-4 lg:grid-cols-3">
                    {filteredProjects.map((project, index) => (
                      <ProjectCard
                        key={`project-${index}`}
                        project={project}
                        setSelectedProject={setSelectedProject}
                      />
                    ))}
                  </div>
                ) : (
                  <EmptyState
                    title="No projects found"
                    description="Create a new project to get started."
                    action={
                      <Button
                        className="mt-4 h-9 w-fit bg-[#0a0a0a]/95 pr-4 font-medium shadow-none hover:bg-[#0a0a0a]/90 focus-visible:z-10 dark:bg-[#fafafa] dark:hover:bg-[#fafafa]/80 lg:mt-0"
                        size="sm"
                        onClick={() => setShowCreateProjectModal(true)}
                      >
                        <Plus strokeWidth={2} />
                        Add New Project
                      </Button>
                    }
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {isNewUser && !wholesaleUser && (
          <Suspense fallback={null}>
            <WelcomeModal handleClaimTokens={handleClaimTokens} />
          </Suspense>
        )}

        <LazyModal open={!!settingsAction} onOpenChange={() => setSettingsAction(undefined)}>
          <SettingsModalContent tab={settingsAction} />
        </LazyModal>

        <LazyModal open={showCreateProjectModal} onOpenChange={setShowCreateProjectModal}>
          <CreateProjectModalContent onOpenChange={setShowCreateProjectModal} />
        </LazyModal>

        <LazyModal open={!!selectedProject} onOpenChange={() => setSelectedProject(null)}>
          <ProjectCardSettingsModal
            project={selectedProject}
            setShowModal={() => setSelectedProject(null)}
          />
        </LazyModal>
      </main>
    </>
  );
}
