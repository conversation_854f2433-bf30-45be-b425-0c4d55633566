(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/facebook-pixel.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "FacebookPixel": (()=>FacebookPixel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/script.js [app-client] (ecmascript)");
"use client";
;
;
const FACEBOOK_PIXEL_ID = "701451716094912";
function FacebookPixel() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        id: "fb-pixel",
        strategy: "afterInteractive",
        dangerouslySetInnerHTML: {
            __html: `
          (function(f,b,e,v,n,t,s) {
            try {
              if(f.fbq)return;
              n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];
              t=b.createElement(e);
              t.async=!0;
              t.src=v;
              try {
                s=b.getElementsByTagName(e)[0];
                if (s && s.parentNode) {
                  s.parentNode.insertBefore(t,s);
                } else {
                  console.log('Facebook Pixel: No script tag found, appending to head');
                  b.head.appendChild(t);
                }
              } catch(insertErr) {
                console.log('Facebook Pixel: Script insertion skipped', insertErr);
              }
              fbq('init', '${FACEBOOK_PIXEL_ID}');
              fbq('track', 'PageView');
            } catch (error) {
              console.log('Facebook Pixel: Setup skipped', error);
            }
          })(window, document,'script', 'https://connect.facebook.net/en_US/fbevents.js');
        `
        }
    }, void 0, false, {
        fileName: "[project]/src/components/facebook-pixel.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = FacebookPixel;
var _c;
__turbopack_context__.k.register(_c, "FacebookPixel");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "formatDate": (()=>formatDate),
    "formatPrice": (()=>formatPrice),
    "transitionVariants": (()=>transitionVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$3$2e$1$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$3$2e$1$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const formatPrice = ({ price, options })=>{
    const { currency = "USD", notation = "standard" } = options || {};
    const numericPrice = typeof price === "string" ? parseFloat(price) : price;
    return new Intl.NumberFormat("en-IN", {
        style: "currency",
        currency,
        notation: numericPrice < 100000 ? undefined : notation,
        maximumFractionDigits: 0
    }).format(numericPrice);
};
function formatDate(date) {
    if (!date?.includes("T")) {
        date = `${date}T00:00:00`;
    }
    return new Date(date).toLocaleString("en-us", {
        month: "long",
        day: "numeric",
        year: "numeric"
    });
}
const transitionVariants = {
    item: {
        hidden: {
            opacity: 0,
            filter: "blur(20px)",
            y: 40
        },
        visible: {
            opacity: 1,
            filter: "blur(0px)",
            y: 0,
            transition: {
                type: "spring",
                bounce: 0.3,
                duration: 1.5,
                delay: 1.2
            }
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/tooltip.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tooltip": (()=>Tooltip),
    "TooltipContent": (()=>TooltipContent),
    "TooltipProvider": (()=>TooltipProvider),
    "TooltipTrigger": (()=>TooltipTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$tooltip$40$1$2e$2_577567665b1888228a51cf76b71cde18$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@radix-ui+react-tooltip@1.2_577567665b1888228a51cf76b71cde18/node_modules/@radix-ui/react-tooltip/dist/index.mjs [app-client] (ecmascript)");
"use client";
;
;
;
function TooltipProvider({ delayDuration = 0, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$tooltip$40$1$2e$2_577567665b1888228a51cf76b71cde18$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        "data-slot": "tooltip-provider",
        delayDuration: delayDuration,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tooltip.tsx",
        lineNumber: 12,
        columnNumber: 5
    }, this);
}
_c = TooltipProvider;
function Tooltip({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$tooltip$40$1$2e$2_577567665b1888228a51cf76b71cde18$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "tooltip",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tooltip.tsx",
        lineNumber: 21,
        columnNumber: 10
    }, this);
}
_c1 = Tooltip;
function TooltipTrigger({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$tooltip$40$1$2e$2_577567665b1888228a51cf76b71cde18$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Trigger"], {
        "data-slot": "tooltip-trigger",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tooltip.tsx",
        lineNumber: 25,
        columnNumber: 10
    }, this);
}
_c2 = TooltipTrigger;
function TooltipContent({ className, sideOffset = 4, children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$tooltip$40$1$2e$2_577567665b1888228a51cf76b71cde18$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Portal"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$tooltip$40$1$2e$2_577567665b1888228a51cf76b71cde18$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"], {
            "data-slot": "tooltip-content",
            sideOffset: sideOffset,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("z-50 max-w-sm rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2", className),
            ...props,
            children: [
                children,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$tooltip$40$1$2e$2_577567665b1888228a51cf76b71cde18$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tooltip$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Arrow"], {
                    className: "z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px] bg-primary fill-primary"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/tooltip.tsx",
                    lineNumber: 46,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/tooltip.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/tooltip.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
}
_c3 = TooltipContent;
;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "TooltipProvider");
__turbopack_context__.k.register(_c1, "Tooltip");
__turbopack_context__.k.register(_c2, "TooltipTrigger");
__turbopack_context__.k.register(_c3, "TooltipContent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/posthog-tracking.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "capture": (()=>capture),
    "identify": (()=>identify),
    "reset": (()=>reset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/posthog-js@1.252.1/node_modules/posthog-js/dist/module.js [app-client] (ecmascript)");
;
function identify(user) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if ("object" !== "undefined" && window.posthog && user.kinde_id) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        window.posthog.identify(user.kinde_id, {
            email: user.email,
            is_superuser: user.userFromDb?.is_superuser,
            is_subscribed: user.isSubscribed,
            plan: user.userFromDb?.plan
        });
    }
}
function reset() {
    try {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reset();
    } catch (error) {
        console.error("Error resetting PostHog:", error);
    }
}
function capture(event, properties) {
    try {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].capture(event, properties);
    } catch (error) {
        console.error("Error capturing event:", error);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-debounce.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDebounce": (()=>useDebounce)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
const useDebounce = (value, delay)=>{
    _s();
    const [debouncedValue, setDebouncedValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDebounce.useEffect": ()=>{
            setIsLoading(true);
            const handler = setTimeout({
                "useDebounce.useEffect.handler": ()=>{
                    setDebouncedValue(value);
                    setIsLoading(false);
                }
            }["useDebounce.useEffect.handler"], delay);
            return ({
                "useDebounce.useEffect": ()=>{
                    clearTimeout(handler);
                }
            })["useDebounce.useEffect"];
        }
    }["useDebounce.useEffect"], [
        value,
        delay
    ]);
    return {
        debouncedValue,
        isLoading
    };
};
_s(useDebounce, "nH/OO4lfJshyoRM+Jff87dBdmI4=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/auth-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAllCookies": (()=>clearAllCookies),
    "getCookie": (()=>getCookie),
    "getItemWithExpiry": (()=>getItemWithExpiry),
    "removeCookie": (()=>removeCookie),
    "removeItemWithExpiry": (()=>removeItemWithExpiry),
    "setCookieWithExpiry": (()=>setCookieWithExpiry),
    "setItemWithExpiry": (()=>setItemWithExpiry)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const setItemWithExpiry = (key, value, ttl)=>{
    // Check if we're in a browser environment
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const now = Date.now();
    const item = {
        value: value,
        expiry: now + ttl
    };
    localStorage.setItem(key, JSON.stringify(item));
};
const getItemWithExpiry = (key)=>{
    // Check if we're in a browser environment
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const itemStr = localStorage.getItem(key);
    if (!itemStr) {
        return null;
    }
    const item = JSON.parse(itemStr);
    const now = Date.now();
    if (now > item.expiry) {
        localStorage.removeItem(key);
        return null;
    }
    return item.value;
};
const removeItemWithExpiry = (key)=>{
    // Check if we're in a browser environment
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    localStorage.removeItem(key);
};
const setCookieWithExpiry = (name, value, ttlMs)=>{
    // Check if we're in a browser environment
    if (typeof document === "undefined") return;
    const isProd = ("TURBOPACK compile-time value", "development") === "production";
    const maxAge = Math.floor(ttlMs / 1000); // Convert milliseconds to seconds for cookie
    document.cookie = `${name}=${value}; path=/; max-age=${maxAge}; SameSite=Lax${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ""}`;
};
const getCookie = (name)=>{
    // Check if we're in a browser environment
    if (typeof document === "undefined") return null;
    try {
        return document.cookie.split("; ").find((row)=>row.startsWith(`${name}=`))?.split("=")[1] || null;
    } catch (error) {
        console.error(`Error getting cookie ${name}:`, error);
        return null;
    }
};
const removeCookie = (name)=>{
    // Check if we're in a browser environment
    if (typeof document === "undefined") return;
    const isProd = ("TURBOPACK compile-time value", "development") === "production";
    // Set multiple cookie deletion attempts with different paths to ensure complete removal
    document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ""}`;
    document.cookie = `${name}=; path=/; domain=${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ""}`;
    document.cookie = `${name}=; path=/; domain=.${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : ""}`;
};
const clearAllCookies = ()=>{
    // Check if we're in a browser environment
    if (typeof document === "undefined") return;
    const cookies = document.cookie.split(";");
    for(let i = 0; i < cookies.length; i++){
        const cookie = cookies[i];
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
        // Skip empty names
        if (!name) continue;
        // Use the removeCookie function for each cookie
        removeCookie(name);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/debug.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "debug": (()=>debug)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const debug = ("TURBOPACK compile-time truthy", 1) ? console.log.bind(console) : ("TURBOPACK unreachable", undefined);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "API_BASE_URL": (()=>API_BASE_URL),
    "activateUser": (()=>activateUser),
    "addGithubCollaborator": (()=>addGithubCollaborator),
    "addMessageToThread": (()=>addMessageToThread),
    "addTeamMember": (()=>addTeamMember),
    "authorizeSupabase": (()=>authorizeSupabase),
    "banUserByAdmin": (()=>banUserByAdmin),
    "closeFilesInEditor": (()=>closeFilesInEditor),
    "connectGitHubCloneWebSocket": (()=>connectGitHubCloneWebSocket),
    "connectRemixProjectWebSocket": (()=>connectRemixProjectWebSocket),
    "connectSupabaseProject": (()=>connectSupabaseProject),
    "connectTerminalSession": (()=>connectTerminalSession),
    "connectVercelDeployWebSocket": (()=>connectVercelDeployWebSocket),
    "createBlog": (()=>createBlog),
    "createCustomerPortalSession": (()=>createCustomerPortalSession),
    "createFile": (()=>createFile),
    "createProject": (()=>createProject),
    "createThread": (()=>createThread),
    "default": (()=>__TURBOPACK__default__export__),
    "deleteBlog": (()=>deleteBlog),
    "deleteFile": (()=>deleteFile),
    "deleteProject": (()=>deleteProject),
    "deleteProjectPrompt": (()=>deleteProjectPrompt),
    "deleteSupabaseOrganization": (()=>deleteSupabaseOrganization),
    "deleteThread": (()=>deleteThread),
    "deleteUserByAdmin": (()=>deleteUserByAdmin),
    "disconnectSupabase": (()=>disconnectSupabase),
    "downloadStripeAnalyticsByAdmin": (()=>downloadStripeAnalyticsByAdmin),
    "enhancePrompt": (()=>enhancePrompt),
    "executeCommand": (()=>executeCommand),
    "freeRequestCheckout": (()=>freeRequestCheckout),
    "getAgentStatus": (()=>getAgentStatus),
    "getBillingDetailsByAdmin": (()=>getBillingDetailsByAdmin),
    "getBlog": (()=>getBlog),
    "getBlogBySlug": (()=>getBlogBySlug),
    "getEnvKeys": (()=>getEnvKeys),
    "getFileContent": (()=>getFileContent),
    "getFilePaths": (()=>getFilePaths),
    "getFiles": (()=>getFiles),
    "getGithubInfo": (()=>getGithubInfo),
    "getKindeUser": (()=>getKindeUser),
    "getOpenFiles": (()=>getOpenFiles),
    "getProject": (()=>getProject),
    "getProjectActiveSessionStatus": (()=>getProjectActiveSessionStatus),
    "getProjectByAdmin": (()=>getProjectByAdmin),
    "getProjectPrompts": (()=>getProjectPrompts),
    "getProjectThreads": (()=>getProjectThreads),
    "getSupabaseOrganizations": (()=>getSupabaseOrganizations),
    "getSupabaseProjectStatus": (()=>getSupabaseProjectStatus),
    "getSupabaseProjects": (()=>getSupabaseProjects),
    "getTeamMembers": (()=>getTeamMembers),
    "getTemplateByIdentifier": (()=>getTemplateByIdentifier),
    "getThread": (()=>getThread),
    "getThreadLlmMessages": (()=>getThreadLlmMessages),
    "getThreadSessionStatus": (()=>getThreadSessionStatus),
    "getToken": (()=>getToken),
    "getTokenUsage": (()=>getTokenUsage),
    "getUserByEmailAdmin": (()=>getUserByEmailAdmin),
    "getUserProjectsByAdmin": (()=>getUserProjectsByAdmin),
    "getUserTransactionsByAdmin": (()=>getUserTransactionsByAdmin),
    "getUsersByAdmin": (()=>getUsersByAdmin),
    "getWallet": (()=>getWallet),
    "getWalletTransactions": (()=>getWalletTransactions),
    "gitCommit": (()=>gitCommit),
    "gitForcePull": (()=>gitForcePull),
    "listBlogs": (()=>listBlogs),
    "listProjects": (()=>listProjects),
    "moveFile": (()=>moveFile),
    "oneTimePaymentCheckout": (()=>oneTimePaymentCheckout),
    "openFilesInEditor": (()=>openFilesInEditor),
    "remixProject": (()=>remixProject),
    "removeTeamMember": (()=>removeTeamMember),
    "renameThread": (()=>renameThread),
    "restoreSupabaseProject": (()=>restoreSupabaseProject),
    "revertGithubCommit": (()=>revertGithubCommit),
    "searchProjectByAdmin": (()=>searchProjectByAdmin),
    "setupVercelCLI": (()=>setupVercelCLI),
    "startAgent": (()=>startAgent),
    "startSessionRun": (()=>startSessionRun),
    "startSessionRunWithWebSocket": (()=>startSessionRunWithWebSocket),
    "stopAgent": (()=>stopAgent),
    "submitAnswer": (()=>submitAnswer),
    "submitFeedback": (()=>submitFeedback),
    "submitPrize": (()=>submitPrize),
    "subscriptionCheckout": (()=>subscriptionCheckout),
    "testSqlQuery": (()=>testSqlQuery),
    "tokenPackageCheckout": (()=>tokenPackageCheckout),
    "topUpWallet": (()=>topUpWallet),
    "unbanUserByAdmin": (()=>unbanUserByAdmin),
    "updateBlog": (()=>updateBlog),
    "updateCustomInstructions": (()=>updateCustomInstructions),
    "updateDeploymentByAdmin": (()=>updateDeploymentByAdmin),
    "updateFile": (()=>updateFile),
    "updateProject": (()=>updateProject),
    "updateProjectByAdmin": (()=>updateProjectByAdmin),
    "updateProjectPrompt": (()=>updateProjectPrompt),
    "updateProjectPrompts": (()=>updateProjectPrompts),
    "updateTechStack": (()=>updateTechStack),
    "updateUserByAdmin": (()=>updateUserByAdmin),
    "uploadFile": (()=>uploadFile),
    "useCheckAndStart": (()=>useCheckAndStart),
    "vercelConnectDomain": (()=>vercelConnectDomain),
    "vercelDeploy": (()=>vercelDeploy),
    "vercelDomainStatus": (()=>vercelDomainStatus),
    "vercelEnvPush": (()=>vercelEnvPush),
    "vercelLink": (()=>vercelLink),
    "vercelLogin": (()=>vercelLogin),
    "vercelUnlink": (()=>vercelUnlink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/auth-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8000") || "http://localhost:8000";
const getToken = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const cookies = document.cookie.split("; ");
        const tokenCookie = cookies.find((row)=>row.startsWith("access_token="));
        if (!tokenCookie) {
            console.warn("No access_token cookie found");
            return null;
        }
        const token = tokenCookie.split("=")[1];
        if (!token) {
            console.warn("Access token is empty");
            return null;
        }
        return token;
    } catch (error) {
        console.error("Error parsing access token from cookies:", error);
        return null;
    }
};
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    withCredentials: true,
    headers: {
        "Content-Type": "application/json"
    }
});
api.interceptors.request.use(async (config)=>{
    let token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])("access_token");
    if (!token) {
        token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemWithExpiry"])("access_token");
        if (token) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Token found in localStorage but not in cookie, restoring cookie");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("access_token", token, 600 * 60 * 1000);
        }
    }
    if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
    } else {
        console.warn("No valid token found for API request");
        const cookieStatus = document.cookie ? "Cookies exist" : "No cookies found";
        const tokenCookie = document.cookie.split("; ").find((row)=>row.startsWith("access_token="));
        console.warn(`Cookie status: ${cookieStatus}, Access token cookie: ${tokenCookie ? "present" : "missing"}`);
        if (!config.url?.includes("/logout") && !config.url?.includes("/kinde_user") && "object" !== "undefined") {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Attempting to refresh auth state before proceeding with request");
            try {
                const authData = localStorage.getItem("auth-storage");
                if (authData) {
                    const parsedData = JSON.parse(authData);
                    if (parsedData?.state?.user?.access_token) {
                        token = parsedData.state.user.access_token;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Found token in auth store, applying to request");
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("access_token", token, 600 * 60 * 1000);
                        config.headers["Authorization"] = `Bearer ${token}`;
                    } else {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("No valid token in auth store, clearing persisted state");
                        localStorage.removeItem("auth-storage");
                    }
                }
            } catch (e) {
                console.error("Error trying to restore auth state:", e);
                localStorage.removeItem("auth-storage");
            }
        }
    }
    return config;
}, (error)=>{
    console.error("Error in request interceptor:", error);
    return Promise.reject(error);
});
const addErrorHandling = (axiosInstance)=>{
    axiosInstance.interceptors.response.use({
        "addErrorHandling.use": (response)=>response
    }["addErrorHandling.use"], {
        "addErrorHandling.use": async (error)=>{
            console.error("API Error:", error.response);
            if (error.response && error.response.status === 401) {
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.clear();
                    localStorage.removeItem("auth-storage");
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearAllCookies"])();
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("access_token");
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("kinde_id");
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("access_token");
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("kinde_id");
                if ("TURBOPACK compile-time truthy", 1) {
                    window.location.href = "/";
                    window.location.reload();
                }
            }
            if (error.response && error.response.data) {
                throw error.response.data;
            }
            throw error;
        }
    }["addErrorHandling.use"]);
};
addErrorHandling(api);
const handleApiError = (error, customMessage)=>{
    console.error(customMessage, error);
    throw error;
};
const createProject = async (name, stack)=>{
    try {
        const response = await api.post("/project", null, {
            params: {
                name,
                stack
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Create project error:");
    }
};
const getProject = async (projectId)=>{
    try {
        const response = await api.get(`/project/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get project error:");
    }
};
const deleteProject = async (projectId)=>{
    try {
        const response = await api.delete(`/project/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Delete project error:");
    }
};
const listProjects = async ()=>{
    try {
        const response = await api.get("/projects");
        return response.data;
    } catch (error) {
        handleApiError(error, "List projects error:");
    }
};
const updateProject = async (projectId, updateData)=>{
    try {
        const response = await api.put(`/project/${projectId}`, {}, {
            params: updateData
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Update project error:");
    }
};
const startAgent = async ({ threadId, workspaceId, projectId, maxIterations = 5, userInput, agentType = "frontend", selectedPaths = [], objectiveImages = [], token })=>{
    try {
        const response = await api.post("/agent/start", {
            thread_id: threadId,
            workspace_id: workspaceId,
            project_id: projectId,
            max_iterations: maxIterations,
            user_input: userInput,
            agent_type: agentType,
            selected_paths: selectedPaths,
            objective_images: objectiveImages,
            token: token
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Start agent error:");
    }
};
const stopAgent = async (threadId)=>{
    try {
        const response = await api.post(`/agent/stop/${threadId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Stop session error:");
    }
};
const getProjectThreads = async (projectId)=>{
    try {
        const response = await api.get(`/project/${projectId}/threads`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get project threads error:");
    }
};
const getThread = async (threadId)=>{
    try {
        const response = await api.get(`/thread/${threadId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get thread error:");
    }
};
const createThread = async (projectId, pageRoute)=>{
    try {
        const response = await api.post("/thread", null, {
            params: {
                project_id: projectId,
                page_route: pageRoute
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Create thread error:");
    }
};
const startSessionRun = async (projectId, threadId, objective, objectiveImages, forceCommunicationInterval, mode = "interactive", promptSet = "1", autonomousIterations = 20, selectedPaths = [], model_id = "1")=>{
    try {
        const formData = new FormData();
        formData.append("project_id", projectId);
        if (threadId) formData.append("thread_id", threadId);
        formData.append("objective", objective);
        formData.append("force_communication_interval", forceCommunicationInterval.toString());
        formData.append("mode", mode);
        formData.append("prompt_set", promptSet);
        formData.append("autonomous_iterations", autonomousIterations.toString());
        formData.append("selected_paths", selectedPaths.join(","));
        formData.append("model_id", model_id);
        if (objectiveImages && objectiveImages.length > 0) {
            objectiveImages.forEach((image)=>{
                formData.append("objective_images", image);
            });
        }
        const response = await api.post("/start_session_run", formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Start session run error:");
    }
};
const addMessageToThread = async (threadId, content, images = [])=>{
    try {
        const formData = new FormData();
        formData.append("content", content);
        // Validate and append each image
        images.forEach((image, index)=>{
            if (!(image instanceof File) || !image.type.startsWith("image/")) {
                throw new Error(`Invalid image file at index ${index}`);
            }
            formData.append("images", image);
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Sending message with form data:", {
            content,
            imageCount: images.length,
            imageTypes: images.map((img)=>img.type)
        });
        const response = await api.post(`/thread/${threadId}/add_message`, formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            },
            // Add timeout for large file uploads
            timeout: 30000
        });
        if (!response.data) {
            throw new Error("No response data received from server");
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("File uploaded successfully:", response.data);
        return response.data;
    } catch (error) {
        const err = error;
        if (err.detail && err.detail.includes("Incomplete tool responses")) {
            throw new Error("Wait a short moment. You have to wait for the Agent to complete his actions, then you can send the message.");
        }
        console.error("Error adding message to thread:", err);
        throw new Error(err.detail || err.message || "Failed to add message to thread");
    }
};
const getAgentStatus = async (threadId)=>{
    try {
        const response = await api.get(`/agent/status/${threadId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get agent status error:");
    }
};
const getThreadLlmMessages = async (threadId, { hideToolMsgs = false, onlyLatestAssistant = false, lastNLlmMessages } = {})=>{
    try {
        const params = new URLSearchParams({
            ...hideToolMsgs && {
                hide_tool_msgs: hideToolMsgs.toString()
            },
            ...onlyLatestAssistant && {
                only_latest_assistant: onlyLatestAssistant.toString()
            },
            ...lastNLlmMessages !== undefined && {
                last_n_llm_messages: lastNLlmMessages.toString()
            }
        });
        const response = await api.get(`/threads/${threadId}/llm_history_messages?${params}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get thread LLM messages error:");
    }
};
const getThreadSessionStatus = async (threadId)=>{
    try {
        const response = await api.get(`/thread_session_status/${threadId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get thread session status error:");
    }
};
const getProjectActiveSessionStatus = async (projectId)=>{
    try {
        const response = await api.get(`/project_active_session_status/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get project active session status error:");
    }
};
const getKindeUser = async (kindeId, logout)=>{
    try {
        const response = await api.get(`/kinde_user/${kindeId}`);
        return response.data;
    } catch (error) {
        if (logout) {
            logout();
        }
        handleApiError(error, "Get Kinde user error:");
    }
};
const subscriptionCheckout = async (plan, kindeId = null, isUpgrade = false, toltReferral = null, discountCode = null)=>{
    try {
        const params = {
            plan,
            kinde_id: kindeId,
            is_upgrade: isUpgrade
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("subscriptionCheckout", params);
        if (toltReferral) {
            params.tolt_referral = toltReferral;
        }
        if (discountCode) {
            params.discount_code = discountCode;
        }
        const response = await api.post("/subscription_checkout", null, {
            params
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Subscription checkout error:");
    }
};
const oneTimePaymentCheckout = async (plan, kindeId = null)=>{
    try {
        const response = await api.post("/one_time_payment_checkout", null, {
            params: {
                plan,
                kinde_id: kindeId
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "One-time payment checkout error:");
    }
};
const freeRequestCheckout = async (email, package_total_free_request)=>{
    try {
        const response = await api.post("/free_request_checkout", null, {
            params: {
                email,
                package_total_free_request
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Free request checkout error:");
    }
};
const createCustomerPortalSession = async (customerId)=>{
    try {
        const response = await api.post("/customer_portal_session", null, {
            params: {
                customer_id: customerId
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Create customer portal session error:");
    }
};
const submitFeedback = async (feedbackData)=>{
    try {
        const response = await api.post("/feedback", feedbackData);
        return response.data;
    } catch (error) {
        handleApiError(error, "Submit feedback error:");
    }
};
const submitAnswer = async (threadId, promptId, answer)=>{
    try {
        const formData = new FormData();
        formData.append("prompt_id", promptId);
        formData.append("answer", answer);
        const response = await api.post(`/thread/${threadId}/submit_answer`, formData, {
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Submit answer error:");
    }
};
const useCheckAndStart = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCheckAndStart.useMutation": async (project_id)=>{
                try {
                    const response = await api.post("/check_and_start_env", {
                        project_id
                    });
                    return response.data;
                } catch (error) {
                    handleApiError(error, "Check and start error:");
                }
            }
        }["useCheckAndStart.useMutation"]
    });
};
_s(useCheckAndStart, "wwwtpB20p0aLiHIvSy5P98MwIUg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const getGithubInfo = async (projectId)=>{
    try {
        const response = await api.get(`/github-info/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get GitHub info error:");
    }
};
const addGithubCollaborator = async (projectId, username, github_repo)=>{
    try {
        const response = await api.post(`/git-add-collaborator/${projectId}`, JSON.stringify({
            username,
            github_repo
        }), {
            headers: {
                "Content-Type": "application/json"
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Add GitHub collaborator error:");
    }
};
const revertGithubCommit = async (projectId, commitHash, revertAndFullReset = false)=>{
    try {
        const response = await api.post(`/git-revert-commit/${projectId}`, JSON.stringify({
            commit: {
                commit_hash: commitHash
            },
            revert_and_full_reset: revertAndFullReset
        }), {
            headers: {
                "Content-Type": "application/json"
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Revert GitHub commit error:");
    }
};
const getFiles = async (project_id, path = "/")=>{
    try {
        const response = await api.get(`/get-files/${project_id}`, {
            params: {
                path
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error fetching files:", error);
        const err = error;
        if (err.response) {
            if (err.response.status === 404) {
                console.warn(`No files found for project ${project_id} at path ${path}`);
                return {
                    files: []
                };
            }
            console.error("Response status:", err.response.status);
            console.error("Response headers:", err.response.headers);
            throw new Error(`Server error: ${err.response.status} - ${err.response.data?.message || "Unknown error"}`);
        } else if (err.request) {
            throw new Error("No response received from server. Please check your network connection.");
        } else {
            throw new Error(`Error setting up request: ${err.message}`);
        }
    }
};
const getFileContent = async (project_id, filePath)=>{
    try {
        const response = await api.get(`/get-file-content/${project_id}`, {
            params: {
                file_path: filePath
            },
            responseType: "arraybuffer"
        });
        const contentType = response.headers["content-type"];
        const fileName = filePath.split("/").pop() || "";
        if (contentType.startsWith("text/") || contentType === "application/json" || fileName.startsWith(".")) {
            const textContent = new TextDecoder().decode(response.data);
            if (contentType === "application/json") {
                return JSON.parse(textContent);
            }
            return textContent;
        } else {
            const blob = new Blob([
                response.data
            ], {
                type: contentType
            });
            return URL.createObjectURL(blob);
        }
    } catch (error) {
        console.error("Error fetching file content:", error);
        const err = error;
        if (err.response) {
            throw new Error(`Failed to fetch file content. Server returned ${err.response.status}`);
        } else if (err.request) {
            throw new Error("No response received from server. Please check your network connection.");
        } else {
            throw new Error(`Error setting up request: ${err.message}`);
        }
    }
};
const createFile = async (project_id, filePath, content = "", isDirectory = false)=>{
    try {
        const response = await api.post("/create-file", {
            project_id,
            path: filePath,
            content,
            isDirectory
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Create file error:");
    }
};
const updateFile = async (project_id, filePath, content)=>{
    try {
        const response = await api.put("/update-file", {
            project_id,
            path: filePath,
            content
        });
        return response.data;
    } catch (error) {
        console.error("Update file error:", error);
        throw error;
    }
};
const deleteFile = async (project_id, filePath)=>{
    try {
        const response = await api.delete("/delete-file", {
            params: {
                project_id,
                path: filePath
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Delete file error:");
    }
};
const executeCommand = async (project_id, command)=>{
    try {
        const response = await api.post("/execute-command", {
            project_id,
            command
        });
        return response.data;
    } catch (error) {
        const err = error;
        if (err.response?.status === 422) {
            console.error("Validation error during command execution:", err.response.data);
            const errorDetail = err.response.data && typeof err.response.data === "object" && "detail" in err.response.data ? Array.isArray(err.response.data.detail) ? err.response.data.detail.map((e)=>e.msg).join(", ") : String(err.response.data.detail) : "Invalid command parameters";
            throw new Error(`Command execution validation failed: ${errorDetail}`);
        }
        handleApiError(error, "Execute command error:");
    }
};
const gitCommit = async (project_id, commit_message)=>{
    try {
        const response = await api.post("/git-commit", {
            project_id,
            commit_message
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Git commit error:");
    }
};
async function uploadFile({ projectId, file, relativePath, onProgress }) {
    try {
        if (!(file instanceof File)) {
            throw new Error("Invalid file object provided");
        }
        const formData = new FormData();
        formData.append("file", file);
        formData.append("relativePath", relativePath || "");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Uploading file:", {
            name: file.name,
            type: file.type,
            size: file.size,
            path: relativePath
        });
        const response = await api.post(`/upload-file`, formData, {
            params: {
                project_id: projectId
            },
            headers: {
                "Content-Type": "multipart/form-data"
            },
            timeout: 60000,
            onUploadProgress: (progressEvent)=>{
                if (progressEvent.total && onProgress) {
                    const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);
                    onProgress(percentCompleted);
                }
            }
        });
        if (!response.data) {
            throw new Error("No data received from server");
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("File uploaded successfully:", response.data);
        return response.data;
    } catch (error) {
        const err = error;
        console.error("Error uploading file:", {
            error: err.response?.data || err.message,
            status: err.response?.status
        });
        if (err.response?.status === 422) {
            const validationError = err.response.data;
            const errorMessage = validationError.detail ? Array.isArray(validationError.detail) ? validationError.detail.map((e)=>e.msg).join(", ") : String(validationError.detail) : "Validation error during file upload";
            throw new Error(errorMessage);
        }
        if (err.response?.status === 413) {
            throw new Error("File size too large. Please try a smaller file.");
        } else if (err.response?.status === 415) {
            throw new Error("Unsupported file type. Please check the file format.");
        } else if (err.response?.status === 408) {
            throw new Error("Upload timeout. Please try again.");
        }
        throw new Error(err.message || "Failed to upload file. Please try again.");
    }
}
const moveFile = async (project_id, sourcePath, targetPath)=>{
    try {
        const response = await api.post("/move-file", {
            project_id,
            sourcePath,
            targetPath
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Move file error:");
    }
};
{
/*
    ===========================
        Code Editor Route
    ===========================
*/ }const getTemplateByIdentifier = async (identifier)=>{
    try {
        const response = await api.get(`/templates/${identifier}`);
        return response.data;
    } catch (error) {
        const err = error;
        console.error(`Error fetching template with id ${identifier}:`, err);
        if (err.response?.status === 404) {
            throw new Error("Template not found");
        }
        throw new Error("Failed to fetch template");
    }
};
const setupVercelCLI = async (projectId, projectName, vercelToken)=>{
    try {
        const response = await api.post("/vercel/setup-cli", {
            project_id: projectId,
            project_name: projectName,
            vercel_token: vercelToken
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Setup Vercel CLI error:");
    }
};
const vercelLogin = async (projectId, projectName, vercelToken)=>{
    try {
        const response = await api.post("/vercel/login", {
            project_id: projectId,
            project_name: projectName,
            vercel_token: vercelToken
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Vercel login error:");
    }
};
const vercelLink = async (projectId, projectName, vercelToken)=>{
    try {
        const response = await api.post("/vercel/link", {
            project_id: projectId,
            project_name: projectName,
            vercel_token: vercelToken
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Vercel link error:");
    }
};
const vercelEnvPush = async (projectId, projectName, vercelToken)=>{
    try {
        const response = await api.post("/vercel/env-push", {
            project_id: projectId,
            project_name: projectName,
            vercel_token: vercelToken
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Vercel env push error:");
    }
};
const vercelDeploy = async (projectId, projectName, vercelToken)=>{
    try {
        const response = await api.post("/vercel/deploy", {
            project_id: projectId,
            project_name: projectName,
            vercel_token: vercelToken
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Vercel deploy error:");
    }
};
const activateUser = async (kindeId)=>{
    try {
        const response = await api.post(`/activate_user/${kindeId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Activate user error:");
    }
};
const startSessionRunWithWebSocket = async (projectId, threadId, objective, objectiveImages, forceCommunicationInterval, mode = "interactive", promptSet = "1", autonomousIterations = 20, selectedPaths = [], model_id = "1")=>{
    try {
        const token = getToken();
        const wsUrl = `${API_BASE_URL.replace("http", "ws")}/start_session_run?token=${token}`;
        const socket = new WebSocket(wsUrl);
        socket.onopen = ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("WebSocket connection established for session run");
            const encodeImageAsBase64 = (file)=>{
                return new Promise((resolve, reject)=>{
                    const reader = new FileReader();
                    reader.readAsDataURL(file);
                    reader.onload = ()=>resolve(reader.result.split(",")[1]);
                    reader.onerror = (error)=>reject(error);
                });
            };
            Promise.all(objectiveImages.map((file)=>encodeImageAsBase64(file))).then((encodedImages)=>{
                const message = JSON.stringify({
                    project_id: projectId,
                    thread_id: threadId,
                    objective: objective,
                    objective_images: encodedImages,
                    force_communication_interval: forceCommunicationInterval,
                    mode: mode,
                    prompt_set: parseInt(promptSet, 10),
                    autonomous_iterations: autonomousIterations,
                    selected_paths: selectedPaths,
                    model_id: model_id
                });
                socket.send(message);
            }).catch((error)=>{
                console.error("Error encoding images:", error);
                handleApiError(error, "Start session run with WebSocket error:");
            });
        };
        socket.onerror = (error)=>{
            console.error("WebSocket error:", error);
        };
        socket.onclose = (event)=>{
            if (!event.wasClean) {
                console.warn(`WebSocket closed unexpectedly, code: ${event.code}`);
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("WebSocket connection closed");
            }
        };
        return socket;
    } catch (error) {
        handleApiError(error, "Start session run with WebSocket error:");
    }
};
const connectTerminalSession = (project_id)=>{
    if (!project_id) {
        console.error("No project_id provided to connectTerminalSession");
        return null;
    }
    const token = getToken();
    const wsUrl = `${API_BASE_URL.replace("http", "ws")}/terminal_session?token=${token}&project_id=${project_id}&session_id=${project_id || "2"}`;
    const socket = new WebSocket(wsUrl);
    socket.onopen = ()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Terminal WebSocket connection established");
    };
    socket.onerror = (error)=>{
        console.error("Terminal WebSocket error:", error);
    };
    socket.onclose = (event)=>{
        if (!event.wasClean) {
            console.warn(`Terminal WebSocket closed unexpectedly, code: ${event.code}`);
        } else {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Terminal WebSocket connection closed");
        }
    };
    return socket;
};
const getFilePaths = async (projectId, fullPath, depth = 4)=>{
    try {
        const response = await api.post("/get-file-paths", {
            project_id: projectId,
            full_path: fullPath,
            depth: depth
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Get file paths error:");
    }
};
const enhancePrompt = async (prompt)=>{
    try {
        const response = await api.post("/project/enhance-prompt", {
            prompt
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Enhance prompt error:");
    }
};
const vercelUnlink = async (projectId)=>{
    try {
        const response = await api.post("/vercel/unlink", {
            project_id: projectId
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Vercel unlink error:");
    }
};
const createBlog = async (headline, metaDescription, image, categoryTitle, tags, mdx)=>{
    try {
        const blogData = {
            headline,
            meta_description: metaDescription,
            image,
            category_title: categoryTitle,
            tags,
            mdx
        };
        const response = await api.post("/blogs", blogData);
        return response.data;
    } catch (error) {
        handleApiError(error, "Create blog error:");
    }
};
const updateBlog = async (blogId, headline, metaDescription, image, categoryTitle, tags, mdx)=>{
    try {
        const blogData = {
            headline,
            meta_description: metaDescription,
            image,
            category_title: categoryTitle,
            tags,
            mdx
        };
        const updateData = Object.fromEntries(Object.entries(blogData).filter(([, value])=>value !== undefined));
        const response = await api.put(`/blogs/${blogId}`, updateData);
        return response.data;
    } catch (error) {
        handleApiError(error, "Update blog error:");
    }
};
const listBlogs = async (limit = null, offset = null)=>{
    try {
        const response = await api.get("/blogs");
        const allBlogs = Array.isArray(response.data) ? response.data : response.data?.items || [];
        const total = allBlogs.length;
        let paginatedBlogs = allBlogs;
        if (limit !== null && offset !== null) {
            paginatedBlogs = allBlogs.slice(offset, offset + limit);
        }
        return {
            items: paginatedBlogs,
            total: total
        };
    } catch (error) {
        console.error("List blogs error:", error);
        return {
            items: [],
            total: 0
        };
    }
};
const getBlog = async (blogId)=>{
    try {
        const response = await api.get(`/blogs/${blogId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get blog error:");
    }
};
const getBlogBySlug = async (slug)=>{
    try {
        const response = await api.get(`/blogs/slug/${slug}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get blog by slug error:");
    }
};
const deleteBlog = async (blogId)=>{
    try {
        const response = await api.delete(`/blogs/${blogId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Delete blog error:");
    }
};
const getTokenUsage = async (customerId, tokenEventName)=>{
    try {
        const response = await api.get(`/token_usage/${customerId}`, {
            params: {
                token_event_name: tokenEventName
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Get token usage error:");
    }
};
const remixProject = async (projectId, envValues)=>{
    try {
        const response = await api.post(`/project/${projectId}/remix`, {
            env_values: envValues
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Remix project error:");
    }
};
const addTeamMember = async (projectId, teamEmail)=>{
    try {
        const response = await api.post(`/project/${projectId}/team`, {
            team_email: teamEmail
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Add team member error:");
    }
};
const removeTeamMember = async (projectId, teamEmail)=>{
    try {
        const response = await api.delete(`/project/${projectId}/team/${teamEmail}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Remove team member error:");
    }
};
const getEnvKeys = async (projectId)=>{
    try {
        const response = await api.get(`/get-env-keys/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get environment keys error:");
    }
};
const submitPrize = async (url, projectId, userId, platform)=>{
    try {
        const response = await api.post("/prize-submission", {
            url,
            project_id: projectId,
            user_id: userId,
            description: "project-social-share",
            prize_amount: 500000,
            platform
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Prize submission error:");
    }
};
const renameThread = async (projectId, threadId, newName)=>{
    try {
        const formData = new FormData();
        formData.append("new_name", newName);
        const response = await api.patch(`/thread/${projectId}/${threadId}/rename`, formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Rename thread error:");
    }
};
const deleteThread = async (projectId, threadId)=>{
    try {
        const response = await api.delete(`/thread/${projectId}/${threadId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Delete thread error:");
    }
};
const tokenPackageCheckout = async (packageSize, kindeId)=>{
    try {
        const response = await api.post("/token_package_checkout", null, {
            params: {
                package_size: packageSize,
                kinde_id: kindeId
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Token package checkout error:");
    }
};
const gitForcePull = async (projectId)=>{
    try {
        const response = await api.post(`/git-force-pull/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Git force pull error:");
    }
};
const updateTechStack = async (projectId, techStackArray)=>{
    try {
        const response = await api.post(`/project/${projectId}/tech-stack`, {
            tech_stack_array: techStackArray
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Update tech stack error:");
    }
};
const updateCustomInstructions = async (projectId, customInstructions)=>{
    try {
        const response = await api.post(`/project/${projectId}/custom-instructions`, {
            custom_instructions: customInstructions
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Update custom instructions error:");
    }
};
const vercelConnectDomain = async (projectId, domain, vercelToken)=>{
    try {
        const response = await api.post("/vercel-domain-connect", {
            project_id: projectId,
            domain,
            vercel_token: vercelToken
        });
        return response.data;
    } catch (error) {
        console.error("Vercel domain connect error:", error);
        handleApiError(error, "Vercel domain connect error:");
    }
};
const vercelDomainStatus = async (projectId, domain, vercelToken)=>{
    try {
        const response = await api.post("/vercel/domain-status", {
            project_id: projectId,
            domain,
            vercel_token: vercelToken
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Vercel domain status check error:");
    }
};
const getTeamMembers = async (projectId)=>{
    try {
        const response = await api.get(`/project/${projectId}/team`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get team members error:");
    }
};
const getUsersByAdmin = async ({ skip = 0, limit = 100, search = "" })=>{
    try {
        const params = {
            skip,
            limit
        };
        if (search) params.search = search;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Requesting users with params:", params);
        const response = await api.get("/admin/users", {
            params
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("API Response structure:", {
            hasUsers: !!response.data.users,
            usersCount: response.data.users?.length || 0,
            total: response.data.total || 0,
            isArray: Array.isArray(response.data)
        });
        // Handle array response (list of users without pagination info)
        if (Array.isArray(response.data)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Converting array response to paginated format");
            return {
                users: response.data,
                total: response.data.length
            };
        }
        // If the API already returns the structured format with pagination, use it
        return response.data;
    } catch (error) {
        console.error("Get users by admin error:", error);
        // Return empty result instead of throwing to prevent UI from breaking
        return {
            users: [],
            total: 0
        };
    }
};
const getUserByEmailAdmin = async (email)=>{
    try {
        const response = await api.get(`/admin/users/${email}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get user by email admin error:");
    }
};
const updateUserByAdmin = async (kindeId, userData)=>{
    try {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Updating user with ID:", kindeId);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Update data:", userData);
        const response = await api.put(`/admin/users/${kindeId}`, userData);
        return response.data;
    } catch (error) {
        handleApiError(error, "Update user by admin error:");
    }
};
const getUserProjectsByAdmin = async (email)=>{
    try {
        const response = await api.get(`/admin/users/${email}/projects`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get user projects by admin error:");
    }
};
const getProjectByAdmin = async (projectId)=>{
    try {
        const response = await api.get(`/admin/projects/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get project by admin error:");
    }
};
const updateProjectByAdmin = async (projectId, projectData)=>{
    try {
        const response = await api.put(`/admin/projects/${projectId}`, projectData);
        return response.data;
    } catch (error) {
        handleApiError(error, "Update project by admin error:");
    }
};
const updateDeploymentByAdmin = async (projectId, deploymentData)=>{
    try {
        const response = await api.put(`/admin/projects/${projectId}/deployment`, deploymentData);
        return response.data;
    } catch (error) {
        handleApiError(error, "Update deployment by admin error:");
    }
};
const getBillingDetailsByAdmin = async (customerId)=>{
    try {
        const response = await api.get(`/admin/billing/customer/${customerId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get billing details by admin error:");
    }
};
const downloadStripeAnalyticsByAdmin = async ()=>{
    try {
        // This will return a blob for download
        const response = await api.get("/admin/billing/analytics/download", {
            responseType: "blob"
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Download stripe analytics by admin error:");
    }
};
const authorizeSupabase = async (projectId)=>{
    try {
        const response = await api.post("/supabase/authorize", {
            project_id: projectId
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Supabase authorization error:");
    }
};
const deleteSupabaseOrganization = async (organizationId)=>{
    try {
        const response = await api.delete(`/supabase/organizations/${organizationId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Delete Supabase organization error:");
    }
};
const getSupabaseOrganizations = async (projectId)=>{
    try {
        const params = projectId ? {
            project_id: projectId
        } : {};
        const response = await api.get("/supabase/organizations", {
            params
        });
        return response.data;
    } catch (error) {
        console.error("Get Supabase organizations error:", error instanceof Error ? error.message : error?.response?.data || "Unknown error");
        // Return a structured error response instead of throwing
        return {
            success: false,
            organizations: []
        };
    }
};
const getSupabaseProjects = async (projectId, organizationId)=>{
    try {
        const response = await api.get(`/supabase/organization/${organizationId}/projects`);
        return response.data;
    } catch (error) {
        console.error("Get Supabase projects error:", error instanceof Error ? error.message : error?.response?.data || "Unknown error");
        // Return a structured error response instead of throwing
        return {
            success: false,
            projects: []
        };
    }
};
const testSqlQuery = async (projectId, query)=>{
    try {
        const response = await api.post(`/supabase/project/${projectId}/sql`, {
            query
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Test SQL query error:");
    }
};
const connectSupabaseProject = async (projectId, supabaseProjectId, organizationId, organizationName, accessToken, refreshToken, apiKey, apiUrl, databasePassword)=>{
    try {
        const response = await api.post("/supabase/project/connect", {
            project_id: projectId,
            supabase_project_id: supabaseProjectId,
            organization_id: organizationId,
            organization_name: organizationName,
            access_token: accessToken,
            refresh_token: refreshToken,
            api_key: apiKey,
            api_url: apiUrl,
            database_password: databasePassword
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Connect Supabase project error:");
    }
};
const disconnectSupabase = async (projectId)=>{
    try {
        const response = await api.delete(`/supabase/disconnect/${projectId}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Disconnect Supabase error:");
    }
};
const deleteUserByAdmin = async (email, confirm = false)=>{
    try {
        const response = await api.post("/admin/users/delete", {
            email,
            confirm
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Delete user error:");
    }
};
const updateProjectPrompts = async (projectId, promptsArray)=>{
    try {
        const response = await api.post(`/project/${projectId}/prompts`, {
            prompts_array: promptsArray
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Update project prompts error:");
    }
};
const getProjectPrompts = async (projectId)=>{
    try {
        const response = await api.get(`/project/${projectId}/prompts`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get project prompts error:");
    }
};
const deleteProjectPrompt = async (projectId, promptKey)=>{
    try {
        const response = await api.delete(`/project/${projectId}/prompts/${promptKey}`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Delete project prompt error:");
    }
};
const updateProjectPrompt = async (projectId, promptKey, promptValue)=>{
    try {
        const response = await api.put(`/project/${projectId}/prompts/${promptKey}`, {
            key: promptKey,
            value: promptValue
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Update project prompt error:");
    }
};
const connectVercelDeployWebSocket = ({ projectId, vercelToken, startStep })=>{
    try {
        const token = getToken();
        const wsUrl = `${API_BASE_URL.replace("http", "ws")}/vercel-deploy?token=${token}&project_id=${projectId}&start_step=${startStep}${vercelToken ? `&vercel_token=${vercelToken}` : ""}`;
        const socket = new WebSocket(wsUrl);
        socket.onopen = ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Vercel deployment WebSocket connection established");
        };
        socket.onmessage = (event)=>{
            try {
                const data = JSON.parse(event.data);
                return data;
            } catch (error) {
                console.error("Error handling WebSocket message:", error);
                throw error;
            }
        };
        socket.onerror = (error)=>{
            console.error("Vercel deployment WebSocket error:", error);
        };
        socket.onclose = (event)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])(`Vercel deployment WebSocket closed: ${event.code} ${event.reason}`);
        };
        return socket;
    } catch (error) {
        handleApiError(error, "Vercel deployment WebSocket connection error:");
        throw error;
    }
};
const getOpenFiles = async (project_id)=>{
    try {
        const response = await api.get(`/get-open-files/${project_id}`);
        return response.data;
    } catch (error) {
        console.error("Error fetching open files:", error);
        throw error;
    }
};
const openFilesInEditor = async (project_id, files)=>{
    try {
        const response = await api.post(`/open-files-in-editor/${project_id}`, {
            files
        });
        return response.data;
    } catch (error) {
        console.error("Error opening files:", error);
        throw error;
    }
};
const closeFilesInEditor = async (project_id, files)=>{
    try {
        const response = await api.post(`/close-files-in-editor/${project_id}`, {
            files
        });
        return response.data;
    } catch (error) {
        console.error("Error closing files:", error);
        throw error;
    }
};
const connectRemixProjectWebSocket = ({ projectId, envValues = null })=>{
    try {
        const token = getToken();
        let wsUrl = `${API_BASE_URL.replace("http", "ws")}/project/${projectId}/remix?token=${token}`;
        // Add env_values as query parameter if provided
        if (envValues) {
            const envValuesString = JSON.stringify(envValues);
            wsUrl += `&env_values=${encodeURIComponent(envValuesString)}`;
        }
        const socket = new WebSocket(wsUrl);
        socket.onopen = ()=>{
            console.log("Remix project WebSocket connection established");
        };
        socket.onmessage = (event)=>{
            try {
                const data = JSON.parse(event.data);
                return data;
            } catch (error) {
                console.error("Error handling WebSocket message:", error);
                throw error;
            }
        };
        socket.onerror = (error)=>{
            console.error("Remix project WebSocket error:", error);
        };
        socket.onclose = (event)=>{
            console.log(`Remix project WebSocket closed: ${event.code} ${event.reason}`);
        };
        return socket;
    } catch (error) {
        handleApiError(error, "Remix project WebSocket connection error:");
        throw error;
    }
};
const banUserByAdmin = async ({ email })=>{
    try {
        const response = await api.post("/admin/users/ban", {
            email
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Ban user error:");
    }
};
const unbanUserByAdmin = async ({ email })=>{
    try {
        const response = await api.post("/admin/users/unban", {
            email
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Unban user error:");
    }
};
const getSupabaseProjectStatus = async ({ supabaseProjectId })=>{
    try {
        const response = await api.post("/supabase/project/status", {
            supabase_project_id: supabaseProjectId
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Get Supabase project status error:");
    }
};
const restoreSupabaseProject = async ({ supabaseProjectId })=>{
    try {
        const response = await api.post("/supabase/project/restore", {
            supabase_project_id: supabaseProjectId
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Restore Supabase project error:");
    }
};
const searchProjectByAdmin = async (searchTerm)=>{
    try {
        const response = await api.get(`/admin/projects/search`, {
            params: {
                search_term: searchTerm
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Search project by admin error:");
    }
};
const getUserTransactionsByAdmin = async (email)=>{
    try {
        const response = await api.get(`/admin/users/${email}/transactions`);
        return response.data;
    } catch (error) {
        handleApiError(error, "Get user transactions by admin error:");
    }
};
const connectGitHubCloneWebSocket = (projectId)=>{
    try {
        const token = getToken();
        const wsUrl = `${API_BASE_URL.replace("http", "ws")}/admin/github-clone/${projectId}?token=${token}`;
        const socket = new WebSocket(wsUrl);
        socket.onopen = ()=>{
            console.log("GitHub clone WebSocket connection established");
        };
        socket.onmessage = (event)=>{
            try {
                const data = JSON.parse(event.data);
                return data;
            } catch (error) {
                console.error("Error handling GitHub clone WebSocket message:", error);
                throw error;
            }
        };
        socket.onerror = (error)=>{
            console.error("GitHub clone WebSocket error:", error);
        };
        socket.onclose = (event)=>{
            console.log(`GitHub clone WebSocket closed: ${event.code} ${event.reason}`);
        };
        return socket;
    } catch (error) {
        handleApiError(error, "GitHub clone WebSocket connection error:");
        throw error;
    }
};
const getWallet = async ()=>{
    try {
        const response = await api.get("/wallet");
        return response.data;
    } catch (error) {
        handleApiError(error, "Get wallet error:");
    }
};
const getWalletTransactions = async ({ pageParam = undefined })=>{
    try {
        const response = await api.get("/wallet/transactions", {
            params: {
                cursor: pageParam,
                limit: 10
            }
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Get wallet transactions error:");
    }
};
const topUpWallet = async (amount)=>{
    try {
        const response = await api.post("/wallet/topup", {
            amount
        });
        return response.data;
    } catch (error) {
        handleApiError(error, "Top up wallet error:");
    }
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/auth-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$debounce$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-debounce.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/auth-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/axios@1.10.0/node_modules/axios/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
const ttl = 60 * 24 * 7 * 60 * 1000; // 7 days in milliseconds (matching backend expiry)
/**
 * Clears the persisted auth state from localStorage
 * This prevents stale auth state from persisting when tokens are expired
 */ const clearPersistedAuthState = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.removeItem("auth-storage");
    }
};
const useAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: {
            access_token: null,
            email: null,
            kinde_id: null,
            userFromDb: null,
            customer_id: null,
            isSubscribed: false,
            isLoading: true,
            showPricingDialog: false,
            cookie: {
                access_token: null,
                kinde_id: null
            }
        },
        lastCheckTimestamp: 0,
        setLastCheckTimestamp: (timestamp)=>set({
                lastCheckTimestamp: timestamp
            }),
        initializeAuth: async ()=>{
            const cookieAccessToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])("access_token");
            const cookieKindeId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])("kinde_id");
            const localAccessToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemWithExpiry"])("access_token");
            const localKindeId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemWithExpiry"])("kinde_id");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Auth state check:", {
                cookieTokenExists: !!cookieAccessToken,
                localTokenExists: !!localAccessToken,
                cookieKindeExists: !!cookieKindeId,
                localKindeExists: !!localKindeId
            });
            if (localAccessToken && localKindeId && (!cookieAccessToken || !cookieKindeId)) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Syncing tokens from localStorage to cookies");
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("access_token", localAccessToken, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("kinde_id", localKindeId, ttl);
            }
            if (cookieAccessToken && cookieKindeId && (!localAccessToken || !localKindeId)) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Syncing tokens from cookies to localStorage");
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("access_token", cookieAccessToken, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("kinde_id", cookieKindeId, ttl);
            }
            if (!cookieAccessToken && !cookieKindeId && !localAccessToken && !localKindeId) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("No auth tokens found, clearing persisted state and setting isLoading to false");
                clearPersistedAuthState();
                set((state)=>({
                        user: {
                            ...state.user,
                            isLoading: false
                        }
                    }));
                return;
            }
            const effectiveToken = cookieAccessToken || localAccessToken;
            const effectiveKindeId = cookieKindeId || localKindeId;
            if (!effectiveToken || !effectiveKindeId) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("No effective tokens found, clearing persisted state and setting isLoading to false");
                clearPersistedAuthState();
                set((state)=>({
                        user: {
                            ...state.user,
                            isLoading: false
                        }
                    }));
                return;
            }
            if (effectiveToken && effectiveKindeId) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("access_token", effectiveToken, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("kinde_id", effectiveKindeId, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("access_token", effectiveToken, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("kinde_id", effectiveKindeId, ttl);
            }
            try {
                const userFromDb = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getKindeUser"])(effectiveKindeId);
                if (!userFromDb) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("No user found in DB, clearing persisted state and setting isLoading to false");
                    clearPersistedAuthState();
                    set((state)=>({
                            user: {
                                ...state.user,
                                isLoading: false
                            }
                        }));
                    return;
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("User data retrieved successfully");
                set((state)=>({
                        user: {
                            ...state.user,
                            access_token: effectiveToken,
                            kinde_id: effectiveKindeId,
                            email: userFromDb.email,
                            userFromDb,
                            customer_id: userFromDb.stripe_customer_id || null,
                            isSubscribed: userFromDb.isSubscribed || false,
                            isLoading: false,
                            cookie: {
                                access_token: effectiveToken,
                                kinde_id: effectiveKindeId
                            }
                        }
                    }));
            } catch (error) {
                console.error("Error during auth initialization:", error);
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AxiosError"] && error.response?.status === 401) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("401 error during auth, clearing tokens and persisted state");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("access_token");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("kinde_id");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("access_token");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("kinde_id");
                    clearPersistedAuthState();
                    set((state)=>({
                            user: {
                                ...state.user,
                                access_token: null,
                                email: null,
                                kinde_id: null,
                                userFromDb: null,
                                customer_id: null,
                                isSubscribed: false,
                                isLoading: false,
                                cookie: {
                                    access_token: null,
                                    kinde_id: null
                                }
                            }
                        }));
                } else {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Non-401 error during auth, clearing persisted state and setting isLoading to false");
                    clearPersistedAuthState();
                    set((state)=>({
                            user: {
                                ...state.user,
                                isLoading: false
                            }
                        }));
                }
            }
        },
        syncTokens: ()=>{
            const cookieAccessToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])("access_token");
            const cookieKindeId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])("kinde_id");
            const localAccessToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemWithExpiry"])("access_token");
            const localKindeId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemWithExpiry"])("kinde_id");
            const effectiveToken = cookieAccessToken || localAccessToken;
            const effectiveKindeId = cookieKindeId || localKindeId;
            if (effectiveToken && effectiveKindeId) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("access_token", effectiveToken, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("kinde_id", effectiveKindeId, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("access_token", effectiveToken, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("kinde_id", effectiveKindeId, ttl);
                set((state)=>({
                        user: {
                            ...state.user,
                            access_token: effectiveToken,
                            kinde_id: effectiveKindeId,
                            cookie: {
                                access_token: effectiveToken,
                                kinde_id: effectiveKindeId
                            }
                        }
                    }));
                return true;
            }
            return false;
        },
        login: async (access_token, email, kinde_id)=>{
            try {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Login called with token and kinde_id");
                set((state)=>({
                        user: {
                            ...state.user,
                            isLoading: true
                        }
                    }));
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("access_token", access_token, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("kinde_id", kinde_id, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("access_token", access_token, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("kinde_id", kinde_id, ttl);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("email", email, ttl);
                const cookieToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCookie"])("access_token");
                const localToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemWithExpiry"])("access_token");
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Token verification after login:", {
                    cookieTokenSet: !!cookieToken,
                    localTokenSet: !!localToken,
                    tokensMatch: cookieToken === access_token && localToken === access_token
                });
                if (!cookieToken || !localToken) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Re-attempting to set tokens");
                    setTimeout(()=>{
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("access_token", access_token, ttl);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCookieWithExpiry"])("kinde_id", kinde_id, ttl);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("access_token", access_token, ttl);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setItemWithExpiry"])("kinde_id", kinde_id, ttl);
                    }, 100);
                }
                const userFromDb = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getKindeUser"])(kinde_id);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("User data retrieved during login");
                set({
                    user: {
                        access_token,
                        email,
                        kinde_id,
                        userFromDb,
                        customer_id: userFromDb?.stripe_customer_id || null,
                        isSubscribed: userFromDb?.isSubscribed || false,
                        isLoading: false,
                        showPricingDialog: false,
                        cookie: {
                            access_token,
                            kinde_id
                        }
                    }
                });
            } catch (error) {
                console.error("Error during login:", error);
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$axios$40$1$2e$10$2e$0$2f$node_modules$2f$axios$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AxiosError"] && error.response?.status === 401) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("access_token");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("kinde_id");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("access_token");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("kinde_id");
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("email");
                    clearPersistedAuthState();
                    set((state)=>({
                            user: {
                                ...state.user,
                                access_token: null,
                                email: null,
                                kinde_id: null,
                                userFromDb: null,
                                customer_id: null,
                                isSubscribed: false,
                                isLoading: false,
                                cookie: {
                                    access_token: null,
                                    kinde_id: null
                                }
                            }
                        }));
                } else {
                    clearPersistedAuthState();
                    set((state)=>({
                            user: {
                                ...state.user,
                                isLoading: false
                            }
                        }));
                }
            }
        },
        logout: ()=>{
            set({
                user: {
                    access_token: null,
                    email: null,
                    kinde_id: null,
                    userFromDb: null,
                    customer_id: null,
                    isSubscribed: false,
                    isLoading: false,
                    showPricingDialog: false,
                    cookie: {
                        access_token: null,
                        kinde_id: null
                    }
                }
            });
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("access_token");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeCookie"])("kinde_id");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("access_token");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("kinde_id");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItemWithExpiry"])("email");
            clearPersistedAuthState();
            localStorage.clear();
            const postLogoutRedirect = encodeURIComponent(("TURBOPACK compile-time value", "http://localhost:3000"));
            const logoutUrl = `${("TURBOPACK compile-time value", "http://localhost:8000")}/logout?post_logout_redirect_uri=${postLogoutRedirect}`;
            window.location.href = logoutUrl;
        },
        checkSubscription: async (kinde_id, initialUserData)=>{
            const now = Date.now();
            const lastCheck = get().lastCheckTimestamp;
            if (now - lastCheck < 60000 && initialUserData) {
                if (initialUserData && !initialUserData.plan && initialUserData.total_free_request <= 0) {
                    window.location.href = "/pricing";
                }
                return initialUserData;
            }
            try {
                const userFromDb = initialUserData || await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getKindeUser"])(kinde_id);
                if (!userFromDb?.plan && userFromDb?.total_free_request <= 0) {
                    window.location.href = "/pricing";
                    return userFromDb;
                }
                if (userFromDb?.is_creating_project) {
                    const checkInterval = setInterval(async ()=>{
                        const currentTime = Date.now();
                        const lastCheckTime = get().lastCheckTimestamp;
                        if (currentTime - lastCheckTime < 1000) {
                            return;
                        }
                        try {
                            const updatedUser = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getKindeUser"])(kinde_id);
                            set((state)=>({
                                    user: {
                                        ...state.user,
                                        userFromDb: updatedUser,
                                        isSubscribed: updatedUser?.isSubscribed || false
                                    },
                                    lastCheckTimestamp: currentTime
                                }));
                            if (!updatedUser.is_creating_project) {
                                clearInterval(checkInterval);
                            }
                        } catch (error) {
                            console.error("Error checking project status:", error);
                        }
                    }, 1000);
                    setTimeout(()=>clearInterval(checkInterval), 5 * 60 * 1000);
                }
                set((state)=>({
                        user: {
                            ...state.user,
                            userFromDb,
                            isSubscribed: userFromDb?.isSubscribed || false,
                            isLoading: false
                        },
                        lastCheckTimestamp: now
                    }));
                return userFromDb;
            } catch (error) {
                console.error("Error checking subscription:", error);
                set((state)=>({
                        user: {
                            ...state.user,
                            isLoading: false
                        }
                    }));
                return null;
            }
        }
    }), {
    name: "auth-storage",
    partialize: (state)=>({
            user: {
                access_token: state.user.access_token,
                email: state.user.email,
                kinde_id: state.user.kinde_id,
                customer_id: state.user.customer_id,
                isSubscribed: state.user.isSubscribed,
                showPricingDialog: state.user.showPricingDialog,
                userFromDb: state.user.userFromDb
            },
            lastCheckTimestamp: state.lastCheckTimestamp
        })
}));
const AuthProvider = ({ children })=>{
    _s();
    const initializeAuth = useAuth({
        "AuthProvider.useAuth[initializeAuth]": (state)=>state.initializeAuth
    }["AuthProvider.useAuth[initializeAuth]"]);
    const syncTokens = useAuth({
        "AuthProvider.useAuth[syncTokens]": (state)=>state.syncTokens
    }["AuthProvider.useAuth[syncTokens]"]);
    const user = useAuth({
        "AuthProvider.useAuth[user]": (state)=>state.user
    }["AuthProvider.useAuth[user]"]);
    const intervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const syncIntervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { debouncedValue: debouncedUser } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$debounce$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDebounce"])(user, 300);
    const handleVisibilityChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[handleVisibilityChange]": ()=>{
            if (document.visibilityState === "visible") {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Tab is now active, refreshing auth state");
                syncTokens();
                initializeAuth().catch({
                    "AuthProvider.useCallback[handleVisibilityChange]": (error)=>{
                        console.error("Error refreshing auth state on visibility change:", error);
                    }
                }["AuthProvider.useCallback[handleVisibilityChange]"]);
            }
        }
    }["AuthProvider.useCallback[handleVisibilityChange]"], [
        initializeAuth,
        syncTokens
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Checking auth");
            if (!user.kinde_id) {
                initializeAuth().then({
                    "AuthProvider.useEffect": ()=>{
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Auth initialized");
                    }
                }["AuthProvider.useEffect"]);
            }
            if (!intervalRef.current && user.kinde_id) {
                intervalRef.current = setInterval({
                    "AuthProvider.useEffect": ()=>{
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Refreshing auth state");
                        initializeAuth();
                    }
                }["AuthProvider.useEffect"], 30 * 60 * 1000);
            }
            if (!syncIntervalRef.current && user.kinde_id) {
                syncIntervalRef.current = setInterval({
                    "AuthProvider.useEffect": ()=>{
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debug"])("Syncing tokens");
                        syncTokens();
                    }
                }["AuthProvider.useEffect"], 5 * 60 * 1000);
            }
            document.addEventListener("visibilitychange", handleVisibilityChange);
            return ({
                "AuthProvider.useEffect": ()=>{
                    if (intervalRef.current) {
                        clearInterval(intervalRef.current);
                        intervalRef.current = null;
                    }
                    if (syncIntervalRef.current) {
                        clearInterval(syncIntervalRef.current);
                        syncIntervalRef.current = null;
                    }
                    document.removeEventListener("visibilitychange", handleVisibilityChange);
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        initializeAuth,
        syncTokens,
        handleVisibilityChange,
        debouncedUser.kinde_id
    ]);
    return children;
};
_s(AuthProvider, "mBt5XHGyBIjjI281lok3Q4wLEDk=", false, function() {
    return [
        useAuth,
        useAuth,
        useAuth,
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$debounce$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDebounce"]
    ];
});
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/posthog/posthog-identify.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PostHogIdentify": (()=>PostHogIdentify)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$posthog$2d$tracking$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/posthog-tracking.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/auth-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
function PostHogIdentify() {
    _s();
    const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])({
        "PostHogIdentify.useAuth[user]": (state)=>state.user
    }["PostHogIdentify.useAuth[user]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PostHogIdentify.useEffect": ()=>{
            if (user?.kinde_id && user?.email) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$posthog$2d$tracking$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["identify"])(user);
            }
        }
    }["PostHogIdentify.useEffect"], [
        user?.kinde_id,
        user?.email
    ]);
    return null;
}
_s(PostHogIdentify, "rWib+r3IaIMs3zydPx5jI+fsn0U=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = PostHogIdentify;
var _c;
__turbopack_context__.k.register(_c, "PostHogIdentify");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/posthog/posthog-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PostHogProvider": (()=>PostHogProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/posthog-js@1.252.1/node_modules/posthog-js/dist/module.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$react$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/posthog-js@1.252.1/node_modules/posthog-js/react/dist/esm/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const PostHogProvider = ({ children })=>{
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PostHogProvider.useEffect": ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].init(("TURBOPACK compile-time value", "phc_WvgVPFfC5x9HNFUKjPnsupJT3GvmkP1CYtTZishpJjo"), {
                api_host: ("TURBOPACK compile-time value", "") || "https://us.i.posthog.com",
                person_profiles: "identified_only",
                loaded: {
                    "PostHogProvider.useEffect": (posthog)=>{
                        if ("TURBOPACK compile-time truthy", 1) posthog.debug();
                    }
                }["PostHogProvider.useEffect"]
            });
        }
    }["PostHogProvider.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$react$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PostHogProvider"], {
        client: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$posthog$2d$js$40$1$2e$252$2e$1$2f$node_modules$2f$posthog$2d$js$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/posthog/posthog-provider.tsx",
        lineNumber: 20,
        columnNumber: 10
    }, this);
};
_s(PostHogProvider, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = PostHogProvider;
var _c;
__turbopack_context__.k.register(_c, "PostHogProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/theme-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$themes$40$0$2e$4$2e$6_react$2d$dom_e207e685aa9cc81adf4eaedb8666d505$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-themes@0.4.6_react-dom_e207e685aa9cc81adf4eaedb8666d505/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
"use client";
;
;
function ThemeProvider({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$themes$40$0$2e$4$2e$6_react$2d$dom_e207e685aa9cc81adf4eaedb8666d505$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/theme-provider.tsx",
        lineNumber: 10,
        columnNumber: 10
    }, this);
}
_c = ThemeProvider;
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_97a30f08._.js.map