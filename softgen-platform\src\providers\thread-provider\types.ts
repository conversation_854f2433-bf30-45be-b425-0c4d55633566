import { ThreadMessage } from "@/types/thread-message";

interface BaseWebSocketMessage {
  type: string;
  isCompleteMessage?: boolean;
  termination?: boolean;
  data?: string;
  content?: string | Record<string, unknown>;
  role?: "user" | "assistant";
}

export interface ContentMessage extends BaseWebSocketMessage {
  type: "content";
  data: string;
}

export type SessionStatusMessage = {
  type: "session_status";
  status: "running" | "stopped";
  thread_id: string;
  project_id: string;
};

interface StopSessionMessage extends BaseWebSocketMessage {
  type: "stop_session";
}

export interface ErrorMessage extends BaseWebSocketMessage {
  type: "error";
  data: string;
}

export interface ThreadUpdateMessage {
  type: "thread_update";
  data: {
    thread_id: string;
    project_id: string;
    creation_date?: string;
    last_updated_date?: string;
    messages: ThreadMessage[];
  };
}

export type WrapUpSummaryMessage = {
  type: "wrap_up_summary";
  data: {
    summary: string;
    suggestions: string[];
  };
};

type CombinedWebSocketMessage =
  | BaseWebSocketMessage
  | ContentMessage
  | SessionStatusMessage
  | StopSessionMessage
  | ErrorMessage
  | ThreadUpdateMessage
  | WrapUpSummaryMessage;

export type WebSocketMessage = CombinedWebSocketMessage & {
  batch?: CombinedWebSocketMessage[];
};

export type NewMessage = {
  action: string;
  message?: string;
  objective_images?: string[];
  autonomous_iterations?: number;
  mode?: string;
  selected_paths?: string[];
  prompt_set?: number;
  model_id?: string;
};
