import { <PERSON><PERSON> } from "@/components/ui/button";
import { CardContent } from "@/components/ui/card";
import Hint from "@/components/ui/hint";
import Loading from "@/components/ui/loading";
import { LazyModal } from "@/components/ui/modal";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useThread } from "@/hooks/use-threads";
import { debug } from "@/lib/debug";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { SanboxPreviewState, useProject } from "@/providers/project-provider";
import {
  useCurrentThreadStore,
  useResetCurrentThread,
  useSubscribeToAgentRunning,
} from "@/stores/current-thread";
import { useNavigateFile } from "@/stores/navigate-file";
import { useAdvancedMode, useResizablePanelConfig } from "@/stores/settings";
import { useSettingsStore } from "@/stores/settings-tab";
import { ChatMessages, ChevronDown, CogOneSolid, ExternalLink, Plus } from "@mynaui/icons-react";
import { useQueryClient } from "@tanstack/react-query";
import { RotateCcw, Undo2 } from "lucide-react";
import { lazy, useCallback, useEffect, useRef, useState } from "react";
import { LuHistory } from "react-icons/lu";
import { useShallow } from "zustand/react/shallow";
import CodeEditor from "../code-editor/code-editor";
import SupabaseIcon from "../icon/supabase";
import Terminal from "../terminal/terminal";
import MessageInput from "../thread/message-input";
import { ThreadActionsDropdown } from "../thread/thread-actions-dropdown";
import ThreadContainer from "../thread/thread-container";
import ThreadHistory from "../thread/thread-history";
import AccountDropdown from "./account-dropdown";
import { BugFinder } from "./bug-finder";
import LoadingPreview from "./loading-preview";
import LogoLink from "./logo-link";
import SandboxUnavailable from "./modal/contact-support-modal";
import SupabaseSheet from "./modal/supabase-sheet";
import NoAccessPanel from "./no-access-panel";
import ProjetRenaemLabel from "./project-rename-label";
import PublishProject from "./publish";
import { SandboxSleepingCard } from "./sandbox-sleeping-card";

const RenameModalContent = lazy(() => import("../thread/rename-modal"));

export interface AppError extends Error {
  id?: string;
  timestamp?: string | Date;
  pageUrl?: string;
  type?:
    | "RUNTIME_ERROR"
    | "CONSOLE_OUTPUT"
    | "NETWORK_REQUEST"
    | "RESOURCE_ERROR"
    | "UNHANDLED_PROMISE_REJECTION"
    | string;
  name: string;
  message: string;
  stack?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  details?: any;
  // Network error specific properties
  url?: string;
  status?: number;
  method?: string;
}

const useErrors = () => {
  const [errors, setErrors] = useState<AppError[]>([]);

  const handleFixError = (error: unknown) => {
    setErrors((prev) => prev.filter((e) => e !== (error as AppError)));
  };

  return { errors, setErrors, handleFixError };
};

const DesktopView = ({ projectId }: { projectId: string }) => {
  const { user } = useAuth();
  const { isAdvancedMode } = useAdvancedMode();

  const [currentPage, setCurrentPage] = useState("/");
  const [iframeUrl, setIframeUrl] = useState("");
  const [isIframeLoading, setIsIframeLoading] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const queryClient = useQueryClient();
  const [showSupabaseSheet, setShowSupabaseSheet] = useState(false);

  const [, setShowLongPreviewWarning] = useState(false);
  const previewWarningTimer = useRef<number>(null);

  const { hasAccess, project, isProjectLoading, sandboxState } = useProject();
  const { createThreadFn, currentThread, setContextLimitReached } = useThread();

  const { currentThreadId, isAgentRunning, setCurrentThreadId } = useCurrentThreadStore(
    useShallow((state) => ({
      currentThreadId: state.id,
      isAgentRunning: state.isAgentRunning,
      setCurrentThreadId: state.setId,
    })),
  );

  const { resizablePanelConfig, setResizablePanelConfig } = useResizablePanelConfig();

  const { activeTab, setActiveTab } = useNavigateFile(
    useShallow((state) => ({
      activeTab: state.activeTab,
      setActiveTab: state.setActiveTab,
    })),
  );

  const { errors, setErrors } = useErrors();
  const [currentTab, setCurrentTab] = useState<"tasks" | "version-history">("tasks");

  const isFreeTier = user?.userFromDb?.plan === "free-tier";
  const showTerminal = !isFreeTier && isAdvancedMode;

  const [threadToRename, setThreadToRename] = useState<{ id: number; name: string } | null>(null);
  const filteredRoutes =
    project?.frontend_page_routes?.filter((route: string) => !route.includes("/api/")) || [];

  const isCreatingThread = createThreadFn.isPending;

  const handleCreateThread = () => {
    createThreadFn.mutate();
  };

  const { resetCurrentThread } = useResetCurrentThread();

  const setSettingsTab = useSettingsStore((state) => state.setSettingsTab);

  useEffect(() => {
    if (project?.env_id) {
      const baseUrl = `${project?.env_url}`;
      const newUrl = `${baseUrl}${currentPage}`;

      setIsIframeLoading(sandboxState === "starting");

      if (project.isRunning === 0) {
        return;
      }

      if (newUrl !== iframeUrl) {
        setIsIframeLoading(true);
        setIframeUrl(newUrl);
      }
    }
  }, [project, currentPage, iframeUrl, sandboxState]);

  const openInNewTab = () => {
    if (iframeUrl) {
      window.open(iframeUrl, "_blank");
    }
  };

  const handleRouteChange = (route: string) => {
    const cleanedRoute = route.includes("?") ? route.split("?")[0] : route;
    setCurrentPage(cleanedRoute);

    const now = Date.now();
    setErrors((prev: AppError[]) =>
      prev.map((error: AppError) => {
        if (
          error.timestamp &&
          now - new Date(error.timestamp).getTime() < 2000 &&
          error.pageUrl !== "/404"
        ) {
          return {
            ...error,
            pageUrl: cleanedRoute,
          };
        }
        return error;
      }),
    );
  };

  const toggleTab = () => {
    setCurrentTab(currentTab === "tasks" ? "version-history" : "tasks");
  };

  const handleIframeLoad = () => {
    if (iframeUrl) {
      // setIsIframeLoading(false);

      if (iframeRef.current && iframeRef.current.contentWindow) {
        try {
          const iframeDocument = iframeRef.current.contentWindow.document;
          if (!iframeDocument || !iframeDocument.head) {
            debug("[Alert] Cannot access iframe document or head");
            return;
          }

          const scriptExists = iframeDocument.querySelector(
            'script[src*="cdn.softgen.ai/script.js"]',
          );

          if (!scriptExists) {
            const script = iframeDocument.createElement("script");
            script.src = "https://cdn.softgen.ai/script.js";
            script.async = true;
            if (iframeDocument.head) {
              iframeDocument.head.appendChild(script);
              debug("Injected error monitoring script into iframe");
            }
          }
        } catch (error) {
          debug("[Alert] Cannot access iframe content due to same-origin policy", error);
        }
      }
    }
  };

  useEffect(() => {
    if (iframeRef.current) {
      iframeRef.current.style.display = activeTab === "preview" ? "block" : "none";
    }
  }, [activeTab]);

  useEffect(() => {
    const handleIframeMessage = (event: MessageEvent) => {
      if (!iframeUrl || !event.data || typeof event.data !== "object") return;

      try {
        if (event.data.type === "MONITOR_SCRIPT_LOADED") {
          setIsIframeLoading(false);
          return;
        }

        if (
          [
            "RUNTIME_ERROR",
            "CONSOLE_OUTPUT",
            "NETWORK_REQUEST",
            "RESOURCE_ERROR",
            "UNHANDLED_PROMISE_REJECTION",
          ].includes(event.data.type)
        ) {
          let newError: AppError = {
            id: `iframe-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,
            timestamp: new Date(),
            pageUrl: currentPage,
            name: "Error",
            message: "Unknown error",
          };

          if (event.data.type === "RUNTIME_ERROR") {
            newError = {
              ...newError,
              message: event.data.error.message || "Unknown runtime error",
              stack: event.data.error.stack,
              name: "RuntimeError",
              type: "runtime",
            };
          } else if (
            event.data.type === "CONSOLE_OUTPUT" &&
            (event.data.level === "error" || event.data.level === "warning")
          ) {
            newError = {
              ...newError,
              message: event.data.message || "Console error",
              name: "ConsoleError",
              type: "console",
            };
          } else if (event.data.type === "NETWORK_REQUEST" && event.data.request) {
            if (event.data.request.status >= 400 || event.data.request.error) {
              newError = {
                ...newError,
                message:
                  event.data.request.error?.message ||
                  `${event.data.request.method} ${event.data.request.url} ${event.data.request.status} (${event.data.request.statusText})`,
                name: "NetworkError",
                type: "network",
                details: {
                  url: event.data.request.url,
                  status: event.data.request.status,
                  method: event.data.request.method,
                },
              };
            } else {
              return;
            }
          } else if (event.data.type === "RESOURCE_ERROR") {
            newError = {
              ...newError,
              message: event.data.error.message || "Resource loading error",
              name: "ResourceError",
              type: "resource",
            };
          } else if (event.data.type === "UNHANDLED_PROMISE_REJECTION") {
            newError = {
              ...newError,
              message: event.data.error.message || "Unhandled promise rejection",
              stack: event.data.error.stack,
              name: "PromiseRejection",
              type: "promise",
            };
          } else {
            return;
          }

          setErrors((prev) => [...prev, newError]);
        } else if (event.data.type === "URL_CHANGED") {
          const newPath = event.data.path || "/";
          if (newPath !== currentPage) {
            setCurrentPage(newPath);
          }
        }
      } catch (error) {
        console.error("Error processing iframe message:", error);
      }
    };

    window.addEventListener("message", handleIframeMessage);

    return () => {
      window.removeEventListener("message", handleIframeMessage);
    };
  }, [iframeUrl, currentPage, setErrors]);

  useEffect(() => {
    if (isIframeLoading) {
      previewWarningTimer.current = window.setTimeout(() => {
        setShowLongPreviewWarning(true);
      }, 6000);
    } else {
      window.clearTimeout(previewWarningTimer?.current ?? undefined);
      setShowLongPreviewWarning(false);
    }
    return () => {
      window.clearTimeout(previewWarningTimer?.current ?? undefined);
    };
  }, [isIframeLoading]);

  const handleRefreshPreview = useCallback(() => {
    setErrors([]);
    setIsIframeLoading(true);

    const timer = setTimeout(() => {
      setIframeUrl((currentUrl) => {
        if (!currentUrl) return "";
        const separator = currentUrl.includes("?") ? "&" : "?";
        return `${currentUrl}${separator}refresh=${Math.random().toString(36).substr(2, 9)}`;
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [setErrors, setIsIframeLoading, setIframeUrl]);

  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {
    if (!isAgentRunning && wasRunning) {
      debug("[DesktopView] Agent stopped, refreshing preview");
      handleRefreshPreview();
    }
  });

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        minSize={28}
        defaultSize={resizablePanelConfig.width}
        onSizeChange={(size) => {
          setResizablePanelConfig({
            width: size,
            previewWidth: 100 - size,
            tasks: resizablePanelConfig.tasks,
          });
        }}
        className="order-1 flex w-full flex-col overflow-hidden"
      >
        {hasAccess ? (
          <div className="flex h-full flex-col">
            <div
              className={cn(
                "flex min-h-12 items-center justify-between px-3 py-2",
                currentThreadId && "pb-1.5",
              )}
            >
              <LogoLink />

              {project?.name && (
                <div className="flex items-center gap-1">
                  {currentThread ? (
                    <ThreadActionsDropdown
                      thread={currentThread}
                      projectId={projectId}
                      icon={<ChevronDown className="h-4 w-4" />}
                      setThreadToRename={setThreadToRename}
                    />
                  ) : (
                    <ProjetRenaemLabel />
                  )}
                </div>
              )}

              <div className="flex items-center gap-1">
                {currentThreadId && (
                  <Button
                    variant="secondary"
                    size="icon"
                    className="hover:bg-transparent md:hover:bg-sidebar-accent"
                    disabled={isAgentRunning}
                    onClick={() => {
                      queryClient.invalidateQueries({ queryKey: ["get-thread", currentThreadId] });
                      resetCurrentThread();
                      setCurrentThreadId(null);
                      setCurrentTab("tasks");
                      setContextLimitReached(false);
                    }}
                  >
                    <Undo2 className="h-4 w-4" strokeWidth={2} />
                  </Button>
                )}

                <Button variant="secondary" size="icon" onClick={() => toggleTab()}>
                  {currentTab === "tasks" ? <LuHistory /> : <ChatMessages />}
                </Button>
              </div>
            </div>

            <div
              className={cn(
                "mt-0 hidden h-full flex-col overflow-hidden bg-transparent data-[state=active]:flex",
                currentTab === "version-history" ? "hidden" : "flex",
              )}
            >
              <div className={cn("flex-1 overflow-auto")}>
                <ThreadContainer setThreadToRename={setThreadToRename} />

                <LazyModal open={!!threadToRename} onOpenChange={() => setThreadToRename(null)}>
                  <RenameModalContent
                    projectId={projectId}
                    threadToRename={threadToRename}
                    onClose={() => setThreadToRename(null)}
                  />
                </LazyModal>
              </div>

              <div className="relative sticky bottom-0 z-20">
                {currentThreadId ? (
                  <MessageInput />
                ) : (
                  <div className="mx-2 mb-2 flex items-center">
                    <Button
                      size="lg"
                      className="h-12 w-full"
                      disabled={false}
                      onClick={() => handleCreateThread()}
                    >
                      {isCreatingThread ? (
                        <>
                          <Loading className="size-4 animate-spin text-background" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="size-4" />
                          Create New Thread
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>

            <div
              className={cn(
                "hidden h-full overflow-auto data-[state=active]:flex",
                currentTab === "tasks" ? "hidden" : "flex",
              )}
            >
              <ThreadHistory id={projectId} isActive={currentTab === "version-history"} />
            </div>
          </div>
        ) : (
          <div className="flex h-full flex-col px-4 py-8">
            <LogoLink />

            <div className="flex flex-1 flex-col justify-center">
              <NoAccessPanel projectId={projectId} />
            </div>
          </div>
        )}
      </ResizablePanel>

      <ResizableHandle className="order-2" />

      <ResizablePanel
        minSize={28}
        defaultSize={resizablePanelConfig.previewWidth}
        onSizeChange={(size) => {
          setResizablePanelConfig({ previewWidth: size, width: 100 - size });
        }}
        className={cn("relative order-3 flex w-full flex-1 rounded-none border-l-0")}
      >
        {hasAccess ? (
          <Tabs
            defaultValue="preview"
            className="h-full w-full rounded-none border-none"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <div className="flex items-center gap-2">
              <div className="flex min-h-11 w-full items-center justify-between p-1 pt-1.5">
                <div className="flex items-center gap-1">
                  <TabsList className="flex w-fit items-center justify-center gap-2 border-l-0">
                    <TabsTrigger
                      value="preview"
                      className="w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0"
                    >
                      Preview
                    </TabsTrigger>
                    <TabsTrigger value="code-editor" className="w-fit" disabled={false} title="">
                      Code Editor
                    </TabsTrigger>
                    {showTerminal && (
                      <TabsTrigger value="terminal" className="w-fit" disabled={false} title="">
                        Terminal
                      </TabsTrigger>
                    )}
                  </TabsList>
                </div>

                <div className="mr-1 flex items-center gap-1">
                  {!project?.onboarding_completed && (
                    <Button
                      variant="secondary"
                      size="sm"
                      className={cn(
                        "ml-auto flex items-center justify-center",
                        "border border-[#34B27B]/20 bg-[#006239]/90 text-[#fafafa]/90 hover:bg-[#006239] hover:text-[#fafafa] dark:border-[#34B27B]/15 dark:text-primary/90 dark:hover:text-primary",
                      )}
                      disabled={false}
                      onClick={() => setShowSupabaseSheet(true)}
                    >
                      <SupabaseIcon className="size-4" />
                      <span className="text-sm font-semibold">Supabase</span>
                    </Button>
                  )}

                  <PublishProject />

                  <Button variant="secondary" size="icon" onClick={() => setSettingsTab("project")}>
                    <CogOneSolid />
                  </Button>

                  <AccountDropdown />
                </div>
              </div>
            </div>

            <div
              className={cn(
                "relative -mt-0.5 h-full w-full",
                activeTab === "preview" ? "block" : "hidden",
              )}
            >
              <TabsContent forceMount value="preview" className={cn("relative h-[90vh] w-full")}>
                <div className="flex w-full items-center justify-between gap-2 rounded-tl-2xl border-y border-l border-primary/10 bg-background p-1 py-1 text-sm font-medium md:px-2">
                  <Hint label="Refresh Preview" side="bottom" delayDuration={125}>
                    <Button
                      size="icon"
                      className="size-7"
                      variant="ghost"
                      onClick={handleRefreshPreview}
                      disabled={isAgentRunning}
                    >
                      {isIframeLoading ? (
                        <Loading className="size-4 animate-spin" />
                      ) : (
                        <RotateCcw
                          className="size-4 text-primary/90 hover:text-primary"
                          strokeWidth={2}
                        />
                      )}
                    </Button>
                  </Hint>
                  {isProjectLoading ? (
                    <Skeleton className="mx-0.5 my-0 h-6 w-full" />
                  ) : (
                    filteredRoutes.length > 0 && (
                      <Select value={currentPage} onValueChange={handleRouteChange}>
                        <SelectTrigger className="mx-0.5 my-0 h-[1.7rem] w-full rounded-full bg-sidebar-accent py-0 hover:bg-sidebar-accent/40">
                          <SelectValue placeholder="Select route" />
                        </SelectTrigger>
                        <SelectContent align="center" className="w-72">
                          {filteredRoutes.map((route: string) => (
                            <SelectItem className="mx-2" key={route} value={route}>
                              {route}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )
                  )}

                  <div className="ml-auto flex items-center gap-1">
                    <Button variant="ghost" size="icon" className="size-7" onClick={openInNewTab}>
                      <ExternalLink className="size-4 text-primary/90 hover:text-primary" />
                    </Button>
                  </div>
                </div>

                <PreviewPanel
                  iframeUrl={iframeUrl}
                  handleIframeLoad={handleIframeLoad}
                  iframeRef={iframeRef}
                  isIframeLoading={isIframeLoading}
                  sandboxState={sandboxState}
                />
              </TabsContent>
              {sandboxState === "idle" && (
                <div className="mx-auto flex w-full items-center justify-center">
                  <BugFinder projectId={projectId} errors={errors} setErrors={setErrors} />
                </div>
              )}
            </div>

            <div
              className={cn(
                "relative -mt-0.5 h-full w-full",
                activeTab === "code-editor" ? "block" : "hidden",
              )}
            >
              <TabsContent forceMount value="code-editor" className={cn("h-screen w-full")}>
                <CardContent className="h-full rounded-tl-2xl border-y border-l border-primary/10 bg-background p-0">
                  <CodeEditor projectId={projectId} envId={project?.env_id || ""} />
                </CardContent>
              </TabsContent>
            </div>

            {showTerminal && (
              <TabsContent
                forceMount
                value="terminal"
                className={cn(
                  "mt-1 h-[calc(100vh-40px)] w-full rounded-tl-2xl",
                  activeTab !== "terminal" ? "hidden" : "block",
                )}
              >
                <CardContent className="h-full rounded-tl-2xl border-y border-l border-primary/10 bg-background p-0">
                  <Terminal projectId={projectId} />
                </CardContent>
              </TabsContent>
            )}
          </Tabs>
        ) : (
          <div className="w-full">
            <PreviewPanel
              iframeUrl={iframeUrl}
              handleIframeLoad={handleIframeLoad}
              iframeRef={iframeRef}
              isIframeLoading={isIframeLoading}
              className="h-full"
              sandboxState={sandboxState}
            />
          </div>
        )}
      </ResizablePanel>

      <SupabaseSheet isOpen={showSupabaseSheet} onClose={() => setShowSupabaseSheet(false)} />
    </ResizablePanelGroup>
  );
};

function PreviewPanel({
  iframeUrl,
  iframeRef,
  isIframeLoading,
  className,
  sandboxState,
  handleIframeLoad,
}: {
  iframeUrl: string;
  iframeRef: React.RefObject<HTMLIFrameElement | null>;
  isIframeLoading: boolean;
  className?: string;
  sandboxState: SanboxPreviewState;
  handleIframeLoad: () => void;
}) {
  return (
    <CardContent
      className={cn("z-20 h-[calc(100vh-90px)] border-l border-foreground/10 p-0", className)}
    >
      <div className="relative flex h-full w-full flex-1 flex-col">
        {sandboxState === "unavailable" ? (
          <div className="flex h-full items-center justify-center">
            <SandboxUnavailable />
          </div>
        ) : sandboxState === "sleeping" ? (
          <div className="flex h-full items-center justify-center">
            <SandboxSleepingCard />
          </div>
        ) : (
          <>
            {/*
            Security Attributes Explanation:

              sandbox: Controls what the iframe content can do
              - allow-scripts: Run JavaScript
              - allow-same-origin: Access same-origin resources
              - allow-forms: Submit forms
              - allow-popups: Open new windows/tabs
              - allow-modals: Show modal dialogs (alert, confirm, etc.)
              - allow-downloads: Trigger file downloads
              - allow-clipboard-*: Enable clipboard operations
              - allow-storage-access-by-user-activation: Allow storage access after user interaction
              - allow-presentation: Enable presentation features (like screen sharing)

            allow: Controls which browser features the iframe can access
            - clipboard-*: Read/write to clipboard
            - geolocation: Access device location
            - microphone/camera: Access media devices
            - display-capture: Screen sharing capability
            - fullscreen: Enter fullscreen mode
            - web-share: Use Web Share API
          */}
            {iframeUrl && (
              <iframe
                src={iframeUrl}
                className="h-full w-full"
                title="Web Application Preview"
                style={{
                  overflow: "auto",
                  scrollbarWidth: "thin",
                  scrollbarColor: "var(--muted) transparent",
                  colorScheme: "normal",
                }}
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-storage-access-by-user-activation allow-presentation"
                allow="clipboard-write; clipboard-read; geolocation *; microphone *; camera *; display-capture *; fullscreen; web-share"
                onLoad={handleIframeLoad}
                ref={iframeRef}
                referrerPolicy="no-referrer"
                loading="lazy"
              />
            )}
            {isIframeLoading && <LoadingPreview />}
          </>
        )}
      </div>
    </CardContent>
  );
}

export default DesktopView;
