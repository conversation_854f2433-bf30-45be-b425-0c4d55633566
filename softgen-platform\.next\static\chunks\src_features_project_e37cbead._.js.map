{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/account-dropdown.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useAdvancedMode } from \"@/stores/settings\";\r\nimport { useSettingsStore } from \"@/stores/settings-tab\";\r\nimport {\r\n  CartSolid,\r\n  CogOneSolid,\r\n  CreditCardSolid,\r\n  Logout,\r\n  UserSolid,\r\n  UsersSolid,\r\n} from \"@mynaui/icons-react\";\r\nimport { Coins, Moon, Sun } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useTokenInfo } from \"../../hooks/use-token-info\";\r\n\r\nconst AccountDropdown = () => {\r\n  const { user, logout } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const { getTokenDisplayInfo } = useTokenInfo();\r\n  const tokenInfo = getTokenDisplayInfo();\r\n  const { isAdvancedMode, toggleAdvancedMode } = useAdvancedMode();\r\n\r\n  const setSettingsTab = useSettingsStore((state) => state.setSettingsTab);\r\n\r\n  const isFreeTier = user?.userFromDb?.plan === \"free-tier\";\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"secondary\" size=\"icon\">\r\n          <UserSolid />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-full p-0\" align=\"end\">\r\n        <div className=\"p-2 pb-0.5\">\r\n          <DropdownMenuLabel className=\"mb-1\">My Account</DropdownMenuLabel>\r\n\r\n          {!isFreeTier && (\r\n            <DropdownMenuItem\r\n              onClick={() => setSettingsTab(\"billing\")}\r\n              className=\"justify-start gap-3\"\r\n            >\r\n              <CreditCardSolid /> Billing\r\n            </DropdownMenuItem>\r\n          )}\r\n\r\n          {tokenInfo.remainingTokens && (\r\n            <DropdownMenuItem className=\"justify-start gap-3\">\r\n              <Coins className=\"h-4 w-4\" strokeWidth={2} />\r\n              {tokenInfo.remainingTokens} Tokens\r\n            </DropdownMenuItem>\r\n          )}\r\n\r\n          <DropdownMenuItem\r\n            onClick={() => setSettingsTab(\"purchase\")}\r\n            className=\"justify-start gap-3\"\r\n          >\r\n            <CartSolid /> Purchase Tokens\r\n          </DropdownMenuItem>\r\n        </div>\r\n\r\n        <DropdownMenuSeparator />\r\n\r\n        <div className=\"p-1 px-2 pt-0.5\">\r\n          <DropdownMenuLabel className=\"mb-1\">Project</DropdownMenuLabel>\r\n\r\n          <DropdownMenuItem\r\n            onClick={() => setSettingsTab(\"project\")}\r\n            className=\"justify-start gap-3\"\r\n          >\r\n            <CogOneSolid /> Project\r\n          </DropdownMenuItem>\r\n\r\n          <DropdownMenuItem onClick={() => setSettingsTab(\"team\")} className=\"justify-start gap-3\">\r\n            <UsersSolid /> Team\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator className=\"-mx-2\" />\r\n\r\n          <DropdownMenuItem\r\n            onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n            className=\"justify-start gap-3\"\r\n          >\r\n            {theme === \"light\" ? (\r\n              <Moon className=\"h-4 w-4\" strokeWidth={2} />\r\n            ) : (\r\n              <Sun className=\"h-4 w-4\" strokeWidth={2} />\r\n            )}\r\n\r\n            {theme === \"light\" ? \"Dark Mode\" : \"Light Mode\"}\r\n          </DropdownMenuItem>\r\n\r\n          {!isFreeTier && (\r\n            <DropdownMenuItem onClick={toggleAdvancedMode} className=\"justify-start gap-3\">\r\n              <UserSolid className=\"h-4 w-4\" strokeWidth={2} />\r\n              {isAdvancedMode ? \"Return to Default Mode\" : \"Enable Advanced Mode\"}\r\n            </DropdownMenuItem>\r\n          )}\r\n\r\n          <DropdownMenuSeparator className=\"-mx-2\" />\r\n\r\n          <DropdownMenuItem className=\"mb-1\" onClick={() => logout()}>\r\n            <span className=\"flex w-full items-center justify-start gap-3 text-red-500 hover:text-red-600\">\r\n              <Logout className=\"h-4 w-4\" strokeWidth={2} />\r\n              Log out\r\n            </span>\r\n          </DropdownMenuItem>\r\n        </div>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n};\r\n\r\nexport default AccountDropdown;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAAA;AAAA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,kBAAkB;;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IAC3C,MAAM,YAAY;IAClB,MAAM,EAAE,cAAc,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAE7D,MAAM,iBAAiB,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;4DAAE,CAAC,QAAU,MAAM,cAAc;;IAEvE,MAAM,aAAa,MAAM,YAAY,SAAS;IAE9C,qBACE,4SAAC,+IAAA,CAAA,eAAY;;0BACX,4SAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAY,MAAK;8BAC/B,cAAA,4SAAC,gUAAA,CAAA,YAAS;;;;;;;;;;;;;;;0BAGd,4SAAC,+IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAa,OAAM;;kCAChD,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAAO;;;;;;4BAEnC,CAAC,4BACA,4SAAC,+IAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,eAAe;gCAC9B,WAAU;;kDAEV,4SAAC,4UAAA,CAAA,kBAAe;;;;;oCAAG;;;;;;;4BAItB,UAAU,eAAe,kBACxB,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,WAAU;;kDAC1B,4SAAC,2RAAA,CAAA,QAAK;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCACvC,UAAU,eAAe;oCAAC;;;;;;;0CAI/B,4SAAC,+IAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,eAAe;gCAC9B,WAAU;;kDAEV,4SAAC,gUAAA,CAAA,YAAS;;;;;oCAAG;;;;;;;;;;;;;kCAIjB,4SAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAAO;;;;;;0CAEpC,4SAAC,+IAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,eAAe;gCAC9B,WAAU;;kDAEV,4SAAC,oUAAA,CAAA,cAAW;;;;;oCAAG;;;;;;;0CAGjB,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS,IAAM,eAAe;gCAAS,WAAU;;kDACjE,4SAAC,kUAAA,CAAA,aAAU;;;;;oCAAG;;;;;;;0CAEhB,4SAAC,+IAAA,CAAA,wBAAqB;gCAAC,WAAU;;;;;;0CAEjC,4SAAC,+IAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;gCACrD,WAAU;;oCAET,UAAU,wBACT,4SAAC,yRAAA,CAAA,OAAI;wCAAC,WAAU;wCAAU,aAAa;;;;;6DAEvC,4SAAC,uRAAA,CAAA,MAAG;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCAGvC,UAAU,UAAU,cAAc;;;;;;;4BAGpC,CAAC,4BACA,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAoB,WAAU;;kDACvD,4SAAC,gUAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,aAAa;;;;;;oCAC3C,iBAAiB,2BAA2B;;;;;;;0CAIjD,4SAAC,+IAAA,CAAA,wBAAqB;gCAAC,WAAU;;;;;;0CAEjC,4SAAC,+IAAA,CAAA,mBAAgB;gCAAC,WAAU;gCAAO,SAAS,IAAM;0CAChD,cAAA,4SAAC;oCAAK,WAAU;;sDACd,4SAAC,iTAAA,CAAA,SAAM;4CAAC,WAAU;4CAAU,aAAa;;;;;;wCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;GA/FM;;QACqB,wIAAA,CAAA,UAAO;QACJ,4PAAA,CAAA,WAAQ;QACJ,uIAAA,CAAA,eAAY;QAEG,4HAAA,CAAA,kBAAe;QAEvC,mIAAA,CAAA,mBAAgB;;;KAPnC;uCAiGS", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/bug-finder/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { LazyModal } from \"@/components/ui/modal\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { executeCommand } from \"@/lib/api\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAgentInput } from \"@/stores/agent-input\";\r\nimport { useIsAgentRunning } from \"@/stores/current-thread\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport { deduplicateErrors } from \"@/utils/error-utils\";\r\nimport { DangerTriangleSolid, PlayCircle, X } from \"@mynaui/icons-react\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { AnimatePresence, motion } from \"motion/react\";\r\nimport { lazy, useCallback, useMemo, useState } from \"react\";\r\nimport { errorToast, successToast } from \"../../global/toast\";\r\nimport { AppError } from \"../desktop-view\";\r\nimport { OperationResult } from \"./modal\";\r\n\r\nconst BugFinderModalContent = lazy(() => import(\"./modal\"));\r\n\r\ninterface Props {\r\n  projectId: string;\r\n  errors: AppError[];\r\n  setErrors: (errors: AppError[]) => void;\r\n}\r\n\r\ninterface CommandResult {\r\n  success: boolean;\r\n  message: string;\r\n  stdout: string;\r\n  code: number;\r\n}\r\n\r\nexport const formatErrorContent = (rawErrors: AppError[]) => {\r\n  const errors = rawErrors.map((e) => ({\r\n    ...e,\r\n    stack: e.stack?.slice(0, 100),\r\n  }));\r\n\r\n  return `I encountered ${errors.length > 1 ? \"these errors\" : \"this error\"} in my code.\r\nPlease analyze and provide a step-by-step solution.\r\n${\r\n  errors.some((e) => e.type === \"network\" || e.type === \"NETWORK_REQUEST\")\r\n    ? `\r\n  <network_errors errors=${JSON.stringify(errors)} />\r\n`\r\n    : ` \r\n  <error_details errors=${JSON.stringify(errors)} /> \r\n`\r\n}`;\r\n};\r\n\r\nconst BugFinder = ({ projectId, errors, setErrors }: Props) => {\r\n  const setAgentInput = useAgentInput((state) => state.setInputContent);\r\n  const { selectThreadId } = useThread();\r\n  const isAgentRunning = useIsAgentRunning();\r\n  const isMobile = useIsMobile();\r\n  const setShowDrawer = useNavigateFile((state) => state.setShowDrawer);\r\n  const [showBugFinder, setShowBugFinder] = useState(true);\r\n\r\n  const [selectedErrorTab, setSelectedErrorTab] = useState<\r\n    AppError[\"type\"] | \"runtime\" | \"lint\" | \"build\" | null\r\n  >(null);\r\n\r\n  const [lintResult, setLintResult] = useState<OperationResult>({\r\n    pending: false,\r\n    message: \"\",\r\n    status: false,\r\n  });\r\n  const [buildResult, setBuildResult] = useState<OperationResult>({\r\n    pending: false,\r\n    message: \"\",\r\n    status: false,\r\n  });\r\n\r\n  const filteredErrors = deduplicateErrors(errors) as AppError[];\r\n  const errorCount = filteredErrors.length;\r\n\r\n  const hasErrors = errorCount > 0;\r\n\r\n  const handleFixErrors = async () => {\r\n    try {\r\n      const errorContent = formatErrorContent(filteredErrors);\r\n\r\n      selectThreadId();\r\n\r\n      debug(\"Setting prefill message with errors\");\r\n      setAgentInput(errorContent);\r\n      setErrors([]);\r\n    } catch (error) {\r\n      console.error(\"Error in handleFixErrors:\", error);\r\n      errorToast(\"Failed to send error details to agent\");\r\n    } finally {\r\n      if (isMobile) {\r\n        setShowDrawer(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  const lintMutation = useMutation({\r\n    mutationFn: async () => {\r\n      if (!projectId) throw new Error(\"No project ID available\");\r\n\r\n      try {\r\n        const result = (await executeCommand(projectId, \"npm run lint\")) as CommandResult;\r\n\r\n        const isSuccess = result.success;\r\n\r\n        return {\r\n          status: isSuccess,\r\n          message: result.message || \"Lint completed\",\r\n          stdout: result.stdout || \"Lint completed\",\r\n        } as OperationResult;\r\n      } catch (error) {\r\n        console.error(\"Lint error:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    onSuccess: (result) => {\r\n      setLintResult(result);\r\n\r\n      if (result.status) {\r\n        successToast(\"Lint completed successfully\");\r\n      } else {\r\n        errorToast(\"Lint found issues\");\r\n        setErrors([\r\n          {\r\n            name: \"Lint Failed\",\r\n            message: result.stdout || result.message || \"Lint failed\",\r\n            type: \"lint\",\r\n          },\r\n        ]);\r\n      }\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Lint mutation error:\", error);\r\n      setLintResult({\r\n        message: \"Lint process failed unexpectedly\",\r\n        status: false,\r\n        pending: false,\r\n      });\r\n      errorToast(\"Lint process failed unexpectedly\");\r\n    },\r\n    onSettled: () => {\r\n      setLintResult((prev) => ({\r\n        ...prev,\r\n        pending: false,\r\n      }));\r\n    },\r\n  });\r\n\r\n  const handleRunLint = useCallback(() => {\r\n    lintMutation.reset();\r\n    lintMutation.mutate();\r\n    setLintResult({\r\n      message: \"\",\r\n      status: false,\r\n      pending: true,\r\n    });\r\n  }, [lintMutation]);\r\n\r\n  const buildMutation = useMutation({\r\n    mutationFn: async () => {\r\n      if (!projectId) throw new Error(\"No project ID available\");\r\n\r\n      try {\r\n        const result = await executeCommand(projectId, \"npm run build\");\r\n\r\n        const isSuccess = !result.error && result.code === 0;\r\n\r\n        return {\r\n          message: result.message || \"Build completed\",\r\n          stdout: result.stdout || \"Build completed\",\r\n          status: isSuccess,\r\n        } as OperationResult;\r\n      } catch (error) {\r\n        console.error(\"Build error:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    onSuccess: (result) => {\r\n      setBuildResult(result);\r\n\r\n      if (result.status) {\r\n        successToast(\"Build completed successfully\");\r\n      } else {\r\n        errorToast(\"Build failed\");\r\n        setErrors([\r\n          {\r\n            name: \"Build Failed\",\r\n            message: result.stdout || result.message || \"Build failed\",\r\n            type: \"build\",\r\n          },\r\n        ]);\r\n      }\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Build mutation error:\", error);\r\n      setBuildResult({\r\n        message: \"Build process failed unexpectedly\",\r\n        status: false,\r\n        pending: false,\r\n      });\r\n      errorToast(\"Build process failed unexpectedly\");\r\n    },\r\n    onSettled: () => {\r\n      setBuildResult((prev) => ({\r\n        ...prev,\r\n        pending: false,\r\n      }));\r\n    },\r\n  });\r\n\r\n  const handleRunBuild = useCallback(() => {\r\n    buildMutation.reset();\r\n    buildMutation.mutate();\r\n    setBuildResult({\r\n      message: \"\",\r\n      status: false,\r\n      pending: true,\r\n    });\r\n  }, [buildMutation]);\r\n\r\n  const cardClassName = useMemo(\r\n    () =>\r\n      cn(\r\n        \"pointer-events-auto mx-auto max-w-sm bg-transparent shadow-sm\",\r\n        !showBugFinder && hasErrors && \"translate-y-[9.9rem] sm:translate-y-[8.2rem]\",\r\n        showBugFinder && \"hover:data-[state=open]:translate-y-[8.5rem]\",\r\n        !showBugFinder && !hasErrors && \"translate-y-[6.2rem] sm:translate-y-[4.6rem]\",\r\n        \"transition-all duration-300 ease-in-out\",\r\n      ),\r\n    [showBugFinder],\r\n  );\r\n\r\n  const handleMouseEnter = useCallback(() => {\r\n    setShowBugFinder(true);\r\n  }, []);\r\n\r\n  if (isAgentRunning) {\r\n    return (\r\n      <div\r\n        className={cn(\r\n          \"pointer-events-auto z-50 flex flex-col gap-2\",\r\n          \"absolute bottom-20 z-50\",\r\n          isMobile && \"bottom-20 z-[100]\",\r\n        )}\r\n      >\r\n        <div className=\"mx-auto w-full max-w-xs\">\r\n          <AnimatePresence mode=\"sync\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: 10 }}\r\n              transition={{ duration: 0.2 }}\r\n              className=\"relative flex w-full max-w-xs translate-x-1/3 transform flex-col\"\r\n            >\r\n              <Card className={cn(\"w-full max-w-[26rem] shadow-sm\")}>\r\n                <CardContent className=\"flex w-full items-center justify-center gap-2 p-3 text-sm\">\r\n                  <div className=\"flex w-full items-center justify-between gap-3 px-3\">\r\n                    <Label className=\"text-base font-medium text-primary\">\r\n                      Agent is running{\"\"}\r\n                      <span className=\"jumping-dots\">\r\n                        <span className=\"dot-1\">.</span>\r\n                        <span className=\"dot-2\">.</span>\r\n                        <span className=\"dot-3\">.</span>\r\n                      </span>\r\n                    </Label>\r\n                    <Loading className=\"size-4 animate-spin\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </motion.div>\r\n          </AnimatePresence>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"z-50 flex flex-col gap-2\",\r\n        \"absolute bottom-20 w-full\",\r\n        isMobile && \"bottom-20 z-[100] w-full\",\r\n        cardClassName,\r\n      )}\r\n    >\r\n      <div className=\"mx-auto w-full max-w-sm\">\r\n        <AnimatePresence mode=\"sync\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: 10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className={cn(\"group relative flex w-full max-w-sm translate-x-1/3 transform flex-col\")}\r\n          >\r\n            <Card onMouseEnter={handleMouseEnter}>\r\n              {hasErrors && (\r\n                <CardContent className=\"flex w-full items-center justify-between gap-4 p-3 text-sm\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <DangerTriangleSolid className=\"h-5 w-5 font-semibold\" />\r\n                    <Label className=\"text-sm font-medium text-primary\">\r\n                      {errorCount} {errorCount === 1 ? \"Error\" : \"Errors\"} found\r\n                    </Label>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-end gap-2\">\r\n                    <Button\r\n                      variant=\"destructive\"\r\n                      size=\"sm\"\r\n                      onClick={() => {\r\n                        handleFixErrors();\r\n                      }}\r\n                      className=\"h-8 px-3 text-xs\"\r\n                    >\r\n                      Fix Errors\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"h-8 px-3 text-xs\"\r\n                      onClick={() => setSelectedErrorTab(\"runtime\")}\r\n                    >\r\n                      See Logs\r\n                    </Button>\r\n                  </div>\r\n                </CardContent>\r\n              )}\r\n\r\n              <Separator className={cn(!hasErrors && \"hidden\", \"m-0\")} />\r\n\r\n              <CardContent className=\"flex w-full items-center justify-between gap-2 p-3 text-sm\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <PlayCircle className=\"h-5 w-5 font-semibold\" />\r\n                  <Label className=\"text-sm font-medium text-primary\">Project Tools</Label>\r\n                </div>\r\n\r\n                <div className=\"flex items-center justify-end gap-2\">\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    size=\"sm\"\r\n                    onClick={() => {\r\n                      setSelectedErrorTab(\"build\");\r\n                    }}\r\n                    className=\"h-8 px-3 text-xs\"\r\n                  >\r\n                    {buildResult.pending ? (\r\n                      <>\r\n                        <Loading className=\"size-4\" />\r\n                        <span className=\"text-xs\">Running...</span>\r\n                      </>\r\n                    ) : (\r\n                      \"Run Build\"\r\n                    )}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    size=\"sm\"\r\n                    onClick={() => {\r\n                      setSelectedErrorTab(\"lint\");\r\n                    }}\r\n                    className=\"h-8 px-3 text-xs\"\r\n                  >\r\n                    {lintResult.pending ? (\r\n                      <>\r\n                        <Loading className=\"size-4\" />\r\n                        <span className=\"text-xs\">Linting...</span>\r\n                      </>\r\n                    ) : (\r\n                      \"Run Lint\"\r\n                    )}\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n\r\n              {showBugFinder && (\r\n                <div className=\"absolute -right-2.5 -top-2.5 z-50\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    size=\"icon\"\r\n                    className=\"size-7 h-fit w-fit rounded-full bg-background p-1 hover:bg-background/90 dark:bg-primary dark:hover:bg-primary/90\"\r\n                    onClick={() => setShowBugFinder(false)}\r\n                  >\r\n                    <X className=\"size-3 text-primary dark:text-background\" />\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </Card>\r\n          </motion.div>\r\n        </AnimatePresence>\r\n\r\n        <LazyModal\r\n          forceMount\r\n          open={!!selectedErrorTab}\r\n          onOpenChange={() => setSelectedErrorTab(null)}\r\n        >\r\n          <BugFinderModalContent\r\n            projectId={projectId}\r\n            errors={filteredErrors}\r\n            selectedErrorTab={selectedErrorTab}\r\n            setSelectedErrorTab={setSelectedErrorTab}\r\n            lintResult={lintResult}\r\n            buildResult={buildResult}\r\n            handleRunBuild={handleRunBuild}\r\n            handleRunLint={handleRunLint}\r\n          />\r\n        </LazyModal>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { BugFinder };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;;;AArBA;;;;;;;;;;;;;;;;;;;;;AAyBA,MAAM,sCAAwB,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE;KAA7B;AAeC,MAAM,qBAAqB,CAAC;IACjC,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,IAAM,CAAC;YACnC,GAAG,CAAC;YACJ,OAAO,EAAE,KAAK,EAAE,MAAM,GAAG;QAC3B,CAAC;IAED,OAAO,CAAC,cAAc,EAAE,OAAO,MAAM,GAAG,IAAI,iBAAiB,aAAa;;AAE5E,EACE,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,aAAa,EAAE,IAAI,KAAK,qBAClD,CAAC;yBACkB,EAAE,KAAK,SAAS,CAAC,QAAQ;AAClD,CAAC,GACK,CAAC;wBACiB,EAAE,KAAK,SAAS,CAAC,QAAQ;AACjD,CAAC,EACC;AACF;AAEA,MAAM,YAAY,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAS;;IACxD,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;kDAAE,CAAC,QAAU,MAAM,eAAe;;IACpE,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IACnC,MAAM,iBAAiB,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,gBAAgB,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;oDAAE,CAAC,QAAU,MAAM,aAAa;;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAErD;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAmB;QAC5D,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAmB;QAC9D,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;IACzC,MAAM,aAAa,eAAe,MAAM;IAExC,MAAM,YAAY,aAAa;IAE/B,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,eAAe,mBAAmB;YAExC;YAEA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YACN,cAAc;YACd,UAAU,EAAE;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;QACb,SAAU;YACR,IAAI,UAAU;gBACZ,cAAc;YAChB;QACF;IACF;IAEA,MAAM,eAAe,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,UAAU;mDAAE;gBACV,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;gBAEhC,IAAI;oBACF,MAAM,SAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;oBAEhD,MAAM,YAAY,OAAO,OAAO;oBAEhC,OAAO;wBACL,QAAQ;wBACR,SAAS,OAAO,OAAO,IAAI;wBAC3B,QAAQ,OAAO,MAAM,IAAI;oBAC3B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,MAAM;gBACR;YACF;;QACA,SAAS;mDAAE,CAAC;gBACV,cAAc;gBAEd,IAAI,OAAO,MAAM,EAAE;oBACjB,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE;gBACf,OAAO;oBACL,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;oBACX,UAAU;wBACR;4BACE,MAAM;4BACN,SAAS,OAAO,MAAM,IAAI,OAAO,OAAO,IAAI;4BAC5C,MAAM;wBACR;qBACD;gBACH;YACF;;QACA,OAAO;mDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,cAAc;oBACZ,SAAS;oBACT,QAAQ;oBACR,SAAS;gBACX;gBACA,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;YACb;;QACA,SAAS;mDAAE;gBACT;2DAAc,CAAC,OAAS,CAAC;4BACvB,GAAG,IAAI;4BACP,SAAS;wBACX,CAAC;;YACH;;IACF;IAEA,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;gDAAE;YAChC,aAAa,KAAK;YAClB,aAAa,MAAM;YACnB,cAAc;gBACZ,SAAS;gBACT,QAAQ;gBACR,SAAS;YACX;QACF;+CAAG;QAAC;KAAa;IAEjB,MAAM,gBAAgB,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QAChC,UAAU;oDAAE;gBACV,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;gBAEhC,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;oBAE/C,MAAM,YAAY,CAAC,OAAO,KAAK,IAAI,OAAO,IAAI,KAAK;oBAEnD,OAAO;wBACL,SAAS,OAAO,OAAO,IAAI;wBAC3B,QAAQ,OAAO,MAAM,IAAI;wBACzB,QAAQ;oBACV;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gBAAgB;oBAC9B,MAAM;gBACR;YACF;;QACA,SAAS;oDAAE,CAAC;gBACV,eAAe;gBAEf,IAAI,OAAO,MAAM,EAAE;oBACjB,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAAE;gBACf,OAAO;oBACL,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;oBACX,UAAU;wBACR;4BACE,MAAM;4BACN,SAAS,OAAO,MAAM,IAAI,OAAO,OAAO,IAAI;4BAC5C,MAAM;wBACR;qBACD;gBACH;YACF;;QACA,OAAO;oDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,eAAe;oBACb,SAAS;oBACT,QAAQ;oBACR,SAAS;gBACX;gBACA,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;YACb;;QACA,SAAS;oDAAE;gBACT;4DAAe,CAAC,OAAS,CAAC;4BACxB,GAAG,IAAI;4BACP,SAAS;wBACX,CAAC;;YACH;;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;iDAAE;YACjC,cAAc,KAAK;YACnB,cAAc,MAAM;YACpB,eAAe;gBACb,SAAS;gBACT,QAAQ;gBACR,SAAS;YACX;QACF;gDAAG;QAAC;KAAc;IAElB,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;4CAC1B,IACE,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACC,iEACA,CAAC,iBAAiB,aAAa,gDAC/B,iBAAiB,gDACjB,CAAC,iBAAiB,CAAC,aAAa,gDAChC;2CAEJ;QAAC;KAAc;IAGjB,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;mDAAE;YACnC,iBAAiB;QACnB;kDAAG,EAAE;IAEL,IAAI,gBAAgB;QAClB,qBACE,4SAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,2BACA,YAAY;sBAGd,cAAA,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC,sVAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,4SAAC,uVAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,4SAAC,mIAAA,CAAA,OAAI;4BAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sCAClB,cAAA,4SAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;;gDAAqC;gDACnC;8DACjB,4SAAC;oDAAK,WAAU;;sEACd,4SAAC;4DAAK,WAAU;sEAAQ;;;;;;sEACxB,4SAAC;4DAAK,WAAU;sEAAQ;;;;;;sEACxB,4SAAC;4DAAK,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;sDAG5B,4SAAC,sIAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASrC;IAEA,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA,6BACA,YAAY,4BACZ;kBAGF,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC,sVAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,4SAAC,uVAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;kCAEd,cAAA,4SAAC,mIAAA,CAAA,OAAI;4BAAC,cAAc;;gCACjB,2BACC,4SAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,4SAAC;4CAAI,WAAU;;8DACb,4SAAC,oVAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;8DAC/B,4SAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;;wDACd;wDAAW;wDAAE,eAAe,IAAI,UAAU;wDAAS;;;;;;;;;;;;;sDAIxD,4SAAC;4CAAI,WAAU;;8DACb,4SAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP;oDACF;oDACA,WAAU;8DACX;;;;;;8DAGD,4SAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,oBAAoB;8DACpC;;;;;;;;;;;;;;;;;;8CAOP,4SAAC,wIAAA,CAAA,YAAS;oCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,aAAa,UAAU;;;;;;8CAEjD,4SAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,4SAAC;4CAAI,WAAU;;8DACb,4SAAC,yTAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,4SAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAmC;;;;;;;;;;;;sDAGtD,4SAAC;4CAAI,WAAU;;8DACb,4SAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,oBAAoB;oDACtB;oDACA,WAAU;8DAET,YAAY,OAAO,iBAClB;;0EACE,4SAAC,sIAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,4SAAC;gEAAK,WAAU;0EAAU;;;;;;;uEAG5B;;;;;;8DAGJ,4SAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,oBAAoB;oDACtB;oDACA,WAAU;8DAET,WAAW,OAAO,iBACjB;;0EACE,4SAAC,sIAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,4SAAC;gEAAK,WAAU;0EAAU;;;;;;;uEAG5B;;;;;;;;;;;;;;;;;;gCAMP,+BACC,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,iBAAiB;kDAEhC,cAAA,4SAAC,uSAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQzB,4SAAC,oIAAA,CAAA,YAAS;oBACR,UAAU;oBACV,MAAM,CAAC,CAAC;oBACR,cAAc,IAAM,oBAAoB;8BAExC,cAAA,4SAAC;wBACC,WAAW;wBACX,QAAQ;wBACR,kBAAkB;wBAClB,qBAAqB;wBACrB,YAAY;wBACZ,aAAa;wBACb,gBAAgB;wBAChB,eAAe;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAvWM;;QACkB,kIAAA,CAAA,gBAAa;QACR,iIAAA,CAAA,YAAS;QACb,qIAAA,CAAA,oBAAiB;QACvB,iIAAA,CAAA,cAAW;QACN,oIAAA,CAAA,kBAAe;QA0ChB,iRAAA,CAAA,cAAW;QA8DV,iRAAA,CAAA,cAAW;;;MA7G7B", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/loading-preview.tsx"], "sourcesContent": ["\"use client\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { TextShimmer } from \"@/components/ui/text-shimmer\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { CheckCircleSolid } from \"@mynaui/icons-react\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\n\r\nconst steps = [\r\n  { text: \"Checking dependencies...\", time: 100 },\r\n  { text: \"Installing packages...\", time: 500 },\r\n  { text: \"Building application...\", time: 100 },\r\n  { text: \"Compiling components...\", time: 100 },\r\n  { text: \"Optimizing assets...\", time: 100 },\r\n  { text: \"Configuring environment...\", time: 100 },\r\n  { text: \"Starting development server...\", time: 100 },\r\n  { text: \"Initializing preview...\", time: 100 },\r\n  { text: \"Almost ready...\", time: 200 },\r\n  { text: \"Final checks...\", time: 200 },\r\n];\r\n\r\nexport default function LoadingPreview() {\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [completedSteps, setCompletedSteps] = useState<number[]>([]);\r\n  const stepsRef = useRef<HTMLDivElement>(null);\r\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  useEffect(() => {\r\n    const advanceStep = (step: number) => {\r\n      if (step >= steps.length) return;\r\n\r\n      setCurrentStep(step);\r\n      setCompletedSteps((prev) => (step > 0 ? [...prev, step - 1] : prev));\r\n\r\n      timerRef.current = setTimeout(() => {\r\n        advanceStep(step + 1);\r\n      }, steps[step].time);\r\n    };\r\n\r\n    advanceStep(0);\r\n\r\n    return () => {\r\n      if (timerRef.current) {\r\n        clearTimeout(timerRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!stepsRef.current) return;\r\n    stepsRef.current.scrollTo({\r\n      top: Math.max(0, (currentStep - 2) * 44),\r\n      behavior: \"smooth\",\r\n    });\r\n  }, [currentStep]);\r\n\r\n  const isStepCompleted = (index: number) => completedSteps.includes(index);\r\n\r\n  return (\r\n    <div className=\"absolute inset-0 z-50 flex items-center justify-center bg-background\">\r\n      <div className=\"relative w-full max-w-md overflow-hidden rounded-lg p-6\">\r\n        <div className=\"mb-6 flex flex-col items-center justify-center gap-6 text-center\">\r\n          <div className=\"relative h-8 w-8\">\r\n            <Loading className=\"h-8 w-8 animate-spin text-primary\" />\r\n          </div>\r\n          <TextShimmer className=\"text-2xl font-semibold\">Building Preview</TextShimmer>\r\n        </div>\r\n\r\n        <div className=\"relative flex h-[240px] flex-col items-stretch overflow-hidden rounded-lg\">\r\n          <div className=\"pointer-events-none absolute left-0 top-0 z-10 h-14 w-full bg-gradient-to-b from-background/85 via-background/85 to-transparent\" />\r\n\r\n          <div\r\n            ref={stepsRef}\r\n            style={{ scrollbarWidth: \"none\" }}\r\n            className=\"scrollbar-hide h-full overflow-y-auto pb-4 pt-8\"\r\n          >\r\n            {steps.map((step, index) => (\r\n              <div\r\n                key={index}\r\n                className={cn(\r\n                  \"flex items-center gap-3 rounded-md border border-transparent p-3 px-4 transition-all duration-300\",\r\n                  index === currentStep\r\n                    ? \"rounded-xl bg-sidebar-border/70 p-3 px-4 text-base font-medium text-primary shadow-sm\"\r\n                    : index < currentStep\r\n                      ? \"text-base text-muted-foreground opacity-90\"\r\n                      : \"text-base text-muted-foreground/50 opacity-70\",\r\n                  index === steps.length - 1 && \"mb-14 focus:mb-0\",\r\n                )}\r\n              >\r\n                <div className=\"flex h-5 w-5 flex-shrink-0 items-center justify-center\">\r\n                  {isStepCompleted(index) ? (\r\n                    <CheckCircleSolid className=\"h-4 w-4 text-green-500\" />\r\n                  ) : index === currentStep ? (\r\n                    <Loading className=\"size-4 animate-spin text-primary\" />\r\n                  ) : (\r\n                    <Loading className=\"size-4 animate-spin text-muted-foreground\" />\r\n                  )}\r\n                </div>\r\n                <span>{step.text}</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"pointer-events-none absolute bottom-0 left-0 z-10 h-14 w-full bg-gradient-to-t from-background via-background/90 to-transparent\" />\r\n        </div>\r\n\r\n        <p className=\"mt-4 text-center text-xs text-muted-foreground\">\r\n          This may take a moment depending on the complexity of your project\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;AALA;;;;;;AAOA,MAAM,QAAQ;IACZ;QAAE,MAAM;QAA4B,MAAM;IAAI;IAC9C;QAAE,MAAM;QAA0B,MAAM;IAAI;IAC5C;QAAE,MAAM;QAA2B,MAAM;IAAI;IAC7C;QAAE,MAAM;QAA2B,MAAM;IAAI;IAC7C;QAAE,MAAM;QAAwB,MAAM;IAAI;IAC1C;QAAE,MAAM;QAA8B,MAAM;IAAI;IAChD;QAAE,MAAM;QAAkC,MAAM;IAAI;IACpD;QAAE,MAAM;QAA2B,MAAM;IAAI;IAC7C;QAAE,MAAM;QAAmB,MAAM;IAAI;IACrC;QAAE,MAAM;QAAmB,MAAM;IAAI;CACtC;AAEc,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,WAAW,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkB;IACxC,MAAM,WAAW,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAyB;IAE/C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;wDAAc,CAAC;oBACnB,IAAI,QAAQ,MAAM,MAAM,EAAE;oBAE1B,eAAe;oBACf;gEAAkB,CAAC,OAAU,OAAO,IAAI;mCAAI;gCAAM,OAAO;6BAAE,GAAG;;oBAE9D,SAAS,OAAO,GAAG;gEAAW;4BAC5B,YAAY,OAAO;wBACrB;+DAAG,KAAK,CAAC,KAAK,CAAC,IAAI;gBACrB;;YAEA,YAAY;YAEZ;4CAAO;oBACL,IAAI,SAAS,OAAO,EAAE;wBACpB,aAAa,SAAS,OAAO;oBAC/B;gBACF;;QACF;mCAAG,EAAE;IAEL,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,SAAS,OAAO,EAAE;YACvB,SAAS,OAAO,CAAC,QAAQ,CAAC;gBACxB,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI;gBACrC,UAAU;YACZ;QACF;mCAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB,CAAC,QAAkB,eAAe,QAAQ,CAAC;IAEnE,qBACE,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC,sIAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,4SAAC,8IAAA,CAAA,cAAW;4BAAC,WAAU;sCAAyB;;;;;;;;;;;;8BAGlD,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;;;;;;sCAEf,4SAAC;4BACC,KAAK;4BACL,OAAO;gCAAE,gBAAgB;4BAAO;4BAChC,WAAU;sCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,4SAAC;oCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qGACA,UAAU,cACN,0FACA,QAAQ,cACN,+CACA,iDACN,UAAU,MAAM,MAAM,GAAG,KAAK;;sDAGhC,4SAAC;4CAAI,WAAU;sDACZ,gBAAgB,uBACf,4SAAC,8UAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;uDAC1B,UAAU,4BACZ,4SAAC,sIAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,4SAAC,sIAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAGvB,4SAAC;sDAAM,KAAK,IAAI;;;;;;;mCApBX;;;;;;;;;;sCAyBX,4SAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,4SAAC;oBAAE,WAAU;8BAAiD;;;;;;;;;;;;;;;;;AAMtE;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 1254, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/logo-link.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\n\r\nconst LogoLink = () => {\r\n  return (\r\n    <Link href=\"/app\" className=\"h-auto w-fit\">\r\n      <svg\r\n        width=\"80\"\r\n        height=\"24\"\r\n        viewBox=\"0 0 482 109\"\r\n        fill=\"none\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        className=\"text-primary\"\r\n      >\r\n        <path\r\n          d=\"M156.645 81.4936C150.337 81.4936 145.103 80.0845 140.943 77.2661C136.782 74.4477 134.232 70.1866 133.293 64.4827L142.956 62.1676C143.493 64.9189 144.399 67.0998 145.674 68.7103C146.949 70.3208 148.526 71.4616 150.405 72.1326C152.351 72.8036 154.431 73.1392 156.645 73.1392C159.933 73.1392 162.45 72.5017 164.194 71.2267C166.006 69.9517 166.912 68.3077 166.912 66.2945C166.912 64.2814 166.073 62.8051 164.396 61.8657C162.718 60.9262 160.235 60.1545 156.947 59.5506L153.525 58.9466C150.17 58.3427 147.116 57.4703 144.365 56.3296C141.614 55.1888 139.433 53.6118 137.823 51.5987C136.212 49.5856 135.407 47.0021 135.407 43.8482C135.407 39.1509 137.151 35.5272 140.641 32.9773C144.13 30.3602 148.76 29.0517 154.531 29.0517C160.101 29.0517 164.664 30.3267 168.221 32.8766C171.844 35.3595 174.193 38.7482 175.267 43.0429L165.604 45.7606C165 42.7409 163.725 40.6272 161.779 39.4193C159.833 38.1443 157.417 37.5068 154.531 37.5068C151.713 37.5068 149.499 38.0436 147.888 39.1173C146.278 40.1239 145.472 41.5666 145.472 43.4455C145.472 45.4587 146.244 46.935 147.787 47.8744C149.398 48.8139 151.545 49.5185 154.229 49.9882L157.752 50.5921C161.309 51.1961 164.564 52.0349 167.516 53.1086C170.469 54.1822 172.784 55.7256 174.461 57.7387C176.206 59.7519 177.078 62.436 177.078 65.7912C177.078 70.757 175.233 74.6155 171.542 77.3667C167.852 80.118 162.886 81.4936 156.645 81.4936Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M207.415 81.4936C202.45 81.4936 198.021 80.4871 194.129 78.474C190.304 76.3937 187.284 73.4411 185.07 69.6162C182.855 65.7913 181.748 61.2617 181.748 56.0276V54.5177C181.748 49.2836 182.855 44.7876 185.07 41.0298C187.284 37.2048 190.304 34.2523 194.129 32.172C198.021 30.0918 202.45 29.0517 207.415 29.0517C212.381 29.0517 216.81 30.0918 220.702 32.172C224.594 34.2523 227.647 37.2048 229.862 41.0298C232.076 44.7876 233.183 49.2836 233.183 54.5177V56.0276C233.183 61.2617 232.076 65.7913 229.862 69.6162C227.647 73.4411 224.594 76.3937 220.702 78.474C216.81 80.4871 212.381 81.4936 207.415 81.4936ZM207.415 72.2333C211.978 72.2333 215.669 70.7905 218.488 67.905C221.373 64.9524 222.816 60.8926 222.816 55.7256V54.8197C222.816 49.6527 221.407 45.6264 218.588 42.7409C215.77 39.7884 212.046 38.3121 207.415 38.3121C202.919 38.3121 199.229 39.7884 196.343 42.7409C193.525 45.6264 192.116 49.6527 192.116 54.8197V55.7256C192.116 60.8926 193.525 64.9524 196.343 67.905C199.229 70.7905 202.919 72.2333 207.415 72.2333Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M248.149 80.0845V39.218H234.258V30.4609H248.149V19.59C248.149 16.5703 249.055 14.1545 250.867 12.3427C252.678 10.5309 255.094 9.625 258.114 9.625H270.897V18.3821H261.436C259.49 18.3821 258.517 19.3887 258.517 21.4018V30.4609H272.81V39.218H258.517V80.0845H248.149Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M295.735 80.0845C292.715 80.0845 290.3 79.1785 288.488 77.3667C286.743 75.5549 285.871 73.1392 285.871 70.1195V39.218H272.181V30.4609H285.871V14.0539H296.238V30.4609H311.035V39.218H296.238V68.3077C296.238 70.3208 297.178 71.3274 299.057 71.3274H309.424V80.0845H295.735Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M313.997 55.5243V54.0145C313.997 48.7803 315.037 44.3179 317.117 40.6272C319.265 36.9364 322.083 34.0845 325.573 32.0714C329.062 30.0582 332.887 29.0517 337.047 29.0517C341.879 29.0517 345.57 29.9576 348.12 31.7694C350.737 33.5812 352.649 35.5272 353.857 37.6075H355.467V30.4609H365.533V90.2507C365.533 93.2704 364.627 95.6862 362.815 97.498C361.071 99.3098 358.655 100.216 355.568 100.216H322.15V91.1567H352.347C354.293 91.1567 355.266 90.1501 355.266 88.137V72.3339H353.656C352.918 73.5418 351.877 74.7832 350.535 76.0582C349.193 77.3332 347.415 78.3733 345.201 79.1785C343.053 79.9838 340.335 80.3864 337.047 80.3864C332.887 80.3864 329.028 79.4134 325.472 77.4674C321.982 75.4543 319.198 72.6023 317.117 68.9116C315.037 65.1538 313.997 60.6913 313.997 55.5243ZM339.866 71.3274C344.362 71.3274 348.052 69.9182 350.938 67.0998C353.891 64.2143 355.367 60.2552 355.367 55.2223V54.3164C355.367 49.1494 353.924 45.1903 351.039 42.439C348.153 39.6206 344.429 38.2114 339.866 38.2114C335.437 38.2114 331.746 39.6206 328.794 42.439C325.908 45.1903 324.465 49.1494 324.465 54.3164V55.2223C324.465 60.2552 325.908 64.2143 328.794 67.0998C331.746 69.9182 335.437 71.3274 339.866 71.3274Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M398.513 81.4936C393.48 81.4936 389.085 80.4535 385.327 78.3733C381.569 76.226 378.617 73.2398 376.469 69.4149C374.389 65.5228 373.349 61.0269 373.349 55.9269V54.7191C373.349 49.552 374.389 45.056 376.469 41.2311C378.549 37.339 381.435 34.3529 385.126 32.2727C388.884 30.1253 393.212 29.0517 398.11 29.0517C402.875 29.0517 407.035 30.1253 410.592 32.2727C414.215 34.3529 417.034 37.2719 419.047 41.0298C421.06 44.7876 422.067 49.183 422.067 54.2158V58.1414H383.918C384.052 62.5031 385.495 65.9926 388.246 68.6096C391.064 71.1596 394.554 72.4346 398.714 72.4346C402.606 72.4346 405.525 71.5622 407.471 69.8175C409.485 68.0728 411.028 66.0597 412.102 63.7781L420.657 68.207C419.718 70.0859 418.342 72.0655 416.53 74.1457C414.786 76.226 412.471 77.9707 409.585 79.3799C406.7 80.789 403.009 81.4936 398.513 81.4936ZM384.018 50.1895H411.498C411.229 46.4317 409.887 43.5126 407.471 41.4324C405.056 39.2851 401.902 38.2114 398.01 38.2114C394.118 38.2114 390.93 39.2851 388.447 41.4324C386.032 43.5126 384.555 46.4317 384.018 50.1895Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M429.514 80.0845V30.4609H439.68V37.9094H441.29C442.23 35.8963 443.907 34.0174 446.323 32.2727C448.739 30.528 452.329 29.6556 457.093 29.6556C460.851 29.6556 464.173 30.4944 467.058 32.172C470.011 33.8496 472.326 36.2318 474.004 39.3186C475.681 42.3383 476.52 45.9955 476.52 50.2902V80.0845H466.153V51.0954C466.153 46.8008 465.079 43.6469 462.932 41.6337C460.784 39.5535 457.832 38.5134 454.074 38.5134C449.779 38.5134 446.323 39.9226 443.706 42.7409C441.156 45.5593 439.881 49.6527 439.881 55.021V80.0845H429.514Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <g clipPath=\"url(#clip0_1503_289)\">\r\n          800%\r\n          <path\r\n            d=\"M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n        </g>\r\n        <defs>\r\n          <clipPath id=\"clip0_1503_289\">\r\n            <rect width=\"109\" height=\"109\" fill=\"currentColor\" />\r\n          </clipPath>\r\n        </defs>\r\n      </svg>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default LogoLink;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,WAAW;IACf,qBACE,4SAAC,8QAAA,CAAA,UAAI;QAAC,MAAK;QAAO,WAAU;kBAC1B,cAAA,4SAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAU;;8BAEV,4SAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,4SAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,4SAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,4SAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,4SAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,4SAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,4SAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,4SAAC;oBAAE,UAAS;;wBAAuB;sCAEjC,4SAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,4SAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,4SAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,4SAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,4SAAC;8BACC,cAAA,4SAAC;wBAAS,IAAG;kCACX,cAAA,4SAAC;4BAAK,OAAM;4BAAM,QAAO;4BAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD;KAlEM;uCAoES", "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/no-access-panel.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useState } from \"react\";\r\nimport { LuCopy } from \"react-icons/lu\";\r\nimport CloneProjectModal from \"./modal/clone-project-modal\";\r\n\r\nexport default function NoAccessPanel({ projectId }: { projectId: string }) {\r\n  const [showCloneProjectModal, setShowCloneProjectModal] = useState(false);\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <div className=\"mb-3 text-center\">\r\n        <h3 className=\"mb-1.5 text-base font-semibold\">View-only Access</h3>\r\n        <p className=\"mb-3 text-sm text-muted-foreground\">\r\n          Clone this project to create your own copy and start making changes.\r\n        </p>\r\n      </div>\r\n\r\n      <Button\r\n        onClick={() => setShowCloneProjectModal(true)}\r\n        className=\"flex items-center gap-2\"\r\n        size=\"default\"\r\n      >\r\n        <LuCopy className=\"h-4 w-4\" />\r\n        Clone Project\r\n      </Button>\r\n\r\n      <CloneProjectModal\r\n        projectId={projectId}\r\n        isOpen={showCloneProjectModal}\r\n        onClose={() => setShowCloneProjectModal(false)}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAEe,SAAS,cAAc,EAAE,SAAS,EAAyB;;IACxE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,qBACE,4SAAC;QAAI,WAAU;;0BACb,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,4SAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;0BAKpD,4SAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,yBAAyB;gBACxC,WAAU;gBACV,MAAK;;kCAEL,4SAAC,kOAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAY;;;;;;;0BAIhC,4SAAC,oKAAA,CAAA,UAAiB;gBAChB,WAAW;gBACX,QAAQ;gBACR,SAAS,IAAM,yBAAyB;;;;;;;;;;;;AAIhD;GA5BwB;KAAA", "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/sandbox-sleeping-card.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { useCheckAndStart } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { Play } from \"@mynaui/icons-react\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { useState } from \"react\";\r\nimport { ImSleepy } from \"react-icons/im\";\r\n\r\ninterface SandboxSleepingCardProps {\r\n  className?: string;\r\n}\r\n\r\nexport const SandboxSleepingCard = ({ className }: SandboxSleepingCardProps) => {\r\n  const { project } = useProject();\r\n  const startEnvironment = useCheckAndStart();\r\n  const queryClient = useQueryClient();\r\n  const [isWakingUp, setIsWakingUp] = useState(false);\r\n\r\n  const handleWakeUp = async () => {\r\n    if (!project?.project_id) return;\r\n\r\n    setIsWakingUp(true);\r\n    try {\r\n      await startEnvironment.mutateAsync(project.project_id, {\r\n        onSettled: () => {\r\n          queryClient.invalidateQueries({ queryKey: [\"get-project\", project.project_id] });\r\n        },\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to wake up sandbox:\", error);\r\n    } finally {\r\n      setIsWakingUp(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      className={cn(\r\n        \"flex h-full w-full flex-col items-center justify-center rounded-none border-none shadow-lg\",\r\n        className,\r\n      )}\r\n      border={false}\r\n    >\r\n      <CardContent className=\"w-full max-w-md space-y-6 p-6\">\r\n        <div className=\"flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2\">\r\n          <ImSleepy className=\"size-8 text-muted-foreground\" />\r\n        </div>\r\n        <div className=\"flex items-start gap-4\">\r\n          <div className=\"space-y-1.5\">\r\n            <Typography.H4>Sandbox is sleeping</Typography.H4>\r\n            <Typography.P className=\"mt-0 leading-normal\">\r\n              Your development environment is currently paused to conserve resources\r\n            </Typography.P>\r\n          </div>\r\n        </div>\r\n\r\n        <Button onClick={handleWakeUp} disabled={isWakingUp} className=\"w-full\" size=\"lg\">\r\n          {isWakingUp ? (\r\n            <>\r\n              <div className=\"mr-2 size-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n              Waking up...\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Play className=\"size-6\" />\r\n              Wake up sandbox\r\n            </>\r\n          )}\r\n        </Button>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAMO,MAAM,sBAAsB,CAAC,EAAE,SAAS,EAA4B;;IACzE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,mBAAmB,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD;IACxC,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,YAAY;QAE1B,cAAc;QACd,IAAI;YACF,MAAM,iBAAiB,WAAW,CAAC,QAAQ,UAAU,EAAE;gBACrD,WAAW;oBACT,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAe,QAAQ,UAAU;yBAAC;oBAAC;gBAChF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,4SAAC,mIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAEF,QAAQ;kBAER,cAAA,4SAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,kOAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAEtB,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,yIAAA,CAAA,UAAU,CAAC,EAAE;0CAAC;;;;;;0CACf,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;gCAAC,WAAU;0CAAsB;;;;;;;;;;;;;;;;;8BAMlD,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAc,UAAU;oBAAY,WAAU;oBAAS,MAAK;8BAC1E,2BACC;;0CACE,4SAAC;gCAAI,WAAU;;;;;;4BAAuF;;qDAIxG;;0CACE,4SAAC,6SAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;;;;;;;;AAQzC;GA5Da;;QACS,2IAAA,CAAA,aAAU;QACL,oHAAA,CAAA,mBAAgB;QACrB,yRAAA,CAAA,iBAAc;;;KAHvB", "debugId": null}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/desktop-view.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { CardContent } from \"@/components/ui/card\";\r\nimport Hint from \"@/components/ui/hint\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { LazyModal } from \"@/components/ui/modal\";\r\nimport { ResizableHandle, ResizablePanel, ResizablePanelGroup } from \"@/components/ui/resizable\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { SanboxPreviewState, useProject } from \"@/providers/project-provider\";\r\nimport {\r\n  useCurrentThreadStore,\r\n  useResetCurrentThread,\r\n  useSubscribeToAgentRunning,\r\n} from \"@/stores/current-thread\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport { useAdvancedMode, useResizablePanelConfig } from \"@/stores/settings\";\r\nimport { useSettingsStore } from \"@/stores/settings-tab\";\r\nimport { ChatMessages, ChevronDown, CogOneSolid, ExternalLink, Plus } from \"@mynaui/icons-react\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { RotateCcw, Undo2 } from \"lucide-react\";\r\nimport { lazy, useCallback, useEffect, useRef, useState } from \"react\";\r\nimport { LuHistory } from \"react-icons/lu\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport CodeEditor from \"../code-editor/code-editor\";\r\nimport SupabaseIcon from \"../icon/supabase\";\r\nimport Terminal from \"../terminal/terminal\";\r\nimport MessageInput from \"../thread/message-input\";\r\nimport { ThreadActionsDropdown } from \"../thread/thread-actions-dropdown\";\r\nimport ThreadContainer from \"../thread/thread-container\";\r\nimport ThreadHistory from \"../thread/thread-history\";\r\nimport AccountDropdown from \"./account-dropdown\";\r\nimport { BugFinder } from \"./bug-finder\";\r\nimport LoadingPreview from \"./loading-preview\";\r\nimport LogoLink from \"./logo-link\";\r\nimport SandboxUnavailable from \"./modal/contact-support-modal\";\r\nimport SupabaseSheet from \"./modal/supabase-sheet\";\r\nimport NoAccessPanel from \"./no-access-panel\";\r\nimport PublishProject from \"./publish\";\r\nimport { SandboxSleepingCard } from \"./sandbox-sleeping-card\";\r\n\r\nconst RenameModalContent = lazy(() => import(\"../thread/rename-modal\"));\r\n\r\nexport interface AppError extends Error {\r\n  id?: string;\r\n  timestamp?: string | Date;\r\n  pageUrl?: string;\r\n  type?:\r\n    | \"RUNTIME_ERROR\"\r\n    | \"CONSOLE_OUTPUT\"\r\n    | \"NETWORK_REQUEST\"\r\n    | \"RESOURCE_ERROR\"\r\n    | \"UNHANDLED_PROMISE_REJECTION\"\r\n    | string;\r\n  name: string;\r\n  message: string;\r\n  stack?: string;\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  details?: any;\r\n  // Network error specific properties\r\n  url?: string;\r\n  status?: number;\r\n  method?: string;\r\n}\r\n\r\nconst useErrors = () => {\r\n  const [errors, setErrors] = useState<AppError[]>([]);\r\n\r\n  const handleFixError = (error: unknown) => {\r\n    setErrors((prev) => prev.filter((e) => e !== (error as AppError)));\r\n  };\r\n\r\n  return { errors, setErrors, handleFixError };\r\n};\r\n\r\nconst DesktopView = ({ projectId }: { projectId: string }) => {\r\n  const { user } = useAuth();\r\n  const { isAdvancedMode } = useAdvancedMode();\r\n\r\n  const [currentPage, setCurrentPage] = useState(\"/\");\r\n  const [iframeUrl, setIframeUrl] = useState(\"\");\r\n  const [isIframeLoading, setIsIframeLoading] = useState(true);\r\n  const iframeRef = useRef<HTMLIFrameElement>(null);\r\n\r\n  const queryClient = useQueryClient();\r\n  const [showSupabaseSheet, setShowSupabaseSheet] = useState(false);\r\n\r\n  const [, setShowLongPreviewWarning] = useState(false);\r\n  const previewWarningTimer = useRef<number>(null);\r\n\r\n  const { hasAccess, project, isProjectLoading, sandboxState } = useProject();\r\n  const { createThreadFn, currentThread, setContextLimitReached } = useThread();\r\n\r\n  const { currentThreadId, isAgentRunning, setCurrentThreadId } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      currentThreadId: state.id,\r\n      isAgentRunning: state.isAgentRunning,\r\n      setCurrentThreadId: state.setId,\r\n    })),\r\n  );\r\n\r\n  const { resizablePanelConfig, setResizablePanelConfig } = useResizablePanelConfig();\r\n\r\n  const { activeTab, setActiveTab } = useNavigateFile(\r\n    useShallow((state) => ({\r\n      activeTab: state.activeTab,\r\n      setActiveTab: state.setActiveTab,\r\n    })),\r\n  );\r\n\r\n  const { errors, setErrors } = useErrors();\r\n  const [currentTab, setCurrentTab] = useState<\"tasks\" | \"version-history\">(\"tasks\");\r\n\r\n  const isFreeTier = user?.userFromDb?.plan === \"free-tier\";\r\n  const showTerminal = !isFreeTier && isAdvancedMode;\r\n\r\n  const [threadToRename, setThreadToRename] = useState<{ id: number; name: string } | null>(null);\r\n  const filteredRoutes =\r\n    project?.frontend_page_routes?.filter((route: string) => !route.includes(\"/api/\")) || [];\r\n\r\n  const isCreatingThread = createThreadFn.isPending;\r\n\r\n  const handleCreateThread = () => {\r\n    createThreadFn.mutate();\r\n  };\r\n\r\n  const { resetCurrentThread } = useResetCurrentThread();\r\n\r\n  const setSettingsTab = useSettingsStore((state) => state.setSettingsTab);\r\n\r\n  useEffect(() => {\r\n    if (project?.env_id) {\r\n      const baseUrl = `${project?.env_url}`;\r\n      const newUrl = `${baseUrl}${currentPage}`;\r\n\r\n      setIsIframeLoading(sandboxState === \"starting\");\r\n\r\n      if (project.isRunning === 0) {\r\n        return;\r\n      }\r\n\r\n      if (newUrl !== iframeUrl) {\r\n        setIsIframeLoading(true);\r\n        setIframeUrl(newUrl);\r\n      }\r\n    }\r\n  }, [project, currentPage, iframeUrl, sandboxState]);\r\n\r\n  const openInNewTab = () => {\r\n    if (iframeUrl) {\r\n      window.open(iframeUrl, \"_blank\");\r\n    }\r\n  };\r\n\r\n  const handleRouteChange = (route: string) => {\r\n    const cleanedRoute = route.includes(\"?\") ? route.split(\"?\")[0] : route;\r\n    setCurrentPage(cleanedRoute);\r\n\r\n    const now = Date.now();\r\n    setErrors((prev: AppError[]) =>\r\n      prev.map((error: AppError) => {\r\n        if (\r\n          error.timestamp &&\r\n          now - new Date(error.timestamp).getTime() < 2000 &&\r\n          error.pageUrl !== \"/404\"\r\n        ) {\r\n          return {\r\n            ...error,\r\n            pageUrl: cleanedRoute,\r\n          };\r\n        }\r\n        return error;\r\n      }),\r\n    );\r\n  };\r\n\r\n  const toggleTab = () => {\r\n    setCurrentTab(currentTab === \"tasks\" ? \"version-history\" : \"tasks\");\r\n  };\r\n\r\n  const handleIframeLoad = () => {\r\n    if (iframeUrl) {\r\n      // setIsIframeLoading(false);\r\n\r\n      if (iframeRef.current && iframeRef.current.contentWindow) {\r\n        try {\r\n          const iframeDocument = iframeRef.current.contentWindow.document;\r\n          if (!iframeDocument || !iframeDocument.head) {\r\n            debug(\"[Alert] Cannot access iframe document or head\");\r\n            return;\r\n          }\r\n\r\n          const scriptExists = iframeDocument.querySelector(\r\n            'script[src*=\"cdn.softgen.ai/script.js\"]',\r\n          );\r\n\r\n          if (!scriptExists) {\r\n            const script = iframeDocument.createElement(\"script\");\r\n            script.src = \"https://cdn.softgen.ai/script.js\";\r\n            script.async = true;\r\n            if (iframeDocument.head) {\r\n              iframeDocument.head.appendChild(script);\r\n              debug(\"Injected error monitoring script into iframe\");\r\n            }\r\n          }\r\n        } catch (error) {\r\n          debug(\"[Alert] Cannot access iframe content due to same-origin policy\", error);\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (iframeRef.current) {\r\n      iframeRef.current.style.display = activeTab === \"preview\" ? \"block\" : \"none\";\r\n    }\r\n  }, [activeTab]);\r\n\r\n  useEffect(() => {\r\n    const handleIframeMessage = (event: MessageEvent) => {\r\n      if (!iframeUrl || !event.data || typeof event.data !== \"object\") return;\r\n\r\n      try {\r\n        if (event.data.type === \"MONITOR_SCRIPT_LOADED\") {\r\n          setIsIframeLoading(false);\r\n          return;\r\n        }\r\n\r\n        if (\r\n          [\r\n            \"RUNTIME_ERROR\",\r\n            \"CONSOLE_OUTPUT\",\r\n            \"NETWORK_REQUEST\",\r\n            \"RESOURCE_ERROR\",\r\n            \"UNHANDLED_PROMISE_REJECTION\",\r\n          ].includes(event.data.type)\r\n        ) {\r\n          let newError: AppError = {\r\n            id: `iframe-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,\r\n            timestamp: new Date(),\r\n            pageUrl: currentPage,\r\n            name: \"Error\",\r\n            message: \"Unknown error\",\r\n          };\r\n\r\n          if (event.data.type === \"RUNTIME_ERROR\") {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.error.message || \"Unknown runtime error\",\r\n              stack: event.data.error.stack,\r\n              name: \"RuntimeError\",\r\n              type: \"runtime\",\r\n            };\r\n          } else if (\r\n            event.data.type === \"CONSOLE_OUTPUT\" &&\r\n            (event.data.level === \"error\" || event.data.level === \"warning\")\r\n          ) {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.message || \"Console error\",\r\n              name: \"ConsoleError\",\r\n              type: \"console\",\r\n            };\r\n          } else if (event.data.type === \"NETWORK_REQUEST\" && event.data.request) {\r\n            if (event.data.request.status >= 400 || event.data.request.error) {\r\n              newError = {\r\n                ...newError,\r\n                message:\r\n                  event.data.request.error?.message ||\r\n                  `${event.data.request.method} ${event.data.request.url} ${event.data.request.status} (${event.data.request.statusText})`,\r\n                name: \"NetworkError\",\r\n                type: \"network\",\r\n                details: {\r\n                  url: event.data.request.url,\r\n                  status: event.data.request.status,\r\n                  method: event.data.request.method,\r\n                },\r\n              };\r\n            } else {\r\n              return;\r\n            }\r\n          } else if (event.data.type === \"RESOURCE_ERROR\") {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.error.message || \"Resource loading error\",\r\n              name: \"ResourceError\",\r\n              type: \"resource\",\r\n            };\r\n          } else if (event.data.type === \"UNHANDLED_PROMISE_REJECTION\") {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.error.message || \"Unhandled promise rejection\",\r\n              stack: event.data.error.stack,\r\n              name: \"PromiseRejection\",\r\n              type: \"promise\",\r\n            };\r\n          } else {\r\n            return;\r\n          }\r\n\r\n          setErrors((prev) => [...prev, newError]);\r\n        } else if (event.data.type === \"URL_CHANGED\") {\r\n          const newPath = event.data.path || \"/\";\r\n          if (newPath !== currentPage) {\r\n            setCurrentPage(newPath);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error processing iframe message:\", error);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"message\", handleIframeMessage);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"message\", handleIframeMessage);\r\n    };\r\n  }, [iframeUrl, currentPage, setErrors]);\r\n\r\n  useEffect(() => {\r\n    if (isIframeLoading) {\r\n      previewWarningTimer.current = window.setTimeout(() => {\r\n        setShowLongPreviewWarning(true);\r\n      }, 6000);\r\n    } else {\r\n      window.clearTimeout(previewWarningTimer?.current ?? undefined);\r\n      setShowLongPreviewWarning(false);\r\n    }\r\n    return () => {\r\n      window.clearTimeout(previewWarningTimer?.current ?? undefined);\r\n    };\r\n  }, [isIframeLoading]);\r\n\r\n  const handleRefreshPreview = useCallback(() => {\r\n    setErrors([]);\r\n    setIsIframeLoading(true);\r\n\r\n    const timer = setTimeout(() => {\r\n      setIframeUrl((currentUrl) => {\r\n        if (!currentUrl) return \"\";\r\n        const separator = currentUrl.includes(\"?\") ? \"&\" : \"?\";\r\n        return `${currentUrl}${separator}refresh=${Math.random().toString(36).substr(2, 9)}`;\r\n      });\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [setErrors, setIsIframeLoading, setIframeUrl]);\r\n\r\n  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {\r\n    if (!isAgentRunning && wasRunning) {\r\n      debug(\"[DesktopView] Agent stopped, refreshing preview\");\r\n      handleRefreshPreview();\r\n    }\r\n  });\r\n\r\n  return (\r\n    <ResizablePanelGroup direction=\"horizontal\">\r\n      <ResizablePanel\r\n        minSize={28}\r\n        defaultSize={resizablePanelConfig.width}\r\n        onSizeChange={(size) => {\r\n          setResizablePanelConfig({\r\n            width: size,\r\n            previewWidth: 100 - size,\r\n            tasks: resizablePanelConfig.tasks,\r\n          });\r\n        }}\r\n        className=\"order-1 flex w-full flex-col overflow-hidden\"\r\n      >\r\n        {hasAccess ? (\r\n          <div className=\"flex h-full flex-col\">\r\n            <div\r\n              className={cn(\r\n                \"flex min-h-12 items-center justify-between px-3 py-2\",\r\n                currentThreadId && \"pb-1.5\",\r\n              )}\r\n            >\r\n              <LogoLink />\r\n\r\n              {project?.name && (\r\n                <div className=\"flex items-center gap-1\">\r\n                  {currentThread && (\r\n                    <ThreadActionsDropdown\r\n                      thread={currentThread}\r\n                      projectId={projectId}\r\n                      icon={<ChevronDown className=\"h-4 w-4\" />}\r\n                      setThreadToRename={setThreadToRename}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex items-center gap-1\">\r\n                {currentThreadId && (\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    size=\"icon\"\r\n                    className=\"hover:bg-transparent md:hover:bg-sidebar-accent\"\r\n                    disabled={isAgentRunning}\r\n                    onClick={() => {\r\n                      queryClient.invalidateQueries({ queryKey: [\"get-thread\", currentThreadId] });\r\n                      resetCurrentThread();\r\n                      setCurrentThreadId(null);\r\n                      setCurrentTab(\"tasks\");\r\n                      setContextLimitReached(false);\r\n                    }}\r\n                  >\r\n                    <Undo2 className=\"h-4 w-4\" strokeWidth={2} />\r\n                  </Button>\r\n                )}\r\n\r\n                <Button variant=\"secondary\" size=\"icon\" onClick={() => toggleTab()}>\r\n                  {currentTab === \"tasks\" ? <LuHistory /> : <ChatMessages />}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              className={cn(\r\n                \"mt-0 hidden h-full flex-col overflow-hidden bg-transparent data-[state=active]:flex\",\r\n                currentTab === \"version-history\" ? \"hidden\" : \"flex\",\r\n              )}\r\n            >\r\n              <div className={cn(\"flex-1 overflow-auto\")}>\r\n                <ThreadContainer setThreadToRename={setThreadToRename} />\r\n\r\n                <LazyModal open={!!threadToRename} onOpenChange={() => setThreadToRename(null)}>\r\n                  <RenameModalContent\r\n                    projectId={projectId}\r\n                    threadToRename={threadToRename}\r\n                    onClose={() => setThreadToRename(null)}\r\n                  />\r\n                </LazyModal>\r\n              </div>\r\n\r\n              <div className=\"relative sticky bottom-0 z-20\">\r\n                {currentThreadId ? (\r\n                  <MessageInput />\r\n                ) : (\r\n                  <div className=\"mx-2 mb-2 flex items-center\">\r\n                    <Button\r\n                      size=\"lg\"\r\n                      className=\"h-12 w-full\"\r\n                      disabled={false}\r\n                      onClick={() => handleCreateThread()}\r\n                    >\r\n                      {isCreatingThread ? (\r\n                        <>\r\n                          <Loading className=\"size-4 animate-spin text-background\" />\r\n                          Creating...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <Plus className=\"size-4\" />\r\n                          Create New Thread\r\n                        </>\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              className={cn(\r\n                \"hidden h-full overflow-auto data-[state=active]:flex\",\r\n                currentTab === \"tasks\" ? \"hidden\" : \"flex\",\r\n              )}\r\n            >\r\n              <ThreadHistory id={projectId} isActive={currentTab === \"version-history\"} />\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex h-full flex-col px-4 py-8\">\r\n            <LogoLink />\r\n\r\n            <div className=\"flex flex-1 flex-col justify-center\">\r\n              <NoAccessPanel projectId={projectId} />\r\n            </div>\r\n          </div>\r\n        )}\r\n      </ResizablePanel>\r\n\r\n      <ResizableHandle className=\"order-2\" />\r\n\r\n      <ResizablePanel\r\n        minSize={28}\r\n        defaultSize={resizablePanelConfig.previewWidth}\r\n        onSizeChange={(size) => {\r\n          setResizablePanelConfig({ previewWidth: size, width: 100 - size });\r\n        }}\r\n        className={cn(\"relative order-3 flex w-full flex-1 rounded-none border-l-0\")}\r\n      >\r\n        {hasAccess ? (\r\n          <Tabs\r\n            defaultValue=\"preview\"\r\n            className=\"h-full w-full rounded-none border-none\"\r\n            value={activeTab}\r\n            onValueChange={setActiveTab}\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"flex min-h-11 w-full items-center justify-between p-1 pt-1.5\">\r\n                <div className=\"flex items-center gap-1\">\r\n                  <TabsList className=\"flex w-fit items-center justify-center gap-2 border-l-0\">\r\n                    <TabsTrigger\r\n                      value=\"preview\"\r\n                      className=\"w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0\"\r\n                    >\r\n                      Preview\r\n                    </TabsTrigger>\r\n                    <TabsTrigger value=\"code-editor\" className=\"w-fit\" disabled={false} title=\"\">\r\n                      Code Editor\r\n                    </TabsTrigger>\r\n                    {showTerminal && (\r\n                      <TabsTrigger value=\"terminal\" className=\"w-fit\" disabled={false} title=\"\">\r\n                        Terminal\r\n                      </TabsTrigger>\r\n                    )}\r\n                  </TabsList>\r\n                </div>\r\n\r\n                <div className=\"mr-1 flex items-center gap-1\">\r\n                  {!project?.onboarding_completed && (\r\n                    <Button\r\n                      variant=\"secondary\"\r\n                      size=\"sm\"\r\n                      className={cn(\r\n                        \"ml-auto flex items-center justify-center\",\r\n                        \"border border-[#34B27B]/20 bg-[#006239]/90 text-[#fafafa]/90 hover:bg-[#006239] hover:text-[#fafafa] dark:border-[#34B27B]/15 dark:text-primary/90 dark:hover:text-primary\",\r\n                      )}\r\n                      disabled={false}\r\n                      onClick={() => setShowSupabaseSheet(true)}\r\n                    >\r\n                      <SupabaseIcon className=\"size-4\" />\r\n                      <span className=\"text-sm font-semibold\">Supabase</span>\r\n                    </Button>\r\n                  )}\r\n\r\n                  <PublishProject />\r\n\r\n                  <Button variant=\"secondary\" size=\"icon\" onClick={() => setSettingsTab(\"project\")}>\r\n                    <CogOneSolid />\r\n                  </Button>\r\n\r\n                  <AccountDropdown />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              className={cn(\r\n                \"relative -mt-0.5 h-full w-full\",\r\n                activeTab === \"preview\" ? \"block\" : \"hidden\",\r\n              )}\r\n            >\r\n              <TabsContent forceMount value=\"preview\" className={cn(\"relative h-[90vh] w-full\")}>\r\n                <div className=\"flex w-full items-center justify-between gap-2 rounded-tl-2xl border-y border-l border-primary/10 bg-background p-1 py-1 text-sm font-medium md:px-2\">\r\n                  <Hint label=\"Refresh Preview\" side=\"bottom\" delayDuration={125}>\r\n                    <Button\r\n                      size=\"icon\"\r\n                      className=\"size-7\"\r\n                      variant=\"ghost\"\r\n                      onClick={handleRefreshPreview}\r\n                      disabled={isAgentRunning}\r\n                    >\r\n                      {isIframeLoading ? (\r\n                        <Loading className=\"size-4 animate-spin\" />\r\n                      ) : (\r\n                        <RotateCcw\r\n                          className=\"size-4 text-primary/90 hover:text-primary\"\r\n                          strokeWidth={2}\r\n                        />\r\n                      )}\r\n                    </Button>\r\n                  </Hint>\r\n                  {isProjectLoading ? (\r\n                    <Skeleton className=\"mx-0.5 my-0 h-6 w-full\" />\r\n                  ) : (\r\n                    filteredRoutes.length > 0 && (\r\n                      <Select value={currentPage} onValueChange={handleRouteChange}>\r\n                        <SelectTrigger className=\"mx-0.5 my-0 h-[1.7rem] w-full rounded-full bg-sidebar-accent py-0 hover:bg-sidebar-accent/40\">\r\n                          <SelectValue placeholder=\"Select route\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent align=\"center\" className=\"w-72\">\r\n                          {filteredRoutes.map((route: string) => (\r\n                            <SelectItem className=\"mx-2\" key={route} value={route}>\r\n                              {route}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                    )\r\n                  )}\r\n\r\n                  <div className=\"ml-auto flex items-center gap-1\">\r\n                    <Button variant=\"ghost\" size=\"icon\" className=\"size-7\" onClick={openInNewTab}>\r\n                      <ExternalLink className=\"size-4 text-primary/90 hover:text-primary\" />\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n                <PreviewPanel\r\n                  iframeUrl={iframeUrl}\r\n                  handleIframeLoad={handleIframeLoad}\r\n                  iframeRef={iframeRef}\r\n                  isIframeLoading={isIframeLoading}\r\n                  sandboxState={sandboxState}\r\n                />\r\n              </TabsContent>\r\n              {sandboxState === \"idle\" && (\r\n                <div className=\"mx-auto flex w-full items-center justify-center\">\r\n                  <BugFinder projectId={projectId} errors={errors} setErrors={setErrors} />\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div\r\n              className={cn(\r\n                \"relative -mt-0.5 h-full w-full\",\r\n                activeTab === \"code-editor\" ? \"block\" : \"hidden\",\r\n              )}\r\n            >\r\n              <TabsContent forceMount value=\"code-editor\" className={cn(\"h-screen w-full\")}>\r\n                <CardContent className=\"h-full rounded-tl-2xl border-y border-l border-primary/10 bg-background p-0\">\r\n                  <CodeEditor projectId={projectId} envId={project?.env_id || \"\"} />\r\n                </CardContent>\r\n              </TabsContent>\r\n            </div>\r\n\r\n            {showTerminal && (\r\n              <TabsContent\r\n                forceMount\r\n                value=\"terminal\"\r\n                className={cn(\r\n                  \"mt-1 h-[calc(100vh-40px)] w-full rounded-tl-2xl\",\r\n                  activeTab !== \"terminal\" ? \"hidden\" : \"block\",\r\n                )}\r\n              >\r\n                <CardContent className=\"h-full rounded-tl-2xl border-y border-l border-primary/10 bg-background p-0\">\r\n                  <Terminal projectId={projectId} />\r\n                </CardContent>\r\n              </TabsContent>\r\n            )}\r\n          </Tabs>\r\n        ) : (\r\n          <div className=\"w-full\">\r\n            <PreviewPanel\r\n              iframeUrl={iframeUrl}\r\n              handleIframeLoad={handleIframeLoad}\r\n              iframeRef={iframeRef}\r\n              isIframeLoading={isIframeLoading}\r\n              className=\"h-full\"\r\n              sandboxState={sandboxState}\r\n            />\r\n          </div>\r\n        )}\r\n      </ResizablePanel>\r\n\r\n      <SupabaseSheet isOpen={showSupabaseSheet} onClose={() => setShowSupabaseSheet(false)} />\r\n    </ResizablePanelGroup>\r\n  );\r\n};\r\n\r\nfunction PreviewPanel({\r\n  iframeUrl,\r\n  iframeRef,\r\n  isIframeLoading,\r\n  className,\r\n  sandboxState,\r\n  handleIframeLoad,\r\n}: {\r\n  iframeUrl: string;\r\n  iframeRef: React.RefObject<HTMLIFrameElement | null>;\r\n  isIframeLoading: boolean;\r\n  className?: string;\r\n  sandboxState: SanboxPreviewState;\r\n  handleIframeLoad: () => void;\r\n}) {\r\n  return (\r\n    <CardContent\r\n      className={cn(\"z-20 h-[calc(100vh-90px)] border-l border-foreground/10 p-0\", className)}\r\n    >\r\n      <div className=\"relative flex h-full w-full flex-1 flex-col\">\r\n        {sandboxState === \"unavailable\" ? (\r\n          <div className=\"flex h-full items-center justify-center\">\r\n            <SandboxUnavailable />\r\n          </div>\r\n        ) : sandboxState === \"sleeping\" ? (\r\n          <div className=\"flex h-full items-center justify-center\">\r\n            <SandboxSleepingCard />\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/*\r\n            Security Attributes Explanation:\r\n\r\n              sandbox: Controls what the iframe content can do\r\n              - allow-scripts: Run JavaScript\r\n              - allow-same-origin: Access same-origin resources\r\n              - allow-forms: Submit forms\r\n              - allow-popups: Open new windows/tabs\r\n              - allow-modals: Show modal dialogs (alert, confirm, etc.)\r\n              - allow-downloads: Trigger file downloads\r\n              - allow-clipboard-*: Enable clipboard operations\r\n              - allow-storage-access-by-user-activation: Allow storage access after user interaction\r\n              - allow-presentation: Enable presentation features (like screen sharing)\r\n\r\n            allow: Controls which browser features the iframe can access\r\n            - clipboard-*: Read/write to clipboard\r\n            - geolocation: Access device location\r\n            - microphone/camera: Access media devices\r\n            - display-capture: Screen sharing capability\r\n            - fullscreen: Enter fullscreen mode\r\n            - web-share: Use Web Share API\r\n          */}\r\n            {iframeUrl && (\r\n              <iframe\r\n                src={iframeUrl}\r\n                className=\"h-full w-full\"\r\n                title=\"Web Application Preview\"\r\n                style={{\r\n                  overflow: \"auto\",\r\n                  scrollbarWidth: \"thin\",\r\n                  scrollbarColor: \"var(--muted) transparent\",\r\n                  colorScheme: \"normal\",\r\n                }}\r\n                sandbox=\"allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-storage-access-by-user-activation allow-presentation\"\r\n                allow=\"clipboard-write; clipboard-read; geolocation *; microphone *; camera *; display-capture *; fullscreen; web-share\"\r\n                onLoad={handleIframeLoad}\r\n                ref={iframeRef}\r\n                referrerPolicy=\"no-referrer\"\r\n                loading=\"lazy\"\r\n              />\r\n            )}\r\n            {isIframeLoading && <LoadingPreview />}\r\n          </>\r\n        )}\r\n      </div>\r\n    </CardContent>\r\n  );\r\n}\r\n\r\nexport default DesktopView;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,mCAAqB,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE;KAA1B;AAwBN,MAAM,YAAY;;IAChB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEnD,MAAM,iBAAiB,CAAC;QACtB,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,MAAO;IAChD;IAEA,OAAO;QAAE;QAAQ;QAAW;IAAe;AAC7C;GARM;AAUN,MAAM,cAAc,CAAC,EAAE,SAAS,EAAyB;;IACvD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAEzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,GAAG,0BAA0B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,sBAAsB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAU;IAE3C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IACxE,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAE1E,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD,EAClF,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;wDAAE,CAAC,QAAU,CAAC;gBACrB,iBAAiB,MAAM,EAAE;gBACzB,gBAAgB,MAAM,cAAc;gBACpC,oBAAoB,MAAM,KAAK;YACjC,CAAC;;IAGH,MAAM,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,0BAAuB,AAAD;IAEhF,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAChD,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;kDAAE,CAAC,QAAU,CAAC;gBACrB,WAAW,MAAM,SAAS;gBAC1B,cAAc,MAAM,YAAY;YAClC,CAAC;;IAGH,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAA+B;IAE1E,MAAM,aAAa,MAAM,YAAY,SAAS;IAC9C,MAAM,eAAe,CAAC,cAAc;IAEpC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAuC;IAC1F,MAAM,iBACJ,SAAS,sBAAsB,OAAO,CAAC,QAAkB,CAAC,MAAM,QAAQ,CAAC,aAAa,EAAE;IAE1F,MAAM,mBAAmB,eAAe,SAAS;IAEjD,MAAM,qBAAqB;QACzB,eAAe,MAAM;IACvB;IAEA,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD;IAEnD,MAAM,iBAAiB,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;wDAAE,CAAC,QAAU,MAAM,cAAc;;IAEvE,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,SAAS,QAAQ;gBACnB,MAAM,UAAU,GAAG,SAAS,SAAS;gBACrC,MAAM,SAAS,GAAG,UAAU,aAAa;gBAEzC,mBAAmB,iBAAiB;gBAEpC,IAAI,QAAQ,SAAS,KAAK,GAAG;oBAC3B;gBACF;gBAEA,IAAI,WAAW,WAAW;oBACxB,mBAAmB;oBACnB,aAAa;gBACf;YACF;QACF;gCAAG;QAAC;QAAS;QAAa;QAAW;KAAa;IAElD,MAAM,eAAe;QACnB,IAAI,WAAW;YACb,OAAO,IAAI,CAAC,WAAW;QACzB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,eAAe,MAAM,QAAQ,CAAC,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QACjE,eAAe;QAEf,MAAM,MAAM,KAAK,GAAG;QACpB,UAAU,CAAC,OACT,KAAK,GAAG,CAAC,CAAC;gBACR,IACE,MAAM,SAAS,IACf,MAAM,IAAI,KAAK,MAAM,SAAS,EAAE,OAAO,KAAK,QAC5C,MAAM,OAAO,KAAK,QAClB;oBACA,OAAO;wBACL,GAAG,KAAK;wBACR,SAAS;oBACX;gBACF;gBACA,OAAO;YACT;IAEJ;IAEA,MAAM,YAAY;QAChB,cAAc,eAAe,UAAU,oBAAoB;IAC7D;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW;YACb,6BAA6B;YAE7B,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,aAAa,EAAE;gBACxD,IAAI;oBACF,MAAM,iBAAiB,UAAU,OAAO,CAAC,aAAa,CAAC,QAAQ;oBAC/D,IAAI,CAAC,kBAAkB,CAAC,eAAe,IAAI,EAAE;wBAC3C,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACN;oBACF;oBAEA,MAAM,eAAe,eAAe,aAAa,CAC/C;oBAGF,IAAI,CAAC,cAAc;wBACjB,MAAM,SAAS,eAAe,aAAa,CAAC;wBAC5C,OAAO,GAAG,GAAG;wBACb,OAAO,KAAK,GAAG;wBACf,IAAI,eAAe,IAAI,EAAE;4BACvB,eAAe,IAAI,CAAC,WAAW,CAAC;4BAChC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACR;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,kEAAkE;gBAC1E;YACF;QACF;IACF;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,cAAc,YAAY,UAAU;YACxE;QACF;gCAAG;QAAC;KAAU;IAEd,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;6DAAsB,CAAC;oBAC3B,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;oBAEjE,IAAI;wBACF,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,yBAAyB;4BAC/C,mBAAmB;4BACnB;wBACF;wBAEA,IACE;4BACE;4BACA;4BACA;4BACA;4BACA;yBACD,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,GAC1B;4BACA,IAAI,WAAqB;gCACvB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;gCACpE,WAAW,IAAI;gCACf,SAAS;gCACT,MAAM;gCACN,SAAS;4BACX;4BAEA,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,iBAAiB;gCACvC,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;oCACrC,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK;oCAC7B,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO,IACL,MAAM,IAAI,CAAC,IAAI,KAAK,oBACpB,CAAC,MAAM,IAAI,CAAC,KAAK,KAAK,WAAW,MAAM,IAAI,CAAC,KAAK,KAAK,SAAS,GAC/D;gCACA,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,OAAO,IAAI;oCAC/B,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,qBAAqB,MAAM,IAAI,CAAC,OAAO,EAAE;gCACtE,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;oCAChE,WAAW;wCACT,GAAG,QAAQ;wCACX,SACE,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,WAC1B,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;wCAC1H,MAAM;wCACN,MAAM;wCACN,SAAS;4CACP,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG;4CAC3B,QAAQ,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM;4CACjC,QAAQ,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM;wCACnC;oCACF;gCACF,OAAO;oCACL;gCACF;4BACF,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,kBAAkB;gCAC/C,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;oCACrC,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,+BAA+B;gCAC5D,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;oCACrC,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK;oCAC7B,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO;gCACL;4BACF;4BAEA;6EAAU,CAAC,OAAS;2CAAI;wCAAM;qCAAS;;wBACzC,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,eAAe;4BAC5C,MAAM,UAAU,MAAM,IAAI,CAAC,IAAI,IAAI;4BACnC,IAAI,YAAY,aAAa;gCAC3B,eAAe;4BACjB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;oBACpD;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC;yCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;gCAAG;QAAC;QAAW;QAAa;KAAU;IAEtC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,iBAAiB;gBACnB,oBAAoB,OAAO,GAAG,OAAO,UAAU;6CAAC;wBAC9C,0BAA0B;oBAC5B;4CAAG;YACL,OAAO;gBACL,OAAO,YAAY,CAAC,qBAAqB,WAAW;gBACpD,0BAA0B;YAC5B;YACA;yCAAO;oBACL,OAAO,YAAY,CAAC,qBAAqB,WAAW;gBACtD;;QACF;gCAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;yDAAE;YACvC,UAAU,EAAE;YACZ,mBAAmB;YAEnB,MAAM,QAAQ;uEAAW;oBACvB;+EAAa,CAAC;4BACZ,IAAI,CAAC,YAAY,OAAO;4BACxB,MAAM,YAAY,WAAW,QAAQ,CAAC,OAAO,MAAM;4BACnD,OAAO,GAAG,aAAa,UAAU,QAAQ,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;wBACtF;;gBACF;sEAAG;YAEH;iEAAO,IAAM,aAAa;;QAC5B;wDAAG;QAAC;QAAW;QAAoB;KAAa;IAEhD,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD;kDAAE,CAAC,gBAAgB;YAC1C,IAAI,CAAC,kBAAkB,YAAY;gBACjC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBACN;YACF;QACF;;IAEA,qBACE,4SAAC,wIAAA,CAAA,sBAAmB;QAAC,WAAU;;0BAC7B,4SAAC,wIAAA,CAAA,iBAAc;gBACb,SAAS;gBACT,aAAa,qBAAqB,KAAK;gBACvC,cAAc,CAAC;oBACb,wBAAwB;wBACtB,OAAO;wBACP,cAAc,MAAM;wBACpB,OAAO,qBAAqB,KAAK;oBACnC;gBACF;gBACA,WAAU;0BAET,0BACC,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,mBAAmB;;8CAGrB,4SAAC,8IAAA,CAAA,UAAQ;;;;;gCAER,SAAS,sBACR,4SAAC;oCAAI,WAAU;8CACZ,+BACC,4SAAC,8JAAA,CAAA,wBAAqB;wCACpB,QAAQ;wCACR,WAAW;wCACX,oBAAM,4SAAC,2TAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAC7B,mBAAmB;;;;;;;;;;;8CAM3B,4SAAC;oCAAI,WAAU;;wCACZ,iCACC,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,UAAU;4CACV,SAAS;gDACP,YAAY,iBAAiB,CAAC;oDAAE,UAAU;wDAAC;wDAAc;qDAAgB;gDAAC;gDAC1E;gDACA,mBAAmB;gDACnB,cAAc;gDACd,uBAAuB;4CACzB;sDAEA,cAAA,4SAAC,+RAAA,CAAA,QAAK;gDAAC,WAAU;gDAAU,aAAa;;;;;;;;;;;sDAI5C,4SAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,MAAK;4CAAO,SAAS,IAAM;sDACpD,eAAe,wBAAU,4SAAC,kOAAA,CAAA,YAAS;;;;qEAAM,4SAAC,6TAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;sCAK7D,4SAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA,eAAe,oBAAoB,WAAW;;8CAGhD,4SAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;;sDACjB,4SAAC,oJAAA,CAAA,UAAe;4CAAC,mBAAmB;;;;;;sDAEpC,4SAAC,oIAAA,CAAA,YAAS;4CAAC,MAAM,CAAC,CAAC;4CAAgB,cAAc,IAAM,kBAAkB;sDACvE,cAAA,4SAAC;gDACC,WAAW;gDACX,gBAAgB;gDAChB,SAAS,IAAM,kBAAkB;;;;;;;;;;;;;;;;;8CAKvC,4SAAC;oCAAI,WAAU;8CACZ,gCACC,4SAAC,iJAAA,CAAA,UAAY;;;;6DAEb,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;4CACV,SAAS,IAAM;sDAEd,iCACC;;kEACE,4SAAC,sIAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAwC;;6EAI7D;;kEACE,4SAAC,6SAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAUzC,4SAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,eAAe,UAAU,WAAW;sCAGtC,cAAA,4SAAC,kJAAA,CAAA,UAAa;gCAAC,IAAI;gCAAW,UAAU,eAAe;;;;;;;;;;;;;;;;yCAI3D,4SAAC;oBAAI,WAAU;;sCACb,4SAAC,8IAAA,CAAA,UAAQ;;;;;sCAET,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC,uJAAA,CAAA,UAAa;gCAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;0BAMlC,4SAAC,wIAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;0BAE3B,4SAAC,wIAAA,CAAA,iBAAc;gBACb,SAAS;gBACT,aAAa,qBAAqB,YAAY;gBAC9C,cAAc,CAAC;oBACb,wBAAwB;wBAAE,cAAc;wBAAM,OAAO,MAAM;oBAAK;gBAClE;gBACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;0BAEb,0BACC,4SAAC,mIAAA,CAAA,OAAI;oBACH,cAAa;oBACb,WAAU;oBACV,OAAO;oBACP,eAAe;;sCAEf,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC,mIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,4SAAC,mIAAA,CAAA,cAAW;oDACV,OAAM;oDACN,WAAU;8DACX;;;;;;8DAGD,4SAAC,mIAAA,CAAA,cAAW;oDAAC,OAAM;oDAAc,WAAU;oDAAQ,UAAU;oDAAO,OAAM;8DAAG;;;;;;gDAG5E,8BACC,4SAAC,mIAAA,CAAA,cAAW;oDAAC,OAAM;oDAAW,WAAU;oDAAQ,UAAU;oDAAO,OAAM;8DAAG;;;;;;;;;;;;;;;;;kDAOhF,4SAAC;wCAAI,WAAU;;4CACZ,CAAC,SAAS,sCACT,4SAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA;gDAEF,UAAU;gDACV,SAAS,IAAM,qBAAqB;;kEAEpC,4SAAC,uIAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;kEACxB,4SAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAI5C,4SAAC,kJAAA,CAAA,UAAc;;;;;0DAEf,4SAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAY,MAAK;gDAAO,SAAS,IAAM,eAAe;0DACpE,cAAA,4SAAC,oUAAA,CAAA,cAAW;;;;;;;;;;0DAGd,4SAAC,qJAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;sCAKtB,4SAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kCACA,cAAc,YAAY,UAAU;;8CAGtC,4SAAC,mIAAA,CAAA,cAAW;oCAAC,UAAU;oCAAC,OAAM;oCAAU,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;;sDACpD,4SAAC;4CAAI,WAAU;;8DACb,4SAAC,mIAAA,CAAA,UAAI;oDAAC,OAAM;oDAAkB,MAAK;oDAAS,eAAe;8DACzD,cAAA,4SAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,WAAU;wDACV,SAAQ;wDACR,SAAS;wDACT,UAAU;kEAET,gCACC,4SAAC,sIAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,4SAAC,uSAAA,CAAA,YAAS;4DACR,WAAU;4DACV,aAAa;;;;;;;;;;;;;;;;gDAKpB,iCACC,4SAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;2DAEpB,eAAe,MAAM,GAAG,mBACtB,4SAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAa,eAAe;;sEACzC,4SAAC,qIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,4SAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,4SAAC,qIAAA,CAAA,gBAAa;4DAAC,OAAM;4DAAS,WAAU;sEACrC,eAAe,GAAG,CAAC,CAAC,sBACnB,4SAAC,qIAAA,CAAA,aAAU;oEAAC,WAAU;oEAAmB,OAAO;8EAC7C;mEAD+B;;;;;;;;;;;;;;;;8DAS5C,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;wDAAS,SAAS;kEAC9D,cAAA,4SAAC,6TAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAK9B,4SAAC;4CACC,WAAW;4CACX,kBAAkB;4CAClB,WAAW;4CACX,iBAAiB;4CACjB,cAAc;;;;;;;;;;;;gCAGjB,iBAAiB,wBAChB,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,wJAAA,CAAA,YAAS;wCAAC,WAAW;wCAAW,QAAQ;wCAAQ,WAAW;;;;;;;;;;;;;;;;;sCAKlE,4SAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kCACA,cAAc,gBAAgB,UAAU;sCAG1C,cAAA,4SAAC,mIAAA,CAAA,cAAW;gCAAC,UAAU;gCAAC,OAAM;gCAAc,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;0CACxD,cAAA,4SAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,4SAAC,uJAAA,CAAA,UAAU;wCAAC,WAAW;wCAAW,OAAO,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;wBAKjE,8BACC,4SAAC,mIAAA,CAAA,cAAW;4BACV,UAAU;4BACV,OAAM;4BACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mDACA,cAAc,aAAa,WAAW;sCAGxC,cAAA,4SAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4SAAC,2IAAA,CAAA,UAAQ;oCAAC,WAAW;;;;;;;;;;;;;;;;;;;;;yCAM7B,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBACC,WAAW;wBACX,kBAAkB;wBAClB,WAAW;wBACX,iBAAiB;wBACjB,WAAU;wBACV,cAAc;;;;;;;;;;;;;;;;0BAMtB,4SAAC,4JAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAmB,SAAS,IAAM,qBAAqB;;;;;;;;;;;;AAGpF;IA1kBM;;QACa,wIAAA,CAAA,UAAO;QACG,4HAAA,CAAA,kBAAe;QAOtB,yRAAA,CAAA,iBAAc;QAM6B,2IAAA,CAAA,aAAU;QACP,iIAAA,CAAA,YAAS;QAEX,qIAAA,CAAA,wBAAqB;QAQ3B,4HAAA,CAAA,0BAAuB;QAE7C,oIAAA,CAAA,kBAAe;QAOrB;QAgBC,qIAAA,CAAA,wBAAqB;QAE7B,mIAAA,CAAA,mBAAgB;QA4NvC,qIAAA,CAAA,6BAA0B;;;MAjRtB;AA4kBN,SAAS,aAAa,EACpB,SAAS,EACT,SAAS,EACT,eAAe,EACf,SAAS,EACT,YAAY,EACZ,gBAAgB,EAQjB;IACC,qBACE,4SAAC,mIAAA,CAAA,cAAW;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+DAA+D;kBAE7E,cAAA,4SAAC;YAAI,WAAU;sBACZ,iBAAiB,8BAChB,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC,sKAAA,CAAA,UAAkB;;;;;;;;;uBAEnB,iBAAiB,2BACnB,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC,6JAAA,CAAA,sBAAmB;;;;;;;;;qCAGtB;;oBAuBG,2BACC,4SAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAM;wBACN,OAAO;4BACL,UAAU;4BACV,gBAAgB;4BAChB,gBAAgB;4BAChB,aAAa;wBACf;wBACA,SAAQ;wBACR,OAAM;wBACN,QAAQ;wBACR,KAAK;wBACL,gBAAe;wBACf,SAAQ;;;;;;oBAGX,iCAAmB,4SAAC,oJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;AAM/C;MA7ES;uCA+EM", "debugId": null}}, {"offset": {"line": 2822, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/mobile-view.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { CardContent } from \"@/components/ui/card\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { LazyModal, Modal, ModalContent } from \"@/components/ui/modal\";\r\nimport { SandboxSleepingCard } from \"@/components/ui/sandbox-sleeping-card\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { useCurrentThreadStore, useSubscribeToAgentRunning } from \"@/stores/current-thread\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport { ExternalLink, Plus } from \"@mynaui/icons-react\";\r\nimport { RotateCcw } from \"lucide-react\";\r\nimport { lazy, useCallback, useEffect, useRef, useState } from \"react\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport Hint from \"../../components/ui/hint\";\r\nimport CodeEditor from \"../code-editor/code-editor\";\r\nimport MessageInput from \"../thread/message-input\";\r\nimport ThreadContainer from \"../thread/thread-container\";\r\nimport ThreadHistory from \"../thread/thread-history\";\r\nimport { BugFinder } from \"./bug-finder\";\r\nimport LoadingPreview from \"./loading-preview\";\r\nimport SandboxUnavailable from \"./modal/contact-support-modal\";\r\nimport SupabaseSheet from \"./modal/supabase-sheet\";\r\n\r\nconst RenameModalContent = lazy(() => import(\"../thread/rename-modal\"));\r\n\r\ninterface AppError extends Error {\r\n  id?: string;\r\n  timestamp?: string | Date;\r\n  pageUrl?: string;\r\n  type?: string;\r\n  name: string;\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  details?: any;\r\n}\r\n\r\ntype Props = {\r\n  projectId: string;\r\n};\r\n\r\nconst useErrors = () => {\r\n  const [errors, setErrors] = useState<AppError[]>([]);\r\n\r\n  const handleFixError = (error: unknown) => {\r\n    setErrors((prev) => prev.filter((e) => e !== (error as AppError)));\r\n  };\r\n\r\n  return { errors, setErrors, handleFixError };\r\n};\r\n\r\nconst MobileView = ({ projectId }: Props) => {\r\n  const [currentPage, setCurrentPage] = useState(\"/\");\r\n  const [iframeUrl, setIframeUrl] = useState(\"\");\r\n  const isMobile = useIsMobile();\r\n  const [isIframeLoading, setIsIframeLoading] = useState(true);\r\n  const iframeRef = useRef<HTMLIFrameElement>(null);\r\n\r\n  const [showSupabaseSheet, setShowSupabaseSheet] = useState(false);\r\n\r\n  const [, setShowLongPreviewWarning] = useState(false);\r\n  const previewWarningTimer = useRef<number>(null);\r\n\r\n  const { project, isProjectLoading, sandboxState } = useProject();\r\n  const { createThreadFn } = useThread();\r\n  const { currentThreadId, isAgentRunning } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      currentThreadId: state.id,\r\n      isAgentRunning: state.isAgentRunning,\r\n    })),\r\n  );\r\n  const { activeTab, setActiveTab, showDrawer, setShowDrawer } = useNavigateFile(\r\n    useShallow((state) => ({\r\n      activeTab: state.activeTab,\r\n      setActiveTab: state.setActiveTab,\r\n      showDrawer: state.showDrawer,\r\n      setShowDrawer: state.setShowDrawer,\r\n    })),\r\n  );\r\n\r\n  const [threadToRename, setThreadToRename] = useState<{ id: number; name: string } | null>(null);\r\n\r\n  const { errors, setErrors } = useErrors();\r\n\r\n  const filteredRoutes =\r\n    project?.frontend_page_routes?.filter((route: string) => !route.includes(\"/api/\")) || [];\r\n\r\n  const isCreatingThread = createThreadFn.isPending;\r\n\r\n  const handleCreateThread = () => {\r\n    createThreadFn.mutate();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (project?.env_id) {\r\n      const baseUrl = `${project?.env_url}`;\r\n      const newUrl = `${baseUrl}${currentPage}`;\r\n\r\n      setIsIframeLoading(sandboxState === \"starting\");\r\n\r\n      if (project.isRunning === 0) {\r\n        return;\r\n      }\r\n\r\n      if (newUrl !== iframeUrl) {\r\n        setIsIframeLoading(true);\r\n        setIframeUrl(newUrl);\r\n      }\r\n    }\r\n  }, [project, currentPage, iframeUrl, sandboxState]);\r\n\r\n  const handleRouteChange = (route: string) => {\r\n    const cleanedRoute = route.includes(\"?\") ? route.split(\"?\")[0] : route;\r\n    setCurrentPage(cleanedRoute);\r\n\r\n    const now = Date.now();\r\n    setErrors((prev: AppError[]) =>\r\n      prev.map((error: AppError) => {\r\n        if (\r\n          error.timestamp &&\r\n          now - new Date(error.timestamp).getTime() < 2000 &&\r\n          error.pageUrl !== \"/404\"\r\n        ) {\r\n          return {\r\n            ...error,\r\n            pageUrl: cleanedRoute,\r\n          };\r\n        }\r\n        return error;\r\n      }),\r\n    );\r\n  };\r\n\r\n  const handleIframeLoad = () => {\r\n    // Only mark as loaded if we have a valid iframe URL\r\n    if (iframeUrl) {\r\n      // setIsIframeLoading(false);\r\n\r\n      // Inject error monitoring script if needed\r\n      if (iframeRef.current && iframeRef.current.contentWindow) {\r\n        try {\r\n          // Check if we can access the iframe content (same-origin policy)\r\n          const iframeDocument = iframeRef.current.contentWindow.document;\r\n          if (!iframeDocument || !iframeDocument.head) {\r\n            debug(\"[Alert] Cannot access iframe document or head\");\r\n            return;\r\n          }\r\n\r\n          const scriptExists = iframeDocument.querySelector(\r\n            'script[src*=\"cdn.softgen.ai/script.js\"]',\r\n          );\r\n\r\n          if (!scriptExists) {\r\n            const script = iframeDocument.createElement(\"script\");\r\n            script.src = \"https://cdn.softgen.ai/script.js\";\r\n            script.async = true;\r\n            if (iframeDocument.head) {\r\n              iframeDocument.head.appendChild(script);\r\n              debug(\"Injected error monitoring script into iframe\");\r\n            }\r\n          }\r\n        } catch (error) {\r\n          // This is expected for cross-origin iframes\r\n          debug(\"Cannot access iframe content due to same-origin policy\", error);\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (iframeRef.current) {\r\n      iframeRef.current.style.display = activeTab === \"preview\" ? \"block\" : \"none\";\r\n    }\r\n  }, [activeTab]);\r\n\r\n  useEffect(() => {\r\n    const handleIframeMessage = (event: MessageEvent) => {\r\n      if (!iframeUrl || !event.data || typeof event.data !== \"object\") return;\r\n\r\n      try {\r\n        if (event.data.type === \"MONITOR_SCRIPT_LOADED\") {\r\n          setIsIframeLoading(false);\r\n          return;\r\n        }\r\n\r\n        if (\r\n          [\r\n            \"RUNTIME_ERROR\",\r\n            \"CONSOLE_OUTPUT\",\r\n            \"NETWORK_REQUEST\",\r\n            \"RESOURCE_ERROR\",\r\n            \"UNHANDLED_PROMISE_REJECTION\",\r\n          ].includes(event.data.type)\r\n        ) {\r\n          debug(\"Received error from iframe:\", event.data);\r\n\r\n          let newError: AppError = {\r\n            id: `iframe-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`,\r\n            timestamp: new Date(),\r\n            pageUrl: currentPage,\r\n            name: \"Error\",\r\n            message: \"Unknown error\",\r\n          };\r\n\r\n          if (event.data.type === \"RUNTIME_ERROR\") {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.error.message || \"Unknown runtime error\",\r\n              stack: event.data.error.stack,\r\n              name: \"RuntimeError\",\r\n              type: \"runtime\",\r\n            };\r\n          } else if (\r\n            event.data.type === \"CONSOLE_OUTPUT\" &&\r\n            (event.data.level === \"error\" || event.data.level === \"warning\")\r\n          ) {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.message || \"Console error\",\r\n              name: \"ConsoleError\",\r\n              type: \"console\",\r\n            };\r\n          } else if (event.data.type === \"NETWORK_REQUEST\" && event.data.request) {\r\n            if (event.data.request.status >= 400 || event.data.request.error) {\r\n              newError = {\r\n                ...newError,\r\n                message:\r\n                  event.data.request.error?.message ||\r\n                  `${event.data.request.method} ${event.data.request.url} ${event.data.request.status} (${event.data.request.statusText})`,\r\n                name: \"NetworkError\",\r\n                type: \"network\",\r\n                details: {\r\n                  url: event.data.request.url,\r\n                  status: event.data.request.status,\r\n                  method: event.data.request.method,\r\n                },\r\n              };\r\n            } else {\r\n              return;\r\n            }\r\n          } else if (event.data.type === \"RESOURCE_ERROR\") {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.error.message || \"Resource loading error\",\r\n              name: \"ResourceError\",\r\n              type: \"resource\",\r\n            };\r\n          } else if (event.data.type === \"UNHANDLED_PROMISE_REJECTION\") {\r\n            newError = {\r\n              ...newError,\r\n              message: event.data.error.message || \"Unhandled promise rejection\",\r\n              stack: event.data.error.stack,\r\n              name: \"PromiseRejection\",\r\n              type: \"promise\",\r\n            };\r\n          } else {\r\n            return;\r\n          }\r\n\r\n          setErrors((prev) => [...prev, newError]);\r\n        } else if (event.data.type === \"URL_CHANGED\") {\r\n          const newPath = event.data.path || \"/\";\r\n          if (newPath !== currentPage) {\r\n            setCurrentPage(newPath);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error processing iframe message:\", error);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"message\", handleIframeMessage);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"message\", handleIframeMessage);\r\n    };\r\n  }, [iframeUrl, currentPage, setErrors]);\r\n\r\n  useEffect(() => {\r\n    if (isIframeLoading) {\r\n      previewWarningTimer.current = window.setTimeout(() => {\r\n        setShowLongPreviewWarning(true);\r\n      }, 6000);\r\n    } else {\r\n      window.clearTimeout(previewWarningTimer?.current ?? undefined);\r\n      setShowLongPreviewWarning(false);\r\n    }\r\n    return () => {\r\n      window.clearTimeout(previewWarningTimer?.current ?? undefined);\r\n    };\r\n  }, [isIframeLoading]);\r\n\r\n  const openInNewTab = () => {\r\n    if (iframeUrl) {\r\n      window.open(iframeUrl, \"_blank\");\r\n    }\r\n  };\r\n\r\n  const handleRefreshPreview = useCallback(() => {\r\n    setErrors([]);\r\n\r\n    setIsIframeLoading(true);\r\n\r\n    const timer = setTimeout(() => {\r\n      setIframeUrl((currentUrl) => {\r\n        if (!currentUrl) return \"\";\r\n        const separator = currentUrl.includes(\"?\") ? \"&\" : \"?\";\r\n        return `${currentUrl}${separator}refresh=${Math.random().toString(36).substr(2, 9)}`;\r\n      });\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [setErrors, setIsIframeLoading, setIframeUrl]);\r\n\r\n  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {\r\n    if (!isAgentRunning && wasRunning) {\r\n      handleRefreshPreview();\r\n    }\r\n  });\r\n\r\n  return (\r\n    <div\r\n      className=\"relative flex h-[calc(100vh-55px)] w-full flex-col md:h-full\"\r\n      style={\r\n        isMobile\r\n          ? {\r\n              height: \"calc(100vh - 55px)\",\r\n              minHeight: \"calc(100vh - 55px)\",\r\n            }\r\n          : undefined\r\n      }\r\n    >\r\n      <Tabs defaultValue=\"tasks\" className=\"flex h-full flex-col\">\r\n        <TabsList\r\n          defaultValue=\"tasks\"\r\n          className={cn(\r\n            \"flex w-full items-center justify-center border-b border-primary/20 px-3 py-0 dark:border-primary/10\",\r\n            currentThreadId ? \"hidden\" : \"mt-2 pb-2\",\r\n          )}\r\n        >\r\n          <TabsTrigger value=\"tasks\" className=\"w-full\">\r\n            Tasks\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"version-history\" className=\"w-full\">\r\n            Version History\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent\r\n          value=\"tasks\"\r\n          className=\"mt-0 hidden h-full flex-col overflow-hidden bg-transparent data-[state=active]:flex\"\r\n        >\r\n          <div className={cn(\"flex-1 overflow-auto pb-4\")}>\r\n            <ThreadContainer setThreadToRename={setThreadToRename} />\r\n\r\n            <LazyModal open={!!threadToRename} onOpenChange={() => setThreadToRename(null)}>\r\n              <RenameModalContent\r\n                projectId={projectId}\r\n                threadToRename={threadToRename}\r\n                onClose={() => setThreadToRename(null)}\r\n              />\r\n            </LazyModal>\r\n          </div>\r\n\r\n          <div className=\"relative sticky bottom-0 z-20 border-t border-border/50 bg-background/95 backdrop-blur-sm\">\r\n            {currentThreadId ? (\r\n              <MessageInput />\r\n            ) : (\r\n              <div className=\"mx-2 mb-2 flex items-center\">\r\n                <Button size=\"lg\" className=\"h-12 w-full\" onClick={() => handleCreateThread()}>\r\n                  {isCreatingThread ? (\r\n                    <>\r\n                      <Loading className=\"size-4 animate-spin text-background\" />\r\n                      Creating...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Plus className=\"size-4\" />\r\n                      Create New Thread\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </TabsContent>\r\n        <TabsContent\r\n          value=\"version-history\"\r\n          className=\"hidden h-full overflow-auto data-[state=active]:flex\"\r\n        >\r\n          <ThreadHistory id={projectId} />\r\n        </TabsContent>\r\n      </Tabs>\r\n\r\n      <Modal open={showDrawer} onOpenChange={setShowDrawer}>\r\n        <ModalContent\r\n          className=\"max-h-[95%] min-h-[95%] w-full items-end overflow-y-clip lg:max-w-full\"\r\n          closeClassName=\"hidden\"\r\n        >\r\n          <Tabs\r\n            defaultValue=\"preview\"\r\n            className=\"h-full w-full rounded-none border-r-[1px] border-primary/10\"\r\n            value={activeTab}\r\n            onValueChange={(value) => {\r\n              setActiveTab(value as \"preview\" | \"code-editor\");\r\n              if (isMobile) {\r\n                setShowDrawer(true);\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center gap-2 border-b border-primary/10\">\r\n              <div className=\"flex w-full flex-col items-start justify-between divide-y divide-primary/20 dark:divide-primary/10\">\r\n                <div className=\"flex w-full items-center justify-between p-1\">\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <TabsList className=\"flex w-fit items-center justify-center gap-2 border-l-0 px-2\">\r\n                      <TabsTrigger\r\n                        value=\"preview\"\r\n                        className=\"w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0\"\r\n                      >\r\n                        Preview\r\n                      </TabsTrigger>\r\n                      <TabsTrigger value=\"code-editor\" className=\"w-fit\">\r\n                        Code Editor\r\n                      </TabsTrigger>\r\n                      {/* {!isFreeTier && (\r\n                        <TabsTrigger value=\"terminal\" className=\"w-fit\">\r\n                          Terminal\r\n                        </TabsTrigger>\r\n                      )} */}\r\n                    </TabsList>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className={cn(\r\n                    \"flex w-full items-center gap-1 p-1\",\r\n                    activeTab === \"code-editor\" || (activeTab === \"terminal\" && \"hidden\"),\r\n                  )}\r\n                >\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Hint label=\"Refresh Preview\" side=\"bottom\" delayDuration={125}>\r\n                      <Button\r\n                        size=\"icon\"\r\n                        className=\"size-7\"\r\n                        variant=\"ghost\"\r\n                        onClick={handleRefreshPreview}\r\n                        disabled={isAgentRunning}\r\n                      >\r\n                        {isIframeLoading ? (\r\n                          <Loading className=\"size-4 animate-spin\" />\r\n                        ) : (\r\n                          <RotateCcw\r\n                            className=\"size-4 text-primary/90 hover:text-primary\"\r\n                            strokeWidth={2}\r\n                          />\r\n                        )}\r\n                      </Button>\r\n                    </Hint>\r\n                  </div>\r\n                  {isProjectLoading ? (\r\n                    <Skeleton className=\"mx-0.5 my-0 h-7 w-full\" />\r\n                  ) : (\r\n                    filteredRoutes.length > 0 && (\r\n                      <Select value={currentPage} onValueChange={handleRouteChange}>\r\n                        <SelectTrigger className=\"mx-0.5 my-0 h-7 w-full rounded-full bg-accent py-0 hover:bg-accent/40\">\r\n                          <SelectValue placeholder=\"Select route\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent align=\"center\" className=\"w-64 px-2\">\r\n                          {filteredRoutes.map((route: string) => (\r\n                            <SelectItem key={route} value={route}>\r\n                              {route}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                    )\r\n                  )}\r\n\r\n                  <div className=\"flex items-center\">\r\n                    <Button variant=\"ghost\" size=\"icon\" className=\"size-7\" onClick={openInNewTab}>\r\n                      <ExternalLink className=\"size-4 text-primary/90 hover:text-primary\" />\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"relative flex-1 overflow-y-clip\">\r\n              <TabsContent\r\n                value=\"preview\"\r\n                className={cn(\"m-0 mx-auto flex w-full flex-1 flex-col\", \"h-[calc(100vh-110px)]\")}\r\n                forceMount\r\n                style={{ display: activeTab === \"preview\" ? \"flex\" : \"none\" }}\r\n              >\r\n                <CardContent className=\"h-[calc(100vh-120px)] p-0\">\r\n                  <div className=\"relative h-full w-full\">\r\n                    {sandboxState === \"unavailable\" ? (\r\n                      <div className=\"flex h-full items-center justify-center\">\r\n                        <SandboxUnavailable />\r\n                      </div>\r\n                    ) : sandboxState === \"sleeping\" ? (\r\n                      <div className=\"flex h-full items-center justify-center\">\r\n                        <SandboxSleepingCard />\r\n                      </div>\r\n                    ) : (\r\n                      <>\r\n                        {iframeUrl && (\r\n                          <iframe\r\n                            src={iframeUrl}\r\n                            className=\"h-full w-full\"\r\n                            title=\"Web Application Preview\"\r\n                            style={{\r\n                              overflow: \"auto\",\r\n                              scrollbarWidth: \"thin\",\r\n                              scrollbarColor: \"var(--muted) transparent\",\r\n                              colorScheme: \"normal\",\r\n                            }}\r\n                            sandbox=\"allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-clipboard-write allow-clipboard-read allow-storage-access-by-user-activation allow-presentation\"\r\n                            allow=\"clipboard-write; clipboard-read; geolocation *; microphone *; camera *; display-capture *; fullscreen *; web-share *\"\r\n                            allowFullScreen\r\n                            onLoad={handleIframeLoad}\r\n                            ref={iframeRef}\r\n                            referrerPolicy=\"no-referrer\"\r\n                            loading=\"lazy\"\r\n                          />\r\n                        )}\r\n                        {isIframeLoading && <LoadingPreview />}\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </CardContent>\r\n                {sandboxState === \"idle\" && (\r\n                  <div className=\"mx-auto flex w-full items-center justify-center\">\r\n                    <BugFinder errors={errors} setErrors={setErrors} projectId={projectId} />\r\n                  </div>\r\n                )}\r\n              </TabsContent>\r\n              <TabsContent\r\n                value=\"code-editor\"\r\n                className={cn(\r\n                  \"m-0 mx-auto flex w-full flex-1 flex-col overflow-hidden\",\r\n                  \"h-[calc(100vh-90px)]\",\r\n                  \"data-[state=active]:block data-[state=inactive]:hidden\",\r\n                )}\r\n                forceMount\r\n                style={{ display: activeTab === \"code-editor\" ? \"flex\" : \"none\" }}\r\n              >\r\n                <CardContent className=\"h-full overflow-hidden p-0\">\r\n                  <div className=\"h-full overflow-auto\">\r\n                    <CodeEditor projectId={projectId} envId={project?.env_id || \"\"} />\r\n                  </div>\r\n                </CardContent>\r\n              </TabsContent>\r\n              {/* {!isFreeTier && (\r\n                <TabsContent\r\n                  value=\"terminal\"\r\n                  className={cn(\r\n                    \"m-0 mx-auto flex w-full flex-1 flex-col overflow-hidden\",\r\n                    \"h-[calc(100vh-5.9rem)]\",\r\n                    \"data-[state=active]:block data-[state=inactive]:hidden\",\r\n                  )}\r\n                  forceMount\r\n                  style={{ display: activeTab === \"terminal\" ? \"flex\" : \"none\" }}\r\n                >\r\n                  <Terminal projectId={projectId} />\r\n                </TabsContent>\r\n              )} */}\r\n            </div>\r\n          </Tabs>\r\n        </ModalContent>\r\n      </Modal>\r\n\r\n      <SupabaseSheet isOpen={showSupabaseSheet} onClose={() => setShowSupabaseSheet(false)} />\r\n    </div>\r\n  );\r\n};\r\n\r\n// function MobilePreviewPanel({\r\n//   iframeUrl,\r\n//   iframeRef,\r\n//   isIframeLoading,\r\n//   handleIframeLoad,\r\n// }: {\r\n//   iframeUrl: string;\r\n//   iframeRef: React.RefObject<HTMLIFrameElement | null>;\r\n//   isIframeLoading: boolean;\r\n//   handleIframeLoad: () => void;\r\n// }) {\r\n//   const { isSandboxSleeping } = useProject();\r\n\r\n//   return (\r\n//     <div className=\"relative h-full w-full\">\r\n//       {isSandboxSleeping ? (\r\n//         <div className=\"flex h-full items-center justify-center\">\r\n//           <SandboxSleepingCard />\r\n//         </div>\r\n//       ) : (\r\n//         <>\r\n//           {iframeUrl && (\r\n//             <iframe\r\n//               src={iframeUrl}\r\n//               className=\"h-full w-full\"\r\n//               title=\"Web Application Preview\"\r\n//               style={{\r\n//                 overflow: \"auto\",\r\n//                 scrollbarWidth: \"thin\",\r\n//                 scrollbarColor: \"var(--muted) transparent\",\r\n//                 colorScheme: \"normal\",\r\n//               }}\r\n//               sandbox=\"allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-clipboard-write allow-clipboard-read allow-storage-access-by-user-activation allow-presentation\"\r\n//               allow=\"clipboard-write; clipboard-read; geolocation *; microphone *; camera *; display-capture *; fullscreen *; web-share *\"\r\n//               allowFullScreen\r\n//               onLoad={handleIframeLoad}\r\n//               ref={iframeRef}\r\n//               referrerPolicy=\"no-referrer\"\r\n//               loading=\"lazy\"\r\n//             />\r\n//           )}\r\n//           {isIframeLoading && <LoadingPreview />}\r\n//         </>\r\n//       )}\r\n//     </div>\r\n//   );\r\n// }\r\n\r\nexport default MobileView;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,mCAAqB,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE;KAA1B;AAgBN,MAAM,YAAY;;IAChB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEnD,MAAM,iBAAiB,CAAC;QACtB,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,MAAO;IAChD;IAEA,OAAO;QAAE;QAAQ;QAAW;IAAe;AAC7C;GARM;AAUN,MAAM,aAAa,CAAC,EAAE,SAAS,EAAS;;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,GAAG,0BAA0B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,sBAAsB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAU;IAE3C,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IAC7D,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD,EAC9D,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;uDAAE,CAAC,QAAU,CAAC;gBACrB,iBAAiB,MAAM,EAAE;gBACzB,gBAAgB,MAAM,cAAc;YACtC,CAAC;;IAEH,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAC3E,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;iDAAE,CAAC,QAAU,CAAC;gBACrB,WAAW,MAAM,SAAS;gBAC1B,cAAc,MAAM,YAAY;gBAChC,YAAY,MAAM,UAAU;gBAC5B,eAAe,MAAM,aAAa;YACpC,CAAC;;IAGH,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1F,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAE9B,MAAM,iBACJ,SAAS,sBAAsB,OAAO,CAAC,QAAkB,CAAC,MAAM,QAAQ,CAAC,aAAa,EAAE;IAE1F,MAAM,mBAAmB,eAAe,SAAS;IAEjD,MAAM,qBAAqB;QACzB,eAAe,MAAM;IACvB;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS,QAAQ;gBACnB,MAAM,UAAU,GAAG,SAAS,SAAS;gBACrC,MAAM,SAAS,GAAG,UAAU,aAAa;gBAEzC,mBAAmB,iBAAiB;gBAEpC,IAAI,QAAQ,SAAS,KAAK,GAAG;oBAC3B;gBACF;gBAEA,IAAI,WAAW,WAAW;oBACxB,mBAAmB;oBACnB,aAAa;gBACf;YACF;QACF;+BAAG;QAAC;QAAS;QAAa;QAAW;KAAa;IAElD,MAAM,oBAAoB,CAAC;QACzB,MAAM,eAAe,MAAM,QAAQ,CAAC,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QACjE,eAAe;QAEf,MAAM,MAAM,KAAK,GAAG;QACpB,UAAU,CAAC,OACT,KAAK,GAAG,CAAC,CAAC;gBACR,IACE,MAAM,SAAS,IACf,MAAM,IAAI,KAAK,MAAM,SAAS,EAAE,OAAO,KAAK,QAC5C,MAAM,OAAO,KAAK,QAClB;oBACA,OAAO;wBACL,GAAG,KAAK;wBACR,SAAS;oBACX;gBACF;gBACA,OAAO;YACT;IAEJ;IAEA,MAAM,mBAAmB;QACvB,oDAAoD;QACpD,IAAI,WAAW;YACb,6BAA6B;YAE7B,2CAA2C;YAC3C,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,aAAa,EAAE;gBACxD,IAAI;oBACF,iEAAiE;oBACjE,MAAM,iBAAiB,UAAU,OAAO,CAAC,aAAa,CAAC,QAAQ;oBAC/D,IAAI,CAAC,kBAAkB,CAAC,eAAe,IAAI,EAAE;wBAC3C,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACN;oBACF;oBAEA,MAAM,eAAe,eAAe,aAAa,CAC/C;oBAGF,IAAI,CAAC,cAAc;wBACjB,MAAM,SAAS,eAAe,aAAa,CAAC;wBAC5C,OAAO,GAAG,GAAG;wBACb,OAAO,KAAK,GAAG;wBACf,IAAI,eAAe,IAAI,EAAE;4BACvB,eAAe,IAAI,CAAC,WAAW,CAAC;4BAChC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACR;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,4CAA4C;oBAC5C,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,0DAA0D;gBAClE;YACF;QACF;IACF;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,cAAc,YAAY,UAAU;YACxE;QACF;+BAAG;QAAC;KAAU;IAEd,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;4DAAsB,CAAC;oBAC3B,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;oBAEjE,IAAI;wBACF,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,yBAAyB;4BAC/C,mBAAmB;4BACnB;wBACF;wBAEA,IACE;4BACE;4BACA;4BACA;4BACA;4BACA;yBACD,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,GAC1B;4BACA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,+BAA+B,MAAM,IAAI;4BAE/C,IAAI,WAAqB;gCACvB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;gCACpE,WAAW,IAAI;gCACf,SAAS;gCACT,MAAM;gCACN,SAAS;4BACX;4BAEA,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,iBAAiB;gCACvC,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;oCACrC,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK;oCAC7B,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO,IACL,MAAM,IAAI,CAAC,IAAI,KAAK,oBACpB,CAAC,MAAM,IAAI,CAAC,KAAK,KAAK,WAAW,MAAM,IAAI,CAAC,KAAK,KAAK,SAAS,GAC/D;gCACA,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,OAAO,IAAI;oCAC/B,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,qBAAqB,MAAM,IAAI,CAAC,OAAO,EAAE;gCACtE,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;oCAChE,WAAW;wCACT,GAAG,QAAQ;wCACX,SACE,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,WAC1B,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;wCAC1H,MAAM;wCACN,MAAM;wCACN,SAAS;4CACP,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG;4CAC3B,QAAQ,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM;4CACjC,QAAQ,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM;wCACnC;oCACF;gCACF,OAAO;oCACL;gCACF;4BACF,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,kBAAkB;gCAC/C,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;oCACrC,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,+BAA+B;gCAC5D,WAAW;oCACT,GAAG,QAAQ;oCACX,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI;oCACrC,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK;oCAC7B,MAAM;oCACN,MAAM;gCACR;4BACF,OAAO;gCACL;4BACF;4BAEA;4EAAU,CAAC,OAAS;2CAAI;wCAAM;qCAAS;;wBACzC,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,eAAe;4BAC5C,MAAM,UAAU,MAAM,IAAI,CAAC,IAAI,IAAI;4BACnC,IAAI,YAAY,aAAa;gCAC3B,eAAe;4BACjB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;oBACpD;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC;wCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;+BAAG;QAAC;QAAW;QAAa;KAAU;IAEtC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,iBAAiB;gBACnB,oBAAoB,OAAO,GAAG,OAAO,UAAU;4CAAC;wBAC9C,0BAA0B;oBAC5B;2CAAG;YACL,OAAO;gBACL,OAAO,YAAY,CAAC,qBAAqB,WAAW;gBACpD,0BAA0B;YAC5B;YACA;wCAAO;oBACL,OAAO,YAAY,CAAC,qBAAqB,WAAW;gBACtD;;QACF;+BAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe;QACnB,IAAI,WAAW;YACb,OAAO,IAAI,CAAC,WAAW;QACzB;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;wDAAE;YACvC,UAAU,EAAE;YAEZ,mBAAmB;YAEnB,MAAM,QAAQ;sEAAW;oBACvB;8EAAa,CAAC;4BACZ,IAAI,CAAC,YAAY,OAAO;4BACxB,MAAM,YAAY,WAAW,QAAQ,CAAC,OAAO,MAAM;4BACnD,OAAO,GAAG,aAAa,UAAU,QAAQ,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;wBACtF;;gBACF;qEAAG;YAEH;gEAAO,IAAM,aAAa;;QAC5B;uDAAG;QAAC;QAAW;QAAoB;KAAa;IAEhD,CAAA,GAAA,qIAAA,CAAA,6BAA0B,AAAD;iDAAE,CAAC,gBAAgB;YAC1C,IAAI,CAAC,kBAAkB,YAAY;gBACjC;YACF;QACF;;IAEA,qBACE,4SAAC;QACC,WAAU;QACV,OACE,WACI;YACE,QAAQ;YACR,WAAW;QACb,IACA;;0BAGN,4SAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAQ,WAAU;;kCACnC,4SAAC,mIAAA,CAAA,WAAQ;wBACP,cAAa;wBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,kBAAkB,WAAW;;0CAG/B,4SAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;0CAAS;;;;;;0CAG9C,4SAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAkB,WAAU;0CAAS;;;;;;;;;;;;kCAI1D,4SAAC,mIAAA,CAAA,cAAW;wBACV,OAAM;wBACN,WAAU;;0CAEV,4SAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;;kDACjB,4SAAC,oJAAA,CAAA,UAAe;wCAAC,mBAAmB;;;;;;kDAEpC,4SAAC,oIAAA,CAAA,YAAS;wCAAC,MAAM,CAAC,CAAC;wCAAgB,cAAc,IAAM,kBAAkB;kDACvE,cAAA,4SAAC;4CACC,WAAW;4CACX,gBAAgB;4CAChB,SAAS,IAAM,kBAAkB;;;;;;;;;;;;;;;;;0CAKvC,4SAAC;gCAAI,WAAU;0CACZ,gCACC,4SAAC,iJAAA,CAAA,UAAY;;;;yDAEb,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;wCAAc,SAAS,IAAM;kDACtD,iCACC;;8DACE,4SAAC,sIAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAwC;;yEAI7D;;8DACE,4SAAC,6SAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAW;;;;;;;;;;;;;;;;;;;;;;;;kCASzC,4SAAC,mIAAA,CAAA,cAAW;wBACV,OAAM;wBACN,WAAU;kCAEV,cAAA,4SAAC,kJAAA,CAAA,UAAa;4BAAC,IAAI;;;;;;;;;;;;;;;;;0BAIvB,4SAAC,oIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAY,cAAc;0BACrC,cAAA,4SAAC,oIAAA,CAAA,eAAY;oBACX,WAAU;oBACV,gBAAe;8BAEf,cAAA,4SAAC,mIAAA,CAAA,OAAI;wBACH,cAAa;wBACb,WAAU;wBACV,OAAO;wBACP,eAAe,CAAC;4BACd,aAAa;4BACb,IAAI,UAAU;gCACZ,cAAc;4BAChB;wBACF;;0CAEA,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC,mIAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,4SAAC,mIAAA,CAAA,cAAW;4DACV,OAAM;4DACN,WAAU;sEACX;;;;;;sEAGD,4SAAC,mIAAA,CAAA,cAAW;4DAAC,OAAM;4DAAc,WAAU;sEAAQ;;;;;;;;;;;;;;;;;;;;;;sDAYzD,4SAAC;4CACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sCACA,cAAc,iBAAkB,cAAc,cAAc;;8DAG9D,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC,mIAAA,CAAA,UAAI;wDAAC,OAAM;wDAAkB,MAAK;wDAAS,eAAe;kEACzD,cAAA,4SAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,WAAU;4DACV,SAAQ;4DACR,SAAS;4DACT,UAAU;sEAET,gCACC,4SAAC,sIAAA,CAAA,UAAO;gEAAC,WAAU;;;;;qFAEnB,4SAAC,uSAAA,CAAA,YAAS;gEACR,WAAU;gEACV,aAAa;;;;;;;;;;;;;;;;;;;;;gDAMtB,iCACC,4SAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;2DAEpB,eAAe,MAAM,GAAG,mBACtB,4SAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAa,eAAe;;sEACzC,4SAAC,qIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,4SAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,4SAAC,qIAAA,CAAA,gBAAa;4DAAC,OAAM;4DAAS,WAAU;sEACrC,eAAe,GAAG,CAAC,CAAC,sBACnB,4SAAC,qIAAA,CAAA,aAAU;oEAAa,OAAO;8EAC5B;mEADc;;;;;;;;;;;;;;;;8DAS3B,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAO,WAAU;wDAAS,SAAS;kEAC9D,cAAA,4SAAC,6TAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,mIAAA,CAAA,cAAW;wCACV,OAAM;wCACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;wCACzD,UAAU;wCACV,OAAO;4CAAE,SAAS,cAAc,YAAY,SAAS;wCAAO;;0DAE5D,4SAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,4SAAC;oDAAI,WAAU;8DACZ,iBAAiB,8BAChB,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC,sKAAA,CAAA,UAAkB;;;;;;;;;+DAEnB,iBAAiB,2BACnB,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC,0JAAA,CAAA,sBAAmB;;;;;;;;;6EAGtB;;4DACG,2BACC,4SAAC;gEACC,KAAK;gEACL,WAAU;gEACV,OAAM;gEACN,OAAO;oEACL,UAAU;oEACV,gBAAgB;oEAChB,gBAAgB;oEAChB,aAAa;gEACf;gEACA,SAAQ;gEACR,OAAM;gEACN,eAAe;gEACf,QAAQ;gEACR,KAAK;gEACL,gBAAe;gEACf,SAAQ;;;;;;4DAGX,iCAAmB,4SAAC,oJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;4CAK1C,iBAAiB,wBAChB,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC,wJAAA,CAAA,YAAS;oDAAC,QAAQ;oDAAQ,WAAW;oDAAW,WAAW;;;;;;;;;;;;;;;;;kDAIlE,4SAAC,mIAAA,CAAA,cAAW;wCACV,OAAM;wCACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA,wBACA;wCAEF,UAAU;wCACV,OAAO;4CAAE,SAAS,cAAc,gBAAgB,SAAS;wCAAO;kDAEhE,cAAA,4SAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC,uJAAA,CAAA,UAAU;oDAAC,WAAW;oDAAW,OAAO,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuB1E,4SAAC,4JAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAmB,SAAS,IAAM,qBAAqB;;;;;;;;;;;;AAGpF;IA1gBM;;QAGa,iIAAA,CAAA,cAAW;QASwB,2IAAA,CAAA,aAAU;QACnC,iIAAA,CAAA,YAAS;QACQ,qIAAA,CAAA,wBAAqB;QAMF,oIAAA,CAAA,kBAAe;QAWhD;QAwO9B,qIAAA,CAAA,6BAA0B;;;MAvQtB;uCA4jBS", "debugId": null}}, {"offset": {"line": 3710, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/mobile/header.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { useSettingsStore } from \"@/stores/settings-tab\";\r\nimport { BrandGithubSolid, CogOneSolid, DatabaseSolid, Users, X } from \"@mynaui/icons-react\";\r\nimport { lazy, Suspense, useCallback, useMemo, useState } from \"react\";\r\nimport AccountDropdown from \"../../global/account-dropdown\";\r\nimport Logo from \"../../global/logo\";\r\nimport ThemeToggle from \"../../global/theme-toggle\";\r\nimport PublishProject from \"../publish\";\r\n\r\nconst SupabaseSheet = lazy(() => import(\"../modal/supabase-sheet\"));\r\n\r\nconst MobileHeader = () => {\r\n  const { user } = useAuth();\r\n  const { project, isProjectLoading } = useProject();\r\n\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [showSupabaseSheet, setShowSupabaseSheet] = useState(false);\r\n\r\n  const setSettingsTab = useSettingsStore((state) => state.setSettingsTab);\r\n\r\n  const onOpenChange = useCallback((open: boolean) => {\r\n    setIsOpen(open);\r\n  }, []);\r\n\r\n  const isFreeTier = useMemo(() => {\r\n    return user?.userFromDb?.plan === \"free-tier\";\r\n  }, [user]);\r\n\r\n  return (\r\n    <nav className=\"relative mx-auto flex w-full items-center justify-between border-b border-primary/10 px-3 py-2\">\r\n      <div className=\"flex items-center gap-2 md:ml-2\">\r\n        <Logo />\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-between gap-2 md:gap-4\">\r\n        <div className=\"flex items-center justify-center gap-2\">\r\n          <Button\r\n            variant=\"outline-primary\"\r\n            size=\"sm\"\r\n            className={cn(\r\n              \"ml-auto flex size-8 items-center justify-center\",\r\n              \"rounded-lg border border-[#34B27B]/20 bg-[#006239]/90 text-primary/90 hover:bg-[#006239] hover:text-primary dark:border-[#34B27B]/15\",\r\n            )}\r\n            onClick={() => {\r\n              if (!isFreeTier) {\r\n                setShowSupabaseSheet(true);\r\n              } else {\r\n                setSettingsTab(\"supabase\");\r\n              }\r\n            }}\r\n          >\r\n            <svg\r\n              width=\"113\"\r\n              height=\"113\"\r\n              viewBox=\"0 0 113 113\"\r\n              className=\"h-8 w-8\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                d=\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z\"\r\n                fill=\"url(#paint0_linear)\"\r\n              ></path>\r\n              <path\r\n                d=\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z\"\r\n                fill=\"url(#paint1_linear)\"\r\n                fill-opacity=\"0.2\"\r\n              ></path>\r\n              <path\r\n                d=\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\"\r\n                fill=\"#3ECF8E\"\r\n              ></path>\r\n              <defs>\r\n                <linearGradient\r\n                  id=\"paint0_linear\"\r\n                  x1=\"53.9738\"\r\n                  y1=\"54.974\"\r\n                  x2=\"94.1635\"\r\n                  y2=\"71.8295\"\r\n                  gradientUnits=\"userSpaceOnUse\"\r\n                >\r\n                  <stop stop-color=\"#249361\"></stop>\r\n                  <stop offset=\"1\" stop-color=\"#3ECF8E\"></stop>\r\n                </linearGradient>\r\n                <linearGradient\r\n                  id=\"paint1_linear\"\r\n                  x1=\"36.1558\"\r\n                  y1=\"30.578\"\r\n                  x2=\"54.4844\"\r\n                  y2=\"65.0806\"\r\n                  gradientUnits=\"userSpaceOnUse\"\r\n                >\r\n                  <stop></stop>\r\n                  <stop offset=\"1\" stop-opacity=\"0\"></stop>\r\n                </linearGradient>\r\n              </defs>\r\n            </svg>\r\n          </Button>\r\n\r\n          <PublishProject />\r\n          <Button\r\n            variant=\"ghost\"\r\n            className=\"size-8 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden\"\r\n            onClick={() => onOpenChange(true)}\r\n            aria-label=\"Toggle menu\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth=\"1.5\"\r\n              stroke=\"currentColor\"\r\n              className=\"!size-6\"\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3.75 9h16.5m-16.5 6.75h16.5\" />\r\n            </svg>\r\n            <span className=\"sr-only\">Toggle Menu</span>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {isOpen && (\r\n        <div className=\"fixed inset-0 z-[100] flex h-full w-full flex-col bg-background md:hidden\">\r\n          <div className=\"flex items-center justify-between border-b p-3 py-2\">\r\n            <Logo />\r\n            <Button\r\n              variant=\"ghost\"\r\n              className=\"mx-0 h-8 w-8 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n              onClick={() => onOpenChange(false)}\r\n              aria-label=\"Toggle menu\"\r\n            >\r\n              <X className=\"h-5 w-5\" />\r\n              <span className=\"sr-only\">Toggle Menu</span>\r\n            </Button>\r\n          </div>\r\n          <div className=\"flex-1 overflow-auto\">\r\n            <div className=\"flex flex-col gap-1\">\r\n              <div className=\"m-1\">\r\n                {isProjectLoading ? (\r\n                  <Skeleton className=\"mt-2 h-9 w-full\" />\r\n                ) : (\r\n                  <Typography.P className=\"mt-2 w-full items-center justify-between gap-2 px-4 text-lg font-medium text-primary\">\r\n                    {project?.name}\r\n                  </Typography.P>\r\n                )}\r\n              </div>\r\n\r\n              <Separator />\r\n\r\n              <div className=\"mx-2 flex flex-col gap-3\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"w-full items-center justify-between gap-2 px-2 text-base text-primary/80 hover:text-primary\"\r\n                  onClick={() => {\r\n                    setSettingsTab(\"team\");\r\n                    setIsOpen(false);\r\n                  }}\r\n                >\r\n                  Team\r\n                  <Users className=\"mr-2 h-5 w-5\" />\r\n                </Button>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"w-full items-center justify-between gap-2 px-2 text-base text-primary/80 hover:bg-transparent hover:text-primary\"\r\n                  onClick={() => {\r\n                    setSettingsTab(\"environment\");\r\n                    setIsOpen(false);\r\n                  }}\r\n                >\r\n                  Environment Variables\r\n                  <DatabaseSolid className=\"mr-2 h-5 w-5\" />\r\n                </Button>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"w-full items-center justify-between gap-2 px-2 text-base text-primary/80 hover:text-primary\"\r\n                  onClick={() => {\r\n                    setSettingsTab(\"project\");\r\n                    setIsOpen(false);\r\n                  }}\r\n                >\r\n                  Project Settings\r\n                  <CogOneSolid className=\"mr-2 h-5 w-5\" />\r\n                </Button>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"w-full items-center justify-between gap-2 px-2 text-base text-primary/80 hover:text-primary\"\r\n                  onClick={() => {\r\n                    setSettingsTab(\"github\");\r\n                    setIsOpen(false);\r\n                  }}\r\n                >\r\n                  GitHub Repository\r\n                  <BrandGithubSolid className=\"mr-2 h-5 w-5\" />\r\n                </Button>\r\n              </div>\r\n\r\n              <Separator />\r\n\r\n              <div className=\"mx-2\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"w-full items-center justify-between gap-2 px-3 text-base text-primary/80 hover:bg-transparent hover:text-primary\"\r\n                >\r\n                  Theme\r\n                  <ThemeToggle className=\"bg-transparent px-0 hover:bg-transparent\" />\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"w-full border-t p-4\">\r\n            <AccountDropdown\r\n              user={user}\r\n              align=\"center\"\r\n              side=\"top\"\r\n              sideOffset={10}\r\n              openTokenModal={() => {\r\n                setSettingsTab(\"purchase\");\r\n                setIsOpen(false);\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {!isFreeTier && (\r\n        <Suspense fallback={null}>\r\n          <SupabaseSheet isOpen={showSupabaseSheet} onClose={() => setShowSupabaseSheet(false)} />\r\n        </Suspense>\r\n      )}\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default MobileHeader;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAEA,MAAM,8BAAgB,CAAA,GAAA,4QAAA,CAAA,OAAI,AAAD,EAAE;KAArB;AAEN,MAAM,eAAe;;IACnB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IAE/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,iBAAiB,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;yDAAE,CAAC,QAAU,MAAM,cAAc;;IAEvE,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAChC,UAAU;QACZ;iDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;4CAAE;YACzB,OAAO,MAAM,YAAY,SAAS;QACpC;2CAAG;QAAC;KAAK;IAET,qBACE,4SAAC;QAAI,WAAU;;0BACb,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC,qIAAA,CAAA,UAAI;;;;;;;;;;0BAGP,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mDACA;4BAEF,SAAS;gCACP,IAAI,CAAC,YAAY;oCACf,qBAAqB;gCACvB,OAAO;oCACL,eAAe;gCACjB;4BACF;sCAEA,cAAA,4SAAC;gCACC,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,WAAU;gCACV,MAAK;gCACL,OAAM;;kDAEN,4SAAC;wCACC,GAAE;wCACF,MAAK;;;;;;kDAEP,4SAAC;wCACC,GAAE;wCACF,MAAK;wCACL,gBAAa;;;;;;kDAEf,4SAAC;wCACC,GAAE;wCACF,MAAK;;;;;;kDAEP,4SAAC;;0DACC,4SAAC;gDACC,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,eAAc;;kEAEd,4SAAC;wDAAK,cAAW;;;;;;kEACjB,4SAAC;wDAAK,QAAO;wDAAI,cAAW;;;;;;;;;;;;0DAE9B,4SAAC;gDACC,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,eAAc;;kEAEd,4SAAC;;;;;kEACD,4SAAC;wDAAK,QAAO;wDAAI,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,4SAAC,kJAAA,CAAA,UAAc;;;;;sCACf,4SAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,aAAa;4BAC5B,cAAW;;8CAEX,4SAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAY;oCACZ,QAAO;oCACP,WAAU;8CAEV,cAAA,4SAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;8CAEvD,4SAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;YAK/B,wBACC,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,qIAAA,CAAA,UAAI;;;;;0CACL,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,aAAa;gCAC5B,cAAW;;kDAEX,4SAAC,uSAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;kDACb,4SAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;kCAG9B,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;8CACZ,iCACC,4SAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;6DAEpB,4SAAC,yIAAA,CAAA,UAAU,CAAC,CAAC;wCAAC,WAAU;kDACrB,SAAS;;;;;;;;;;;8CAKhB,4SAAC,wIAAA,CAAA,YAAS;;;;;8CAEV,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,eAAe;gDACf,UAAU;4CACZ;;gDACD;8DAEC,4SAAC,+SAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,eAAe;gDACf,UAAU;4CACZ;;gDACD;8DAEC,4SAAC,wUAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;sDAE3B,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,eAAe;gDACf,UAAU;4CACZ;;gDACD;8DAEC,4SAAC,oUAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;sDAEzB,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,eAAe;gDACf,UAAU;4CACZ;;gDACD;8DAEC,4SAAC,8UAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAIhC,4SAAC,wIAAA,CAAA,YAAS;;;;;8CAEV,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;4CACX;0DAEC,4SAAC,gJAAA,CAAA,UAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,oJAAA,CAAA,UAAe;4BACd,MAAM;4BACN,OAAM;4BACN,MAAK;4BACL,YAAY;4BACZ,gBAAgB;gCACd,eAAe;gCACf,UAAU;4BACZ;;;;;;;;;;;;;;;;;YAKP,CAAC,4BACA,4SAAC,4QAAA,CAAA,WAAQ;gBAAC,UAAU;0BAClB,cAAA,4SAAC;oBAAc,QAAQ;oBAAmB,SAAS,IAAM,qBAAqB;;;;;;;;;;;;;;;;;AAKxF;GA5NM;;QACa,wIAAA,CAAA,UAAO;QACc,2IAAA,CAAA,aAAU;QAKzB,mIAAA,CAAA,mBAAgB;;;MAPnC;uCA8NS", "debugId": null}}]}