import { BlurFade } from "@/components/ui/blur";
import { Button } from "@/components/ui/button";
import Typography from "@/components/ui/typography";
import { Message } from "@mynaui/icons-react";

interface ContinueMessageProps {
  text?: string;
  onContinue: () => void;
}

export const ContinueMessage = ({
  text = "Continue or type your next instruction",
  onContinue,
}: ContinueMessageProps) => {
  return (
    <div className="mb-4 flex w-full items-center justify-start py-3">
      <BlurFade className="flex w-full flex-col items-start">
        <div className="relative flex w-full max-w-md items-center justify-between gap-3 rounded-xl border border-chart-2/70 bg-chart-2/20 px-4 py-3">
          <div className="flex items-start gap-2">
            <Message className="size-5 text-primary/70" />
            <div className="flex flex-col">
              <Typography.H6 className="font-medium text-primary">Softgen is ready</Typography.H6>
              <Typography.P className="mt-0 text-sm">{text}</Typography.P>
            </div>
          </div>
          <Button onClick={onContinue} className="text-sm" size="sm">
            Continue
          </Button>
        </div>
      </BlurFade>
    </div>
  );
};
