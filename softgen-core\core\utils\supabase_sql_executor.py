import logging
import asyncio
from typing import Dict, Any
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.models import SupabaseConnection, SupabaseOrganization
from core.envs.env_ops import env_ops


class SupabaseSQLExecutor:
    """Utility class for executing SQL queries on Supabase with automatic project restoration"""
    
    @staticmethod
    async def execute_with_retry(
        session: AsyncSession,
        project_id: str,
        sql_query: str,
        supabase_service: Any = None
    ) -> Dict[str, Any]:
        """
        Execute SQL query on a Supabase project with automatic retry and project restoration.
        
        Args:
            session: Database session
            project_id: SoftGen project ID
            sql_query: SQL query to execute
            supabase_service: Optional SupabaseService instance (will be created if not provided)
            
        Returns:
            Dict with success status and result/error message
        """
        try:
            # Get the Supabase connection
            conn_stmt = select(SupabaseConnection).where(
                SupabaseConnection.project_id == project_id
            ).order_by(SupabaseConnection.last_updated.desc())
            
            conn_result = await session.execute(conn_stmt)
            connection = conn_result.scalars().first()
            
            if not connection:
                return {
                    "success": False,
                    "message": "No Supabase connection found for this project"
                }
            
            # Get the organization to access the tokens
            org_stmt = select(SupabaseOrganization).where(
                SupabaseOrganization.organization_id == connection.organization_id
            ).order_by(SupabaseOrganization.last_updated.desc())
            
            org_result = await session.execute(org_stmt)
            organization = org_result.scalars().first()
            
            if not organization:
                return {
                    "success": False,
                    "message": "No Supabase organization found for this connection"
                }
            
            # Execute the SQL query first
            query_result = await env_ops.execute_supabase_sql(
                access_token=organization.access_token,
                project_ref=connection.supabase_project_id,
                sql_query=sql_query,
                project_id=project_id  # Pass project_id for token refresh
            )
            
            # If query succeeded, return the result
            if query_result.get("success"):
                return query_result
            
            # Check if it's a connection error
            error_message = query_result.get("message") or query_result.get("error", "")
            is_connection_error = (
                "connection timeout" in error_message.lower() or 
                "connection terminated" in error_message.lower() or
                "connection refused" in error_message.lower() or
                "econnrefused" in error_message.lower()
            )
            
            if not is_connection_error:
                # Not a connection error, return the original error
                # Ensure we have a 'message' field for consistency
                if 'message' not in query_result and 'error' in query_result:
                    query_result['message'] = query_result['error']
                return query_result
            
            # Connection error detected, check project status
            logging.info("Connection timeout detected, checking project status...")
            
            # Import SupabaseService here to avoid circular imports
            if not supabase_service:
                from core.platform.supabase import SupabaseService
                from core.db import Database
                supabase_service = SupabaseService(Database())
            
            status_check = await supabase_service.check_project_status(
                access_token=organization.access_token,
                project_id=connection.supabase_project_id
            )
            
            if not status_check.get("success"):
                logging.error(f"Failed to check project status: {status_check.get('error', 'Unknown error')}")
                return query_result
            
            project_status = status_check.get("status")
            status_reason = status_check.get("status_reason")
            
            # Handle different project states
            if project_status == "paused":
                return await SupabaseSQLExecutor._handle_paused_project(
                    supabase_service=supabase_service,
                    organization=organization,
                    connection=connection,
                    sql_query=sql_query,
                    project_id=project_id,
                    status_reason=status_reason
                )
            elif project_status in ["suspended", "inactive", "past_due"]:
                return {
                    "success": False,
                    "message": f"Supabase project is {project_status}. {status_reason}. Please resolve the issue in the Supabase dashboard to continue.",
                    "project_status": project_status,
                    "status_reason": status_reason
                }
            else:
                # Project is active but query still failed, return original error
                logging.info(f"Project status is {project_status}, but query still failed. Returning original error.")
                return query_result
                
        except Exception as e:
            logging.error(f"Error executing SQL query: {str(e)}")
            logging.exception("Full exception details:")
            return {
                "success": False,
                "message": f"Error executing SQL query: {str(e)}"
            }
    
    @staticmethod
    async def _handle_paused_project(
        supabase_service: Any,
        organization: SupabaseOrganization,
        connection: SupabaseConnection,
        sql_query: str,
        project_id: str,
        status_reason: str
    ) -> Dict[str, Any]:
        """Handle paused project by attempting to restore and retry the query"""
        logging.info(f"Project {connection.supabase_project_id} is paused. Attempting to restore...")
        
        restore_result = await supabase_service.restore_project(
            access_token=organization.access_token,
            project_id=connection.supabase_project_id
        )
        
        if not restore_result.get("success"):
            return {
                "success": False,
                "message": f"Supabase project is paused and could not be restored: {restore_result.get('message')}. Please manually restore your project in the Supabase dashboard.",
                "project_status": "paused",
                "status_reason": status_reason
            }
        
        logging.info(f"Successfully restored project {connection.supabase_project_id}")
        # Wait for the project to fully restore
        logging.info("Waiting 30 seconds for project to fully restore...")
        await asyncio.sleep(30)
        
        # Retry the query multiple times with increasing delays
        max_retries = 3
        for attempt in range(max_retries):
            logging.info(f"Retrying SQL query after project restoration (attempt {attempt + 1}/{max_retries})...")
            
            query_result = await env_ops.execute_supabase_sql(
                access_token=organization.access_token,
                project_ref=connection.supabase_project_id,
                sql_query=sql_query,
                project_id=project_id
            )
            
            if query_result.get("success"):
                logging.info(f"SQL query succeeded on attempt {attempt + 1}")
                return query_result
            
            # Check if it's still a connection error
            retry_error = query_result.get("message") or query_result.get("error", "")
            is_still_connection_error = (
                "connection timeout" in retry_error.lower() or 
                "connection terminated" in retry_error.lower() or
                "connection refused" in retry_error.lower() or
                "econnrefused" in retry_error.lower()
            )
            
            if not is_still_connection_error:
                # Different error now, return it
                logging.info(f"Retry failed with non-connection error: {retry_error}")
                # Ensure we have a 'message' field for consistency
                if 'message' not in query_result and 'error' in query_result:
                    query_result['message'] = query_result['error']
                return query_result
            
            # Still a connection error, wait before next attempt
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 15  # 15s, 30s, 45s
                logging.info(f"Query still failed with connection error, waiting {wait_time} seconds before next attempt...")
                await asyncio.sleep(wait_time)
            else:
                logging.error("All retry attempts failed with connection errors")
                # Ensure we have a 'message' field for consistency
                if 'message' not in query_result and 'error' in query_result:
                    query_result['message'] = query_result['error']
                return query_result


# Create a singleton instance
supabase_sql_executor = SupabaseSQLExecutor() 