import json
import csv
import re
from typing import Dict, List, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_number(id_string: str) -> int:
    """Extract the number after the last dash in strings like 'preview-123', 'sandbox-123', 'workspace-123', etc."""
    match = re.search(r'[^-]+-(\d+)$', id_string)
    if not match:
        logger.warning(f"Could not extract number from ID: {id_string}")
        return None
    return int(match.group(1))

def load_workspace_data(filepath: str) -> Dict[int, List[str]]:
    """Load and process workspace IDs from JSON file"""
    with open(filepath, 'r') as f:
        workspace_data = json.load(f)

    number_to_workspace = {}
    
    # Track any problematic IDs
    problematic_ids = []

    # Process single workspaces
    for workspace in workspace_data.get('single_workspaces', []):
        workspace_id = workspace['id']  # Get the 'id' field from the workspace object
        number = extract_number(workspace_id)
        if number is not None:
            if number not in number_to_workspace:
                number_to_workspace[number] = []
            number_to_workspace[number].append(workspace_id)
        else:
            problematic_ids.append(workspace_id)

    # Log some sample mappings for verification
    sample_mappings = list(number_to_workspace.items())[:5]
    logger.info("Sample number to workspace mappings:")
    for number, workspaces in sample_mappings:
        logger.info(f"Number {number} -> Workspaces {workspaces}")

    if problematic_ids:
        logger.warning(f"Found {len(problematic_ids)} problematic workspace IDs: {problematic_ids[:5]}...")

    return number_to_workspace

def process_env_data(csv_filepath: str, number_to_workspace: Dict[int, List[str]]) -> Dict[str, Any]:
    """Process environment data and match with workspaces"""
    result = {
        "matched": {},
        "unmatched": [],
        "unmatched_workspaces": [],
        "stats": {
            "total_environments": 0,
            "environments_with_contents": 0,
            "matched_environments": 0,
            "total_workspace_matches": 0,
            "unmatched_workspace_count": 0
        }
    }

    # Keep track of which workspace IDs were matched
    matched_workspace_ids = set()

    with open(csv_filepath, 'r') as f:
        csv_reader = csv.DictReader(f)
        for row in csv_reader:
            result["stats"]["total_environments"] += 1
            
            env_id = row['env_id'].strip('"')
            env_number = extract_number(env_id)
            
            if row['env_contents'].strip():
                result["stats"]["environments_with_contents"] += 1

            entry = {
                "env_id": env_id,
                "project_id": row['project_id'],
                "github_repo": row['github_repo'],
                "has_env_contents": bool(row['env_contents'].strip())
            }
            
            if env_number is not None and env_number in number_to_workspace:
                matching_workspaces = number_to_workspace[env_number]
                result["matched"][env_id] = {
                    **entry,
                    "matching_workspaces": matching_workspaces
                }
                result["stats"]["matched_environments"] += 1
                result["stats"]["total_workspace_matches"] += len(matching_workspaces)
                
                # Add matched workspace IDs to our set
                matched_workspace_ids.update(matching_workspaces)
            else:
                result["unmatched"].append(entry)

    # Find unmatched workspaces
    for number, workspace_ids in number_to_workspace.items():
        for workspace_id in workspace_ids:
            if workspace_id not in matched_workspace_ids:
                result["unmatched_workspaces"].append(workspace_id)
                result["stats"]["unmatched_workspace_count"] += 1

    return result

def main():
    # Load workspace data
    workspace_filepath = 'env-migration-marko/workspace_ids.json'
    number_to_workspace = load_workspace_data(workspace_filepath)
    logger.info(f"Loaded mappings for {len(number_to_workspace)} unique workspace numbers")

    # Process environment data
    csv_filepath = 'env-migration-marko/combined-env-data.csv'
    result = process_env_data(csv_filepath, number_to_workspace)

    # Write results
    output_file = 'env-migration-marko/environment_matches.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2)

    # Updated statistics logging
    stats = result["stats"]
    logger.info(f"""
Matching Results:
----------------
Total Environments: {stats['total_environments']}
Environments with Contents: {stats['environments_with_contents']}
Matched Environments: {stats['matched_environments']}
Total Workspace Matches: {stats['total_workspace_matches']}
Unmatched Environments: {len(result['unmatched'])}
Unmatched Workspaces: {stats['unmatched_workspace_count']}
Average Workspaces per Matched Environment: {stats['total_workspace_matches']/stats['matched_environments'] if stats['matched_environments'] > 0 else 0:.2f}
    """)

    # Log some sample unmatched workspaces
    if result["unmatched_workspaces"]:
        logger.info("\nSample of unmatched workspace IDs (first 10):")
        for workspace_id in result["unmatched_workspaces"][:10]:
            logger.info(workspace_id)

if __name__ == "__main__":
    main() 