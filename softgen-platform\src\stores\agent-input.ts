import { useEffect } from "react";
import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

type AgentInputStore = {
  inputContent: string | null;
  mode: "creative" | "standard" | "plan";
  setInputContent: (content: string | null) => void;
  setMode: (mode: "creative" | "standard" | "plan") => void;
  reset: () => void;
};

export const useAgentInput = create<AgentInputStore>()(
  subscribeWithSelector((set) => ({
    inputContent: null,
    mode: "creative",
    setInputContent: (content) => set({ inputContent: content }),
    setMode: (mode) => set({ mode }),
    reset: () => set({ inputContent: null, mode: "creative" }),
  })),
);

export const useSubscribeToAgentInput = (
  callback: (content: string | null, prevContent: string | null) => void,
) => {
  useEffect(() => {
    const unsubscribe = useAgentInput.subscribe((state) => state.inputContent, callback);
    return () => unsubscribe();
  }, [callback]);
};
