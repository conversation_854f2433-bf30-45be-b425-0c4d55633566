"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Mo<PERSON>, ModalContent } from "@/components/ui/modal";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { useCurrentThreadStore } from "@/stores/current-thread";
import Link from "next/link";
import { useShallow } from "zustand/react/shallow";

const NoTokenModal = () => {
  const { user } = useAuth();

  const { noTokenModalOpen, setNoTokenModalOpen } = useCurrentThreadStore(
    useShallow((state) => ({
      noTokenModalOpen: state.noTokenModalOpen,
      setNoTokenModalOpen: state.setNoTokenModalOpen,
    })),
  );

  return (
    <Modal open={noTokenModalOpen} onOpenChange={setNoTokenModalOpen}>
      <ModalContent className="rounded-2xl text-center lg:max-w-md" tabIndex={-1}>
        <div className="relative flex flex-col gap-4 overflow-hidden p-4 pt-10">
          <div className="absolute -right-20 top-0 z-0 h-20 w-64 bg-purple-600/10 blur-[3rem]" />
          <div className="absolute right-10 top-0 z-0 h-20 w-80 bg-pink-600/10 blur-[3rem]" />
          <div className="flex flex-col items-center gap-6">
            <div className={cn("h-[120px] w-fit")}>
              <svg
                width="120"
                height="120"
                viewBox="0 0 120 120"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="text-primary"
              >
                <g clipPath="url(#clip0_1503_289)">
                  <path
                    d="M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z"
                    fill="currentColor"
                  />
                  <path
                    d="M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z"
                    fill="currentColor"
                  />
                  <path
                    d="M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z"
                    fill="currentColor"
                  />
                  <path
                    d="M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z"
                    fill="currentColor"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_1503_289">
                    <rect width="109" height="109" fill="currentColor" />
                  </clipPath>
                </defs>
              </svg>
            </div>

            {user.wholesalePlan ? (
              <div className="flex flex-col gap-8 pb-6">
                <div className="flex flex-col gap-2">
                  <p className="max-w-xs text-muted-foreground">
                    You&apos;ve run out of funds. Please top up your wallet to continue using
                    Softgen.
                  </p>
                </div>

                <div className="flex flex-col gap-2">
                  <Button asChild className="text-sm font-semibold">
                    <Link href="/wallet" target="_blank" prefetch={false}>
                      Top up
                    </Link>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col gap-8 pb-6">
                <div className="flex flex-col gap-2">
                  <h2 className="text-2xl font-bold">It&apos;s time to upgrade</h2>
                  <p className="max-w-xs text-sm text-muted-foreground">
                    You&apos;ve run out of tokens. Please upgrade your plan to continue using
                    Softgen.
                  </p>
                </div>

                <div className="flex flex-col gap-2">
                  <Button asChild className="text-sm font-semibold">
                    <Link href="/pricing" target="_blank" prefetch={false}>
                      Upgrade
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </ModalContent>
    </Modal>
  );
};

export default NoTokenModal;
