import aiohttp
from typing import Optional
import logging
import ssl
from core.config import settings

# Cloudflare API credentials
CLOUDFLARE_ACCOUNT_ID = settings.cloudflare_account_id
CLOUDFLARE_API_TOKEN = settings.cloudflare_api_token
CLOUDFLARE_IMAGES_API_URL = f'https://api.cloudflare.com/client/v4/accounts/{CLOUDFLARE_ACCOUNT_ID}/images/v1'

async def upload_to_cloudflare(image_data: bytes, filename: str) -> Optional[str]:
    """
    Upload an image to Cloudflare Images and return the URL.

    :param image_data: The image data as bytes
    :param filename: The filename to use for the uploaded image
    :return: The URL of the uploaded image, or None if the upload failed
    """
    logging.info(f"Uploading image to Cloudflare: {filename}")

    headers = {
        'Authorization': f'Bearer {CLOUDFLARE_API_TOKEN}',
    }

    data = aiohttp.FormData()
    data.add_field('file', image_data, filename=filename, content_type='image/png')

    try:
        # Create a custom SSL context that doesn't verify certificates
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
            async with session.post(CLOUDFLARE_IMAGES_API_URL, headers=headers, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('success'):
                        # Use the direct URL provided by Cloudflare instead of constructing it
                        image_url = result['result']['variants'][0]
                        logging.info(f"Image uploaded successfully: {image_url}")
                        return image_url
                    else:
                        logging.error(f"Cloudflare API error: {result.get('errors')}")
                else:
                    logging.error(f"Cloudflare API request failed with status {response.status}")
    except Exception as e:
        logging.exception(f"Error uploading image to Cloudflare: {str(e)}")

    return None
