from daytona import Daytona, DaytonaConfig
import os
import json
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import List

# Initialize the Daytona client
config = DaytonaConfig(
    api_key="dtn_f092546b75b789012215f4e6956de8016d52926eb31388c40aa4427972519b2a",
    server_url="https://daytona.work/api",
    target="us"
)
daytona = Daytona(config)

def execute_in_tmux(workspace, command, session_name, wait_for_completion=False):
    try:
        # Create new tmux session
        workspace.process.exec(f"tmux new-session -d -s {session_name}")
        
        # Create a done flag file
        if wait_for_completion:
            flag_file = f"/tmp/{session_name}.done"
            full_command = f"{command} && touch {flag_file} || touch {flag_file}.error"
        else:
            full_command = command
            
        # Send command
        workspace.process.exec(f"tmux send-keys -t {session_name} '{full_command}' Enter")
        
        if wait_for_completion:
            # Wait for the flag file to appear
            max_wait = 600  # Maximum wait time in seconds
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                # Check for error flag
                error_check = workspace.process.exec(f"test -f {flag_file}.error")
                if error_check.code == 0:
                    workspace.process.exec(f"rm -f {flag_file}.error")
                    raise Exception(f"Command failed in session {session_name}")
                
                # Check for success flag
                done_check = workspace.process.exec(f"test -f {flag_file}")
                if done_check.code == 0:
                    workspace.process.exec(f"rm -f {flag_file}")
                    break
                    
                time.sleep(2)
            else:
                raise Exception(f"Command timed out in session {session_name}")
        
        return workspace.process.exec(f"tmux capture-pane -t {session_name} -p")
        
    except Exception as e:
        print(f"Error in tmux execution: {str(e)}")
        return None

def install_eslint_packages(workspace):
    try:
        install_cmd = (
            "cd /app && "
            "npm install --save-dev "
            "@typescript-eslint/eslint-plugin@^8.7.0 "
            "@typescript-eslint/parser@^8.7.0 "
            "eslint@^8.57.1 "
            "eslint-config-next@14.2.13"
        )
        
        response = execute_in_tmux(workspace, install_cmd, "eslint-install", wait_for_completion=True)
        
        if response:
            print("Successfully installed ESLint packages")
            return True
        else:
            print("Failed to install ESLint packages")
            return False

    except Exception as e:
        print(f"Error installing ESLint packages: {str(e)}")
        return False

def process_workspace(workspace_id):
    try:
        print(f"\nProcessing workspace: {workspace_id}")
        workspace = daytona.get(workspace_id)
        print(f"Current workspace ID: {workspace.id}")
        
        # Install ESLint packages
        success = install_eslint_packages(workspace)
        if success:
            print(f"Successfully updated ESLint packages for workspace {workspace_id}")
            return True
        else:
            print(f"Failed to update ESLint packages for workspace {workspace_id}")
            return False
        
    except Exception as e:
        print(f"Error processing workspace {workspace_id}: {str(e)}")
        return False

async def process_workspace_batch(workspace_ids: List[str], timestamp: str, batch_num: int):
    successful_ids = []
    failed_ids = []
    
    with ThreadPoolExecutor(max_workers=len(workspace_ids)) as executor:
        futures = [
            executor.submit(process_workspace, workspace_id)
            for workspace_id in workspace_ids
        ]
        
        for workspace_id, future in zip(workspace_ids, futures):
            try:
                success = future.result()
                if success:
                    successful_ids.append(workspace_id)
                else:
                    failed_ids.append(workspace_id)
            except Exception as e:
                print(f"Error processing {workspace_id}: {str(e)}")
                failed_ids.append(workspace_id)
    
    # Save batch results
    if successful_ids:
        with open(f'result_successful_{timestamp}_batch{batch_num}.json', 'w') as f:
            json.dump(successful_ids, f, indent=4)
    if failed_ids:
        with open(f'result_failed_{timestamp}_batch{batch_num}.json', 'w') as f:
            json.dump(failed_ids, f, indent=4)
    
    return successful_ids, failed_ids

async def main():
    # Get the script's directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Load workspace IDs from current JSON file using absolute path
    workspace_ids_path = os.path.join(script_dir, 'workspace_ids_current.json')
    with open(workspace_ids_path, 'r') as f:
        workspace_data = json.load(f)
        all_workspace_ids = [workspace["id"] for workspace in workspace_data["single_workspaces"]]
    
    # Update result file paths to use script directory
    def get_result_path(filename):
        return os.path.join(script_dir, filename)
    
    batch_size = 1000
    batches = [
        all_workspace_ids[i:i + batch_size]
        for i in range(0, len(all_workspace_ids), batch_size)
    ]
    
    print(f"Processing {len(all_workspace_ids)} workspaces in {len(batches)} batches")
    
    all_successful = []
    all_failed = []
    
    for batch_num, batch in enumerate(batches, 1):
        print(f"\nProcessing batch {batch_num}/{len(batches)} ({len(batch)} workspaces)")
        
        successful, failed = await process_workspace_batch(batch, timestamp, batch_num)
        
        all_successful.extend(successful)
        all_failed.extend(failed)
        
        print(f"\nBatch {batch_num} complete:")
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
    
    # Save final results with updated paths
    if all_successful:
        with open(get_result_path(f'result_successful_{timestamp}_final.json'), 'w') as f:
            json.dump(all_successful, f, indent=4)
    if all_failed:
        with open(get_result_path(f'result_failed_{timestamp}_final.json'), 'w') as f:
            json.dump(all_failed, f, indent=4)
    
    print("\n=== Processing Complete ===")
    print(f"Total workspaces: {len(all_workspace_ids)}")
    print(f"Total successful: {len(all_successful)}")
    print(f"Total failed: {len(all_failed)}")
    
    print("\nFinal results saved to:")
    print(f"All successful IDs: result_successful_{timestamp}_final.json")
    if all_failed:
        print(f"All failed IDs: result_failed_{timestamp}_final.json")

if __name__ == "__main__":
    asyncio.run(main()) 