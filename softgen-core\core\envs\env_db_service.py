from typing import Op<PERSON>, <PERSON><PERSON>, List
from sqlalchemy import select, func
from core.db import Database
from core.models import Environment
import logging
from core.config import settings
from core.envs.env_ops import env_ops

class EnvironmentService:
    def __init__(self, db: Database):
        self.db = db

    async def get_environment_by_id(self, env_id: str) -> Optional[Environment]:
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Environment).filter(Environment.env_id == env_id)
            )
            return result.scalar_one_or_none()

    async def get_environment_by_project(self, project_id: str) -> Optional[Environment]:
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Environment).filter(Environment.project_id == project_id)
            )
            return result.scalar_one_or_none()

    async def create_environment(self, env_id: str, template: str = settings.image) -> Environment:
        async with self.db.get_async_session() as session:
            new_env = Environment(
                env_id=env_id,
                assigned=False,
                status='initialising',
                template=template
            )
            session.add(new_env)
            await session.commit()
            return new_env

    async def assign_to_project(self, env_id: str, project_id: str, github_repo: Optional[str] = None) -> Optional[Environment]:
        async with self.db.get_async_session() as session:
            env = await session.execute(
                select(Environment).filter(Environment.env_id == env_id)
            )
            env = env.scalar_one_or_none()

            if env:
                env.assigned = True
                env.project_id = project_id
                if github_repo:
                    env.github_repo = github_repo
                await session.commit()
                return env
            return None

    async def update_status(self, env_id: str, status: str) -> Optional[Environment]:
        async with self.db.get_async_session() as session:
            env = await session.execute(
                select(Environment).filter(Environment.env_id == env_id)
            )
            env = env.scalar_one_or_none()

            if env:
                env.status = status
                await session.commit()
                return env
            return None

    async def delete_environment(self, env_id: str) -> bool:
        """Delete environment from both Daytona and database.

        Args:
            env_id (str): The environment ID to delete
            daytona (Optional[Daytona]): Daytona instance for workspace removal

        Returns:
            bool: True if deletion was successful
        """
        try:
            # Try to remove from Daytona first if provided
            try:
                sandbox = await env_ops.daytona.get(env_id)
                if sandbox:
                    await env_ops.remove_sandbox(sandbox)
                    logging.info(f"Successfully removed workspace from Daytona: {env_id}")
            except Exception as e:
                logging.error(f"Error removing workspace from Daytona: {str(e)}")
                # Continue with database deletion even if Daytona fails

            # Then remove from database
            async with self.db.get_async_session() as session:
                env = await session.execute(
                    select(Environment).filter(Environment.env_id == env_id)
                )
                env = env.scalar_one_or_none()

                if env:
                    await session.delete(env)
                    await session.commit()
                    logging.info(f"Successfully deleted environment from database: {env_id}")
                    return True

                logging.warning(f"Environment not found in database: {env_id}")
                return False

        except Exception as e:
            logging.error(f"Error during environment deletion: {str(e)}")
            return False

    async def get_unassigned_environment(self) -> Optional[Environment]:
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Environment)
                .filter(~Environment.assigned)
                .filter(Environment.status == 'ready')
            )
            return result.scalar_one_or_none()

    async def get_all_environments(self):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(Environment))
            return result.scalars().all()

    async def update_github_info(self, env_id: str, github_repo: str, github_branch: str = 'main') -> Optional[Environment]:
        """Update GitHub repository information for an environment."""
        async with self.db.get_async_session() as session:
            env = await session.execute(
                select(Environment).filter(Environment.env_id == env_id)
            )
            env = env.scalar_one_or_none()

            if env:
                env.github_repo = github_repo
                env.github_branch = github_branch
                await session.commit()
                return env
            return None

    async def assign_or_create_environment(self, project_id: str) -> Tuple[Environment, bool]:
        """
        Find an unassigned environment or create a new one and assign it to a project.

        Args:
            project_id (str): The project ID to assign the environment to

        Returns:
            Tuple[Environment, bool]: (Environment object, True if new environment was created)
        """
        async with self.db.get_async_session() as session:
            try:
                # First try to find an unassigned environment
                unassigned_env = await session.execute(
                    select(Environment)
                    .filter(~Environment.assigned)
                    .filter(Environment.status == 'ready')
                    .order_by(Environment.env_id)
                    .limit(1)
                )
                unassigned_env = unassigned_env.scalar_one_or_none()

                if unassigned_env:
                    logging.info(f"Found unassigned environment {unassigned_env.env_id}")
                    # Assign the environment to the project
                    unassigned_env.assigned = True
                    unassigned_env.project_id = project_id
                    await session.commit()
                    return unassigned_env, False

                # If no unassigned environment is found, return None and True to indicate
                # that a new environment needs to be created
                return None, True

            except Exception as e:
                logging.error(f"Error in assign_or_create_environment: {str(e)}")
                await session.rollback()
                raise

    async def get_environment_count(self) -> int:
        """Get the total count of environments."""
        async with self.db.get_async_session() as session:
            result = await session.execute(select(func.count(Environment.id)))
            return result.scalar_one()

    async def get_assigned_environment(self, project_id: str) -> Optional[Environment]:
        """Get the environment assigned to a specific project."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Environment)
                .filter(Environment.project_id == project_id)
                .filter(Environment.assigned)
            )
            return result.scalar_one_or_none()

    async def unassign_environment(self, env_id: str) -> Optional[Environment]:
        """Unassign an environment from its project."""
        async with self.db.get_async_session() as session:
            env = await session.execute(
                select(Environment).filter(Environment.env_id == env_id)
            )
            env = env.scalar_one_or_none()

            if env:
                env.assigned = False
                env.project_id = None
                await session.commit()
                return env
            return None

    async def get_environments_by_status(self, status: str) -> List[Environment]:
        """Get all environments with a specific status."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Environment).filter(Environment.status == status)
            )
            return result.scalars().all()

    async def get_number_of_unassigned_environments(self) -> int:
        """Get the count of unassigned environments that are ready."""
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(func.count(Environment.id))
                .filter(~Environment.assigned)
                .filter(Environment.status == 'ready')
            )
            return result.scalar_one()
