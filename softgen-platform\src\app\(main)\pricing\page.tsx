"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Loading from "@/components/ui/loading";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { pricingPlans } from "@/constants/pricing";
import { Layout } from "@/features/global/layout";
import CTA from "@/features/marketing/cta";
import {
  createCustomerPortalSession,
  getKindeUser,
  PlanType,
  subscriptionCheckout,
} from "@/lib/api";
import { debug } from "@/lib/debug";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { getItemWithExpiry, setItemWithExpiry } from "@/utils/auth-utils";
import { CheckCircleSolid } from "@mynaui/icons-react";
import { Wand2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

const getPlanSlug = (planName: string, selectedPlanName?: string) => {
  if (selectedPlanName) {
    const scaleOptions = pricingPlans[3].scaleOptions;
    const selectedOption = scaleOptions?.find((option) => option.name === selectedPlanName);
    return selectedOption?.slug || selectedPlanName.toLowerCase().replace(/\s+/g, "_");
  }

  const plan = pricingPlans.find((p) => p.name === planName);
  return plan?.slug || planName.toLowerCase().replace(/\s+/g, "_");
};

const comparePlansByPrice = (currentPlan: string, selectedPlan: string) => {
  const scaleOptions = pricingPlans[3].scaleOptions;
  const currentPlanOption = scaleOptions?.find((option) => option.slug === currentPlan);
  const selectedPlanOption = scaleOptions?.find((option) => option.slug === selectedPlan);

  if (currentPlanOption && selectedPlanOption) {
    if (currentPlanOption.price < selectedPlanOption.price) {
      return "Upgrade Plan";
    } else if (currentPlanOption.price > selectedPlanOption.price) {
      return "Downgrade Plan";
    }
    return "Manage Subscription";
  }
  return "Upgrade Plan";
};

const PricingPage = () => {
  const [selectedPlan, setSelectedPlan] = useState(
    pricingPlans[3].scaleOptions?.[0] || { name: "Fly", price: 100 },
  );
  const [isManaging, setIsManaging] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [userPlan, setUserPlan] = useState<string | null>(null);
  const { user, logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    const fetchUserPlan = async () => {
      try {
        const kindeId = await getItemWithExpiry("kinde_id");
        if (kindeId) {
          const userData = await getKindeUser(kindeId, logout);
          setUserPlan(userData.plan?.toLowerCase());
        }
      } catch (error) {
        console.error("Error fetching user plan:", error);
      }
    };
    fetchUserPlan();
  }, []);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const discountCode = searchParams.get("discountcode");

    if (discountCode) {
      setItemWithExpiry("discount_code", discountCode, 86400000);
    }
  }, []);

  const handlePlanChange = (value: string) => {
    const newPlan =
      pricingPlans[3]?.scaleOptions?.find((opt) => opt.name === value) ||
      pricingPlans[3].scaleOptions?.[0];
    setSelectedPlan(newPlan || { name: "Fly", price: 100 });
  };

  const handleCustomerPortal = async () => {
    debug("handleCustomerPortal");
    setIsManaging(true);
    try {
      const kindeId = await getItemWithExpiry("kinde_id");
      if (!kindeId) {
        window.location.replace(
          `${API_BASE_URL}/register?post_login_redirect_uri=${encodeURIComponent(`${process.env.NEXT_PUBLIC_APP_URL}/pricing`)}`,
        );
        return;
      }
      const userData = await getKindeUser(kindeId, logout);
      if (userData.stripe_customer_id) {
        const portalSession = await createCustomerPortalSession(userData.stripe_customer_id);
        window.open(portalSession.url, "_blank");
      }
    } catch (error) {
      console.error("Error creating customer portal session:", error);
    } finally {
      setIsManaging(false);
    }
  };

  const handleSubscriptionCheckout = async (planName: string) => {
    debug("handleSubscriptionCheckout");
    setLoadingPlan(planName);
    try {
      const kindeId = await getItemWithExpiry("kinde_id");

      if (!kindeId) {
        window.location.replace(
          `${API_BASE_URL}/register?post_login_redirect_uri=${encodeURIComponent(`${process.env.NEXT_PUBLIC_APP_URL}/pricing`)}`,
        );
      } else {
        const isUpgrade = !!userPlan;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const toltReferral = (window as any).tolt_referral;
        const discountCode = getItemWithExpiry("discount_code");

        const checkout = await subscriptionCheckout(
          getPlanSlug(planName) as PlanType,
          kindeId,
          isUpgrade,
          toltReferral,
          discountCode,
        );
        window.location.href = checkout.url;
      }
    } catch (error) {
      console.error("Subscription checkout error:", error);
    } finally {
      setLoadingPlan(null);
    }
  };

  const getButtonText = (planName: string, selectedPlanName?: string) => {
    const plan = planName.toLowerCase();
    const selectedPlanLower = selectedPlanName
      ? getPlanSlug(planName, selectedPlanName)
      : undefined;

    if (plan === "free") {
      if (userPlan) {
        return null;
      }
      return "Get Started";
    }

    if ((plan === "entry" && userPlan === "entry") || (plan === "boost" && userPlan === "boost")) {
      return "Manage Subscription";
    }

    if (userPlan === plan && !pricingPlans[3].hasScaleOptions) {
      return "Manage Subscription";
    }

    if (userPlan) {
      const higherTierPlans = ["fly", "pro_enterprise", "elite_enterprise"];
      if (higherTierPlans.includes(userPlan) && (plan === "boost" || plan === "entry")) {
        return "Downgrade Plan";
      }

      if (plan === "fly" && selectedPlanLower) {
        return comparePlansByPrice(userPlan, selectedPlanLower);
      }

      const currentPlanIndex = pricingPlans.findIndex((p) => p.name.toLowerCase() === userPlan);
      const newPlanIndex = pricingPlans.findIndex((p) => p.name.toLowerCase() === plan);

      return currentPlanIndex < newPlanIndex ? "Upgrade Plan" : "Downgrade Plan";
    }

    return "Get Started";
  };

  return (
    <>
      <Layout rootClassName="dark:bg-black">
        <main className="mt-24 h-full w-full pt-12">
          <div className="mx-auto">
            <div className="space-y-4 px-4 text-center">
              <h2 className="text-balance text-3xl font-semibold md:text-4xl">Choose your plan</h2>
              <p className="mt-6 text-balance text-base text-muted-foreground">
                While competitors with multi-million dollar funding focus on prototypes, we&apos;re
                the only platform that delivers production-ready Full Stack Web Apps you can
                actually launch.
              </p>
            </div>

            <div className="my-16 grid grid-cols-1 gap-8 px-4 sm:grid-cols-2 lg:grid-cols-4">
              {pricingPlans.map((plan, index) => (
                <div
                  key={index}
                  className={cn(
                    "relative h-full rounded-xl border border-border ring-1 ring-border",
                    index === 2 && "bg-primary text-background",
                  )}
                >
                  <div className="flex h-full flex-col justify-between rounded-xl p-8">
                    {index === 2 && (
                      <div className="absolute right-4 top-4 cursor-default rounded-lg border bg-primary px-3 py-1 text-xs font-medium text-background">
                        Most Popular
                      </div>
                    )}

                    <div>
                      <h1 className="text-3xl font-bold">{plan.name}</h1>

                      {plan.price ? (
                        <div className="mt-4">
                          <p className="text-4xl font-bold">
                            ${plan.hasScaleOptions ? selectedPlan.price : plan.price}/mo
                          </p>
                        </div>
                      ) : (
                        <div className="mt-4">
                          <p className="text-4xl font-bold">$0/mo</p>
                        </div>
                      )}

                      {plan.hasScaleOptions && (
                        <Select
                          onValueChange={(value) => handlePlanChange(value)}
                          defaultValue={selectedPlan.name}
                        >
                          <SelectTrigger className="mt-4 w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {plan.scaleOptions?.map((option) => (
                              <SelectItem key={option.name} value={option.name}>
                                {option.name} - ${option.price}/mo
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}

                      <ul className="mt-6 space-y-3">
                        {plan.features.map((feature, featureIndex) => (
                          <li
                            className={cn(
                              "flex items-center gap-2 text-sm text-muted-foreground",
                              index === 2 && "text-background",
                            )}
                            key={featureIndex}
                          >
                            <CheckCircleSolid className="size-4" /> {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {getButtonText(plan.name) === null ? (
                      <Button
                        size="lg"
                        className="mt-8 w-full rounded-lg text-sm font-semibold"
                        onClick={() => {
                          if (user) {
                            router.push("/app");
                          } else {
                            window.location.replace(
                              `${API_BASE_URL}/register?post_login_redirect_uri=${encodeURIComponent(`${process.env.NEXT_PUBLIC_APP_URL}/app`)}`,
                            );
                          }
                        }}
                      >
                        Dashboard
                      </Button>
                    ) : getButtonText(plan.name) === "Manage Subscription" ? (
                      <Button
                        size="lg"
                        className={cn(
                          "mt-8 w-full rounded-lg text-sm font-semibold",
                          index === 2 && "bg-background text-primary hover:bg-background",
                        )}
                        onClick={() => {
                          const planToUse = plan.hasScaleOptions
                            ? getPlanSlug(plan.name, selectedPlan.name)
                            : plan.name.toLowerCase();

                          if (userPlan === planToUse) {
                            handleCustomerPortal();
                          } else {
                            handleSubscriptionCheckout(planToUse);
                          }
                        }}
                        disabled={loadingPlan === plan.name.toLowerCase() || isManaging}
                      >
                        {loadingPlan === plan.name.toLowerCase() || isManaging ? (
                          <Loading className="size-5 animate-spin text-background" />
                        ) : (
                          <>
                            {getButtonText(plan.name)}
                            {!userPlan && <Wand2 className="ml-2 h-4 w-4" />}
                          </>
                        )}
                      </Button>
                    ) : index === 2 ? (
                      <Button
                        size="lg"
                        className={cn(
                          "mt-8 w-full rounded-lg border border-background/30 text-sm font-semibold",
                          index === 2 && "bg-background text-primary hover:bg-background",
                        )}
                        onClick={() => {
                          const planToUse = plan.hasScaleOptions
                            ? getPlanSlug(plan.name, selectedPlan.name)
                            : plan.name.toLowerCase();

                          if (userPlan === planToUse) {
                            handleCustomerPortal();
                          } else {
                            handleSubscriptionCheckout(planToUse);
                          }
                        }}
                        disabled={loadingPlan === plan.name.toLowerCase() || isManaging}
                      >
                        {loadingPlan === plan.name.toLowerCase() || isManaging ? (
                          <Loading className="size-5 animate-spin text-primary" />
                        ) : (
                          <>
                            {plan.hasScaleOptions
                              ? getButtonText(plan.name, selectedPlan.name)
                              : getButtonText(plan.name)}
                            {!userPlan && <Wand2 className="ml-2 h-4 w-4" />}
                          </>
                        )}
                      </Button>
                    ) : (
                      <Button
                        size="lg"
                        variant={index === 2 ? "default" : "invert-outline-primary"}
                        className={cn(
                          "mt-8 w-full rounded-lg border border-primary/10 text-sm font-semibold",
                          index === 2 && "bg-background text-primary",
                        )}
                        onClick={() => {
                          if (plan.name === "Free") {
                            window.location.href = "/app";
                          } else {
                            const planToUse = plan.hasScaleOptions
                              ? getPlanSlug(plan.name, selectedPlan.name)
                              : plan.name.toLowerCase();

                            if (userPlan === planToUse) {
                              handleCustomerPortal();
                            } else {
                              handleSubscriptionCheckout(planToUse);
                            }
                          }
                        }}
                        disabled={loadingPlan === plan.name.toLowerCase() || isManaging}
                      >
                        {loadingPlan === plan.name.toLowerCase() || isManaging ? (
                          <Loading className="size-5 animate-spin text-background" />
                        ) : (
                          <>
                            {plan.hasScaleOptions
                              ? getButtonText(plan.name, selectedPlan.name)
                              : getButtonText(plan.name)}
                            {!userPlan && <Wand2 className="ml-2 h-4 w-4" />}
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="pt-24">
              <CTA />
            </div>
          </div>
        </main>
      </Layout>
    </>
  );
};

export default PricingPage;
