{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined);\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    };\r\n    mql.addEventListener(\"change\", onChange);\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    return () => mql.removeEventListener(\"change\", onChange);\r\n  }, []);\r\n\r\n  return !!isMobile;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,oTAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-threads.ts"], "sourcesContent": ["import { debug } from \"@/lib/debug\";\r\nimport { useThreadContext } from \"@/providers/thread-provider\";\r\nimport { useCallback } from \"react\";\r\n\r\nexport type TSendMessage = {\r\n  content: string;\r\n  options?: {\r\n    images?: File[];\r\n    selectedPaths?: string[];\r\n    model: \"creative\" | \"standard\" | \"plan\";\r\n  };\r\n};\r\n\r\nexport const useThread = () => {\r\n  const {\r\n    currentThread,\r\n    isLoading,\r\n    createThreadFn,\r\n    sendMessageFn: contextSendMessage,\r\n    selectThreadId,\r\n    stopSession,\r\n    selectFirstThread,\r\n    contextLimitReached,\r\n    setContextLimitReached,\r\n  } = useThreadContext();\r\n\r\n  const sendMessage = useCallback(\r\n    async (data: TSendMessage) => {\r\n      try {\r\n        let attempts = 0;\r\n        const maxAttempts = 3;\r\n        const backoffMs = 500;\r\n\r\n        const attemptSend = async (): Promise<void> => {\r\n          try {\r\n            attempts++;\r\n            await contextSendMessage.mutateAsync({\r\n              content: data.content,\r\n              images: data.options?.images,\r\n              selectedPaths: data.options?.selectedPaths,\r\n              model: data.options?.model || \"creative\",\r\n            });\r\n          } catch (error) {\r\n            console.error(`Attempt ${attempts} failed:`, error);\r\n\r\n            if (attempts < maxAttempts) {\r\n              const delay = backoffMs * Math.pow(2, attempts - 1);\r\n              debug(`Retrying in ${delay}ms...`);\r\n              await new Promise((resolve) => setTimeout(resolve, delay));\r\n              return attemptSend();\r\n            }\r\n\r\n            throw error;\r\n          }\r\n        };\r\n\r\n        await attemptSend();\r\n      } catch (error) {\r\n        console.warn(\"error found\", error);\r\n        console.error(\"Failed to send message after multiple attempts:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    [contextSendMessage],\r\n  );\r\n\r\n  return {\r\n    currentThread,\r\n    isLoading,\r\n    createThreadFn,\r\n    sendMessage,\r\n    selectThreadId,\r\n    stopSession,\r\n    selectFirstThread,\r\n    contextLimitReached,\r\n    setContextLimitReached,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAWO,MAAM,YAAY;IACvB,MAAM,EACJ,aAAa,EACb,SAAS,EACT,cAAc,EACd,eAAe,kBAAkB,EACjC,cAAc,EACd,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACnB,sBAAsB,EACvB,GAAG,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC5B,OAAO;QACL,IAAI;YACF,IAAI,WAAW;YACf,MAAM,cAAc;YACpB,MAAM,YAAY;YAElB,MAAM,cAAc;gBAClB,IAAI;oBACF;oBACA,MAAM,mBAAmB,WAAW,CAAC;wBACnC,SAAS,KAAK,OAAO;wBACrB,QAAQ,KAAK,OAAO,EAAE;wBACtB,eAAe,KAAK,OAAO,EAAE;wBAC7B,OAAO,KAAK,OAAO,EAAE,SAAS;oBAChC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,QAAQ,CAAC,EAAE;oBAE7C,IAAI,WAAW,aAAa;wBAC1B,MAAM,QAAQ,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;wBACjD,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,YAAY,EAAE,MAAM,KAAK,CAAC;wBACjC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;wBACnD,OAAO;oBACT;oBAEA,MAAM;gBACR;YACF;YAEA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,eAAe;YAC5B,QAAQ,KAAK,CAAC,mDAAmD;YACjE,MAAM;QACR;IACF,GACA;QAAC;KAAmB;IAGtB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-copy.ts"], "sourcesContent": ["import { useState } from \"react\";\r\n\r\nexport function useCopy(duration = 1500) {\r\n  const [copied, setCopied] = useState<boolean>(false);\r\n\r\n  const copy = async (text: string) => {\r\n    try {\r\n      await navigator.clipboard.writeText(text);\r\n      setCopied(true);\r\n      setTimeout(() => setCopied(false), duration);\r\n      return true;\r\n    } catch (err) {\r\n      console.error(\"Failed to copy text: \", err);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  return {\r\n    copied,\r\n    copy,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,QAAQ,WAAW,IAAI;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAW;IAE9C,MAAM,OAAO,OAAO;QAClB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;YACnC,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;IAEA,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-token-info.ts"], "sourcesContent": ["import { getTokenUsage } from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { format } from \"date-fns\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\n\r\ninterface TokenUsage {\r\n  total_usage: number;\r\n  period_start?: number;\r\n  period_end?: number;\r\n}\r\n\r\nexport const useTokenInfo = () => {\r\n  const [tokenUsage, setTokenUsage] = useState<TokenUsage | null>(null);\r\n  const [isExcessUsageModalOpen, setIsExcessUsageModalOpen] = useState(false);\r\n  const [excessUsageMessage, setExcessUsageMessage] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const { user } = useAuth();\r\n\r\n  const fetchTokenUsage = useCallback(async () => {\r\n    if (!user?.userFromDb?.stripe_customer_id || !user?.userFromDb?.token_event_name) {\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const usage = await getTokenUsage(\r\n        user.userFromDb.stripe_customer_id,\r\n        user.userFromDb.token_event_name,\r\n      );\r\n      setTokenUsage(usage);\r\n    } catch (error) {\r\n      console.error(\"Error fetching token usage:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [user?.userFromDb?.stripe_customer_id, user?.userFromDb?.token_event_name]);\r\n\r\n  useEffect(() => {\r\n    fetchTokenUsage();\r\n    const intervalId = setInterval(fetchTokenUsage, 30000);\r\n    return () => clearInterval(intervalId);\r\n  }, [fetchTokenUsage]);\r\n\r\n  const formatTokens = (tokens: number) => {\r\n    if (tokens >= 1000000) {\r\n      return `${(tokens / 1000000).toFixed(2)}M`;\r\n    } else {\r\n      return `${Math.round(tokens / 1000)}k`;\r\n    }\r\n  };\r\n\r\n  const getPlanTokens = () => {\r\n    const plan = user?.userFromDb?.plan?.toLowerCase() || \"launch\";\r\n    switch (plan) {\r\n      case \"launch\":\r\n        return 3;\r\n      case \"elite\":\r\n        return 7;\r\n      case \"business\":\r\n        return 16;\r\n      case \"entry\":\r\n        return 2.5;\r\n      case \"boost\":\r\n        return 6;\r\n      case \"fly\":\r\n        return 14;\r\n      case \"pro_enterprise\":\r\n        return 30;\r\n      case \"elite_enterprise\":\r\n        return 85;\r\n      case \"free-tier\":\r\n        return 0.25;\r\n      default:\r\n        return 3;\r\n    }\r\n  };\r\n\r\n  const getRemainingTokens = () => {\r\n    return user?.userFromDb?.free_total_token || 0;\r\n  };\r\n\r\n  const getTokenDisplayInfo = () => {\r\n    const remaining = getRemainingTokens();\r\n    const total = getPlanTokens() * 1000000; // Convert to tokens\r\n    const isExcess = tokenUsage?.total_usage && tokenUsage.total_usage > 0;\r\n    const usedTokens = tokenUsage?.total_usage || 0;\r\n    const effectiveTotal = Math.max(remaining, total);\r\n\r\n    return {\r\n      usedText: usedTokens > 0 ? formatTokens(usedTokens) : \"\",\r\n      remainingTokens: formatTokens(remaining),\r\n      effectiveTotalTokens: formatTokens(effectiveTotal),\r\n      remainingText: `${formatTokens(remaining)}/${formatTokens(effectiveTotal)}`,\r\n      isWarning: isExcess,\r\n      tooltipContent: isExcess\r\n        ? `You've used ${formatTokens(usedTokens)} tokens this billing period. Excess usage charges may apply.`\r\n        : `You have ${formatTokens(remaining)} tokens remaining out of ${formatTokens(effectiveTotal)} tokens.`,\r\n      totalUsage: usedTokens,\r\n      remaining,\r\n      total: total / 1000000, // Keep in millions for calculations\r\n    };\r\n  };\r\n\r\n  const handleExcessUsageClick = () => {\r\n    const info = getTokenDisplayInfo();\r\n    const periodStart = tokenUsage?.period_start\r\n      ? format(new Date(tokenUsage.period_start * 1000), \"MMMM dd, yyyy\")\r\n      : \"N/A\";\r\n    const periodEnd = tokenUsage?.period_end\r\n      ? format(new Date(tokenUsage.period_end * 1000), \"MMMM dd, yyyy\")\r\n      : \"N/A\";\r\n    const userPlan = user?.userFromDb?.plan || \"launch\";\r\n    const isNewPlan = [\"entry\", \"boost\", \"fly\"].includes(userPlan.toLowerCase());\r\n\r\n    if (isNewPlan) {\r\n      const excessTokensIn100k = Math.ceil(info.totalUsage / 100000);\r\n      const message = `\r\n        You've exceeded your monthly token limit by ${formatTokens(info.totalUsage)} tokens.\r\n\r\n        Your next billing amount will be $${excessTokensIn100k} for the period ${periodStart} to ${periodEnd}.\r\n\r\n        Billing breakdown:\r\n        • Base plan includes ${formatTokens(info.total * 1000000)} tokens\r\n        • Additional usage is charged at $1 per 100k tokens\r\n        • You are using ${excessTokensIn100k} additional block(s) of 100k tokens\r\n      `;\r\n      setExcessUsageMessage(message);\r\n    } else {\r\n      const usageAmount =\r\n        {\r\n          launch: 25,\r\n          elite: 50,\r\n          business: 100,\r\n          entry: 25,\r\n          boost: 50,\r\n          fly: 100,\r\n          pro_enterprise: 200,\r\n          elite_enterprise: 500,\r\n          free_tier: 0.25,\r\n        }[userPlan.toLowerCase()] || 25;\r\n\r\n      const additionalBlocksUsed = Math.ceil(info.totalUsage / (info.total * 1000000));\r\n      const nextBillingAmount = usageAmount * additionalBlocksUsed;\r\n\r\n      const message = `\r\n        You've exceeded your monthly token limit by ${formatTokens(info.totalUsage)} tokens.\r\n\r\n        Your next billing amount will be $${nextBillingAmount} for the period ${periodStart} to ${periodEnd}.\r\n\r\n        Billing breakdown:\r\n        • Base plan includes ${formatTokens(info.total * 1000000)} tokens\r\n        • Additional usage is charged at $${usageAmount} per ${formatTokens(info.total * 1000000)} tokens\r\n        • You are using ${additionalBlocksUsed} additional block(s) of ${formatTokens(info.total * 1000000)} tokens\r\n      `;\r\n      setExcessUsageMessage(message);\r\n    }\r\n\r\n    setIsExcessUsageModalOpen(true);\r\n  };\r\n\r\n  return {\r\n    tokenUsage,\r\n    isLoading,\r\n    getTokenDisplayInfo,\r\n    formatTokens,\r\n    handleExcessUsageClick,\r\n    isExcessUsageModalOpen,\r\n    setIsExcessUsageModalOpen,\r\n    excessUsageMessage,\r\n    refreshTokenUsage: fetchTokenUsage,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAQO,MAAM,eAAe;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,kBAAkB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,MAAM,YAAY,sBAAsB,CAAC,MAAM,YAAY,kBAAkB;YAChF,aAAa;YACb;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,QAAQ,MAAM,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAC9B,KAAK,UAAU,CAAC,kBAAkB,EAClC,KAAK,UAAU,CAAC,gBAAgB;YAElC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC,MAAM,YAAY;QAAoB,MAAM,YAAY;KAAiB;IAE7E,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,MAAM,aAAa,YAAY,iBAAiB;QAChD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,SAAS;YACrB,OAAO,GAAG,CAAC,SAAS,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO;YACL,OAAO,GAAG,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC;QACxC;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,OAAO,MAAM,YAAY,MAAM,iBAAiB;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,MAAM,YAAY,oBAAoB;IAC/C;IAEA,MAAM,sBAAsB;QAC1B,MAAM,YAAY;QAClB,MAAM,QAAQ,kBAAkB,SAAS,oBAAoB;QAC7D,MAAM,WAAW,YAAY,eAAe,WAAW,WAAW,GAAG;QACrE,MAAM,aAAa,YAAY,eAAe;QAC9C,MAAM,iBAAiB,KAAK,GAAG,CAAC,WAAW;QAE3C,OAAO;YACL,UAAU,aAAa,IAAI,aAAa,cAAc;YACtD,iBAAiB,aAAa;YAC9B,sBAAsB,aAAa;YACnC,eAAe,GAAG,aAAa,WAAW,CAAC,EAAE,aAAa,iBAAiB;YAC3E,WAAW;YACX,gBAAgB,WACZ,CAAC,YAAY,EAAE,aAAa,YAAY,4DAA4D,CAAC,GACrG,CAAC,SAAS,EAAE,aAAa,WAAW,yBAAyB,EAAE,aAAa,gBAAgB,QAAQ,CAAC;YACzG,YAAY;YACZ;YACA,OAAO,QAAQ;QACjB;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,OAAO;QACb,MAAM,cAAc,YAAY,eAC5B,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,YAAY,GAAG,OAAO,mBACjD;QACJ,MAAM,YAAY,YAAY,aAC1B,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,UAAU,GAAG,OAAO,mBAC/C;QACJ,MAAM,WAAW,MAAM,YAAY,QAAQ;QAC3C,MAAM,YAAY;YAAC;YAAS;YAAS;SAAM,CAAC,QAAQ,CAAC,SAAS,WAAW;QAEzE,IAAI,WAAW;YACb,MAAM,qBAAqB,KAAK,IAAI,CAAC,KAAK,UAAU,GAAG;YACvD,MAAM,UAAU,CAAC;oDAC6B,EAAE,aAAa,KAAK,UAAU,EAAE;;0CAE1C,EAAE,mBAAmB,gBAAgB,EAAE,YAAY,IAAI,EAAE,UAAU;;;6BAGhF,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;;wBAE1C,EAAE,mBAAmB;MACvC,CAAC;YACD,sBAAsB;QACxB,OAAO;YACL,MAAM,cACJ;gBACE,QAAQ;gBACR,OAAO;gBACP,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,KAAK;gBACL,gBAAgB;gBAChB,kBAAkB;gBAClB,WAAW;YACb,CAAC,CAAC,SAAS,WAAW,GAAG,IAAI;YAE/B,MAAM,uBAAuB,KAAK,IAAI,CAAC,KAAK,UAAU,GAAG,CAAC,KAAK,KAAK,GAAG,OAAO;YAC9E,MAAM,oBAAoB,cAAc;YAExC,MAAM,UAAU,CAAC;oDAC6B,EAAE,aAAa,KAAK,UAAU,EAAE;;0CAE1C,EAAE,kBAAkB,gBAAgB,EAAE,YAAY,IAAI,EAAE,UAAU;;;6BAG/E,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;0CACxB,EAAE,YAAY,KAAK,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;wBAC1E,EAAE,qBAAqB,wBAAwB,EAAE,aAAa,KAAK,KAAK,GAAG,SAAS;MACtG,CAAC;YACD,sBAAsB;QACxB;QAEA,0BAA0B;IAC5B;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,mBAAmB;IACrB;AACF", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/settings.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { createJSONStorage, persist } from \"zustand/middleware\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\n\r\ninterface ResizablePanelConfig {\r\n  width: number;\r\n  previewWidth: number;\r\n  tasks: boolean;\r\n}\r\n\r\ntype SettingsStore = {\r\n  resizablePanelConfig: ResizablePanelConfig;\r\n  setResizablePanelConfig: (config: Partial<ResizablePanelConfig>) => void;\r\n  isAdvancedMode: boolean;\r\n  toggleAdvancedMode: () => void;\r\n};\r\n\r\nexport const useSettings = create<SettingsStore>()(\r\n  persist(\r\n    (set, get) => ({\r\n      resizablePanelConfig: {\r\n        width: 28,\r\n        previewWidth: 72,\r\n        tasks: true,\r\n      },\r\n      setResizablePanelConfig: (config) =>\r\n        set({\r\n          resizablePanelConfig: {\r\n            ...get().resizablePanelConfig,\r\n            ...config,\r\n          },\r\n        }),\r\n      isAdvancedMode: false,\r\n      toggleAdvancedMode: () => set((state) => ({ isAdvancedMode: !state.isAdvancedMode })),\r\n    }),\r\n    {\r\n      name: \"settings-storage\",\r\n      storage: createJSONStorage(() => localStorage),\r\n    },\r\n  ),\r\n);\r\n\r\nexport const useResizablePanelConfig = () => {\r\n  return useSettings(\r\n    useShallow((state) => ({\r\n      resizablePanelConfig: state.resizablePanelConfig,\r\n      setResizablePanelConfig: state.setResizablePanelConfig,\r\n    })),\r\n  );\r\n};\r\n\r\nexport const useAdvancedMode = () => {\r\n  return useSettings(\r\n    useShallow((state) => ({\r\n      isAdvancedMode: state.isAdvancedMode,\r\n      toggleAdvancedMode: state.toggleAdvancedMode,\r\n    })),\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAeO,MAAM,cAAc,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,yPAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,sBAAsB;YACpB,OAAO;YACP,cAAc;YACd,OAAO;QACT;QACA,yBAAyB,CAAC,SACxB,IAAI;gBACF,sBAAsB;oBACpB,GAAG,MAAM,oBAAoB;oBAC7B,GAAG,MAAM;gBACX;YACF;QACF,gBAAgB;QAChB,oBAAoB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,gBAAgB,CAAC,MAAM,cAAc;gBAAC,CAAC;IACrF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,yPAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;AACnC;AAIG,MAAM,0BAA0B;IACrC,OAAO,YACL,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,sBAAsB,MAAM,oBAAoB;YAChD,yBAAyB,MAAM,uBAAuB;QACxD,CAAC;AAEL;AAEO,MAAM,kBAAkB;IAC7B,OAAO,YACL,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,gBAAgB,MAAM,cAAc;YACpC,oBAAoB,MAAM,kBAAkB;QAC9C,CAAC;AAEL", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/settings-tab.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { subscribeWithSelector } from \"zustand/middleware\";\r\n\r\nexport type SettingsTab =\r\n  | \"supabase\"\r\n  | \"project\"\r\n  | \"team\"\r\n  | \"github\"\r\n  | \"clone\"\r\n  | \"billing\"\r\n  | \"purchase\"\r\n  | \"environment\"\r\n  | \"publish\";\r\n\r\ninterface SettingsState {\r\n  settingsTab: SettingsTab | null;\r\n}\r\n\r\ninterface Actions {\r\n  setSettingsTab: (tab: SettingsTab | null) => void;\r\n  closeModal: () => void;\r\n  reset: () => void;\r\n}\r\n\r\nconst initialState: SettingsState = {\r\n  settingsTab: null,\r\n};\r\n\r\nexport const useSettingsStore = create<SettingsState & Actions>()(\r\n  subscribeWithSelector((set) => ({\r\n    ...initialState,\r\n    setSettingsTab: (tab) => set({ settingsTab: tab }),\r\n    closeModal: () => set({ settingsTab: null }),\r\n    reset: () => set(initialState),\r\n  })),\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAuBA,MAAM,eAA8B;IAClC,aAAa;AACf;AAEO,MAAM,mBAAmB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,IACnC,CAAA,GAAA,yPAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,MAAQ,CAAC;QAC9B,GAAG,YAAY;QACf,gBAAgB,CAAC,MAAQ,IAAI;gBAAE,aAAa;YAAI;QAChD,YAAY,IAAM,IAAI;gBAAE,aAAa;YAAK;QAC1C,OAAO,IAAM,IAAI;IACnB,CAAC", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/agent-input.ts"], "sourcesContent": ["import { useEffect } from \"react\";\r\nimport { create } from \"zustand\";\r\nimport { subscribeWithSelector } from \"zustand/middleware\";\r\n\r\ntype AgentInputStore = {\r\n  inputContent: string | null;\r\n  mode: \"creative\" | \"standard\" | \"plan\";\r\n  setInputContent: (content: string | null) => void;\r\n  setMode: (mode: \"creative\" | \"standard\" | \"plan\") => void;\r\n  reset: () => void;\r\n};\r\n\r\nexport const useAgentInput = create<AgentInputStore>()(\r\n  subscribeWithSelector((set) => ({\r\n    inputContent: null,\r\n    mode: \"creative\",\r\n    setInputContent: (content) => set({ inputContent: content }),\r\n    setMode: (mode) => set({ mode }),\r\n    reset: () => set({ inputContent: null, mode: \"creative\" }),\r\n  })),\r\n);\r\n\r\nexport const useSubscribeToAgentInput = (\r\n  callback: (content: string | null, prevContent: string | null) => void,\r\n) => {\r\n  useEffect(() => {\r\n    const unsubscribe = useAgentInput.subscribe((state) => state.inputContent, callback);\r\n    return () => unsubscribe();\r\n  }, [callback]);\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAUO,MAAM,gBAAgB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,yPAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,MAAQ,CAAC;QAC9B,cAAc;QACd,MAAM;QACN,iBAAiB,CAAC,UAAY,IAAI;gBAAE,cAAc;YAAQ;QAC1D,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,OAAO,IAAM,IAAI;gBAAE,cAAc;gBAAM,MAAM;YAAW;IAC1D,CAAC;AAGI,MAAM,2BAA2B,CACtC;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,cAAc,SAAS,CAAC,CAAC,QAAU,MAAM,YAAY,EAAE;QAC3E,OAAO,IAAM;IACf,GAAG;QAAC;KAAS;AACf", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/input-prompt.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { createJSONStorage, persist } from \"zustand/middleware\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\n\r\ninterface InputPromptState {\r\n  prompt: string;\r\n  setPrompt: (prompt: string) => void;\r\n  isNewUser: boolean;\r\n  setIsNewUser: (isNewUser: boolean) => void;\r\n}\r\n\r\nexport const useInputPromptStore = create<InputPromptState>()(\r\n  persist(\r\n    (set) => ({\r\n      prompt: \"\",\r\n      setPrompt: (prompt) => set({ prompt }),\r\n      isNewUser: false,\r\n      setIsNewUser: (isNewUser) => set({ isNewUser }),\r\n    }),\r\n    {\r\n      name: \"input-prompt\",\r\n      storage: createJSONStorage(() => localStorage),\r\n    },\r\n  ),\r\n);\r\n\r\nexport const useInputPrompt = () =>\r\n  useInputPromptStore(\r\n    useShallow((state) => ({\r\n      prompt: state.prompt,\r\n      setPrompt: state.setPrompt,\r\n    })),\r\n  );\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AASO,MAAM,sBAAsB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,IACtC,CAAA,GAAA,yPAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QACpC,WAAW;QACX,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC/C,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,yPAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;AACnC;AAIG,MAAM,iBAAiB,IAC5B,oBACE,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,QAAQ,MAAM,MAAM;YACpB,WAAW,MAAM,SAAS;QAC5B,CAAC", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/shiki.ts"], "sourcesContent": ["// https://shiki.style/guide/bundles#fine-grained-bundle\r\n\r\nimport { captureException } from \"@sentry/nextjs\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { createdBundledHighlighter, createSingletonShorthands } from \"shiki/core\";\r\nimport { createJavaScriptRegexEngine } from \"shiki/engine/javascript\";\r\n\r\nconst BundledLanguage = {\r\n  typescript: () => import(\"@shikijs/langs/typescript\"),\r\n  javascript: () => import(\"@shikijs/langs/javascript\"),\r\n  jsx: () => import(\"@shikijs/langs/jsx\"),\r\n  tsx: () => import(\"@shikijs/langs/tsx\"),\r\n  css: () => import(\"@shikijs/langs/css\"),\r\n  scss: () => import(\"@shikijs/langs/scss\"),\r\n  html: () => import(\"@shikijs/langs/html\"),\r\n  sql: () => import(\"@shikijs/langs/sql\"),\r\n  json: () => import(\"@shikijs/langs/json\"),\r\n  markdown: () => import(\"@shikijs/langs/markdown\"),\r\n  yaml: () => import(\"@shikijs/langs/yaml\"),\r\n  bash: () => import(\"@shikijs/langs/bash\"),\r\n};\r\n\r\nconst BundledTheme = {\r\n  \"light-plus\": () => import(\"@shikijs/themes/light-plus\"),\r\n  houston: () => import(\"@shikijs/themes/houston\"),\r\n};\r\n\r\nconst createHighlighter = /* @__PURE__ */ createdBundledHighlighter<\r\n  keyof typeof BundledLanguage,\r\n  keyof typeof BundledTheme\r\n>({\r\n  langs: BundledLanguage,\r\n  themes: BundledTheme,\r\n  engine: () => createJavaScriptRegexEngine(),\r\n});\r\n\r\nconst { codeToHtml: codeToHtmlSingleton } =\r\n  /* @__PURE__ */ createSingletonShorthands(createHighlighter);\r\n\r\nconst codeToHtml = async (code: string, language: string) => {\r\n  return codeToHtmlSingleton(code, {\r\n    lang: language,\r\n    themes: { light: \"light-plus\", dark: \"houston\" },\r\n  });\r\n};\r\n\r\nconst LANGUAGE_MAP: Record<string, string> = {\r\n  js: \"javascript\",\r\n  cjs: \"javascript\",\r\n  mjs: \"javascript\",\r\n  jsx: \"jsx\",\r\n  ts: \"typescript\",\r\n  tsx: \"tsx\",\r\n  css: \"css\",\r\n  scss: \"scss\",\r\n  html: \"html\",\r\n  json: \"json\",\r\n  md: \"markdown\",\r\n  sh: \"bash\",\r\n  sql: \"sql\",\r\n  yml: \"yaml\",\r\n  yaml: \"yaml\",\r\n};\r\n\r\nfunction getLanguageFromFilename(filename: string): string {\r\n  if (!filename) return \"text\";\r\n\r\n  const extension = filename.split(\".\").pop()?.toLowerCase() || \"\";\r\n\r\n  return LANGUAGE_MAP[extension] || \"text\";\r\n}\r\n\r\nconst useHighlightedCode = (code: string, language: string) => {\r\n  const [highlightedHtml, setHighlightedHtml] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const highlightCode = async () => {\r\n      if (!code || !isMounted) return;\r\n\r\n      try {\r\n        const html = await codeToHtml(code, language);\r\n        if (isMounted) {\r\n          setHighlightedHtml(html);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to highlight code:\", error);\r\n        captureException(error);\r\n\r\n        if (isMounted) {\r\n          setHighlightedHtml(code);\r\n        }\r\n      }\r\n    };\r\n\r\n    highlightCode();\r\n\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [code, language]);\r\n\r\n  return highlightedHtml;\r\n};\r\n\r\nexport { getLanguageFromFilename, useHighlightedCode };\r\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;;AAExD;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,kBAAkB;IACtB,YAAY;IACZ,YAAY;IACZ,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,UAAU;IACV,MAAM;IACN,MAAM;AACR;AAEA,MAAM,eAAe;IACnB,cAAc;IACd,SAAS;AACX;AAEA,MAAM,oBAAoB,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD,EAGhE;IACA,OAAO;IACP,QAAQ;IACR,QAAQ,IAAM,CAAA,GAAA,0JAAA,CAAA,8BAA2B,AAAD;AAC1C;AAEA,MAAM,EAAE,YAAY,mBAAmB,EAAE,GACvC,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD,EAAE;AAE5C,MAAM,aAAa,OAAO,MAAc;IACtC,OAAO,oBAAoB,MAAM;QAC/B,MAAM;QACN,QAAQ;YAAE,OAAO;YAAc,MAAM;QAAU;IACjD;AACF;AAEA,MAAM,eAAuC;IAC3C,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,MAAM;AACR;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IAE9D,OAAO,YAAY,CAAC,UAAU,IAAI;AACpC;AAEA,MAAM,qBAAqB,CAAC,MAAc;IACxC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;QAEhB,MAAM,gBAAgB;YACpB,IAAI,CAAC,QAAQ,CAAC,WAAW;YAEzB,IAAI;gBACF,MAAM,OAAO,MAAM,WAAW,MAAM;gBACpC,IAAI,WAAW;oBACb,mBAAmB;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE;gBAEjB,IAAI,WAAW;oBACb,mBAAmB;gBACrB;YACF;QACF;QAEA;QAEA,OAAO;YACL,YAAY;QACd;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/xml-parser.ts"], "sourcesContent": ["import { MessageActions, ParsedCommand } from \"@/types/thread-message\";\r\nconst ACTION_TAG_REGEX = /<([a-z_]+)([^>]*?)(?:\\/>|>([\\s\\S]*?)<\\/\\1\\s*>|>([\\s\\S]*)?$)/gi;\r\nconst ATTR_REGEX = /(\\w+)=(?:\"([^\"]*)\"|\\[([^\\]]*)\\])/g;\r\nconst FILE_SPLIT_REGEX = /\\s*,\\s*/;\r\nconst QUOTE_TRIM_REGEX = /^[\"']|[\"']$/g;\r\nconst ERROR_REGEX = /(error|failed)/i;\r\n\r\n// file operations are handled in file-operations.ts\r\nconst ACTION_TAGS = new Set([\r\n  \"open_files_in_editor\",\r\n  \"close_files_in_editor\",\r\n  \"check_for_errors\",\r\n  \"send_terminal_command\",\r\n  \"reset_next_server\",\r\n  \"execute_sql_query\",\r\n]);\r\n\r\nexport interface ParsedStreamData {\r\n  communication?: string;\r\n  actions: ParsedCommand[];\r\n}\r\n\r\nconst COMMUNICATION_START = \"<communication>\";\r\nconst COMMUNICATION_END = \"</communication>\";\r\nconst ACTIONS_START = \"<actions>\";\r\nconst ACTIONS_END = \"</actions>\";\r\n\r\nfunction extractSection(content: string, startTag: string, endTag: string): string | null {\r\n  const startIdx = content.indexOf(startTag);\r\n  if (startIdx === -1) return \"\";\r\n\r\n  const contentStart = startIdx + startTag.length;\r\n  const endIdx = content.indexOf(endTag, contentStart);\r\n\r\n  // Stop at the first end tag found\r\n  if (endIdx === -1) return content.slice(contentStart).trim();\r\n\r\n  return content.slice(contentStart, endIdx).trim();\r\n}\r\n\r\nexport function extractActions(content: string): string | null {\r\n  return extractSection(content, ACTIONS_START, ACTIONS_END);\r\n}\r\n\r\nexport function parseStreamContent(content: string): ParsedStreamData {\r\n  if (!content?.trim()) return { actions: [] };\r\n\r\n  const result: ParsedStreamData = { actions: [] };\r\n\r\n  // Extract communication section if present\r\n  const communicationContent = extractSection(content, COMMUNICATION_START, COMMUNICATION_END);\r\n  if (communicationContent) {\r\n    result.communication = communicationContent;\r\n  }\r\n\r\n  // Extract actions section\r\n  const actionsContent = extractActions(content);\r\n  if (!actionsContent) {\r\n    return result;\r\n  }\r\n\r\n  // Reset lastIndex for safety with global regex\r\n  ACTION_TAG_REGEX.lastIndex = 0;\r\n  let match;\r\n\r\n  // Use pre-compiled regex for action tags\r\n  while ((match = ACTION_TAG_REGEX.exec(actionsContent)) !== null) {\r\n    const [fullMatch, type, attrs, content, incompleteContent] = match;\r\n    if (!ACTION_TAGS.has(type)) continue;\r\n\r\n    // Skip if this is a partial match at the end of the string\r\n    if (match.index + fullMatch.length > actionsContent.length) {\r\n      break;\r\n    }\r\n\r\n    // TODO: add with_build and failed to the command\r\n    const command: ParsedCommand = {\r\n      type: type as MessageActions,\r\n      status: incompleteContent !== undefined ? \"pending\" : \"completed\",\r\n    };\r\n\r\n    // Parse attributes using pre-compiled regex\r\n    ATTR_REGEX.lastIndex = 0; // Reset lastIndex for global regex\r\n    let attrMatch;\r\n\r\n    while ((attrMatch = ATTR_REGEX.exec(attrs)) !== null) {\r\n      const [, key, dqValue, sqValue] = attrMatch;\r\n      const value = dqValue !== undefined ? dqValue : sqValue;\r\n\r\n      if (!key) continue;\r\n\r\n      if (key === \"file_path\") command.path = value;\r\n      if (key === \"message\") command.message = value;\r\n      // if (key === \"with_build\") command.with_build = value === \"true\";\r\n      if (key === \"command\") command.command = value;\r\n\r\n      // Special handling for files array\r\n      if (key === \"files\" && value) {\r\n        // More efficient file array parsing\r\n        command.files = value\r\n          .split(FILE_SPLIT_REGEX)\r\n          .map((s) => s.replace(QUOTE_TRIM_REGEX, \"\"))\r\n          .filter(Boolean);\r\n      }\r\n    }\r\n\r\n    // Handle content for non-file operations\r\n    const actualContent = content || incompleteContent;\r\n    if (actualContent && ![\"create_file\", \"update_file\", \"delete_file\"].includes(type)) {\r\n      command.content = actualContent.trim();\r\n\r\n      // Check for errors in content using pre-compiled regex\r\n      if (command.content && ERROR_REGEX.test(command.content)) {\r\n        command.status = \"failed\";\r\n        command.result = command.content;\r\n      }\r\n    }\r\n\r\n    result.actions.push(command);\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n// File operations parsing\r\nexport interface FileOperation {\r\n  type:\r\n    | \"create_file\"\r\n    | \"update_file\"\r\n    | \"full_file_rewrite\"\r\n    | \"update_file_sections\"\r\n    | \"delete_file\";\r\n  status: \"pending\" | \"completed\" | \"failed\";\r\n  filePath: string;\r\n  failed?: boolean;\r\n  length?: number;\r\n}\r\n\r\nconst TAG_TYPES = [\r\n  \"create_file\",\r\n  \"update_file\",\r\n  \"full_file_rewrite\",\r\n  \"update_file_sections\",\r\n  \"check_for_errors\",\r\n];\r\n\r\nconst FILE_OPERATION_PATTERN = new RegExp(\r\n  `<(${TAG_TYPES.join(\"|\")})\\\\s+file_path=\"([^\"]+)\"[^>]*>([\\\\s\\\\S]*?)(?=<(?:${TAG_TYPES.join(\"|\")}|/)|$)`,\r\n  \"g\",\r\n);\r\n\r\nexport const parseFileOperations = (content: string): FileOperation[] => {\r\n  const fileOperations: FileOperation[] = [];\r\n\r\n  if (!content) return fileOperations;\r\n\r\n  const actionsContent = extractActions(content);\r\n  if (!actionsContent) return fileOperations;\r\n\r\n  FILE_OPERATION_PATTERN.lastIndex = 0;\r\n  let match;\r\n  while ((match = FILE_OPERATION_PATTERN.exec(actionsContent)) !== null) {\r\n    const [, type, filePath, content] = match;\r\n\r\n    // Check if this operation has a closing tag\r\n    const hasClosingTag = actionsContent.includes(`</${type}>`);\r\n\r\n    fileOperations.push({\r\n      type: type as FileOperation[\"type\"],\r\n      status: hasClosingTag ? \"completed\" : \"pending\",\r\n      filePath,\r\n      failed: content.trim() === \"true\" ? true : false,\r\n      ...(hasClosingTag ? {} : { length: content.length }),\r\n    });\r\n  }\r\n\r\n  return fileOperations;\r\n};\r\n\r\nexport const COMMUNICATION_REGEX = /<communication>([\\s\\S]*?)<\\/communication>/i;\r\nexport const ACTIONS_REGEX = /<actions>/i;\r\n\r\nexport const parseCommunication = (content: string) => {\r\n  const newContent = typeof content === \"string\" ? content : String(content || \"\");\r\n\r\n  const communicationMatch = newContent.match(COMMUNICATION_REGEX);\r\n  if (communicationMatch?.[1]) {\r\n    return {\r\n      communication: communicationMatch[1].trim().replace(/<\\/?communication>/gi, \"\"),\r\n    };\r\n  }\r\n\r\n  if (newContent.includes(\"<communication>\")) {\r\n    const startIndex = newContent.indexOf(\"<communication>\") + \"<communication>\".length;\r\n    const actionsIndex = newContent.indexOf(\"<actions>\");\r\n    const endIndex = actionsIndex !== -1 ? actionsIndex : newContent.length;\r\n    return {\r\n      communication: newContent.slice(startIndex, endIndex).trim(),\r\n    };\r\n  }\r\n\r\n  if (newContent.includes(\"</communication>\")) {\r\n    const endIndex = newContent.indexOf(\"</communication>\");\r\n    return {\r\n      communication: newContent.slice(0, endIndex).trim(),\r\n    };\r\n  }\r\n\r\n  const actionsIndex = newContent.indexOf(\"<actions>\");\r\n  return {\r\n    communication:\r\n      actionsIndex !== -1 ? newContent.slice(0, actionsIndex).trim() : newContent.trim(),\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AACA,MAAM,mBAAmB;AACzB,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AAEpB,oDAAoD;AACpD,MAAM,cAAc,IAAI,IAAI;IAC1B;IACA;IACA;IACA;IACA;IACA;CACD;AAOD,MAAM,sBAAsB;AAC5B,MAAM,oBAAoB;AAC1B,MAAM,gBAAgB;AACtB,MAAM,cAAc;AAEpB,SAAS,eAAe,OAAe,EAAE,QAAgB,EAAE,MAAc;IACvE,MAAM,WAAW,QAAQ,OAAO,CAAC;IACjC,IAAI,aAAa,CAAC,GAAG,OAAO;IAE5B,MAAM,eAAe,WAAW,SAAS,MAAM;IAC/C,MAAM,SAAS,QAAQ,OAAO,CAAC,QAAQ;IAEvC,kCAAkC;IAClC,IAAI,WAAW,CAAC,GAAG,OAAO,QAAQ,KAAK,CAAC,cAAc,IAAI;IAE1D,OAAO,QAAQ,KAAK,CAAC,cAAc,QAAQ,IAAI;AACjD;AAEO,SAAS,eAAe,OAAe;IAC5C,OAAO,eAAe,SAAS,eAAe;AAChD;AAEO,SAAS,mBAAmB,OAAe;IAChD,IAAI,CAAC,SAAS,QAAQ,OAAO;QAAE,SAAS,EAAE;IAAC;IAE3C,MAAM,SAA2B;QAAE,SAAS,EAAE;IAAC;IAE/C,2CAA2C;IAC3C,MAAM,uBAAuB,eAAe,SAAS,qBAAqB;IAC1E,IAAI,sBAAsB;QACxB,OAAO,aAAa,GAAG;IACzB;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,eAAe;IACtC,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IAEA,+CAA+C;IAC/C,iBAAiB,SAAS,GAAG;IAC7B,IAAI;IAEJ,yCAAyC;IACzC,MAAO,CAAC,QAAQ,iBAAiB,IAAI,CAAC,eAAe,MAAM,KAAM;QAC/D,MAAM,CAAC,WAAW,MAAM,OAAO,SAAS,kBAAkB,GAAG;QAC7D,IAAI,CAAC,YAAY,GAAG,CAAC,OAAO;QAE5B,2DAA2D;QAC3D,IAAI,MAAM,KAAK,GAAG,UAAU,MAAM,GAAG,eAAe,MAAM,EAAE;YAC1D;QACF;QAEA,iDAAiD;QACjD,MAAM,UAAyB;YAC7B,MAAM;YACN,QAAQ,sBAAsB,YAAY,YAAY;QACxD;QAEA,4CAA4C;QAC5C,WAAW,SAAS,GAAG,GAAG,mCAAmC;QAC7D,IAAI;QAEJ,MAAO,CAAC,YAAY,WAAW,IAAI,CAAC,MAAM,MAAM,KAAM;YACpD,MAAM,GAAG,KAAK,SAAS,QAAQ,GAAG;YAClC,MAAM,QAAQ,YAAY,YAAY,UAAU;YAEhD,IAAI,CAAC,KAAK;YAEV,IAAI,QAAQ,aAAa,QAAQ,IAAI,GAAG;YACxC,IAAI,QAAQ,WAAW,QAAQ,OAAO,GAAG;YACzC,mEAAmE;YACnE,IAAI,QAAQ,WAAW,QAAQ,OAAO,GAAG;YAEzC,mCAAmC;YACnC,IAAI,QAAQ,WAAW,OAAO;gBAC5B,oCAAoC;gBACpC,QAAQ,KAAK,GAAG,MACb,KAAK,CAAC,kBACN,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO,CAAC,kBAAkB,KACvC,MAAM,CAAC;YACZ;QACF;QAEA,yCAAyC;QACzC,MAAM,gBAAgB,WAAW;QACjC,IAAI,iBAAiB,CAAC;YAAC;YAAe;YAAe;SAAc,CAAC,QAAQ,CAAC,OAAO;YAClF,QAAQ,OAAO,GAAG,cAAc,IAAI;YAEpC,uDAAuD;YACvD,IAAI,QAAQ,OAAO,IAAI,YAAY,IAAI,CAAC,QAAQ,OAAO,GAAG;gBACxD,QAAQ,MAAM,GAAG;gBACjB,QAAQ,MAAM,GAAG,QAAQ,OAAO;YAClC;QACF;QAEA,OAAO,OAAO,CAAC,IAAI,CAAC;IACtB;IAEA,OAAO;AACT;AAgBA,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,yBAAyB,IAAI,OACjC,CAAC,EAAE,EAAE,UAAU,IAAI,CAAC,KAAK,iDAAiD,EAAE,UAAU,IAAI,CAAC,KAAK,MAAM,CAAC,EACvG;AAGK,MAAM,sBAAsB,CAAC;IAClC,MAAM,iBAAkC,EAAE;IAE1C,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,iBAAiB,eAAe;IACtC,IAAI,CAAC,gBAAgB,OAAO;IAE5B,uBAAuB,SAAS,GAAG;IACnC,IAAI;IACJ,MAAO,CAAC,QAAQ,uBAAuB,IAAI,CAAC,eAAe,MAAM,KAAM;QACrE,MAAM,GAAG,MAAM,UAAU,QAAQ,GAAG;QAEpC,4CAA4C;QAC5C,MAAM,gBAAgB,eAAe,QAAQ,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAE1D,eAAe,IAAI,CAAC;YAClB,MAAM;YACN,QAAQ,gBAAgB,cAAc;YACtC;YACA,QAAQ,QAAQ,IAAI,OAAO,SAAS,OAAO;YAC3C,GAAI,gBAAgB,CAAC,IAAI;gBAAE,QAAQ,QAAQ,MAAM;YAAC,CAAC;QACrD;IACF;IAEA,OAAO;AACT;AAEO,MAAM,sBAAsB;AAC5B,MAAM,gBAAgB;AAEtB,MAAM,qBAAqB,CAAC;IACjC,MAAM,aAAa,OAAO,YAAY,WAAW,UAAU,OAAO,WAAW;IAE7E,MAAM,qBAAqB,WAAW,KAAK,CAAC;IAC5C,IAAI,oBAAoB,CAAC,EAAE,EAAE;QAC3B,OAAO;YACL,eAAe,kBAAkB,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,wBAAwB;QAC9E;IACF;IAEA,IAAI,WAAW,QAAQ,CAAC,oBAAoB;QAC1C,MAAM,aAAa,WAAW,OAAO,CAAC,qBAAqB,kBAAkB,MAAM;QACnF,MAAM,eAAe,WAAW,OAAO,CAAC;QACxC,MAAM,WAAW,iBAAiB,CAAC,IAAI,eAAe,WAAW,MAAM;QACvE,OAAO;YACL,eAAe,WAAW,KAAK,CAAC,YAAY,UAAU,IAAI;QAC5D;IACF;IAEA,IAAI,WAAW,QAAQ,CAAC,qBAAqB;QAC3C,MAAM,WAAW,WAAW,OAAO,CAAC;QACpC,OAAO;YACL,eAAe,WAAW,KAAK,CAAC,GAAG,UAAU,IAAI;QACnD;IACF;IAEA,MAAM,eAAe,WAAW,OAAO,CAAC;IACxC,OAAO;QACL,eACE,iBAAiB,CAAC,IAAI,WAAW,KAAK,CAAC,GAAG,cAAc,IAAI,KAAK,WAAW,IAAI;IACpF;AACF", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/format-timestamp.ts"], "sourcesContent": ["import { format, formatDistanceToNow, isToday, isYesterday } from \"date-fns\";\r\n\r\nexport function formatTimestamp(\r\n  isoString: string,\r\n  type:\r\n    | \"edited\"\r\n    | \"lastChat\"\r\n    | \"message-timestamp\"\r\n    | \"deployed\"\r\n    | \"default\"\r\n    | \"commit-history\" = \"default\",\r\n  year: boolean = false,\r\n): string {\r\n  const utcString = isoString.endsWith(\"Z\") ? isoString : isoString + \"Z\";\r\n  const date = new Date(utcString);\r\n  const currentYear = new Date().getFullYear();\r\n  const dateYear = date.getFullYear();\r\n\r\n  switch (type) {\r\n    case \"edited\":\r\n      return `Edited on ${format(date, dateYear === currentYear && !year ? \"d MMMM\" : \"d MMMM yyyy\")}`;\r\n    case \"message-timestamp\":\r\n      return format(\r\n        date,\r\n        dateYear === currentYear && !year ? \"h:mm a d MMMM\" : \"h:mm a d MMMM yyyy\",\r\n      );\r\n    case \"lastChat\":\r\n      if (isToday(date)) {\r\n        return `Last chat today at ${format(date, \"h:mm a\")}`;\r\n      } else if (isYesterday(date)) {\r\n        return \"Last chat yesterday\";\r\n      } else {\r\n        return `Last chat ${formatDistanceToNow(date)} ago`;\r\n      }\r\n    case \"deployed\":\r\n      return `Deployed on ${format(date, dateYear === currentYear && !year ? \"h:mm a d MMMM\" : \"h:mm a d MMMM yyyy\")}`;\r\n    case \"commit-history\":\r\n      if (isToday(date)) {\r\n        return `Last commit today at ${format(date, \"h:mm a\")}`;\r\n      } else if (isYesterday(date)) {\r\n        return \"Last commit yesterday\";\r\n      } else {\r\n        return `Last commit ${formatDistanceToNow(date)} ago`;\r\n      }\r\n    default:\r\n      return format(\r\n        date,\r\n        dateYear === currentYear && !year ? \"h:mm a d MMMM\" : \"h:mm a d MMMM yyyy\",\r\n      );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;;AAEO,SAAS,gBACd,SAAiB,EACjB,OAMuB,SAAS,EAChC,OAAgB,KAAK;IAErB,MAAM,YAAY,UAAU,QAAQ,CAAC,OAAO,YAAY,YAAY;IACpE,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,WAAW,KAAK,WAAW;IAEjC,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,UAAU,EAAE,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,MAAM,aAAa,eAAe,CAAC,OAAO,WAAW,gBAAgB;QAClG,KAAK;YACH,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EACV,MACA,aAAa,eAAe,CAAC,OAAO,kBAAkB;QAE1D,KAAK;YACH,IAAI,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACjB,OAAO,CAAC,mBAAmB,EAAE,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,MAAM,WAAW;YACvD,OAAO,IAAI,CAAA,GAAA,kMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;gBAC5B,OAAO;YACT,OAAO;gBACL,OAAO,CAAC,UAAU,EAAE,CAAA,GAAA,0MAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,IAAI,CAAC;YACrD;QACF,KAAK;YACH,OAAO,CAAC,YAAY,EAAE,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,MAAM,aAAa,eAAe,CAAC,OAAO,kBAAkB,uBAAuB;QAClH,KAAK;YACH,IAAI,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACjB,OAAO,CAAC,qBAAqB,EAAE,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,MAAM,WAAW;YACzD,OAAO,IAAI,CAAA,GAAA,kMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;gBAC5B,OAAO;YACT,OAAO;gBACL,OAAO,CAAC,YAAY,EAAE,CAAA,GAAA,0MAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,IAAI,CAAC;YACvD;QACF;YACE,OAAO,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EACV,MACA,aAAa,eAAe,CAAC,OAAO,kBAAkB;IAE5D;AACF", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/constants/image-validation.ts"], "sourcesContent": ["export const MAX_IMAGES_PER_MESSAGE = 5;\r\nexport const MAX_IMAGE_SIZE = 10 * 1024 * 1024;\r\nexport const ALLOWED_IMAGE_TYPES = [\"image/jpeg\", \"image/png\", \"image/gif\", \"image/webp\"];\r\n\r\nexport const validateImage = (file: File): void => {\r\n  if (file.size > MAX_IMAGE_SIZE) {\r\n    throw new Error(`Image too large: ${file.name}. Maximum size is 10MB per image.`);\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    throw new Error(`Invalid image type: ${file.name}. Allowed types: JPEG, PNG, GIF, WebP.`);\r\n  }\r\n};\r\n\r\nexport const validateImageCount = (currentCount: number, additionalCount: number = 0): void => {\r\n  if (currentCount + additionalCount > MAX_IMAGES_PER_MESSAGE) {\r\n    throw new Error(`Maximum ${MAX_IMAGES_PER_MESSAGE} are allowed per message.`);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAO,MAAM,yBAAyB;AAC/B,MAAM,iBAAiB,KAAK,OAAO;AACnC,MAAM,sBAAsB;IAAC;IAAc;IAAa;IAAa;CAAa;AAElF,MAAM,gBAAgB,CAAC;IAC5B,IAAI,KAAK,IAAI,GAAG,gBAAgB;QAC9B,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,iCAAiC,CAAC;IAClF;IAEA,IAAI,CAAC,oBAAoB,QAAQ,CAAC,KAAK,IAAI,GAAG;QAC5C,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,KAAK,IAAI,CAAC,sCAAsC,CAAC;IAC1F;AACF;AAEO,MAAM,qBAAqB,CAAC,cAAsB,kBAA0B,CAAC;IAClF,IAAI,eAAe,kBAAkB,wBAAwB;QAC3D,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,uBAAuB,yBAAyB,CAAC;IAC9E;AACF", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/utils/error-utils.ts"], "sourcesContent": ["/**\r\n * Filters out webpack hot-update related errors that should be ignored\r\n * @param {Array<unknown>} errors - Array of error objects\r\n * @returns {Array<unknown>} - Filtered array of errors\r\n */\r\nexport const filterIgnorableErrors = (errors: unknown[]): unknown[] => {\r\n  if (!errors || !errors.length) return [];\r\n\r\n  return errors.filter((error) => {\r\n    // Skip empty error objects\r\n    if (\r\n      !error ||\r\n      typeof error !== \"object\" ||\r\n      !(\"message\" in error) ||\r\n      !error.message ||\r\n      error.message === \"{}\" ||\r\n      error.message === \"undefined\" ||\r\n      error.message === \"[object Object]\" ||\r\n      (typeof error.message === \"string\" && error.message.trim() === \"\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip cancelled errors\r\n    if (typeof error.message === \"string\" && error.message.includes('\"cancelled\": true')) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Next.js router navigation errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"Invariant: attempted to hard navigate to the same URL\") &&\r\n      \"stack\" in error &&\r\n      typeof error.stack === \"string\" &&\r\n      error.stack.includes(\"handleHardNavigation\") &&\r\n      error.stack.includes(\"Router.change\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Next.js hydration errors with specific stack trace pattern\r\n    if (\r\n      \"stack\" in error &&\r\n      typeof error.stack === \"string\" &&\r\n      error.stack.includes(\"hydration-error-info.js\") &&\r\n      error.stack.includes(\"setup-hydration-warning.js\") &&\r\n      error.stack.includes(\"handleRouteInfoError\") &&\r\n      error.stack.includes(\"getRouteInfo\") &&\r\n      error.stack.includes(\"Router.change\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Next.js internal errors with \"{}\" at the beginning of the message and stack trace\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message.startsWith(\"{}\") &&\r\n      (error.message.includes(\"webpack-internal\") ||\r\n        error.message.includes(\"next/dist/client\") ||\r\n        error.message.includes(\"hydration-error-info.js\") ||\r\n        error.message.includes(\"setup-hydration-warning.js\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip \"Page not found\" errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      (error.message.includes(\"Page not found\") ||\r\n        error.message.includes(\"page not found\") ||\r\n        error.message.includes(\"Cannot find page\") ||\r\n        error.message.includes(\"cannot find page\") ||\r\n        error.message.includes(\"page does not exist\") ||\r\n        error.message.includes(\"Page does not exist\") ||\r\n        error.message.includes(\"at /\") ||\r\n        error.message.includes(\"Path: /_next/static/chunks/pages/\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip 404 Not Found errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      (error.message.includes(\"404 Not Found\") ||\r\n        error.message.includes(\"not found\") ||\r\n        error.message.includes(\"Failed to load script\") ||\r\n        error.message.toLowerCase().includes(\"404\") ||\r\n        error.message.includes(\".html\") ||\r\n        (\"status\" in error && error.status === 404) ||\r\n        (\"statusCode\" in error && error.statusCode === 404) ||\r\n        (\"code\" in error && error.code === 404))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Next.js hydration error with [object Object] and specific stack trace\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message === \"[object Object]\" &&\r\n      \"stack\" in error &&\r\n      typeof error.stack === \"string\" &&\r\n      error.stack.includes(\"hydration-error-info.js\") &&\r\n      error.stack.includes(\"setup-hydration-warning.js\") &&\r\n      error.stack.includes(\"handleRouteInfoError\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Next.js hydration errors with [object Object] and hydration-error-info\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"[object Object]\") &&\r\n      \"stack\" in error &&\r\n      typeof error.stack === \"string\" &&\r\n      error.stack.includes(\"hydration-error-info\") &&\r\n      error.stack.includes(\"handleRouteInfoError\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip hydration-related errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      (error.message.includes(\"Hydration failed\") ||\r\n        error.message.includes(\"hydration error\") ||\r\n        error.message.includes(\"An error occurred during hydration\") ||\r\n        error.message.includes(\"react-hydration-error\") ||\r\n        error.message.includes(\"validateDOMNesting\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip webpack hot-update errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      (error.message.includes(\"webpack.hot-update.json\") ||\r\n        error.message.includes(\"hot-update.js\") ||\r\n        error.message.includes(\"webpack-hmr\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip unknown errors with no details\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message === \"Unknown error\" &&\r\n      (!(\"stack\" in error) || !error.stack)\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip SoftGen script related errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      // error.message.includes('cdn.softgen.ai/script.js') ||\r\n      (error.message.includes(\"Failed to send message\") || error.message.includes(\"SoftGen\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip \"Abort fetching component for route\" errors\r\n    // TODO: Consider refactoring agent to handle route fetching/cancellation more gracefully -> router.replace, router.replace\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"Abort fetching component for route:\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Next.js resource errors\r\n    if (typeof error.message === \"string\" && error.message.includes(\"/_next/static/\")) {\r\n      // Keep only page-specific errors that might be relevant\r\n      const isPageError =\r\n        error.message.includes(\"/_next/static/chunks/pages/\") &&\r\n        !error.message.includes(\"/_next/static/chunks/pages/_app.js\") &&\r\n        !error.message.includes(\"/_next/static/chunks/pages/_error.js\");\r\n\r\n      return isPageError;\r\n    }\r\n\r\n    // Skip any errors containing _next/static (comprehensive filtering)\r\n    if (typeof error.message === \"string\" && error.message.toLowerCase().includes(\"_next/static\")) {\r\n      return false;\r\n    }\r\n\r\n    // Skip specific Firebase/Firestore 404 errors that are expected or configuration-related\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"404 Not Found\") &&\r\n      (error.message.includes(\"identitytoolkit.googleapis.com/v1/accounts:lookup\") ||\r\n        error.message.includes(\"identitytoolkit.googleapis.com/v1/accounts:update\") ||\r\n        error.message.includes(\"identitytoolkit.googleapis.com/v1/accounts:signInWithPassword\") ||\r\n        error.message.includes(\"securetoken.googleapis.com/v1/token\") ||\r\n        error.message.includes(\r\n          \"firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel\",\r\n        ) ||\r\n        error.message.includes(\r\n          \"firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel\",\r\n        ))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Firestore channel errors and other Firebase API errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      (error.message.includes(\"Failed to fetch\") || error.message === \"Error: Failed to fetch\") &&\r\n      \"stack\" in error &&\r\n      typeof error.stack === \"string\" &&\r\n      (error.stack.includes(\r\n        \"firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel\",\r\n      ) ||\r\n        error.stack.includes(\r\n          \"firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel\",\r\n        ) ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:lookup\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:update\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:signInWithPassword\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:signUp\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:sendOobCode\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:resetPassword\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:delete\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:sendVerificationCode\") ||\r\n        error.stack.includes(\"identitytoolkit.googleapis.com/v1/accounts:verifyPassword\") ||\r\n        error.stack.includes(\"securetoken.googleapis.com/v1/token\") ||\r\n        error.stack.includes(\"firebaseinstallations.googleapis.com\") ||\r\n        error.stack.includes(\"fcmregistrations.googleapis.com\") ||\r\n        error.stack.includes(\"firebasedatabase.app\") ||\r\n        error.stack.includes(\"firestore.googleapis.com/google.firestore.v1.Firestore/BatchGet\") ||\r\n        error.stack.includes(\"firestore.googleapis.com/google.firestore.v1.Firestore/RunQuery\") ||\r\n        error.stack.includes(\"firestore.googleapis.com/google.firestore.v1.Firestore/Commit\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip network errors related to Firestore channels\r\n    if (\r\n      \"type\" in error &&\r\n      error.type === \"network\" &&\r\n      \"details\" in error &&\r\n      error.details &&\r\n      typeof error.details === \"object\" &&\r\n      \"url\" in error.details &&\r\n      typeof error.details.url === \"string\" &&\r\n      (error.details.url.includes(\r\n        \"firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel\",\r\n      ) ||\r\n        error.details.url.includes(\r\n          \"firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel\",\r\n        ))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip console errors related to Firestore channels\r\n    if (\r\n      \"type\" in error &&\r\n      error.type === \"console\" &&\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"Fetch error for\") &&\r\n      (error.message.includes(\r\n        \"firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel\",\r\n      ) ||\r\n        error.message.includes(\r\n          \"firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel\",\r\n        ))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip Next.js stack frame errors\r\n    if (\r\n      (typeof error.message === \"string\" &&\r\n        error.message.includes(\"/__nextjs_original-stack-frame\")) ||\r\n      (\"details\" in error &&\r\n        error.details &&\r\n        typeof error.details === \"object\" &&\r\n        \"url\" in error.details &&\r\n        typeof error.details.url === \"string\" &&\r\n        error.details.url.includes(\"/__nextjs_original-stack-frame\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip \"Fetch error for /__nextjs_original-stack-frame\" errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"Fetch error for /__nextjs_original-stack-frame\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip \"404 Not Found\" errors for Next.js stack frames\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"404 Not Found\") &&\r\n      error.message.includes(\"/__nextjs_original-stack-frame\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip network errors related to Next.js resources\r\n    if (\r\n      \"type\" in error &&\r\n      error.type === \"network\" &&\r\n      \"details\" in error &&\r\n      error.details &&\r\n      typeof error.details === \"object\" &&\r\n      \"url\" in error.details &&\r\n      typeof error.details.url === \"string\" &&\r\n      error.details.url.includes(\"/_next/\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip hydration mismatch errors for Next.js links with query parameters\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      (error.message.includes(\"Prop `%s` did not match\") ||\r\n        error.message.includes(\"Warning: Prop\")) &&\r\n      error.message.includes(\"href\") &&\r\n      error.message.includes(\"?\")\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Skip font and image loading errors\r\n    if (\r\n      typeof error.message === \"string\" &&\r\n      (error.message.toLowerCase().includes(\"fonts\") ||\r\n        error.message.toLowerCase().includes(\"images\") ||\r\n        error.message.toLowerCase().includes(\".png\") ||\r\n        error.message.toLowerCase().includes(\".jpeg\") ||\r\n        error.message.toLowerCase().includes(\".jpg\") ||\r\n        error.message.toLowerCase().includes(\".webp\") ||\r\n        error.message.toLowerCase().includes(\"font\") ||\r\n        error.message.toLowerCase().includes(\"woff\") ||\r\n        error.message.toLowerCase().includes(\"ttf\") ||\r\n        error.message.toLowerCase().includes(\"otf\") ||\r\n        error.message.toLowerCase().includes(\"eot\") ||\r\n        error.message.toLowerCase().includes(\"_next\"))\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  });\r\n};\r\n\r\n/**\r\n * Deduplicates errors based on their type and message\r\n * @param {Array<unknown>} errors - Array of error objects\r\n * @returns {Array<unknown>} - Deduplicated array of errors\r\n */\r\nexport const deduplicateErrors = (errors: unknown[]): unknown[] => {\r\n  if (!errors || !errors.length) return [];\r\n\r\n  // First filter out ignorable errors\r\n  const filteredErrors = filterIgnorableErrors(errors);\r\n\r\n  // Create a map to deduplicate errors\r\n  const errorMap = new Map<string, unknown>();\r\n\r\n  filteredErrors.forEach((error) => {\r\n    if (!error || typeof error !== \"object\") return;\r\n\r\n    // Skip network errors related to images\r\n    if (\r\n      \"type\" in error &&\r\n      error.type === \"network\" &&\r\n      \"details\" in error &&\r\n      error.details &&\r\n      typeof error.details === \"object\" &&\r\n      \"url\" in error.details &&\r\n      typeof error.details.url === \"string\" &&\r\n      (error.details.url.includes(\"images\") ||\r\n        error.details.url.includes(\"__nextjs\") ||\r\n        error.details.url.includes(\"fonts\"))\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      \"type\" in error &&\r\n      error.type === \"runtime\" &&\r\n      \"message\" in error &&\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"Unknown runtime error\")\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    // For Next.js resource errors, group them by the type of resource\r\n    if (\r\n      \"message\" in error &&\r\n      typeof error.message === \"string\" &&\r\n      error.message.includes(\"/_next/static/chunks/\")\r\n    ) {\r\n      // Extract the resource type (e.g., pages, webpack, main)\r\n      const resourceTypeMatch = error.message.match(/\\/_next\\/static\\/chunks\\/([^\\/\\.]+)/);\r\n      const resourceType = resourceTypeMatch ? resourceTypeMatch[1] : \"unknown\";\r\n\r\n      // For pages, extract the actual page path\r\n      if (resourceType === \"pages\") {\r\n        const pagePathMatch = error.message.match(/\\/_next\\/static\\/chunks\\/pages(\\/[^\\.]+)\\.js/);\r\n        if (pagePathMatch && pagePathMatch[1]) {\r\n          const key = `next-page-${pagePathMatch[1]}`;\r\n\r\n          if (!errorMap.has(key)) {\r\n            errorMap.set(key, {\r\n              ...error,\r\n              id:\r\n                \"id\" in error && error.id\r\n                  ? error.id\r\n                  : `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n              simplifiedMessage: `Page not found: ${pagePathMatch[1]}`,\r\n            });\r\n          }\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Create a key based on the resource type\r\n      const key = `next-resource-${resourceType}`;\r\n\r\n      // Only keep one error per resource type\r\n      if (!errorMap.has(key)) {\r\n        errorMap.set(key, {\r\n          ...error,\r\n          id:\r\n            \"id\" in error && error.id\r\n              ? error.id\r\n              : `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n          simplifiedMessage: `Failed to load Next.js ${resourceType} resources`,\r\n        });\r\n      }\r\n    } else {\r\n      // For other errors, use the first line of the message as the key\r\n      const messageFirstLine =\r\n        \"message\" in error && typeof error.message === \"string\"\r\n          ? error.message.split(\"\\n\")[0]\r\n          : \"unknown\";\r\n      const errorType = \"type\" in error && error.type ? String(error.type) : \"unknown\";\r\n      const key = `${errorType}-${messageFirstLine}`;\r\n\r\n      // Only keep the first occurrence of each error\r\n      if (!errorMap.has(key)) {\r\n        errorMap.set(key, {\r\n          ...error,\r\n          id:\r\n            \"id\" in error && error.id\r\n              ? error.id\r\n              : `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n        });\r\n      }\r\n    }\r\n  });\r\n\r\n  // Convert map values back to array\r\n  return Array.from(errorMap.values());\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AACM,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,EAAE,OAAO,EAAE;IAExC,OAAO,OAAO,MAAM,CAAC,CAAC;QACpB,2BAA2B;QAC3B,IACE,CAAC,SACD,OAAO,UAAU,YACjB,CAAC,CAAC,aAAa,KAAK,KACpB,CAAC,MAAM,OAAO,IACd,MAAM,OAAO,KAAK,QAClB,MAAM,OAAO,KAAK,eAClB,MAAM,OAAO,KAAK,qBACjB,OAAO,MAAM,OAAO,KAAK,YAAY,MAAM,OAAO,CAAC,IAAI,OAAO,IAC/D;YACA,OAAO;QACT;QAEA,wBAAwB;QACxB,IAAI,OAAO,MAAM,OAAO,KAAK,YAAY,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB;YACpF,OAAO;QACT;QAEA,wCAAwC;QACxC,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,4DACvB,WAAW,SACX,OAAO,MAAM,KAAK,KAAK,YACvB,MAAM,KAAK,CAAC,QAAQ,CAAC,2BACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,kBACrB;YACA,OAAO;QACT;QAEA,kEAAkE;QAClE,IACE,WAAW,SACX,OAAO,MAAM,KAAK,KAAK,YACvB,MAAM,KAAK,CAAC,QAAQ,CAAC,8BACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,iCACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,2BACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,mBACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,kBACrB;YACA,OAAO;QACT;QAEA,yFAAyF;QACzF,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,UAAU,CAAC,SACzB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,uBACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,uBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,8BACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,6BAA6B,GACtD;YACA,OAAO;QACT;QAEA,+BAA+B;QAC/B,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,qBACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,qBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,uBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,uBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,0BACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,0BACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,WACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,oCAAoC,GAC7D;YACA,OAAO;QACT;QAEA,4BAA4B;QAC5B,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,gBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,4BACvB,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,MAAM,OAAO,CAAC,QAAQ,CAAC,YACtB,YAAY,SAAS,MAAM,MAAM,KAAK,OACtC,gBAAgB,SAAS,MAAM,UAAU,KAAK,OAC9C,UAAU,SAAS,MAAM,IAAI,KAAK,GAAI,GACzC;YACA,OAAO;QACT;QAEA,6EAA6E;QAC7E,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,KAAK,qBAClB,WAAW,SACX,OAAO,MAAM,KAAK,KAAK,YACvB,MAAM,KAAK,CAAC,QAAQ,CAAC,8BACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,iCACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,yBACrB;YACA,OAAO;QACT;QAEA,8EAA8E;QAC9E,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,sBACvB,WAAW,SACX,OAAO,MAAM,KAAK,KAAK,YACvB,MAAM,KAAK,CAAC,QAAQ,CAAC,2BACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,yBACrB;YACA,OAAO;QACT;QAEA,gCAAgC;QAChC,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,uBACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,sBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,yCACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,4BACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB,GAC9C;YACA,OAAO;QACT;QAEA,iCAAiC;QACjC,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,8BACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc,GACvC;YACA,OAAO;QACT;QAEA,sCAAsC;QACtC,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,KAAK,mBAClB,CAAC,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK,GACpC;YACA,OAAO;QACT;QAEA,qCAAqC;QACrC,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,wDAAwD;QACxD,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,6BAA6B,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,GACtF;YACA,OAAO;QACT;QAEA,mDAAmD;QACnD,2HAA2H;QAC3H,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,wCACvB;YACA,OAAO;QACT;QAEA,+BAA+B;QAC/B,IAAI,OAAO,MAAM,OAAO,KAAK,YAAY,MAAM,OAAO,CAAC,QAAQ,CAAC,mBAAmB;YACjF,wDAAwD;YACxD,MAAM,cACJ,MAAM,OAAO,CAAC,QAAQ,CAAC,kCACvB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,yCACxB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC;YAE1B,OAAO;QACT;QAEA,oEAAoE;QACpE,IAAI,OAAO,MAAM,OAAO,KAAK,YAAY,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,iBAAiB;YAC7F,OAAO;QACT;QAEA,yFAAyF;QACzF,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACvB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,wDACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,wDACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,oEACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,0CACvB,MAAM,OAAO,CAAC,QAAQ,CACpB,4EAEF,MAAM,OAAO,CAAC,QAAQ,CACpB,uEACD,GACH;YACA,OAAO;QACT;QAEA,8DAA8D;QAC9D,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB,MAAM,OAAO,KAAK,wBAAwB,KACxF,WAAW,SACX,OAAO,MAAM,KAAK,KAAK,YACvB,CAAC,MAAM,KAAK,CAAC,QAAQ,CACnB,2EAEA,MAAM,KAAK,CAAC,QAAQ,CAClB,4EAEF,MAAM,KAAK,CAAC,QAAQ,CAAC,wDACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,wDACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,oEACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,wDACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,6DACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,+DACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,wDACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,sEACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,gEACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,0CACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,2CACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,sCACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,2BACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,sEACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,sEACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,gEAAgE,GACvF;YACA,OAAO;QACT;QAEA,oDAAoD;QACpD,IACE,UAAU,SACV,MAAM,IAAI,KAAK,aACf,aAAa,SACb,MAAM,OAAO,IACb,OAAO,MAAM,OAAO,KAAK,YACzB,SAAS,MAAM,OAAO,IACtB,OAAO,MAAM,OAAO,CAAC,GAAG,KAAK,YAC7B,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CACzB,2EAEA,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CACxB,wEACD,GACH;YACA,OAAO;QACT;QAEA,oDAAoD;QACpD,IACE,UAAU,SACV,MAAM,IAAI,KAAK,aACf,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,sBACvB,CAAC,MAAM,OAAO,CAAC,QAAQ,CACrB,2EAEA,MAAM,OAAO,CAAC,QAAQ,CACpB,wEACD,GACH;YACA,OAAO;QACT;QAEA,kCAAkC;QAClC,IACE,AAAC,OAAO,MAAM,OAAO,KAAK,YACxB,MAAM,OAAO,CAAC,QAAQ,CAAC,qCACxB,aAAa,SACZ,MAAM,OAAO,IACb,OAAO,MAAM,OAAO,KAAK,YACzB,SAAS,MAAM,OAAO,IACtB,OAAO,MAAM,OAAO,CAAC,GAAG,KAAK,YAC7B,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,mCAC7B;YACA,OAAO;QACT;QAEA,+DAA+D;QAC/D,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,mDACvB;YACA,OAAO;QACT;QAEA,uDAAuD;QACvD,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,mCACvB;YACA,OAAO;QACT;QAEA,mDAAmD;QACnD,IACE,UAAU,SACV,MAAM,IAAI,KAAK,aACf,aAAa,SACb,MAAM,OAAO,IACb,OAAO,MAAM,OAAO,KAAK,YACzB,SAAS,MAAM,OAAO,IACtB,OAAO,MAAM,OAAO,CAAC,GAAG,KAAK,YAC7B,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,YAC3B;YACA,OAAO;QACT;QAEA,yEAAyE;QACzE,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,8BACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,gBAAgB,KACzC,MAAM,OAAO,CAAC,QAAQ,CAAC,WACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,MACvB;YACA,OAAO;QACT;QAEA,qCAAqC;QACrC,IACE,OAAO,MAAM,OAAO,KAAK,YACzB,CAAC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YACpC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,aACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,GAC/C;YACA,OAAO;QACT;QAEA,OAAO;IACT;AACF;AAOO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,EAAE,OAAO,EAAE;IAExC,oCAAoC;IACpC,MAAM,iBAAiB,sBAAsB;IAE7C,qCAAqC;IACrC,MAAM,WAAW,IAAI;IAErB,eAAe,OAAO,CAAC,CAAC;QACtB,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QAEzC,wCAAwC;QACxC,IACE,UAAU,SACV,MAAM,IAAI,KAAK,aACf,aAAa,SACb,MAAM,OAAO,IACb,OAAO,MAAM,OAAO,KAAK,YACzB,SAAS,MAAM,OAAO,IACtB,OAAO,MAAM,OAAO,CAAC,GAAG,KAAK,YAC7B,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,aAC1B,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,eAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,GACrC;YACA;QACF;QAEA,IACE,UAAU,SACV,MAAM,IAAI,KAAK,aACf,aAAa,SACb,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,0BACvB;YACA;QACF;QAEA,kEAAkE;QAClE,IACE,aAAa,SACb,OAAO,MAAM,OAAO,KAAK,YACzB,MAAM,OAAO,CAAC,QAAQ,CAAC,0BACvB;YACA,yDAAyD;YACzD,MAAM,oBAAoB,MAAM,OAAO,CAAC,KAAK,CAAC;YAC9C,MAAM,eAAe,oBAAoB,iBAAiB,CAAC,EAAE,GAAG;YAEhE,0CAA0C;YAC1C,IAAI,iBAAiB,SAAS;gBAC5B,MAAM,gBAAgB,MAAM,OAAO,CAAC,KAAK,CAAC;gBAC1C,IAAI,iBAAiB,aAAa,CAAC,EAAE,EAAE;oBACrC,MAAM,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,EAAE;oBAE3C,IAAI,CAAC,SAAS,GAAG,CAAC,MAAM;wBACtB,SAAS,GAAG,CAAC,KAAK;4BAChB,GAAG,KAAK;4BACR,IACE,QAAQ,SAAS,MAAM,EAAE,GACrB,MAAM,EAAE,GACR,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;4BACtE,mBAAmB,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAE,EAAE;wBAC1D;oBACF;oBACA;gBACF;YACF;YAEA,0CAA0C;YAC1C,MAAM,MAAM,CAAC,cAAc,EAAE,cAAc;YAE3C,wCAAwC;YACxC,IAAI,CAAC,SAAS,GAAG,CAAC,MAAM;gBACtB,SAAS,GAAG,CAAC,KAAK;oBAChB,GAAG,KAAK;oBACR,IACE,QAAQ,SAAS,MAAM,EAAE,GACrB,MAAM,EAAE,GACR,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACtE,mBAAmB,CAAC,uBAAuB,EAAE,aAAa,UAAU,CAAC;gBACvE;YACF;QACF,OAAO;YACL,iEAAiE;YACjE,MAAM,mBACJ,aAAa,SAAS,OAAO,MAAM,OAAO,KAAK,WAC3C,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAC5B;YACN,MAAM,YAAY,UAAU,SAAS,MAAM,IAAI,GAAG,OAAO,MAAM,IAAI,IAAI;YACvE,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,kBAAkB;YAE9C,+CAA+C;YAC/C,IAAI,CAAC,SAAS,GAAG,CAAC,MAAM;gBACtB,SAAS,GAAG,CAAC,KAAK;oBAChB,GAAG,KAAK;oBACR,IACE,QAAQ,SAAS,MAAM,EAAE,GACrB,MAAM,EAAE,GACR,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACxE;YACF;QACF;IACF;IAEA,mCAAmC;IACnC,OAAO,MAAM,IAAI,CAAC,SAAS,MAAM;AACnC", "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/app/%28main%29/project/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { LazyModal } from \"@/components/ui/modal\";\r\nimport BannedUser from \"@/features/app/banned-user\";\r\nimport { ZendeskScript } from \"@/features/app/zendesk-help-button\";\r\nimport DesktopView from \"@/features/project/desktop-view\";\r\nimport MobileView from \"@/features/project/mobile-view\";\r\nimport MobileHeader from \"@/features/project/mobile/header\";\r\nimport NoTokenModal from \"@/features/project/modal/no-token-modal\";\r\nimport RestoreSupabasePausedProject from \"@/features/project/modal/restore-supabase-paused-project\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useCurrentThreadStore } from \"@/stores/current-thread\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport { useSettingsStore } from \"@/stores/settings-tab\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { lazy, use, useEffect, useState } from \"react\";\r\n\r\nconst SettingsModalContent = lazy(() => import(\"@/features/project/project-settings-modal\"));\r\nconst RemixSuccessModal = lazy(() => import(\"@/features/project/remix-success-modal\"));\r\n\r\ntype Props = {\r\n  params: Promise<{ id: string }>;\r\n};\r\n\r\nconst ProjectPage = ({ params }: Props) => {\r\n  const { id } = use(params);\r\n  const resetNavigateFileStore = useNavigateFile((state) => state.reset);\r\n  const resetSettingsStore = useSettingsStore((state) => state.reset);\r\n  const setCurrentThreadId = useCurrentThreadStore((state) => state.setId);\r\n  const searchParams = useSearchParams();\r\n  const isRemix = searchParams.get(\"remix\") === \"true\";\r\n  const [isRemixSuccessModalOpen, setIsRemixSuccessModalOpen] = useState(isRemix);\r\n\r\n  const isMobile = useIsMobile();\r\n  const { selectFirstThread } = useThread();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n\r\n  const closeModal = useSettingsStore((state) => state.closeModal);\r\n  const open = useSettingsStore((state) => state.settingsTab !== null);\r\n  const handleClose = useSettingsStore((state) => state.closeModal);\r\n\r\n  // reset stores on project change and on unmount\r\n  useEffect(() => {\r\n    resetNavigateFileStore();\r\n    setCurrentThreadId(null);\r\n    resetSettingsStore();\r\n    return () => {\r\n      resetNavigateFileStore();\r\n      setCurrentThreadId(null);\r\n      resetSettingsStore();\r\n    };\r\n  }, [id, resetNavigateFileStore, setCurrentThreadId, resetSettingsStore]);\r\n\r\n  const handleStartBuilding = () => {\r\n    const threadId = selectFirstThread();\r\n    if (threadId) {\r\n      setCurrentThreadId(threadId);\r\n      setIsRemixSuccessModalOpen(false);\r\n      const newSearchParams = new URLSearchParams(Array.from(searchParams.entries()));\r\n      newSearchParams.delete(\"remix\");\r\n      const base = `/project/${id}`;\r\n      const query = newSearchParams.toString();\r\n      const url = query ? `${base}?${query}` : base;\r\n      router.replace(url);\r\n    }\r\n  };\r\n\r\n  if (user?.userFromDb?.plan === \"banned\") {\r\n    return <BannedUser />;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {isMobile && <MobileHeader />}\r\n\r\n      {user?.userFromDb?.plan && user.userFromDb.plan !== \"free-tier\" && (\r\n        <ZendeskScript project={true} />\r\n      )}\r\n\r\n      <div className=\"flex flex-1 flex-col gap-4\">\r\n        <div className=\"flex-1 md:min-h-min\">\r\n          <div\r\n            className=\"z-20 flex flex-1 flex-col gap-4 bg-background md:h-screen\"\r\n            style={{\r\n              scrollBehavior: \"smooth\",\r\n              scrollbarWidth: \"none\",\r\n            }}\r\n          >\r\n            <div className=\"flex flex-1 bg-background\">\r\n              <div className=\"mx-auto flex w-full flex-1 flex-col md:h-screen\">\r\n                {isMobile ? <MobileView projectId={id} /> : <DesktopView projectId={id} />}\r\n              </div>\r\n\r\n              <NoTokenModal />\r\n\r\n              <LazyModal open={open} onOpenChange={handleClose}>\r\n                <SettingsModalContent setShowModal={closeModal} />\r\n              </LazyModal>\r\n\r\n              {isRemix && (\r\n                <RemixSuccessModal\r\n                  open={isRemixSuccessModalOpen}\r\n                  onOpenChange={setIsRemixSuccessModalOpen}\r\n                  onStartBuilding={handleStartBuilding}\r\n                />\r\n              )}\r\n            </div>\r\n          </div>\r\n          <RestoreSupabasePausedProject />\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ProjectPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAjBA;;;;;;;;;;;;;;;;;;AAmBA,MAAM,qCAAuB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AAClC,MAAM,kCAAoB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AAM/B,MAAM,cAAc,CAAC,EAAE,MAAM,EAAS;IACpC,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,oTAAA,CAAA,MAAG,AAAD,EAAE;IACnB,MAAM,yBAAyB,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK;IACrE,MAAM,qBAAqB,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK;IAClE,MAAM,qBAAqB,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK;IACvE,MAAM,eAAe,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,UAAU,aAAa,GAAG,CAAC,aAAa;IAC9C,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IACtC,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU;IAC/D,MAAM,OAAO,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,WAAW,KAAK;IAC/D,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU;IAEhE,gDAAgD;IAChD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,mBAAmB;QACnB;QACA,OAAO;YACL;YACA,mBAAmB;YACnB;QACF;IACF,GAAG;QAAC;QAAI;QAAwB;QAAoB;KAAmB;IAEvE,MAAM,sBAAsB;QAC1B,MAAM,WAAW;QACjB,IAAI,UAAU;YACZ,mBAAmB;YACnB,2BAA2B;YAC3B,MAAM,kBAAkB,IAAI,gBAAgB,MAAM,IAAI,CAAC,aAAa,OAAO;YAC3E,gBAAgB,MAAM,CAAC;YACvB,MAAM,OAAO,CAAC,SAAS,EAAE,IAAI;YAC7B,MAAM,QAAQ,gBAAgB,QAAQ;YACtC,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,OAAO,GAAG;YACzC,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,IAAI,MAAM,YAAY,SAAS,UAAU;QACvC,qBAAO,6VAAC,yIAAA,CAAA,UAAU;;;;;IACpB;IAEA,qBACE;;YACG,0BAAY,6VAAC,+IAAA,CAAA,UAAY;;;;;YAEzB,MAAM,YAAY,QAAQ,KAAK,UAAU,CAAC,IAAI,KAAK,6BAClD,6VAAC,oJAAA,CAAA,gBAAa;gBAAC,SAAS;;;;;;0BAG1B,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BACC,WAAU;4BACV,OAAO;gCACL,gBAAgB;gCAChB,gBAAgB;4BAClB;sCAEA,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;kDACZ,yBAAW,6VAAC,6IAAA,CAAA,UAAU;4CAAC,WAAW;;;;;iEAAS,6VAAC,8IAAA,CAAA,UAAW;4CAAC,WAAW;;;;;;;;;;;kDAGtE,6VAAC,4JAAA,CAAA,UAAY;;;;;kDAEb,6VAAC,iIAAA,CAAA,YAAS;wCAAC,MAAM;wCAAM,cAAc;kDACnC,cAAA,6VAAC;4CAAqB,cAAc;;;;;;;;;;;oCAGrC,yBACC,6VAAC;wCACC,MAAM;wCACN,cAAc;wCACd,iBAAiB;;;;;;;;;;;;;;;;;sCAKzB,6VAAC,gLAAA,CAAA,UAA4B;;;;;;;;;;;;;;;;;;AAKvC;uCAEe", "debugId": null}}]}