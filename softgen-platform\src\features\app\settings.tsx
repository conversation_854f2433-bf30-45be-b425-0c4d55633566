"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalTitle } from "@/components/ui/modal";
import SettingsBilling from "@/features/project/settings/billing";
import SettingsPurchase from "@/features/project/settings/purchase";
import SoftgenIcon from "@/features/thread/thread-message/softgen-icon";
import { useAuth } from "@/providers/auth-provider";
import { CreditCardSolid, UserSolid } from "@mynaui/icons-react";
import Link from "next/link";
import { useMemo, useState } from "react";
import { FaMoneyBill } from "react-icons/fa";
import SettingsProfile from "./modal/profile";

type SettingsTab = "billing" | "purchase" | "profile";

interface SettingsModalProps {
  tab?: SettingsTab;
}

export default function SettingsModalContent({ tab }: SettingsModalProps) {
  const { user } = useAuth();
  const isFreeTier = useMemo(() => user?.userFromDb?.plan === "free-tier", [user]);

  const [activeTab, setActiveTab] = useState<SettingsTab>(tab ?? "profile");

  const handleTabChange = (tab: SettingsTab) => {
    setActiveTab(tab);
  };

  const tabs = useMemo(() => {
    const tabs = [
      { id: "profile", label: "Profile", icon: <UserSolid />, disabled: false },
      { id: "billing", label: "Billing", icon: <CreditCardSolid />, disabled: isFreeTier },
    ];
    if (!user?.wholesalePlan) {
      tabs.push({ id: "purchase", label: "Purchase", icon: <FaMoneyBill />, disabled: false });
    }
    return tabs as { id: SettingsTab; label: string; icon: React.ReactNode; disabled?: boolean }[];
  }, [isFreeTier, user]);

  return (
    <ModalContentInner className="w-full lg:max-w-4xl">
      <div className="grid h-[600px] grid-cols-12 overflow-hidden">
        <div className="col-span-12 hidden flex-col border-r border-ring/15 bg-sidebar-ring/10 px-2 lg:col-span-3 lg:flex">
          <div className="flex flex-col gap-4 space-y-2 px-2 py-5 text-left">
            <SoftgenIcon />
          </div>

          <div className="flex flex-1 gap-1 md:flex-col">
            {tabs
              .sort((a, b) => (a.disabled ? 1 : b.disabled ? -1 : 0))
              .map((tab) => (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? "default" : "ghost"}
                  className="h-fit w-full justify-between rounded-md px-3 py-2"
                  onClick={() => {
                    handleTabChange(tab.id);
                  }}
                  disabled={tab.disabled}
                >
                  <div className="flex items-center gap-2">
                    {tab.icon}
                    {tab.label}
                  </div>
                  {tab.disabled && (
                    <Badge variant="terminal" className="px-2 py-0.5">
                      Pro
                    </Badge>
                  )}
                </Button>
              ))}
          </div>

          <ModalFooter className="-m-2 mb-0 flex flex-col items-start justify-start rounded-none border-t-0 bg-transparent py-0 dark:bg-transparent sm:flex-col sm:justify-start md:px-0 md:pr-3">
            {isFreeTier && (
              <Button className="relative my-3 mb-2 h-10 w-full text-sm">
                <Link href="/pricing" target="_blank">
                  Upgrade Now
                </Link>
              </Button>
            )}
          </ModalFooter>
        </div>

        <div className="col-span-12 hidden lg:col-span-9 lg:block">
          <ModalHeader>
            <ModalTitle>
              {tabs.find((tab) => tab.id === activeTab)?.label ||
                activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </ModalTitle>
          </ModalHeader>

          <div className="p-4">
            {activeTab === "billing" && <SettingsBilling />}
            {activeTab === "purchase" && <SettingsPurchase />}
            {activeTab === "profile" && <SettingsProfile />}
          </div>
        </div>

        <div className="col-span-12 w-full lg:hidden">
          <ModalHeader>
            <ModalTitle>
              {tabs.find((tab) => tab.id === activeTab)?.label ||
                activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </ModalTitle>
          </ModalHeader>

          <div className="flex grow flex-row flex-wrap gap-2 p-4 md:p-2">
            {tabs.map((tab) => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? "default" : "ghost"}
                className="h-fit w-fit justify-between rounded-md px-3 py-2"
                onClick={() => {
                  handleTabChange(tab.id);
                }}
              >
                <div className="flex items-center gap-2">
                  {tab.icon}
                  {tab.label}
                </div>
              </Button>
            ))}
          </div>

          <div className="p-4">
            {activeTab === "billing" && <SettingsBilling />}
            {activeTab === "purchase" && <SettingsPurchase />}
            {activeTab === "profile" && <SettingsProfile />}
          </div>
        </div>
      </div>
    </ModalContentInner>
  );
}
