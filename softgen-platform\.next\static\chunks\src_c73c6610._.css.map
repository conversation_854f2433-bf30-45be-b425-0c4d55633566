{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/font/geist_71a83cc0.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'geist';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/GeistVF.woff%22,%22preload%22:true,%22has_size_adjust%22:true}') format('woff');\n    font-display: swap;\n    font-weight: 100 200 300 400 500 600 700 800 900;\n}\n\n@font-face {\n    font-family: 'geist Fallback';\n    src: local(\"Arial\");\n    ascent-override: 85.83%;\ndescent-override: 20.52%;\nline-gap-override: 9.33%;\nsize-adjust: 107.19%;\n\n}\n\n.className {\n    font-family: 'geist', 'geist Fallback';\n    \n}\n.variable {\n    --font-geist-sans: 'geist', 'geist Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/font/geistmono_cb8291bf.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'geist<PERSON>ono';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/GeistMono.ttf%22,%22preload%22:true,%22has_size_adjust%22:true}') format('truetype');\n    font-display: swap;\n    font-weight: 100 200 300 400 500 600 700 800 900;\n}\n\n@font-face {\n    font-family: 'geistMono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 76.43%;\ndescent-override: 22.43%;\nline-gap-override: 0.00%;\nsize-adjust: 131.49%;\n\n}\n\n.className {\n    font-family: 'geistMono', 'geistMono Fallback';\n    \n}\n.variable {\n    --font-geist-mono: 'geistMono', 'geistMono Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/font/inter_f772a68f.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'inter';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/inter.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}') format('woff2');\n    font-display: swap;\n    font-weight: 100 200 300 400 500 600 700 800 900;\n}\n\n@font-face {\n    font-family: 'inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 89.79%;\ndescent-override: 22.36%;\nline-gap-override: 0.00%;\nsize-adjust: 107.89%;\n\n}\n\n.className {\n    font-family: 'inter', 'inter Fallback';\n    \n}\n.variable {\n    --font-inter: 'inter', 'inter Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/global/markdown.css"], "sourcesContent": [".markdown .contains-task-list {\r\n  list-style-type: none;\r\n  padding-left: 0px;\r\n}\r\n\r\n.markdown .task-list-item {\r\n  margin-top: 0.25rem;\r\n  margin-bottom: 0.25rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.markdown .task-list-item input[type=\"checkbox\"] {\r\n  height: 1rem;\r\n  width: 1rem;\r\n  border-radius: 0.25rem;\r\n  border-color: hsl(var(--primary) / 0.2);\r\n  color: hsl(var(--primary) / 0.8);\r\n}\r\n\r\n.markdown .task-list-item input[type=\"checkbox\"]:focus {\r\n  --tw-ring-color: hsl(var(--primary) / 0.2);\r\n  --tw-ring-offset-width: 0px;\r\n}\r\n\r\n.markdown .task-list-item input[type=\"checkbox\"] {\r\n  cursor: default;\r\n}\r\n\r\n.markdown .task-list-item input[type=\"checkbox\"]:checked {\r\n  border-color: hsl(var(--primary) / 0.8);\r\n  background-color: hsl(var(--primary) / 0.8);\r\n}\r\n\r\n.markdown h1 {\r\n  font-size: 2.25rem;\r\n  line-height: 2.5rem;\r\n  font-weight: 700;\r\n  letter-spacing: -0.025em;\r\n  color: hsl(var(--primary));\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.markdown h2 {\r\n  font-size: 1.875rem;\r\n  line-height: 2.25rem;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n  color: hsl(var(--primary));\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.markdown h3 {\r\n  font-size: 1.5rem;\r\n  line-height: 2rem;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n  color: hsl(var(--primary));\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.markdown h4 {\r\n  font-size: 1.25rem;\r\n  line-height: 1.75rem;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n  color: hsl(var(--primary));\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.markdown h5 {\r\n  font-size: 1.125rem;\r\n  line-height: 1.75rem;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n  color: hsl(var(--primary));\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.markdown h6 {\r\n  font-size: 1rem;\r\n  line-height: 1.5rem;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n  color: hsl(var(--primary));\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.markdown p {\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  line-height: 1.75rem;\r\n  color: hsl(var(--primary) / 0.8);\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.markdown ul {\r\n  margin-top: 1rem;\r\n  margin-bottom: 1rem;\r\n  list-style-type: disc;\r\n  padding-left: 1.5rem;\r\n  font-size: 1.125rem;\r\n  line-height: 1.75rem;\r\n}\r\n\r\n.markdown ol {\r\n  margin-top: 1rem;\r\n  margin-bottom: 1rem;\r\n  list-style-type: decimal;\r\n  padding-left: 1.5rem;\r\n  font-size: 1.125rem;\r\n  line-height: 1.75rem;\r\n}\r\n\r\n.markdown li {\r\n  margin-top: 0.5rem;\r\n  margin-bottom: 0.5rem;\r\n  font-size: 1rem;\r\n  line-height: 1.5rem;\r\n}\r\n\r\n.markdown a {\r\n  --tw-border-spacing-x: 0.5rem;\r\n  --tw-border-spacing-y: 0.5rem;\r\n  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);\r\n  border-bottom-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.8);\r\n  font-size: 1.1rem;\r\n  font-weight: 400;\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n\r\n.markdown a:hover {\r\n  border-color: hsl(var(--primary));\r\n}\r\n\r\n.markdown a {\r\n  color: hsl(var(--primary) / 0.8);\r\n}\r\n\r\n.markdown a:hover {\r\n  color: hsl(var(--primary));\r\n}\r\n\r\n.markdown blockquote {\r\n  display: flex;\r\n  border-radius: 0.38rem;\r\n  padding: 1rem;\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n  width: 100%;\r\n  padding-left: 2rem;\r\n  padding-right: 2rem;\r\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\r\n  --tw-gradient-from: hsl(var(--primary) / 0.05) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\r\n  overflow-x: auto;\r\n  border-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.1);\r\n}\r\n\r\n.markdown blockquote > p {\r\n  margin-bottom: 0px;\r\n  margin-top: 0px;\r\n}\r\n\r\n.markdown blockquote > ul {\r\n  list-style-type: none;\r\n  padding-left: 0px;\r\n}\r\n\r\n.markdown table {\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n  width: 100%;\r\n  border-collapse: separate;\r\n  --tw-border-spacing-x: 0px;\r\n  --tw-border-spacing-y: 0px;\r\n  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);\r\n  overflow: hidden;\r\n  border-radius: var(--radius);\r\n  border-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.2);\r\n}\r\n\r\nthead tr:first-child {\r\n  background-color: hsl(var(--primary) / 0.05);\r\n}\r\n\r\n.markdown th {\r\n  padding-left: 1rem;\r\n  padding-right: 1rem;\r\n  padding-top: 0.5rem;\r\n  padding-bottom: 0.5rem;\r\n  text-align: left;\r\n  font-weight: 600;\r\n  border-bottom-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.2);\r\n  color: hsl(var(--primary));\r\n}\r\n\r\n.markdown th:first-child {\r\n  border-top-left-radius: calc(var(--radius) + 4px);\r\n}\r\n\r\n.markdown th:last-child {\r\n  border-top-right-radius: calc(var(--radius) + 4px);\r\n}\r\n\r\n.markdown td {\r\n  border-bottom-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.1);\r\n  padding-left: 1rem;\r\n  padding-right: 1rem;\r\n  padding-top: 0.5rem;\r\n  padding-bottom: 0.5rem;\r\n  color: hsl(var(--primary) / 0.8);\r\n}\r\n\r\n.markdown tr:last-child td {\r\n  border-bottom-width: 0px;\r\n}\r\n\r\n.markdown tr:last-child td:first-child {\r\n  border-bottom-left-radius: calc(var(--radius) + 4px);\r\n}\r\n\r\n.markdown tr:last-child td:last-child {\r\n  border-bottom-right-radius: calc(var(--radius) + 4px);\r\n}\r\n\r\n:root {\r\n  --ctp-bg: #1e1e2e;\r\n  --ctp-text: #cdd6f4;\r\n  --ctp-comment: #6c7086;\r\n  --ctp-string: #a6e3a1;\r\n  --ctp-keyword: #cba6f7;\r\n  --ctp-function: #89b4fa;\r\n  --ctp-error: #f38ba8;\r\n  --ctp-warning: #fab387;\r\n  --ctp-info: #89dceb;\r\n  --ctp-success: #94e2d5;\r\n  --shiki-color-text: #414141;\r\n  --shiki-color-background: transparent;\r\n  --shiki-token-constant: #1976d2;\r\n  --shiki-token-string: #22863a;\r\n  --shiki-token-comment: #6a737d;\r\n  --shiki-token-keyword: #d32f2f;\r\n  --shiki-token-parameter: #ff9800;\r\n  --shiki-token-function: #6f42c1;\r\n  --shiki-token-string-expression: #22863a;\r\n  --shiki-token-punctuation: #24292e;\r\n  --shiki-token-link: #22863a;\r\n}\r\n\r\n[data-theme=\"dark\"] {\r\n  --shiki-color-text: #d1d1d1;\r\n  --shiki-token-constant: #79b8ff;\r\n  --shiki-token-string: #ffab70;\r\n  --shiki-token-comment: #6b737c;\r\n  --shiki-token-keyword: #f97583;\r\n  --shiki-token-parameter: #ff9800;\r\n  --shiki-token-function: #b392f0;\r\n  --shiki-token-string-expression: #ffab70;\r\n  --shiki-token-punctuation: #bbbbbb;\r\n  --shiki-token-link: #ffab70;\r\n}\r\n\r\n.markdown pre > code {\r\n  counter-reset: line;\r\n}\r\n\r\n.markdown code[data-line-numbers] {\r\n  counter-reset: line;\r\n  padding: 0;\r\n}\r\n\r\n.markdown code[data-line-numbers] > [data-line]::before {\r\n  /* counter-increment: line;\r\n    content: counter(line); */\r\n  margin-right: 1rem;\r\n  display: inline-block;\r\n  width: 1rem;\r\n  text-align: right;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\r\n}\r\n\r\n.markdown code > [data-line]::before {\r\n  /* counter-increment: line; */\r\n  /* content: counter(line); */\r\n  display: inline-block;\r\n  width: 1rem;\r\n  margin-right: 2rem;\r\n  text-align: right;\r\n  color: var(--ctp-text);\r\n}\r\n\r\n.markdown code[data-line-numbers-max-digits=\"2\"] > [data-line]::before {\r\n  width: 2rem;\r\n}\r\n\r\n.markdown code[data-line-numbers-max-digits=\"3\"] > [data-line]::before {\r\n  width: 3rem;\r\n}\r\n\r\n.markdown p code {\r\n  background-color: hsl(var(--primary) / 0.05);\r\n  border-radius: 0.38rem;\r\n  padding-left: 0.5rem;\r\n  padding-right: 0.5rem;\r\n  padding-top: 0.125rem;\r\n  padding-bottom: 0.125rem;\r\n  margin-left: 0.125rem;\r\n  border-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.1);\r\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\r\n}\r\n\r\n.markdown :where(pre):not(:where([class~=\"not-prose\"], [class~=\"not-prose\"] *)) {\r\n  color: var(--tw-prose-pre-code);\r\n  background-color: var(--ctp-bg);\r\n  overflow-x: auto;\r\n  font-weight: 400;\r\n  width: auto;\r\n  text-align: left;\r\n  font-size: 0.875em;\r\n  line-height: 1.7142857;\r\n  margin-top: 1.7142857em;\r\n  margin-bottom: 1.7142857em;\r\n  border-radius: 0.375rem;\r\n  padding: 1.1428571em;\r\n}\r\n\r\n:root {\r\n  --ctp-bg: #1e1e2e;\r\n  --ctp-text: #cdd6f4;\r\n  --ctp-comment: #6c7086;\r\n  --ctp-string: #a6e3a1;\r\n  --ctp-keyword: #cba6f7;\r\n  --ctp-function: #89b4fa;\r\n  --ctp-error: #f38ba8;\r\n  --ctp-warning: #fab387;\r\n  --ctp-info: #89dceb;\r\n  --ctp-success: #94e2d5;\r\n}\r\n\r\n.markdown code {\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n  line-height: 1.625;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n\r\n  .markdown code {\r\n    font-size: 1rem;\r\n    line-height: 1.5rem;\r\n  }\r\n}\r\n\r\n.markdown code {\r\n  border-radius: var(--radius);\r\n  padding-left: 0.5rem;\r\n  padding-right: 0.5rem;\r\n  padding-top: 0.125rem;\r\n  padding-bottom: 0.125rem;\r\n  background-color: hsl(var(--primary) / 0.05);\r\n  color: hsl(var(--primary));\r\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\r\n}\r\n\r\n.markdown pre {\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n  border-radius: calc(var(--radius) + 1px);\r\n  padding: 1rem;\r\n  background-color: hsl(var(--primary) / 0.05);\r\n  overflow-x: auto;\r\n  border-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.1);\r\n}\r\n\r\n.markdown pre code {\r\n  background-color: transparent;\r\n  padding: 0px;\r\n  display: block;\r\n  font-size: 0.85rem;\r\n}\r\n\r\n.markdown :where(pre):not(:where([class~=\"not-prose\"], [class~=\"not-prose\"] *)) {\r\n  background-color: hsl(var(--primary) / 0.05);\r\n  overflow-x: auto;\r\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\r\n  font-weight: 400;\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n\r\n  .markdown :where(pre):not(:where([class~=\"not-prose\"], [class~=\"not-prose\"] *)) {\r\n    font-size: 1rem;\r\n    line-height: 1.5rem;\r\n  }\r\n}\r\n\r\n.markdown :where(pre):not(:where([class~=\"not-prose\"], [class~=\"not-prose\"] *)) {\r\n  line-height: 1.625;\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n  border-radius: calc(var(--radius) + 1px);\r\n  padding: 1rem;\r\n}\r\n\r\n.markdown pre > code {\r\n  counter-reset: line;\r\n}\r\n\r\n.markdown code[data-line-numbers] {\r\n  counter-reset: line;\r\n  padding: 0px;\r\n}\r\n\r\n.markdown code[data-line-numbers] > [data-line]::before {\r\n  counter-increment: line;\r\n  content: counter(line);\r\n  margin-right: 1rem;\r\n  display: inline-block;\r\n  width: 1.5rem;\r\n  text-align: right;\r\n  color: hsl(var(--primary) / 0.4);\r\n}\r\n\r\n.markdown img {\r\n  height: auto;\r\n  max-width: 100%;\r\n  border-radius: 0.38rem;\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.markdown p img {\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  margin-bottom: 0.75rem;\r\n  border-radius: 0.38rem;\r\n  border-width: 1px;\r\n  object-fit: cover;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n\r\n  .markdown p img {\r\n    border-radius: calc(var(--radius) + 1px);\r\n  }\r\n}\r\n\r\n.markdown p em {\r\n  padding-right: 0.25rem;\r\n  font-size: 1rem;\r\n  line-height: 1.5rem;\r\n  font-weight: 300;\r\n  font-style: italic;\r\n  color: hsl(var(--primary) / 0.7);\r\n}\r\n\r\n.markdown hr {\r\n  margin-top: 2rem;\r\n  margin-bottom: 2rem;\r\n  border-top-width: 1px;\r\n  border-color: hsl(var(--primary) / 0.2);\r\n}\r\n\r\n.markdown .code-line {\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n\r\n.markdown .code-line:is(.dark *) {\r\n  color: hsl(var(--primary));\r\n}\r\n\r\n.markdown .scrollbar-w-2::-webkit-scrollbar {\r\n  width: 0.25rem;\r\n  height: 0.25rem;\r\n}\r\n\r\n.markdown .scrollbar-track-blue-lighter::-webkit-scrollbar-track {\r\n  background-color: #00000015;\r\n}\r\n\r\n.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb {\r\n  background-color: #13131374;\r\n}\r\n\r\n.markdown .scrollbar-thumb-rounded::-webkit-scrollbar-thumb {\r\n  border-radius: 7px;\r\n}\r\n\r\n.markdown .scrollbar-w-2::-webkit-scrollbar {\r\n  height: 0.375rem;\r\n  width: 0.375rem;\r\n}\r\n\r\n.markdown .scrollbar-track-blue-lighter::-webkit-scrollbar-track {\r\n  background-color: hsl(var(--primary) / 0.05);\r\n  border-radius: 9999px;\r\n}\r\n\r\n.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb {\r\n  background-color: hsl(var(--primary) / 0.2);\r\n  border-radius: 9999px;\r\n}\r\n\r\n.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb:hover {\r\n  background-color: hsl(var(--primary) / 0.3);\r\n}\r\n\r\n.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb {\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 200ms;\r\n  animation-duration: 200ms;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;AAmBA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA;;;;;;;;;;;;;AAsBA;;;;;;;;;AAWA;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;;;;AAaA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;AAaA;;;;;AAMA;EAEE;;;;;;AAMF;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;;;AASA;EAEE;;;;;;AAMF;;;;;;;;AAQA;;;;AAIA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;AASA;EAEE;;;;;AAKF;;;;;;;;;AASA;;;;;;;AAOA;;;;AAIA;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/app/globals.css"], "sourcesContent": ["@import \"@/features/global/markdown.css\";\r\n\r\n*, ::before, ::after{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\r\n\r\n::backdrop{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\r\n\r\n/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*/\r\n\r\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\r\n\r\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\r\n\r\n::before,\n::after {\n  --tw-content: '';\n}\r\n\r\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\r\n\r\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\r\n\r\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\r\n\r\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\r\n\r\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\r\n\r\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\r\n\r\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\r\n\r\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\r\n\r\n/*\nRemove the default font size and weight for headings.\n*/\r\n\r\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\r\n\r\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\r\n\r\na {\n  color: inherit;\n  text-decoration: inherit;\n}\r\n\r\n/*\nAdd the correct font weight in Edge and Safari.\n*/\r\n\r\nb,\nstrong {\n  font-weight: bolder;\n}\r\n\r\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\r\n\r\ncode,\nkbd,\nsamp,\npre {\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\r\n\r\n/*\nAdd the correct font size in all browsers.\n*/\r\n\r\nsmall {\n  font-size: 80%;\n}\r\n\r\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\r\n\r\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\r\n\r\nsub {\n  bottom: -0.25em;\n}\r\n\r\nsup {\n  top: -0.5em;\n}\r\n\r\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\r\n\r\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\r\n\r\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\r\n\r\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\r\n\r\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\r\n\r\nbutton,\nselect {\n  text-transform: none;\n}\r\n\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\r\n\r\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\r\n\r\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\r\n\r\n:-moz-focusring {\n  outline: auto;\n}\r\n\r\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\r\n\r\n:-moz-ui-invalid {\n  box-shadow: none;\n}\r\n\r\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\r\n\r\nprogress {\n  vertical-align: baseline;\n}\r\n\r\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\r\n\r\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\r\n\r\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\r\n\r\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\r\n\r\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\r\n\r\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\r\n\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\r\n\r\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\r\n\r\n/*\nAdd the correct display in Chrome and Safari.\n*/\r\n\r\nsummary {\n  display: list-item;\n}\r\n\r\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\r\n\r\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\r\n\r\nfieldset {\n  margin: 0;\n  padding: 0;\n}\r\n\r\nlegend {\n  padding: 0;\n}\r\n\r\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\r\n\r\n/*\nReset default styling for dialogs.\n*/\r\n\r\ndialog {\n  padding: 0;\n}\r\n\r\n/*\nPrevent resizing textareas horizontally by default.\n*/\r\n\r\ntextarea {\n  resize: vertical;\n}\r\n\r\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\r\n\r\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\r\n\r\n/*\nSet the default cursor for buttons.\n*/\r\n\r\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\r\n\r\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\r\n\r\n:disabled {\n  cursor: default;\n}\r\n\r\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\r\n\r\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\r\n\r\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\r\n\r\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\r\n\r\n/* Make elements with the HTML hidden attribute stay hidden by default */\r\n\r\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n\r\n:root{\n  --inherit: inherit;\n  --current: currentColor;\n  --transparent: transparent;\n  --black: #000;\n  --white: #fff;\n  --slate-50: #f8fafc;\n  --slate-100: #f1f5f9;\n  --slate-200: #e2e8f0;\n  --slate-300: #cbd5e1;\n  --slate-400: #94a3b8;\n  --slate-500: #64748b;\n  --slate-600: #475569;\n  --slate-700: #334155;\n  --slate-800: #1e293b;\n  --slate-900: #0f172a;\n  --slate-950: #020617;\n  --gray-50: #f9fafb;\n  --gray-100: #f3f4f6;\n  --gray-200: #e5e7eb;\n  --gray-300: #d1d5db;\n  --gray-400: #9ca3af;\n  --gray-500: #6b7280;\n  --gray-600: #4b5563;\n  --gray-700: #374151;\n  --gray-800: #1f2937;\n  --gray-900: #111827;\n  --gray-950: #030712;\n  --zinc-50: #fafafa;\n  --zinc-100: #f4f4f5;\n  --zinc-200: #e4e4e7;\n  --zinc-300: #d4d4d8;\n  --zinc-400: #a1a1aa;\n  --zinc-500: #71717a;\n  --zinc-600: #52525b;\n  --zinc-700: #3f3f46;\n  --zinc-800: #27272a;\n  --zinc-900: #18181b;\n  --zinc-950: #09090b;\n  --neutral-50: #fafafa;\n  --neutral-100: #f5f5f5;\n  --neutral-200: #e5e5e5;\n  --neutral-300: #d4d4d4;\n  --neutral-400: #a3a3a3;\n  --neutral-500: #737373;\n  --neutral-600: #525252;\n  --neutral-700: #404040;\n  --neutral-800: #262626;\n  --neutral-900: #171717;\n  --neutral-950: #0a0a0a;\n  --stone-50: #fafaf9;\n  --stone-100: #f5f5f4;\n  --stone-200: #e7e5e4;\n  --stone-300: #d6d3d1;\n  --stone-400: #a8a29e;\n  --stone-500: #78716c;\n  --stone-600: #57534e;\n  --stone-700: #44403c;\n  --stone-800: #292524;\n  --stone-900: #1c1917;\n  --stone-950: #0c0a09;\n  --red-50: #fef2f2;\n  --red-100: #fee2e2;\n  --red-200: #fecaca;\n  --red-300: #fca5a5;\n  --red-400: #f87171;\n  --red-500: #ef4444;\n  --red-600: #dc2626;\n  --red-700: #b91c1c;\n  --red-800: #991b1b;\n  --red-900: #7f1d1d;\n  --red-950: #450a0a;\n  --orange-50: #fff7ed;\n  --orange-100: #ffedd5;\n  --orange-200: #fed7aa;\n  --orange-300: #fdba74;\n  --orange-400: #fb923c;\n  --orange-500: #f97316;\n  --orange-600: #ea580c;\n  --orange-700: #c2410c;\n  --orange-800: #9a3412;\n  --orange-900: #7c2d12;\n  --orange-950: #431407;\n  --amber-50: #fffbeb;\n  --amber-100: #fef3c7;\n  --amber-200: #fde68a;\n  --amber-300: #fcd34d;\n  --amber-400: #fbbf24;\n  --amber-500: #f59e0b;\n  --amber-600: #d97706;\n  --amber-700: #b45309;\n  --amber-800: #92400e;\n  --amber-900: #78350f;\n  --amber-950: #451a03;\n  --yellow-50: #fefce8;\n  --yellow-100: #fef9c3;\n  --yellow-200: #fef08a;\n  --yellow-300: #fde047;\n  --yellow-400: #facc15;\n  --yellow-500: #eab308;\n  --yellow-600: #ca8a04;\n  --yellow-700: #a16207;\n  --yellow-800: #854d0e;\n  --yellow-900: #713f12;\n  --yellow-950: #422006;\n  --lime-50: #f7fee7;\n  --lime-100: #ecfccb;\n  --lime-200: #d9f99d;\n  --lime-300: #bef264;\n  --lime-400: #a3e635;\n  --lime-500: #84cc16;\n  --lime-600: #65a30d;\n  --lime-700: #4d7c0f;\n  --lime-800: #3f6212;\n  --lime-900: #365314;\n  --lime-950: #1a2e05;\n  --green-50: #f0fdf4;\n  --green-100: #dcfce7;\n  --green-200: #bbf7d0;\n  --green-300: #86efac;\n  --green-400: #4ade80;\n  --green-500: #22c55e;\n  --green-600: #16a34a;\n  --green-700: #15803d;\n  --green-800: #166534;\n  --green-900: #14532d;\n  --green-950: #052e16;\n  --emerald-50: #ecfdf5;\n  --emerald-100: #d1fae5;\n  --emerald-200: #a7f3d0;\n  --emerald-300: #6ee7b7;\n  --emerald-400: #34d399;\n  --emerald-500: #10b981;\n  --emerald-600: #059669;\n  --emerald-700: #047857;\n  --emerald-800: #065f46;\n  --emerald-900: #064e3b;\n  --emerald-950: #022c22;\n  --teal-50: #f0fdfa;\n  --teal-100: #ccfbf1;\n  --teal-200: #99f6e4;\n  --teal-300: #5eead4;\n  --teal-400: #2dd4bf;\n  --teal-500: #14b8a6;\n  --teal-600: #0d9488;\n  --teal-700: #0f766e;\n  --teal-800: #115e59;\n  --teal-900: #134e4a;\n  --teal-950: #042f2e;\n  --cyan-50: #ecfeff;\n  --cyan-100: #cffafe;\n  --cyan-200: #a5f3fc;\n  --cyan-300: #67e8f9;\n  --cyan-400: #22d3ee;\n  --cyan-500: #06b6d4;\n  --cyan-600: #0891b2;\n  --cyan-700: #0e7490;\n  --cyan-800: #155e75;\n  --cyan-900: #164e63;\n  --cyan-950: #083344;\n  --sky-50: #f0f9ff;\n  --sky-100: #e0f2fe;\n  --sky-200: #bae6fd;\n  --sky-300: #7dd3fc;\n  --sky-400: #38bdf8;\n  --sky-500: #0ea5e9;\n  --sky-600: #0284c7;\n  --sky-700: #0369a1;\n  --sky-800: #075985;\n  --sky-900: #0c4a6e;\n  --sky-950: #082f49;\n  --blue-50: #eff6ff;\n  --blue-100: #dbeafe;\n  --blue-200: #bfdbfe;\n  --blue-300: #93c5fd;\n  --blue-400: #60a5fa;\n  --blue-500: #3b82f6;\n  --blue-600: #2563eb;\n  --blue-700: #1d4ed8;\n  --blue-800: #1e40af;\n  --blue-900: #1e3a8a;\n  --blue-950: #172554;\n  --indigo-50: #eef2ff;\n  --indigo-100: #e0e7ff;\n  --indigo-200: #c7d2fe;\n  --indigo-300: #a5b4fc;\n  --indigo-400: #818cf8;\n  --indigo-500: #6366f1;\n  --indigo-600: #4f46e5;\n  --indigo-700: #4338ca;\n  --indigo-800: #3730a3;\n  --indigo-900: #312e81;\n  --indigo-950: #1e1b4b;\n  --violet-50: #f5f3ff;\n  --violet-100: #ede9fe;\n  --violet-200: #ddd6fe;\n  --violet-300: #c4b5fd;\n  --violet-400: #a78bfa;\n  --violet-500: #8b5cf6;\n  --violet-600: #7c3aed;\n  --violet-700: #6d28d9;\n  --violet-800: #5b21b6;\n  --violet-900: #4c1d95;\n  --violet-950: #2e1065;\n  --purple-50: #faf5ff;\n  --purple-100: #f3e8ff;\n  --purple-200: #e9d5ff;\n  --purple-300: #d8b4fe;\n  --purple-400: #c084fc;\n  --purple-500: #a855f7;\n  --purple-600: #9333ea;\n  --purple-700: #7e22ce;\n  --purple-800: #6b21a8;\n  --purple-900: #581c87;\n  --purple-950: #3b0764;\n  --fuchsia-50: #fdf4ff;\n  --fuchsia-100: #fae8ff;\n  --fuchsia-200: #f5d0fe;\n  --fuchsia-300: #f0abfc;\n  --fuchsia-400: #e879f9;\n  --fuchsia-500: #d946ef;\n  --fuchsia-600: #c026d3;\n  --fuchsia-700: #a21caf;\n  --fuchsia-800: #86198f;\n  --fuchsia-900: #701a75;\n  --fuchsia-950: #4a044e;\n  --pink-50: #fdf2f8;\n  --pink-100: #fce7f3;\n  --pink-200: #fbcfe8;\n  --pink-300: #f9a8d4;\n  --pink-400: #f472b6;\n  --pink-500: #ec4899;\n  --pink-600: #db2777;\n  --pink-700: #be185d;\n  --pink-800: #9d174d;\n  --pink-900: #831843;\n  --pink-950: #500724;\n  --rose-50: #fff1f2;\n  --rose-100: #ffe4e6;\n  --rose-200: #fecdd3;\n  --rose-300: #fda4af;\n  --rose-400: #fb7185;\n  --rose-500: #f43f5e;\n  --rose-600: #e11d48;\n  --rose-700: #be123c;\n  --rose-800: #9f1239;\n  --rose-900: #881337;\n  --rose-950: #4c0519;\n  --background: hsl(var(--background));\n  --foreground: hsl(var(--foreground));\n  --card: hsl(var(--card));\n  --card-foreground: hsl(var(--card-foreground));\n  --popover: hsl(var(--popover));\n  --popover-foreground: hsl(var(--popover-foreground));\n  --primary: hsl(var(--primary));\n  --primary-foreground: hsl(var(--primary-foreground));\n  --secondary: hsl(var(--secondary));\n  --secondary-foreground: hsl(var(--secondary-foreground));\n  --muted: hsl(var(--muted));\n  --muted-foreground: hsl(var(--muted-foreground));\n  --accent: hsl(var(--accent));\n  --accent-foreground: hsl(var(--accent-foreground));\n  --destructive: hsl(var(--destructive));\n  --destructive-foreground: hsl(var(--destructive-foreground));\n  --border: hsl(var(--border));\n  --input: hsl(var(--input));\n  --ring: hsl(var(--ring));\n  --chart-1: hsl(var(--chart-1));\n  --chart-2: hsl(var(--chart-2));\n  --chart-3: hsl(var(--chart-3));\n  --chart-4: hsl(var(--chart-4));\n  --chart-5: hsl(var(--chart-5));\n  --dark: hsl(var(--dark));\n  --sidebar: hsl(var(--sidebar-background));\n  --sidebar-foreground: hsl(var(--sidebar-foreground));\n  --sidebar-primary: hsl(var(--sidebar-primary));\n  --sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));\n  --sidebar-accent: hsl(var(--sidebar-accent));\n  --sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));\n  --sidebar-border: hsl(var(--sidebar-border));\n  --sidebar-ring: hsl(var(--sidebar-ring));\n  --color-1: hsl(var(--color-1));\n  --color-2: hsl(var(--color-2));\n  --color-3: hsl(var(--color-3));\n  --color-4: hsl(var(--color-4));\n  --color-5: hsl(var(--color-5));\n  --overlay: hsl(var(--overlay));\r\n    --background: 0 0% 98.04%;\r\n    --foreground: 0 0% 0%;\r\n    --card: 223.8136 -172.5242% 100%;\r\n    --card-foreground: 0 0% 0%;\r\n    --popover: 223.8136 0.0005% 98.6829%;\r\n    --popover-foreground: 0 0% 0%;\r\n    --primary: 0 0% 0%;\r\n    --primary-foreground: 223.8136 -172.5242% 100%;\r\n    --secondary: 223.8136 0.0001% 92.1478%;\r\n    --secondary-foreground: 0 0% 0%;\r\n    --muted: 223.8136 0.0002% 96.0587%;\r\n    --muted-foreground: 223.8136 0% 32.3067%;\r\n    --accent: 223.8136 0.0001% 92.1478%;\r\n    --accent-foreground: 0 0% 0%;\r\n    --destructive: 358.4334 74.912% 59.7455%;\r\n    --destructive-foreground: 223.8136 -172.5242% 100%;\r\n    --border: 223.8136 0.0001% 89.5577%;\r\n    --input: 223.8136 0.0001% 92.1478%;\r\n    --ring: 0 0% 0%;\r\n    --chart-1: 40.6655 100.2361% 50.9228%;\r\n    --chart-2: 223.749 85.9924% 55.8092%;\r\n    --chart-3: 223.8136 0% 64.471%;\r\n    --chart-4: 223.8136 0.0001% 89.5577%;\r\n    --chart-5: 223.8136 0% 45.6078%;\r\n    --sidebar: 223.8136 0.0005% 98.6829%;\r\n    --sidebar-foreground: 0 0% 0%;\r\n    --sidebar-primary: 0 0% 0%;\r\n    --sidebar-primary-foreground: 223.8136 -172.5242% 100%;\r\n    --sidebar-accent: 223.8136 0.0001% 92.1478%;\r\n    --sidebar-accent-foreground: 0 0% 0%;\r\n    --sidebar-border: 223.8136 0.0001% 92.1478%;\r\n    --sidebar-ring: 0 0% 0%;\r\n    --font-sans: Geist, sans-serif;\r\n    --font-serif: Georgia, serif;\r\n    --font-mono: Geist Mono, monospace;\r\n    --radius: 0.5rem;\r\n    --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);\r\n    --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);\r\n    --shadow-sm: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-md: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-lg: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);\r\n\r\n    --overlay: 240 11.11% 5.29%;\n}\r\n\r\n.dark {\r\n    --background: 240 10.53% 7.45%;\r\n    --foreground: 223.8136 -172.5242% 100%;\r\n    --card: 223.8136 0% 3.5452%;\r\n    --card-foreground: 223.8136 -172.5242% 100%;\r\n    --popover: 223.8136 0% 6.8692%;\r\n    --popover-foreground: 223.8136 -172.5242% 100%;\r\n    --primary: 223.8136 -172.5242% 100%;\r\n    --primary-foreground: 0 0% 0%;\r\n    --secondary: 223.8136 0% 13.1499%;\r\n    --secondary-foreground: 223.8136 -172.5242% 100%;\r\n    --muted: 223.8136 0% 11.304%;\r\n    --muted-foreground: 223.8136 0% 64.471%;\r\n    --accent: 223.8136 0% 19.8916%;\r\n    --accent-foreground: 223.8136 -172.5242% 100%;\r\n    --destructive: 359.9132 100.2494% 67.8807%;\r\n    --destructive-foreground: 0 0% 0%;\r\n    --border: 223.8136 0% 14.0871%;\r\n    --input: 223.8136 0% 19.8916%;\r\n    --ring: 223.8136 0% 64.471%;\r\n    --chart-1: 40.6655 100.2361% 50.9228%;\r\n    --chart-2: 218.1624 90.0354% 55.1618%;\r\n    --chart-3: 223.8136 0% 45.6078%;\r\n    --chart-4: 223.8136 0% 32.3067%;\r\n    --chart-5: 223.8136 0.0001% 89.5577%;\r\n    --sidebar: 223.8136 0% 6.8692%;\r\n    --sidebar-foreground: 223.8136 -172.5242% 100%;\r\n    --sidebar-primary: 223.8136 -172.5242% 100%;\r\n    --sidebar-primary-foreground: 0 0% 0%;\r\n    --sidebar-accent: 223.8136 0% 19.8916%;\r\n    --sidebar-accent-foreground: 223.8136 -172.5242% 100%;\r\n    --sidebar-border: 223.8136 0% 19.8916%;\r\n    --sidebar-ring: 223.8136 0% 64.471%;\r\n    --font-sans: Geist, sans-serif;\r\n    --font-serif: Georgia, serif;\r\n    --font-mono: Geist Mono, monospace;\r\n    --radius: 0.5rem;\r\n    --shadow-2xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);\r\n    --shadow-xs: 0px 1px 2px 0px hsl(0 0% 0% / 0.09);\r\n    --shadow-sm: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 1px 2px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-md: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 2px 4px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-lg: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 4px 6px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.18), 0px 8px 10px -1px hsl(0 0% 0% / 0.18);\r\n    --shadow-2xl: 0px 1px 2px 0px hsl(0 0% 0% / 0.45);\r\n  }\r\n\r\n.theme {\r\n    --animate-shine: shine var(--duration) infinite linear;\r\n  }\r\n\r\n*{\n  border-color: hsl(var(--border));\n  outline-color: hsl(var(--ring) / 0.5);\n}\r\n\r\nbody{\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n}\r\n.\\!container{\n  width: 100% !important;\n}\r\n.container{\n  width: 100%;\n}\r\n@media (min-width: 640px){\r\n\r\n  .\\!container{\n    max-width: 640px !important;\n  }\r\n\r\n  .container{\n    max-width: 640px;\n  }\n}\r\n@media (min-width: 768px){\r\n\r\n  .\\!container{\n    max-width: 768px !important;\n  }\r\n\r\n  .container{\n    max-width: 768px;\n  }\n}\r\n@media (min-width: 1024px){\r\n\r\n  .\\!container{\n    max-width: 1024px !important;\n  }\r\n\r\n  .container{\n    max-width: 1024px;\n  }\n}\r\n@media (min-width: 1280px){\r\n\r\n  .\\!container{\n    max-width: 1280px !important;\n  }\r\n\r\n  .container{\n    max-width: 1280px;\n  }\n}\r\n@media (min-width: 1536px){\r\n\r\n  .\\!container{\n    max-width: 1536px !important;\n  }\r\n\r\n  .container{\n    max-width: 1536px;\n  }\n}\r\n.dark .shiki,\r\n  .dark .shiki span {\r\n    color: var(--shiki-dark) !important;\r\n    background-color: var(--shiki-dark-bg) !important;\r\n    font-style: var(--shiki-dark-font-style) !important;\r\n    font-weight: var(--shiki-dark-font-weight) !important;\r\n    text-decoration: var(--shiki-dark-text-decoration) !important;\r\n  }\r\n.sr-only{\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\r\n.pointer-events-none{\n  pointer-events: none;\n}\r\n.pointer-events-auto{\n  pointer-events: auto;\n}\r\n.visible{\n  visibility: visible;\n}\r\n.static{\n  position: static;\n}\r\n.fixed{\n  position: fixed;\n}\r\n.absolute{\n  position: absolute;\n}\r\n.relative{\n  position: relative;\n}\r\n.sticky{\n  position: sticky;\n}\r\n.inset-0{\n  inset: 0px;\n}\r\n.inset-px{\n  inset: 1px;\n}\r\n.inset-x-0{\n  left: 0px;\n  right: 0px;\n}\r\n.inset-y-0{\n  top: 0px;\n  bottom: 0px;\n}\r\n.-bottom-20{\n  bottom: -5rem;\n}\r\n.-bottom-\\[105\\%\\]{\n  bottom: -105%;\n}\r\n.-left-\\[45\\%\\]{\n  left: -45%;\n}\r\n.-right-1\\.5{\n  right: -0.375rem;\n}\r\n.-right-2\\.5{\n  right: -0.625rem;\n}\r\n.-right-20{\n  right: -5rem;\n}\r\n.-right-\\[35\\%\\]{\n  right: -35%;\n}\r\n.-right-\\[45\\%\\]{\n  right: -45%;\n}\r\n.-top-1\\.5{\n  top: -0.375rem;\n}\r\n.-top-2\\.5{\n  top: -0.625rem;\n}\r\n.-top-24{\n  top: -6rem;\n}\r\n.-top-6{\n  top: -1.5rem;\n}\r\n.-top-\\[105\\%\\]{\n  top: -105%;\n}\r\n.bottom-0{\n  bottom: 0px;\n}\r\n.bottom-10{\n  bottom: 2.5rem;\n}\r\n.bottom-20{\n  bottom: 5rem;\n}\r\n.bottom-3{\n  bottom: 0.75rem;\n}\r\n.bottom-4{\n  bottom: 1rem;\n}\r\n.bottom-6{\n  bottom: 1.5rem;\n}\r\n.bottom-8{\n  bottom: 2rem;\n}\r\n.bottom-96{\n  bottom: 24rem;\n}\r\n.bottom-\\[15\\%\\]{\n  bottom: 15%;\n}\r\n.left-0{\n  left: 0px;\n}\r\n.left-1\\/2{\n  left: 50%;\n}\r\n.left-1\\/4{\n  left: 25%;\n}\r\n.left-2{\n  left: 0.5rem;\n}\r\n.left-3{\n  left: 0.75rem;\n}\r\n.left-8{\n  left: 2rem;\n}\r\n.left-\\[0\\%\\]{\n  left: 0%;\n}\r\n.left-\\[25\\%\\]{\n  left: 25%;\n}\r\n.left-\\[50\\%\\]{\n  left: 50%;\n}\r\n.right-0{\n  right: 0px;\n}\r\n.right-1\\/2{\n  right: 50%;\n}\r\n.right-10{\n  right: 2.5rem;\n}\r\n.right-2{\n  right: 0.5rem;\n}\r\n.right-3{\n  right: 0.75rem;\n}\r\n.right-4{\n  right: 1rem;\n}\r\n.top-0{\n  top: 0px;\n}\r\n.top-1\\/2{\n  top: 50%;\n}\r\n.top-2{\n  top: 0.5rem;\n}\r\n.top-3{\n  top: 0.75rem;\n}\r\n.top-4{\n  top: 1rem;\n}\r\n.top-6{\n  top: 1.5rem;\n}\r\n.top-\\[15\\%\\]{\n  top: 15%;\n}\r\n.top-\\[50\\%\\]{\n  top: 50%;\n}\r\n.top-\\[60\\%\\]{\n  top: 60%;\n}\r\n.top-\\[calc\\(1\\.5rem\\+0\\.125rem\\)\\]{\n  top: calc(1.5rem + 0.125rem);\n}\r\n.isolate{\n  isolation: isolate;\n}\r\n.-z-10{\n  z-index: -10;\n}\r\n.z-0{\n  z-index: 0;\n}\r\n.z-10{\n  z-index: 10;\n}\r\n.z-20{\n  z-index: 20;\n}\r\n.z-30{\n  z-index: 30;\n}\r\n.z-40{\n  z-index: 40;\n}\r\n.z-50{\n  z-index: 50;\n}\r\n.z-\\[100\\]{\n  z-index: 100;\n}\r\n.z-\\[1\\]{\n  z-index: 1;\n}\r\n.z-\\[200\\]{\n  z-index: 200;\n}\r\n.z-\\[299\\]{\n  z-index: 299;\n}\r\n.z-\\[50\\]{\n  z-index: 50;\n}\r\n.z-\\[60\\]{\n  z-index: 60;\n}\r\n.z-\\[999\\]{\n  z-index: 999;\n}\r\n.-order-1{\n  order: -1;\n}\r\n.order-1{\n  order: 1;\n}\r\n.order-2{\n  order: 2;\n}\r\n.order-3{\n  order: 3;\n}\r\n.col-span-1{\n  grid-column: span 1 / span 1;\n}\r\n.col-span-12{\n  grid-column: span 12 / span 12;\n}\r\n.col-span-2{\n  grid-column: span 2 / span 2;\n}\r\n.col-span-9{\n  grid-column: span 9 / span 9;\n}\r\n.row-span-1{\n  grid-row: span 1 / span 1;\n}\r\n.-m-2{\n  margin: -0.5rem;\n}\r\n.m-0{\n  margin: 0px;\n}\r\n.m-0\\.5{\n  margin: 0.125rem;\n}\r\n.m-1{\n  margin: 0.25rem;\n}\r\n.-mx-1{\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\r\n.-mx-2{\n  margin-left: -0.5rem;\n  margin-right: -0.5rem;\n}\r\n.-mx-6{\n  margin-left: -1.5rem;\n  margin-right: -1.5rem;\n}\r\n.-my-1\\.5{\n  margin-top: -0.375rem;\n  margin-bottom: -0.375rem;\n}\r\n.mx-0{\n  margin-left: 0px;\n  margin-right: 0px;\n}\r\n.mx-0\\.5{\n  margin-left: 0.125rem;\n  margin-right: 0.125rem;\n}\r\n.mx-1{\n  margin-left: 0.25rem;\n  margin-right: 0.25rem;\n}\r\n.mx-2{\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\r\n.mx-auto{\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.my-0{\n  margin-top: 0px;\n  margin-bottom: 0px;\n}\r\n.my-1{\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\r\n.my-1\\.5{\n  margin-top: 0.375rem;\n  margin-bottom: 0.375rem;\n}\r\n.my-10{\n  margin-top: 2.5rem;\n  margin-bottom: 2.5rem;\n}\r\n.my-12{\n  margin-top: 3rem;\n  margin-bottom: 3rem;\n}\r\n.my-16{\n  margin-top: 4rem;\n  margin-bottom: 4rem;\n}\r\n.my-2{\n  margin-top: 0.5rem;\n  margin-bottom: 0.5rem;\n}\r\n.my-2\\.5{\n  margin-top: 0.625rem;\n  margin-bottom: 0.625rem;\n}\r\n.my-24{\n  margin-top: 6rem;\n  margin-bottom: 6rem;\n}\r\n.my-3{\n  margin-top: 0.75rem;\n  margin-bottom: 0.75rem;\n}\r\n.my-4{\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\r\n.my-6{\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\r\n.my-7{\n  margin-top: 1.75rem;\n  margin-bottom: 1.75rem;\n}\r\n.my-8{\n  margin-top: 2rem;\n  margin-bottom: 2rem;\n}\r\n.-mr-1{\n  margin-right: -0.25rem;\n}\r\n.-mt-0\\.5{\n  margin-top: -0.125rem;\n}\r\n.-mt-4{\n  margin-top: -1rem;\n}\r\n.mb-0{\n  margin-bottom: 0px;\n}\r\n.mb-1{\n  margin-bottom: 0.25rem;\n}\r\n.mb-1\\.5{\n  margin-bottom: 0.375rem;\n}\r\n.mb-10{\n  margin-bottom: 2.5rem;\n}\r\n.mb-12{\n  margin-bottom: 3rem;\n}\r\n.mb-14{\n  margin-bottom: 3.5rem;\n}\r\n.mb-2{\n  margin-bottom: 0.5rem;\n}\r\n.mb-3{\n  margin-bottom: 0.75rem;\n}\r\n.mb-4{\n  margin-bottom: 1rem;\n}\r\n.mb-5{\n  margin-bottom: 1.25rem;\n}\r\n.mb-6{\n  margin-bottom: 1.5rem;\n}\r\n.mb-8{\n  margin-bottom: 2rem;\n}\r\n.mb-auto{\n  margin-bottom: auto;\n}\r\n.ml-0{\n  margin-left: 0px;\n}\r\n.ml-1{\n  margin-left: 0.25rem;\n}\r\n.ml-2{\n  margin-left: 0.5rem;\n}\r\n.ml-3{\n  margin-left: 0.75rem;\n}\r\n.ml-4{\n  margin-left: 1rem;\n}\r\n.ml-5{\n  margin-left: 1.25rem;\n}\r\n.ml-6{\n  margin-left: 1.5rem;\n}\r\n.ml-8{\n  margin-left: 2rem;\n}\r\n.ml-9{\n  margin-left: 2.25rem;\n}\r\n.ml-auto{\n  margin-left: auto;\n}\r\n.mr-0{\n  margin-right: 0px;\n}\r\n.mr-1{\n  margin-right: 0.25rem;\n}\r\n.mr-2{\n  margin-right: 0.5rem;\n}\r\n.mr-8{\n  margin-right: 2rem;\n}\r\n.mr-auto{\n  margin-right: auto;\n}\r\n.mt-0{\n  margin-top: 0px;\n}\r\n.mt-0\\.5{\n  margin-top: 0.125rem;\n}\r\n.mt-1{\n  margin-top: 0.25rem;\n}\r\n.mt-1\\.5{\n  margin-top: 0.375rem;\n}\r\n.mt-10{\n  margin-top: 2.5rem;\n}\r\n.mt-12{\n  margin-top: 3rem;\n}\r\n.mt-16{\n  margin-top: 4rem;\n}\r\n.mt-2{\n  margin-top: 0.5rem;\n}\r\n.mt-20{\n  margin-top: 5rem;\n}\r\n.mt-24{\n  margin-top: 6rem;\n}\r\n.mt-3{\n  margin-top: 0.75rem;\n}\r\n.mt-4{\n  margin-top: 1rem;\n}\r\n.mt-6{\n  margin-top: 1.5rem;\n}\r\n.mt-8{\n  margin-top: 2rem;\n}\r\n.mt-auto{\n  margin-top: auto;\n}\r\n.box-border{\n  box-sizing: border-box;\n}\r\n.line-clamp-1{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.line-clamp-2{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\r\n.line-clamp-5{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 5;\n}\r\n.block{\n  display: block;\n}\r\n.inline-block{\n  display: inline-block;\n}\r\n.inline{\n  display: inline;\n}\r\n.flex{\n  display: flex;\n}\r\n.inline-flex{\n  display: inline-flex;\n}\r\n.table{\n  display: table;\n}\r\n.grid{\n  display: grid;\n}\r\n.contents{\n  display: contents;\n}\r\n.list-item{\n  display: list-item;\n}\r\n.hidden{\n  display: none;\n}\r\n.aspect-square{\n  aspect-ratio: 1 / 1;\n}\r\n.aspect-video{\n  aspect-ratio: 16 / 9;\n}\r\n.\\!size-6{\n  width: 1.5rem !important;\n  height: 1.5rem !important;\n}\r\n.size-1{\n  width: 0.25rem;\n  height: 0.25rem;\n}\r\n.size-10{\n  width: 2.5rem;\n  height: 2.5rem;\n}\r\n.size-12{\n  width: 3rem;\n  height: 3rem;\n}\r\n.size-16{\n  width: 4rem;\n  height: 4rem;\n}\r\n.size-2{\n  width: 0.5rem;\n  height: 0.5rem;\n}\r\n.size-2\\.5{\n  width: 0.625rem;\n  height: 0.625rem;\n}\r\n.size-3{\n  width: 0.75rem;\n  height: 0.75rem;\n}\r\n.size-3\\.5{\n  width: 0.875rem;\n  height: 0.875rem;\n}\r\n.size-4{\n  width: 1rem;\n  height: 1rem;\n}\r\n.size-5{\n  width: 1.25rem;\n  height: 1.25rem;\n}\r\n.size-6{\n  width: 1.5rem;\n  height: 1.5rem;\n}\r\n.size-7{\n  width: 1.75rem;\n  height: 1.75rem;\n}\r\n.size-8{\n  width: 2rem;\n  height: 2rem;\n}\r\n.size-9{\n  width: 2.25rem;\n  height: 2.25rem;\n}\r\n.size-\\[1\\.1rem\\]{\n  width: 1.1rem;\n  height: 1.1rem;\n}\r\n.size-\\[var\\(--icon-size\\)\\]{\n  width: var(--icon-size);\n  height: var(--icon-size);\n}\r\n.size-fit{\n  width: fit-content;\n  height: fit-content;\n}\r\n.size-full{\n  width: 100%;\n  height: 100%;\n}\r\n.h-0{\n  height: 0px;\n}\r\n.h-0\\.5{\n  height: 0.125rem;\n}\r\n.h-1{\n  height: 0.25rem;\n}\r\n.h-1\\.5{\n  height: 0.375rem;\n}\r\n.h-1\\/4{\n  height: 25%;\n}\r\n.h-10{\n  height: 2.5rem;\n}\r\n.h-11{\n  height: 2.75rem;\n}\r\n.h-12{\n  height: 3rem;\n}\r\n.h-14{\n  height: 3.5rem;\n}\r\n.h-16{\n  height: 4rem;\n}\r\n.h-2{\n  height: 0.5rem;\n}\r\n.h-2\\.5{\n  height: 0.625rem;\n}\r\n.h-20{\n  height: 5rem;\n}\r\n.h-24{\n  height: 6rem;\n}\r\n.h-28{\n  height: 7rem;\n}\r\n.h-3{\n  height: 0.75rem;\n}\r\n.h-3\\.5{\n  height: 0.875rem;\n}\r\n.h-4{\n  height: 1rem;\n}\r\n.h-5{\n  height: 1.25rem;\n}\r\n.h-6{\n  height: 1.5rem;\n}\r\n.h-64{\n  height: 16rem;\n}\r\n.h-7{\n  height: 1.75rem;\n}\r\n.h-8{\n  height: 2rem;\n}\r\n.h-9{\n  height: 2.25rem;\n}\r\n.h-\\[1\\.7rem\\]{\n  height: 1.7rem;\n}\r\n.h-\\[100\\%\\]{\n  height: 100%;\n}\r\n.h-\\[120px\\]{\n  height: 120px;\n}\r\n.h-\\[140\\%\\]{\n  height: 140%;\n}\r\n.h-\\[169\\%\\]{\n  height: 169%;\n}\r\n.h-\\[1px\\]{\n  height: 1px;\n}\r\n.h-\\[200\\%\\]{\n  height: 200%;\n}\r\n.h-\\[200px\\]{\n  height: 200px;\n}\r\n.h-\\[20rem\\]{\n  height: 20rem;\n}\r\n.h-\\[240px\\]{\n  height: 240px;\n}\r\n.h-\\[25rem\\]{\n  height: 25rem;\n}\r\n.h-\\[30px\\]{\n  height: 30px;\n}\r\n.h-\\[30rem\\]{\n  height: 30rem;\n}\r\n.h-\\[32rem\\]{\n  height: 32rem;\n}\r\n.h-\\[350px\\]{\n  height: 350px;\n}\r\n.h-\\[38px\\]{\n  height: 38px;\n}\r\n.h-\\[400px\\]{\n  height: 400px;\n}\r\n.h-\\[40rem\\]{\n  height: 40rem;\n}\r\n.h-\\[45rem\\]{\n  height: 45rem;\n}\r\n.h-\\[50\\%\\]{\n  height: 50%;\n}\r\n.h-\\[500px\\]{\n  height: 500px;\n}\r\n.h-\\[60\\%\\]{\n  height: 60%;\n}\r\n.h-\\[600px\\]{\n  height: 600px;\n}\r\n.h-\\[70px\\]{\n  height: 70px;\n}\r\n.h-\\[800px\\]{\n  height: 800px;\n}\r\n.h-\\[85vh\\]{\n  height: 85vh;\n}\r\n.h-\\[90vh\\]{\n  height: 90vh;\n}\r\n.h-\\[93\\%\\]{\n  height: 93%;\n}\r\n.h-\\[calc\\(100vh-10rem\\)\\]{\n  height: calc(100vh - 10rem);\n}\r\n.h-\\[calc\\(100vh-110px\\)\\]{\n  height: calc(100vh - 110px);\n}\r\n.h-\\[calc\\(100vh-120px\\)\\]{\n  height: calc(100vh - 120px);\n}\r\n.h-\\[calc\\(100vh-40px\\)\\]{\n  height: calc(100vh - 40px);\n}\r\n.h-\\[calc\\(100vh-5\\.9rem\\)\\]{\n  height: calc(100vh - 5.9rem);\n}\r\n.h-\\[calc\\(100vh-55px\\)\\]{\n  height: calc(100vh - 55px);\n}\r\n.h-\\[calc\\(100vh-90px\\)\\]{\n  height: calc(100vh - 90px);\n}\r\n.h-\\[var\\(--radix-select-trigger-height\\)\\]{\n  height: var(--radix-select-trigger-height);\n}\r\n.h-auto{\n  height: auto;\n}\r\n.h-fit{\n  height: fit-content;\n}\r\n.h-full{\n  height: 100%;\n}\r\n.h-px{\n  height: 1px;\n}\r\n.h-screen{\n  height: 100vh;\n}\r\n.max-h-32{\n  max-height: 8rem;\n}\r\n.max-h-40{\n  max-height: 10rem;\n}\r\n.max-h-52{\n  max-height: 13rem;\n}\r\n.max-h-60{\n  max-height: 15rem;\n}\r\n.max-h-64{\n  max-height: 16rem;\n}\r\n.max-h-80{\n  max-height: 20rem;\n}\r\n.max-h-96{\n  max-height: 24rem;\n}\r\n.max-h-\\[160px\\]{\n  max-height: 160px;\n}\r\n.max-h-\\[200px\\]{\n  max-height: 200px;\n}\r\n.max-h-\\[300px\\]{\n  max-height: 300px;\n}\r\n.max-h-\\[370px\\]{\n  max-height: 370px;\n}\r\n.max-h-\\[500px\\]{\n  max-height: 500px;\n}\r\n.max-h-\\[531px\\]{\n  max-height: 531px;\n}\r\n.max-h-\\[565px\\]{\n  max-height: 565px;\n}\r\n.max-h-\\[70vh\\]{\n  max-height: 70vh;\n}\r\n.max-h-\\[80vh\\]{\n  max-height: 80vh;\n}\r\n.max-h-\\[85vh\\]{\n  max-height: 85vh;\n}\r\n.max-h-\\[90\\%\\]{\n  max-height: 90%;\n}\r\n.max-h-\\[90vh\\]{\n  max-height: 90vh;\n}\r\n.max-h-\\[95\\%\\]{\n  max-height: 95%;\n}\r\n.min-h-11{\n  min-height: 2.75rem;\n}\r\n.min-h-12{\n  min-height: 3rem;\n}\r\n.min-h-40{\n  min-height: 10rem;\n}\r\n.min-h-\\[100px\\]{\n  min-height: 100px;\n}\r\n.min-h-\\[300px\\]{\n  min-height: 300px;\n}\r\n.min-h-\\[350px\\]{\n  min-height: 350px;\n}\r\n.min-h-\\[400px\\]{\n  min-height: 400px;\n}\r\n.min-h-\\[44px\\]{\n  min-height: 44px;\n}\r\n.min-h-\\[60px\\]{\n  min-height: 60px;\n}\r\n.min-h-\\[80px\\]{\n  min-height: 80px;\n}\r\n.min-h-\\[95\\%\\]{\n  min-height: 95%;\n}\r\n.min-h-fit{\n  min-height: fit-content;\n}\r\n.min-h-screen{\n  min-height: 100vh;\n}\r\n.w-0{\n  width: 0px;\n}\r\n.w-0\\.5{\n  width: 0.125rem;\n}\r\n.w-1{\n  width: 0.25rem;\n}\r\n.w-1\\.5{\n  width: 0.375rem;\n}\r\n.w-1\\/2{\n  width: 50%;\n}\r\n.w-10{\n  width: 2.5rem;\n}\r\n.w-11{\n  width: 2.75rem;\n}\r\n.w-12{\n  width: 3rem;\n}\r\n.w-14{\n  width: 3.5rem;\n}\r\n.w-16{\n  width: 4rem;\n}\r\n.w-2{\n  width: 0.5rem;\n}\r\n.w-2\\.5{\n  width: 0.625rem;\n}\r\n.w-2\\/3{\n  width: 66.666667%;\n}\r\n.w-20{\n  width: 5rem;\n}\r\n.w-24{\n  width: 6rem;\n}\r\n.w-28{\n  width: 7rem;\n}\r\n.w-3{\n  width: 0.75rem;\n}\r\n.w-3\\.5{\n  width: 0.875rem;\n}\r\n.w-3\\/4{\n  width: 75%;\n}\r\n.w-32{\n  width: 8rem;\n}\r\n.w-4{\n  width: 1rem;\n}\r\n.w-48{\n  width: 12rem;\n}\r\n.w-5{\n  width: 1.25rem;\n}\r\n.w-56{\n  width: 14rem;\n}\r\n.w-6{\n  width: 1.5rem;\n}\r\n.w-64{\n  width: 16rem;\n}\r\n.w-7{\n  width: 1.75rem;\n}\r\n.w-72{\n  width: 18rem;\n}\r\n.w-8{\n  width: 2rem;\n}\r\n.w-80{\n  width: 20rem;\n}\r\n.w-9{\n  width: 2.25rem;\n}\r\n.w-96{\n  width: 24rem;\n}\r\n.w-\\[113\\.6\\%\\]{\n  width: 113.6%;\n}\r\n.w-\\[120px\\]{\n  width: 120px;\n}\r\n.w-\\[138\\%\\]{\n  width: 138%;\n}\r\n.w-\\[1px\\]{\n  width: 1px;\n}\r\n.w-\\[250px\\]{\n  width: 250px;\n}\r\n.w-\\[83\\%\\]{\n  width: 83%;\n}\r\n.w-\\[85vw\\]{\n  width: 85vw;\n}\r\n.w-\\[90\\%\\]{\n  width: 90%;\n}\r\n.w-\\[95\\%\\]{\n  width: 95%;\n}\r\n.w-\\[calc\\(100\\%-2rem\\)\\]{\n  width: calc(100% - 2rem);\n}\r\n.w-auto{\n  width: auto;\n}\r\n.w-fit{\n  width: fit-content;\n}\r\n.w-full{\n  width: 100%;\n}\r\n.min-w-0{\n  min-width: 0px;\n}\r\n.min-w-20{\n  min-width: 5rem;\n}\r\n.min-w-28{\n  min-width: 7rem;\n}\r\n.min-w-48{\n  min-width: 12rem;\n}\r\n.min-w-72{\n  min-width: 18rem;\n}\r\n.min-w-\\[10rem\\]{\n  min-width: 10rem;\n}\r\n.min-w-\\[11rem\\]{\n  min-width: 11rem;\n}\r\n.min-w-\\[14rem\\]{\n  min-width: 14rem;\n}\r\n.min-w-\\[256px\\]{\n  min-width: 256px;\n}\r\n.min-w-\\[8rem\\]{\n  min-width: 8rem;\n}\r\n.min-w-\\[calc\\(var\\(--radix-select-trigger-width\\)-8px\\)\\]{\n  min-width: calc(var(--radix-select-trigger-width) - 8px);\n}\r\n.min-w-\\[var\\(--radix-popper-anchor-width\\)\\]{\n  min-width: var(--radix-popper-anchor-width);\n}\r\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\]{\n  min-width: var(--radix-select-trigger-width);\n}\r\n.min-w-fit{\n  min-width: fit-content;\n}\r\n.max-w-28{\n  max-width: 7rem;\n}\r\n.max-w-2xl{\n  max-width: 42rem;\n}\r\n.max-w-3xl{\n  max-width: 48rem;\n}\r\n.max-w-40{\n  max-width: 10rem;\n}\r\n.max-w-44{\n  max-width: 11rem;\n}\r\n.max-w-4xl{\n  max-width: 56rem;\n}\r\n.max-w-52{\n  max-width: 13rem;\n}\r\n.max-w-5xl{\n  max-width: 64rem;\n}\r\n.max-w-64{\n  max-width: 16rem;\n}\r\n.max-w-72{\n  max-width: 18rem;\n}\r\n.max-w-7xl{\n  max-width: 80rem;\n}\r\n.max-w-80{\n  max-width: 20rem;\n}\r\n.max-w-\\[26rem\\]{\n  max-width: 26rem;\n}\r\n.max-w-\\[80\\%\\]{\n  max-width: 80%;\n}\r\n.max-w-\\[90\\%\\]{\n  max-width: 90%;\n}\r\n.max-w-\\[90vw\\]{\n  max-width: 90vw;\n}\r\n.max-w-fit{\n  max-width: fit-content;\n}\r\n.max-w-full{\n  max-width: 100%;\n}\r\n.max-w-lg{\n  max-width: 32rem;\n}\r\n.max-w-md{\n  max-width: 28rem;\n}\r\n.max-w-sm{\n  max-width: 24rem;\n}\r\n.max-w-xl{\n  max-width: 36rem;\n}\r\n.max-w-xs{\n  max-width: 20rem;\n}\r\n.flex-1{\n  flex: 1 1 0%;\n}\r\n.flex-shrink-0{\n  flex-shrink: 0;\n}\r\n.shrink-0{\n  flex-shrink: 0;\n}\r\n.flex-grow{\n  flex-grow: 1;\n}\r\n.grow{\n  flex-grow: 1;\n}\r\n.caption-bottom{\n  caption-side: bottom;\n}\r\n.border-separate{\n  border-collapse: separate;\n}\r\n.border-spacing-0{\n  --tw-border-spacing-x: 0px;\n  --tw-border-spacing-y: 0px;\n  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);\n}\r\n.border-spacing-2{\n  --tw-border-spacing-x: 0.5rem;\n  --tw-border-spacing-y: 0.5rem;\n  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);\n}\r\n.-translate-x-1\\/2{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-1\\/2{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-0\\.5{\n  --tw-translate-x: 0.125rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-1\\/3{\n  --tw-translate-x: 33.333333%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[-50\\%\\]{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-1\\/2{\n  --tw-translate-y: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[-50\\%\\]{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[15\\%\\]{\n  --tw-translate-y: 15%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[6\\.2rem\\]{\n  --tw-translate-y: 6.2rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[75\\%\\]{\n  --tw-translate-y: 75%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[9\\.9rem\\]{\n  --tw-translate-y: 9.9rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[calc\\(-50\\%_-_2px\\)\\]{\n  --tw-translate-y: calc(-50% - 2px);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-0{\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-180{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-3{\n  --tw-rotate: 3deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-45{\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-90{\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-0{\n  --tw-scale-x: 0;\n  --tw-scale-y: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-100{\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-y-\\[1\\]{\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform-gpu{\n  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes blink{\r\n\r\n  0%, 100%{\n    opacity: 1;\n  }\r\n\r\n  50%{\n    opacity: 0;\n  }\n}\r\n.animate-\\[blink_1s_step-end_infinite\\]{\n  animation: blink 1s step-end infinite;\n}\r\n@keyframes bounce-dots{\r\n\r\n  0%, 100%{\n    transform: scale(0.8);\n    opacity: 0.5;\n  }\r\n\r\n  50%{\n    transform: scale(1.2);\n    opacity: 1;\n  }\n}\r\n.animate-\\[bounce-dots_1\\.4s_ease-in-out_infinite\\]{\n  animation: bounce-dots 1.4s ease-in-out infinite;\n}\r\n@keyframes loading-dots{\r\n\r\n  0%, 100%{\n    opacity: 0;\n  }\r\n\r\n  50%{\n    opacity: 1;\n  }\n}\r\n.animate-\\[loading-dots_1\\.4s_infinite_0\\.2s\\]{\n  animation: loading-dots 1.4s infinite 0.2s;\n}\r\n@keyframes loading-dots{\r\n\r\n  0%, 100%{\n    opacity: 0;\n  }\r\n\r\n  50%{\n    opacity: 1;\n  }\n}\r\n.animate-\\[loading-dots_1\\.4s_infinite_0\\.4s\\]{\n  animation: loading-dots 1.4s infinite 0.4s;\n}\r\n@keyframes loading-dots{\r\n\r\n  0%, 100%{\n    opacity: 0;\n  }\r\n\r\n  50%{\n    opacity: 1;\n  }\n}\r\n.animate-\\[loading-dots_1\\.4s_infinite_0\\.6s\\]{\n  animation: loading-dots 1.4s infinite 0.6s;\n}\r\n@keyframes pulse-dot{\r\n\r\n  0%, 100%{\n    transform: scale(1);\n    opacity: 0.8;\n  }\r\n\r\n  50%{\n    transform: scale(1.5);\n    opacity: 1;\n  }\n}\r\n.animate-\\[pulse-dot_1\\.2s_ease-in-out_infinite\\]{\n  animation: pulse-dot 1.2s ease-in-out infinite;\n}\r\n@keyframes shimmer{\r\n\r\n  0%{\n    background-position: 200% 50%;\n  }\r\n\r\n  100%{\n    background-position: -200% 50%;\n  }\n}\r\n.animate-\\[shimmer_4s_infinite_linear\\]{\n  animation: shimmer 4s infinite linear;\n}\r\n@keyframes spinner-fade{\r\n\r\n  0%{\n    opacity: 0;\n  }\r\n\r\n  100%{\n    opacity: 1;\n  }\n}\r\n.animate-\\[spinner-fade_1\\.2s_linear_infinite\\]{\n  animation: spinner-fade 1.2s linear infinite;\n}\r\n@keyframes text-blink{\r\n\r\n  0%, 100%{\n    color: var(--primary);\n  }\r\n\r\n  50%{\n    color: var(--muted-foreground);\n  }\n}\r\n.animate-\\[text-blink_2s_ease-in-out_infinite\\]{\n  animation: text-blink 2s ease-in-out infinite;\n}\r\n@keyframes thin-pulse{\r\n\r\n  0%, 100%{\n    transform: scale(0.95);\n    opacity: 0.8;\n  }\r\n\r\n  50%{\n    transform: scale(1.05);\n    opacity: 0.4;\n  }\n}\r\n.animate-\\[thin-pulse_1\\.5s_ease-in-out_infinite\\]{\n  animation: thin-pulse 1.5s ease-in-out infinite;\n}\r\n@keyframes typing{\r\n\r\n  0%, 100%{\n    transform: translateY(0);\n    opacity: 0.5;\n  }\r\n\r\n  50%{\n    transform: translateY(-2px);\n    opacity: 1;\n  }\n}\r\n.animate-\\[typing_1s_infinite\\]{\n  animation: typing 1s infinite;\n}\r\n@keyframes wave-bars{\r\n\r\n  0%, 100%{\n    transform: scaleY(1);\n    opacity: 0.5;\n  }\r\n\r\n  50%{\n    transform: scaleY(0.6);\n    opacity: 1;\n  }\n}\r\n.animate-\\[wave-bars_1\\.2s_ease-in-out_infinite\\]{\n  animation: wave-bars 1.2s ease-in-out infinite;\n}\r\n@keyframes wave{\r\n\r\n  0%, 100%{\n    transform: scaleY(1);\n  }\r\n\r\n  50%{\n    transform: scaleY(0.6);\n  }\n}\r\n.animate-\\[wave_1s_ease-in-out_infinite\\]{\n  animation: wave 1s ease-in-out infinite;\n}\r\n@keyframes aurora{\r\n\r\n  0%{\n    background-position: 0% 50%;\n    transform: rotate(-5deg) scale(0.9);\n  }\r\n\r\n  25%{\n    background-position: 50% 100%;\n    transform: rotate(5deg) scale(1.1);\n  }\r\n\r\n  50%{\n    background-position: 100% 50%;\n    transform: rotate(-3deg) scale(0.95);\n  }\r\n\r\n  75%{\n    background-position: 50% 0%;\n    transform: rotate(3deg) scale(1.05);\n  }\r\n\r\n  100%{\n    background-position: 0% 50%;\n    transform: rotate(-5deg) scale(0.9);\n  }\n}\r\n.animate-aurora{\n  animation: aurora 8s ease-in-out infinite alternate;\n}\r\n@keyframes marquee{\r\n\r\n  from{\n    transform: translateX(0);\n  }\r\n\r\n  to{\n    transform: translateX(calc(-100% - 4rem));\n  }\n}\r\n.animate-marquee{\n  animation: marquee 60s linear infinite;\n}\r\n@keyframes orbit{\r\n\r\n  0%{\n    transform: rotate(calc(var(--angle) * 1deg)) translateY(calc(var(--radius) * 1px)) rotate(calc(var(--angle) * -1deg));\n  }\r\n\r\n  100%{\n    transform: rotate(calc(var(--angle) * 1deg + 360deg)) translateY(calc(var(--radius) * 1px)) rotate(calc((var(--angle) * -1deg) - 360deg));\n  }\n}\r\n.animate-orbit{\n  animation: orbit calc(var(--duration)*1s) linear infinite;\n}\r\n@keyframes pulse{\r\n\r\n  50%{\n    opacity: .5;\n  }\n}\r\n.animate-pulse{\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n@keyframes shiny-text{\r\n\r\n  0%, 90%, 100%{\n    background-position: calc(-100% - var(--shiny-width)) 0;\n  }\r\n\r\n  30%, 60%{\n    background-position: calc(100% + var(--shiny-width)) 0;\n  }\n}\r\n.animate-shiny-text{\n  animation: shiny-text 8s infinite;\n}\r\n@keyframes spin{\r\n\r\n  to{\n    transform: rotate(360deg);\n  }\r\n\r\n  0%{\n    transform: rotate(0deg);\n  }\r\n\r\n  100%{\n    transform: rotate(360deg);\n  }\n}\r\n.animate-spin{\n  animation: spin 1s linear infinite;\n}\r\n@keyframes spotlight{\r\n\r\n  0%{\n    opacity: 0;\n    transform: translate(-72%, -62%) scale(0.5);\n  }\r\n\r\n  100%{\n    opacity: 1;\n    transform: translate(-50%,-40%) scale(1);\n  }\n}\r\n.animate-spotlight{\n  animation: spotlight 2s ease .75s 1 forwards;\n}\r\n.cursor-auto{\n  cursor: auto;\n}\r\n.cursor-default{\n  cursor: default;\n}\r\n.cursor-help{\n  cursor: help;\n}\r\n.cursor-n-resize{\n  cursor: n-resize;\n}\r\n.cursor-not-allowed{\n  cursor: not-allowed;\n}\r\n.cursor-pointer{\n  cursor: pointer;\n}\r\n.touch-none{\n  touch-action: none;\n}\r\n.select-none{\n  user-select: none;\n}\r\n.select-text{\n  user-select: text;\n}\r\n.resize-none{\n  resize: none;\n}\r\n.resize{\n  resize: both;\n}\r\n.scroll-m-20{\n  scroll-margin: 5rem;\n}\r\n.scroll-my-1{\n  scroll-margin-top: 0.25rem;\n  scroll-margin-bottom: 0.25rem;\n}\r\n.list-inside{\n  list-style-position: inside;\n}\r\n.list-decimal{\n  list-style-type: decimal;\n}\r\n.list-disc{\n  list-style-type: disc;\n}\r\n.columns-1{\n  columns: 1;\n}\r\n.break-inside-avoid{\n  break-inside: avoid;\n}\r\n.grid-cols-1{\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\r\n.grid-cols-12{\n  grid-template-columns: repeat(12, minmax(0, 1fr));\n}\r\n.grid-cols-2{\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\r\n.grid-cols-3{\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\r\n.grid-cols-4{\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\r\n.flex-row{\n  flex-direction: row;\n}\r\n.flex-col{\n  flex-direction: column;\n}\r\n.flex-col-reverse{\n  flex-direction: column-reverse;\n}\r\n.flex-wrap{\n  flex-wrap: wrap;\n}\r\n.flex-nowrap{\n  flex-wrap: nowrap;\n}\r\n.items-start{\n  align-items: flex-start;\n}\r\n.items-end{\n  align-items: flex-end;\n}\r\n.items-center{\n  align-items: center;\n}\r\n.items-baseline{\n  align-items: baseline;\n}\r\n.items-stretch{\n  align-items: stretch;\n}\r\n.justify-start{\n  justify-content: flex-start;\n}\r\n.justify-end{\n  justify-content: flex-end;\n}\r\n.justify-center{\n  justify-content: center;\n}\r\n.justify-between{\n  justify-content: space-between;\n}\r\n.justify-around{\n  justify-content: space-around;\n}\r\n.gap-0{\n  gap: 0px;\n}\r\n.gap-0\\.5{\n  gap: 0.125rem;\n}\r\n.gap-1{\n  gap: 0.25rem;\n}\r\n.gap-1\\.5{\n  gap: 0.375rem;\n}\r\n.gap-12{\n  gap: 3rem;\n}\r\n.gap-2{\n  gap: 0.5rem;\n}\r\n.gap-20{\n  gap: 5rem;\n}\r\n.gap-3{\n  gap: 0.75rem;\n}\r\n.gap-4{\n  gap: 1rem;\n}\r\n.gap-5{\n  gap: 1.25rem;\n}\r\n.gap-6{\n  gap: 1.5rem;\n}\r\n.gap-8{\n  gap: 2rem;\n}\r\n.gap-x-3{\n  column-gap: 0.75rem;\n}\r\n.gap-x-8{\n  column-gap: 2rem;\n}\r\n.gap-y-2{\n  row-gap: 0.5rem;\n}\r\n.gap-y-6{\n  row-gap: 1.5rem;\n}\r\n.-space-x-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(-0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(-0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-y-0 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0px * var(--tw-space-y-reverse));\n}\r\n.space-y-0\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\r\n.space-y-10 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-12 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(3rem * var(--tw-space-y-reverse));\n}\r\n.space-y-16 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(4rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));\n}\r\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\r\n.space-y-5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\r\n.divide-y > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\n}\r\n.divide-primary\\/20 > :not([hidden]) ~ :not([hidden]){\n  border-color: hsl(var(--primary) / 0.2);\n}\r\n.overflow-auto{\n  overflow: auto;\n}\r\n.overflow-hidden{\n  overflow: hidden;\n}\r\n.overflow-clip{\n  overflow: clip;\n}\r\n.overflow-x-auto{\n  overflow-x: auto;\n}\r\n.overflow-y-auto{\n  overflow-y: auto;\n}\r\n.overflow-x-hidden{\n  overflow-x: hidden;\n}\r\n.overflow-y-clip{\n  overflow-y: clip;\n}\r\n.overflow-x-visible{\n  overflow-x: visible;\n}\r\n.scroll-smooth{\n  scroll-behavior: smooth;\n}\r\n.truncate{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\r\n.whitespace-normal{\n  white-space: normal;\n}\r\n.whitespace-nowrap{\n  white-space: nowrap;\n}\r\n.whitespace-pre{\n  white-space: pre;\n}\r\n.whitespace-pre-wrap{\n  white-space: pre-wrap;\n}\r\n.text-wrap{\n  text-wrap: wrap;\n}\r\n.text-nowrap{\n  text-wrap: nowrap;\n}\r\n.text-balance{\n  text-wrap: balance;\n}\r\n.text-pretty{\n  text-wrap: pretty;\n}\r\n.break-words{\n  overflow-wrap: break-word;\n}\r\n.break-all{\n  word-break: break-all;\n}\r\n.rounded{\n  border-radius: 0.25rem;\n}\r\n.rounded-2xl{\n  border-radius: calc(var(--radius) + 4px);\n}\r\n.rounded-3xl{\n  border-radius: calc(var(--radius) + 6px);\n}\r\n.rounded-4xl{\n  border-radius: calc(var(--radius) + 8px);\n}\r\n.rounded-5xl{\n  border-radius: calc(var(--radius) + 10px);\n}\r\n.rounded-\\[0\\.4rem\\]{\n  border-radius: 0.4rem;\n}\r\n.rounded-\\[2px\\]{\n  border-radius: 2px;\n}\r\n.rounded-\\[inherit\\]{\n  border-radius: inherit;\n}\r\n.rounded-full{\n  border-radius: 9999px;\n}\r\n.rounded-lg{\n  border-radius: 0.38rem;\n}\r\n.rounded-md{\n  border-radius: var(--radius);\n}\r\n.rounded-none{\n  border-radius: 0px;\n}\r\n.rounded-sm{\n  border-radius: calc(var(--radius) - 4px);\n}\r\n.rounded-xl{\n  border-radius: calc(var(--radius) + 1px);\n}\r\n.rounded-b-2xl{\n  border-bottom-right-radius: calc(var(--radius) + 4px);\n  border-bottom-left-radius: calc(var(--radius) + 4px);\n}\r\n.rounded-b-lg{\n  border-bottom-right-radius: 0.38rem;\n  border-bottom-left-radius: 0.38rem;\n}\r\n.rounded-b-none{\n  border-bottom-right-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\r\n.rounded-b-xl{\n  border-bottom-right-radius: calc(var(--radius) + 1px);\n  border-bottom-left-radius: calc(var(--radius) + 1px);\n}\r\n.rounded-l-2xl{\n  border-top-left-radius: calc(var(--radius) + 4px);\n  border-bottom-left-radius: calc(var(--radius) + 4px);\n}\r\n.rounded-l-xl{\n  border-top-left-radius: calc(var(--radius) + 1px);\n  border-bottom-left-radius: calc(var(--radius) + 1px);\n}\r\n.rounded-r-2xl{\n  border-top-right-radius: calc(var(--radius) + 4px);\n  border-bottom-right-radius: calc(var(--radius) + 4px);\n}\r\n.rounded-r-xl{\n  border-top-right-radius: calc(var(--radius) + 1px);\n  border-bottom-right-radius: calc(var(--radius) + 1px);\n}\r\n.rounded-t-2xl{\n  border-top-left-radius: calc(var(--radius) + 4px);\n  border-top-right-radius: calc(var(--radius) + 4px);\n}\r\n.rounded-t-3xl{\n  border-top-left-radius: calc(var(--radius) + 6px);\n  border-top-right-radius: calc(var(--radius) + 6px);\n}\r\n.rounded-t-lg{\n  border-top-left-radius: 0.38rem;\n  border-top-right-radius: 0.38rem;\n}\r\n.rounded-t-none{\n  border-top-left-radius: 0px;\n  border-top-right-radius: 0px;\n}\r\n.rounded-t-xl{\n  border-top-left-radius: calc(var(--radius) + 1px);\n  border-top-right-radius: calc(var(--radius) + 1px);\n}\r\n.rounded-se{\n  border-start-end-radius: 0.25rem;\n}\r\n.rounded-tl-2xl{\n  border-top-left-radius: calc(var(--radius) + 4px);\n}\r\n.border{\n  border-width: 1px;\n}\r\n.border-0{\n  border-width: 0px;\n}\r\n.border-2{\n  border-width: 2px;\n}\r\n.border-4{\n  border-width: 4px;\n}\r\n.border-\\[1px\\]{\n  border-width: 1px;\n}\r\n.border-x-0{\n  border-left-width: 0px;\n  border-right-width: 0px;\n}\r\n.border-y{\n  border-top-width: 1px;\n  border-bottom-width: 1px;\n}\r\n.border-b{\n  border-bottom-width: 1px;\n}\r\n.border-b-0{\n  border-bottom-width: 0px;\n}\r\n.border-b-2{\n  border-bottom-width: 2px;\n}\r\n.border-b-\\[1\\.5px\\]{\n  border-bottom-width: 1.5px;\n}\r\n.border-b-\\[1px\\]{\n  border-bottom-width: 1px;\n}\r\n.border-l{\n  border-left-width: 1px;\n}\r\n.border-l-0{\n  border-left-width: 0px;\n}\r\n.border-l-2{\n  border-left-width: 2px;\n}\r\n.border-l-\\[1\\.5px\\]{\n  border-left-width: 1.5px;\n}\r\n.border-r{\n  border-right-width: 1px;\n}\r\n.border-r-\\[1\\.5px\\]{\n  border-right-width: 1.5px;\n}\r\n.border-r-\\[1px\\]{\n  border-right-width: 1px;\n}\r\n.border-t{\n  border-top-width: 1px;\n}\r\n.border-t-0{\n  border-top-width: 0px;\n}\r\n.border-t-\\[1\\.5px\\]{\n  border-top-width: 1.5px;\n}\r\n.border-dashed{\n  border-style: dashed;\n}\r\n.border-none{\n  border-style: none;\n}\r\n.border-\\[\\#34B27B\\]\\/20{\n  border-color: rgb(52 178 123 / 0.2);\n}\r\n.border-amber-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\n}\r\n.border-amber-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(251 191 36 / var(--tw-border-opacity, 1));\n}\r\n.border-amber-600\\/40{\n  border-color: rgb(217 119 6 / 0.4);\n}\r\n.border-background{\n  border-color: hsl(var(--background));\n}\r\n.border-background\\/10{\n  border-color: hsl(var(--background) / 0.1);\n}\r\n.border-background\\/30{\n  border-color: hsl(var(--background) / 0.3);\n}\r\n.border-blue-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-500\\/10{\n  border-color: rgb(59 130 246 / 0.1);\n}\r\n.border-blue-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-800{\n  --tw-border-opacity: 1;\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\n}\r\n.border-border{\n  border-color: hsl(var(--border));\n}\r\n.border-border\\/50{\n  border-color: hsl(var(--border) / 0.5);\n}\r\n.border-border\\/60{\n  border-color: hsl(var(--border) / 0.6);\n}\r\n.border-border\\/70{\n  border-color: hsl(var(--border) / 0.7);\n}\r\n.border-border\\/80{\n  border-color: hsl(var(--border) / 0.8);\n}\r\n.border-chart-2\\/50{\n  border-color: hsl(var(--chart-2) / 0.5);\n}\r\n.border-chart-2\\/70{\n  border-color: hsl(var(--chart-2) / 0.7);\n}\r\n.border-current{\n  border-color: currentColor;\n}\r\n.border-destructive{\n  border-color: hsl(var(--destructive));\n}\r\n.border-destructive\\/20{\n  border-color: hsl(var(--destructive) / 0.2);\n}\r\n.border-destructive\\/50{\n  border-color: hsl(var(--destructive) / 0.5);\n}\r\n.border-emerald-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));\n}\r\n.border-emerald-500\\/10{\n  border-color: rgb(16 185 129 / 0.1);\n}\r\n.border-emerald-800{\n  --tw-border-opacity: 1;\n  border-color: rgb(6 95 70 / var(--tw-border-opacity, 1));\n}\r\n.border-foreground\\/10{\n  border-color: hsl(var(--foreground) / 0.1);\n}\r\n.border-foreground\\/20{\n  border-color: hsl(var(--foreground) / 0.2);\n}\r\n.border-gray-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\r\n.border-green-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\r\n.border-green-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\r\n.border-inherit{\n  border-color: inherit;\n}\r\n.border-input{\n  border-color: hsl(var(--input));\n}\r\n.border-neutral-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));\n}\r\n.border-neutral-600\\/40{\n  border-color: rgb(82 82 82 / 0.4);\n}\r\n.border-orange-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\n}\r\n.border-primary{\n  border-color: hsl(var(--primary));\n}\r\n.border-primary\\/10{\n  border-color: hsl(var(--primary) / 0.1);\n}\r\n.border-primary\\/15{\n  border-color: hsl(var(--primary) / 0.15);\n}\r\n.border-primary\\/20{\n  border-color: hsl(var(--primary) / 0.2);\n}\r\n.border-primary\\/40{\n  border-color: hsl(var(--primary) / 0.4);\n}\r\n.border-primary\\/5{\n  border-color: hsl(var(--primary) / 0.05);\n}\r\n.border-primary\\/50{\n  border-color: hsl(var(--primary) / 0.5);\n}\r\n.border-primary\\/80{\n  border-color: hsl(var(--primary) / 0.8);\n}\r\n.border-primary\\/95{\n  border-color: hsl(var(--primary) / 0.95);\n}\r\n.border-purple-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));\n}\r\n.border-purple-600\\/40{\n  border-color: rgb(147 51 234 / 0.4);\n}\r\n.border-red-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\r\n.border-red-500\\/10{\n  border-color: rgb(239 68 68 / 0.1);\n}\r\n.border-red-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\r\n.border-red-600\\/30{\n  border-color: rgb(220 38 38 / 0.3);\n}\r\n.border-red-900\\/50{\n  border-color: rgb(127 29 29 / 0.5);\n}\r\n.border-ring\\/15{\n  border-color: hsl(var(--ring) / 0.15);\n}\r\n.border-ring\\/40{\n  border-color: hsl(var(--ring) / 0.4);\n}\r\n.border-secondary\\/10{\n  border-color: hsl(var(--secondary) / 0.1);\n}\r\n.border-sky-400\\/40{\n  border-color: rgb(56 189 248 / 0.4);\n}\r\n.border-transparent{\n  border-color: transparent;\n}\r\n.border-yellow-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-500\\/10{\n  border-color: rgb(234 179 8 / 0.1);\n}\r\n.border-yellow-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-600\\/40{\n  border-color: rgb(202 138 4 / 0.4);\n}\r\n.border-zinc-800{\n  --tw-border-opacity: 1;\n  border-color: rgb(39 39 42 / var(--tw-border-opacity, 1));\n}\r\n.border-l-transparent{\n  border-left-color: transparent;\n}\r\n.border-t-transparent{\n  border-top-color: transparent;\n}\r\n.bg-\\[\\#006239\\]\\/90{\n  background-color: rgb(0 98 57 / 0.9);\n}\r\n.bg-\\[\\#0C97B0\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(12 151 176 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#0a0a0a\\]\\/95{\n  background-color: rgb(10 10 10 / 0.95);\n}\r\n.bg-\\[\\#E1E1E1\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(225 225 225 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#E5F6FF\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 246 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#F5F5F5\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#FFD1C1\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 209 193 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#FFE5E5\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 229 229 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#FFEF94\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 239 148 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#fafafa\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[rgba\\(93\\2c 208\\2c 220\\)\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgba(93, 208, 220, var(--tw-bg-opacity, 1));\n}\r\n.bg-accent{\n  background-color: hsl(var(--accent));\n}\r\n.bg-accent\\/50{\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.bg-accent\\/60{\n  background-color: hsl(var(--accent) / 0.6);\n}\r\n.bg-accent\\/80{\n  background-color: hsl(var(--accent) / 0.8);\n}\r\n.bg-amber-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\n}\r\n.bg-amber-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));\n}\r\n.bg-amber-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\n}\r\n.bg-background{\n  background-color: hsl(var(--background));\n}\r\n.bg-background\\/20{\n  background-color: hsl(var(--background) / 0.2);\n}\r\n.bg-background\\/50{\n  background-color: hsl(var(--background) / 0.5);\n}\r\n.bg-background\\/70{\n  background-color: hsl(var(--background) / 0.7);\n}\r\n.bg-background\\/80{\n  background-color: hsl(var(--background) / 0.8);\n}\r\n.bg-background\\/90{\n  background-color: hsl(var(--background) / 0.9);\n}\r\n.bg-background\\/95{\n  background-color: hsl(var(--background) / 0.95);\n}\r\n.bg-black\\/80{\n  background-color: rgb(0 0 0 / 0.8);\n}\r\n.bg-black\\/90{\n  background-color: rgb(0 0 0 / 0.9);\n}\r\n.bg-blue-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-300\\/70{\n  background-color: rgb(147 197 253 / 0.7);\n}\r\n.bg-blue-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-800{\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));\n}\r\n.bg-border{\n  background-color: hsl(var(--border));\n}\r\n.bg-card{\n  background-color: hsl(var(--card));\n}\r\n.bg-chart-2\\/20{\n  background-color: hsl(var(--chart-2) / 0.2);\n}\r\n.bg-destructive{\n  background-color: hsl(var(--destructive));\n}\r\n.bg-destructive\\/5{\n  background-color: hsl(var(--destructive) / 0.05);\n}\r\n.bg-emerald-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));\n}\r\n.bg-emerald-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));\n}\r\n.bg-foreground\\/10{\n  background-color: hsl(var(--foreground) / 0.1);\n}\r\n.bg-foreground\\/5{\n  background-color: hsl(var(--foreground) / 0.05);\n}\r\n.bg-gray-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-300\\/70{\n  background-color: rgb(134 239 172 / 0.7);\n}\r\n.bg-green-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\r\n.bg-input\\/40{\n  background-color: hsl(var(--input) / 0.4);\n}\r\n.bg-muted{\n  background-color: hsl(var(--muted));\n}\r\n.bg-muted-foreground\\/10{\n  background-color: hsl(var(--muted-foreground) / 0.1);\n}\r\n.bg-muted\\/30{\n  background-color: hsl(var(--muted) / 0.3);\n}\r\n.bg-muted\\/50{\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.bg-neutral-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));\n}\r\n.bg-neutral-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\r\n.bg-overlay\\/90{\n  background-color: hsl(var(--overlay) / 0.9);\n}\r\n.bg-pink-600\\/10{\n  background-color: rgb(219 39 119 / 0.1);\n}\r\n.bg-pink-600\\/30{\n  background-color: rgb(219 39 119 / 0.3);\n}\r\n.bg-popover{\n  background-color: hsl(var(--popover));\n}\r\n.bg-primary{\n  background-color: hsl(var(--primary));\n}\r\n.bg-primary\\/10{\n  background-color: hsl(var(--primary) / 0.1);\n}\r\n.bg-primary\\/20{\n  background-color: hsl(var(--primary) / 0.2);\n}\r\n.bg-primary\\/5{\n  background-color: hsl(var(--primary) / 0.05);\n}\r\n.bg-primary\\/50{\n  background-color: hsl(var(--primary) / 0.5);\n}\r\n.bg-primary\\/90{\n  background-color: hsl(var(--primary) / 0.9);\n}\r\n.bg-purple-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-purple-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-purple-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\r\n.bg-purple-600\\/10{\n  background-color: rgb(147 51 234 / 0.1);\n}\r\n.bg-purple-600\\/30{\n  background-color: rgb(147 51 234 / 0.3);\n}\r\n.bg-red-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-300\\/70{\n  background-color: rgb(252 165 165 / 0.7);\n}\r\n.bg-red-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-800{\n  --tw-bg-opacity: 1;\n  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));\n}\r\n.bg-secondary{\n  background-color: hsl(var(--secondary));\n}\r\n.bg-secondary\\/20{\n  background-color: hsl(var(--secondary) / 0.2);\n}\r\n.bg-secondary\\/30{\n  background-color: hsl(var(--secondary) / 0.3);\n}\r\n.bg-sidebar-accent{\n  background-color: hsl(var(--sidebar-accent));\n}\r\n.bg-sidebar-accent\\/10{\n  background-color: hsl(var(--sidebar-accent) / 0.1);\n}\r\n.bg-sidebar-accent\\/15{\n  background-color: hsl(var(--sidebar-accent) / 0.15);\n}\r\n.bg-sidebar-accent\\/40{\n  background-color: hsl(var(--sidebar-accent) / 0.4);\n}\r\n.bg-sidebar-accent\\/60{\n  background-color: hsl(var(--sidebar-accent) / 0.6);\n}\r\n.bg-sidebar-accent\\/80{\n  background-color: hsl(var(--sidebar-accent) / 0.8);\n}\r\n.bg-sidebar-accent\\/95{\n  background-color: hsl(var(--sidebar-accent) / 0.95);\n}\r\n.bg-sidebar-border\\/70{\n  background-color: hsl(var(--sidebar-border) / 0.7);\n}\r\n.bg-sidebar-primary{\n  background-color: hsl(var(--sidebar-primary));\n}\r\n.bg-sidebar-primary\\/80{\n  background-color: hsl(var(--sidebar-primary) / 0.8);\n}\r\n.bg-sidebar-ring{\n  background-color: hsl(var(--sidebar-ring));\n}\r\n.bg-sidebar-ring\\/10{\n  background-color: hsl(var(--sidebar-ring) / 0.1);\n}\r\n.bg-sidebar-ring\\/20{\n  background-color: hsl(var(--sidebar-ring) / 0.2);\n}\r\n.bg-sky-400\\/10{\n  background-color: rgb(56 189 248 / 0.1);\n}\r\n.bg-teal-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\n}\r\n.bg-teal-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));\n}\r\n.bg-transparent{\n  background-color: transparent;\n}\r\n.bg-yellow-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-300\\/70{\n  background-color: rgb(253 224 71 / 0.7);\n}\r\n.bg-yellow-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\r\n.bg-opacity-20{\n  --tw-bg-opacity: 0.2;\n}\r\n.bg-\\[image\\:radial-gradient\\(90\\%_40\\%_at_50\\%_-20\\%\\2c rgba\\(93\\2c 208\\2c 220\\2c 0\\.7\\)\\2c rgba\\(255\\2c 255\\2c 255\\2c 0\\)\\)\\]{\n  background-image: radial-gradient(90% 40% at 50% -20%,rgba(93,208,220,0.7),rgba(255,255,255,0));\n}\r\n.bg-\\[image\\:radial-gradient\\(90\\%_50\\%_at_50\\%_-20\\%\\2c rgba\\(93\\2c 208\\2c 220\\2c 0\\.7\\)\\2c rgba\\(255\\2c 255\\2c 255\\2c 0\\)\\)\\]{\n  background-image: radial-gradient(90% 50% at 50% -20%,rgba(93,208,220,0.7),rgba(255,255,255,0));\n}\r\n.bg-\\[image\\:radial-gradient\\(90\\%_50\\%_at_50\\%_-35\\%\\2c rgba\\(93\\2c 208\\2c 220\\)\\2c rgba\\(255\\2c 255\\2c 255\\2c 0\\)\\)\\]{\n  background-image: radial-gradient(90% 50% at 50% -35%,rgba(93,208,220),rgba(255,255,255,0));\n}\r\n.bg-gradient-to-b{\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-br{\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-l{\n  background-image: linear-gradient(to left, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-r{\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-t{\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\r\n.from-\\[\\#FFE9C5\\]{\n  --tw-gradient-from: #FFE9C5 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 233 197 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-amber-50{\n  --tw-gradient-from: #fffbeb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 251 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-amber-500\\/80{\n  --tw-gradient-from: rgb(245 158 11 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-amber-600\\/80{\n  --tw-gradient-from: rgb(217 119 6 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-background{\n  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-background\\/10{\n  --tw-gradient-from: hsl(var(--background) / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-background\\/85{\n  --tw-gradient-from: hsl(var(--background) / 0.85) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-100{\n  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-500\\/10{\n  --tw-gradient-from: rgb(59 130 246 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-600\\/80{\n  --tw-gradient-from: rgb(37 99 235 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-cyan-600\\/80{\n  --tw-gradient-from: rgb(8 145 178 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(8 145 178 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-emerald-500\\/10{\n  --tw-gradient-from: rgb(16 185 129 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-emerald-600\\/80{\n  --tw-gradient-from: rgb(5 150 105 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-fuchsia-600\\/80{\n  --tw-gradient-from: rgb(192 38 211 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(192 38 211 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-green-600\\/80{\n  --tw-gradient-from: rgb(22 163 74 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-indigo-600\\/80{\n  --tw-gradient-from: rgb(79 70 229 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-lime-500\\/80{\n  --tw-gradient-from: rgb(132 204 22 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(132 204 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-lime-600\\/80{\n  --tw-gradient-from: rgb(101 163 13 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(101 163 13 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-muted-foreground{\n  --tw-gradient-from: hsl(var(--muted-foreground)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--muted-foreground) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-orange-600\\/80{\n  --tw-gradient-from: rgb(234 88 12 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-pink-600\\/80{\n  --tw-gradient-from: rgb(219 39 119 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-primary\\/20{\n  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-primary\\/40{\n  --tw-gradient-from: hsl(var(--primary) / 0.4) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-primary\\/50{\n  --tw-gradient-from: hsl(var(--primary) / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-purple-600\\/80{\n  --tw-gradient-from: rgb(147 51 234 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-red-500\\/10{\n  --tw-gradient-from: rgb(239 68 68 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-red-600\\/80{\n  --tw-gradient-from: rgb(220 38 38 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-rose-500\\/80{\n  --tw-gradient-from: rgb(244 63 94 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(244 63 94 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-rose-600\\/80{\n  --tw-gradient-from: rgb(225 29 72 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(225 29 72 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-sky-600\\/80{\n  --tw-gradient-from: rgb(2 132 199 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(2 132 199 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-teal-600\\/80{\n  --tw-gradient-from: rgb(13 148 136 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(13 148 136 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-transparent{\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-violet-600\\/80{\n  --tw-gradient-from: rgb(124 58 237 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(124 58 237 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-yellow-500\\/10{\n  --tw-gradient-from: rgb(234 179 8 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-yellow-600\\/80{\n  --tw-gradient-from: rgb(202 138 4 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(202 138 4 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-20\\%{\n  --tw-gradient-from-position: 20%;\n}\r\n.from-5\\%{\n  --tw-gradient-from-position: 5%;\n}\r\n.via-\\[\\#0C97B0\\]{\n  --tw-gradient-to: rgb(12 151 176 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #0C97B0 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-amber-500\\/60{\n  --tw-gradient-to: rgb(245 158 11 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(245 158 11 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-background\\/50{\n  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-background\\/85{\n  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.85) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-background\\/90{\n  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-black\\/80{\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-blue-500\\/60{\n  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(59 130 246 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-cyan-500\\/60{\n  --tw-gradient-to: rgb(6 182 212 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(6 182 212 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-emerald-500\\/60{\n  --tw-gradient-to: rgb(16 185 129 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(16 185 129 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-foreground{\n  --tw-gradient-to: hsl(var(--foreground) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--foreground)) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-fuchsia-500\\/60{\n  --tw-gradient-to: rgb(217 70 239 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(217 70 239 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-green-500\\/60{\n  --tw-gradient-to: rgb(34 197 94 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(34 197 94 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-indigo-500\\/60{\n  --tw-gradient-to: rgb(99 102 241 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(99 102 241 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-lime-500\\/60{\n  --tw-gradient-to: rgb(132 204 22 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(132 204 22 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-orange-400\\/60{\n  --tw-gradient-to: rgb(251 146 60 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(251 146 60 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-orange-500\\/60{\n  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(249 115 22 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-pink-500\\/60{\n  --tw-gradient-to: rgb(236 72 153 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(236 72 153 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-primary\\/40{\n  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-primary\\/90{\n  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-purple-500\\/60{\n  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-red-500\\/60{\n  --tw-gradient-to: rgb(239 68 68 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(239 68 68 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-rose-500\\/60{\n  --tw-gradient-to: rgb(244 63 94 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(244 63 94 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-sky-500\\/60{\n  --tw-gradient-to: rgb(14 165 233 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(14 165 233 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-teal-500\\/60{\n  --tw-gradient-to: rgb(20 184 166 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(20 184 166 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-transparent{\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-violet-500\\/60{\n  --tw-gradient-to: rgb(139 92 246 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(139 92 246 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-yellow-400\\/60{\n  --tw-gradient-to: rgb(250 204 21 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(250 204 21 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-yellow-500\\/60{\n  --tw-gradient-to: rgb(234 179 8 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(234 179 8 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-50\\%{\n  --tw-gradient-via-position: 50%;\n}\r\n.via-60\\%{\n  --tw-gradient-via-position: 60%;\n}\r\n.to-\\[\\#11274c\\]{\n  --tw-gradient-to: #11274c var(--tw-gradient-to-position);\n}\r\n.to-amber-300\\/70{\n  --tw-gradient-to: rgb(252 211 77 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-amber-400\\/70{\n  --tw-gradient-to: rgb(251 191 36 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-background{\n  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);\n}\r\n.to-background\\/5{\n  --tw-gradient-to: hsl(var(--background) / 0.05) var(--tw-gradient-to-position);\n}\r\n.to-blue-400\\/70{\n  --tw-gradient-to: rgb(96 165 250 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-blue-500\\/5{\n  --tw-gradient-to: rgb(59 130 246 / 0.05) var(--tw-gradient-to-position);\n}\r\n.to-cyan-400\\/70{\n  --tw-gradient-to: rgb(34 211 238 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-emerald-400\\/70{\n  --tw-gradient-to: rgb(52 211 153 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-emerald-500\\/5{\n  --tw-gradient-to: rgb(16 185 129 / 0.05) var(--tw-gradient-to-position);\n}\r\n.to-fuchsia-400\\/70{\n  --tw-gradient-to: rgb(232 121 249 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-green-400\\/70{\n  --tw-gradient-to: rgb(74 222 128 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-indigo-400\\/70{\n  --tw-gradient-to: rgb(129 140 248 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-lime-400\\/70{\n  --tw-gradient-to: rgb(163 230 53 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-muted-foreground{\n  --tw-gradient-to: hsl(var(--muted-foreground)) var(--tw-gradient-to-position);\n}\r\n.to-orange-300\\/70{\n  --tw-gradient-to: rgb(253 186 116 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-orange-50{\n  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);\n}\r\n.to-pink-400\\/70{\n  --tw-gradient-to: rgb(244 114 182 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-primary\\/20{\n  --tw-gradient-to: hsl(var(--primary) / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-primary\\/30{\n  --tw-gradient-to: hsl(var(--primary) / 0.3) var(--tw-gradient-to-position);\n}\r\n.to-purple-100{\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\n}\r\n.to-purple-400\\/70{\n  --tw-gradient-to: rgb(192 132 252 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-purple-500\\/10{\n  --tw-gradient-to: rgb(168 85 247 / 0.1) var(--tw-gradient-to-position);\n}\r\n.to-red-400\\/70{\n  --tw-gradient-to: rgb(248 113 113 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-red-500\\/5{\n  --tw-gradient-to: rgb(239 68 68 / 0.05) var(--tw-gradient-to-position);\n}\r\n.to-rose-400\\/70{\n  --tw-gradient-to: rgb(251 113 133 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-sky-400\\/70{\n  --tw-gradient-to: rgb(56 189 248 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-teal-400\\/70{\n  --tw-gradient-to: rgb(45 212 191 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-transparent{\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\r\n.to-violet-400\\/70{\n  --tw-gradient-to: rgb(167 139 250 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-yellow-400\\/70{\n  --tw-gradient-to: rgb(250 204 21 / 0.7) var(--tw-gradient-to-position);\n}\r\n.to-yellow-500\\/5{\n  --tw-gradient-to: rgb(234 179 8 / 0.05) var(--tw-gradient-to-position);\n}\r\n.to-100\\%{\n  --tw-gradient-to-position: 100%;\n}\r\n.to-50\\%{\n  --tw-gradient-to-position: 50%;\n}\r\n.bg-\\[200\\%_auto\\]{\n  background-size: 200% auto;\n}\r\n.bg-\\[length\\:200\\%_100\\%\\]{\n  background-size: 200% 100%;\n}\r\n.bg-\\[length\\:200\\%_auto\\]{\n  background-size: 200% auto;\n}\r\n.bg-clip-padding{\n  background-clip: padding-box;\n}\r\n.bg-clip-text{\n  background-clip: text;\n}\r\n.bg-center{\n  background-position: center;\n}\r\n.bg-no-repeat{\n  background-repeat: no-repeat;\n}\r\n.fill-amber-500{\n  fill: #f59e0b;\n}\r\n.fill-blue-500{\n  fill: #3b82f6;\n}\r\n.fill-current{\n  fill: currentColor;\n}\r\n.fill-primary{\n  fill: hsl(var(--primary));\n}\r\n.fill-purple-500{\n  fill: #a855f7;\n}\r\n.stroke-black\\/10{\n  stroke: rgb(0 0 0 / 0.1);\n}\r\n.stroke-primary{\n  stroke: hsl(var(--primary));\n}\r\n.stroke-primary\\/10{\n  stroke: hsl(var(--primary) / 0.1);\n}\r\n.stroke-primary\\/5{\n  stroke: hsl(var(--primary) / 0.05);\n}\r\n.stroke-1{\n  stroke-width: 1;\n}\r\n.object-contain{\n  object-fit: contain;\n}\r\n.object-cover{\n  object-fit: cover;\n}\r\n.object-center{\n  object-position: center;\n}\r\n.p-0{\n  padding: 0px;\n}\r\n.p-0\\.5{\n  padding: 0.125rem;\n}\r\n.p-1{\n  padding: 0.25rem;\n}\r\n.p-1\\.5{\n  padding: 0.375rem;\n}\r\n.p-2{\n  padding: 0.5rem;\n}\r\n.p-2\\.5{\n  padding: 0.625rem;\n}\r\n.p-3{\n  padding: 0.75rem;\n}\r\n.p-4{\n  padding: 1rem;\n}\r\n.p-6{\n  padding: 1.5rem;\n}\r\n.p-7{\n  padding: 1.75rem;\n}\r\n.p-8{\n  padding: 2rem;\n}\r\n.p-\\[1px\\]{\n  padding: 1px;\n}\r\n.px-0{\n  padding-left: 0px;\n  padding-right: 0px;\n}\r\n.px-1{\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\r\n.px-1\\.5{\n  padding-left: 0.375rem;\n  padding-right: 0.375rem;\n}\r\n.px-2{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.px-2\\.5{\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\r\n.px-3{\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n.px-4{\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-5{\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\r\n.px-6{\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.px-\\[0\\.3rem\\]{\n  padding-left: 0.3rem;\n  padding-right: 0.3rem;\n}\r\n.py-0{\n  padding-top: 0px;\n  padding-bottom: 0px;\n}\r\n.py-0\\.5{\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\r\n.py-1{\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\r\n.py-1\\.5{\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\r\n.py-10{\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\r\n.py-12{\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\r\n.py-16{\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\r\n.py-2{\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-2\\.5{\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\r\n.py-20{\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\r\n.py-24{\n  padding-top: 6rem;\n  padding-bottom: 6rem;\n}\r\n.py-3{\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n.py-4{\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n.py-40{\n  padding-top: 10rem;\n  padding-bottom: 10rem;\n}\r\n.py-5{\n  padding-top: 1.25rem;\n  padding-bottom: 1.25rem;\n}\r\n.py-6{\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\r\n.py-8{\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\r\n.py-\\[0\\.2rem\\]{\n  padding-top: 0.2rem;\n  padding-bottom: 0.2rem;\n}\r\n.py-\\[0\\.8rem\\]{\n  padding-top: 0.8rem;\n  padding-bottom: 0.8rem;\n}\r\n.pb-0{\n  padding-bottom: 0px;\n}\r\n.pb-0\\.5{\n  padding-bottom: 0.125rem;\n}\r\n.pb-1{\n  padding-bottom: 0.25rem;\n}\r\n.pb-1\\.5{\n  padding-bottom: 0.375rem;\n}\r\n.pb-10{\n  padding-bottom: 2.5rem;\n}\r\n.pb-12{\n  padding-bottom: 3rem;\n}\r\n.pb-2{\n  padding-bottom: 0.5rem;\n}\r\n.pb-20{\n  padding-bottom: 5rem;\n}\r\n.pb-24{\n  padding-bottom: 6rem;\n}\r\n.pb-3{\n  padding-bottom: 0.75rem;\n}\r\n.pb-4{\n  padding-bottom: 1rem;\n}\r\n.pb-5{\n  padding-bottom: 1.25rem;\n}\r\n.pb-6{\n  padding-bottom: 1.5rem;\n}\r\n.pb-8{\n  padding-bottom: 2rem;\n}\r\n.pb-\\[1\\.6rem\\]{\n  padding-bottom: 1.6rem;\n}\r\n.pl-0{\n  padding-left: 0px;\n}\r\n.pl-10{\n  padding-left: 2.5rem;\n}\r\n.pl-2{\n  padding-left: 0.5rem;\n}\r\n.pl-2\\.5{\n  padding-left: 0.625rem;\n}\r\n.pl-3{\n  padding-left: 0.75rem;\n}\r\n.pl-4{\n  padding-left: 1rem;\n}\r\n.pl-5{\n  padding-left: 1.25rem;\n}\r\n.pl-6{\n  padding-left: 1.5rem;\n}\r\n.pl-8{\n  padding-left: 2rem;\n}\r\n.pr-0{\n  padding-right: 0px;\n}\r\n.pr-1{\n  padding-right: 0.25rem;\n}\r\n.pr-1\\.5{\n  padding-right: 0.375rem;\n}\r\n.pr-2{\n  padding-right: 0.5rem;\n}\r\n.pr-3{\n  padding-right: 0.75rem;\n}\r\n.pr-4{\n  padding-right: 1rem;\n}\r\n.pr-8{\n  padding-right: 2rem;\n}\r\n.pt-0{\n  padding-top: 0px;\n}\r\n.pt-0\\.5{\n  padding-top: 0.125rem;\n}\r\n.pt-1{\n  padding-top: 0.25rem;\n}\r\n.pt-1\\.5{\n  padding-top: 0.375rem;\n}\r\n.pt-10{\n  padding-top: 2.5rem;\n}\r\n.pt-12{\n  padding-top: 3rem;\n}\r\n.pt-14{\n  padding-top: 3.5rem;\n}\r\n.pt-16{\n  padding-top: 4rem;\n}\r\n.pt-2{\n  padding-top: 0.5rem;\n}\r\n.pt-24{\n  padding-top: 6rem;\n}\r\n.pt-3{\n  padding-top: 0.75rem;\n}\r\n.pt-4{\n  padding-top: 1rem;\n}\r\n.pt-5{\n  padding-top: 1.25rem;\n}\r\n.pt-6{\n  padding-top: 1.5rem;\n}\r\n.pt-8{\n  padding-top: 2rem;\n}\r\n.pt-\\[0\\.14rem\\]{\n  padding-top: 0.14rem;\n}\r\n.pt-\\[56\\.25\\%\\]{\n  padding-top: 56.25%;\n}\r\n.text-left{\n  text-align: left;\n}\r\n.text-center{\n  text-align: center;\n}\r\n.text-right{\n  text-align: right;\n}\r\n.align-middle{\n  vertical-align: middle;\n}\r\n.font-inter{\n  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\r\n.font-mono{\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\r\n.font-sans{\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\r\n.text-2xl{\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\r\n.text-3xl{\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\r\n.text-4xl{\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\r\n.text-5xl{\n  font-size: 3rem;\n  line-height: 1;\n}\r\n.text-6xl{\n  font-size: 3.75rem;\n  line-height: 1;\n}\r\n.text-\\[0\\.95rem\\]{\n  font-size: 0.95rem;\n}\r\n.text-\\[0\\.9rem\\]{\n  font-size: 0.9rem;\n}\r\n.text-\\[11px\\]{\n  font-size: 11px;\n}\r\n.text-base{\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\r\n.text-lg{\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\r\n.text-sm{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.text-xl{\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\r\n.text-xs{\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.font-bold{\n  font-weight: 700;\n}\r\n.font-extrabold{\n  font-weight: 800;\n}\r\n.font-light{\n  font-weight: 300;\n}\r\n.font-medium{\n  font-weight: 500;\n}\r\n.font-normal{\n  font-weight: 400;\n}\r\n.font-semibold{\n  font-weight: 600;\n}\r\n.font-thin{\n  font-weight: 100;\n}\r\n.uppercase{\n  text-transform: uppercase;\n}\r\n.capitalize{\n  text-transform: capitalize;\n}\r\n.italic{\n  font-style: italic;\n}\r\n.leading-4{\n  line-height: 1rem;\n}\r\n.leading-6{\n  line-height: 1.5rem;\n}\r\n.leading-7{\n  line-height: 1.75rem;\n}\r\n.leading-\\[130\\%\\]{\n  line-height: 130%;\n}\r\n.leading-\\[22px\\]{\n  line-height: 22px;\n}\r\n.leading-\\[34px\\]{\n  line-height: 34px;\n}\r\n.leading-loose{\n  line-height: 2;\n}\r\n.leading-none{\n  line-height: 1;\n}\r\n.leading-normal{\n  line-height: 1.5;\n}\r\n.leading-relaxed{\n  line-height: 1.625;\n}\r\n.leading-tight{\n  line-height: 1.25;\n}\r\n.tracking-normal{\n  letter-spacing: 0em;\n}\r\n.tracking-tight{\n  letter-spacing: -0.025em;\n}\r\n.tracking-wider{\n  letter-spacing: 0.05em;\n}\r\n.tracking-widest{\n  letter-spacing: 0.1em;\n}\r\n.text-\\[\\#000000\\]{\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#0a0a0a\\]{\n  --tw-text-opacity: 1;\n  color: rgb(10 10 10 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#56eda1\\]{\n  --tw-text-opacity: 1;\n  color: rgb(86 237 161 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#fafafa\\]\\/90{\n  color: rgb(250 250 250 / 0.9);\n}\r\n.text-accent-foreground{\n  color: hsl(var(--accent-foreground));\n}\r\n.text-amber-500{\n  --tw-text-opacity: 1;\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\n}\r\n.text-amber-600{\n  --tw-text-opacity: 1;\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\n}\r\n.text-amber-700{\n  --tw-text-opacity: 1;\n  color: rgb(180 83 9 / var(--tw-text-opacity, 1));\n}\r\n.text-amber-800{\n  --tw-text-opacity: 1;\n  color: rgb(146 64 14 / var(--tw-text-opacity, 1));\n}\r\n.text-background{\n  color: hsl(var(--background));\n}\r\n.text-background\\/90{\n  color: hsl(var(--background) / 0.9);\n}\r\n.text-blue-200{\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-400{\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-500{\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-600{\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-700{\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-800{\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\r\n.text-card-foreground{\n  color: hsl(var(--card-foreground));\n}\r\n.text-current{\n  color: currentColor;\n}\r\n.text-destructive{\n  color: hsl(var(--destructive));\n}\r\n.text-destructive-foreground{\n  color: hsl(var(--destructive-foreground));\n}\r\n.text-destructive\\/90{\n  color: hsl(var(--destructive) / 0.9);\n}\r\n.text-emerald-500{\n  --tw-text-opacity: 1;\n  color: rgb(16 185 129 / var(--tw-text-opacity, 1));\n}\r\n.text-emerald-800{\n  --tw-text-opacity: 1;\n  color: rgb(6 95 70 / var(--tw-text-opacity, 1));\n}\r\n.text-foreground{\n  color: hsl(var(--foreground));\n}\r\n.text-foreground\\/80{\n  color: hsl(var(--foreground) / 0.8);\n}\r\n.text-foreground\\/90{\n  color: hsl(var(--foreground) / 0.9);\n}\r\n.text-gray-400{\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-500{\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-600{\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-900{\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\r\n.text-green-400{\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\r\n.text-green-500{\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\r\n.text-green-600{\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\r\n.text-green-700{\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\r\n.text-green-800{\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\r\n.text-inherit{\n  color: inherit;\n}\r\n.text-muted{\n  color: hsl(var(--muted));\n}\r\n.text-muted-foreground{\n  color: hsl(var(--muted-foreground));\n}\r\n.text-muted-foreground\\/50{\n  color: hsl(var(--muted-foreground) / 0.5);\n}\r\n.text-muted-foreground\\/70{\n  color: hsl(var(--muted-foreground) / 0.7);\n}\r\n.text-muted-foreground\\/80{\n  color: hsl(var(--muted-foreground) / 0.8);\n}\r\n.text-neutral-400\\/80{\n  color: rgb(163 163 163 / 0.8);\n}\r\n.text-neutral-600\\/70{\n  color: rgb(82 82 82 / 0.7);\n}\r\n.text-neutral-800{\n  --tw-text-opacity: 1;\n  color: rgb(38 38 38 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-500{\n  --tw-text-opacity: 1;\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-600{\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-700{\n  --tw-text-opacity: 1;\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-800{\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\r\n.text-popover-foreground{\n  color: hsl(var(--popover-foreground));\n}\r\n.text-primary{\n  color: hsl(var(--primary));\n}\r\n.text-primary-foreground{\n  color: hsl(var(--primary-foreground));\n}\r\n.text-primary\\/45{\n  color: hsl(var(--primary) / 0.45);\n}\r\n.text-primary\\/50{\n  color: hsl(var(--primary) / 0.5);\n}\r\n.text-primary\\/60{\n  color: hsl(var(--primary) / 0.6);\n}\r\n.text-primary\\/65{\n  color: hsl(var(--primary) / 0.65);\n}\r\n.text-primary\\/70{\n  color: hsl(var(--primary) / 0.7);\n}\r\n.text-primary\\/75{\n  color: hsl(var(--primary) / 0.75);\n}\r\n.text-primary\\/80{\n  color: hsl(var(--primary) / 0.8);\n}\r\n.text-primary\\/85{\n  color: hsl(var(--primary) / 0.85);\n}\r\n.text-primary\\/90{\n  color: hsl(var(--primary) / 0.9);\n}\r\n.text-primary\\/95{\n  color: hsl(var(--primary) / 0.95);\n}\r\n.text-purple-500{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\r\n.text-purple-800{\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\r\n.text-red-200{\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\r\n.text-red-500{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\r\n.text-red-600{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\r\n.text-red-800{\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\r\n.text-secondary-foreground{\n  color: hsl(var(--secondary-foreground));\n}\r\n.text-sidebar-foreground{\n  color: hsl(var(--sidebar-foreground));\n}\r\n.text-teal-700{\n  --tw-text-opacity: 1;\n  color: rgb(15 118 110 / var(--tw-text-opacity, 1));\n}\r\n.text-transparent{\n  color: transparent;\n}\r\n.text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-500{\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-800{\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\r\n.underline{\n  text-decoration-line: underline;\n}\r\n.underline-offset-4{\n  text-underline-offset: 4px;\n}\r\n.antialiased{\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\r\n.placeholder-zinc-500::placeholder{\n  --tw-placeholder-opacity: 1;\n  color: rgb(113 113 122 / var(--tw-placeholder-opacity, 1));\n}\r\n.opacity-0{\n  opacity: 0;\n}\r\n.opacity-100{\n  opacity: 1;\n}\r\n.opacity-20{\n  opacity: 0.2;\n}\r\n.opacity-25{\n  opacity: 0.25;\n}\r\n.opacity-50{\n  opacity: 0.5;\n}\r\n.opacity-60{\n  opacity: 0.6;\n}\r\n.opacity-65{\n  opacity: 0.65;\n}\r\n.opacity-70{\n  opacity: 0.7;\n}\r\n.opacity-75{\n  opacity: 0.75;\n}\r\n.opacity-80{\n  opacity: 0.8;\n}\r\n.opacity-90{\n  opacity: 0.9;\n}\r\n.shadow{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-md{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-none{\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-sm{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.outline-none{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.outline{\n  outline-style: solid;\n}\r\n.outline-primary{\n  outline-color: hsl(var(--primary));\n}\r\n.ring-0{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-1{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-2{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-\\[1\\.2px\\]{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1.2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-inset{\n  --tw-ring-inset: inset;\n}\r\n.ring-blue-600{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity, 1));\n}\r\n.ring-border{\n  --tw-ring-color: hsl(var(--border));\n}\r\n.ring-border\\/50{\n  --tw-ring-color: hsl(var(--border) / 0.5);\n}\r\n.ring-chart-2\\/30{\n  --tw-ring-color: hsl(var(--chart-2) / 0.3);\n}\r\n.ring-primary\\/10{\n  --tw-ring-color: hsl(var(--primary) / 0.1);\n}\r\n.ring-primary\\/20{\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n.ring-red-900\\/30{\n  --tw-ring-color: rgb(127 29 29 / 0.3);\n}\r\n.ring-offset-background{\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.blur{\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.blur-3xl{\n  --tw-blur: blur(64px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.blur-\\[3rem\\]{\n  --tw-blur: blur(3rem);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.blur-\\[90rem\\]{\n  --tw-blur: blur(90rem);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.blur-sm{\n  --tw-blur: blur(4px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.blur-xl{\n  --tw-blur: blur(24px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.invert{\n  --tw-invert: invert(100%);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur-\\[1px\\]{\n  --tw-backdrop-blur: blur(1px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-blur-lg{\n  --tw-backdrop-blur: blur(16px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-blur-sm{\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-\\[color\\2c box-shadow\\]{\n  transition-property: color,box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-none{\n  transition-property: none;\n}\r\n.transition-opacity{\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-transform{\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.duration-100{\n  transition-duration: 100ms;\n}\r\n.duration-1000{\n  transition-duration: 1000ms;\n}\r\n.duration-150{\n  transition-duration: 150ms;\n}\r\n.duration-200{\n  transition-duration: 200ms;\n}\r\n.duration-300{\n  transition-duration: 300ms;\n}\r\n.duration-500{\n  transition-duration: 500ms;\n}\r\n.duration-700{\n  transition-duration: 700ms;\n}\r\n.ease-in{\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\r\n.ease-in-out{\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-out{\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\r\n.will-change-\\[background-position\\]{\n  will-change: background-position;\n}\r\n@keyframes enter{\r\n\r\n  from{\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\r\n@keyframes exit{\r\n\r\n  to{\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\r\n.animate-in{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.fade-in{\n  --tw-enter-opacity: 0;\n}\r\n.fade-in-0{\n  --tw-enter-opacity: 0;\n}\r\n.zoom-in-95{\n  --tw-enter-scale: .95;\n}\r\n.duration-100{\n  animation-duration: 100ms;\n}\r\n.duration-1000{\n  animation-duration: 1000ms;\n}\r\n.duration-150{\n  animation-duration: 150ms;\n}\r\n.duration-200{\n  animation-duration: 200ms;\n}\r\n.duration-300{\n  animation-duration: 300ms;\n}\r\n.duration-500{\n  animation-duration: 500ms;\n}\r\n.duration-700{\n  animation-duration: 700ms;\n}\r\n.ease-in{\n  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\r\n.ease-in-out{\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-out{\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\r\n.running{\n  animation-play-state: running;\n}\r\n.paused{\n  animation-play-state: paused;\n}\r\n.\\[animation-direction\\:reverse\\]{\n  animation-direction: reverse;\n}\r\n.\\[backface-visibility\\:hidden\\]{\n  backface-visibility: hidden;\n}\r\n.\\[background-position\\:0_0\\]{\n  background-position: 0 0;\n}\r\n.\\[background-size\\:var\\(--shiny-width\\)_100\\%\\]{\n  background-size: var(--shiny-width) 100%;\n}\r\n.\\[mask-image\\:radial-gradient\\(75\\%_50\\%_at_top_center\\2c white\\2c transparent\\)\\]{\n  mask-image: radial-gradient(75% 50% at top center,white,transparent);\n}\r\n.\\[mask-image\\:radial-gradient\\(75\\%_70\\%_at_top_center\\2c white\\2c transparent\\)\\]{\n  mask-image: radial-gradient(75% 70% at top center,white,transparent);\n}\r\n.\\[perspective\\:1000px\\]{\n  perspective: 1000px;\n}\r\n.\\[transform\\:translate3d\\(0\\2c 0\\2c 0\\)\\]{\n  transform: translate3d(0,0,0);\n}\r\n.\\[transition\\:background-position_1s_cubic-bezier\\(\\.6\\2c \\.6\\2c 0\\2c 1\\)_infinite\\]{\n  transition: background-position 1s cubic-bezier(.6,.6,0,1) infinite;\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 6px;\r\n  height: 6px;\r\n  background: transparent;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background: transparent;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background: transparent;\r\n  border-radius: 3px;\r\n  transition: background 0.3s;\r\n}\r\n\r\n*:hover::-webkit-scrollbar-thumb,\r\n*:active::-webkit-scrollbar-thumb {\r\n  background: rgba(155, 155, 155, 0.5);\r\n}\r\n\r\n*:hover::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(155, 155, 155, 0.8);\r\n}\r\n\r\n* {\r\n  scrollbar-width: thin;\r\n  scrollbar-color: transparent transparent;\r\n}\r\n\r\n*:hover,\r\n*:active {\r\n  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;\r\n}\r\n\r\nbody {\r\n  -ms-overflow-style: -ms-autohiding-scrollbar;\r\n}\r\n\r\n/* Animation delay utilities */\r\n\r\n.grainy {\r\n  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwBAMAAAClLOS0AAAAElBMVEUAAAD8/vz08vT09vT8+vzs7uxH16TeAAAAAXRSTlMAQObYZgAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAuFJREFUOI0Vk+3NLiEIRG1B8ClAYAsQ2AIEt4D9ePtv5Xp/mZgYJ2fOFJKEfInkVWY2aglmQFkimRTV7MblYyVqD7HXyhKsSuPX12MeDhRHLtGvRG+P+B/S0Vu4OswR9tmvwNPyhdCDbVayJGads/WiUWcjCvCnruTBNHS9gmX2VzVbk7ZvB1gb1hkWFGl+A/n+/FowcO34U/XvKqZ/fHY+6vgRfU92XrOBUbGeeDfQmjWjdrK+frc6FdGReQhfSF5JvR29O2QrfNw1huTwlgsyXLo0u+5So82sgv7tsFZR2nxB6lXiquHrfD8nfYZ9SeT0LiuvSoVrxGY16pCNRZKqvwWsn5OHypPBELzohMCaRaa0ceTHYqe7X/gfJEEtKFbJpWoNqO+aS1cuTykGPpK5Ga48m6L3NefTr013KqYBQu929iP1oQ/7UwSR+i3zqruUmT84qmhzLpxyj7pr9kg7LKvqaXxZmdpn+6o8sHqSqojy02gU3U8q9PnpidiaLks0mbMYz+q2uVXsoBQ8bfURULYxRgZVYCHMv9F4OA7qxT2NPPpvGQ/sTDH2yznKh7E2AcErfcNsaIoN1izzbJiaY63x4QjUFdBSvDCvugPpu5xDny0jzEeuUQbcP1aGT9V90uixngTRLYNEIIZ6yOF1H8tm7rj2JxiefsVy53zGVy3ag5uuPsdufYOzYxLRxngKe7nhx3VAq54pmz/DK9/Q3aDam2Yt3hNXB4HuU87jKNd/CKZn77Qdn5QkXPfqSkhk7hGOXXB+7v09KbBbqdvxGqa0AqfK/atIrL2WXdAgXAJ43Wtwe/aIoacXezeGPMlhDOHDbSfHnaXsL2QzbT82GRwZuezdwcoWzx5pnOnGMUdHuiY7lhdyWzWiHnucLZQxYStMJbtcydHaQ6vtMbe0AcDbxG+QG14AL94xry4297xpy9Cpf1OoxZ740gHDfrK+gtsy0xabwJmfgtCeii79B6aj0SJeLbd7AAAAAElFTkSuQmCC);\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n.message > span{\n  font-size: 0.95rem;\n  line-height: 1.75rem;\n  color: hsl(var(--primary) / 0.85);\n}\r\n\r\n.message > span:last-child{\n  margin-bottom: 1rem;\n}\r\n\r\n.message > span:not(:first-child){\n  margin-top: 1rem;\n}\r\n\r\n@keyframes slide-up {\r\n  0% {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    transform: translateY(-8px);\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\n@keyframes fade-in-up {\r\n  0% {\r\n    transform: translateY(8px);\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.animate-slide-up {\r\n  animation: slide-up 0.3s ease-out forwards;\r\n}\r\n\r\n.animate-fade-in-up {\r\n  animation: fade-in-up 0.3s ease-out forwards;\r\n}\r\n\r\n.jumping-dots span {\r\n  position: relative;\r\n  bottom: 0px;\r\n  -webkit-animation: jump 1500ms infinite;\r\n  animation: jump 2s infinite;\r\n}\r\n.jumping-dots .dot-1 {\r\n  -webkit-animation-delay: 200ms;\r\n  animation-delay: 200ms;\r\n}\r\n.jumping-dots .dot-2 {\r\n  -webkit-animation-delay: 400ms;\r\n  animation-delay: 400ms;\r\n}\r\n.jumping-dots .dot-3 {\r\n  -webkit-animation-delay: 600ms;\r\n  animation-delay: 600ms;\r\n}\r\n\r\n@-webkit-keyframes jump {\r\n  0% {\r\n    bottom: 0px;\r\n  }\r\n  20% {\r\n    bottom: 5px;\r\n  }\r\n  40% {\r\n    bottom: 0px;\r\n  }\r\n}\r\n\r\n@keyframes jump {\r\n  0% {\r\n    bottom: 0px;\r\n  }\r\n  20% {\r\n    bottom: 5px;\r\n  }\r\n  40% {\r\n    bottom: 0px;\r\n  }\r\n}\r\n\r\n.file\\:me-3::file-selector-button{\n  margin-inline-end: 0.75rem;\n}\r\n\r\n.file\\:inline-flex::file-selector-button{\n  display: inline-flex;\n}\r\n\r\n.file\\:h-7::file-selector-button{\n  height: 1.75rem;\n}\r\n\r\n.file\\:h-full::file-selector-button{\n  height: 100%;\n}\r\n\r\n.file\\:border-0::file-selector-button{\n  border-width: 0px;\n}\r\n\r\n.file\\:border-r::file-selector-button{\n  border-right-width: 1px;\n}\r\n\r\n.file\\:border-solid::file-selector-button{\n  border-style: solid;\n}\r\n\r\n.file\\:border-input::file-selector-button{\n  border-color: hsl(var(--input));\n}\r\n\r\n.file\\:bg-transparent::file-selector-button{\n  background-color: transparent;\n}\r\n\r\n.file\\:px-3::file-selector-button{\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n\r\n.file\\:text-sm::file-selector-button{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n\r\n.file\\:font-medium::file-selector-button{\n  font-weight: 500;\n}\r\n\r\n.file\\:not-italic::file-selector-button{\n  font-style: normal;\n}\r\n\r\n.file\\:text-foreground::file-selector-button{\n  color: hsl(var(--foreground));\n}\r\n\r\n.placeholder\\:text-muted-foreground::placeholder{\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.placeholder\\:text-muted-foreground\\/70::placeholder{\n  color: hsl(var(--muted-foreground) / 0.7);\n}\r\n\r\n.after\\:absolute::after{\n  content: var(--tw-content);\n  position: absolute;\n}\r\n\r\n.after\\:inset-0::after{\n  content: var(--tw-content);\n  inset: 0px;\n}\r\n\r\n.after\\:inset-y-0::after{\n  content: var(--tw-content);\n  top: 0px;\n  bottom: 0px;\n}\r\n\r\n.after\\:left-1\\/2::after{\n  content: var(--tw-content);\n  left: 50%;\n}\r\n\r\n.after\\:w-1::after{\n  content: var(--tw-content);\n  width: 0.25rem;\n}\r\n\r\n.after\\:-translate-x-1\\/2::after{\n  content: var(--tw-content);\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.first\\:mt-0:first-child{\n  margin-top: 0px;\n}\r\n\r\n.first\\:mt-4:first-child{\n  margin-top: 1rem;\n}\r\n\r\n.first\\:mt-6:first-child{\n  margin-top: 1.5rem;\n}\r\n\r\n.first\\:rounded-s-lg:first-child{\n  border-start-start-radius: 0.38rem;\n  border-end-start-radius: 0.38rem;\n}\r\n\r\n.last\\:mb-0:last-child{\n  margin-bottom: 0px;\n}\r\n\r\n.last\\:mb-2:last-child{\n  margin-bottom: 0.5rem;\n}\r\n\r\n.last\\:mb-4:last-child{\n  margin-bottom: 1rem;\n}\r\n\r\n.last\\:mb-6:last-child{\n  margin-bottom: 1.5rem;\n}\r\n\r\n.last\\:rounded-e-lg:last-child{\n  border-start-end-radius: 0.38rem;\n  border-end-end-radius: 0.38rem;\n}\r\n\r\n.last\\:border-b-0:last-child{\n  border-bottom-width: 0px;\n}\r\n\r\n.last\\:pb-0:last-child{\n  padding-bottom: 0px;\n}\r\n\r\n.focus-within\\:border-primary\\/15:focus-within{\n  border-color: hsl(var(--primary) / 0.15);\n}\r\n\r\n.focus-within\\:font-semibold:focus-within{\n  font-weight: 600;\n}\r\n\r\n.focus-within\\:text-primary:focus-within{\n  color: hsl(var(--primary));\n}\r\n\r\n.focus-within\\:ring-2:focus-within{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus-within\\:ring-primary\\/20:focus-within{\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n\r\n.focus-within\\:ring-offset-2:focus-within{\n  --tw-ring-offset-width: 2px;\n}\r\n\r\n.hover\\:z-30:hover{\n  z-index: 30;\n}\r\n\r\n.hover\\:-translate-y-2:hover{\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.hover\\:scale-105:hover{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.hover\\:transform:hover{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.hover\\:cursor-pointer:hover{\n  cursor: pointer;\n}\r\n\r\n.hover\\:border-none:hover{\n  border-style: none;\n}\r\n\r\n.hover\\:border-primary:hover{\n  border-color: hsl(var(--primary));\n}\r\n\r\n.hover\\:border-primary\\/10:hover{\n  border-color: hsl(var(--primary) / 0.1);\n}\r\n\r\n.hover\\:border-primary\\/50:hover{\n  border-color: hsl(var(--primary) / 0.5);\n}\r\n\r\n.hover\\:border-red-900:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(127 29 29 / var(--tw-border-opacity, 1));\n}\r\n\r\n.hover\\:bg-\\[\\#006239\\]:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 98 57 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-\\[\\#0a0a0a\\]\\/90:hover{\n  background-color: rgb(10 10 10 / 0.9);\n}\r\n\r\n.hover\\:bg-\\[\\#fafafa\\]\\/95:hover{\n  background-color: rgb(250 250 250 / 0.95);\n}\r\n\r\n.hover\\:bg-accent:hover{\n  background-color: hsl(var(--accent));\n}\r\n\r\n.hover\\:bg-accent\\/10:hover{\n  background-color: hsl(var(--accent) / 0.1);\n}\r\n\r\n.hover\\:bg-accent\\/15:hover{\n  background-color: hsl(var(--accent) / 0.15);\n}\r\n\r\n.hover\\:bg-accent\\/40:hover{\n  background-color: hsl(var(--accent) / 0.4);\n}\r\n\r\n.hover\\:bg-accent\\/50:hover{\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n\r\n.hover\\:bg-accent\\/90:hover{\n  background-color: hsl(var(--accent) / 0.9);\n}\r\n\r\n.hover\\:bg-background:hover{\n  background-color: hsl(var(--background));\n}\r\n\r\n.hover\\:bg-background\\/20:hover{\n  background-color: hsl(var(--background) / 0.2);\n}\r\n\r\n.hover\\:bg-background\\/80:hover{\n  background-color: hsl(var(--background) / 0.8);\n}\r\n\r\n.hover\\:bg-background\\/90:hover{\n  background-color: hsl(var(--background) / 0.9);\n}\r\n\r\n.hover\\:bg-blue-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-destructive\\/90:hover{\n  background-color: hsl(var(--destructive) / 0.9);\n}\r\n\r\n.hover\\:bg-foreground\\/10:hover{\n  background-color: hsl(var(--foreground) / 0.1);\n}\r\n\r\n.hover\\:bg-foreground\\/5:hover{\n  background-color: hsl(var(--foreground) / 0.05);\n}\r\n\r\n.hover\\:bg-gray-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-green-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-green-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-muted\\/30:hover{\n  background-color: hsl(var(--muted) / 0.3);\n}\r\n\r\n.hover\\:bg-muted\\/40:hover{\n  background-color: hsl(var(--muted) / 0.4);\n}\r\n\r\n.hover\\:bg-muted\\/50:hover{\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n\r\n.hover\\:bg-orange-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-primary:hover{\n  background-color: hsl(var(--primary));\n}\r\n\r\n.hover\\:bg-primary\\/10:hover{\n  background-color: hsl(var(--primary) / 0.1);\n}\r\n\r\n.hover\\:bg-primary\\/5:hover{\n  background-color: hsl(var(--primary) / 0.05);\n}\r\n\r\n.hover\\:bg-primary\\/90:hover{\n  background-color: hsl(var(--primary) / 0.9);\n}\r\n\r\n.hover\\:bg-secondary\\/50:hover{\n  background-color: hsl(var(--secondary) / 0.5);\n}\r\n\r\n.hover\\:bg-secondary\\/80:hover{\n  background-color: hsl(var(--secondary) / 0.8);\n}\r\n\r\n.hover\\:bg-secondary\\/90:hover{\n  background-color: hsl(var(--secondary) / 0.9);\n}\r\n\r\n.hover\\:bg-sidebar-accent:hover{\n  background-color: hsl(var(--sidebar-accent));\n}\r\n\r\n.hover\\:bg-sidebar-accent\\/20:hover{\n  background-color: hsl(var(--sidebar-accent) / 0.2);\n}\r\n\r\n.hover\\:bg-sidebar-accent\\/25:hover{\n  background-color: hsl(var(--sidebar-accent) / 0.25);\n}\r\n\r\n.hover\\:bg-sidebar-accent\\/40:hover{\n  background-color: hsl(var(--sidebar-accent) / 0.4);\n}\r\n\r\n.hover\\:bg-sidebar-accent\\/60:hover{\n  background-color: hsl(var(--sidebar-accent) / 0.6);\n}\r\n\r\n.hover\\:bg-sidebar-accent\\/80:hover{\n  background-color: hsl(var(--sidebar-accent) / 0.8);\n}\r\n\r\n.hover\\:bg-sidebar-accent\\/95:hover{\n  background-color: hsl(var(--sidebar-accent) / 0.95);\n}\r\n\r\n.hover\\:bg-sidebar-primary:hover{\n  background-color: hsl(var(--sidebar-primary));\n}\r\n\r\n.hover\\:bg-sidebar-ring\\/20:hover{\n  background-color: hsl(var(--sidebar-ring) / 0.2);\n}\r\n\r\n.hover\\:bg-teal-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-transparent:hover{\n  background-color: transparent;\n}\r\n\r\n.hover\\:bg-zinc-800\\/50:hover{\n  background-color: rgb(39 39 42 / 0.5);\n}\r\n\r\n.hover\\:text-\\[\\#fafafa\\]:hover{\n  --tw-text-opacity: 1;\n  color: rgb(250 250 250 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-accent-foreground:hover{\n  color: hsl(var(--accent-foreground));\n}\r\n\r\n.hover\\:text-background:hover{\n  color: hsl(var(--background));\n}\r\n\r\n.hover\\:text-destructive:hover{\n  color: hsl(var(--destructive));\n}\r\n\r\n.hover\\:text-foreground:hover{\n  color: hsl(var(--foreground));\n}\r\n\r\n.hover\\:text-green-400:hover{\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-green-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-orange-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-primary:hover{\n  color: hsl(var(--primary));\n}\r\n\r\n.hover\\:text-primary\\/45:hover{\n  color: hsl(var(--primary) / 0.45);\n}\r\n\r\n.hover\\:text-primary\\/85:hover{\n  color: hsl(var(--primary) / 0.85);\n}\r\n\r\n.hover\\:text-red-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:underline:hover{\n  text-decoration-line: underline;\n}\r\n\r\n.hover\\:opacity-100:hover{\n  opacity: 1;\n}\r\n\r\n.hover\\:ring-0:hover{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\:mb-0:focus{\n  margin-bottom: 0px;\n}\r\n\r\n.focus\\:rounded-none:focus{\n  border-radius: 0px;\n}\r\n\r\n.focus\\:rounded-xl:focus{\n  border-radius: calc(var(--radius) + 1px);\n}\r\n\r\n.focus\\:bg-accent:focus{\n  background-color: hsl(var(--accent));\n}\r\n\r\n.focus\\:bg-accent\\/50:focus{\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n\r\n.focus\\:bg-foreground\\/10:focus{\n  background-color: hsl(var(--foreground) / 0.1);\n}\r\n\r\n.focus\\:bg-teal-100:focus{\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.focus\\:text-accent-foreground:focus{\n  color: hsl(var(--accent-foreground));\n}\r\n\r\n.focus\\:text-primary:focus{\n  color: hsl(var(--primary));\n}\r\n\r\n.focus\\:outline-none:focus{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n\r\n.focus\\:ring-0:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\:ring-1:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\:ring-2:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\:ring-primary\\/20:focus{\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n\r\n.focus\\:ring-ring:focus{\n  --tw-ring-color: hsl(var(--ring));\n}\r\n\r\n.focus\\:ring-offset-2:focus{\n  --tw-ring-offset-width: 2px;\n}\r\n\r\n.hover\\:focus\\:ring-0:focus:hover{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus-visible\\:z-10:focus-visible{\n  z-index: 10;\n}\r\n\r\n.focus-visible\\:border-ring:focus-visible{\n  border-color: hsl(var(--ring));\n}\r\n\r\n.focus-visible\\:border-ring\\/60:focus-visible{\n  border-color: hsl(var(--ring) / 0.6);\n}\r\n\r\n.focus-visible\\:bg-teal-100:focus-visible{\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.focus-visible\\:bg-transparent:focus-visible{\n  background-color: transparent;\n}\r\n\r\n.focus-visible\\:outline-none:focus-visible{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n\r\n.focus-visible\\:outline-0:focus-visible{\n  outline-width: 0px;\n}\r\n\r\n.focus-visible\\:ring-0:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus-visible\\:ring-1:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus-visible\\:ring-2:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus-visible\\:ring-\\[3px\\]:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus-visible\\:ring-destructive\\/20:focus-visible{\n  --tw-ring-color: hsl(var(--destructive) / 0.2);\n}\r\n\r\n.focus-visible\\:ring-primary:focus-visible{\n  --tw-ring-color: hsl(var(--primary));\n}\r\n\r\n.focus-visible\\:ring-primary\\/10:focus-visible{\n  --tw-ring-color: hsl(var(--primary) / 0.1);\n}\r\n\r\n.focus-visible\\:ring-primary\\/20:focus-visible{\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n\r\n.focus-visible\\:ring-ring:focus-visible{\n  --tw-ring-color: hsl(var(--ring));\n}\r\n\r\n.focus-visible\\:ring-ring\\/50:focus-visible{\n  --tw-ring-color: hsl(var(--ring) / 0.5);\n}\r\n\r\n.focus-visible\\:ring-offset-0:focus-visible{\n  --tw-ring-offset-width: 0px;\n}\r\n\r\n.focus-visible\\:ring-offset-1:focus-visible{\n  --tw-ring-offset-width: 1px;\n}\r\n\r\n.focus-visible\\:ring-offset-2:focus-visible{\n  --tw-ring-offset-width: 2px;\n}\r\n\r\n.focus-visible\\:ring-offset-background:focus-visible{\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n\r\n.disabled\\:pointer-events-none:disabled{\n  pointer-events: none;\n}\r\n\r\n.disabled\\:cursor-not-allowed:disabled{\n  cursor: not-allowed;\n}\r\n\r\n.disabled\\:bg-gray-100:disabled{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.disabled\\:text-gray-400:disabled{\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\r\n\r\n.disabled\\:opacity-100:disabled{\n  opacity: 1;\n}\r\n\r\n.disabled\\:opacity-50:disabled{\n  opacity: 0.5;\n}\r\n\r\n.disabled\\:ring-0:disabled{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.group:hover .group-hover\\:flex{\n  display: flex;\n}\r\n\r\n.group:hover .group-hover\\:hidden{\n  display: none;\n}\r\n\r\n.group:hover .group-hover\\:translate-x-0{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group:hover .group-hover\\:translate-y-\\[9\\%\\]{\n  --tw-translate-y: 9%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group:hover .group-hover\\:rotate-0{\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group:hover .group-hover\\:bg-\\[\\#3ECF8E\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(62 207 142 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.group:hover .group-hover\\:text-primary{\n  color: hsl(var(--primary));\n}\r\n\r\n.group:hover .group-hover\\:text-primary\\/80{\n  color: hsl(var(--primary) / 0.8);\n}\r\n\r\n.group:hover .group-hover\\:text-zinc-200{\n  --tw-text-opacity: 1;\n  color: rgb(228 228 231 / var(--tw-text-opacity, 1));\n}\r\n\r\n.group:hover .group-hover\\:underline{\n  text-decoration-line: underline;\n}\r\n\r\n.group:hover .group-hover\\:opacity-100{\n  opacity: 1;\n}\r\n\r\n.group:hover .group-hover\\:transition-all{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n\r\n.group:hover .group-hover\\:duration-700{\n  transition-duration: 700ms;\n}\r\n\r\n.group:hover .group-hover\\:ease-in{\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\r\n\r\n.group:hover .group-hover\\:duration-700{\n  animation-duration: 700ms;\n}\r\n\r\n.group:hover .group-hover\\:ease-in{\n  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\r\n\r\n.group.toaster .group-\\[\\.toaster\\]\\:border-border{\n  border-color: hsl(var(--border));\n}\r\n\r\n.group.toast .group-\\[\\.toast\\]\\:bg-muted{\n  background-color: hsl(var(--muted));\n}\r\n\r\n.group.toast .group-\\[\\.toast\\]\\:bg-primary{\n  background-color: hsl(var(--primary));\n}\r\n\r\n.group.toaster .group-\\[\\.toaster\\]\\:bg-background{\n  background-color: hsl(var(--background));\n}\r\n\r\n.group.toast .group-\\[\\.toast\\]\\:text-muted-foreground{\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.group.toast .group-\\[\\.toast\\]\\:text-primary-foreground{\n  color: hsl(var(--primary-foreground));\n}\r\n\r\n.group.toaster .group-\\[\\.toaster\\]\\:text-foreground{\n  color: hsl(var(--foreground));\n}\r\n\r\n.group.toaster .group-\\[\\.toaster\\]\\:shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n\r\n.peer:focus-visible ~ .peer-focus-visible\\:outline{\n  outline-style: solid;\n}\r\n\r\n.peer:focus-visible ~ .peer-focus-visible\\:outline-2{\n  outline-width: 2px;\n}\r\n\r\n.peer:focus-visible ~ .peer-focus-visible\\:outline-ring\\/70{\n  outline-color: hsl(var(--ring) / 0.7);\n}\r\n\r\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed{\n  cursor: not-allowed;\n}\r\n\r\n.peer:disabled ~ .peer-disabled\\:opacity-70{\n  opacity: 0.7;\n}\r\n\r\n.has-\\[\\>svg\\]\\:px-2\\.5:has(>svg){\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\r\n\r\n.has-\\[\\>svg\\]\\:px-3:has(>svg){\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n\r\n.has-\\[\\>svg\\]\\:px-4:has(>svg){\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n\r\n.has-\\[\\>svg\\]\\:pl-\\[10px\\]:has(>svg){\n  padding-left: 10px;\n}\r\n\r\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled]{\n  pointer-events: none;\n}\r\n\r\n.data-\\[state\\=active\\]\\:block[data-state=\"active\"]{\n  display: block;\n}\r\n\r\n.data-\\[state\\=active\\]\\:flex[data-state=\"active\"]{\n  display: flex;\n}\r\n\r\n.data-\\[state\\=inactive\\]\\:hidden[data-state=\"inactive\"]{\n  display: none;\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:h-px[data-panel-group-direction=\"vertical\"]{\n  height: 1px;\n}\r\n\r\n.data-\\[size\\=default\\]\\:h-9[data-size=\"default\"]{\n  height: 2.25rem;\n}\r\n\r\n.data-\\[size\\=sm\\]\\:h-8[data-size=\"sm\"]{\n  height: 2rem;\n}\r\n\r\n.data-\\[orientation\\=horizontal\\]\\:w-full[data-orientation=\"horizontal\"]{\n  width: 100%;\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:w-full[data-panel-group-direction=\"vertical\"]{\n  width: 100%;\n}\r\n\r\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"]{\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"]{\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"]{\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"]{\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[state\\=checked\\]\\:translate-x-5[data-state=\"checked\"]{\n  --tw-translate-x: 1.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"]{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[orientation\\=horizontal\\]\\:flex-row[data-orientation=\"horizontal\"]{\n  flex-direction: row;\n}\r\n\r\n.data-\\[orientation\\=vertical\\]\\:flex-col[data-orientation=\"vertical\"]{\n  flex-direction: column;\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:flex-col[data-panel-group-direction=\"vertical\"]{\n  flex-direction: column;\n}\r\n\r\n.data-\\[state\\=active\\]\\:border-0[data-state=\"active\"]{\n  border-width: 0px;\n}\r\n\r\n.data-\\[state\\=open\\]\\:border-t[data-state=\"open\"]{\n  border-top-width: 1px;\n}\r\n\r\n.data-\\[state\\=active\\]\\:border-sidebar-accent\\/80[data-state=\"active\"]{\n  border-color: hsl(var(--sidebar-accent) / 0.8);\n}\r\n\r\n.data-\\[state\\=open\\]\\:border-muted-foreground[data-state=\"open\"]{\n  border-color: hsl(var(--muted-foreground));\n}\r\n\r\n.data-\\[state\\=active\\]\\:bg-primary[data-state=\"active\"]{\n  background-color: hsl(var(--primary));\n}\r\n\r\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"]{\n  background-color: hsl(var(--primary));\n}\r\n\r\n.data-\\[state\\=completed\\]\\:bg-primary[data-state=\"completed\"]{\n  background-color: hsl(var(--primary));\n}\r\n\r\n.data-\\[state\\=inactive\\]\\:bg-sidebar-accent\\/80[data-state=\"inactive\"]{\n  background-color: hsl(var(--sidebar-accent) / 0.8);\n}\r\n\r\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"]{\n  background-color: hsl(var(--accent));\n}\r\n\r\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"]{\n  background-color: hsl(var(--secondary));\n}\r\n\r\n.data-\\[state\\=open\\]\\:bg-sidebar-accent\\/70[data-state=\"open\"]{\n  background-color: hsl(var(--sidebar-accent) / 0.7);\n}\r\n\r\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"]{\n  background-color: hsl(var(--muted));\n}\r\n\r\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"]{\n  background-color: hsl(var(--input));\n}\r\n\r\n.data-\\[inset\\]\\:pl-8[data-inset]{\n  padding-left: 2rem;\n}\r\n\r\n.data-\\[placeholder\\]\\:text-muted-foreground[data-placeholder]{\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.data-\\[state\\=active\\]\\:text-background[data-state=\"active\"]{\n  color: hsl(var(--background));\n}\r\n\r\n.data-\\[state\\=active\\]\\:text-primary-foreground[data-state=\"active\"]{\n  color: hsl(var(--primary-foreground));\n}\r\n\r\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"]{\n  color: hsl(var(--primary-foreground));\n}\r\n\r\n.data-\\[state\\=completed\\]\\:text-primary-foreground[data-state=\"completed\"]{\n  color: hsl(var(--primary-foreground));\n}\r\n\r\n.data-\\[state\\=inactive\\]\\:text-muted-foreground[data-state=\"inactive\"]{\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=\"open\"]{\n  color: hsl(var(--accent-foreground));\n}\r\n\r\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"]{\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.data-\\[variant\\=destructive\\]\\:text-destructive[data-variant=\"destructive\"]{\n  color: hsl(var(--destructive));\n}\r\n\r\n.data-\\[disabled\\]\\:opacity-50[data-disabled]{\n  opacity: 0.5;\n}\r\n\r\n.data-\\[state\\=active\\]\\:shadow[data-state=\"active\"]{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"]{\n  transition-duration: 300ms;\n}\r\n\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"]{\n  transition-duration: 500ms;\n}\r\n\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"]{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"]{\n  --tw-exit-opacity: 0;\n}\r\n\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"]{\n  --tw-enter-opacity: 0;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"]{\n  --tw-exit-scale: .95;\n}\r\n\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"]{\n  --tw-enter-scale: .95;\n}\r\n\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"]{\n  --tw-enter-translate-y: -0.5rem;\n}\r\n\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"]{\n  --tw-enter-translate-x: 0.5rem;\n}\r\n\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"]{\n  --tw-enter-translate-x: -0.5rem;\n}\r\n\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"]{\n  --tw-enter-translate-y: 0.5rem;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"]{\n  --tw-exit-translate-y: 100%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"]{\n  --tw-exit-translate-x: -100%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"]{\n  --tw-exit-translate-x: -50%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"]{\n  --tw-exit-translate-x: 100%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"]{\n  --tw-exit-translate-y: -100%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"]{\n  --tw-exit-translate-y: -48%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"]{\n  --tw-enter-translate-y: 100%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"]{\n  --tw-enter-translate-x: -100%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"]{\n  --tw-enter-translate-x: -50%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"]{\n  --tw-enter-translate-x: 100%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"]{\n  --tw-enter-translate-y: -100%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"]{\n  --tw-enter-translate-y: -48%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"]{\n  animation-duration: 300ms;\n}\r\n\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"]{\n  animation-duration: 500ms;\n}\r\n\r\n.\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1[data-slot=\"select-value\"] > *{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n\r\n.\\*\\:data-\\[slot\\=select-value\\]\\:flex[data-slot=\"select-value\"] > *{\n  display: flex;\n}\r\n\r\n.\\*\\:data-\\[slot\\=select-value\\]\\:items-center[data-slot=\"select-value\"] > *{\n  align-items: center;\n}\r\n\r\n.\\*\\:data-\\[slot\\=select-value\\]\\:gap-2[data-slot=\"select-value\"] > *{\n  gap: 0.5rem;\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  left: 0px;\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  height: 0.25rem;\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  width: 100%;\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.hover\\:data-\\[state\\=open\\]\\:translate-y-\\[8\\.5rem\\][data-state=\"open\"]:hover{\n  --tw-translate-y: 8.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10:focus[data-variant=\"destructive\"]{\n  background-color: hsl(var(--destructive) / 0.1);\n}\r\n\r\n.data-\\[variant\\=destructive\\]\\:focus\\:text-destructive:focus[data-variant=\"destructive\"]{\n  color: hsl(var(--destructive));\n}\r\n\r\n.group\\/stepper[data-orientation=\"horizontal\"] .group-data-\\[orientation\\=horizontal\\]\\/stepper\\:h-0\\.5{\n  height: 0.125rem;\n}\r\n\r\n.group\\/stepper[data-orientation=\"vertical\"] .group-data-\\[orientation\\=vertical\\]\\/stepper\\:h-12{\n  height: 3rem;\n}\r\n\r\n.group\\/stepper[data-orientation=\"vertical\"] .group-data-\\[orientation\\=vertical\\]\\/stepper\\:h-\\[calc\\(100\\%-1\\.5rem-0\\.25rem\\)\\]{\n  height: calc(100% - 1.5rem - 0.25rem);\n}\r\n\r\n.group\\/stepper[data-orientation=\"horizontal\"] .group-data-\\[orientation\\=horizontal\\]\\/stepper\\:w-full{\n  width: 100%;\n}\r\n\r\n.group\\/stepper[data-orientation=\"vertical\"] .group-data-\\[orientation\\=vertical\\]\\/stepper\\:w-0\\.5{\n  width: 0.125rem;\n}\r\n\r\n.group\\/stepper[data-orientation=\"horizontal\"] .group-data-\\[orientation\\=horizontal\\]\\/stepper\\:flex-1{\n  flex: 1 1 0%;\n}\r\n\r\n.group[data-expanded] .group-data-\\[expanded\\]\\:rotate-180{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group[data-expanded] .group-data-\\[expanded\\]\\:rotate-90{\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-90{\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group\\/step[data-state=\"completed\"] .group-data-\\[state\\=completed\\]\\/step\\:scale-0{\n  --tw-scale-x: 0;\n  --tw-scale-y: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group\\/step[data-state=\"completed\"] .group-data-\\[state\\=completed\\]\\/step\\:scale-100{\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.group\\/stepper[data-orientation=\"horizontal\"] .group-data-\\[orientation\\=horizontal\\]\\/stepper\\:flex-row{\n  flex-direction: row;\n}\r\n\r\n.group\\/stepper[data-orientation=\"vertical\"] .group-data-\\[orientation\\=vertical\\]\\/stepper\\:flex-col{\n  flex-direction: column;\n}\r\n\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rounded-b-none{\n  border-bottom-right-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\r\n\r\n.group\\/step[data-state=\"completed\"] .group-data-\\[state\\=completed\\]\\/step\\:bg-primary{\n  background-color: hsl(var(--primary));\n}\r\n\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:pb-1\\.5{\n  padding-bottom: 0.375rem;\n}\r\n\r\n.group\\/step[data-state=\"completed\"] .group-data-\\[state\\=completed\\]\\/step\\:opacity-0{\n  opacity: 0;\n}\r\n\r\n.group\\/step[data-state=\"completed\"] .group-data-\\[state\\=completed\\]\\/step\\:opacity-100{\n  opacity: 1;\n}\r\n\r\n.dark\\:block:is(.dark *){\n  display: block;\n}\r\n\r\n.dark\\:hidden:is(.dark *){\n  display: none;\n}\r\n\r\n.dark\\:divide-primary\\/10:is(.dark *) > :not([hidden]) ~ :not([hidden]){\n  border-color: hsl(var(--primary) / 0.1);\n}\r\n\r\n.dark\\:border-0:is(.dark *){\n  border-width: 0px;\n}\r\n\r\n.dark\\:border-\\[\\#34B27B\\]\\/15:is(.dark *){\n  border-color: rgb(52 178 123 / 0.15);\n}\r\n\r\n.dark\\:border-amber-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(146 64 14 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-blue-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-blue-900:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(30 58 138 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-destructive:is(.dark *){\n  border-color: hsl(var(--destructive));\n}\r\n\r\n.dark\\:border-emerald-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(6 95 70 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-gray-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-green-900:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(20 83 45 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-input:is(.dark *){\n  border-color: hsl(var(--input));\n}\r\n\r\n.dark\\:border-neutral-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(38 38 38 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-orange-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-primary\\/10:is(.dark *){\n  border-color: hsl(var(--primary) / 0.1);\n}\r\n\r\n.dark\\:border-primary\\/15:is(.dark *){\n  border-color: hsl(var(--primary) / 0.15);\n}\r\n\r\n.dark\\:border-primary\\/60:is(.dark *){\n  border-color: hsl(var(--primary) / 0.6);\n}\r\n\r\n.dark\\:border-purple-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-red-900:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(127 29 29 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-yellow-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:border-yellow-900:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(113 63 18 / var(--tw-border-opacity, 1));\n}\r\n\r\n.dark\\:bg-\\[\\#0a0a0a\\]:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(10 10 10 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.dark\\:bg-\\[\\#29292D\\]:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(41 41 45 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.dark\\:bg-\\[\\#fafafa\\]:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.dark\\:bg-accent\\/30:is(.dark *){\n  background-color: hsl(var(--accent) / 0.3);\n}\r\n\r\n.dark\\:bg-amber-900\\/30:is(.dark *){\n  background-color: rgb(120 53 15 / 0.3);\n}\r\n\r\n.dark\\:bg-amber-900\\/50:is(.dark *){\n  background-color: rgb(120 53 15 / 0.5);\n}\r\n\r\n.dark\\:bg-background:is(.dark *){\n  background-color: hsl(var(--background));\n}\r\n\r\n.dark\\:bg-background\\/60:is(.dark *){\n  background-color: hsl(var(--background) / 0.6);\n}\r\n\r\n.dark\\:bg-black:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.dark\\:bg-blue-900\\/20:is(.dark *){\n  background-color: rgb(30 58 138 / 0.2);\n}\r\n\r\n.dark\\:bg-blue-900\\/30:is(.dark *){\n  background-color: rgb(30 58 138 / 0.3);\n}\r\n\r\n.dark\\:bg-blue-900\\/50:is(.dark *){\n  background-color: rgb(30 58 138 / 0.5);\n}\r\n\r\n.dark\\:bg-destructive\\/60:is(.dark *){\n  background-color: hsl(var(--destructive) / 0.6);\n}\r\n\r\n.dark\\:bg-emerald-900\\/30:is(.dark *){\n  background-color: rgb(6 78 59 / 0.3);\n}\r\n\r\n.dark\\:bg-emerald-900\\/50:is(.dark *){\n  background-color: rgb(6 78 59 / 0.5);\n}\r\n\r\n.dark\\:bg-gray-700:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.dark\\:bg-gray-800\\/50:is(.dark *){\n  background-color: rgb(31 41 55 / 0.5);\n}\r\n\r\n.dark\\:bg-input\\/30:is(.dark *){\n  background-color: hsl(var(--input) / 0.3);\n}\r\n\r\n.dark\\:bg-neutral-900\\/30:is(.dark *){\n  background-color: rgb(23 23 23 / 0.3);\n}\r\n\r\n.dark\\:bg-neutral-900\\/50:is(.dark *){\n  background-color: rgb(23 23 23 / 0.5);\n}\r\n\r\n.dark\\:bg-orange-950\\/20:is(.dark *){\n  background-color: rgb(67 20 7 / 0.2);\n}\r\n\r\n.dark\\:bg-pink-600\\/10:is(.dark *){\n  background-color: rgb(219 39 119 / 0.1);\n}\r\n\r\n.dark\\:bg-primary:is(.dark *){\n  background-color: hsl(var(--primary));\n}\r\n\r\n.dark\\:bg-purple-600\\/10:is(.dark *){\n  background-color: rgb(147 51 234 / 0.1);\n}\r\n\r\n.dark\\:bg-purple-900\\/30:is(.dark *){\n  background-color: rgb(88 28 135 / 0.3);\n}\r\n\r\n.dark\\:bg-purple-900\\/50:is(.dark *){\n  background-color: rgb(88 28 135 / 0.5);\n}\r\n\r\n.dark\\:bg-red-900\\/20:is(.dark *){\n  background-color: rgb(127 29 29 / 0.2);\n}\r\n\r\n.dark\\:bg-red-900\\/30:is(.dark *){\n  background-color: rgb(127 29 29 / 0.3);\n}\r\n\r\n.dark\\:bg-sidebar-accent\\/15:is(.dark *){\n  background-color: hsl(var(--sidebar-accent) / 0.15);\n}\r\n\r\n.dark\\:bg-sidebar-primary\\/40:is(.dark *){\n  background-color: hsl(var(--sidebar-primary) / 0.4);\n}\r\n\r\n.dark\\:bg-transparent:is(.dark *){\n  background-color: transparent;\n}\r\n\r\n.dark\\:bg-yellow-900\\/30:is(.dark *){\n  background-color: rgb(113 63 18 / 0.3);\n}\r\n\r\n.dark\\:bg-yellow-900\\/50:is(.dark *){\n  background-color: rgb(113 63 18 / 0.5);\n}\r\n\r\n.dark\\:bg-yellow-950\\/20:is(.dark *){\n  background-color: rgb(66 32 6 / 0.2);\n}\r\n\r\n.dark\\:bg-opacity-40:is(.dark *){\n  --tw-bg-opacity: 0.4;\n}\r\n\r\n.dark\\:from-amber-950\\/20:is(.dark *){\n  --tw-gradient-from: rgb(69 26 3 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(69 26 3 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:from-black:is(.dark *){\n  --tw-gradient-from: #000 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:from-blue-500\\/5:is(.dark *){\n  --tw-gradient-from: rgb(59 130 246 / 0.05) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:from-blue-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-amber-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(245 158 11 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(245 158 11 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-blue-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(59 130 246 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-cyan-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(6 182 212 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(6 182 212 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-emerald-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(16 185 129 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(16 185 129 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-fuchsia-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(217 70 239 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(217 70 239 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-green-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(34 197 94 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(34 197 94 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-indigo-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(99 102 241 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(99 102 241 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-lime-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(132 204 22 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(132 204 22 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-orange-400\\/30:is(.dark *){\n  --tw-gradient-to: rgb(251 146 60 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(251 146 60 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-orange-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(249 115 22 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-pink-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(236 72 153 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(236 72 153 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-purple-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-red-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(239 68 68 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(239 68 68 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-rose-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(244 63 94 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(244 63 94 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-sky-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(14 165 233 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(14 165 233 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-teal-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(20 184 166 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(20 184 166 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-violet-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(139 92 246 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(139 92 246 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-white\\/80:is(.dark *){\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-yellow-400\\/30:is(.dark *){\n  --tw-gradient-to: rgb(250 204 21 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(250 204 21 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:via-yellow-500\\/30:is(.dark *){\n  --tw-gradient-to: rgb(234 179 8 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(234 179 8 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n\r\n.dark\\:to-\\[color-mix\\(in_oklab\\2c var\\(--primary\\)_75\\%\\2c var\\(--background\\)\\)\\]:is(.dark *){\n  --tw-gradient-to: color-mix(in oklab,var(--primary) 75%,var(--background)) var(--tw-gradient-to-position);\n}\r\n\r\n.dark\\:to-orange-950\\/20:is(.dark *){\n  --tw-gradient-to: rgb(67 20 7 / 0.2) var(--tw-gradient-to-position);\n}\r\n\r\n.dark\\:to-purple-500\\/5:is(.dark *){\n  --tw-gradient-to: rgb(168 85 247 / 0.05) var(--tw-gradient-to-position);\n}\r\n\r\n.dark\\:to-purple-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\n}\r\n\r\n.dark\\:stroke-white\\/10:is(.dark *){\n  stroke: rgb(255 255 255 / 0.1);\n}\r\n\r\n.dark\\:text-\\[\\#fafafa\\]:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(250 250 250 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-amber-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(252 211 77 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-amber-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-amber-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-background:is(.dark *){\n  color: hsl(var(--background));\n}\r\n\r\n.dark\\:text-blue-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-blue-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-blue-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-emerald-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-emerald-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(16 185 129 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-gray-100:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-gray-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-green-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-neutral-400\\/70:is(.dark *){\n  color: rgb(163 163 163 / 0.7);\n}\r\n\r\n.dark\\:text-neutral-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(115 115 115 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-orange-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(253 186 116 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-primary:is(.dark *){\n  color: hsl(var(--primary));\n}\r\n\r\n.dark\\:text-primary\\/60:is(.dark *){\n  color: hsl(var(--primary) / 0.6);\n}\r\n\r\n.dark\\:text-primary\\/75:is(.dark *){\n  color: hsl(var(--primary) / 0.75);\n}\r\n\r\n.dark\\:text-primary\\/80:is(.dark *){\n  color: hsl(var(--primary) / 0.8);\n}\r\n\r\n.dark\\:text-primary\\/90:is(.dark *){\n  color: hsl(var(--primary) / 0.9);\n}\r\n\r\n.dark\\:text-primary\\/95:is(.dark *){\n  color: hsl(var(--primary) / 0.95);\n}\r\n\r\n.dark\\:text-purple-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-purple-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-red-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-red-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-yellow-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-yellow-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:text-yellow-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\r\n\r\n.dark\\:\\[--color-border\\:color-mix\\(in_oklab\\2c var\\(--primary\\)_10\\%\\2c transparent\\)\\]:is(.dark *){\n  --color-border: color-mix(in oklab,var(--primary) 10%,transparent);\n}\r\n\r\n.dark\\:hover\\:bg-\\[\\#0a0a0a\\]\\/95:hover:is(.dark *){\n  background-color: rgb(10 10 10 / 0.95);\n}\r\n\r\n.dark\\:hover\\:bg-\\[\\#fafafa\\]\\/80:hover:is(.dark *){\n  background-color: rgb(250 250 250 / 0.8);\n}\r\n\r\n.dark\\:hover\\:bg-accent\\/50:hover:is(.dark *){\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n\r\n.dark\\:hover\\:bg-gray-800\\/50:hover:is(.dark *){\n  background-color: rgb(31 41 55 / 0.5);\n}\r\n\r\n.dark\\:hover\\:bg-input\\/50:hover:is(.dark *){\n  background-color: hsl(var(--input) / 0.5);\n}\r\n\r\n.dark\\:hover\\:bg-primary\\/90:hover:is(.dark *){\n  background-color: hsl(var(--primary) / 0.9);\n}\r\n\r\n.dark\\:hover\\:text-primary:hover:is(.dark *){\n  color: hsl(var(--primary));\n}\r\n\r\n.dark\\:focus-visible\\:ring-destructive\\/40:focus-visible:is(.dark *){\n  --tw-ring-color: hsl(var(--destructive) / 0.4);\n}\r\n\r\n.dark\\:data-\\[state\\=active\\]\\:border-inherit[data-state=\"active\"]:is(.dark *){\n  border-color: inherit;\n}\r\n\r\n.dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20:focus[data-variant=\"destructive\"]:is(.dark *){\n  background-color: hsl(var(--destructive) / 0.2);\n}\r\n\r\n@media (min-width: 640px){\r\n\r\n  .sm\\:inset-6{\n    inset: 1.5rem;\n  }\r\n\r\n  .sm\\:mb-0{\n    margin-bottom: 0px;\n  }\r\n\r\n  .sm\\:mr-3{\n    margin-right: 0.75rem;\n  }\r\n\r\n  .sm\\:mt-0{\n    margin-top: 0px;\n  }\r\n\r\n  .sm\\:mt-1{\n    margin-top: 0.25rem;\n  }\r\n\r\n  .sm\\:block{\n    display: block;\n  }\r\n\r\n  .sm\\:hidden{\n    display: none;\n  }\r\n\r\n  .sm\\:w-\\[var\\(--width\\)\\]{\n    width: var(--width);\n  }\r\n\r\n  .sm\\:w-auto{\n    width: auto;\n  }\r\n\r\n  .sm\\:w-fit{\n    width: fit-content;\n  }\r\n\r\n  .sm\\:w-full{\n    width: 100%;\n  }\r\n\r\n  .sm\\:max-w-5xl{\n    max-width: 64rem;\n  }\r\n\r\n  .sm\\:max-w-\\[425px\\]{\n    max-width: 425px;\n  }\r\n\r\n  .sm\\:max-w-\\[500px\\]{\n    max-width: 500px;\n  }\r\n\r\n  .sm\\:max-w-md{\n    max-width: 28rem;\n  }\r\n\r\n  .sm\\:max-w-sm{\n    max-width: 24rem;\n  }\r\n\r\n  .sm\\:max-w-xl{\n    max-width: 36rem;\n  }\r\n\r\n  .sm\\:flex-shrink-0{\n    flex-shrink: 0;\n  }\r\n\r\n  .sm\\:translate-y-\\[4\\.6rem\\]{\n    --tw-translate-y: 4.6rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\r\n\r\n  .sm\\:translate-y-\\[8\\.2rem\\]{\n    --tw-translate-y: 8.2rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\r\n\r\n  .sm\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\r\n\r\n  .sm\\:flex-row{\n    flex-direction: row;\n  }\r\n\r\n  .sm\\:flex-col{\n    flex-direction: column;\n  }\r\n\r\n  .sm\\:items-start{\n    align-items: flex-start;\n  }\r\n\r\n  .sm\\:items-center{\n    align-items: center;\n  }\r\n\r\n  .sm\\:justify-start{\n    justify-content: flex-start;\n  }\r\n\r\n  .sm\\:justify-end{\n    justify-content: flex-end;\n  }\r\n\r\n  .sm\\:justify-center{\n    justify-content: center;\n  }\r\n\r\n  .sm\\:gap-y-0{\n    row-gap: 0px;\n  }\r\n\r\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\r\n\r\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\r\n\r\n  .sm\\:rounded-lg{\n    border-radius: 0.38rem;\n  }\r\n\r\n  .sm\\:rounded-b-3xl{\n    border-bottom-right-radius: calc(var(--radius) + 6px);\n    border-bottom-left-radius: calc(var(--radius) + 6px);\n  }\r\n\r\n  .sm\\:px-12{\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\r\n\r\n  .sm\\:px-6{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\r\n\r\n  .sm\\:py-24{\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\r\n\r\n  .sm\\:py-32{\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n  }\r\n\r\n  .sm\\:pb-32{\n    padding-bottom: 8rem;\n  }\r\n\r\n  .sm\\:text-left{\n    text-align: left;\n  }\r\n\r\n  .sm\\:text-6xl{\n    font-size: 3.75rem;\n    line-height: 1;\n  }\r\n\r\n  .sm\\:text-7xl{\n    font-size: 4.5rem;\n    line-height: 1;\n  }\r\n\r\n  .sm\\:text-lg{\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\r\n\r\n  .sm\\:text-xl{\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\r\n\r\n@media (min-width: 768px){\r\n\r\n  .md\\:absolute{\n    position: absolute;\n  }\r\n\r\n  .md\\:order-1{\n    order: 1;\n  }\r\n\r\n  .md\\:col-span-10{\n    grid-column: span 10 / span 10;\n  }\r\n\r\n  .md\\:col-span-12{\n    grid-column: span 12 / span 12;\n  }\r\n\r\n  .md\\:col-span-2{\n    grid-column: span 2 / span 2;\n  }\r\n\r\n  .md\\:col-span-3{\n    grid-column: span 3 / span 3;\n  }\r\n\r\n  .md\\:col-span-8{\n    grid-column: span 8 / span 8;\n  }\r\n\r\n  .md\\:col-span-9{\n    grid-column: span 9 / span 9;\n  }\r\n\r\n  .md\\:mx-auto{\n    margin-left: auto;\n    margin-right: auto;\n  }\r\n\r\n  .md\\:mb-2{\n    margin-bottom: 0.5rem;\n  }\r\n\r\n  .md\\:mb-8{\n    margin-bottom: 2rem;\n  }\r\n\r\n  .md\\:ml-2{\n    margin-left: 0.5rem;\n  }\r\n\r\n  .md\\:mr-1{\n    margin-right: 0.25rem;\n  }\r\n\r\n  .md\\:mt-0{\n    margin-top: 0px;\n  }\r\n\r\n  .md\\:mt-20{\n    margin-top: 5rem;\n  }\r\n\r\n  .md\\:mt-32{\n    margin-top: 8rem;\n  }\r\n\r\n  .md\\:mt-4{\n    margin-top: 1rem;\n  }\r\n\r\n  .md\\:block{\n    display: block;\n  }\r\n\r\n  .md\\:flex{\n    display: flex;\n  }\r\n\r\n  .md\\:grid{\n    display: grid;\n  }\r\n\r\n  .md\\:hidden{\n    display: none;\n  }\r\n\r\n  .md\\:h-\\[21rem\\]{\n    height: 21rem;\n  }\r\n\r\n  .md\\:h-\\[300px\\]{\n    height: 300px;\n  }\r\n\r\n  .md\\:h-\\[30rem\\]{\n    height: 30rem;\n  }\r\n\r\n  .md\\:h-\\[40\\.5rem\\]{\n    height: 40.5rem;\n  }\r\n\r\n  .md\\:h-full{\n    height: 100%;\n  }\r\n\r\n  .md\\:h-screen{\n    height: 100vh;\n  }\r\n\r\n  .md\\:min-h-\\[300px\\]{\n    min-height: 300px;\n  }\r\n\r\n  .md\\:min-h-min{\n    min-height: min-content;\n  }\r\n\r\n  .md\\:w-12{\n    width: 3rem;\n  }\r\n\r\n  .md\\:w-3\\/4{\n    width: 75%;\n  }\r\n\r\n  .md\\:w-32{\n    width: 8rem;\n  }\r\n\r\n  .md\\:w-80{\n    width: 20rem;\n  }\r\n\r\n  .md\\:w-\\[85\\%\\]{\n    width: 85%;\n  }\r\n\r\n  .md\\:w-\\[95\\%\\]{\n    width: 95%;\n  }\r\n\r\n  .md\\:w-auto{\n    width: auto;\n  }\r\n\r\n  .md\\:w-fit{\n    width: fit-content;\n  }\r\n\r\n  .md\\:w-full{\n    width: 100%;\n  }\r\n\r\n  .md\\:max-w-3xl{\n    max-width: 48rem;\n  }\r\n\r\n  .md\\:max-w-7xl{\n    max-width: 80rem;\n  }\r\n\r\n  .md\\:max-w-\\[26rem\\]{\n    max-width: 26rem;\n  }\r\n\r\n  .md\\:max-w-full{\n    max-width: 100%;\n  }\r\n\r\n  .md\\:columns-2{\n    columns: 2;\n  }\r\n\r\n  .md\\:grid-cols-1{\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\r\n\r\n  .md\\:grid-cols-12{\n    grid-template-columns: repeat(12, minmax(0, 1fr));\n  }\r\n\r\n  .md\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\r\n\r\n  .md\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\r\n\r\n  .md\\:grid-cols-\\[100px_1fr\\]{\n    grid-template-columns: 100px 1fr;\n  }\r\n\r\n  .md\\:flex-row{\n    flex-direction: row;\n  }\r\n\r\n  .md\\:flex-col{\n    flex-direction: column;\n  }\r\n\r\n  .md\\:items-start{\n    align-items: flex-start;\n  }\r\n\r\n  .md\\:items-center{\n    align-items: center;\n  }\r\n\r\n  .md\\:justify-end{\n    justify-content: flex-end;\n  }\r\n\r\n  .md\\:justify-center{\n    justify-content: center;\n  }\r\n\r\n  .md\\:justify-between{\n    justify-content: space-between;\n  }\r\n\r\n  .md\\:gap-0{\n    gap: 0px;\n  }\r\n\r\n  .md\\:gap-1{\n    gap: 0.25rem;\n  }\r\n\r\n  .md\\:gap-1\\.5{\n    gap: 0.375rem;\n  }\r\n\r\n  .md\\:gap-2{\n    gap: 0.5rem;\n  }\r\n\r\n  .md\\:gap-3{\n    gap: 0.75rem;\n  }\r\n\r\n  .md\\:gap-4{\n    gap: 1rem;\n  }\r\n\r\n  .md\\:gap-6{\n    gap: 1.5rem;\n  }\r\n\r\n  .md\\:gap-8{\n    gap: 2rem;\n  }\r\n\r\n  .md\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\r\n\r\n  .md\\:space-y-16 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(4rem * var(--tw-space-y-reverse));\n  }\r\n\r\n  .md\\:overflow-hidden{\n    overflow: hidden;\n  }\r\n\r\n  .md\\:rounded-xl{\n    border-radius: calc(var(--radius) + 1px);\n  }\r\n\r\n  .md\\:rounded-b-2xl{\n    border-bottom-right-radius: calc(var(--radius) + 4px);\n    border-bottom-left-radius: calc(var(--radius) + 4px);\n  }\r\n\r\n  .md\\:border-b{\n    border-bottom-width: 1px;\n  }\r\n\r\n  .md\\:border-r{\n    border-right-width: 1px;\n  }\r\n\r\n  .md\\:border-t-0{\n    border-top-width: 0px;\n  }\r\n\r\n  .md\\:p-2{\n    padding: 0.5rem;\n  }\r\n\r\n  .md\\:p-4{\n    padding: 1rem;\n  }\r\n\r\n  .md\\:p-6{\n    padding: 1.5rem;\n  }\r\n\r\n  .md\\:p-8{\n    padding: 2rem;\n  }\r\n\r\n  .md\\:px-0{\n    padding-left: 0px;\n    padding-right: 0px;\n  }\r\n\r\n  .md\\:px-1{\n    padding-left: 0.25rem;\n    padding-right: 0.25rem;\n  }\r\n\r\n  .md\\:px-2{\n    padding-left: 0.5rem;\n    padding-right: 0.5rem;\n  }\r\n\r\n  .md\\:px-4{\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\r\n\r\n  .md\\:px-6{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\r\n\r\n  .md\\:py-0{\n    padding-top: 0px;\n    padding-bottom: 0px;\n  }\r\n\r\n  .md\\:py-1\\.5{\n    padding-top: 0.375rem;\n    padding-bottom: 0.375rem;\n  }\r\n\r\n  .md\\:py-12{\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\r\n\r\n  .md\\:py-16{\n    padding-top: 4rem;\n    padding-bottom: 4rem;\n  }\r\n\r\n  .md\\:py-20{\n    padding-top: 5rem;\n    padding-bottom: 5rem;\n  }\r\n\r\n  .md\\:py-32{\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n  }\r\n\r\n  .md\\:pb-20{\n    padding-bottom: 5rem;\n  }\r\n\r\n  .md\\:pb-6{\n    padding-bottom: 1.5rem;\n  }\r\n\r\n  .md\\:pl-0{\n    padding-left: 0px;\n  }\r\n\r\n  .md\\:pl-2{\n    padding-left: 0.5rem;\n  }\r\n\r\n  .md\\:pr-3{\n    padding-right: 0.75rem;\n  }\r\n\r\n  .md\\:pt-0{\n    padding-top: 0px;\n  }\r\n\r\n  .md\\:pt-1{\n    padding-top: 0.25rem;\n  }\r\n\r\n  .md\\:text-2xl{\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\r\n\r\n  .md\\:text-4xl{\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\r\n\r\n  .md\\:text-base{\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\r\n\r\n  .md\\:text-xl{\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\r\n\r\n  .md\\:leading-8{\n    line-height: 2rem;\n  }\r\n\r\n  .md\\:hover\\:-translate-y-2:hover{\n    --tw-translate-y: -0.5rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\r\n\r\n  .md\\:hover\\:-rotate-1:hover{\n    --tw-rotate: -1deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\r\n\r\n  .md\\:hover\\:rotate-1:hover{\n    --tw-rotate: 1deg;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\r\n\r\n  .md\\:hover\\:bg-sidebar-accent:hover{\n    background-color: hsl(var(--sidebar-accent));\n  }\n}\r\n\r\n@media (min-width: 1024px){\r\n\r\n  .lg\\:bottom-auto{\n    bottom: auto;\n  }\r\n\r\n  .lg\\:left-4{\n    left: 1rem;\n  }\r\n\r\n  .lg\\:left-\\[50\\%\\]{\n    left: 50%;\n  }\r\n\r\n  .lg\\:top-\\[50\\%\\]{\n    top: 50%;\n  }\r\n\r\n  .lg\\:col-span-3{\n    grid-column: span 3 / span 3;\n  }\r\n\r\n  .lg\\:col-span-9{\n    grid-column: span 9 / span 9;\n  }\r\n\r\n  .lg\\:mb-6{\n    margin-bottom: 1.5rem;\n  }\r\n\r\n  .lg\\:mt-0{\n    margin-top: 0px;\n  }\r\n\r\n  .lg\\:mt-1{\n    margin-top: 0.25rem;\n  }\r\n\r\n  .lg\\:block{\n    display: block;\n  }\r\n\r\n  .lg\\:flex{\n    display: flex;\n  }\r\n\r\n  .lg\\:grid{\n    display: grid;\n  }\r\n\r\n  .lg\\:hidden{\n    display: none;\n  }\r\n\r\n  .lg\\:h-\\[600px\\]{\n    height: 600px;\n  }\r\n\r\n  .lg\\:h-\\[90vh\\]{\n    height: 90vh;\n  }\r\n\r\n  .lg\\:h-auto{\n    height: auto;\n  }\r\n\r\n  .lg\\:h-fit{\n    height: fit-content;\n  }\r\n\r\n  .lg\\:w-\\[84\\%\\]{\n    width: 84%;\n  }\r\n\r\n  .lg\\:w-fit{\n    width: fit-content;\n  }\r\n\r\n  .lg\\:w-full{\n    width: 100%;\n  }\r\n\r\n  .lg\\:max-w-2xl{\n    max-width: 42rem;\n  }\r\n\r\n  .lg\\:max-w-3xl{\n    max-width: 48rem;\n  }\r\n\r\n  .lg\\:max-w-4xl{\n    max-width: 56rem;\n  }\r\n\r\n  .lg\\:max-w-5xl{\n    max-width: 64rem;\n  }\r\n\r\n  .lg\\:max-w-6xl{\n    max-width: 72rem;\n  }\r\n\r\n  .lg\\:max-w-7xl{\n    max-width: 80rem;\n  }\r\n\r\n  .lg\\:max-w-\\[90vw\\]{\n    max-width: 90vw;\n  }\r\n\r\n  .lg\\:max-w-full{\n    max-width: 100%;\n  }\r\n\r\n  .lg\\:max-w-lg{\n    max-width: 32rem;\n  }\r\n\r\n  .lg\\:max-w-md{\n    max-width: 28rem;\n  }\r\n\r\n  .lg\\:max-w-xl{\n    max-width: 36rem;\n  }\r\n\r\n  .lg\\:translate-x-\\[-50\\%\\]{\n    --tw-translate-x: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\r\n\r\n  .lg\\:translate-y-\\[-50\\%\\]{\n    --tw-translate-y: -50%;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\r\n\r\n  .lg\\:columns-4{\n    columns: 4;\n  }\r\n\r\n  .lg\\:grid-cols-12{\n    grid-template-columns: repeat(12, minmax(0, 1fr));\n  }\r\n\r\n  .lg\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\r\n\r\n  .lg\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\r\n\r\n  .lg\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\r\n\r\n  .lg\\:flex-row{\n    flex-direction: row;\n  }\r\n\r\n  .lg\\:items-center{\n    align-items: center;\n  }\r\n\r\n  .lg\\:justify-between{\n    justify-content: space-between;\n  }\r\n\r\n  .lg\\:gap-12{\n    gap: 3rem;\n  }\r\n\r\n  .lg\\:gap-2{\n    gap: 0.5rem;\n  }\r\n\r\n  .lg\\:gap-8{\n    gap: 2rem;\n  }\r\n\r\n  .lg\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\r\n\r\n  .lg\\:space-y-20 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(5rem * var(--tw-space-y-reverse));\n  }\r\n\r\n  .lg\\:space-y-8 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n  }\r\n\r\n  .lg\\:overflow-visible{\n    overflow: visible;\n  }\r\n\r\n  .lg\\:rounded-2xl{\n    border-radius: calc(var(--radius) + 4px);\n  }\r\n\r\n  .lg\\:rounded-xl{\n    border-radius: calc(var(--radius) + 1px);\n  }\r\n\r\n  .lg\\:rounded-b-2xl{\n    border-bottom-right-radius: calc(var(--radius) + 4px);\n    border-bottom-left-radius: calc(var(--radius) + 4px);\n  }\r\n\r\n  .lg\\:border{\n    border-width: 1px;\n  }\r\n\r\n  .lg\\:p-6{\n    padding: 1.5rem;\n  }\r\n\r\n  .lg\\:px-0{\n    padding-left: 0px;\n    padding-right: 0px;\n  }\r\n\r\n  .lg\\:px-8{\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\r\n\r\n  .lg\\:py-32{\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n  }\r\n\r\n  .lg\\:text-lg{\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\r\n\r\n  .lg\\:duration-200{\n    transition-duration: 200ms;\n    animation-duration: 200ms;\n  }\r\n\r\n  .lg\\:data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"]{\n    animation-name: enter;\n    animation-duration: 150ms;\n    --tw-enter-opacity: initial;\n    --tw-enter-scale: initial;\n    --tw-enter-rotate: initial;\n    --tw-enter-translate-x: initial;\n    --tw-enter-translate-y: initial;\n  }\r\n\r\n  .lg\\:data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"]{\n    animation-name: exit;\n    animation-duration: 150ms;\n    --tw-exit-opacity: initial;\n    --tw-exit-scale: initial;\n    --tw-exit-rotate: initial;\n    --tw-exit-translate-x: initial;\n    --tw-exit-translate-y: initial;\n  }\r\n\r\n  .lg\\:data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"]{\n    --tw-exit-opacity: 0;\n  }\r\n\r\n  .lg\\:data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"]{\n    --tw-enter-opacity: 0;\n  }\r\n\r\n  .lg\\:data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"]{\n    --tw-exit-scale: .95;\n  }\r\n\r\n  .lg\\:data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"]{\n    --tw-enter-scale: .95;\n  }\r\n\r\n  .lg\\:data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"]{\n    --tw-exit-translate-x: -50%;\n  }\r\n\r\n  .lg\\:data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"]{\n    --tw-exit-translate-y: -48%;\n  }\r\n\r\n  .lg\\:data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"]{\n    --tw-enter-translate-x: -50%;\n  }\r\n\r\n  .lg\\:data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"]{\n    --tw-enter-translate-y: -48%;\n  }\n}\r\n\r\n@media (min-width: 1280px){\r\n\r\n  .xl\\:text-8xl{\n    font-size: 6rem;\n    line-height: 1;\n  }\n}\r\n\r\n.rtl\\:space-x-reverse:where([dir=\"rtl\"], [dir=\"rtl\"] *) > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 1;\n}\r\n\r\n.\\[\\&\\:\\:-webkit-search-cancel-button\\]\\:appearance-none::-webkit-search-cancel-button{\n  appearance: none;\n}\r\n\r\n.\\[\\&\\:\\:-webkit-search-decoration\\]\\:appearance-none::-webkit-search-decoration{\n  appearance: none;\n}\r\n\r\n.\\[\\&\\:\\:-webkit-search-results-button\\]\\:appearance-none::-webkit-search-results-button{\n  appearance: none;\n}\r\n\r\n.\\[\\&\\:\\:-webkit-search-results-decoration\\]\\:appearance-none::-webkit-search-results-decoration{\n  appearance: none;\n}\r\n\r\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]){\n  padding-right: 0px;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:my-4:not(:first-child){\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:my-5:not(:first-child){\n  margin-top: 1.25rem;\n  margin-bottom: 1.25rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:my-8:not(:first-child){\n  margin-top: 2rem;\n  margin-bottom: 2rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:mb-2:not(:first-child){\n  margin-bottom: 0.5rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:mt-12:not(:first-child){\n  margin-top: 3rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:mt-2:not(:first-child){\n  margin-top: 0.5rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:mt-4:not(:first-child){\n  margin-top: 1rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:mt-5:not(:first-child){\n  margin-top: 1.25rem;\n}\r\n\r\n.\\[\\&\\:not\\(\\:first-child\\)\\]\\:mt-6:not(:first-child){\n  margin-top: 1.5rem;\n}\r\n\r\n.\\[\\&\\>code\\]\\:mt-6>code{\n  margin-top: 1.5rem;\n}\r\n\r\n.\\[\\&\\>code\\]\\:border-none>code{\n  border-style: none;\n}\r\n\r\n.\\[\\&\\>code\\]\\:bg-transparent>code{\n  background-color: transparent;\n}\r\n\r\n.\\[\\&\\>code\\]\\:font-mono>code{\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\r\n\r\n.\\[\\&\\>code\\]\\:text-\\[0\\.95rem\\]>code{\n  font-size: 0.95rem;\n}\r\n\r\n.\\[\\&\\>code\\]\\:font-normal>code{\n  font-weight: 400;\n}\r\n\r\n.\\[\\&\\>li\\]\\:mt-1>li{\n  margin-top: 0.25rem;\n}\r\n\r\n.\\[\\&\\>li\\]\\:text-primary\\/45>li{\n  color: hsl(var(--primary) / 0.45);\n}\r\n\r\n.\\[\\&\\>li\\]\\:text-primary\\/85>li{\n  color: hsl(var(--primary) / 0.85);\n}\r\n\r\n.\\[\\&\\>pre\\]\\:w-full>pre{\n  width: 100%;\n}\r\n\r\n.\\[\\&\\>pre\\]\\:whitespace-pre-wrap>pre{\n  white-space: pre-wrap;\n}\r\n\r\n.\\[\\&\\>pre\\]\\:break-words>pre{\n  overflow-wrap: break-word;\n}\r\n\r\n.\\[\\&\\>pre\\]\\:p-0>pre{\n  padding: 0px;\n}\r\n\r\n.\\[\\&\\>pre\\]\\:px-4>pre{\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n\r\n.\\[\\&\\>pre\\]\\:py-4>pre{\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n\r\n.\\[\\&\\>span\\]\\:line-clamp-1>span{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n\r\n.\\[\\&\\>span\\]\\:flex>span{\n  display: flex;\n}\r\n\r\n.\\[\\&\\>span\\]\\:items-center>span{\n  align-items: center;\n}\r\n\r\n.\\[\\&\\>span\\]\\:gap-2>span{\n  gap: 0.5rem;\n}\r\n\r\n.\\[\\&\\>span_svg\\]\\:shrink-0>span svg{\n  flex-shrink: 0;\n}\r\n\r\n.\\[\\&\\>span_svg\\]\\:text-muted-foreground\\/80>span svg{\n  color: hsl(var(--muted-foreground) / 0.8);\n}\r\n\r\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div{\n  --tw-translate-y: -3px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.\\[\\&\\>svg\\]\\:pointer-events-none>svg{\n  pointer-events: none;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:absolute>svg{\n  position: absolute;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:left-4>svg{\n  left: 1rem;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:top-4>svg{\n  top: 1rem;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:size-3>svg{\n  width: 0.75rem;\n  height: 0.75rem;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:h-3\\.5>svg{\n  height: 0.875rem;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:w-3\\.5>svg{\n  width: 0.875rem;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:text-destructive>svg{\n  color: hsl(var(--destructive));\n}\r\n\r\n.\\[\\&\\>svg\\]\\:text-foreground>svg{\n  color: hsl(var(--foreground));\n}\r\n\r\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~*{\n  padding-left: 1.75rem;\n}\r\n\r\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr{\n  border-bottom-width: 0px;\n}\r\n\r\n.\\[\\&\\[align\\=center\\]\\]\\:text-center[align=center]{\n  text-align: center;\n}\r\n\r\n.\\[\\&\\[align\\=right\\]\\]\\:text-right[align=right]{\n  text-align: right;\n}\r\n\r\n.\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90[data-panel-group-direction=vertical]>div{\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\>span\\>svg\\]\\:shrink-0 *[role=option]>span>svg{\n  flex-shrink: 0;\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\>span\\>svg\\]\\:text-muted-foreground\\/80 *[role=option]>span>svg{\n  color: hsl(var(--muted-foreground) / 0.8);\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\>span\\]\\:end-2 *[role=option]>span{\n  inset-inline-end: 0.5rem;\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\>span\\]\\:start-auto *[role=option]>span{\n  inset-inline-start: auto;\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\>span\\]\\:flex *[role=option]>span{\n  display: flex;\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\>span\\]\\:items-center *[role=option]>span{\n  align-items: center;\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\>span\\]\\:gap-2 *[role=option]>span{\n  gap: 0.5rem;\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\]\\:pe-8 *[role=option]{\n  padding-inline-end: 2rem;\n}\r\n\r\n.\\[\\&_\\*\\[role\\=option\\]\\]\\:ps-2 *[role=option]{\n  padding-inline-start: 0.5rem;\n}\r\n\r\n.\\[\\&_code\\]\\:whitespace-pre-wrap code{\n  white-space: pre-wrap;\n}\r\n\r\n.\\[\\&_code\\]\\:break-words code{\n  overflow-wrap: break-word;\n}\r\n\r\n.\\[\\&_p\\]\\:leading-relaxed p{\n  line-height: 1.625;\n}\r\n\r\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 svg:not([class*='size-']){\n  width: 1rem;\n  height: 1rem;\n}\r\n\r\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground svg:not([class*='text-']){\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.\\[\\&_svg\\]\\:pointer-events-none svg{\n  pointer-events: none;\n}\r\n\r\n.\\[\\&_svg\\]\\:size-4 svg{\n  width: 1rem;\n  height: 1rem;\n}\r\n\r\n.\\[\\&_svg\\]\\:shrink-0 svg{\n  flex-shrink: 0;\n}\r\n\r\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child{\n  border-width: 0px;\n}\r\n\r\n.\\[\\&_tr\\]\\:border-b tr{\n  border-bottom-width: 1px;\n}\r\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;AAQA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAYA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AASA;;;;AAUA;;;;;AAgBA;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+UA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;;;;AAIA;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;EAEE;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;;AAIF;;;;;;;;AAQA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AA6BA;;;;;;;;;;AAhBA;;;;AAaA;;;;AAaA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;;;AAcA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;AAIA;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAUA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAMF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;;;;;EAUA;;;;;;;;;;EAUA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EAEE;;;;;;AAMF;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA", "debugId": null}}]}