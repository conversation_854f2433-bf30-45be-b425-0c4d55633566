# core/utils/supabase_token_manager.py
import logging
import aiohttp
from typing import Dict, Optional, Any
from datetime import datetime
from core.db import Database
from core.models import SupabaseOrganization
from sqlalchemy import select

class SupabaseTokenManager:
    """Utility class for managing Supabase authentication tokens"""
    
    def __init__(self, refresh_url: str = None, db_connector=None):
        self.refresh_url = refresh_url
        self.db_connector = db_connector
    
    async def refresh_token(self, refresh_token: str, refresh_url: str = None, 
                           client_id: str = None, client_secret: str = None) -> Optional[Dict]:
        """
        Refresh an expired access token using the refresh token
        
        Args:
            refresh_token: The refresh token to use
            refresh_url: Optional URL override for token refresh endpoint
            client_id: Optional client ID for OAuth2 authentication
            client_secret: Optional client secret for OAuth2 authentication
            
        Returns:
            Dict containing new tokens or None if refresh failed
        """
        try:
            if not refresh_token:
                logging.error("No refresh token provided")
                return None
            
            url = refresh_url or self.refresh_url
            if not url:
                logging.error("No refresh URL provided")
                return None
            
            logging.info(f"Refreshing access token with refresh token: {refresh_token[:10]}...")
            
            # Prepare request data
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json"
            }
            
            # Prepare form data
            form_data = {
                "grant_type": "refresh_token",
                "refresh_token": refresh_token
            }
            
            # Add client credentials if provided
            if client_id:
                form_data["client_id"] = client_id
            if client_secret:
                form_data["client_secret"] = client_secret
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=form_data) as response:
                    if response.status in [200, 201]:
                        token_data = await response.json()
                        logging.info("Successfully refreshed token")
                        
                        # Validate the token data
                        access_token = token_data.get("access_token")
                        new_refresh_token = token_data.get("refresh_token", refresh_token)  # Use old refresh token if not provided
                        
                        if not access_token:
                            logging.error("Refresh response missing access_token")
                            return None
                        
                        logging.info(f"New access token: {access_token[:10]}...")
                        
                        return {
                            "access_token": access_token,
                            "refresh_token": new_refresh_token,
                            "token_type": token_data.get("token_type"),
                            "expires_in": token_data.get("expires_in")
                        }
                    else:
                        error_text = await response.text()
                        logging.error(f"Failed to refresh token. Status: {response.status}, Response: {error_text}")
                        return None
                        
        except Exception as e:
            logging.error(f"Error refreshing token: {str(e)}")
            return None
    
    async def save_tokens_to_db(self, organization_id: str, new_tokens: Dict[str, Any]) -> bool:
        """
        Save refreshed tokens to the database
        
        Args:
            organization_id: The organization ID (string)
            new_tokens: The new tokens to save
            
        Returns:
            bool: True if tokens were saved successfully, False otherwise
        """
        try:
            if not self.db_connector:
                logging.error("No database connector provided")
                return False
            
            # Ensure we have valid token values
            access_token = new_tokens.get("access_token")
            refresh_token = new_tokens.get("refresh_token")
            
            if not access_token or not refresh_token:
                logging.error("Missing access_token or refresh_token in new_tokens")
                return False
            
            # Log token values for debugging (mask most of the token)
            logging.info(f"Saving new access token: {access_token[:10]}... and refresh token: {refresh_token[:10]}...")
            
            async with self.db_connector.get_async_session() as session:
                async with session.begin():
                    # Get the organization by organization_id (string) - use first() to handle multiple rows
                    org_stmt = select(SupabaseOrganization).where(
                        SupabaseOrganization.organization_id == organization_id
                    ).order_by(SupabaseOrganization.last_updated.desc())  # Get the most recently updated one
                    org_result = await session.execute(org_stmt)
                    organization = org_result.scalars().first()  # Use first() instead of scalar_one_or_none()
                    
                    if not organization:
                        logging.error(f"No organization found with organization_id {organization_id}")
                        return False
                    
                    # Update the tokens
                    organization.access_token = access_token
                    organization.refresh_token = refresh_token
                    organization.last_updated = datetime.now().isoformat()
                    
                    await session.commit()
                    
                    logging.info(f"Successfully saved new tokens for organization {organization.organization_id}")
                    return True
            
        except Exception as e:
            logging.error(f"Error saving tokens to database: {str(e)}")
            return False

# Initialize the token manager with the database connector
db = Database()
supabase_token_manager = SupabaseTokenManager(db_connector=db)