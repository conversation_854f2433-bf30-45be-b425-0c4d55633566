// "use client";

// import { Particles } from "@/components/ui/particles";
// import { animate, motion, useMotionTemplate, useMotionValue } from "motion/react";
// import { useTheme } from "next-themes";
// import { useCallback, useEffect, useRef, useState } from "react";
// import MarketingBadge from "./badge";
// import LandingTextArea from "./input-prompt";

// // const hexCodes = [
// //   "#0C97B0", // Used multiple times (BubbleText hover effect + gradient)
// //   "#79e3f6", // Gradient start
// //   "#0D93AB", // Gradient end
// // ];

// const hexCodes = [
//   // "#FFFFFF",
//   // "#ECFBFE",
//   // "#D9F7FC",
//   // "#C6F3FB",
//   // "#B3EFF9",
//   // "#A0EBF8",
//   // "#8DE7F6",
//   // "#7AE3F5",
//   // "#67DFF4",
//   "#54DAF2",
//   "#41D6F1",
//   "#2ED2EF",
//   "#1BCEEE",
//   "#11C4E4",
//   "#10B4D1",
//   "#0EA3BE",
//   "#0D93AB",
//   "#0B8398",
//   "#0A7285",
//   // "#096272",
//   // "#07525F",
//   // "#06414C",
//   // "#043139",
//   // "#032126",
//   // "#011013"
// ]

// const Hero = () => {
//   const { resolvedTheme } = useTheme();
//   const [color, setColor] = useState("#ffffff");
//   const inputRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     setColor(resolvedTheme === "dark" ? "#ffffff" : "#000000");
//   }, [resolvedTheme]);

//   const handleInputClick = useCallback(() => {
//     if (window.innerWidth <= 768 && inputRef.current) {
//       setTimeout(() => {
//         inputRef.current?.scrollIntoView({
//           behavior: "smooth",
//           block: "center",
//           inline: "nearest",
//         });
//       }, 100);
//     }
//   }, []);

//   const hexCode = useMotionValue(hexCodes[0]);

//   useEffect(() => {
//     animate(hexCode, hexCodes, {
//       ease: "easeInOut",

//       duration: 10,

//       repeat: Infinity,

//       repeatType: "mirror",
//     });
//   }, []);

//   const backgroundImage = useMotionTemplate`radial-gradient(90% 50% at 50% -20%, ${hexCode}, rgba(255, 255, 255, 0))`;

//   return (
//     <section className="relative z-20 h-screen w-full overflow-hidden dark:bg-black">
//       {/* <div className="absolute inset-0 -z-10 hidden scale-y-[1] bg-[image:] md:flex"></div> */}
//       <motion.div
//         className="animate-radial-morph absolute inset-0 -z-10 hidden scale-y-[1] md:flex"
//         style={{
//           backgroundImage,
//         }}
//       />

//       {/* <div className="absolute inset-0 -z-10 bg-[image:radial-gradient(90%_50%_at_50%_-35%,rgba(93,208,220),rgba(255,255,255,0))] md:hidden"></div> */}

//       <svg
//         // className="absolute inset-0 -z-10 h-full w-full stroke-primary/5 [mask-image:radial-gradient(75%_50%_at_top_center,white,transparent)]"
//         className="absolute inset-0 -z-10 h-full w-full transform-gpu stroke-primary/10 [mask-image:radial-gradient(75%_70%_at_top_center,white,transparent)]"
//         aria-hidden="true"
//       >
//         <defs>
//           <pattern id="hero" width="80" height="80" x="50%" y="-1" patternUnits="userSpaceOnUse">
//             <path d="M.5 200V.5H200" fill="none"></path>
//           </pattern>
//         </defs>
//         <rect width="100%" height="100%" strokeWidth="0" fill="url(#hero)"></rect>
//       </svg>

//       <Particles className="bottom-96 mx-auto max-w-3xl" quantity={15} color={color} />

//       <div className="relative mx-auto my-24 flex w-full max-w-7xl flex-col items-center justify-center px-4 py-20 pt-14 sm:px-6 md:mt-32 md:pb-20 lg:px-8">
//         <div className="z-30 mt-8 flex w-full flex-col items-center justify-center space-y-6 text-center">
//           <MarketingBadge />

//           <div className="flex flex-col items-center justify-center space-y-16 w-full">
//             <div className="z-30">
//               {/* <h1 className="z-10 text-balance bg-gradient-to-br from-[#FFE9C5] via-[#0C97B0] to-[#11274c] bg-clip-text text-5xl font-bold tracking-tight text-transparent sm:text-7xl xl:text-8xl">
//                 Turn your ideas into apps with AI
//                 </h1> */}
//               <BubbleText>Turn your ideas into apps with AI</BubbleText>
//             </div>

//             <div ref={inputRef} className="relative z-20 w-full max-w-3xl mx-auto">
//               <LandingTextArea handleInputClick={handleInputClick} />
//             </div>

//             {/* <div className="hidden flex-col items-center justify-center gap-5 md:flex md:flex-row">
//               <div className="flex -space-x-3">
//                 {["4.jpg", "7.jpeg", "0.jpg", "5.jpg", "1.jpg", "6.jpeg"].map((image, index) => (
//                   <div
//                     key={index}
//                     className="z-20 h-9 w-9 overflow-hidden rounded-full border-2 border-background transition-all duration-500 ease-in-out hover:z-30 hover:-translate-y-2 hover:transform dark:border-primary/60"
//                   >
//                     <Image
//                       src={`/users/${image}`}
//                       alt={`User ${index + 1}`}
//                       width={40}
//                       height={40}
//                       className="object-cover"
//                     />
//                   </div>
//                 ))}
//               </div>
//               <div className="flex items-center">
//                 <p>
//                   Used by <span className="text-balance font-semibold text-primary">110,000+</span>{" "}
//                   developers & teams
//                 </p>
//               </div>
//             </div> */}
//           </div>
//         </div>

//         {/* {projectTypes.map((project) => (
//           <Card
//             key={project.id}
//             className="pointer-events-auto z-10 hidden aspect-video w-64 overflow-hidden border border-primary/10 bg-background/80 shadow-lg backdrop-blur-sm md:absolute md:block md:w-80"
//             style={{
//               top: project.top,
//               left: project.left,
//               transform: `rotate(${project.rotate}) scale(${project.scale})`,
//               zIndex: project.zIndex,
//               transition: "transform 0.3s ease-out",
//             }}
//             onMouseEnter={(e) => {
//               e.currentTarget.style.transform = `rotate(calc(${project.rotate} + 5deg)) scale(${project.scale})`;
//             }}
//             onMouseLeave={(e) => {
//               e.currentTarget.style.transform = `rotate(${project.rotate}) scale(${project.scale})`;
//             }}
//           >
//             <div className="relative flex h-full w-full flex-1 overflow-hidden rounded-xl border border-primary/10 bg-accent/50 p-1">
//               <a
//                 href={project.link}
//                 target="_blank"
//                 rel="noopener noreferrer"
//                 className="block h-full w-full transition-transform duration-300 hover:scale-105"
//               >
//                 <CardContent className="flex h-full flex-col p-0">
//                   <div className="relative w-full flex-1 overflow-hidden rounded-md border border-primary/10 bg-muted">
//                     {renderMedia(project)}
//                   </div>
//                 </CardContent>
//               </a>
//             </div>
//           </Card>
//         ))} */}
//       </div>
//     </section>
//   );
// };

// const BubbleText = ({ children }: { children: string }) => {
//   useEffect(() => {
//     const spans = document.querySelectorAll(".hover-text span") as NodeListOf<HTMLSpanElement>;

//     spans.forEach((span) => {
//       span.addEventListener("mouseenter", function (this: typeof span) {
//         this.style.fontWeight = "900";

//         this.style.color = "#0C97B0";

//         const leftNeighbor = this.previousElementSibling as HTMLSpanElement;

//         const rightNeighbor = this.nextElementSibling as HTMLSpanElement;

//         if (leftNeighbor) {
//           leftNeighbor.style.fontWeight = "700";

//           leftNeighbor.style.color = "#0C97B0";
//         }

//         if (rightNeighbor) {
//           rightNeighbor.style.fontWeight = "700";

//           rightNeighbor.style.color = "#0C97B0";
//         }
//       });

//       span.addEventListener("mouseleave", function (this: typeof span) {
//         this.style.fontWeight = "200";

//         this.style.color = "#0C97B0";

//         const leftNeighbor = this.previousElementSibling as HTMLSpanElement;

//         const rightNeighbor = this.nextElementSibling as HTMLSpanElement;

//         if (leftNeighbor) {
//           leftNeighbor.style.fontWeight = "200";

//           leftNeighbor.style.color = "#0C97B0";
//         }

//         if (rightNeighbor) {
//           rightNeighbor.style.fontWeight = "200";

//           rightNeighbor.style.color = "#0C97B0";
//         }
//       });
//     });
//   }, []);

//   return (
//     <h2 className="hover-text z-10 cursor-default text-balance bg-gradient-to-br from-[#FFE9C5] via-[#0C97B0] to-[#11274c] bg-clip-text text-center text-5xl font-thin tracking-tight text-transparent sm:text-7xl xl:text-8xl font-inter">
//       <Text>{children}</Text>
//     </h2>
//   );
// };

// const Text = ({ children }: { children: string }) => {
//   return (
//     <>
//       {children.split("").map((child, idx) => (
//         <span
//           className="font-inter"
//           style={{
//             transition: "0.35s font-weight, 0.35s color",
//           }}
//           key={idx}
//         >
//           {child}
//         </span>
//       ))}
//     </>
//   );
// };

// export default Hero;

"use client";

import { Particles } from "@/components/ui/particles";
import { useTheme } from "next-themes";
import { useCallback, useEffect, useRef, useState } from "react";
import MarketingBadge from "./badge";
import LandingTextArea from "./input-prompt";

const Hero = () => {
  const { resolvedTheme } = useTheme();
  const [color, setColor] = useState("#ffffff");
  const inputRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setColor(resolvedTheme === "dark" ? "#ffffff" : "#000000");
  }, [resolvedTheme]);

  const handleInputClick = useCallback(() => {
    if (window.innerWidth <= 768 && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }, 100);
    }
  }, []);

  return (
    <section className="relative z-20 w-full overflow-hidden dark:bg-black">
      <div className="absolute inset-0 -z-10 hidden bg-[image:radial-gradient(90%_50%_at_50%_-20%,rgba(93,208,220,0.7),rgba(255,255,255,0))] md:flex"></div>
      <div className="absolute inset-0 -z-10 bg-[image:radial-gradient(90%_50%_at_50%_-35%,rgba(93,208,220),rgba(255,255,255,0))] md:hidden"></div>

      <svg
        // className="absolute inset-0 -z-10 h-full w-full stroke-primary/5 [mask-image:radial-gradient(75%_50%_at_top_center,white,transparent)]"
        className="absolute inset-0 -z-10 h-full w-full transform-gpu stroke-primary/10 [mask-image:radial-gradient(75%_70%_at_top_center,white,transparent)]"
        aria-hidden="true"
      >
        <defs>
          <pattern id="hero" width="80" height="80" x="50%" y="-1" patternUnits="userSpaceOnUse">
            <path d="M.5 200V.5H200" fill="none"></path>
          </pattern>
        </defs>
        <rect width="100%" height="100%" strokeWidth="0" fill="url(#hero)"></rect>
      </svg>

      <Particles className="bottom-96 mx-auto max-w-3xl" quantity={15} color={color} />

      <div className="relative mx-auto my-24 flex w-full max-w-7xl flex-col items-center justify-center px-4 py-20 pt-14 sm:px-6 md:mt-32 md:pb-20 lg:px-8">
        <div className="z-30 mt-8 flex w-full flex-col items-center justify-center space-y-6 text-center">
          <MarketingBadge />

          <div className="flex flex-col items-center justify-center space-y-16">
            <div className="z-30">
              {/* <h1 className="z-10 text-balance bg-gradient-to-br from-[#FFE9C5] via-[#0C97B0] to-[#11274c] bg-clip-text text-5xl font-bold tracking-tight text-transparent sm:text-7xl xl:text-8xl">
                Turn your ideas into apps with AI
              </h1> */}
              <BubbleText>Turn your ideas into apps with AI</BubbleText>
            </div>

            <div ref={inputRef} className="relative z-20 w-full max-w-3xl">
              <LandingTextArea handleInputClick={handleInputClick} />
            </div>
          </div>
        </div>

        {/* {projectTypes.map((project) => (
          <Card
            key={project.id}
            className="pointer-events-auto z-10 hidden aspect-video w-64 overflow-hidden border border-primary/10 bg-background/80 shadow-lg backdrop-blur-sm md:absolute md:block md:w-80"
            style={{
              top: project.top,
              left: project.left,
              transform: `rotate(${project.rotate}) scale(${project.scale})`,
              zIndex: project.zIndex,
              transition: "transform 0.3s ease-out",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = `rotate(calc(${project.rotate} + 5deg)) scale(${project.scale})`;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = `rotate(${project.rotate}) scale(${project.scale})`;
            }}
          >
            <div className="relative flex h-full w-full flex-1 overflow-hidden rounded-xl border border-primary/10 bg-accent/50 p-1">
              <a
                href={project.link}
                target="_blank"
                rel="noopener noreferrer"
                className="block h-full w-full transition-transform duration-300 hover:scale-105"
              >
                <CardContent className="flex h-full flex-col p-0">
                  <div className="relative w-full flex-1 overflow-hidden rounded-md border border-primary/10 bg-muted">
                    {renderMedia(project)}
                  </div>
                </CardContent>
              </a>
            </div>
          </Card>
        ))} */}
      </div>
    </section>
  );
};

const BubbleText = ({ children }: { children: string }) => {
  useEffect(() => {
    const spans = document.querySelectorAll(".hover-text span") as NodeListOf<HTMLSpanElement>;

    spans.forEach((span) => {
      span.addEventListener("mouseenter", function (this: typeof span) {
        this.style.fontWeight = "900";

        this.style.color = "#0C97B0";

        const leftNeighbor = this.previousElementSibling as HTMLSpanElement;

        const rightNeighbor = this.nextElementSibling as HTMLSpanElement;

        if (leftNeighbor) {
          leftNeighbor.style.fontWeight = "700";

          leftNeighbor.style.color = "#0C97B0";
        }

        if (rightNeighbor) {
          rightNeighbor.style.fontWeight = "700";

          rightNeighbor.style.color = "#0C97B0";
        }
      });

      span.addEventListener("mouseleave", function (this: typeof span) {
        this.style.fontWeight = "200";

        this.style.color = "#0C97B0";

        const leftNeighbor = this.previousElementSibling as HTMLSpanElement;

        const rightNeighbor = this.nextElementSibling as HTMLSpanElement;

        if (leftNeighbor) {
          leftNeighbor.style.fontWeight = "200";

          leftNeighbor.style.color = "#0C97B0";
        }

        if (rightNeighbor) {
          rightNeighbor.style.fontWeight = "200";

          rightNeighbor.style.color = "#0C97B0";
        }
      });
    });
  }, []);

  return (
    <h2 className="hover-text z-10 cursor-default text-balance bg-gradient-to-br from-[#FFE9C5] via-[#0C97B0] to-[#11274c] bg-clip-text text-center font-inter text-6xl font-thin tracking-tight text-transparent sm:text-7xl xl:text-8xl">
      <Text>{children}</Text>
    </h2>
  );
};

const Text = ({ children }: { children: string }) => {
  return (
    <>
      {children.split("").map((child, idx) => (
        <span
          className="font-inter"
          style={{
            transition: "0.35s font-weight, 0.35s color",
          }}
          key={idx}
        >
          {child}
        </span>
      ))}
    </>
  );
};

export default Hero;
