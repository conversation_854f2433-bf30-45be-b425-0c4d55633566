module.exports = {

"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/features/app/welcome-modal.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_517094f8._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/welcome-modal.tsx [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/features/app/modal/create-project-modal.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_7442fa3f._.js",
  "server/chunks/ssr/node_modules__pnpm_7752a16f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/modal/create-project-modal.tsx [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/features/app/projects/project-card-setting-modal.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_1880f2b6._.js",
  "server/chunks/ssr/src_967436a1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/projects/project-card-setting-modal.tsx [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/features/app/settings.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_bc7b4244._.js",
  "server/chunks/ssr/8a1dc_zod_dist_esm_1a7ab0a0._.js",
  "server/chunks/ssr/b2692_react-icons_fa_index_mjs_11d9ab58._.js",
  "server/chunks/ssr/b2692_react-icons_lib_88c18abe._.js",
  "server/chunks/ssr/node_modules__pnpm_6d8abaca._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/features/app/settings.tsx [app-ssr] (ecmascript)");
    });
});
}}),

};