"use client";

import { errorToast } from "@/features/global/toast";
import { API_BASE_URL, createThread, getThread, getToken } from "@/lib/api";
import { debug } from "@/lib/debug";
import { useCurrentThreadStore, useSubscribeToAgentRunning } from "@/stores/current-thread";
import { Thread, WebSocketMessage } from "@/types/thread-message";
import { captureException } from "@sentry/nextjs";
import { useMutation, UseMutationResult, useQuery, useQueryClient } from "@tanstack/react-query";
import { nanoid } from "nanoid";
import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";
import { useNavigateFile } from "../../stores/navigate-file";
import { useProject } from "../project-provider";
import { useWebSocketManager } from "./hooks";
import { ErrorMessage, ThreadUpdateMessage } from "./types";

interface ThreadContextType {
  currentThread?: Thread;
  isLoading: boolean;
  createThreadFn: UseMutationResult<Thread, Error, void>;
  sendMessageFn: UseMutationResult<
    void,
    Error,
    {
      content: string;
      images?: File[];
      selectedPaths?: string[];
      promptSet?: number;
      model: "creative" | "standard" | "plan";
    }
  >;
  stopSession: () => Promise<boolean>;
  selectThreadId: () => number | null;
  selectFirstThread: () => number | undefined;
  contextLimitReached: boolean;
  setContextLimitReached: (isLimitReached: boolean) => void;
}

interface ThreadProviderProps {
  projectId: string;
  children: React.ReactNode;
}

export const ThreadContext = createContext<ThreadContextType | undefined>(undefined);

const getModelId = (model: "creative" | "standard" | "plan") => {
  switch (model) {
    case "creative":
      return "2";
    case "standard":
      return "1";
    case "plan":
      return "8";
    default:
      return "2";
  }
};

const AUTONOMOUS_ITERATIONS = 20;

export const ThreadProvider: React.FC<ThreadProviderProps> = ({ projectId, children }) => {
  const queryClient = useQueryClient();

  const [contextLimitReached, setContextLimitReached] = useState(false);

  const { threads, refetch } = useProject();

  const { setFile, setActiveTab } = useNavigateFile(
    useShallow((state) => ({
      setFile: state.setFile,
      setActiveTab: state.setActiveTab,
    })),
  );

  const {
    currentThreadId,
    setCurrentThreadId,
    addThreadMessages,
    setThreadMessages,
    setIsAgentRunning,
    setNoTokenModalOpen,
    setThreadName,
    setIsSoftgenProcessing,
  } = useCurrentThreadStore(
    useShallow((state) => ({
      currentThreadId: state.id,
      setCurrentThreadId: state.setId,
      addThreadMessages: state.addThreadMessages,
      setThreadMessages: state.setThreadMessages,
      setIsAgentRunning: state.setIsAgentRunning,
      setNoTokenModalOpen: state.setNoTokenModalOpen,
      setThreadName: state.setThreadName,
      setIsSoftgenProcessing: state.setIsSoftgenProcessing,
    })),
  );

  useSubscribeToAgentRunning((isRunning, prevWasRunning) => {
    console.debug(`Agent running state changed: ${isRunning} (prev: ${prevWasRunning})`);

    if (!isRunning && prevWasRunning) {
      refetch();
      setActiveTab("preview");
      setFile("");
    }
  });

  const { data: currentThread, isLoading } = useQuery<Thread>({
    queryKey: ["get-thread", currentThreadId],
    queryFn: async () => {
      if (!currentThreadId) return null;
      const thread = await getThread(currentThreadId);
      return thread;
    },
    enabled: !!currentThreadId,
    refetchOnWindowFocus: false,
  });

  // update thread messages when currentThread changes
  useEffect(() => {
    if (currentThread) {
      setThreadMessages(currentThread.messages);
    }
  }, [currentThread]);

  const onComplete = (content: string) => {
    addThreadMessages([
      {
        role: "assistant",
        content,
        message_id: nanoid(),
      },
    ]);
  };

  const onMessage = (message: WebSocketMessage) => {
    if (message.type === "thread_update") {
      const { data } = message as ThreadUpdateMessage;
      if (data?.messages) {
        setThreadMessages(data.messages);
      }
    }
  };

  const onError = (error: WebSocketMessage) => {
    console.error("WebSocket error:", error);

    // Show a toast notification for WebSocket errors
    if (error.type === "connection_error") {
      toast.error("Connection issue detected", {
        description:
          "The connection to the AI agent was interrupted. You may need to refresh the page if messages stop appearing.",
        duration: 10000,
      });
    }
  };

  const onErrorMessage = (errorMsg: ErrorMessage) => {
    if (typeof errorMsg.data === "string" && errorMsg.data.includes("No tokens available")) {
      setNoTokenModalOpen(true);
    }
    setIsAgentRunning(false);
  };

  const onSoftgenProcessing = (isProcessing: boolean) => {
    setIsSoftgenProcessing(isProcessing);
  };

  const {
    connect: connectWebSocket,
    disconnect: disconnectWebSocket,
    send: sendWebSocketMessage,
  } = useWebSocketManager({
    onComplete,
    onMessage,
    onErrorMessage,
    setIsAgentRunning,
    onError,
    onSoftgenProcessing,
    setContextLimitReached,
  });

  useEffect(() => {
    // Clean up WebSocket when thread changes to null/undefined
    if (!currentThreadId) {
      setThreadMessages([]);
      disconnectWebSocket(1000, "Thread cleared");
      setIsAgentRunning(false);
    }

    // cleanup on component unmount
    return () => {
      setThreadMessages([]);
      disconnectWebSocket(1000, currentThreadId ? "Thread disconnected" : "Component unmounted");
      setIsAgentRunning(false);
    };
  }, [currentThreadId, disconnectWebSocket]);
  const createThreadFn = useMutation({
    mutationFn: (): Promise<Thread> => createThread(projectId),
    onSuccess: (newThread: Thread) => {
      queryClient.invalidateQueries({ queryKey: ["threads", projectId] });
      setCurrentThreadId(newThread.thread_id);
      setThreadName(newThread.name);
      // return newThread;
    },
    onError: (error) => {
      errorToast(error.toString());
    },
  });

  const selectThreadId = useCallback(() => {
    if (currentThreadId) return currentThreadId;

    console.warn("No current thread ID available, selecting latest thread");

    const latestThread = threads?.[0];

    if (latestThread) {
      setCurrentThreadId(latestThread.thread_id);
      return latestThread.thread_id;
    }

    return null;
  }, [currentThreadId, setCurrentThreadId, threads]);

  const sendMessageFn = useMutation({
    mutationFn: async ({
      content,
      images = [],
      selectedPaths = [],
      promptSet = 10,
      model = "standard",
    }: {
      content: string;
      images?: File[];
      selectedPaths?: string[];
      promptSet?: number;
      model: "creative" | "standard" | "plan";
    }) => {
      try {
        setIsAgentRunning(true);

        let effectiveThreadId = selectThreadId();

        if (!effectiveThreadId) {
          debug("No thread ID available, creating a new thread...");

          try {
            const newThread = await createThreadFn.mutateAsync();
            effectiveThreadId = newThread.thread_id;
            debug(`Created new thread with ID: ${effectiveThreadId}`);
          } catch (error) {
            console.error("Failed to create a new thread:", error);
            throw new Error(
              "Could not create a new thread to send your message. Please try again.",
            );
          }
        }

        const newUserMessage = {
          role: "user" as const,
          content,
          message_id: nanoid(),
          type: "user_message",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          include_in_llm_message_history: true,
        };

        addThreadMessages([newUserMessage]);

        const encodeImageAsBase64 = (file: File) => {
          return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
              const result = reader.result?.toString().split(",")[1];
              resolve(result || "");
            };
            reader.onerror = (error) => reject(error);
          });
        };

        const encodedImages = await Promise.all(
          images.map((file: File) => encodeImageAsBase64(file)),
        );

        const messageData = {
          action: "new_message",
          message: content,
          objective_images: encodedImages,
          autonomous_iterations: AUTONOMOUS_ITERATIONS,
          mode: "autonomous",
          selected_paths: selectedPaths,
          prompt_set: promptSet,
          model_id: getModelId(model),
          chat_mode: model === "plan",
        };

        const token = getToken(); // Make sure to import getToken
        const wsUrl = `${API_BASE_URL.replace("http", "ws")}/start_thread_websocket/${projectId}/${effectiveThreadId}?token=${token}`;
        await connectWebSocket(wsUrl);
        sendWebSocketMessage(messageData);
      } catch (error) {
        captureException(error);
        errorToast("Failed to send message");
      } finally {
        setIsAgentRunning(false);
      }
    },
    onError: (error: Error) => {
      console.error("Error sending message:", error);
      errorToast(error.message);
      setIsAgentRunning(false);
    },
  });

  const stopSession = useCallback(async () => {
    try {
      sendWebSocketMessage({ action: "stop" });
      disconnectWebSocket(1000, "Session stopped by user");
      setIsAgentRunning(false);
      return true;
    } catch (error) {
      console.error("Error stopping session:", error);
      errorToast("Failed to stop session");
      return false;
    }
  }, [disconnectWebSocket, sendWebSocketMessage]);

  const selectFirstThread = useCallback(() => {
    const latestThread = threads?.[0];
    if (latestThread) {
      setCurrentThreadId(latestThread.thread_id);
      return latestThread.thread_id;
    }
  }, [threads, setCurrentThreadId]);

  const contextValue = useMemo<ThreadContextType>(
    () => ({
      currentThread,
      isLoading,
      createThreadFn,
      sendMessageFn,
      selectThreadId,
      stopSession,
      selectFirstThread,
      contextLimitReached,
      setContextLimitReached,
    }),
    [
      currentThread,
      isLoading,
      createThreadFn,
      sendMessageFn,
      selectThreadId,
      stopSession,
      selectFirstThread,
      contextLimitReached,
      setContextLimitReached,
    ],
  );

  return <ThreadContext.Provider value={contextValue}>{children}</ThreadContext.Provider>;
};

export const useThreadContext = () => {
  const context = useContext(ThreadContext);
  if (context === undefined) {
    throw new Error("useThreadContext must be used within a ThreadProvider");
  }
  return context;
};
