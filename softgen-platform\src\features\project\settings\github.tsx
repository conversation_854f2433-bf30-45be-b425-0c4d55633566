import BaseEmptyState from "@/components/base-empty-state";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loading from "@/components/ui/loading";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Typography from "@/components/ui/typography";
import TaskStatus from "@/features/global/task-status";
import { errorToast, successToast } from "@/features/global/toast";
import { addGithubCollaborator, getGithubInfo, gitForcePull, revertGithubCommit } from "@/lib/api";
import { formatTimestamp } from "@/lib/format-timestamp";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { useProject } from "@/providers/project-provider";
import { BrandGithubSolid, DangerTriangleSolid } from "@mynaui/icons-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ChevronDown, ChevronUp, Crown, GitBranch } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

type Commit = {
  hash: string;
  message: string;
  date: string;
};

const SettingsGithub = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [collaboratorUsername, setCollaboratorUsername] = useState("");
  const [expandedCommits, setExpandedCommits] = useState<Record<string, boolean>>({});
  const [confirmRevert, setConfirmRevert] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("history");
  const [revertType, setRevertType] = useState("standard");
  const [confirmText, setConfirmText] = useState("");
  const [showForcePullConfirm, setShowForcePullConfirm] = useState(false);
  const { user } = useAuth();
  const { projectId } = useProject();

  const {
    data: githubInfo,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["githubInfo", projectId],
    queryFn: () => getGithubInfo(projectId),
    staleTime: 1000 * 60,
  });

  const addCollaboratorMutation = useMutation({
    mutationFn: (): Promise<boolean> =>
      addGithubCollaborator(projectId, collaboratorUsername, githubInfo?.github_repo),
    onSuccess: (data) => {
      if (data) {
        successToast(`Invited ${collaboratorUsername} as a collaborator`);
      } else {
        errorToast("Invitation Failed", {
          description: "They may already have access or the username might be incorrect.",
        });
      }
      setCollaboratorUsername("");
    },
    onError: () => {
      errorToast("Invitation Failed", {
        description:
          "Unable to invite collaborator. They may already have access or the username might be incorrect.",
      });
    },
  });

  const revertCommitMutation = useMutation({
    mutationFn: () => revertGithubCommit(projectId, confirmRevert!, revertType === "full"),
    onSuccess: async () => {
      successToast("Building...", {
        description: "Running npm build after revert...",
      });

      successToast("Success", {
        description: `Successfully ${
          revertType === "full" ? "reset" : "reverted"
        } commit and rebuilt project`,
      });

      router.refresh();

      queryClient.invalidateQueries({ queryKey: ["githubInfo", projectId] });
      setConfirmRevert(null);
      setRevertType("standard");
      setConfirmText("");
    },
    onError: (error: Error) => {
      console.error("Revert error:", error);
      errorToast("Error", {
        description: `Failed to ${
          revertType === "full" ? "reset" : "revert"
        } commit${error.message ? `: ${error.message}` : ""}`,
      });
    },
  });

  const forcePullMutation = useMutation({
    mutationFn: () => gitForcePull(projectId),
    onSuccess: () => {
      successToast("Success", {
        description: "Successfully pulled latest changes from main branch",
      });
      setShowForcePullConfirm(false);
      queryClient.invalidateQueries({ queryKey: ["githubInfo", projectId] });
    },
    onError: (error: Error) => {
      errorToast("Error", {
        description: error.message || "Failed to pull changes",
      });
    },
  });

  const handleAddCollaborator = () => {
    addCollaboratorMutation.mutate();
  };

  const handleRevertCommit = (commitHash: string) => {
    setConfirmRevert(commitHash);
  };

  const confirmRevertCommit = () => {
    if (revertType === "full" && confirmText !== "sudo reset") {
      errorToast("Error", {
        description: "Please type sudo reset to proceed with full reset.",
      });
      return;
    }

    revertCommitMutation.mutate();
  };

  const toggleCommitExpansion = (commitHash: string) => {
    setExpandedCommits((prev) => ({
      ...prev,
      [commitHash]: !prev[commitHash],
    }));
  };

  const formatCommitMessage = (message: string) => {
    const [title, ...description] = message.split("\n").filter((line) => line.trim() !== "");
    return { title, description: description.join("\n") };
  };

  const isFirebaseSetupCommit = (message: string) => {
    return message.toLowerCase().includes("firebase setup completed");
  };

  const isCommitRevertible = (commit: Commit, commits: Commit[]) => {
    const firebaseSetupCommit = commits.find((c) => isFirebaseSetupCommit(c.message));
    if (!firebaseSetupCommit) return true;

    return new Date(commit.date) >= new Date(firebaseSetupCommit.date);
  };

  if (user?.userFromDb?.plan === "free-tier") {
    return (
      <BaseEmptyState
        icon={<Crown className="size-8 text-muted-foreground" />}
        title="GitHub is not available on the free tier"
        description="Please upgrade to a paid plan to access GitHub version history."
        buttonText="Upgrade"
        href="/pricing"
        className="min-h-[400px]"
      />
    );
  }

  console.log("error", error);

  if (error) {
    return (
      <div className="flex w-full flex-col items-start justify-start gap-3">
        <TaskStatus variant="error" className="flex items-center text-sm">
          Repository not initialized. Please initialize the repository first.
        </TaskStatus>
      </div>
    );
  }

  return (
    <div className="-mt-4 flex flex-col gap-4">
      <div className="space-y-6 py-4 pt-0 lg:mt-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mx-0 rounded-lg border-0">
          <TabsList className="flex h-auto w-full items-center justify-start gap-2 py-3">
            <TabsTrigger
              value="history"
              className="w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0"
            >
              Version History
            </TabsTrigger>
            <TabsTrigger
              value="github"
              className="w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0"
            >
              GitHub
            </TabsTrigger>
          </TabsList>

          <TabsContent value="history" className="mt-4 focus:rounded-none lg:mt-0">
            {isLoading ? (
              <div className="flex h-[500px] items-center justify-center gap-2">
                <Loading className="animate-spin" /> Loading commit history...
              </div>
            ) : githubInfo?.commits ? (
              <ScrollArea className="mx-0 h-[500px] rounded-md px-0">
                <div className="flex w-full flex-col gap-2">
                  {githubInfo.commits.map((commit: Commit) => {
                    const { title, description } = formatCommitMessage(commit.message);
                    const canRevert = isCommitRevertible(commit, githubInfo.commits);

                    return (
                      <>
                        <div
                          key={commit.hash}
                          className="flex w-full items-center justify-between gap-1 rounded-xl bg-sidebar-ring/10 p-3 px-4 last:mb-0"
                        >
                          <div className="flex w-full items-start justify-between">
                            <div className="min-w-0 flex-1">
                              <Typography.P className="mt-0 break-words text-sm text-primary">
                                {title}
                              </Typography.P>
                              <Typography.P className="mt-1 text-xs text-muted-foreground">
                                <span className="font-mono">{commit.hash.substring(0, 7)}</span> •{" "}
                                {formatTimestamp(commit.date, "commit-history")}
                              </Typography.P>
                            </div>
                            <Button
                              size="sm"
                              onClick={() => handleRevertCommit(commit.hash)}
                              className="ml-2"
                              disabled={!canRevert}
                            >
                              Revert
                            </Button>
                          </div>
                          {description && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleCommitExpansion(commit.hash)}
                              className="mt-1 h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
                            >
                              {expandedCommits[commit.hash] ? (
                                <>
                                  <ChevronUp className="mr-1 h-3 w-3" />
                                  Hide details
                                </>
                              ) : (
                                <>
                                  <ChevronDown className="mr-1 h-3 w-3" />
                                  Show details
                                </>
                              )}
                            </Button>
                          )}
                          {expandedCommits[commit.hash] && description && (
                            <Typography.P className="mt-2 whitespace-pre-wrap text-xs text-muted-foreground">
                              {description}
                            </Typography.P>
                          )}
                        </div>
                      </>
                    );
                  })}
                </div>
              </ScrollArea>
            ) : (
              <p>No commit history available</p>
            )}
          </TabsContent>

          <TabsContent value="github" className="mt-0 focus:rounded-none">
            {isLoading ? (
              <div className="flex h-[200px] items-center justify-center">
                <Loading className="animate-spin text-primary" />
              </div>
            ) : githubInfo && githubInfo.github_repo ? (
              <div className="flex w-full flex-col gap-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between gap-1 space-y-1 py-3">
                    <div className="flex flex-col gap-0">
                      <Typography.H5>Repository</Typography.H5>
                      <Typography.P>Visit your codebase on GitHub.</Typography.P>
                    </div>

                    <Button size="sm" className="h-8" asChild>
                      <Link href={githubInfo.github_repo} target="_blank" rel="noopener noreferrer">
                        <BrandGithubSolid className="text-muted" />
                        Visit Repository
                      </Link>
                    </Button>
                  </div>

                  <div className="flex w-full items-center justify-between gap-1 py-3">
                    <div className="flex w-full flex-col items-start justify-between space-y-3 sm:space-y-0">
                      <div className="flex items-center gap-2 text-base text-primary">
                        <GitBranch className="size-4" />
                        <Typography.H5>Force Pull Main Branch</Typography.H5>
                      </div>

                      <Typography.P>
                        Force pull latest changes from main branch. Local changes will be lost.
                      </Typography.P>
                    </div>
                    <Button size="sm" onClick={() => setShowForcePullConfirm(true)}>
                      <GitBranch className="size-4" />
                      Force Pull Main
                    </Button>
                  </div>
                </div>

                <div className="flex w-full flex-col items-center justify-between gap-1 space-y-2 py-3">
                  <div className="flex w-full flex-col gap-0.5">
                    <Typography.H5>Add Collaborator</Typography.H5>
                    <Typography.P className="text-sm text-muted-foreground">
                      This is a private repository. You must invite collaborators before they can
                      access the project.
                    </Typography.P>
                  </div>
                  <div className="flex w-full items-center gap-2">
                    <Input
                      id="collaborator-username"
                      value={collaboratorUsername}
                      onChange={(e) => setCollaboratorUsername(e.target.value)}
                      placeholder="GitHub username"
                      className="w-full"
                      disabled={addCollaboratorMutation.isPending}
                    />
                    <Button
                      onClick={handleAddCollaborator}
                      className="flex h-8 w-fit items-center gap-2"
                      disabled={addCollaboratorMutation.isPending || !collaboratorUsername.trim()}
                    >
                      {addCollaboratorMutation.isPending ? (
                        <div className="flex items-center gap-2">
                          <Loading className="size-4 animate-spin text-background" />
                          Inviting...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">Invite</div>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex h-[400px] flex-col items-center justify-center gap-6">
                <div className="flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2">
                  <DangerTriangleSolid className="size-8 text-muted-foreground" />
                </div>
                <div className="flex flex-col items-center justify-center gap-2">
                  <Typography.H5>Repository not initialized</Typography.H5>
                  <Typography.P className="mt-0 text-muted-foreground">
                    Please initialize the repository first.
                  </Typography.P>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <AlertDialog
          open={!!confirmRevert}
          onOpenChange={(open) => !open && setConfirmRevert(null)}
        >
          <AlertDialogContent className="lg:max-w-xl">
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Revert</AlertDialogTitle>
              <AlertDialogDescription>
                Choose how you want to revert to this commit:
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="">
              <RadioGroup
                value={revertType}
                onValueChange={(value) => setRevertType(value as "standard" | "full")}
                className="space-y-4"
              >
                <Card
                  className={cn(
                    "border-none bg-transparent shadow-none",
                    revertType === "standard" && "ring-2 ring-primary/20",
                  )}
                >
                  <CardContent className="pt-6">
                    <div className="flex cursor-pointer items-center space-x-2">
                      <RadioGroupItem value="standard" id="standard" />
                      <Label htmlFor="standard" className="cursor-pointer text-lg font-semibold">
                        Standard Revert
                      </Label>
                    </div>
                    <Typography.P className="mt-2 pl-6 text-sm text-muted-foreground">
                      This option will:
                      <ul className="mt-1 list-inside list-disc space-y-1">
                        <li>Create a new commit that undoes the changes</li>
                        <li>Keep the entire version history</li>
                        <li>Can be undone by reverting the revert commit</li>
                      </ul>
                    </Typography.P>
                    <TaskStatus variant="warning" className="mt-3 flex items-center text-sm">
                      This may lead to a confused project context as potentially conflicting commits
                      will remain.
                    </TaskStatus>
                  </CardContent>
                </Card>
                <Card
                  border={false}
                  className={cn(
                    "rounded-xl border border-red-900/50 bg-transparent hover:border-red-900",
                    revertType === "full" && "ring-2 ring-red-900/30",
                  )}
                >
                  <CardContent className="pt-6">
                    <div className="flex cursor-pointer items-center space-x-2">
                      <RadioGroupItem value="full" id="full" />
                      <Label htmlFor="full" className="cursor-pointer text-lg font-semibold">
                        Full Reset (Caution)
                      </Label>
                    </div>
                    <Typography.P className="mt-2 pl-6 text-sm text-muted-foreground">
                      This option will:
                      <ul className="mt-1 list-inside list-disc space-y-1">
                        <li>Clear all subsequent version history</li>
                        <li>Reset the project entirely to the selected commit state</li>
                        <li>Remove all commits after the selected one</li>
                      </ul>
                    </Typography.P>

                    <TaskStatus variant="error" className="mt-3 flex items-center text-sm">
                      This action cannot be undone. You&apos;ll lose all work after this commit.
                    </TaskStatus>
                  </CardContent>
                  {revertType === "full" && (
                    <CardFooter className="flex flex-col items-start gap-2 bg-transparent pt-4">
                      <p className="mt-0 text-sm font-normal text-primary/80">
                        Type <p className="inline font-bold text-primary">sudo reset</p> to proceed:
                      </p>
                      <Input
                        id="confirmText"
                        value={confirmText}
                        onChange={(e) => setConfirmText(e.target.value)}
                        placeholder="Type sudo reset here"
                        className="w-full border-red-900/50"
                      />
                    </CardFooter>
                  )}
                </Card>
              </RadioGroup>
            </div>
            <AlertDialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end">
              <Button
                variant="outline"
                className="w-full sm:w-fit"
                onClick={() => setConfirmRevert(null)}
                disabled={revertCommitMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                className={cn("w-full sm:w-fit")}
                variant={revertType === "full" ? "destructive" : "default"}
                onClick={confirmRevertCommit}
                disabled={
                  revertCommitMutation.isPending ||
                  (revertType === "full" && confirmText !== "sudo reset")
                }
              >
                {revertCommitMutation.isPending ? (
                  <>
                    <Loading className="size-4 animate-spin text-background" />
                    Reverting...
                  </>
                ) : revertType === "full" ? (
                  "Confirm Full Reset"
                ) : (
                  "Confirm Standard Revert"
                )}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <AlertDialog open={showForcePullConfirm} onOpenChange={setShowForcePullConfirm}>
          <AlertDialogContent className="bg-background px-5 py-4 text-primary">
            <AlertDialogHeader className="flex flex-col gap-2">
              <AlertDialogTitle>Force Pull from Main Branch</AlertDialogTitle>
              <AlertDialogDescription className="mt-6 text-sm">
                This action will:
                <ul className="mt-2 list-inside list-disc space-y-1 text-sm">
                  <li>Fetch the latest changes from remote</li>
                  <li>Reset your local branch to match remote main</li>
                  <li>Discard all local changes</li>
                  <li>This action cannot be undone</li>
                </ul>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex items-center gap-2">
              <AlertDialogCancel onClick={() => setShowForcePullConfirm(false)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction className="w-fit" onClick={() => forcePullMutation.mutate()}>
                Force Pull
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default SettingsGithub;
