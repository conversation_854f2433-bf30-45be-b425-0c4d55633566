import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Typography from "@/components/ui/typography";
import { useCheckAndStart } from "@/lib/api";
import { cn } from "@/lib/utils";
import { useProject } from "@/providers/project-provider";
import { Play } from "@mynaui/icons-react";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { ImSleepy } from "react-icons/im";

interface SandboxSleepingCardProps {
  className?: string;
}

export const SandboxSleepingCard = ({ className }: SandboxSleepingCardProps) => {
  const { project } = useProject();
  const startEnvironment = useCheckAndStart();
  const queryClient = useQueryClient();
  const [isWakingUp, setIsWakingUp] = useState(false);

  const handleWakeUp = async () => {
    if (!project?.project_id) return;

    setIsWakingUp(true);
    try {
      await startEnvironment.mutateAsync(project.project_id, {
        onSettled: () => {
          queryClient.invalidateQueries({ queryKey: ["get-project", project.project_id] });
        },
      });
    } catch (error) {
      console.error("Failed to wake up sandbox:", error);
    } finally {
      setIsWakingUp(false);
    }
  };

  return (
    <Card
      className={cn(
        "flex h-full w-full flex-col items-center justify-center rounded-none border-none shadow-lg",
        className,
      )}
      border={false}
    >
      <CardContent className="w-full max-w-md space-y-6 p-6">
        <div className="flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2">
          <ImSleepy className="size-8 text-muted-foreground" />
        </div>
        <div className="flex items-start gap-4">
          <div className="space-y-1.5">
            <Typography.H4>Sandbox is sleeping</Typography.H4>
            <Typography.P className="mt-0 leading-normal">
              Your development environment is currently paused to conserve resources
            </Typography.P>
          </div>
        </div>

        <Button onClick={handleWakeUp} disabled={isWakingUp} className="w-full" size="lg">
          {isWakingUp ? (
            <>
              <div className="mr-2 size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              Waking up...
            </>
          ) : (
            <>
              <Play className="size-6" />
              Wake up sandbox
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
