import logging
import asyncio
from fastapi import WebSocket
from starlette.websockets import WebSocketState
from sqlalchemy import select
from sqlalchemy.future import select as future_select
from core.models import ProjectThread, Project, ProjectAgentRun, User
from core.utils.error_store import error_store
import json
import sys

def optimize_messages_for_websocket(messages, max_payload_size_mb=1.0):
    """
    Optimize message payload for websocket transmission to prevent timeouts.

    Args:
        messages: List of message objects
        max_payload_size_mb: Maximum payload size in MB (default: 1.0MB)

    Returns:
        Optimized messages list and metadata about optimization
    """
    max_size_bytes = int(max_payload_size_mb * 1024 * 1024)

    if not messages:
        return messages, {"optimized": False, "original_count": 0, "final_count": 0}

    # Calculate current payload size
    current_size = sys.getsizeof(json.dumps(messages))

    if current_size <= max_size_bytes:
        return messages, {
            "optimized": False,
            "original_count": len(messages),
            "final_count": len(messages),
            "size_bytes": current_size
        }

    logging.warning(f"🚨 LARGE WEBSOCKET PAYLOAD: {current_size / 1024 / 1024:.1f}MB - Optimizing...")

    # Strategy 1: Keep only recent messages (last 50)
    recent_messages = messages[-50:] if len(messages) > 50 else messages
    recent_size = sys.getsizeof(json.dumps(recent_messages))

    if recent_size <= max_size_bytes:
        logging.info(f"✅ PAYLOAD OPTIMIZED: {len(messages)} → {len(recent_messages)} messages ({recent_size / 1024 / 1024:.1f}MB)")
        return recent_messages, {
            "optimized": True,
            "strategy": "recent_messages",
            "original_count": len(messages),
            "final_count": len(recent_messages),
            "size_bytes": recent_size
        }

    # Strategy 2: Truncate message content if still too large
    truncated_messages = []
    for msg in recent_messages:
        truncated_msg = msg.copy()
        if isinstance(truncated_msg.get('content'), str) and len(truncated_msg['content']) > 1000:
            truncated_msg['content'] = truncated_msg['content'][:1000] + "... [truncated for websocket]"
        truncated_messages.append(truncated_msg)

    final_size = sys.getsizeof(json.dumps(truncated_messages))
    logging.info(f"✅ PAYLOAD OPTIMIZED: {len(messages)} → {len(truncated_messages)} messages, content truncated ({final_size / 1024 / 1024:.1f}MB)")

    return truncated_messages, {
        "optimized": True,
        "strategy": "truncated_content",
        "original_count": len(messages),
        "final_count": len(truncated_messages),
        "size_bytes": final_size
    }

async def _send_thread_update(db, websocket: WebSocket, thread_id: int, current_user: User):
    """Internal function that performs the actual update sending"""
    if not websocket or websocket.client_state != WebSocketState.CONNECTED:
        return

    try:
        async with db.get_async_session() as session:
            # Get thread data
            stmt = future_select(ProjectThread).where(ProjectThread.thread_id == thread_id)
            result = await session.execute(stmt)
            thread = result.scalar_one_or_none()
            
            if not thread:
                return
            
            # Get project data
            stmt = future_select(Project).where(Project.project_id == thread.project_id)
            result = await session.execute(stmt)
            project = result.scalar_one_or_none()
            
            if not project:
                return

            user = current_user
            total_free_request = current_user.total_free_request
            free_total_token = current_user.free_total_token
            # if user is owner - pull latest data
            if user.id == project.owner_id:
                # Only fetch the specific columns we need
                stmt = future_select(User.total_free_request, User.free_total_token).where(User.id == project.owner_id)
                result = await session.execute(stmt)
                refreshed_data = result.one_or_none()

                if not refreshed_data:
                    return

                total_free_request, free_total_token = refreshed_data

            messages = json.loads(thread.messages)

            # Get latest agent run
            stmt = (
                select(ProjectAgentRun)
                .where(ProjectAgentRun.thread_id == thread_id)
                .order_by(ProjectAgentRun.creation_date.desc())
            )
            result = await session.execute(stmt)
            latest_run = result.scalar_one_or_none()

            latest_session_status = None
            if latest_run:
                latest_session_status = {
                    "project_agent_run": latest_run.run_id,
                    "status": latest_run.status,
                    "creation_date": latest_run.creation_date,
                    "objective": latest_run.objective
                }

            error = error_store.get_error(thread_id)
            if error:
                error_store.clear_error(thread_id)

            # Optimize messages payload to prevent websocket timeouts
            optimized_messages, optimization_info = optimize_messages_for_websocket(messages)

            if optimization_info["optimized"]:
                logging.info(f"📦 WEBSOCKET PAYLOAD OPTIMIZED for thread {thread_id}:")
                logging.info(f"  📊 Messages: {optimization_info['original_count']} → {optimization_info['final_count']}")
                logging.info(f"  📏 Size: {optimization_info['size_bytes'] / 1024 / 1024:.1f}MB")
                logging.info(f"  🔧 Strategy: {optimization_info['strategy']}")

            # Send update
            await websocket.send_json({
                "type": "thread_update",
                "data": {
                    "thread_id": thread.thread_id,
                    "project_id": thread.project_id,
                    "creation_date": thread.creation_date,
                    "last_updated_date": thread.last_updated_date,
                    "total_messages": len(messages),  # Original count for UI
                    "messages": optimized_messages,  # Optimized payload
                    "latest_session_status": latest_session_status,
                    "error": error,
                    "total_free_requests": total_free_request,
                    "free_total_token": free_total_token,
                    "payload_optimized": optimization_info["optimized"],  # Let frontend know if optimized
                    "displayed_messages": len(optimized_messages)  # Actual count being sent
                }
            })

    except Exception as e:
        logging.error(f"Error sending thread update: {str(e)}")

def send_thread_update(db, websocket: WebSocket, thread_id: int, current_user: User):
    """
    Non-blocking function to send thread updates via websocket.
    Creates a background task for the update operation.
    """
    return asyncio.create_task(_send_thread_update(db, websocket, thread_id, current_user))
