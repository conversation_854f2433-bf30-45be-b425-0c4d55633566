"use client";

import { Button } from "@/components/ui/button";
import Loading from "@/components/ui/loading";
import { Layout } from "@/features/global/layout";
import { subscriptionCheckout, type PlanType } from "@/lib/api";
import { useAuth } from "@/providers/auth-provider";
import { setCookieWithExpiry } from "@/utils/auth-utils";
import { getDetectedCountry, getMembershipPrice, isOecdCountry } from "@/utils/country-utils";
import { CheckCircleSolid } from "@mynaui/icons-react";
import { Wand2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const WholesalePricingPage = () => {
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState("US");

  useEffect(() => {
    const country = getDetectedCountry();
    setSelectedCountry(country);
  }, []);

  if (user.isLoading) {
    return <Loading />;
  }

  const isOecd = isOecdCountry(selectedCountry);
  const membershipPrice = getMembershipPrice(selectedCountry);

  const handleSubscribe = async () => {
    setLoading(true);

    const currentPlan = isOecd ? "wholesale_oecd" : "wholesale_non_oecd";

    setCookieWithExpiry("initial_plan", currentPlan, 600 * 60 * 1000);

    try {
      if (!user.kinde_id) {
        const loginUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/register?post_login_redirect_uri=${encodeURIComponent(`${process.env.NEXT_PUBLIC_APP_URL}/pricing/wholesale`)}`;
        console.log("No user, redirecting to register:", loginUrl);
        window.location.href = loginUrl;
        return;
      }

      console.log("Calling subscriptionCheckout with plan:", currentPlan);
      const checkout = await subscriptionCheckout(
        currentPlan as PlanType,
        user.kinde_id,
        false, // isUpgrade
        null, // toltReferral
        null, // discountCode
      );
      console.log("subscriptionCheckout response:", checkout);

      if (checkout?.url) {
        console.log("Redirecting to checkout URL:", checkout.url);
        window.location.href = checkout.url;
      } else {
        throw new Error("No checkout URL received in response");
      }
    } catch (error) {
      console.error("Error during checkout:", error);
      toast.error(
        `Failed to start checkout: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    } finally {
      console.log("Cleaning up loading state");
      setLoading(false);
    }
  };

  return (
    <Layout>
      <main className="mt-24 h-full w-full pt-12">
        <div className="mx-auto max-w-4xl px-4">
          <div className="space-y-4 text-center">
            <h1 className="text-4xl font-bold tracking-tight">Wholesale Membership</h1>
            <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
              Get exclusive access to our platform with our annual membership program
            </p>
          </div>

          <div className="mt-16 grid gap-8 md:grid-cols-1">
            <div className="relative overflow-hidden rounded-2xl border bg-card p-8 shadow-lg">
              <div className="flex flex-col space-y-6">
                <div className="space-y-2">
                  <h2 className="text-2xl font-bold">Annual Membership</h2>
                  <p className="text-muted-foreground">
                    Unlimited access to all platform features for one low annual fee
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-baseline space-x-2">
                      <span className="text-4xl font-bold">${membershipPrice}</span>
                      <span className="text-muted-foreground">per year</span>
                      {!isOecd && (
                        <span className="ml-2 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                          Special Rate
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <Button className="w-full" onClick={handleSubscribe} disabled={loading}>
                    {loading ? (
                      <Loading className="mr-2 h-4 w-4" />
                    ) : (
                      <Wand2 className="mr-2 h-4 w-4" />
                    )}
                    {!user.kinde_id ? "Register" : "Get Started"}
                  </Button>
                </div>

                <div className="space-y-4 pt-4">
                  <h3 className="font-medium">Membership Includes:</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircleSolid className="mr-2 h-5 w-5 shrink-0 text-green-500" />
                      <span>5 included projects</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircleSolid className="mr-2 h-5 w-5 shrink-0 text-green-500" />
                      <span>Wholesale AI usage pricing</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircleSolid className="mr-2 h-5 w-5 shrink-0 text-green-500" />
                      <span>Access to add-on services</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircleSolid className="mr-2 h-5 w-5 shrink-0 text-green-500" />
                      <span>Cooperative benefits over time</span>
                    </li>
                  </ul>
                </div>

                <div className="pt-4 text-sm text-muted-foreground">
                  <p className="mt-2 text-xs">
                    Pricing varies by country to ensure fair access to our platform worldwide.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </Layout>
  );
};

export default WholesalePricingPage;
