"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Loading from "@/components/ui/loading";
import Typography from "@/components/ui/typography";
import { errorToast, successToast } from "@/features/global/toast";
import { useCheckAndStart } from "@/lib/api";
import { cn } from "@/lib/utils";
import { useProject } from "@/providers/project-provider";
import { DangerTriangleSolid } from "@mynaui/icons-react";
import { useCallback } from "react";

const SandboxUnavailable = () => {
  const { projectId } = useProject();

  const startEnvironment = useCheckAndStart();

  const zendeskUrl = "https://softgen.zendesk.com/hc/en-us/requests/new";

  const handleRetry = useCallback(async () => {
    try {
      await startEnvironment.mutateAsync(projectId);
      successToast("Attempting to restart the workspace...");
    } catch (error) {
      console.error(error);
      errorToast("Unable to restart workspace. Please contact support.");
    }
  }, [startEnvironment, projectId]);

  return (
    <Card
      className={cn(
        "flex h-full w-full flex-col items-center justify-center rounded-none border-none shadow-lg",
      )}
      border={false}
    >
      <CardContent className="w-full max-w-md space-y-6 p-6">
        <div className="flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2">
          <DangerTriangleSolid className="size-8 text-muted-foreground" />
        </div>
        <div className="flex items-start gap-4">
          <div className="space-y-1.5">
            <Typography.H4>Workspace Unavailable</Typography.H4>

            <Typography.P className="mt-0 leading-normal">
              The environment can&apos;t start right now. Please try again or contact support.
            </Typography.P>
          </div>
        </div>

        <div className="space-y-3">
          <Button
            onClick={handleRetry}
            disabled={startEnvironment.isPending}
            className="w-full"
            size="lg"
          >
            {startEnvironment.isPending ? (
              <>
                <Loading className="text-background" /> Refreshing...
              </>
            ) : (
              "Retry"
            )}
          </Button>
          {/* 
          <span className="flex items-center">
            <span className="h-px flex-1 bg-gradient-to-r from-transparent to-primary/30" />
            <span className="shrink-0 px-4 text-muted-foreground">or</span>
            <span className="h-px flex-1 bg-gradient-to-l from-transparent to-primary/30" />
          </span> */}

          {startEnvironment.error?.message ===
            "Workspace is currently unavailable due to an error state" && (
            <Button
              onClick={() => window.open(zendeskUrl, "_blank")}
              size="lg"
              className="flex w-full items-center gap-2"
            >
              Create Support Ticket
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SandboxUnavailable;
