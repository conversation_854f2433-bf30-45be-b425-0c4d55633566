"use client";

import { useProjectAccess } from "@/hooks/use-project-access";
import { getProject, getProjectThreads, useCheckAndStart } from "@/lib/api";
import { Thread } from "@/types/thread-message";
import { useQuery } from "@tanstack/react-query";
import { notFound } from "next/navigation";
import { useTopLoader } from "nextjs-toploader";
import { createContext, useContext, useEffect, useRef } from "react";
import { CACHE_PARAMS } from "./query-provider";

export const SandBoxState = {
  CREATING: "creating",
  RESTORING: "restoring",
  DESTROYED: "destroyed",
  DESTROYING: "destroying",
  STARTED: "started",
  STOPPED: "stopped",
  STARTING: "starting",
  STOPPING: "stopping",
  ERROR: "error",
  BUILD_FAILED: "build_failed",
  PENDING_BUILD: "pending_build",
  BUILDING_SNAPSHOT: "building_snapshot",
  UNKNOWN: "unknown",
  PULLING_SNAPSHOT: "pulling_snapshot",
  ARCHIVING: "archiving",
  ARCHIVED: "archived",
};

export type SanboxPreviewState = "starting" | "sleeping" | "idle" | "unavailable";

export type Project = {
  project_id: string;
  name: string;
  creation_date: string;
  last_updated_date: string;
  sandbox_state: SanboxPreviewState;
  env_id: string;
  env_domain: string;
  env_url: string;
  frontend_page_routes: string[];
  github_repo: string;
  isRunning: number;
  preview_image_url: string | null;
  onboarding_completed: boolean;
  tech_stack_prompt: string[] | null;
  custom_instructions: string | null;
  users_prompt_Array: string[] | null;
  deployment: {
    is_deployed?: boolean;
    deployment_url?: string;
    last_deployed?: string;
    production_url?: string;
    vercel_token?: string | null;
  } | null;
  isPublic: boolean;
  owner_id: number;
  team_emails: string[];
  supabase?: {
    connected: boolean;
    organization_id?: string;
    organization_name?: string;
    supabase_project_id?: string;
    api_key?: string;
    api_url?: string;
    service_role_key?: string;
    database_password?: string;
    region?: string;
    created_at?: string;
    ref?: string;
    anon_key?: string;
    access_token?: string;
    refresh_token?: string;
  };
  supabase_organizations?: {
    organization_id: string;
    name: string;
    access_token: string;
    refresh_token: string;
  }[];
  isSupabaseConnected?: boolean;
};

type ProjectContextType = {
  projectId: string;
  project: Project | null;
  isProjectLoading: boolean;
  threads: Thread[];
  isLoadingThreads: boolean;
  hasAccess: boolean;
  refetch: () => void;
  sandboxState: SanboxPreviewState;
};

const ProjectContext = createContext<ProjectContextType | null>(null);

export const ProjectProvider = ({
  children,
  projectId,
}: {
  children: React.ReactNode;
  projectId: string;
}) => {
  const MAX_NO_DOMAIN_RETRIES = 15;

  const {
    data: rawProject,
    isLoading: isProjectLoading,
    error,
    refetch,
  } = useQuery<
    Omit<Project, "team_emails"> & {
      team_emails: string;
      tech_stack_prompt: string;
      users_prompt_Array: string;
    },
    { detail: string }
  >({
    queryKey: ["get-project", projectId],
    queryFn: () => getProject(projectId),
    retry: (count, error) => {
      if (error?.detail?.includes("Not authorized to access this project")) return false;
      return count < 1;
    },
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    ...CACHE_PARAMS,
    refetchInterval: (query) => {
      if (
        query.state.data?.isRunning === 0 &&
        query.state.dataUpdateCount < MAX_NO_DOMAIN_RETRIES
      ) {
        return 1000 * 5;
      }

      return 30000;
    },
  });

  const loader = useTopLoader();

  useEffect(() => {
    if (isProjectLoading) {
      loader.start();
    } else {
      loader.done();
    }

    return () => {
      loader.done();
    };
  }, [isProjectLoading]);

  const project = rawProject
    ? {
        ...rawProject,
        team_emails: parseData(rawProject.team_emails),
        tech_stack_prompt: parseData(rawProject.tech_stack_prompt),
        users_prompt_Array: parseData(rawProject.users_prompt_Array),
      }
    : null;

  const { checkProjectAccess } = useProjectAccess();
  const hasAccess = checkProjectAccess(project);

  const startAttemptRef = useRef(false);

  const startEnvironment = useCheckAndStart();
  useEffect(() => {
    if (hasAccess && rawProject?.project_id) {
      startEnvironment.mutate(rawProject.project_id, {
        onSettled: () => {
          refetch().finally(() => {
            startAttemptRef.current = true;
          });
        },
      });
    }
  }, [hasAccess, rawProject?.project_id]);

  const { data: threads = [], isLoading: isLoadingThreads } = useQuery({
    queryKey: ["threads", projectId],
    queryFn: async () => {
      try {
        const threads = await getProjectThreads(projectId);
        return threads.reverse();
      } catch (error) {
        if ((error as { detail: string })?.detail === "No threads found for this project") {
          return [];
        }
        throw error;
      }
    },
    enabled: !isProjectLoading && hasAccess,
    ...CACHE_PARAMS,
  });

  if (isProjectLoading) return null;

  const hasReadAccess = !error?.detail?.includes("Not authorized to access this project");
  const projectNotFound = error?.detail?.includes("Project not found");

  if (!hasReadAccess || projectNotFound) return notFound();

  // no start attempt or sandbox is starting or restoring
  const sandboxStarting =
    project?.sandbox_state !== SandBoxState.STARTED &&
    (!startAttemptRef.current ||
      project?.sandbox_state === SandBoxState.STARTING ||
      project?.sandbox_state === SandBoxState.RESTORING);

  const isSandboxSleeping =
    startAttemptRef.current &&
    (project?.sandbox_state === SandBoxState.STOPPED ||
      project?.sandbox_state === SandBoxState.ARCHIVED);

  const sandboxState = sandboxStarting
    ? "starting"
    : isSandboxSleeping
      ? "sleeping"
      : project?.sandbox_state === SandBoxState.ERROR ||
          // @ts-expect-error error type is not defined
          startEnvironment.error?.error === "workspace_unavailable"
        ? "unavailable"
        : "idle";

  return (
    <ProjectContext.Provider
      value={{
        projectId,
        isProjectLoading,
        project,
        threads,
        isLoadingThreads,
        hasAccess,
        refetch,
        sandboxState,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
};

function parseData(data: string): string[] {
  try {
    return JSON.parse(data);
  } catch (error) {
    console.error("Failed to parse data:", error);
    return [];
  }
}

export const useProject = (): ProjectContextType => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error("useProjectContext must be used within a ProjectProvider");
  }
  return context;
};
