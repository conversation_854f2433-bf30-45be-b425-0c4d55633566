import asyncio
from sqlalchemy import select, and_
from core.db import Database
from core.models import User, Project, ProjectThread, MemoryModule, Deployment, Environment
import json
import datetime
from typing import List, Dict

async def fetch_user_details_by_email(email):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            print("\n=== User Details ===")
            print(f"ID: {user.id}")
            print(f"Email: {user.email}")
            print(f"Kinde ID: {user.kinde_id}")
            print(f"Is Active: {user.is_active}")
            print(f"Is Superuser: {user.is_superuser}")
            print(f"Stripe Customer ID: {user.stripe_customer_id}")
            print(f"Plan: {user.plan}")
            print(f"Subscription ID: {user.subscription_id}")
            print(f"Is Subscribed: {user.isSubscribed}")
            print(f"Subscription End Date: {user.subscription_end_date}")
            print(f"Free Total Tokens: {user.free_total_token}")
            print(f"Project Limit: {user.project_limit}")
            print(f"Is Request Based: {user.isRequestBased}")
            print(f"Total Free Requests: {user.total_free_request}")
            print(f"Is Free Trial: {user.isFreeTrial}")
            print(f"Is Creating Project: {user.is_creating_project}")
            print(f"token_event_name: {user.token_event_name}")

            # Fetch projects for this user
            project_query = select(Project).where(Project.owner_id == user.id)
            project_result = await session.execute(project_query)
            user_projects = project_result.scalars().all()

            print("\n=== User Projects ===")
            for project in user_projects:
                print(f"\nProject Name: {project.name}")
                print(f"Project ID: {project.project_id}")
                print(f"Environment ID: {project.env_id}")
                print(f"Creation Date: {project.creation_date}")
                print(f"Last Updated: {project.last_updated_date}")
                print(f"Preview Image URL: {project.preview_image_url}")
                print(f"Total Input Tokens: {project.total_input_token}")
                print(f"Total Output Tokens: {project.total_output_token}")
                print(f"Total Requests: {project.total_request}")
                print(f"Onboarding Completed: {project.onboarding_completed}")
                print(f"Team email: {project.team_emails}")
                # if project.agent_instructions:
                #     print(f"Agent Instructions: {project.agent_instructions[:100]}...")
                # if project.agent_continue_instructions:
                #     print(f"Agent Continue Instructions: {project.agent_continue_instructions[:100]}...")
                # if project.agent_rules_to_follow:
                #     print(f"Agent Rules: {project.agent_rules_to_follow[:100]}...")
        else:
            print(f"No user found with email: {email}")

    await db.close()

async def update_user_free_requests(email, new_request_count):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Update the total_free_request
            user.total_free_request += new_request_count
            await session.commit()
            
            print(f"\nUpdated total_free_request to {new_request_count} for user: {email}")
            
            # Display updated user details
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"Total Free Requests: {user.total_free_request}")
        else:
            print(f"No user found with email: {email}")

    await db.close()


async def fetch_project_thread_runs(project_id):
    db = Database()

    async with db.get_async_session() as session:
        # First verify the project exists
        project_query = select(Project).where(Project.project_id == project_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if not project:
            print(f"No project found with ID: {project_id}")
            return

        # Get all threads for this project
        threads_query = select(ProjectThread).where(ProjectThread.project_id == project_id)
        threads_result = await session.execute(threads_query)
        threads = threads_result.scalars().all()

        print(f"\n=== Thread Runs for Project: {project.name} ===")
        print(f"Project ID: {project_id}")

        for thread in threads:
            print(f"\nThread ID: {thread.thread_id}")
            print(f"Thread Creation Date: {thread.creation_date}")
            print(f"Thread Last Updated: {thread.last_updated_date}")
            print(f"Page Route: {thread.page_route}")
            
            # # Get all runs for this thread
            # runs_query = select(ProjectThreadRun).where(ProjectThreadRun.thread_id == thread.thread_id)
            # runs_result = await session.execute(runs_query)
            # thread_runs = runs_result.scalars().all()
            
            # print("\nThread Runs:")
            # if thread_runs:
            #     for run in thread_runs:
            #         print(f"\n  Run ID: {run.run_id}")
            #         print(f"  Creation Date: {run.creation_date}")
            #         print(f"  Project Agent Run ID: {run.project_agent_run}")
            #         print(f"  Messages: {run.messages[:10000]}..." if run.messages else "  Messages: None")
            #         print(f"  Working Memory: {run.working_memory[:100]}..." if run.working_memory else "  Working Memory: None")
            # else:
            #     print("  No runs found for this thread")
            # print("-" * 50)

    await db.close()

async def update_user_project_limit(email, additional_projects):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Update the project_limit
            user.project_limit += additional_projects
            await session.commit()
            
            print(f"\nIncreased project limit by {additional_projects} for user: {email}")
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"New Project Limit: {user.project_limit}")
        else:
            print(f"No user found with email: {email}")

    await db.close()


async def reward_user_with_tokens(email, token_amount=1000000):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Add tokens to free_total_token
            user.free_total_token += token_amount
            await session.commit()
            
            print(f"\nAdded {token_amount} tokens to Free Total Tokens for user: {email}")
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"Free Total Tokens: {user.free_total_token}")
        else:
            print(f"No user found with email: {email}")

    await db.close()   


async def fetch_thread_messages(project_id, thread_limit=2, output_file=None, hide_tool_msgs=False, only_latest_assistant=False, last_n=None):
    db = Database()
    
    # If no output file specified, create one with project_id
    if output_file is None:
        output_file = f"project_{project_id}_last_{thread_limit}_threads.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        async with db.get_async_session() as session:
            # Verify project exists
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()

            if not project:
                message = f"No project found with ID: {project_id}"
                print(message)
                f.write(message)
                return

            # Write header
            header = f"\n=== Messages from Last {thread_limit} Threads for Project: {project.name} ===\n"
            header += f"Project ID: {project_id}\n"
            print(header)
            f.write(header)

            # Get last N threads
            threads_query = (
                select(ProjectThread)
                .where(ProjectThread.project_id == project_id)
                .order_by(ProjectThread.last_updated_date.desc())
                .limit(thread_limit)
            )
            threads_result = await session.execute(threads_query)
            threads = threads_result.scalars().all()

            if not threads:
                message = "No threads found for this project\n"
                print(message)
                f.write(message)
                return

            for thread in threads:
                thread_header = f"\n{'='*50}\n"
                thread_header += f"Thread ID: {thread.thread_id}\n"
                thread_header += f"Thread Creation Date: {thread.creation_date}\n"
                thread_header += f"Thread Last Updated: {thread.last_updated_date}\n"
                thread_header += f"Page Route: {thread.page_route}\n"
                thread_header += f"{'='*50}\n"
                
                print(thread_header)
                f.write(thread_header)

                # Process messages
                if thread.messages:
                    messages = json.loads(thread.messages)
                    
                    # Filter messages based on parameters
                    if only_latest_assistant:
                        for msg in reversed(messages):
                            if msg.get('role') == 'assistant':
                                messages = [msg]
                                break
                        else:
                            messages = []
                    
                    if last_n is not None:
                        # Get last N messages that include at least one user message
                        temp_messages = []
                        user_found = False
                        for msg in reversed(messages):
                            if msg.get('role') == 'user':
                                user_found = True
                            if user_found:
                                temp_messages.append(msg)
                            if len(temp_messages) >= last_n:
                                break
                        messages = list(reversed(temp_messages))
                    
                    if hide_tool_msgs:
                        messages = [
                            {k: v for k, v in msg.items() if k != 'tool_calls'}
                            for msg in messages
                            if msg.get('role') != 'tool'
                        ]
                    
                    # Filter to regular message types
                    messages = [
                        msg for msg in messages
                        if msg.get('role') in ['system', 'assistant', 'tool', 'user']
                    ]

                    # Write messages
                    for msg in messages:
                        message_block = f"\nRole: {msg.get('role')}\n"
                        message_block += f"Content: {msg.get('content')}\n"
                        if 'tool_calls' in msg:
                            message_block += f"Tool Calls: {msg.get('tool_calls')}\n"
                        message_block += f"{'-'*50}\n"
                        
                        print(message_block)
                        f.write(message_block)
                else:
                    no_messages = "No messages found for this thread\n"
                    print(no_messages)
                    f.write(no_messages)

    await db.close()
    print(f"\nMessages have been saved to: {output_file}")

async def fetch_recent_thread_conversations(project_id, thread_limit=5, output_file=None):
    db = Database()
    
    if output_file is None:
        output_file = f"project_{project_id}_last_{thread_limit}_conversations.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        async with db.get_async_session() as session:
            # Verify project exists
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()

            if not project:
                message = f"No project found with ID: {project_id}"
                print(message)
                f.write(message)
                return

            # Write header
            header = f"\n=== Conversations from Last {thread_limit} Threads for Project: {project.name} ===\n"
            header += f"Project ID: {project_id}\n\n"
            print(header)
            f.write(header)

            # Get last N threads
            threads_query = (
                select(ProjectThread)
                .where(ProjectThread.project_id == project_id)
                .order_by(ProjectThread.last_updated_date.desc())
                .limit(thread_limit)
            )
            threads_result = await session.execute(threads_query)
            threads = threads_result.scalars().all()

            if not threads:
                message = "No threads found for this project\n"
                print(message)
                f.write(message)
                return

            for thread in threads:
                thread_header = f"\n{'='*80}\n"
                thread_header += f"Thread ID: {thread.thread_id}\n"
                thread_header += f"Creation Date: {thread.creation_date}\n"
                thread_header += f"Last Updated: {thread.last_updated_date}\n"
                thread_header += f"Page Route: {thread.page_route}\n"
                thread_header += f"{'='*80}\n"
                
                print(thread_header)
                f.write(thread_header)

                if thread.messages:
                    messages = json.loads(thread.messages)
                    
                    # Filter to only user and assistant messages
                    conversation_messages = [
                        msg for msg in messages
                        if msg.get('role') in ['user', 'assistant']
                    ]

                    # Write conversation messages
                    for msg in conversation_messages:
                        role = msg.get('role', '').upper()
                        content = msg.get('content', '')
                        
                        message_block = f"\n[{role}]:\n"
                        message_block += f"{content}\n"
                        message_block += f"{'-'*80}\n"
                        
                        print(message_block)
                        f.write(message_block)
                else:
                    no_messages = "No messages found for this thread\n"
                    print(no_messages)
                    f.write(no_messages)

    await db.close()
    print(f"\nConversations have been saved to: {output_file}")

async def get_email_from_project_id(project_id):
    db = Database()
    try:
        async with db.get_async_session() as session:
            # First get the project to find the owner_id
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()

            if not project:
                print(f"No project found with ID: {project_id}")
                return None

            # Get the user using the owner_id
            user_query = select(User).where(User.id == project.owner_id)
            user_result = await session.execute(user_query)
            user = user_result.scalar_one_or_none()

            if user:
                print(f"Found email: {user.email} for project ID: {project_id}")
                return user.email
            else:
                print(f"No user found for project ID: {project_id}")
                return None
    finally:
        await db.close()

async def clear_project_open_files(project_id):
    db = Database()

    async with db.get_async_session() as session:
        # First verify the project exists
        project_query = select(Project).where(Project.project_id == project_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if not project:
            print(f"No project found with ID: {project_id}")
            return

        # Query the memory module for openfiles
        memory_query = select(MemoryModule).where(
            (MemoryModule.project_id == project_id) & 
            (MemoryModule.module_name == 'OpenFilesInEditor')
        )
        memory_result = await session.execute(memory_query)
        memory_module = memory_result.scalar_one_or_none()

        if memory_module:
            # Check current content
            current_data = memory_module.data
            print(f"\nCurrent openfiles content: {current_data}")

            # Update to empty array
            memory_module.data = '[]'
            await session.commit()
            
            print(f"Successfully cleared openfiles for project: {project_id}")
            print("New openfiles content: []")
        else:
            print(f"No openfiles memory module found for project: {project_id}")

    await db.close()

async def update_user_tokens_and_requests(email, new_token_count=0, new_request_count=0):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Update both values directly
            user.free_total_token = new_token_count
            user.total_free_request = new_request_count
            await session.commit()
            
            print(f"\nUpdated values for user: {email}")
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"Free Total Tokens: {user.free_total_token}")
            print(f"Total Free Requests: {user.total_free_request}")
        else:
            print(f"No user found with email: {email}")

    await db.close()

async def get_project_id_from_env_id(env_id):
    db = Database()

    async with db.get_async_session() as session:
        # Query the project by env_id
        project_query = select(Project).where(Project.env_id == env_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if project:
            print(f"\nFound project for env_id: {env_id}")
            print(f"Project ID: {project.project_id}")
            print(f"Project Name: {project.name}")
            return project.project_id
        else:
            print(f"No project found with env_id: {env_id}")
            return None

    await db.close()

async def calculate_project_total_tokens(project_id):
    db = Database()

    async with db.get_async_session() as session:
        # First verify the project exists
        project_query = select(Project).where(Project.project_id == project_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if not project:
            print(f"No project found with ID: {project_id}")
            return

        # Get all threads for this project
        threads_query = select(ProjectThread).where(ProjectThread.project_id == project_id)
        threads_result = await session.execute(threads_query)
        threads = threads_result.scalars().all()

        total_tokens = 0
        total_messages = 0
        total_messages_with_tokens = 0

        print(f"\n=== Token Usage for Project: {project.name} ===")
        print(f"Project ID: {project_id}")

        for thread in threads:
            if thread.messages:
                messages = json.loads(thread.messages)
                for msg in messages:
                    total_messages += 1
                    if isinstance(msg, dict) and 'total_tokens' in msg:
                        total_tokens += msg['total_tokens']
                        total_messages_with_tokens += 1

        print(f"\nTotal tokens used: {total_tokens:,}")
        print(f"Total messages: {total_messages}")
        print(f"Messages with token counts: {total_messages_with_tokens}")
        print(f"Messages without token counts: {total_messages - total_messages_with_tokens}")
        
        # Also show the project's stored token counts
        print("\nStored project token counts:")
        print(f"Total input tokens: {project.total_input_token:,}")
        print(f"Total output tokens: {project.total_output_token:,}")
        print(f"Combined stored tokens: {(project.total_input_token + project.total_output_token):,}")

    await db.close()
    return total_tokens

async def calculate_tokens_for_multiple_projects():
    project_ids = [
        "004f5801-6086-4bd5-9d22-ef2619169d20",  # yourchoicetickets.com
        "1508158a-5296-480e-91a6-604f00568176",  # yourchoicetickets.com
        "1b52e811-65ad-415b-b72b-f8a51dc385a6",  # yct.com
        "d63d1c67-43a9-4bde-83cf-7dcdd809b98f"  # yourchoicetickets.com Remix
    ]
    
    print("\n=== Token Usage Summary for All Projects ===\n")
    
    for project_id in project_ids:
        print(f"\n{'='*50}")
        total_tokens = await calculate_project_total_tokens(project_id)
        print(f"{'='*50}")

async def cancel_user_subscription(email):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Update subscription-related fields
            user.isSubscribed = False
            user.plan = None
            user.subscription_end_date = None
            await session.commit()
            
            print(f"\nCancelled subscription for user: {email}")
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"Is Subscribed: {user.isSubscribed}")
            print(f"Plan: {user.plan}")
            print(f"Subscription End Date: {user.subscription_end_date}")
        else:
            print(f"No user found with email: {email}")

    await db.close()

async def fetch_project_details(project_id: str):
    """
    Fetch comprehensive details for a project, including environment information.
    
    Args:
        project_id (str): The ID of the project to fetch details for
    """
    db = Database()

    async with db.get_async_session() as session:
        try:
            # Query the project
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()

            if not project:
                print(f"No project found with ID: {project_id}")
                return

            # Get owner details
            user_query = select(User).where(User.id == project.owner_id)
            user_result = await session.execute(user_query)
            owner = user_result.scalar_one_or_none()

            # Get environment details
            env_query = select(Environment).where(Environment.project_id == project_id)
            env_result = await session.execute(env_query)
            environment = env_result.scalar_one_or_none()

            # Get deployment details
            deployment_query = select(Deployment).where(Deployment.project_id == project_id)
            deployment_result = await session.execute(deployment_query)
            deployment = deployment_result.scalar_one_or_none()

            # Print project details
            print("\n=== Project Details ===")
            print(f"Project ID: {project.project_id}")
            print(f"Name: {project.name}")
            print(f"Creation Date: {project.creation_date}")
            print(f"Last Updated: {project.last_updated_date}")
            print(f"Environment ID (from project): {project.env_id}")
            print(f"Preview Image URL: {project.preview_image_url}")
            print(f"Onboarding Completed: {project.onboarding_completed}")
            print(f"Is Public: {project.isPublic}")
            
            # Print token and request usage
            print("\n=== Usage Statistics ===")
            print(f"Total Input Tokens: {project.total_input_token:,}")
            print(f"Total Output Tokens: {project.total_output_token:,}")
            print(f"Total Requests: {project.total_request:,}")

            # Print team information
            try:
                team_emails = json.loads(project.team_emails)
                print("\n=== Team Information ===")
                print(f"Team Size: {len(team_emails)}")
                if team_emails:
                    print("Team Emails:")
                    for email in team_emails:
                        print(f"- {email}")
            except (ValueError, TypeError, AttributeError):
                print(f"Team Emails (raw): {project.team_emails}")

            # Print owner information
            if owner:
                print("\n=== Owner Information ===")
                print(f"Owner ID: {owner.id}")
                print(f"Owner Email: {owner.email}")
                print(f"Owner Plan: {owner.plan}")
                print(f"Is Subscribed: {owner.isSubscribed}")
                print(f"Free Total Tokens: {owner.free_total_token:,}")
            else:
                print("\n=== Owner Information ===")
                print(f"Owner ID: {project.owner_id}")
                print("Owner details not found")

            # Print environment information
            print("\n=== Environment Information ===")
            if environment:
                print(f"Environment ID: {environment.env_id}")
                print(f"Status: {environment.status}")
                print(f"Assigned: {environment.assigned}")
                print(f"GitHub Repository: {environment.github_repo}")
                print(f"Template: {environment.template}")
            else:
                print("No environment found for this project")
                
                # Check if there's an environment with the project's env_id
                if project.env_id:
                    env_by_id_query = select(Environment).where(Environment.env_id == project.env_id)
                    env_by_id_result = await session.execute(env_by_id_query)
                    env_by_id = env_by_id_result.scalar_one_or_none()
                    
                    if env_by_id:
                        print("\n=== Environment Found by env_id ===")
                        print(f"Environment ID: {env_by_id.env_id}")
                        print(f"Status: {env_by_id.status}")
                        print(f"Assigned: {env_by_id.assigned}")
                        print(f"Associated Project ID: {env_by_id.project_id}")
                        print(f"GitHub Repository: {env_by_id.github_repo}")
                        print(f"Template: {env_by_id.template}")

            # Print deployment information
            if deployment:
                print("\n=== Deployment Information ===")
                print(f"Deployment URL: {deployment.deployment_url}")
                print(f"Production URL: {deployment.production_url}")
                print(f"Last Deployed: {deployment.last_deployed}")
                print(f"Is Deployed: {deployment.is_deployed}")
                print(f"Has Vercel Token: {'Yes' if deployment.vercel_token else 'No'}")
            else:
                print("\n=== Deployment Information ===")
                print("No deployment found for this project")

        except Exception as e:
            print(f"Error fetching project details: {str(e)}")
            import traceback
            traceback.print_exc()

    await db.close()

async def set_user_creating_project_false(email):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Update is_creating_project to False
            user.is_creating_project = False
            await session.commit()
            
            print(f"\nUpdated is_creating_project to False for user: {email}")
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"Is Creating Project: {user.is_creating_project}")
        else:
            print(f"No user found with email: {email}")

    await db.close()

async def fetch_latest_thread_messages(project_id: str, output_file: str = None):
    """
    Fetch all messages from the latest thread of a specific project and save to JSON.
    
    Args:
        project_id (str): The project ID to fetch messages from
        output_file (str, optional): Output JSON file path. Defaults to project_id_latest_thread.json
    """
    try:
        db = Database()
        
        if output_file is None:
            output_file = f"project_{project_id}_latest_thread.json"
        
        async with db.get_async_session() as session:
            # Verify project exists
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()

            if not project:
                print(f"No project found with ID: {project_id}")
                return

            # Get the latest thread
            latest_thread_query = (
                select(ProjectThread)
                .where(ProjectThread.project_id == project_id)
                .order_by(ProjectThread.last_updated_date.desc())
                .limit(1)
            )
            thread_result = await session.execute(latest_thread_query)
            latest_thread = thread_result.scalar_one_or_none()

            if not latest_thread:
                print("No threads found for this project")
                return

            thread_data = {
                "thread_id": latest_thread.thread_id,
                "creation_date": latest_thread.creation_date,
                "last_updated_date": latest_thread.last_updated_date,
                "page_route": latest_thread.page_route,
                "messages": json.loads(latest_thread.messages) if latest_thread.messages else []
            }

            # Save to JSON file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(thread_data, f, indent=2)

            print(f"\nThread details saved to: {output_file}")
            print(f"Thread ID: {latest_thread.thread_id}")
            print(f"Creation Date: {latest_thread.creation_date}")
            print(f"Last Updated: {latest_thread.last_updated_date}")
            print(f"Page Route: {latest_thread.page_route}")
            print(f"Total Messages: {len(thread_data['messages'])}")

        await db.close()

    except Exception as e:
        print(f"Error fetching thread messages: {str(e)}")
        import traceback
        traceback.print_exc()

async def get_available_environment_count():
    db = Database()

    async with db.get_async_session() as session:
        # Query environments that are unassigned and ready
        env_query = select(Environment).where(
            (~Environment.assigned) &
            (Environment.status == 'ready')
        )
        result = await session.execute(env_query)
        environments = result.scalars().all()
        
        count = len(environments)
        
        print("\n=== Available Environment Summary ===")
        print(f"Total unassigned and ready environments: {count}")
        
        # Optional: Print details of each available environment
        if count > 0:
            print("\nAvailable Environment Details:")
            for env in environments:
                print(f"Environment ID: {env.env_id}")
                print(f"Template: {env.template}")
                print("-" * 50)

        print("\n=== Available Environment Summary ===")
        print(f"Total unassigned and ready environments: {count}")        

    await db.close()
    return count

async def check_and_start_environment(env_id: str):
    """
    Checks if an environment is running and starts it if it's stopped.
    
    Args:
        env_id (str): The environment ID to check and start
    """
    try:
        from core.envs.env_manager import EnvManager
        from core.envs.env_ops import env_ops
        env_manager = EnvManager()
        
        print(f"\n=== Checking Environment Status: {env_id} ===")
        
        # Get initial status
        env_details = await env_manager.get_environment(env_id)
        if not env_details:
            print("Could not get environment details")
            return False
            
        initial_status = "running" if env_details.get('isRunning') else "stopped"
        print(f"Initial Status: {initial_status}")
        
        # If already running, we're done
        if env_details.get('isRunning'):
            print("Environment is already running")
            return True
            
        # Try to start the environment
        print("Environment is stopped. Attempting to start...")
        success = await env_ops.get_started_sandbox(env_id)
        
        if success:
            print("Successfully started the environment")
            
            # Get final status to verify
            final_details = await env_manager.get_environment(env_id)
            if final_details and final_details.get('isRunning'):
                print("Verified environment is now running")
                return True
            else:
                print("Environment start command succeeded but status check failed")
                return False
        else:
            print("Failed to start the environment")
            return False
            
    except Exception as e:
        print(f"Error checking/starting environment: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def update_user_to_free_plan(email, token_amount=3000000):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Update user details for free plan
            user.plan = "free"
            user.isSubscribed = True
            user.free_total_token = token_amount
            user.project_limit = 25  # Standard free plan project limit
            user.is_active = True
            user.isRequestBased = False
            user.isFreeTrial = False
            await session.commit()
            
            print(f"\nUpdated user to free plan: {email}")
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"Plan: {user.plan}")
            print(f"Is Subscribed: {user.isSubscribed}")
            print(f"Free Total Tokens: {user.free_total_token:,}")
            print(f"Project Limit: {user.project_limit}")
            print(f"Is Request Based: {user.isRequestBased}")
            print(f"Is Free Trial: {user.isFreeTrial}")
        else:
            print(f"No user found with email: {email}")

    await db.close()

async def set_user_active_status(email, is_active=True):
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Update is_active status
            user.is_active = is_active
            await session.commit()
            
            print(f"\nUpdated is_active status for user: {email}")
            print("\n=== Updated User Details ===")
            print(f"Email: {user.email}")
            print(f"Is Active: {user.is_active}")
        else:
            print(f"No user found with email: {email}")

    await db.close()

async def transfer_project(project_id: str, new_owner_email: str):
    """
    Transfer a project to a new owner.
    
    Args:
        project_id (str): The ID of the project to transfer
        new_owner_email (str): The email of the new owner
    """
    db = Database()

    async with db.get_async_session() as session:
        # Get the new owner
        new_owner_query = select(User).where(User.email == new_owner_email)
        new_owner_result = await session.execute(new_owner_query)
        new_owner = new_owner_result.scalar_one_or_none()

        if not new_owner:
            print(f"No user found with email: {new_owner_email}")
            return

        # Get the project
        project_query = select(Project).where(Project.project_id == project_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if not project:
            print(f"No project found with ID: {project_id}")
            return

        # Store old owner info for logging
        old_owner_query = select(User).where(User.id == project.owner_id)
        old_owner_result = await session.execute(old_owner_query)
        old_owner = old_owner_result.scalar_one_or_none()
        old_owner_email = old_owner.email if old_owner else "Unknown"

        # Update the project's owner
        project.owner_id = new_owner.id
        project.last_updated_date = datetime.datetime.now().isoformat()
        await session.commit()

        print("\n=== Project Transfer Complete ===")
        print(f"Project: {project.name} (ID: {project.project_id})")
        print(f"Transferred from: {old_owner_email}")
        print(f"Transferred to: {new_owner_email}")
        print(f"Transfer date: {project.last_updated_date}")

    await db.close()

async def start_multiple_environments(env_ids: list):
    """
    Starts multiple environments in sequence.
    
    Args:
        env_ids (list): List of environment IDs to start
    """
    print("\n=== Starting Multiple Environments ===")
    
    results = []
    for env_id in env_ids:
        print(f"\nProcessing environment: {env_id}")
        success = await check_and_start_environment(env_id)
        results.append({
            "env_id": env_id,
            "success": success
        })
    
    # Print summary
    print("\n=== Summary ===")
    successful = [r for r in results if r["success"]]
    failed = [r for r in results if not r["success"]]
    
    print(f"Total environments processed: {len(results)}")
    print(f"Successfully started: {len(successful)}")
    print(f"Failed to start: {len(failed)}")
    
    if failed:
        print("\nFailed environments:")
        for f in failed:
            print(f"- {f['env_id']}")
    
    return results

async def get_environment_details(env_ids: list):
    """
    Fetches and displays detailed information for multiple environments.
    
    Args:
        env_ids (list): List of environment IDs to check
    """
    try:
        from core.envs.env_manager import EnvManager
        env_manager = EnvManager()
        
        print("\n=== Environment Details ===")
        
        results = []
        for env_id in env_ids:
            print(f"\nFetching details for environment: {env_id}")
            env_details = await env_manager.get_environment(env_id)
            
            if env_details:
                details = {
                    "env_id": env_details.get('env_id'),
                    "env_url": env_details.get('env_url'),
                    "name": env_details.get('name'),
                    "status": "running" if env_details.get('isRunning') else "stopped",
                    "creation_date": env_details.get('createdAt'),
                    "provider_metadata": env_details.get('providerMetadata')
                }
                
                print(f"\nEnvironment ID: {details['env_id']}")
                print(f"Environment URL: {details['env_url']}")
                print(f"Name: {details['name']}")
                print(f"Status: {details['status']}")
                print(f"Created: {details['creation_date']}")
                if details['provider_metadata']:
                    print("\nProvider Metadata:")
                    print(f"  {details['provider_metadata']}")
                print("-" * 50)
                
                results.append(details)
            else:
                print(f"Could not fetch details for environment: {env_id}")
                results.append({
                    "env_id": env_id,
                    "status": "error",
                    "error": "Could not fetch environment details"
                })
        
        # Print summary
        print("\n=== Summary ===")
        running = len([r for r in results if r.get('status') == 'running'])
        stopped = len([r for r in results if r.get('status') == 'stopped'])
        errors = len([r for r in results if r.get('status') == 'error'])
        
        print(f"Total environments checked: {len(results)}")
        print(f"Running: {running}")
        print(f"Stopped: {stopped}")
        print(f"Errors: {errors}")
        
        return results
            
    except Exception as e:
        print(f"Error fetching environment details: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def save_github_urls(env_ids: list, output_file: str = "environment_github_urls.json"):
    """
    Fetches environment details and GitHub URLs from the database and saves them to a JSON file.
    
    Args:
        env_ids (list): List of environment IDs to process
        output_file (str): Name of the output JSON file
    """
    try:
        db = Database()
        from core.envs.env_manager import EnvManager
        env_manager = EnvManager()
        
        print("\n=== Fetching GitHub URLs for Environments ===")
        
        github_data = {}
        
        async with db.get_async_session() as session:
            for env_id in env_ids:
                print(f"\nProcessing environment: {env_id}")
                
                # Get environment from database
                query = select(Environment).where(Environment.env_id == env_id)
                result = await session.execute(query)
                db_env = result.scalar_one_or_none()
                
                # Get environment details from EnvManager
                env_details = await env_manager.get_environment(env_id)
                
                if db_env and env_details:
                    github_data[env_id] = {
                        "github_url": db_env.github_repo,
                        "env_url": env_details.get('env_url'),
                        "status": "running" if env_details.get('isRunning') else "stopped",
                        "template": db_env.template,
                        "project_id": db_env.project_id,
                        "db_status": db_env.status
                    }
                    print(f"Found GitHub URL: {db_env.github_repo}")
                else:
                    github_data[env_id] = {
                        "error": "Environment not found in database" if not db_env else "Could not fetch environment details",
                        "status": "unknown"
                    }
                    print("Could not fetch complete environment information")
        
        # Save to JSON file
        with open(output_file, 'w') as f:
            json.dump(github_data, f, indent=2)
        
        print(f"\nSaved GitHub URLs to {output_file}")
        
        # Print summary
        urls_found = len([env for env in github_data.values() if "github_url" in env and env["github_url"] is not None])
        print("\n=== Summary ===")
        print(f"Total environments processed: {len(env_ids)}")
        print(f"GitHub URLs found: {urls_found}")
        print(f"GitHub URLs missing: {len(env_ids) - urls_found}")
        
        return github_data
            
    except Exception as e:
        print(f"Error saving GitHub URLs: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

async def get_project_ids_from_env_ids(env_ids: List[str], input_file: str = "environment_github_urls.json", output_file: str = None) -> Dict:
    """
    Get project IDs for given environment IDs from the environment_github_urls.json file.
    
    Args:
        env_ids (List[str]): List of environment IDs
        input_file (str): Path to the JSON file containing environment data
        output_file (str): Optional path to save results. If None, uses timestamp
        
    Returns:
        Dict: Dictionary mapping env_ids to their project_ids
    """
    try:
        with open(input_file, 'r') as f:
            env_data = json.load(f)
        
        result = {}
        for env_id in env_ids:
            env_info = env_data.get(env_id, {})
            project_id = env_info.get('project_id')
            if project_id:
                result[env_id] = {
                    'project_id': project_id,
                    'github_url': env_info.get('github_url'),
                    'env_url': env_info.get('env_url'),
                    'status': env_info.get('status'),
                    'template': env_info.get('template')
                }
            else:
                print(f"No project ID found for environment: {env_id}")
        
        print("\n=== Environment to Project ID Mapping ===")
        for env_id, info in result.items():
            print(f"\nEnvironment: {env_id}")
            print(f"Project ID: {info['project_id']}")
            print(f"GitHub URL: {info['github_url']}")
            print(f"Environment URL: {info['env_url']}")
            print(f"Status: {info['status']}")
            print(f"Template: {info['template']}")
        
        # Save to JSON file with timestamp if no output file specified
        if output_file is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f'environment_project_ids_{timestamp}.json'
        
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"\nResults saved to: {output_file}")
        
        return result
            
    except Exception as e:
        print(f"Error getting project IDs: {str(e)}")
        return {}



# async def fetch_prize_submissions_by_date(start_date: str = None, end_date: str = None):
#     """
#     Fetch prize submissions filtered by date range.
    
#     Args:
#         start_date (str, optional): Start date in ISO format (YYYY-MM-DD)
#         end_date (str, optional): End date in ISO format (YYYY-MM-DD)
#     """
#     db = Database()

#     async with db.get_async_session() as session:
#         try:
#             # Base query with joins
#             submission_query = (
#                 select(PrizeSubmission, User, Project)
#                 .join(User, PrizeSubmission.user_id == User.id)
#                 .join(Project, PrizeSubmission.project_id == Project.project_id)
#             )
            
#             # Add date filters if provided
#             if start_date:
#                 submission_query = submission_query.where(PrizeSubmission.created_at >= f"{start_date}T00:00:00")
#             if end_date:
#                 submission_query = submission_query.where(PrizeSubmission.created_at <= f"{end_date}T23:59:59")
            
#             # Order by creation date
#             submission_query = submission_query.order_by(PrizeSubmission.created_at.desc())
            
#             # Execute query
#             result = await session.execute(submission_query)
#             submissions = result.all()
            
#             if not submissions:
#                 print("No prize submissions found" + 
#                       (f" between {start_date} and {end_date}" if start_date and end_date else 
#                        f" from {start_date}" if start_date else 
#                        f" until {end_date}" if end_date else ""))
#                 return
            
#             print("\n=== Prize Submissions ===")
#             print(f"Date Range: {start_date or 'Beginning'} to {end_date or 'Present'}")
            
#             for submission, user, project in submissions:
#                 print(f"\nSubmission Details:")
#                 print(f"ID: {submission.id}")
#                 print(f"URL: {submission.url}")
#                 print(f"Created At: {submission.created_at}")
#                 print(f"Description: {submission.description}")
#                 print(f"Prize Granted: {submission.prize_granted}")
#                 print(f"Prize Amount: ${submission.prize_amount:,.2f}" if submission.prize_amount else "Prize Amount: Not set")
                
#                 print(f"\nProject Details:")
#                 print(f"Project ID: {project.project_id}")
#                 print(f"Project Name: {project.name}")
#                 print(f"Project Creation Date: {project.creation_date}")
                
#                 print(f"\nUser Details:")
#                 print(f"User ID: {user.id}")
#                 print(f"User Email: {user.email}")
#                 print(f"User Plan: {user.plan}")
#                 print("-" * 50)
            
#             # Summary statistics
#             print("\n=== Summary Statistics ===")
#             print(f"Total Submissions: {len(submissions)}")
            
#             total_prize_amount = sum(sub[0].prize_amount or 0 for sub in submissions)
#             print(f"Total Prize Amount: ${total_prize_amount:,.2f}")
            
#             granted_count = sum(1 for sub in submissions if sub[0].prize_granted)
#             print(f"Prizes Granted: {granted_count}")
#             print(f"Prizes Pending: {len(submissions) - granted_count}")
            
#             # Daily breakdown
#             print("\n=== Daily Submission Breakdown ===")
#             daily_counts = {}
#             for sub, _, _ in submissions:
#                 date = sub.created_at.split('T')[0]  # Extract date part
#                 daily_counts[date] = daily_counts.get(date, 0) + 1
            
#             for date, count in sorted(daily_counts.items()):
#                 print(f"{date}: {count} submission(s)")
            
#             # Project breakdown
#             print("\n=== Project Breakdown ===")
#             project_counts = {}
#             for sub, _, project in submissions:
#                 project_counts[project.name] = project_counts.get(project.name, 0) + 1
            
#             for project_name, count in project_counts.items():
#                 print(f"{project_name}: {count} submission(s)")
                
#         except Exception as e:
#             print(f"Error fetching prize submissions: {str(e)}")
#             import traceback
#             traceback.print_exc()
        
#     await db.close()

# async def reward_prize_submissions_with_tokens(token_amount: int = 500000, output_file: str = None):
#     """
#     Rewards all users who submitted prizes with tokens using reward_user_with_tokens,
#     deletes their submissions, and saves user details before and after the reward.
    
#     Args:
#         token_amount (int): Amount of tokens to reward (default: 500000)
#         output_file (str): Optional custom path for the JSON output file
#     """
#     db = Database()
    
#     # Generate default output filename with timestamp if none provided
#     if output_file is None:
#         timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
#         output_file = f'prize_submission_rewards_{timestamp}.json'
    
#     reward_data = {
#         "timestamp": datetime.datetime.now().isoformat(),
#         "token_amount": token_amount,
#         "users": []
#     }

#     async with db.get_async_session() as session:
#         try:
#             # Get all unique users with prize submissions
#             user_query = (
#                 select(User, PrizeSubmission)
#                 .join(PrizeSubmission, User.id == PrizeSubmission.user_id)
#                 .distinct(User.id)
#             )
            
#             result = await session.execute(user_query)
#             user_submissions = result.all()
            
#             if not user_submissions:
#                 print("No prize submissions found")
#                 return
            
#             print(f"\n=== Processing Prize Submission Rewards ===")
#             print(f"Token Amount per User: {token_amount:,}")
            
#             for user, submission in user_submissions:
#                 # Store initial user state
#                 user_data = {
#                     "user_id": user.id,
#                     "email": user.email,
#                     "before_reward": {
#                         "free_total_token": user.free_total_token,
#                         "plan": user.plan,
#                         "is_subscribed": user.isSubscribed
#                     }
#                 }
                
#                 # Add tokens using reward_user_with_tokens
#                 print(f"\nProcessing user: {user.email}")
#                 print(f"Current tokens: {user.free_total_token:,}")
                
#                 await reward_user_with_tokens(user.email, token_amount)
                
#                 # Refresh user data to get updated token count
#                 user_query = select(User).where(User.id == user.id)
#                 user_result = await session.execute(user_query)
#                 updated_user = user_result.scalar_one()
                
#                 print(f"New token balance: {updated_user.free_total_token:,}")
                
#                 # Store updated user state
#                 user_data["after_reward"] = {
#                     "free_total_token": updated_user.free_total_token,
#                     "plan": updated_user.plan,
#                     "is_subscribed": updated_user.isSubscribed
#                 }
                
#                 # Get all submissions for this user
#                 submission_query = select(PrizeSubmission).where(PrizeSubmission.user_id == user.id)
#                 submissions_result = await session.execute(submission_query)
#                 user_submissions = submissions_result.scalars().all()
                
#                 # Store submission details before deletion
#                 user_data["submissions"] = [{
#                     "id": sub.id,
#                     "url": sub.url,
#                     "project_id": sub.project_id,
#                     "created_at": sub.created_at,
#                     "prize_granted": sub.prize_granted,
#                     "prize_amount": sub.prize_amount
#                 } for sub in user_submissions]
                
#                 # Delete submissions
#                 for sub in user_submissions:
#                     await session.delete(sub)
                
#                 print(f"Deleted {len(user_data['submissions'])} submission(s)")
#                 reward_data["users"].append(user_data)
            
#             # Commit all changes
#             await session.commit()
            
#             # Save all data to JSON file
#             with open(output_file, 'w') as f:
#                 json.dump(reward_data, f, indent=2)
            
#             # Print summary
#             print(f"\n=== Reward Process Complete ===")
#             print(f"Total users processed: {len(reward_data['users'])}")
#             print(f"Total tokens distributed: {len(reward_data['users']) * token_amount:,}")
#             print(f"Details saved to: {output_file}")
            
#         except Exception as e:
#             print(f"Error processing rewards: {str(e)}")
#             import traceback
#             traceback.print_exc()
        
#     await db.close()

# async def verify_prize_submission_rewards(json_file: str):
#     """
#     Verify user details from a prize submission rewards JSON file against current database state.
    
#     Args:
#         json_file (str): Path to the JSON file containing reward data
#     """
#     db = Database()
    
#     try:
#         # Load JSON data
#         with open(json_file, 'r') as f:
#             reward_data = json.load(f)
        
#         print(f"\n=== Verifying Prize Submission Rewards ===")
#         print(f"File: {json_file}")
#         print(f"Timestamp: {reward_data['timestamp']}")
#         print(f"Token Amount: {reward_data['token_amount']:,}")
        
#         async with db.get_async_session() as session:
#             for user_data in reward_data['users']:
#                 email = user_data['email']
                
#                 # Query current user state
#                 user_query = select(User).where(User.email == email)
#                 result = await session.execute(user_query)
#                 current_user = result.scalar_one_or_none()
                
#                 print(f"\n{'='*50}")
#                 print(f"User: {email}")
#                 print(f"User ID: {user_data['user_id']}")
                
#                 if current_user:
#                     print("\nToken Comparison:")
#                     print(f"Before Reward: {user_data['before_reward']['free_total_token']:,}")
#                     print(f"After Reward: {user_data['after_reward']['free_total_token']:,}")
#                     print(f"Current: {current_user.free_total_token:,}")
                    
#                     print("\nPlan Status:")
#                     print(f"Before: {user_data['before_reward']['plan']}")
#                     print(f"After: {user_data['after_reward']['plan']}")
#                     print(f"Current: {current_user.plan}")
                    
#                     print("\nSubscription Status:")
#                     print(f"Before: {user_data['before_reward']['is_subscribed']}")
#                     print(f"After: {user_data['after_reward']['is_subscribed']}")
#                     print(f"Current: {current_user.isSubscribed}")
                    
#                     # Check for submissions
#                     submission_query = select(PrizeSubmission).where(PrizeSubmission.user_id == current_user.id)
#                     result = await session.execute(submission_query)
#                     current_submissions = result.scalars().all()
                    
#                     print("\nSubmissions:")
#                     print(f"Original Count: {len(user_data['submissions'])}")
#                     print(f"Current Count: {len(current_submissions)}")
                    
#                     if current_submissions:
#                         print("\nWarning: User still has prize submissions:")
#                         for sub in current_submissions:
#                             print(f"- ID: {sub.id}, URL: {sub.url}")
                    
#                     # Calculate expected tokens
#                     expected_tokens = user_data['before_reward']['free_total_token'] + reward_data['token_amount']
#                     if current_user.free_total_token != expected_tokens:
#                         print(f"\nWarning: Token mismatch!")
#                         print(f"Expected: {expected_tokens:,}")
#                         print(f"Actual: {current_user.free_total_token:,}")
#                         print(f"Difference: {current_user.free_total_token - expected_tokens:,}")
#                 else:
#                     print("Warning: User not found in database!")
                
#                 # Show original submission details
#                 print("\nOriginal Submissions:")
#                 for sub in user_data['submissions']:
#                     print(f"- ID: {sub['id']}")
#                     print(f"  URL: {sub['url']}")
#                     print(f"  Project: {sub['project_id']}")
#                     print(f"  Created: {sub['created_at']}")
#                     print(f"  Prize Amount: ${sub['prize_amount']:,}")
#                     print(f"  Granted: {sub['prize_granted']}")
        
#         print(f"\n{'='*50}")
#         print(f"Verification Complete")
#         print(f"Total Users Processed: {len(reward_data['users'])}")
        
#     except Exception as e:
#         print(f"Error verifying rewards: {str(e)}")
#         import traceback
#         traceback.print_exc()
    
#     await db.close()

async def fetch_open_files_data(project_id: str):
    """
    Fetch the OpenFilesInEditor memory module data for a specific project.
    
    Args:
        project_id (str): The project ID to fetch open files data for
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query the memory module for OpenFilesInEditor
        memory_query = select(MemoryModule).where(
            (MemoryModule.project_id == project_id) & 
            (MemoryModule.module_name == 'OpenFilesInEditor')
        )
        result = await session.execute(memory_query)
        memory_module = result.scalar_one_or_none()

        if memory_module:
            print(f"\n=== Open Files Data for Project: {project_id} ===")
            try:
                # Parse the JSON data
                files_data = json.loads(memory_module.data)
                print(f"\nTotal open files: {len(files_data)}")
                
                # Display each open file
                for file in files_data:
                    print(f"\nFile: {file}")
            except json.JSONDecodeError:
                print("Error: Could not parse open files data (invalid JSON)")
                print(f"Raw data: {memory_module.data}")
        else:
            print(f"No OpenFilesInEditor module found for project: {project_id}")

    await db.close()

async def fetch_all_memory_data(project_id: str):
    """
    Fetch all memory module data for a specific project.
    
    Args:
        project_id (str): The project ID to fetch memory data for
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query all memory modules for the project
        memory_query = select(MemoryModule).where(MemoryModule.project_id == project_id)
        result = await session.execute(memory_query)
        memory_modules = result.scalars().all()

        if memory_modules:
            print(f"\n=== Memory Module Data for Project: {project_id} ===")
            for module in memory_modules:
                print(f"\nModule Name: {module.module_name}")
                try:
                    # Try to parse as JSON for better formatting
                    data = json.loads(module.data)
                    print("Data (JSON):")
                    print(json.dumps(data, indent=2))
                except json.JSONDecodeError:
                    # If not JSON, print raw data
                    print("Data (Raw):")
                    print(module.data)
                print("-" * 50)
            
            print(f"\nTotal Memory Modules: {len(memory_modules)}")
        else:
            print(f"No memory modules found for project: {project_id}")

    await db.close()

async def get_project_id_by_name(project_name: str):
    """
    Get project ID and owner details for a project with a specific name.
    
    Args:
        project_name (str): The name of the project to search for
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query the project by name
        project_query = select(Project).where(Project.name == project_name)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if project:
            # Get owner details
            user_query = select(User).where(User.id == project.owner_id)
            user_result = await session.execute(user_query)
            owner = user_result.scalar_one_or_none()

            print("\n=== Project Found ===")
            print(f"Name: {project.name}")
            print(f"Project ID: {project.project_id}")
            print(f"Environment ID: {project.env_id}")
            print(f"Creation Date: {project.creation_date}")
            
            if owner:
                print("\n=== Owner Details ===")
                print(f"Owner ID: {owner.id}")
                print(f"Owner Email: {owner.email}")
                print(f"Owner Plan: {owner.plan}")
            else:
                print(f"\nOwner not found for ID: {project.owner_id}")
                
            return project.project_id
        else:
            print(f"No project found with name: {project_name}")
            return None

    await db.close()

async def check_project_environments(output_file: str = "project_environments.json"):
    """
    Check all projects and find those whose env_id exists in environments table
    but are unassigned and ready. Save results to JSON file.
    """
    db = Database()

    async with db.get_async_session() as session:
        try:
            # Get all projects
            projects_query = select(Project)
            projects_result = await session.execute(projects_query)
            projects = projects_result.scalars().all()

            print("\n=== Checking Project Environments ===")
            print(f"Total Projects: {len(projects)}")

            mismatched_projects = []
            for project in projects:
                if project.env_id:  # Only check projects with env_id
                    # Get environment that is unassigned
                    env_query = select(Environment).where(
                        (Environment.env_id == project.env_id) &
                        (~Environment.assigned)  # Only get unassigned environments
                    )
                    env_result = await session.execute(env_query)
                    environment = env_result.scalar_one_or_none()

                    if environment:
                        # Get owner details
                        user_query = select(User).where(User.id == project.owner_id)
                        user_result = await session.execute(user_query)
                        owner = user_result.scalar_one_or_none()

                        mismatched_projects.append({
                            "project_name": project.name,
                            "project_id": project.project_id,
                            "env_id": project.env_id,
                            "owner_email": owner.email if owner else "Unknown",
                            "creation_date": project.creation_date,
                            "last_updated": project.last_updated_date,
                            "environment_status": environment.status,
                            "environment_assigned": environment.assigned,
                            "environment_project_id": environment.project_id
                        })

            # Save to JSON file
            with open(output_file, 'w') as f:
                json.dump(mismatched_projects, f, indent=2)

            print(f"\nSaved {len(mismatched_projects)} unassigned environments to {output_file}")

            # Print results
            if mismatched_projects:
                print(f"\nFound {len(mismatched_projects)} projects with unassigned environments:")
                for proj in mismatched_projects:
                    print("\n" + "="*50)
                    print(f"Project Name: {proj['project_name']}")
                    print(f"Project ID: {proj['project_id']}")
                    print(f"Environment ID: {proj['env_id']}")
                    print(f"Environment Status: {proj['environment_status']}")
                    print(f"Owner Email: {proj['owner_email']}")
            else:
                print("\nNo projects found with unassigned environments.")

            return mismatched_projects

        except Exception as e:
            print(f"Error checking project environments: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    await db.close()

async def update_environment_mappings(input_file: str = "project_environments.json"):
    """
    Update environment mappings based on the JSON file data.
    Sets environments as assigned and updates their project IDs.
    """
    try:
        # Load the JSON file
        with open(input_file, 'r') as f:
            project_mappings = json.load(f)

        db = Database()
        async with db.get_async_session() as session:
            print("\n=== Updating Environment Mappings ===")
            
            updated_count = 0
            for mapping in project_mappings:
                env_query = select(Environment).where(Environment.env_id == mapping['env_id'])
                result = await session.execute(env_query)
                environment = result.scalar_one_or_none()

                if environment:
                    # Update environment
                    environment.project_id = mapping['project_id']
                    environment.assigned = True
                    updated_count += 1
                    
                    print("\nUpdated environment mapping:")
                    print(f"Environment ID: {environment.env_id}")
                    print(f"Project Name: {mapping['project_name']}")
                    print(f"Project ID: {mapping['project_id']}")
                    print("-" * 50)

            await session.commit()
            print("\n=== Update Complete ===")
            print(f"Successfully updated {updated_count} environments")

    except FileNotFoundError:
        print(f"Error: Could not find input file {input_file}")
    except Exception as e:
        print(f"Error updating environment mappings: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        await db.close()


async def set_user_plan(email: str, plan_name: str = "entry"):
    """
    Set a user's plan with predefined settings.
    
    Args:
        email (str): User's email address
        plan_name (str): Plan name to set (default: "entry")
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query the user by email
        user_query = select(User).where(User.email == email)
        user_result = await session.execute(user_query)
        user = user_result.scalar_one_or_none()

        if user:
            # Store initial state for logging
            initial_state = {
                "plan": user.plan,
                "is_subscribed": user.isSubscribed,
                "is_active": user.is_active,
                "free_total_token": user.free_total_token,
                "project_limit": user.project_limit
            }

            # Update user settings
            user.plan = plan_name
            user.isSubscribed = True
            user.is_active = True
            user.free_total_token = 2500000  # 2.5M tokens
            user.project_limit = 25
            
            await session.commit()
            
            print(f"\nUpdated plan settings for user: {email}")
            print("\n=== Previous State ===")
            for key, value in initial_state.items():
                print(f"{key}: {value}")
            
            print("\n=== Updated State ===")
            print(f"Plan: {user.plan}")
            print(f"Is Subscribed: {user.isSubscribed}")
            print(f"Is Active: {user.is_active}")
            print(f"Free Total Tokens: {user.free_total_token:,}")
            print(f"Project Limit: {user.project_limit}")
        else:
            print(f"No user found with email: {email}")

    await db.close()

async def update_deployment_url(project_id: str, new_url: str):
    """
    Update the deployment URL for a specific project.
    
    Args:
        project_id (str): The ID of the project to update
        new_url (str): The new deployment URL to set
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query the deployment for the project
        deployment_query = select(Deployment).where(Deployment.project_id == project_id)
        deployment_result = await session.execute(deployment_query)
        deployment = deployment_result.scalar_one_or_none()

        if deployment:
            # Store original URL for logging
            original_url = deployment.deployment_url
            
            # Update the deployment URL
            deployment.deployment_url = new_url
            await session.commit()
            
            print("\n=== Deployment URL Updated ===")
            print(f"Project ID: {project_id}")
            print(f"Original URL: {original_url}")
            print(f"New URL: {new_url}")
            print(f"Update Time: {datetime.datetime.now().isoformat()}")
        else:
            print(f"No deployment found for project ID: {project_id}")

    await db.close()

async def fetch_project_github_repos(project_ids=None, email=None):
    """
    Fetch GitHub repository URLs for projects based on project IDs or user email.
    
    Args:
        project_ids (List[str], optional): List of project IDs to fetch repos for
        email (str, optional): User email to fetch all their projects' repos
    """
    db = Database()

    async with db.get_async_session() as session:
        try:
            # Build the query based on input parameters
            if project_ids:
                # Query for specific project IDs
                projects_query = select(Project).where(Project.project_id.in_(project_ids))
            elif email:
                # Get user ID first
                user_query = select(User).where(User.email == email)
                user_result = await session.execute(user_query)
                user = user_result.scalar_one_or_none()
                
                if not user:
                    print(f"No user found with email: {email}")
                    return
                
                # Query projects for this user
                projects_query = select(Project).where(Project.owner_id == user.id)
            else:
                print("Error: Either project_ids or email must be provided")
                return
            
            # Execute the query
            projects_result = await session.execute(projects_query)
            projects = projects_result.scalars().all()
            
            if not projects:
                print("No projects found")
                return
            
            print("\n=== Project GitHub Repositories ===")
            
            for project in projects:
                print(f"\nProject: {project.name}")
                print(f"Project ID: {project.project_id}")
                
                if not project.env_id:
                    print("No environment associated with this project")
                    continue
                
                # Query environment to get GitHub repo
                env_query = select(Environment).where(Environment.env_id == project.env_id)
                env_result = await session.execute(env_query)
                env = env_result.scalar_one_or_none()
                
                if env:
                    print(f"Environment ID: {env.env_id}")
                    print(f"GitHub Repository: {env.github_repo or 'Not set'}")
                    print(f"Template: {env.template or 'Not set'}")
                    print(f"Status: {env.status or 'Not set'}")
                else:
                    print(f"No environment found with ID: {project.env_id}")
                
                print("-" * 50)
            
        except Exception as e:
            print(f"Error fetching GitHub repositories: {str(e)}")
            import traceback
            traceback.print_exc()
    
    await db.close()

async def delete_user_account(email, confirm=False, dry_run=True):
    """
    Delete a user account and all associated data.
    
    Args:
        email (str): Email of the user to delete
        confirm (bool): Set to True to confirm deletion
        dry_run (bool): If True, only show what would be deleted
    
    Returns:
        bool: True if deletion was successful, False otherwise
    """
    if not confirm:
        print("\n⚠️ DELETION NOT CONFIRMED ⚠️")
        print("To confirm deletion, call this function with confirm=True")
        print(f"Currently in dry_run mode: {dry_run}")
        return False
    
    db = Database()
    deletion_summary = {
        "user": None,
        "projects": [],
        "threads": 0,
        "deployments": 0,
        "memory_modules": 0
    }
    
    try:
        async with db.get_async_session() as session:
            # 1. Find the user
            user_query = select(User).where(User.email == email)
            user_result = await session.execute(user_query)
            user = user_result.scalar_one_or_none()
            
            if not user:
                print(f"No user found with email: {email}")
                return False
            
            # Store user details for logging
            deletion_summary["user"] = {
                "id": user.id,
                "email": user.email,
                "kinde_id": user.kinde_id,
                "plan": user.plan,
                "is_subscribed": user.isSubscribed,
                "project_limit": user.project_limit
            }
            
            print(f"\n=== {'WOULD DELETE' if dry_run else 'DELETING'} USER ===")
            print(f"User ID: {user.id}")
            print(f"Email: {user.email}")
            print(f"Plan: {user.plan}")
            
            # 2. Find all user's projects
            projects_query = select(Project).where(Project.owner_id == user.id)
            projects_result = await session.execute(projects_query)
            projects = projects_result.scalars().all()
            
            print(f"\nFound {len(projects)} projects for this user")
            
            # 3. For each project, delete all associated data
            for project in projects:
                project_summary = {
                    "project_id": project.project_id,
                    "name": project.name,
                    "env_id": project.env_id,
                    "threads": 0,
                    "deployments": 0,
                    "memory_modules": 0
                }
                
                print(f"\n{'-'*50}")
                print(f"Project: {project.name} (ID: {project.project_id})")
                
                # 3a. Delete threads
                threads_query = select(ProjectThread).where(ProjectThread.project_id == project.project_id)
                threads_result = await session.execute(threads_query)
                threads = threads_result.scalars().all()
                
                project_summary["threads"] = len(threads)
                deletion_summary["threads"] += len(threads)
                print(f"Found {len(threads)} threads")
                
                if not dry_run:
                    for thread in threads:
                        await session.delete(thread)
                
                # 3b. Delete deployments
                deployment_query = select(Deployment).where(Deployment.project_id == project.project_id)
                deployment_result = await session.execute(deployment_query)
                deployment = deployment_result.scalar_one_or_none()
                
                if deployment:
                    project_summary["deployments"] = 1
                    deletion_summary["deployments"] += 1
                    print(f"Found deployment: {deployment.deployment_url}")
                    
                    if not dry_run:
                        await session.delete(deployment)
                
                # 3c. Delete memory modules
                memory_query = select(MemoryModule).where(MemoryModule.project_id == project.project_id)
                memory_result = await session.execute(memory_query)
                memory_modules = memory_result.scalars().all()
                
                project_summary["memory_modules"] = len(memory_modules)
                deletion_summary["memory_modules"] += len(memory_modules)
                print(f"Found {len(memory_modules)} memory modules")
                
                if not dry_run:
                    for module in memory_modules:
                        await session.delete(module)
                
                # 3d. Delete the project itself
                if not dry_run:
                    await session.delete(project)
                
                deletion_summary["projects"].append(project_summary)
            
            # 4. Finally, delete the user
            if not dry_run:
                await session.delete(user)
                await session.commit()
                print(f"\n✅ User {email} and all associated data have been deleted")
            else:
                print("\n🔍 DRY RUN: No data was actually deleted")
            
            # 5. Print summary
            print("\n=== Deletion Summary ===")
            print(f"User: {deletion_summary['user']['email']}")
            print(f"Projects: {len(deletion_summary['projects'])}")
            print(f"Threads: {deletion_summary['threads']}")
            print(f"Deployments: {deletion_summary['deployments']}")
            print(f"Memory Modules: {deletion_summary['memory_modules']}")
            
            # 6. Save detailed log to file
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = f"user_deletion_{email.replace('@', '_at_')}_{timestamp}.json"
            
            with open(log_file, 'w') as f:
                json.dump({
                    "timestamp": datetime.datetime.now().isoformat(),
                    "dry_run": dry_run,
                    "deletion_summary": deletion_summary
                }, f, indent=2)
            
            print(f"\nDetailed deletion log saved to: {log_file}")
            
            return True
            
    except Exception as e:
        print(f"Error during user deletion: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await db.close()

async def fetch_comprehensive_project_details(project_id: str):
    """
    Fetch comprehensive details for a project, including related information
    from environments and deployment tables.
    
    Args:
        project_id (str): The ID of the project to fetch details for
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query the project
        project_query = select(Project).where(Project.project_id == project_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if not project:
            print(f"No project found with ID: {project_id}")
            return

        # Get owner details
        user_query = select(User).where(User.id == project.owner_id)
        user_result = await session.execute(user_query)
        owner = user_result.scalar_one_or_none()

        # Get environment details from project's env_id
        env_id = project.env_id
        env_query = select(Environment).where(Environment.env_id == env_id)
        env_result = await session.execute(env_query)
        environment_by_env_id = env_result.scalar_one_or_none()

        # Also check for environment by project_id
        env_query2 = select(Environment).where(Environment.project_id == project_id)
        env_result2 = await session.execute(env_query2)
        environment_by_project_id = env_result2.scalar_one_or_none()

        # Get deployment details
        deployment_query = select(Deployment).where(Deployment.project_id == project_id)
        deployment_result = await session.execute(deployment_query)
        deployment = deployment_result.scalar_one_or_none()

        # Print comprehensive project details
        print("\n" + "="*80)
        print(f"COMPREHENSIVE PROJECT DETAILS: {project.name}")
        print("="*80)

        print("\n=== Basic Project Information ===")
        print(f"Project ID: {project.project_id}")
        print(f"Name: {project.name}")
        print(f"Creation Date: {project.creation_date}")
        print(f"Last Updated: {project.last_updated_date}")
        print(f"Environment ID (from project): {project.env_id}")
        print(f"Preview Image URL: {project.preview_image_url}")
        print(f"Onboarding Completed: {project.onboarding_completed}")
        print(f"Is Public: {project.isPublic}")
        
        # Print token and request usage
        print("\n=== Usage Statistics ===")
        print(f"Total Input Tokens: {project.total_input_token:,}")
        print(f"Total Output Tokens: {project.total_output_token:,}")
        print(f"Total Requests: {project.total_request:,}")

        # Print team information
        try:
            team_emails = json.loads(project.team_emails)
            print("\n=== Team Information ===")
            print(f"Team Size: {len(team_emails)}")
            if team_emails:
                print("Team Emails:")
                for email in team_emails:
                    print(f"- {email}")
        except (ValueError, TypeError, AttributeError):
            print(f"Team Emails (raw): {project.team_emails}")

        # Print owner information
        if owner:
            print("\n=== Owner Information ===")
            print(f"Owner ID: {owner.id}")
            print(f"Owner Email: {owner.email}")
            print(f"Owner Plan: {owner.plan}")
            print(f"Is Subscribed: {owner.isSubscribed}")
            print(f"Free Total Tokens: {owner.free_total_token:,}")
        else:
            print("\n=== Owner Information ===")
            print(f"Owner ID: {project.owner_id}")
            print("Owner details not found")

        # Print environment information by env_id
        print("\n=== Environment Information (by env_id) ===")
        if environment_by_env_id:
            print(f"Environment ID: {environment_by_env_id.env_id}")
            print(f"Status: {environment_by_env_id.status}")
            print(f"Assigned: {environment_by_env_id.assigned}")
            print(f"Project ID in Environment: {environment_by_env_id.project_id}")
            print(f"GitHub Repository: {environment_by_env_id.github_repo}")
            print(f"Template: {environment_by_env_id.template}")
        else:
            print(f"No environment found with env_id: {env_id}")

        # Print environment information by project_id
        print("\n=== Environment Information (by project_id) ===")
        if environment_by_project_id:
            print(f"Environment ID: {environment_by_project_id.env_id}")
            print(f"Status: {environment_by_project_id.status}")
            print(f"Assigned: {environment_by_project_id.assigned}")
            print(f"Project ID in Environment: {environment_by_project_id.project_id}")
            print(f"GitHub Repository: {environment_by_project_id.github_repo}")
            print(f"Template: {environment_by_project_id.template}")
        else:
            print(f"No environment found with project_id: {project_id}")

        # Print deployment information
        if deployment:
            print("\n=== Deployment Information ===")
            print(f"Deployment URL: {deployment.deployment_url}")
            print(f"Production URL: {deployment.production_url}")
            print(f"Last Deployed: {deployment.last_deployed}")
            print(f"Is Deployed: {deployment.is_deployed}")
            print(f"Vercel Token: {deployment.vercel_token}")
        else:
            print("\n=== Deployment Information ===")
            print("No deployment found for this project")

    await db.close()

async def clone_and_replace_environment(project_id: str):
    """
    Clone the GitHub repository from an existing environment and create a new environment,
    then associate it with the same project.
    
    Args:
        project_id (str): The ID of the project to update with a new environment
    """
    db = Database()
    
    try:
        async with db.get_async_session() as session:
            # Get project details
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()
            
            if not project:
                print(f"No project found with ID: {project_id}")
                return
                
            print(f"\n=== Cloning Environment for Project: {project.name} ===")
            
            # Get current environment details
            env_query = select(Environment).where(
                (Environment.project_id == project_id) | 
                (Environment.env_id == project.env_id)
            )
            env_result = await session.execute(env_query)
            environment = env_result.scalar_one_or_none()
            
            if not environment or not environment.github_repo:
                print("No environment or GitHub repository found for this project")
                return
                
            github_url = environment.github_repo
            print(f"Found GitHub repository: {github_url}")
            
            # First, remove the project_id from the existing environment to avoid unique constraint error
            print(f"Removing project association from existing environment: {environment.env_id}")
            environment.project_id = None
            environment.assigned = False
            await session.commit()
            
            # Import GitHub repo using EnvManager - fixed import path
            from envs.envs import EnvManager
            env_manager = EnvManager()
            
            print("Creating new environment from GitHub repository...")
            new_env = await env_manager.import_github_repo(github_url, project_id)
            
            if not new_env:
                print("Failed to create new environment")
                return
                
            new_env_id = new_env.get('env_id')
            print(f"New environment created with ID: {new_env_id}")
            
            # Update project with new environment ID
            old_env_id = project.env_id
            project.env_id = new_env_id
            await session.commit()
            
            print(f"Updated project environment ID from {old_env_id} to {new_env_id}")
            
            # Print comprehensive details of the updated project
            await fetch_comprehensive_project_details(project_id)
            
            return new_env_id
            
    except Exception as e:
        print(f"Error cloning environment: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
        
    finally:
        await db.close()

async def update_environment_github_repo(env_id: str, new_github_url: str):
    """
    Update the GitHub repository URL for an environment.
    
    Args:
        env_id (str): The ID of the environment to update
        new_github_url (str): The new GitHub repository URL
    """
    db = Database()
    
    try:
        async with db.get_async_session() as session:
            # Get environment details
            env_query = select(Environment).where(Environment.env_id == env_id)
            env_result = await session.execute(env_query)
            environment = env_result.scalar_one_or_none()
            
            if not environment:
                print(f"No environment found with ID: {env_id}")
                return False
                
            print(f"\n=== Updating GitHub Repository for Environment: {env_id} ===")
            print(f"Current GitHub Repository: {environment.github_repo}")
            print(f"New GitHub Repository: {new_github_url}")
            
            # Update the GitHub repository URL
            environment.github_repo = new_github_url
            await session.commit()
            
            print("✅ GitHub repository URL updated successfully")
            return True
            
    except Exception as e:
        print(f"Error updating GitHub repository URL: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await db.close()


async def calculate_project_tokens_by_date_range(project_id, start_date=None, end_date=None):
    """
    Calculate token usage for a project within a specific date range.
    
    Args:
        project_id (str): The ID of the project to analyze
        start_date (str, optional): Start date in ISO format (YYYY-MM-DD)
        end_date (str, optional): End date in ISO format (YYYY-MM-DD)
    
    Returns:
        dict: Token usage statistics within the date range
    """
    db = Database()

    # Format date strings for comparison
    if start_date:
        start_date = f"{start_date}T00:00:00"
    if end_date:
        end_date = f"{end_date}T23:59:59"

    async with db.get_async_session() as session:
        # First verify the project exists
        project_query = select(Project).where(Project.project_id == project_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if not project:
            print(f"No project found with ID: {project_id}")
            return None

        # Get all threads for this project
        threads_query = select(ProjectThread).where(ProjectThread.project_id == project_id)
        
        # Apply date filters if provided
        if start_date:
            threads_query = threads_query.where(ProjectThread.creation_date >= start_date)
        if end_date:
            threads_query = threads_query.where(ProjectThread.creation_date <= end_date)
            
        threads_result = await session.execute(threads_query)
        threads = threads_result.scalars().all()

        # Initialize counters and data structures
        total_tokens = 0
        total_messages = 0
        total_messages_with_tokens = 0
        daily_tokens = {}
        model_usage = {}

        print(f"\n=== Token Usage for Project: {project.name} ===")
        print(f"Project ID: {project_id}")
        print(f"Date Range: {start_date or 'Beginning'} to {end_date or 'Present'}")

        for thread in threads:
            thread_date = thread.creation_date.split('T')[0] if thread.creation_date else "Unknown"
            
            if thread.messages:
                messages = json.loads(thread.messages)
                for msg in messages:
                    total_messages += 1
                    
                    if isinstance(msg, dict) and 'total_tokens' in msg:
                        tokens = msg['total_tokens']
                        total_tokens += tokens
                        total_messages_with_tokens += 1
                        
                        # Track daily usage
                        if thread_date != "Unknown":
                            if thread_date not in daily_tokens:
                                daily_tokens[thread_date] = 0
                            daily_tokens[thread_date] += tokens
                        
                        # Track model usage if available
                        model = msg.get('model', 'unknown')
                        if model not in model_usage:
                            model_usage[model] = 0
                        model_usage[model] += tokens

        # Prepare results
        results = {
            "project_name": project.name,
            "project_id": project_id,
            "date_range": {
                "start": start_date,
                "end": end_date
            },
            "total_tokens": total_tokens,
            "total_messages": total_messages,
            "messages_with_tokens": total_messages_with_tokens,
            "messages_without_tokens": total_messages - total_messages_with_tokens,
            "daily_tokens": daily_tokens,
            "model_usage": model_usage,
            "stored_project_tokens": {
                "input_tokens": project.total_input_token,
                "output_tokens": project.total_output_token,
                "combined_tokens": project.total_input_token + project.total_output_token
            }
        }

        # Print summary
        print(f"\nTotal tokens used: {total_tokens:,}")
        print(f"Total messages: {total_messages}")
        print(f"Messages with token counts: {total_messages_with_tokens}")
        print(f"Messages without token counts: {total_messages - total_messages_with_tokens}")
        
        # Print daily breakdown
        print("\n=== Daily Token Usage ===")
        for date in sorted(daily_tokens.keys()):
            print(f"{date}: {daily_tokens[date]:,} tokens")
        
        # Print model usage
        if model_usage:
            print("\n=== Model Usage ===")
            for model, tokens in model_usage.items():
                print(f"{model}: {tokens:,} tokens")
        
        # Print stored token counts
        print("\nStored project token counts:")
        print(f"Total input tokens: {project.total_input_token:,}")
        print(f"Total output tokens: {project.total_output_token:,}")
        print(f"Combined stored tokens: {(project.total_input_token + project.total_output_token):,}")

    await db.close()
    return results

async def calculate_tokens_for_multiple_projects_by_date(project_ids, start_date=None, end_date=None):
    """
    Calculate token usage for multiple projects within a specific date range.
    
    Args:
        project_ids (list): List of project IDs to analyze
        start_date (str, optional): Start date in ISO format (YYYY-MM-DD)
        end_date (str, optional): End date in ISO format (YYYY-MM-DD)
    
    Returns:
        dict: Combined token usage statistics
    """
    print("\n=== Token Usage Summary for Multiple Projects ===")
    print(f"Date Range: {start_date or 'Beginning'} to {end_date or 'Present'}")
    
    combined_results = {
        "projects": [],
        "total_tokens": 0,
        "total_messages": 0,
        "daily_tokens": {},
        "model_usage": {}
    }
    
    for project_id in project_ids:
        print(f"\n{'='*50}")
        results = await calculate_project_tokens_by_date_range(project_id, start_date, end_date)
        print(f"{'='*50}")
        
        if results:
            combined_results["projects"].append({
                "project_name": results["project_name"],
                "project_id": results["project_id"],
                "total_tokens": results["total_tokens"]
            })
            
            combined_results["total_tokens"] += results["total_tokens"]
            combined_results["total_messages"] += results["total_messages"]
            
            # Combine daily tokens
            for date, tokens in results["daily_tokens"].items():
                if date not in combined_results["daily_tokens"]:
                    combined_results["daily_tokens"][date] = 0
                combined_results["daily_tokens"][date] += tokens
            
            # Combine model usage
            for model, tokens in results["model_usage"].items():
                if model not in combined_results["model_usage"]:
                    combined_results["model_usage"][model] = 0
                combined_results["model_usage"][model] += tokens
    
    # Print combined summary
    print("\n=== Combined Token Usage Summary ===")
    print(f"Total Projects: {len(combined_results['projects'])}")
    print(f"Total Tokens: {combined_results['total_tokens']:,}")
    print(f"Total Messages: {combined_results['total_messages']:,}")
    
    # Print daily breakdown
    print("\n=== Combined Daily Token Usage ===")
    for date in sorted(combined_results["daily_tokens"].keys()):
        print(f"{date}: {combined_results['daily_tokens'][date]:,} tokens")
    
    # Print model usage
    if combined_results["model_usage"]:
        print("\n=== Combined Model Usage ===")
        for model, tokens in combined_results["model_usage"].items():
            print(f"{model}: {tokens:,} tokens")
    
    # Print project breakdown
    print("\n=== Project Breakdown ===")
    for project in combined_results["projects"]:
        print(f"{project['project_name']}: {project['total_tokens']:,} tokens")
    
    return combined_results

async def export_token_usage_to_csv(project_id, start_date=None, end_date=None, output_file=None):
    """
    Export token usage data to a CSV file for analysis.
    
    Args:
        project_id (str): The ID of the project to analyze
        start_date (str, optional): Start date in ISO format (YYYY-MM-DD)
        end_date (str, optional): End date in ISO format (YYYY-MM-DD)
        output_file (str, optional): Output CSV file path
    """
    import csv
    
    # Generate default filename if not provided
    if output_file is None:
        project_id_short = project_id[:8]
        date_range = f"{start_date or 'start'}_to_{end_date or 'end'}"
        output_file = f"token_usage_{project_id_short}_{date_range}.csv"
    
    # Get token usage data
    results = await calculate_project_tokens_by_date_range(project_id, start_date, end_date)
    
    if not results:
        print("No data to export")
        return
    
    # Write daily usage to CSV
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['Date', 'Tokens Used'])
        
        # Write daily data
        for date in sorted(results["daily_tokens"].keys()):
            writer.writerow([date, results["daily_tokens"][date]])
        
        # Add summary row
        writer.writerow(['', ''])
        writer.writerow(['Total', results["total_tokens"]])
        
        # Add model usage
        if results["model_usage"]:
            writer.writerow(['', ''])
            writer.writerow(['Model', 'Tokens Used'])
            for model, tokens in results["model_usage"].items():
                writer.writerow([model, tokens])
    
    print(f"\nToken usage data exported to: {output_file}")
    return output_file

# ... existing code ...

async def calculate_tokens_for_specific_date_range():
    """
    Calculate token usage for predefined projects within a specific date range.
    This function analyzes token usage from March 26, 2025 to March 28, 2025.
    """
    project_ids = [
        "004f5801-6086-4bd5-9d22-ef2619169d20",  # yourchoicetickets.com
        "1508158a-5296-480e-91a6-604f00568176",  # yourchoicetickets.com
        "1b52e811-65ad-415b-b72b-f8a51dc385a6",  # yct.com
        "d63d1c67-43a9-4bde-83cf-7dcdd809b98f"  # yourchoicetickets.com Remix
    ]
    
    start_date = "2025-03-25"
    end_date = "2025-03-28"
    
    print("\n=== Token Usage Analysis ===")
    print(f"Date Range: {start_date} to {end_date}")
    print("Projects: YourChoiceTickets.com and related projects")
    
    # Call the function to analyze multiple projects by date range
    results = await calculate_tokens_for_multiple_projects_by_date(
        project_ids, 
        start_date=start_date, 
        end_date=end_date
    )
    
    # Export results to CSV
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_file = f"token_usage_yct_{start_date}_to_{end_date}_{timestamp}.csv"
    
    # Create a more detailed CSV with project breakdown
    import csv
    with open(csv_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['Date', 'Total Tokens'] + [p['project_name'] for p in results['projects']])
        
        # Write daily data
        for date in sorted(results["daily_tokens"].keys()):
            row = [date, results["daily_tokens"][date]]
            
            # Add per-project data if available
            for project in results['projects']:
                project_id = project['project_id']
                project_data = next((p for p in results['projects'] if p['project_id'] == project_id), None)
                if project_data and 'daily_tokens' in project_data and date in project_data['daily_tokens']:
                    row.append(project_data['daily_tokens'][date])
                else:
                    row.append(0)
                    
            writer.writerow(row)
        
        # Add summary row
        writer.writerow(['Total', results["total_tokens"]] + [p['total_tokens'] for p in results['projects']])
    
    print(f"\nDetailed token usage exported to: {csv_file}")
    return results

# You can run this function directly to analyze the specified date range
# await calculate_tokens_for_specific_date_range()    

async def remove_vercel_token(project_id: str, confirm: bool = False):
    """
    Remove or reset the Vercel token for a project's deployment.
    
    Args:
        project_id (str): The ID of the project whose token should be removed
        confirm (bool): Set to True to confirm the token removal
    
    Returns:
        bool: True if token was successfully removed, False otherwise
    """
    if not confirm:
        print("\n⚠️ TOKEN REMOVAL NOT CONFIRMED ⚠️")
        print("To confirm removal, call this function with confirm=True")
        return False
    
    db = Database()
    
    try:
        async with db.get_async_session() as session:
            # Get project details first for logging
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()
            
            if not project:
                print(f"No project found with ID: {project_id}")
                return False
            
            # Get deployment details
            deployment_query = select(Deployment).where(Deployment.project_id == project_id)
            deployment_result = await session.execute(deployment_query)
            deployment = deployment_result.scalar_one_or_none()
            
            if not deployment:
                print(f"No deployment found for project: {project.name} (ID: {project_id})")
                return False
            
            # Store original token for logging (masked for security)
            original_token = deployment.vercel_token
            masked_token = None
            if original_token:
                # Mask the token for display (show only first 4 and last 4 characters)
                if len(original_token) > 8:
                    masked_token = original_token[:4] + '*' * (len(original_token) - 8) + original_token[-4:]
                else:
                    masked_token = '****' + original_token[-4:] if len(original_token) > 4 else original_token
            
            # Reset the token
            deployment.vercel_token = None
            await session.commit()
            
            print("\n=== Vercel Token Removed ===")
            print(f"Project: {project.name}")
            print(f"Project ID: {project_id}")
            print(f"Original Token: {masked_token or 'None'}")
            print("Current Token: None")
            print(f"Update Time: {datetime.datetime.now().isoformat()}")
            
            return True
            
    except Exception as e:
        print(f"Error removing Vercel token: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await db.close()

async def set_project_public_status(project_id: str, is_public: bool = True):
    """
    Update a project's public status.
    
    Args:
        project_id (str): The ID of the project to update
        is_public (bool): Whether the project should be public (default: True)
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query the project
        project_query = select(Project).where(Project.project_id == project_id)
        project_result = await session.execute(project_query)
        project = project_result.scalar_one_or_none()

        if not project:
            print(f"No project found with ID: {project_id}")
            return

        # Store original status for logging
        original_status = project.isPublic
        
        # Update the public status
        project.isPublic = is_public
        await session.commit()
        
        print("\n=== Project Public Status Updated ===")
        print(f"Project: {project.name}")
        print(f"Project ID: {project_id}")
        print(f"Previous Status: {original_status}")
        print(f"New Status: {project.isPublic}")
        print(f"Update Time: {datetime.datetime.now().isoformat()}")

    await db.close()

async def clear_deployment_info(project_id: str):
    """
    Clear all deployment information for a specific project.
    
    Args:
        project_id (str): The ID of the project to clear deployment info for
    """
    db = Database()

    async with db.get_async_session() as session:
        # Query the deployment for the project
        deployment_query = select(Deployment).where(Deployment.project_id == project_id)
        deployment_result = await session.execute(deployment_query)
        deployment = deployment_result.scalar_one_or_none()

        if deployment:
            # Store original values for logging
            original_info = {
                "deployment_url": deployment.deployment_url,
                "production_url": deployment.production_url,
                "last_deployed": deployment.last_deployed,
                "is_deployed": deployment.is_deployed
            }
            
            # Clear all deployment information
            deployment.deployment_url = None
            deployment.production_url = None
            deployment.last_deployed = None
            deployment.is_deployed = False
            await session.commit()
            
            print("\n=== Deployment Information Cleared ===")
            print(f"Project ID: {project_id}")
            print("\nOriginal Values:")
            print(f"Deployment URL: {original_info['deployment_url']}")
            print(f"Production URL: {original_info['production_url']}")
            print(f"Last Deployed: {original_info['last_deployed']}")
            print(f"Is Deployed: {original_info['is_deployed']}")
            
            print("\nNew Values:")
            print("All deployment information has been cleared")
        else:
            print(f"No deployment found for project ID: {project_id}")

    await db.close()

async def batch_delete_users(email_list: List[str], confirm: bool = False, dry_run: bool = True) -> Dict:
    """
    Delete multiple user accounts in batch.
    
    Args:
        email_list (List[str]): List of emails to delete
        confirm (bool): Set to True to confirm deletion
        dry_run (bool): If True, only show what would be deleted
    
    Returns:
        Dict: Results of the batch deletion process
    """
    print("\n=== Batch User Deletion ===")
    print(f"Total users to process: {len(email_list)}")
    print(f"Confirm deletion: {confirm}")
    print(f"Dry run: {dry_run}")
    
    results = {
        "successful": [],
        "failed": [],
        "not_found": []
    }
    
    # Remove duplicates while preserving order
    email_list = list(dict.fromkeys(email_list))
    
    for i, email in enumerate(email_list, 1):
        print(f"\nProcessing {i}/{len(email_list)}: {email}")
        try:
            success = await delete_user_account(email.strip(), confirm=confirm, dry_run=dry_run)
            if success:
                results["successful"].append(email)
            else:
                results["failed"].append(email)
        except Exception as e:
            print(f"Error processing {email}: {str(e)}")
            results["failed"].append(email)
    
    # Print summary
    print("\n=== Deletion Summary ===")
    print(f"Total processed: {len(email_list)}")
    print(f"Successful: {len(results['successful'])}")
    print(f"Failed: {len(results['failed'])}")
    
    if results["failed"]:
        print("\nFailed deletions:")
        for email in results["failed"]:
            print(f"- {email}")
    
    # Save results to a log file
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"user_deletion_log_{timestamp}.json"
    
    log_data = {
        "timestamp": datetime.datetime.now().isoformat(),
        "dry_run": dry_run,
        "total_emails": len(email_list),
        "successful_deletions": results['successful'],
        "failed_deletions": results['failed']
    }
    
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=2)
    
    print(f"\nDetailed log saved to: {log_file}")
    return results

async def delete_users_from_list(emails_file: str = None, confirm: bool = False, dry_run: bool = True):
    """
    Delete users from a list of emails, either provided directly or from a file.
    
    Args:
        emails_file (str, optional): Path to file containing emails (one per line)
        confirm (bool): Set to True to confirm deletion
        dry_run (bool): If True, only show what would be deleted
    """
    emails_to_delete = [
        "<EMAIL>"
    ]
    
    if emails_file:
        try:
            with open(emails_file, 'r') as f:
                emails_to_delete = [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"Error reading emails file: {str(e)}")
            return
    
    # First run in dry-run mode
    print("\n=== STEP 1: DRY RUN ===")
    dry_run_results = await batch_delete_users(emails_to_delete, confirm=False, dry_run=True)
    
    if not confirm:
        print("\nDry run complete. To perform actual deletion, run again with confirm=True")
        return
    
    # If confirmed, proceed with actual deletion
    print("\n=== STEP 2: ACTUAL DELETION ===")
    print("Proceeding with actual deletion...")
    deletion_results = await batch_delete_users(emails_to_delete, confirm=True, dry_run=False)
    
    return deletion_results

async def count_free_tier_users():
    """
    Count and print the number of users on the free-tier plan (plan == 'free-tier'),
    show the cumulative and average free_total_token, and show theoretical and used tokens.
    Also count how many projects free-tier users have, and calculate stats for users who have at least one project.
    """
    db = Database()
    async with db.get_async_session() as session:
        # Get all free-tier users
        query = select(User).where(User.plan == 'free-tier')
        result = await session.execute(query)
        free_users = result.scalars().all()
        count = len(free_users)
        print("\n=== Free-Tier User Count ===")
        print(f"Total users on free-tier plan: {count}")
        if count > 0:
            total_tokens = sum(user.free_total_token or 0 for user in free_users)
            avg_tokens = total_tokens / count if count else 0
            theoretical_total = 100_000 * count
            tokens_used = theoretical_total - total_tokens
            percent_used = (tokens_used / theoretical_total * 100) if theoretical_total else 0
            print(f"Cumulative free_total_token: {total_tokens:,}")
            print(f"Average free_total_token per user: {avg_tokens:,.2f}")
            print("\n--- Token Usage Stats ---")
            print(f"Theoretical total tokens (if all started with 100,000): {theoretical_total:,}")
            print(f"Tokens used: {tokens_used:,}")
            print(f"Percent of tokens used: {percent_used:.2f}%")

            # Count projects for free-tier users
            user_ids = [user.id for user in free_users]
            project_query = select(Project).where(Project.owner_id.in_(user_ids))
            project_result = await session.execute(project_query)
            projects = project_result.scalars().all()
            total_projects = len(projects)
            print("\n--- Project Stats ---")
            print(f"Total projects owned by free-tier users: {total_projects}")
            avg_projects_per_user = total_projects / count if count else 0
            print(f"Average projects per user: {avg_projects_per_user:.2f}")

            # Users who have at least one project
            from collections import defaultdict
            user_project_count = defaultdict(int)
            for project in projects:
                user_project_count[project.owner_id] += 1
            users_with_projects = [uid for uid, cnt in user_project_count.items() if cnt > 0]
            num_users_with_projects = len(users_with_projects)
            total_projects_for_users_with_projects = sum(user_project_count[uid] for uid in users_with_projects)
            avg_projects_per_active_user = (
                total_projects_for_users_with_projects / num_users_with_projects if num_users_with_projects else 0
            )
            # Token usage for these users
            tokens_used_by_active = 0
            total_tokens_active = 0
            for user in free_users:
                if user.id in users_with_projects:
                    tokens_used_by_active += (100_000 - (user.free_total_token or 0))
                    total_tokens_active += (user.free_total_token or 0)
            avg_tokens_used_by_active = (
                tokens_used_by_active / num_users_with_projects if num_users_with_projects else 0
            )
            theoretical_total_active = 100_000 * num_users_with_projects
            percent_used_active = (tokens_used_by_active / theoretical_total_active * 100) if theoretical_total_active else 0
            print("\n--- Free-Tier Users WITH Projects ---")
            print(f"Users on free-tier with at least one project: {num_users_with_projects}")
            print(f"Total projects (for these users): {total_projects_for_users_with_projects}")
            print(f"Average projects per such user: {avg_projects_per_active_user:.2f}")
            print(f"Total tokens used by these users: {tokens_used_by_active:,}")
            print(f"Average tokens used per such user: {avg_tokens_used_by_active:,.2f}")
            print(f"Percent of tokens used (these users): {percent_used_active:.2f}%")

            print("\nSample emails (up to 20):")
            for user in free_users[:20]:
                print(f"- {user.email}")

        april_start = "2025-04-01T00:00:00"
        april_end = "2025-04-30T23:59:59"

        # 1. Get all free-tier user IDs
        free_user_ids = [user.id for user in (await session.execute(select(User).where(User.plan == 'free-tier'))).scalars()]

        # 2. Get all projects for these users
        project_ids = [p.project_id for p in (await session.execute(select(Project).where(Project.owner_id.in_(free_user_ids)))).scalars()]

        # 3. Find projects with thread activity in April
        active_project_ids = set()
        for project_id in project_ids:
            thread = (await session.execute(
                select(ProjectThread)
                .where(
                    and_(
                        ProjectThread.project_id == project_id,
                        ProjectThread.last_updated_date >= april_start,
                        ProjectThread.last_updated_date <= april_end
                    )
                )
            )).scalars().first()
            if thread:
                active_project_ids.add(project_id)

        print(f"Active free-tier projects in April: {len(active_project_ids)}")

        # Build user_id -> [all projects, active projects in April]
        # Map project_id to owner_id
        project_id_to_owner = {}
        for p in (await session.execute(select(Project).where(Project.project_id.in_(project_ids)))).scalars():
            project_id_to_owner[p.project_id] = p.owner_id

        # Map user_id to all their projects
        user_projects = defaultdict(list)
        for pid, uid in project_id_to_owner.items():
            user_projects[uid].append(pid)

        # Map user_id to their active projects in April
        user_active_projects = defaultdict(list)
        for pid in active_project_ids:
            uid = project_id_to_owner.get(pid)
            if uid:
                user_active_projects[uid].append(pid)

        print("\n--- Free-Tier Users WITH Active Projects in April ---")
        print(f"User count: {len(user_active_projects)}\n")
        april_active_user_ids = set(user_active_projects.keys())
        for user in free_users:
            uid = user.id
            if uid in user_active_projects:
                print(f"Email: {user.email}")
                print(f"User ID: {uid}")
                print(f"Total projects owned: {len(user_projects[uid])}")
                print(f"Active projects in April: {len(user_active_projects[uid])}")
                print(f"free_total_token: {user.free_total_token}")
                print("-")

        # Summary stats for these users
        n_active_users = len(april_active_user_ids)
        if n_active_users > 0:
            total_tokens_active = sum(user.free_total_token or 0 for user in free_users if user.id in april_active_user_ids)
            tokens_used_active = 100_000 * n_active_users - total_tokens_active
            avg_tokens_used_active = tokens_used_active / n_active_users if n_active_users else 0
            theoretical_total_active = 100_000 * n_active_users
            percent_used_active = (tokens_used_active / theoretical_total_active * 100) if theoretical_total_active else 0
            total_projects_active_users = sum(len(user_projects[uid]) for uid in april_active_user_ids)
            avg_projects_per_active_user = total_projects_active_users / n_active_users if n_active_users else 0

            print("\n--- Stats for Free-Tier Users WITH Active Projects in April ---")
            print(f"Users: {n_active_users}")
            print(f"Total tokens used: {tokens_used_active:,}")
            print(f"Average tokens used per such user: {avg_tokens_used_active:,.2f}")
            print(f"Percent of tokens used: {percent_used_active:.2f}%")
            print(f"Total projects owned by these users: {total_projects_active_users}")
            print(f"Average projects per such user: {avg_projects_per_active_user:.2f}")
    await db.close()

async def export_project_threads_to_json(project_id: str, output_file: str = None):
    """
    Export all threads and their messages for a given project to a JSON file.
    Args:
        project_id (str): The project ID to export threads from
        output_file (str, optional): Output JSON file path. Defaults to project_{project_id}_threads.json
    """
    db = Database()
    if output_file is None:
        output_file = f"project_{project_id}_threads.json"
    try:
        async with db.get_async_session() as session:
            # Verify project exists
            project_query = select(Project).where(Project.project_id == project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()
            if not project:
                print(f"No project found with ID: {project_id}")
                return
            # Get all threads for this project
            threads_query = select(ProjectThread).where(ProjectThread.project_id == project_id)
            threads_result = await session.execute(threads_query)
            threads = threads_result.scalars().all()
            export_data = {
                "project_id": project_id,
                "project_name": project.name,
                "threads": []
            }
            for thread in threads:
                thread_data = {
                    "thread_id": thread.thread_id,
                    "creation_date": thread.creation_date,
                    "last_updated_date": thread.last_updated_date,
                    "page_route": thread.page_route,
                    "name": thread.name,
                    "messages": []
                }
                if thread.messages:
                    try:
                        thread_data["messages"] = json.loads(thread.messages)
                    except Exception as e:
                        print(f"Error parsing messages for thread {thread.thread_id}: {e}")
                        thread_data["messages"] = thread.messages
                export_data["threads"].append(thread_data)
            # Write to JSON file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2)
            print(f"Exported {len(export_data['threads'])} threads to {output_file}")
    except Exception as e:
        print(f"Error exporting threads: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        await db.close()

async def import_threads_to_project(json_file: str, target_project_id: str):
    """
    Import threads and their messages from a JSON file into a specified project.
    Args:
        json_file (str): Path to the JSON file exported by export_project_threads_to_json
        target_project_id (str): The project ID to import threads into
    """
    db = Database()
    try:
        # Load threads from JSON file
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        threads = data.get('threads', [])
        print(f"Loaded {len(threads)} threads from {json_file}")
        async with db.get_async_session() as session:
            # Verify target project exists
            project_query = select(Project).where(Project.project_id == target_project_id)
            project_result = await session.execute(project_query)
            project = project_result.scalar_one_or_none()
            if not project:
                print(f"No project found with ID: {target_project_id}")
                return
            imported_count = 0
            for thread in threads:
                # Create new ProjectThread (do not copy thread_id)
                new_thread = ProjectThread(
                    project_id=target_project_id,
                    messages=json.dumps(thread.get('messages', [])),
                    creation_date=thread.get('creation_date'),
                    last_updated_date=thread.get('last_updated_date'),
                    page_route=thread.get('page_route'),
                    name=thread.get('name')
                )
                session.add(new_thread)
                imported_count += 1
            await session.commit()
            print(f"Imported {imported_count} threads into project {target_project_id}")
    except Exception as e:
        print(f"Error importing threads: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        await db.close()

if __name__ == "__main__":
    try:
        email = "<EMAIL>"
        project_id = "a9adaeee-461a-45e2-a328-4bceab8d6bab"
        
        # asyncio.run(get_available_environment_count())
        # asyncio.run(count_free_tier_users())
        # asyncio.run(export_project_threads_to_json("69e4231f-d660-4622-bb75-fc16522a4924"))
        # asyncio.run(import_threads_to_project("project_a9adaeee-461a-45e2-a328-4bceab8d6bab_threads.json", "a9adaeee-461a-45e2-a328-4bceab8d6bab"))
        # asyncio.run(fetch_user_details_by_email(email))
        # asyncio.run(fetch_user_details_by_email(email))
        # asyncio.run(calculate_project_total_tokens("a734c386-d99b-4d91-b1dc-551d591a4d90"))

        # asyncio.run(reward_user_with_tokens(email,14_000_000))
        # asyncio.run(reward_user_with_tokens(email,2500000))
        # asyncio.run(fetch_user_details_by_email(email))
        # asyncio.run(set_user_plan(email))  # Add this line to test the function
        # asyncio.run(fetch_user_details_by_email(email))
        # asyncio.run(transfer_project("a9adaeee-461a-45e2-a328-4bceab8d6bab","<EMAIL>"))
        # new_url = "https://mystery-box-sandbox-0b049344.vercel.app/"
        # asyncio.run(update_deployment_url("4073e401-43c9-4f58-8049-bac00301efb6", new_url))
        # asyncio.run(fetch_project_details(project_id))
        # asyncio.run(clear_deployment_info(project_id))

        # #refunded users
        # refunded_email = "<EMAIL>"
        # asyncio.run(fetch_user_details_by_email(refunded_email))
        # asyncio.run(update_user_tokens_and_requests(refunded_email,0,0))
        # asyncio.run(fetch_user_details_by_email(refunded_email))

        # project_id = "89336cb4-52ae-4f22-b5e9-f9d19697d80b"
        # asyncio.run(fetch_project_details(project_id))
        # asyncio.run(get_environment_details(["sandbox-c955e5ce"]))
        # asyncio.run(check_and_start_environment("sandbox-eeb17c76"))
        # asyncio.run(get_available_environment_count())

        # asyncio.run(calculate_tokens_for_specific_date_range())


        # asyncio.run(r)
        # asyncio.run(cleanup_old_threads(120, batch_size=10))  # Clean threads older than 120 days
        # asyncio.run(fetch_project_github_repos(email=email))  # Add this line to test the function
        # asyncio.run(fetch_comprehensive_project_details(project_id))

        # Uncomment the line below to run the new function
        # asyncio.run(clone_and_replace_environment(project_id))

        # Add the new function to the test section
        # env_id = "sandbox-81a8f604"  # Replace with your environment ID
        # new_github_url = "https://github.com/softgenai/sg-sandbox-eeb17c76-1741675849"
        
        # # Uncomment the line below to run the new function
        # asyncio.run(update_environment_github_repo(env_id, new_github_url))
        # asyncio.run(set_project_public_status(project_id, True))

        # Import threads from source to target project
        asyncio.run(import_threads_to_project(
            "/Users/<USER>/Desktop/work/softgen-core/project_69e4231f-d660-4622-bb75-fc16522a4924_threads.json",
            "a082f953-f36d-4677-93d8-bb6053c28222"
        ))
    except Exception as e:
        print(f"Error: {str(e)}")