{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/code-editor/file-tree/index.tsx"], "sourcesContent": ["import {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/classic/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { LazyModal } from \"@/components/ui/modal\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { getFiles } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useSetFile } from \"@/stores/navigate-file\";\r\nimport {\r\n  ChevronDown,\r\n  ChevronRight,\r\n  DotsVertical,\r\n  FileTextSolid,\r\n  FolderPlusSolid,\r\n  Move,\r\n  Trash,\r\n  Upload,\r\n} from \"@mynaui/icons-react\";\r\nimport { CopyMinusIcon } from \"lucide-react\";\r\nimport React, { lazy, useCallback, useEffect, useMemo, useRef, useState } from \"react\";\r\nimport { Io<PERSON>older, Io<PERSON>older<PERSON><PERSON> } from \"react-icons/io5\";\r\nimport { LuUpload } from \"react-icons/lu\";\r\n\r\nconst MoveFileModalContent = lazy(() => import(\"./move-file-modal\"));\r\nconst NewFileModalContent = lazy(() => import(\"./new-file-modal\"));\r\nconst NewFolderModalContent = lazy(() => import(\"./new-folder-modal\"));\r\nconst DeleteModalContent = lazy(() => import(\"./delete-modal\"));\r\nconst UploadFileModalContent = lazy(() => import(\"./upload-file-modal-content\"));\r\n\r\nexport interface FileNode {\r\n  name: string;\r\n  isDirectory: boolean;\r\n  children?: FileNode[];\r\n}\r\n\r\ninterface FileTreeProps {\r\n  projectId: string;\r\n  onFileSelect: (path: string) => void;\r\n  selectedFile: string;\r\n  className?: string;\r\n  filesAndFolders: FileNode[];\r\n  isLoadingFilePaths: boolean;\r\n  allFilePaths: FileNode[];\r\n  refetchFileTree: () => void;\r\n}\r\n\r\nconst FileTreeSkeleton = () => {\r\n  return (\r\n    <div className=\"p-1\">\r\n      {Array.from({ length: 25 }).map((_, index) => (\r\n        <div key={index} className=\"flex animate-pulse items-center gap-2 rounded-sm pb-1.5\">\r\n          <Skeleton className=\"h-2 w-full rounded-sm\" />\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst flattenFileTree = (nodes: FileNode[], basePath = \"\"): string[] => {\r\n  const paths: string[] = [];\r\n\r\n  nodes.forEach((node) => {\r\n    const fullPath = basePath ? `${basePath}/${node.name}` : node.name;\r\n\r\n    if (!node.isDirectory) {\r\n      paths.push(fullPath);\r\n    } else if (node.children) {\r\n      paths.push(...flattenFileTree(node.children, fullPath));\r\n    }\r\n  });\r\n\r\n  return paths;\r\n};\r\n\r\nconst FileTree: React.FC<FileTreeProps> = ({\r\n  projectId,\r\n  onFileSelect,\r\n  selectedFile,\r\n  className,\r\n  filesAndFolders,\r\n  isLoadingFilePaths,\r\n  allFilePaths,\r\n  refetchFileTree,\r\n}) => {\r\n  const [expandedFolders, setExpandedFolders] = useState<{ [key: string]: boolean }>({});\r\n  const [loadingFolders, setLoadingFolders] = useState<{ [key: string]: boolean }>({});\r\n  const [directoryContents, setDirectoryContents] = useState<{ [key: string]: FileNode[] }>({});\r\n  const setFile = useSetFile();\r\n  const prevFilesAndFoldersRef = useRef(filesAndFolders);\r\n  const previousPathsLengthRef = useRef<number>(0);\r\n  const [showContextMenuModal, setShowContextMenuModal] = useState({\r\n    file: {\r\n      state: false,\r\n      file: \"\",\r\n    },\r\n    folder: {\r\n      state: false,\r\n      file: \"\",\r\n    },\r\n    move: {\r\n      state: false,\r\n      file: \"\",\r\n    },\r\n    delete: { state: false, files: \"\" },\r\n    upload: { state: false, files: \"\" },\r\n  });\r\n\r\n  // Convert FileNode tree to flat array of file paths\r\n  const flatFilePaths = useMemo(() => {\r\n    return flattenFileTree(allFilePaths || []);\r\n  }, [allFilePaths]);\r\n\r\n  // Store the previous filesAndFolders to prevent flickering during updates\r\n  const displayedFilesAndFolders = useMemo(() => {\r\n    // Use previous value if current is empty but we have data from before\r\n    if (filesAndFolders.length === 0 && prevFilesAndFoldersRef.current.length > 0) {\r\n      return prevFilesAndFoldersRef.current;\r\n    }\r\n\r\n    // Otherwise use the current value and update the ref\r\n    prevFilesAndFoldersRef.current = filesAndFolders;\r\n    return filesAndFolders;\r\n  }, [filesAndFolders]);\r\n\r\n  // Load saved expanded folders from localStorage when component mounts or project changes\r\n  useEffect(() => {\r\n    const savedExpandedFolders = localStorage.getItem(`expandedFolders-${projectId}`);\r\n    if (savedExpandedFolders) {\r\n      try {\r\n        setExpandedFolders(JSON.parse(savedExpandedFolders));\r\n      } catch (e) {\r\n        console.error(\"Failed to parse saved expanded folders\", e);\r\n      }\r\n    }\r\n  }, [projectId]);\r\n\r\n  // Save expanded folders to localStorage whenever they change\r\n  useEffect(() => {\r\n    if (Object.keys(expandedFolders).length > 0) {\r\n      localStorage.setItem(`expandedFolders-${projectId}`, JSON.stringify(expandedFolders));\r\n    }\r\n  }, [expandedFolders, projectId]);\r\n\r\n  // Check if the file structure has changed and update expandedFolders if needed\r\n  useEffect(() => {\r\n    // If the file paths have been updated (especially after streaming)\r\n    if (flatFilePaths.length !== previousPathsLengthRef.current) {\r\n      previousPathsLengthRef.current = flatFilePaths.length;\r\n\r\n      // Get the currently saved folder structure\r\n      const savedExpandedFolders = localStorage.getItem(`expandedFolders-${projectId}`);\r\n      if (savedExpandedFolders) {\r\n        try {\r\n          const parsedFolders = JSON.parse(savedExpandedFolders);\r\n\r\n          // Find and expand parent folders of the selected file if it exists\r\n          if (selectedFile) {\r\n            const pathParts = selectedFile.split(\"/\");\r\n            let currentPath = \"\";\r\n\r\n            // Create an updated expanded folders object\r\n            const updatedExpandedFolders = { ...parsedFolders };\r\n\r\n            // Expand all parent directories of the selected file\r\n            for (let i = 0; i < pathParts.length - 1; i++) {\r\n              currentPath = currentPath ? `${currentPath}/${pathParts[i]}` : pathParts[i];\r\n              updatedExpandedFolders[currentPath] = true;\r\n            }\r\n\r\n            setExpandedFolders(updatedExpandedFolders);\r\n            localStorage.setItem(\r\n              `expandedFolders-${projectId}`,\r\n              JSON.stringify(updatedExpandedFolders),\r\n            );\r\n          }\r\n        } catch (e) {\r\n          console.error(\"Failed to process expanded folders after file structure change\", e);\r\n        }\r\n      }\r\n    }\r\n  }, [allFilePaths, selectedFile, projectId]);\r\n\r\n  const toggleFolder = useCallback(\r\n    async (path: string, event: React.MouseEvent) => {\r\n      event.stopPropagation();\r\n\r\n      const isCurrentlyExpanded = expandedFolders[path];\r\n\r\n      if (!isCurrentlyExpanded && !directoryContents[path]) {\r\n        setLoadingFolders((prev) => ({ ...prev, [path]: true }));\r\n\r\n        try {\r\n          const response = await getFiles(projectId, path);\r\n\r\n          if (response && response.files) {\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            const newFiles = response.files.map((file: any) => ({\r\n              name: file.name,\r\n              isDirectory: file.isDirectory,\r\n              children: file.children || [],\r\n            }));\r\n\r\n            setDirectoryContents((prev) => ({\r\n              ...prev,\r\n              [path]: newFiles,\r\n            }));\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching directory contents:\", error);\r\n        } finally {\r\n          setLoadingFolders((prev) => ({ ...prev, [path]: false }));\r\n        }\r\n      }\r\n\r\n      setExpandedFolders((prev) => {\r\n        const newState = { ...prev };\r\n        newState[path] = !prev[path];\r\n\r\n        localStorage.setItem(`expandedFolders-${projectId}`, JSON.stringify(newState));\r\n        return newState;\r\n      });\r\n    },\r\n    [expandedFolders, projectId, directoryContents],\r\n  );\r\n\r\n  const getFileIcon = useCallback((fileName: string) => {\r\n    const extension = fileName.split(\".\").pop()?.toLowerCase();\r\n\r\n    switch (extension) {\r\n      case \"js\":\r\n      case \"mjs\":\r\n        return (\r\n          <div className=\"text-yellow-500\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      case \"jsx\":\r\n        return (\r\n          <div className=\"text-blue-400\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      case \"ts\":\r\n      case \"tsx\":\r\n        return (\r\n          <div className=\"text-blue-600\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      case \"html\":\r\n        return (\r\n          <div className=\"text-orange-500\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      case \"css\":\r\n        return (\r\n          <div className=\"text-purple-500\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      case \"md\":\r\n        return (\r\n          <div className=\"text-gray-500\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      case \"png\":\r\n      case \"jpg\":\r\n      case \"jpeg\":\r\n      case \"webp\":\r\n      case \"gif\":\r\n      case \"svg\":\r\n        return (\r\n          <div className=\"text-green-500\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      case \"json\":\r\n        return (\r\n          <div className=\"text-amber-500\">\r\n            <FileTextSolid className=\"h-4 w-4\" />\r\n          </div>\r\n        );\r\n      default:\r\n        return <FileTextSolid className=\"h-4 w-4\" />;\r\n    }\r\n  }, []);\r\n\r\n  const renderTreeNode = useCallback(\r\n    (file: FileNode, basePath: string, depth: number) => {\r\n      const fullPath = `${basePath}${file.name}`;\r\n      const isSelected = fullPath === selectedFile;\r\n      const isExpanded = expandedFolders[fullPath];\r\n      const isLoading = loadingFolders[fullPath];\r\n      const children = directoryContents[fullPath] || file.children || [];\r\n\r\n      return (\r\n        <div key={fullPath} className=\"file-tree-node\">\r\n          <div\r\n            className={cn(\r\n              \"flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 transition-colors\",\r\n              isSelected ? \"bg-primary/10 font-medium text-primary\" : \"hover:bg-foreground/5\",\r\n            )}\r\n            style={{ paddingLeft: `${depth * 12 + 8}px` }}\r\n            onClick={(e) => {\r\n              if (file.isDirectory) {\r\n                setFile(\"\");\r\n                toggleFolder(fullPath, e);\r\n              } else {\r\n                onFileSelect(fullPath);\r\n              }\r\n            }}\r\n          >\r\n            <div className=\"flex w-full items-center gap-2\">\r\n              <div className=\"flex min-w-0 flex-1 items-center gap-2\">\r\n                {file.isDirectory ? (\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"mr-1 flex h-4 w-4 items-center justify-center\">\r\n                      {isLoading ? (\r\n                        <div className=\"h-3.5 w-3.5 animate-spin rounded-full border-2 border-primary border-t-transparent\" />\r\n                      ) : isExpanded ? (\r\n                        <ChevronDown className=\"h-3.5 w-3.5 text-muted-foreground\" />\r\n                      ) : (\r\n                        <ChevronRight className=\"h-3.5 w-3.5 text-muted-foreground\" />\r\n                      )}\r\n                    </div>\r\n                    {isExpanded ? (\r\n                      <IoFolderOpen className=\"h-4 w-4 text-primary\" />\r\n                    ) : (\r\n                      <IoFolder className=\"h-4 w-4 text-primary\" />\r\n                    )}\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"ml-5\">{getFileIcon(file.name)}</div>\r\n                )}\r\n                <span className=\"min-w-0 flex-1 truncate text-sm\">{file.name}</span>\r\n              </div>\r\n\r\n              {!file.isDirectory && (\r\n                <DropdownMenu>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      className=\"h-fit w-fit flex-shrink-0 items-center justify-between p-0 text-primary hover:text-primary\"\r\n                    >\r\n                      <DotsVertical className=\"size-4 text-primary\" />\r\n                    </Button>\r\n                  </DropdownMenuTrigger>\r\n\r\n                  <DropdownMenuContent className=\"min-w-28\" side=\"bottom\" align=\"end\">\r\n                    <DropdownMenuItem\r\n                      className=\"flex items-center justify-between\"\r\n                      onClick={() => {\r\n                        setShowContextMenuModal({\r\n                          ...showContextMenuModal,\r\n                          move: { state: true, file: fullPath },\r\n                        });\r\n                      }}\r\n                    >\r\n                      <span>Move</span>\r\n                      <Move className=\"size-4\" />\r\n                    </DropdownMenuItem>\r\n                    <DropdownMenuItem\r\n                      onClick={() => {\r\n                        setShowContextMenuModal({\r\n                          ...showContextMenuModal,\r\n                          delete: { state: true, files: fullPath },\r\n                        });\r\n                      }}\r\n                      className=\"flex items-center justify-between\"\r\n                    >\r\n                      <span className=\"text-destructive/90 hover:text-destructive\">Delete</span>\r\n                      <Trash className=\"size-4 text-destructive/90 hover:text-destructive\" />\r\n                    </DropdownMenuItem>\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              )}\r\n            </div>\r\n          </div>\r\n          {file.isDirectory && isExpanded && (\r\n            <div className=\"nested-files\">\r\n              {isLoading ? (\r\n                <div\r\n                  className=\"py-1.5 text-xs italic text-muted-foreground\"\r\n                  style={{ paddingLeft: `${(depth + 1) * 12 + 24}px` }}\r\n                >\r\n                  Loading...\r\n                </div>\r\n              ) : children.length > 0 ? (\r\n                children.map((childFile: FileNode) =>\r\n                  renderTreeNode(childFile, `${fullPath}/`, depth + 1),\r\n                )\r\n              ) : (\r\n                <div\r\n                  className=\"py-1.5 text-xs italic text-muted-foreground\"\r\n                  style={{ paddingLeft: `${(depth + 1) * 12 + 24}px` }}\r\n                >\r\n                  Empty folder\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      );\r\n    },\r\n    [\r\n      expandedFolders,\r\n      selectedFile,\r\n      onFileSelect,\r\n      toggleFolder,\r\n      getFileIcon,\r\n      setFile,\r\n      loadingFolders,\r\n      directoryContents,\r\n    ],\r\n  );\r\n\r\n  const onCloseFileModal = useCallback(() => {\r\n    setShowContextMenuModal({\r\n      file: { state: false, file: \"\" },\r\n      folder: { state: false, file: \"\" },\r\n      move: { state: false, file: \"\" },\r\n      delete: { state: false, files: \"\" },\r\n      upload: { state: false, files: \"\" },\r\n    });\r\n  }, []);\r\n\r\n  const folderList = useMemo(() => {\r\n    const folders = allFilePaths\r\n      .filter((file) => file.isDirectory)\r\n      .map((folder) => folder.name)\r\n      .sort();\r\n    return [\"/\", ...folders];\r\n  }, [allFilePaths]);\r\n\r\n  return (\r\n    <div className={cn(\"flex h-screen w-full flex-1 flex-col\", className)}>\r\n      <div className=\"hidden items-center justify-between border-b px-3 py-2 md:flex\">\r\n        <span className=\"text-xs font-semibold uppercase text-primary\">Explorer</span>\r\n\r\n        <div className=\"flex items-center gap-1\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"size-7\"\r\n            onClick={() => {\r\n              setExpandedFolders({});\r\n            }}\r\n          >\r\n            <CopyMinusIcon className=\"size-4\" />\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"icon\" className=\"size-7\">\r\n                <LuUpload className=\"size-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"min-w-[10rem]\">\r\n              <DropdownMenuItem\r\n                onClick={() => {\r\n                  setShowContextMenuModal({\r\n                    ...showContextMenuModal,\r\n                    file: { state: true, file: \"\" },\r\n                  });\r\n                }}\r\n                className=\"group flex items-center justify-between gap-2 px-2.5\"\r\n              >\r\n                New File\r\n                <FileTextSolid className=\"size-4 text-primary/85 group-hover:text-primary\" />\r\n              </DropdownMenuItem>\r\n\r\n              <DropdownMenuItem\r\n                onClick={() => {\r\n                  setShowContextMenuModal({\r\n                    ...showContextMenuModal,\r\n                    folder: { state: true, file: \"\" },\r\n                  });\r\n                }}\r\n                className=\"group flex items-center justify-between gap-2 px-2.5\"\r\n              >\r\n                New Folder\r\n                <FolderPlusSolid className=\"size-4 text-primary/85 group-hover:text-primary\" />\r\n              </DropdownMenuItem>\r\n\r\n              <DropdownMenuItem\r\n                onClick={() => {\r\n                  setShowContextMenuModal({\r\n                    ...showContextMenuModal,\r\n                    upload: { state: true, files: selectedFile },\r\n                  });\r\n                }}\r\n                className=\"group flex items-center justify-between gap-2 px-2.5\"\r\n              >\r\n                Upload Files\r\n                <Upload className=\"size-4 text-primary/85 group-hover:text-primary\" />\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n      <div className=\"hidden h-screen flex-1 flex-col overflow-y-auto bg-background p-1 pb-24 md:flex\">\r\n        {isLoadingFilePaths && flatFilePaths.length === 0 ? (\r\n          <FileTreeSkeleton />\r\n        ) : displayedFilesAndFolders.length === 0 ? (\r\n          <div className=\"flex h-full items-center justify-center text-sm text-muted-foreground\">\r\n            No files found\r\n          </div>\r\n        ) : (\r\n          displayedFilesAndFolders.map((file: FileNode) => renderTreeNode(file, \"\", 0))\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex w-full items-center gap-2 p-2 md:hidden\">\r\n        <Select\r\n          value={selectedFile}\r\n          onValueChange={(value) => {\r\n            onFileSelect(value);\r\n          }}\r\n        >\r\n          <SelectTrigger className=\"w-[83%] justify-between bg-background px-3 font-normal\">\r\n            <SelectValue\r\n              placeholder=\"Search file...\"\r\n              className={cn(!selectedFile && \"text-primary/80\")}\r\n            >\r\n              {selectedFile\r\n                ? flatFilePaths\r\n                    .find((filePath) => filePath === selectedFile)\r\n                    ?.split(\"/\")\r\n                    .pop()\r\n                : \"Search file...\"}\r\n            </SelectValue>\r\n          </SelectTrigger>\r\n          <SelectContent className=\"w-full min-w-[var(--radix-popper-anchor-width)] border-primary/10\">\r\n            <SelectGroup>\r\n              {flatFilePaths.length === 0 ? (\r\n                <div className=\"px-2 py-4 text-center text-sm text-muted-foreground\">\r\n                  No files found.\r\n                </div>\r\n              ) : (\r\n                flatFilePaths.map((filePath) => (\r\n                  <SelectItem key={filePath} value={filePath}>\r\n                    {filePath}\r\n                  </SelectItem>\r\n                ))\r\n              )}\r\n            </SelectGroup>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <div className=\"flex items-center gap-1.5\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"m-0 text-primary hover:text-primary\"\r\n            onClick={() => {\r\n              setShowContextMenuModal({\r\n                ...showContextMenuModal,\r\n                file: { state: true, file: \"\" },\r\n              });\r\n            }}\r\n          >\r\n            <FileTextSolid className=\"size-4\" />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"m-0 text-primary hover:text-primary\"\r\n            onClick={() => {\r\n              setShowContextMenuModal({\r\n                ...showContextMenuModal,\r\n                folder: { state: true, file: \"\" },\r\n              });\r\n            }}\r\n          >\r\n            <FolderPlusSolid className=\"size-4\" />\r\n          </Button>\r\n\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"m-0 text-primary hover:text-primary\"\r\n            onClick={() => {\r\n              setShowContextMenuModal({\r\n                ...showContextMenuModal,\r\n                upload: { state: true, files: selectedFile },\r\n              });\r\n            }}\r\n          >\r\n            <Upload className=\"size-4\" />\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild className=\"ml-auto\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                disabled={!selectedFile}\r\n                className=\"ml-auto flex items-center justify-center text-primary hover:text-primary\"\r\n              >\r\n                <DotsVertical className=\"size-4 text-primary/90 hover:text-primary\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n\r\n            <DropdownMenuContent className=\"min-w-28\" side=\"bottom\" align=\"end\">\r\n              <DropdownMenuItem\r\n                className=\"flex items-center justify-between text-primary/90 hover:text-primary\"\r\n                onClick={() => {\r\n                  setShowContextMenuModal({\r\n                    ...showContextMenuModal,\r\n                    move: { state: true, file: selectedFile },\r\n                  });\r\n                }}\r\n              >\r\n                <span>Move</span>\r\n                <Move className=\"size-4\" />\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={() => {\r\n                  setShowContextMenuModal({\r\n                    ...showContextMenuModal,\r\n                    delete: { state: true, files: selectedFile },\r\n                  });\r\n                }}\r\n                className=\"flex items-center justify-between\"\r\n              >\r\n                <span className=\"text-destructive/90 hover:text-destructive\">Delete</span>\r\n                <Trash className=\"size-4 text-destructive/90 hover:text-destructive\" />\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n\r\n      <LazyModal open={showContextMenuModal.move.state} onOpenChange={onCloseFileModal}>\r\n        <MoveFileModalContent\r\n          onClose={onCloseFileModal}\r\n          file={showContextMenuModal.move.file}\r\n          folderList={folderList || [\"/\"]}\r\n          projectId={projectId}\r\n          refetchFileTree={refetchFileTree}\r\n        />\r\n      </LazyModal>\r\n\r\n      <LazyModal open={showContextMenuModal.file.state} onOpenChange={onCloseFileModal}>\r\n        <NewFileModalContent\r\n          onClose={onCloseFileModal}\r\n          file={showContextMenuModal.file.file}\r\n          folderList={folderList || [\"/\"]}\r\n          projectId={projectId}\r\n          refetchFileTree={refetchFileTree}\r\n        />\r\n      </LazyModal>\r\n\r\n      <LazyModal open={showContextMenuModal.folder.state} onOpenChange={onCloseFileModal}>\r\n        <NewFolderModalContent\r\n          onClose={onCloseFileModal}\r\n          file={showContextMenuModal.folder.file}\r\n          folderList={folderList || [\"/\"]}\r\n          projectId={projectId}\r\n          refetchFileTree={refetchFileTree}\r\n        />\r\n      </LazyModal>\r\n\r\n      <LazyModal open={showContextMenuModal.delete.state} onOpenChange={onCloseFileModal}>\r\n        <DeleteModalContent\r\n          onClose={onCloseFileModal}\r\n          file={showContextMenuModal.delete.files}\r\n          projectId={projectId}\r\n          refetchFileTree={refetchFileTree}\r\n        />\r\n      </LazyModal>\r\n\r\n      <LazyModal open={showContextMenuModal.upload.state} onOpenChange={onCloseFileModal}>\r\n        <UploadFileModalContent\r\n          onClose={onCloseFileModal}\r\n          folderList={folderList || [\"/\"]}\r\n          projectId={projectId}\r\n          refetchFileTree={refetchFileTree}\r\n        />\r\n      </LazyModal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileTree;\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEA,MAAM,qCAAuB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AAClC,MAAM,oCAAsB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AACjC,MAAM,sCAAwB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AACnC,MAAM,mCAAqB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AAChC,MAAM,uCAAyB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AAmBpC,MAAM,mBAAmB;IACvB,qBACE,6VAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,6VAAC;gBAAgB,WAAU;0BACzB,cAAA,6VAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;eADZ;;;;;;;;;;AAMlB;AAEA,MAAM,kBAAkB,CAAC,OAAmB,WAAW,EAAE;IACvD,MAAM,QAAkB,EAAE;IAE1B,MAAM,OAAO,CAAC,CAAC;QACb,MAAM,WAAW,WAAW,GAAG,SAAS,CAAC,EAAE,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI;QAElE,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,MAAM,IAAI,CAAC;QACb,OAAO,IAAI,KAAK,QAAQ,EAAE;YACxB,MAAM,IAAI,IAAI,gBAAgB,KAAK,QAAQ,EAAE;QAC/C;IACF;IAEA,OAAO;AACT;AAEA,MAAM,WAAoC,CAAC,EACzC,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EAChB;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IACpF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAClF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiC,CAAC;IAC3F,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IACzB,MAAM,yBAAyB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAE;IACtC,MAAM,yBAAyB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAU;IAC9C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;QAC/D,MAAM;YACJ,OAAO;YACP,MAAM;QACR;QACA,QAAQ;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,OAAO;YACP,MAAM;QACR;QACA,QAAQ;YAAE,OAAO;YAAO,OAAO;QAAG;QAClC,QAAQ;YAAE,OAAO;YAAO,OAAO;QAAG;IACpC;IAEA,oDAAoD;IACpD,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,gBAAgB,gBAAgB,EAAE;IAC3C,GAAG;QAAC;KAAa;IAEjB,0EAA0E;IAC1E,MAAM,2BAA2B,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QACvC,sEAAsE;QACtE,IAAI,gBAAgB,MAAM,KAAK,KAAK,uBAAuB,OAAO,CAAC,MAAM,GAAG,GAAG;YAC7E,OAAO,uBAAuB,OAAO;QACvC;QAEA,qDAAqD;QACrD,uBAAuB,OAAO,GAAG;QACjC,OAAO;IACT,GAAG;QAAC;KAAgB;IAEpB,yFAAyF;IACzF,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB,aAAa,OAAO,CAAC,CAAC,gBAAgB,EAAE,WAAW;QAChF,IAAI,sBAAsB;YACxB,IAAI;gBACF,mBAAmB,KAAK,KAAK,CAAC;YAChC,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;IACF,GAAG;QAAC;KAAU;IAEd,6DAA6D;IAC7D,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,IAAI,CAAC,iBAAiB,MAAM,GAAG,GAAG;YAC3C,aAAa,OAAO,CAAC,CAAC,gBAAgB,EAAE,WAAW,EAAE,KAAK,SAAS,CAAC;QACtE;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,+EAA+E;IAC/E,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,mEAAmE;QACnE,IAAI,cAAc,MAAM,KAAK,uBAAuB,OAAO,EAAE;YAC3D,uBAAuB,OAAO,GAAG,cAAc,MAAM;YAErD,2CAA2C;YAC3C,MAAM,uBAAuB,aAAa,OAAO,CAAC,CAAC,gBAAgB,EAAE,WAAW;YAChF,IAAI,sBAAsB;gBACxB,IAAI;oBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC;oBAEjC,mEAAmE;oBACnE,IAAI,cAAc;wBAChB,MAAM,YAAY,aAAa,KAAK,CAAC;wBACrC,IAAI,cAAc;wBAElB,4CAA4C;wBAC5C,MAAM,yBAAyB;4BAAE,GAAG,aAAa;wBAAC;wBAElD,qDAAqD;wBACrD,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,GAAG,GAAG,IAAK;4BAC7C,cAAc,cAAc,GAAG,YAAY,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,EAAE;4BAC3E,sBAAsB,CAAC,YAAY,GAAG;wBACxC;wBAEA,mBAAmB;wBACnB,aAAa,OAAO,CAClB,CAAC,gBAAgB,EAAE,WAAW,EAC9B,KAAK,SAAS,CAAC;oBAEnB;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,kEAAkE;gBAClF;YACF;QACF;IACF,GAAG;QAAC;QAAc;QAAc;KAAU;IAE1C,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC7B,OAAO,MAAc;QACnB,MAAM,eAAe;QAErB,MAAM,sBAAsB,eAAe,CAAC,KAAK;QAEjD,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,KAAK,EAAE;YACpD,kBAAkB,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAK,CAAC;YAEtD,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,iHAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;gBAE3C,IAAI,YAAY,SAAS,KAAK,EAAE;oBAC9B,8DAA8D;oBAC9D,MAAM,WAAW,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;4BAClD,MAAM,KAAK,IAAI;4BACf,aAAa,KAAK,WAAW;4BAC7B,UAAU,KAAK,QAAQ,IAAI,EAAE;wBAC/B,CAAC;oBAED,qBAAqB,CAAC,OAAS,CAAC;4BAC9B,GAAG,IAAI;4BACP,CAAC,KAAK,EAAE;wBACV,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD,SAAU;gBACR,kBAAkB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,KAAK,EAAE;oBAAM,CAAC;YACzD;QACF;QAEA,mBAAmB,CAAC;YAClB,MAAM,WAAW;gBAAE,GAAG,IAAI;YAAC;YAC3B,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;YAE5B,aAAa,OAAO,CAAC,CAAC,gBAAgB,EAAE,WAAW,EAAE,KAAK,SAAS,CAAC;YACpE,OAAO;QACT;IACF,GACA;QAAC;QAAiB;QAAW;KAAkB;IAGjD,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QAE7C,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B,KAAK;YACL,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B,KAAK;gBACH,qBACE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;YAG/B;gBACE,qBAAO,6VAAC,qUAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC/B,CAAC,MAAgB,UAAkB;QACjC,MAAM,WAAW,GAAG,WAAW,KAAK,IAAI,EAAE;QAC1C,MAAM,aAAa,aAAa;QAChC,MAAM,aAAa,eAAe,CAAC,SAAS;QAC5C,MAAM,YAAY,cAAc,CAAC,SAAS;QAC1C,MAAM,WAAW,iBAAiB,CAAC,SAAS,IAAI,KAAK,QAAQ,IAAI,EAAE;QAEnE,qBACE,6VAAC;YAAmB,WAAU;;8BAC5B,6VAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yFACA,aAAa,2CAA2C;oBAE1D,OAAO;wBAAE,aAAa,GAAG,QAAQ,KAAK,EAAE,EAAE,CAAC;oBAAC;oBAC5C,SAAS,CAAC;wBACR,IAAI,KAAK,WAAW,EAAE;4BACpB,QAAQ;4BACR,aAAa,UAAU;wBACzB,OAAO;4BACL,aAAa;wBACf;oBACF;8BAEA,cAAA,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;;oCACZ,KAAK,WAAW,iBACf,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;0DACZ,0BACC,6VAAC;oDAAI,WAAU;;;;;2DACb,2BACF,6VAAC,wTAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6VAAC,0TAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;4CAG3B,2BACC,6VAAC,gOAAA,CAAA,eAAY;gDAAC,WAAU;;;;;qEAExB,6VAAC,gOAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;6DAIxB,6VAAC;wCAAI,WAAU;kDAAQ,YAAY,KAAK,IAAI;;;;;;kDAE9C,6VAAC;wCAAK,WAAU;kDAAmC,KAAK,IAAI;;;;;;;;;;;;4BAG7D,CAAC,KAAK,WAAW,kBAChB,6VAAC,iJAAA,CAAA,eAAY;;kDACX,6VAAC,iJAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6VAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDAEV,cAAA,6VAAC,0TAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAI5B,6VAAC,iJAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAW,MAAK;wCAAS,OAAM;;0DAC5D,6VAAC,iJAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;oDACP,wBAAwB;wDACtB,GAAG,oBAAoB;wDACvB,MAAM;4DAAE,OAAO;4DAAM,MAAM;wDAAS;oDACtC;gDACF;;kEAEA,6VAAC;kEAAK;;;;;;kEACN,6VAAC,0SAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;0DAElB,6VAAC,iJAAA,CAAA,mBAAgB;gDACf,SAAS;oDACP,wBAAwB;wDACtB,GAAG,oBAAoB;wDACvB,QAAQ;4DAAE,OAAO;4DAAM,OAAO;wDAAS;oDACzC;gDACF;gDACA,WAAU;;kEAEV,6VAAC;wDAAK,WAAU;kEAA6C;;;;;;kEAC7D,6VAAC,4SAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,KAAK,WAAW,IAAI,4BACnB,6VAAC;oBAAI,WAAU;8BACZ,0BACC,6VAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,aAAa,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;wBAAC;kCACpD;;;;;+BAGC,SAAS,MAAM,GAAG,IACpB,SAAS,GAAG,CAAC,CAAC,YACZ,eAAe,WAAW,GAAG,SAAS,CAAC,CAAC,EAAE,QAAQ,oBAGpD,6VAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,aAAa,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;wBAAC;kCACpD;;;;;;;;;;;;WApGC;;;;;IA4Gd,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,MAAM,mBAAmB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACnC,wBAAwB;YACtB,MAAM;gBAAE,OAAO;gBAAO,MAAM;YAAG;YAC/B,QAAQ;gBAAE,OAAO;gBAAO,MAAM;YAAG;YACjC,MAAM;gBAAE,OAAO;gBAAO,MAAM;YAAG;YAC/B,QAAQ;gBAAE,OAAO;gBAAO,OAAO;YAAG;YAClC,QAAQ;gBAAE,OAAO;gBAAO,OAAO;YAAG;QACpC;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,UAAU,aACb,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,EACjC,GAAG,CAAC,CAAC,SAAW,OAAO,IAAI,EAC3B,IAAI;QACP,OAAO;YAAC;eAAQ;SAAQ;IAC1B,GAAG;QAAC;KAAa;IAEjB,qBACE,6VAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;;0BACzD,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAK,WAAU;kCAA+C;;;;;;kCAE/D,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;oCACP,mBAAmB,CAAC;gCACtB;0CAEA,cAAA,6VAAC,wSAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG3B,6VAAC,iJAAA,CAAA,eAAY;;kDACX,6VAAC,iJAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6VAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;sDAC5C,cAAA,6VAAC,+NAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGxB,6VAAC,iJAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,6VAAC,iJAAA,CAAA,mBAAgB;gDACf,SAAS;oDACP,wBAAwB;wDACtB,GAAG,oBAAoB;wDACvB,MAAM;4DAAE,OAAO;4DAAM,MAAM;wDAAG;oDAChC;gDACF;gDACA,WAAU;;oDACX;kEAEC,6VAAC,qUAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;;0DAG3B,6VAAC,iJAAA,CAAA,mBAAgB;gDACf,SAAS;oDACP,wBAAwB;wDACtB,GAAG,oBAAoB;wDACvB,QAAQ;4DAAE,OAAO;4DAAM,MAAM;wDAAG;oDAClC;gDACF;gDACA,WAAU;;oDACX;kEAEC,6VAAC,yUAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;;0DAG7B,6VAAC,iJAAA,CAAA,mBAAgB;gDACf,SAAS;oDACP,wBAAwB;wDACtB,GAAG,oBAAoB;wDACvB,QAAQ;4DAAE,OAAO;4DAAM,OAAO;wDAAa;oDAC7C;gDACF;gDACA,WAAU;;oDACX;kEAEC,6VAAC,8SAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,6VAAC;gBAAI,WAAU;0BACZ,sBAAsB,cAAc,MAAM,KAAK,kBAC9C,6VAAC;;;;2BACC,yBAAyB,MAAM,KAAK,kBACtC,6VAAC;oBAAI,WAAU;8BAAwE;;;;;2BAIvF,yBAAyB,GAAG,CAAC,CAAC,OAAmB,eAAe,MAAM,IAAI;;;;;;0BAI9E,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,kIAAA,CAAA,SAAM;wBACL,OAAO;wBACP,eAAe,CAAC;4BACd,aAAa;wBACf;;0CAEA,6VAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6VAAC,kIAAA,CAAA,cAAW;oCACV,aAAY;oCACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,gBAAgB;8CAE9B,eACG,cACG,IAAI,CAAC,CAAC,WAAa,aAAa,eAC/B,MAAM,KACP,QACH;;;;;;;;;;;0CAGR,6VAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6VAAC,kIAAA,CAAA,cAAW;8CACT,cAAc,MAAM,KAAK,kBACxB,6VAAC;wCAAI,WAAU;kDAAsD;;;;;+CAIrE,cAAc,GAAG,CAAC,CAAC,yBACjB,6VAAC,kIAAA,CAAA,aAAU;4CAAgB,OAAO;sDAC/B;2CADc;;;;;;;;;;;;;;;;;;;;;kCAS3B,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;oCACP,wBAAwB;wCACtB,GAAG,oBAAoB;wCACvB,MAAM;4CAAE,OAAO;4CAAM,MAAM;wCAAG;oCAChC;gCACF;0CAEA,cAAA,6VAAC,qUAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;oCACP,wBAAwB;wCACtB,GAAG,oBAAoB;wCACvB,QAAQ;4CAAE,OAAO;4CAAM,MAAM;wCAAG;oCAClC;gCACF;0CAEA,cAAA,6VAAC,yUAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;0CAG7B,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;oCACP,wBAAwB;wCACtB,GAAG,oBAAoB;wCACvB,QAAQ;4CAAE,OAAO;4CAAM,OAAO;wCAAa;oCAC7C;gCACF;0CAEA,cAAA,6VAAC,8SAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,6VAAC,iJAAA,CAAA,eAAY;;kDACX,6VAAC,iJAAA,CAAA,sBAAmB;wCAAC,OAAO;wCAAC,WAAU;kDACrC,cAAA,6VAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,UAAU,CAAC;4CACX,WAAU;sDAEV,cAAA,6VAAC,0TAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAI5B,6VAAC,iJAAA,CAAA,sBAAmB;wCAAC,WAAU;wCAAW,MAAK;wCAAS,OAAM;;0DAC5D,6VAAC,iJAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;oDACP,wBAAwB;wDACtB,GAAG,oBAAoB;wDACvB,MAAM;4DAAE,OAAO;4DAAM,MAAM;wDAAa;oDAC1C;gDACF;;kEAEA,6VAAC;kEAAK;;;;;;kEACN,6VAAC,0SAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;0DAElB,6VAAC,iJAAA,CAAA,mBAAgB;gDACf,SAAS;oDACP,wBAAwB;wDACtB,GAAG,oBAAoB;wDACvB,QAAQ;4DAAE,OAAO;4DAAM,OAAO;wDAAa;oDAC7C;gDACF;gDACA,WAAU;;kEAEV,6VAAC;wDAAK,WAAU;kEAA6C;;;;;;kEAC7D,6VAAC,4SAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,6VAAC,iIAAA,CAAA,YAAS;gBAAC,MAAM,qBAAqB,IAAI,CAAC,KAAK;gBAAE,cAAc;0BAC9D,cAAA,6VAAC;oBACC,SAAS;oBACT,MAAM,qBAAqB,IAAI,CAAC,IAAI;oBACpC,YAAY,cAAc;wBAAC;qBAAI;oBAC/B,WAAW;oBACX,iBAAiB;;;;;;;;;;;0BAIrB,6VAAC,iIAAA,CAAA,YAAS;gBAAC,MAAM,qBAAqB,IAAI,CAAC,KAAK;gBAAE,cAAc;0BAC9D,cAAA,6VAAC;oBACC,SAAS;oBACT,MAAM,qBAAqB,IAAI,CAAC,IAAI;oBACpC,YAAY,cAAc;wBAAC;qBAAI;oBAC/B,WAAW;oBACX,iBAAiB;;;;;;;;;;;0BAIrB,6VAAC,iIAAA,CAAA,YAAS;gBAAC,MAAM,qBAAqB,MAAM,CAAC,KAAK;gBAAE,cAAc;0BAChE,cAAA,6VAAC;oBACC,SAAS;oBACT,MAAM,qBAAqB,MAAM,CAAC,IAAI;oBACtC,YAAY,cAAc;wBAAC;qBAAI;oBAC/B,WAAW;oBACX,iBAAiB;;;;;;;;;;;0BAIrB,6VAAC,iIAAA,CAAA,YAAS;gBAAC,MAAM,qBAAqB,MAAM,CAAC,KAAK;gBAAE,cAAc;0BAChE,cAAA,6VAAC;oBACC,SAAS;oBACT,MAAM,qBAAqB,MAAM,CAAC,KAAK;oBACvC,WAAW;oBACX,iBAAiB;;;;;;;;;;;0BAIrB,6VAAC,iIAAA,CAAA,YAAS;gBAAC,MAAM,qBAAqB,MAAM,CAAC,KAAK;gBAAE,cAAc;0BAChE,cAAA,6VAAC;oBACC,SAAS;oBACT,YAAY,cAAc;wBAAC;qBAAI;oBAC/B,WAAW;oBACX,iBAAiB;;;;;;;;;;;;;;;;;AAK3B;uCAEe", "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/code-editor/text-editor/file-update-modal.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { DangerTriangle } from \"@mynaui/icons-react\";\r\nimport { AnimatePresence, motion } from \"motion/react\";\r\nimport { FileStatus } from \"./types\";\r\n\r\ninterface Props {\r\n  side: \"left\" | \"right\";\r\n  fileStatus: FileStatus;\r\n  onReset?: () => void;\r\n  onSave?: () => void;\r\n  isSaving?: boolean;\r\n  isMobile?: boolean;\r\n}\r\n\r\nconst getFileStatusLabel = (fileStatus: FileStatus) => {\r\n  switch (fileStatus) {\r\n    case \"unsaved\":\r\n      return \"Unsaved Changes\";\r\n    case \"saved\":\r\n      return \"Saved Changes\";\r\n    case \"error\":\r\n      return \"Discard Changes\";\r\n    case \"no-changes\":\r\n      return \"No Changes\";\r\n  }\r\n};\r\n\r\nconst FileUpdateModal = ({\r\n  side,\r\n  fileStatus,\r\n  onReset,\r\n  isMobile,\r\n  onSave,\r\n  isSaving = false,\r\n}: Props) => {\r\n  const shouldShowSaveButton = fileStatus === \"unsaved\";\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"z-50 flex flex-col gap-2\",\r\n        side === \"left\" && \"absolute -right-[35%] bottom-20 z-50 w-full\",\r\n        side === \"right\" && \"absolute bottom-20 left-[25%] z-50 w-full\",\r\n        side === \"left\" && isSaving && \"absolute -right-[35%] bottom-20 z-50 w-full\",\r\n        side === \"right\" && isSaving && \"absolute bottom-20 left-[0%] z-50 w-full\",\r\n        isMobile && \"absolute bottom-20 left-1/4 z-50 w-full\",\r\n      )}\r\n    >\r\n      {shouldShowSaveButton && (\r\n        <AnimatePresence mode=\"sync\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: 10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className={cn(\r\n              \"flex w-full max-w-sm flex-col\",\r\n              isSaving && \"mx-auto max-w-fit transition-all duration-200\",\r\n              isMobile && \"w-fit\",\r\n            )}\r\n          >\r\n            <Card className=\"w-full bg-primary text-primary-foreground shadow-sm\">\r\n              {isSaving ? (\r\n                <CardContent\r\n                  className={cn(\r\n                    \"flex w-full items-center justify-between gap-2 p-3 py-2 text-sm\",\r\n                    isMobile ? \"flex-col gap-4\" : \"flex-row\",\r\n                  )}\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Loading className=\"size-5 animate-spin text-background\" />\r\n                    <Label className=\"text-sm font-medium text-primary-foreground\">Saving...</Label>\r\n                  </div>\r\n                </CardContent>\r\n              ) : (\r\n                <CardContent\r\n                  className={cn(\r\n                    \"flex w-full items-center justify-between gap-2 p-3 py-2 text-sm\",\r\n                    isMobile ? \"flex-col gap-4\" : \"flex-row\",\r\n                  )}\r\n                >\r\n                  <div\r\n                    className={cn(\r\n                      \"flex items-center gap-2\",\r\n                      isMobile && \"items-start justify-start\",\r\n                    )}\r\n                  >\r\n                    <DangerTriangle className=\"h-5 w-5 font-semibold\" />\r\n                    <Label className=\"text-sm font-medium text-primary-foreground\">\r\n                      {getFileStatusLabel(fileStatus)}\r\n                    </Label>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-end gap-2\">\r\n                    <Button\r\n                      variant=\"invert-outline-primary\"\r\n                      size=\"sm\"\r\n                      className=\"h-8 px-3 text-xs\"\r\n                      onClick={() => {\r\n                        if (onReset) onReset();\r\n                      }}\r\n                    >\r\n                      Reset\r\n                    </Button>\r\n                    <Button\r\n                      variant=\"invert\"\r\n                      size=\"sm\"\r\n                      className=\"h-8 text-xs text-primary\"\r\n                      onClick={onSave}\r\n                      disabled={isSaving}\r\n                    >\r\n                      {isSaving ? (\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Loading className=\"size-3 animate-spin\" />\r\n                          <span>Saving...</span>\r\n                        </div>\r\n                      ) : (\r\n                        \"Save Changes\"\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                </CardContent>\r\n              )}\r\n            </Card>\r\n          </motion.div>\r\n        </AnimatePresence>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpdateModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAYA,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;IACX;AACF;AAEA,MAAM,kBAAkB,CAAC,EACvB,IAAI,EACJ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,MAAM,EACN,WAAW,KAAK,EACV;IACN,MAAM,uBAAuB,eAAe;IAE5C,qBACE,6VAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA,SAAS,UAAU,+CACnB,SAAS,WAAW,6CACpB,SAAS,UAAU,YAAY,+CAC/B,SAAS,WAAW,YAAY,4CAChC,YAAY;kBAGb,sCACC,6VAAC,mVAAA,CAAA,kBAAe;YAAC,MAAK;sBACpB,cAAA,6VAAC,oVAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC1B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iCACA,YAAY,iDACZ,YAAY;0BAGd,cAAA,6VAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACb,yBACC,6VAAC,gIAAA,CAAA,cAAW;wBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA,WAAW,mBAAmB;kCAGhC,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6VAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAA8C;;;;;;;;;;;;;;;;6CAInE,6VAAC,gIAAA,CAAA,cAAW;wBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA,WAAW,mBAAmB;;0CAGhC,6VAAC;gCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2BACA,YAAY;;kDAGd,6VAAC,8TAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,6VAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDACd,mBAAmB;;;;;;;;;;;;0CAIxB,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;4CACP,IAAI,SAAS;wCACf;kDACD;;;;;;kDAGD,6VAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;wCACT,UAAU;kDAET,yBACC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,mIAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6VAAC;8DAAK;;;;;;;;;;;mDAGR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB;uCAEe", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/code-editor/text-editor/index.tsx"], "sourcesContent": ["import {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { TextShimmer } from \"@/components/ui/text-shimmer\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { getFileContent, gitCommit, updateFile } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport type { BeforeMount, EditorProps, OnMount } from \"@monaco-editor/react\";\r\nimport { File } from \"@mynaui/icons-react\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport type { editor } from \"monaco-editor\";\r\nimport { useTheme } from \"next-themes\";\r\nimport dynamic from \"next/dynamic\";\r\nimport path from \"path\";\r\nimport React, { memo, useCallback, useEffect, useRef, useState } from \"react\";\r\nimport { TbLayoutSidebar, TbLayoutSidebarFilled } from \"react-icons/tb\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport Hint from \"../../../components/ui/hint\";\r\nimport { errorToast, loadingToast } from \"../../global/toast\";\r\nimport FileUpdateModal from \"./file-update-modal\";\r\nimport { FileStatus } from \"./types\";\r\n\r\nconst Editor = dynamic(() => import(\"@monaco-editor/react\"), { ssr: false });\r\n\r\nconst darkThemeData = {\r\n  base: \"vs-dark\" as const,\r\n  inherit: true,\r\n  rules: [\r\n    { token: \"comment\", foreground: \"eef0f98f\" },\r\n    { token: \"keyword\", foreground: \"54b9ff\" },\r\n    { token: \"string\", foreground: \"ffd493\" },\r\n    { token: \"number\", foreground: \"ffd493\" },\r\n    { token: \"operator\", foreground: \"eef0f9\" },\r\n    { token: \"identifier\", foreground: \"4bf3c8\" },\r\n    { token: \"type\", foreground: \"acafff\" },\r\n    { token: \"function\", foreground: \"00daef\" },\r\n  ],\r\n  colors: {\r\n    \"editor.background\": \"#111115\",\r\n    \"editor.foreground\": \"#eef0f9\",\r\n    \"editorCursor.foreground\": \"#aeafad\",\r\n    \"editor.lineHighlightBackground\": \"#23262d\",\r\n    \"editorLineNumber.foreground\": \"#545864\",\r\n    \"editor.selectionBackground\": \"#ad5dca44\",\r\n    \"editor.inactiveSelectionBackground\": \"#2a2d34\",\r\n    \"editorIndentGuide.background\": \"#343841\",\r\n    \"editor.selectionHighlightBackground\": \"#add6ff34\",\r\n    \"editor.wordHighlightBackground\": \"#494949b8\",\r\n    \"editorBracketMatch.background\": \"#545864\",\r\n    \"editorBracketMatch.border\": \"#ffffff00\",\r\n  },\r\n};\r\n\r\nconst lightThemeData = {\r\n  base: \"vs\" as const,\r\n  inherit: true,\r\n  rules: [\r\n    { token: \"comment\", foreground: \"6a7280\" },\r\n    { token: \"keyword\", foreground: \"0077cc\" },\r\n    { token: \"string\", foreground: \"d97706\" },\r\n    { token: \"number\", foreground: \"d97706\" },\r\n    { token: \"operator\", foreground: \"111827\" },\r\n    { token: \"identifier\", foreground: \"059669\" },\r\n    { token: \"type\", foreground: \"6366f1\" },\r\n    { token: \"function\", foreground: \"0891b2\" },\r\n    { token: \"variable\", foreground: \"111827\" },\r\n    { token: \"constant\", foreground: \"0369a1\" },\r\n    { token: \"regexp\", foreground: \"be123c\" },\r\n    { token: \"entity.name.tag\", foreground: \"7f1d1d\" },\r\n    { token: \"entity.other.attribute-name\", foreground: \"b91c1c\" },\r\n    { token: \"storage\", foreground: \"1e40af\" },\r\n    { token: \"keyword.control\", foreground: \"7e22ce\" },\r\n  ],\r\n  colors: {\r\n    \"editor.background\": \"#fafafa\",\r\n    \"editor.foreground\": \"#000000\",\r\n    \"editorCursor.foreground\": \"#000000\",\r\n    \"editor.lineHighlightBackground\": \"#F8F8F8\",\r\n    \"editorLineNumber.foreground\": \"#767676\",\r\n    \"editor.selectionBackground\": \"#ADD6FF\",\r\n    \"editor.inactiveSelectionBackground\": \"#E5EBF1\",\r\n    \"editorIndentGuide.background1\": \"#D3D3D3\",\r\n    \"editorIndentGuide.activeBackground1\": \"#939393\",\r\n    \"editor.selectionHighlightBackground\": \"#ADD6FF80\",\r\n    \"editor.wordHighlightBackground\": \"#E8E8E8\",\r\n    \"editorBracketMatch.background\": \"#C9DDFC\",\r\n    \"editorBracketMatch.border\": \"#2760BB\",\r\n    \"sideBarSectionHeader.background\": \"#0000\",\r\n    \"sideBarSectionHeader.border\": \"#61616130\",\r\n    \"sideBarTitle.foreground\": \"#6F6F6F\",\r\n  },\r\n};\r\n\r\nconst EDITOR_OPTIONS: EditorProps[\"options\"] = {\r\n  minimap: { enabled: false, showSlider: \"always\", size: \"proportional\" },\r\n  fontFamily: \"monospace\",\r\n  fontSize: 13,\r\n  tabCompletion: \"on\",\r\n  readOnlyMessage: { value: \"Agent is running... Please wait\" },\r\n  lineNumbers: \"on\",\r\n  padding: { top: 10, bottom: 10 },\r\n  scrollBeyondLastLine: false,\r\n  automaticLayout: true,\r\n  formatOnPaste: true,\r\n  formatOnType: true,\r\n  folding: true,\r\n  foldingHighlight: true,\r\n  renderLineHighlight: \"all\",\r\n  cursorBlinking: \"blink\",\r\n  cursorSmoothCaretAnimation: \"on\",\r\n  cursorWidth: 1,\r\n  smoothScrolling: true,\r\n  mouseWheelScrollSensitivity: 3,\r\n  fastScrollSensitivity: 20,\r\n  wordWrap: \"on\",\r\n  bracketPairColorization: {\r\n    enabled: true,\r\n  },\r\n};\r\n\r\nconst LANGUAGES_BY_FILE_EXTENSION = {\r\n  ts: \"typescript\",\r\n  tsx: \"typescript\",\r\n  js: \"javascript\",\r\n  jsx: \"javascript\",\r\n  cjs: \"javascript\",\r\n  mjs: \"javascript\",\r\n  css: \"css\",\r\n  json: \"json\",\r\n  yml: \"yaml\",\r\n  yaml: \"yaml\",\r\n  md: \"markdown\",\r\n  sql: \"sql\",\r\n};\r\n\r\nconst configureMonaco: BeforeMount = (monaco) => {\r\n  monaco.editor.defineTheme(\"custom-dark\", darkThemeData);\r\n  monaco.editor.defineTheme(\"custom-light\", lightThemeData);\r\n\r\n  try {\r\n    monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({\r\n      noSemanticValidation: true,\r\n      noSyntaxValidation: true,\r\n      noSuggestionDiagnostics: true,\r\n    });\r\n\r\n    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({\r\n      allowNonTsExtensions: true,\r\n      allowJs: true,\r\n      checkJs: false,\r\n      noUnusedLocals: false,\r\n      noUnusedParameters: false,\r\n      strict: false,\r\n      target: monaco.languages.typescript.ScriptTarget.Latest,\r\n    });\r\n  } catch (error) {\r\n    console.warn(\"Error configuring Monaco editor:\", error);\r\n  }\r\n};\r\n\r\nconst Breadcrumbs = memo(({ file, isMobile }: { file: string; isMobile?: boolean }) => {\r\n  return (\r\n    <Breadcrumb className=\"truncate px-4 pb-2 pt-1.5 text-sm font-normal text-muted-foreground md:py-1.5\">\r\n      <BreadcrumbList>\r\n        {file.split(\"/\").map((part, index, array) => {\r\n          // If mobile and there are more than 3 parts, show only the last part\r\n          if (isMobile && array.length > 3) {\r\n            if (index === array.length - 1) {\r\n              return (\r\n                <React.Fragment key={index}>\r\n                  <BreadcrumbItem>\r\n                    <BreadcrumbPage className=\"font-medium text-foreground\">{part}</BreadcrumbPage>\r\n                  </BreadcrumbItem>\r\n                </React.Fragment>\r\n              );\r\n            }\r\n\r\n            if (index === 0) {\r\n              return (\r\n                <React.Fragment key={index}>\r\n                  <BreadcrumbItem>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger asChild>\r\n                        <Button variant=\"ghost\" size=\"sm\" className=\"h-6 px-1 text-xs\">\r\n                          ...\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent align=\"start\" className=\"w-56\">\r\n                        {array.slice(0, array.length - 1).map((middlePart, middleIndex) => (\r\n                          <DropdownMenuItem key={middleIndex}>{middlePart}</DropdownMenuItem>\r\n                        ))}\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </BreadcrumbItem>\r\n                  <BreadcrumbSeparator className=\"mt-0.5\" />\r\n                </React.Fragment>\r\n              );\r\n            }\r\n            return null;\r\n          }\r\n\r\n          return (\r\n            <React.Fragment key={index}>\r\n              {index > 0 && <BreadcrumbSeparator className=\"mt-0.5\" />}\r\n              <BreadcrumbItem>\r\n                {index === array.length - 1 ? (\r\n                  <BreadcrumbPage className=\"font-medium text-foreground\">{part}</BreadcrumbPage>\r\n                ) : (\r\n                  <BreadcrumbLink>{part}</BreadcrumbLink>\r\n                )}\r\n              </BreadcrumbItem>\r\n            </React.Fragment>\r\n          );\r\n        })}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  );\r\n});\r\n\r\nBreadcrumbs.displayName = \"TextEditorBreadcrumbs\";\r\n\r\nconst TextEditor = (props: {\r\n  isAgentRunning: boolean;\r\n  projectId: string;\r\n  file: string;\r\n  isMobile?: boolean;\r\n  className?: string;\r\n}) => {\r\n  const { theme } = useTheme();\r\n  const queryClient = useQueryClient();\r\n  const { showFileTree, setShowFileTree } = useNavigateFile(\r\n    useShallow((state) => ({\r\n      showFileTree: state.showFileTree,\r\n      setShowFileTree: state.setShowFileTree,\r\n    })),\r\n  );\r\n\r\n  const [showGitDialog, setShowGitDialog] = useState(false);\r\n  const [fileStatus, setFileStatus] = useState<FileStatus>(\"no-changes\");\r\n\r\n  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);\r\n  const currentFileRef = useRef<string | null>(null);\r\n  const contentRef = useRef<string | undefined>(undefined);\r\n\r\n  const { data: content, isLoading } = useQuery({\r\n    queryKey: [\"fileContent\", props.projectId, props.file],\r\n    queryFn: () => getFileContent(props.projectId, props.file),\r\n    enabled: !!props.projectId && !!props.file,\r\n    staleTime: 1000 * 30,\r\n    gcTime: 1000 * 60,\r\n  });\r\n\r\n  // Handle content and file changes\r\n  useEffect(() => {\r\n    contentRef.current = content;\r\n\r\n    if (!editorRef.current) return;\r\n\r\n    // Clear editor when loading or no content\r\n    if (isLoading || !content) {\r\n      editorRef.current.setValue(\"\");\r\n      return;\r\n    }\r\n\r\n    const isNewFile = props.file !== currentFileRef.current;\r\n\r\n    if (isNewFile) {\r\n      currentFileRef.current = props.file;\r\n      setFileStatus(\"no-changes\");\r\n    }\r\n\r\n    const currentValue = editorRef.current.getValue();\r\n\r\n    if (isNewFile || currentValue !== content) {\r\n      editorRef.current.setValue(content || \"\");\r\n    }\r\n  }, [content, props.file, isLoading]);\r\n\r\n  // Handle editor mount\r\n  const handleEditorDidMount: OnMount = useCallback((editor) => {\r\n    editorRef.current = editor;\r\n\r\n    if (contentRef.current) {\r\n      editor.setValue(contentRef.current);\r\n    }\r\n  }, []);\r\n\r\n  // Handle save content mutation\r\n  const { mutate: saveContent, isPending: isSaving } = useMutation({\r\n    mutationFn: (content: string) => {\r\n      return updateFile(props.projectId, props.file, content);\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [\"fileContent\", props.projectId, props.file],\r\n      });\r\n      setFileStatus(\"saved\");\r\n      setShowGitDialog(true);\r\n    },\r\n    onError: (error) => {\r\n      errorToast(\r\n        `Failed to save file: ${error instanceof Error ? error.message : \"Unknown error\"}`,\r\n      );\r\n    },\r\n  });\r\n\r\n  // Handle commit changes mutation\r\n  const { mutate: commitChanges, isPending: isCommitting } = useMutation({\r\n    mutationFn: () => {\r\n      return loadingToast(\r\n        \"Committing changes...\",\r\n        gitCommit(props.projectId, `Edit ${props.file}`),\r\n      );\r\n    },\r\n    onError: (error) => {\r\n      errorToast(\r\n        `Failed to commit changes: ${error instanceof Error ? error.message : \"Unknown error\"}`,\r\n      );\r\n    },\r\n  });\r\n\r\n  const handleSave = useCallback(() => {\r\n    if (editorRef.current) {\r\n      const content = editorRef.current.getValue();\r\n      saveContent(content);\r\n    }\r\n  }, [saveContent]);\r\n\r\n  const handleGitCommit = useCallback(() => {\r\n    commitChanges();\r\n  }, [commitChanges]);\r\n\r\n  const handleDiscard = useCallback(() => {\r\n    if (content !== undefined) {\r\n      editorRef.current?.setValue(content);\r\n      setFileStatus(\"no-changes\");\r\n    }\r\n  }, [content, editorRef, setFileStatus]);\r\n\r\n  const handleEditorChange = useCallback(() => {\r\n    if (!editorRef.current) return;\r\n    const currentValue = editorRef.current.getValue();\r\n    const isChanged = currentValue !== (content || \"\");\r\n    setFileStatus(isChanged ? \"unsaved\" : \"no-changes\");\r\n  }, [content]);\r\n\r\n  // cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (editorRef.current) {\r\n        editorRef.current.dispose();\r\n        editorRef.current = null;\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  if (!props.file) {\r\n    return (\r\n      <Card\r\n        className={cn(\r\n          \"flex h-full w-full flex-col items-center justify-center rounded-none border-none bg-transparent shadow-none\",\r\n        )}\r\n        border={false}\r\n      >\r\n        <CardContent className=\"flex w-full max-w-xl flex-col items-center justify-center space-y-6 p-6\">\r\n          <div className=\"flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2\">\r\n            <File className=\"size-8 text-muted-foreground\" />\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-4\">\r\n            <div className=\"space-y-1.5 text-center\">\r\n              <Typography.H4>No file selected</Typography.H4>\r\n              <Typography.P className=\"mt-0 text-balance leading-normal\">\r\n                Please select a file to edit.\r\n              </Typography.P>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  const fileExtension = path.extname(props.file || \"\").slice(1);\r\n  const language =\r\n    LANGUAGES_BY_FILE_EXTENSION[fileExtension as keyof typeof LANGUAGES_BY_FILE_EXTENSION] ||\r\n    \"plaintext\";\r\n  const editorTheme = theme === \"dark\" ? \"custom-dark\" : \"custom-light\";\r\n  const readOnlyEditor = props.isAgentRunning || isLoading;\r\n\r\n  return (\r\n    <div className={cn(\"relative flex h-full w-full flex-col overflow-hidden\", props.className)}>\r\n      <div\r\n        className={cn(\r\n          \"flex items-center justify-between py-1.5 text-sm font-medium md:border-b md:border-t-0\",\r\n        )}\r\n      >\r\n        <div className=\"flex w-full items-center justify-between md:pl-2\">\r\n          <Hint label={showFileTree ? \"Hide File Tree\" : \"Show File Tree\"} side=\"bottom\">\r\n            <Button\r\n              size=\"icon\"\r\n              className=\"hidden rounded-lg md:flex\"\r\n              variant=\"ghost\"\r\n              onClick={() => setShowFileTree(!showFileTree)}\r\n            >\r\n              {showFileTree ? (\r\n                <TbLayoutSidebarFilled className=\"h-4 w-4\" />\r\n              ) : (\r\n                <TbLayoutSidebar className=\"h-4 w-4\" />\r\n              )}\r\n            </Button>\r\n          </Hint>\r\n\r\n          <Breadcrumbs file={props.file} isMobile={props.isMobile} />\r\n\r\n          <div className=\"flex items-center gap-2 py-1 pr-2 md:px-0 md:py-0\">\r\n            {props.isAgentRunning && (\r\n              <Badge variant=\"terminal\" className=\"flex items-center gap-2\">\r\n                <Loading className=\"size-3 animate-spin\" />\r\n                Agent is running...\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex-1 tracking-wider\">\r\n        {isLoading && (\r\n          <div className=\"flex h-full w-full items-center justify-center bg-background text-muted-foreground\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <TextShimmer className=\"text-base font-normal\">Fetching file content...</TextShimmer>\r\n              <Loading className=\"size-4 animate-spin\" />\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <Editor\r\n          height=\"100%\"\r\n          language={language}\r\n          className={cn(\"h-full pb-5\", isLoading && \"hidden\")}\r\n          theme={editorTheme}\r\n          onMount={handleEditorDidMount}\r\n          beforeMount={configureMonaco}\r\n          onChange={handleEditorChange}\r\n          options={{\r\n            ...EDITOR_OPTIONS,\r\n            readOnly: readOnlyEditor,\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {!props.isAgentRunning && (\r\n        <FileUpdateModal\r\n          side=\"right\"\r\n          fileStatus={fileStatus ?? \"saved\"}\r\n          onReset={handleDiscard}\r\n          onSave={handleSave}\r\n          isSaving={isSaving}\r\n          isMobile={props.isMobile}\r\n        />\r\n      )}\r\n\r\n      <AlertDialog open={showGitDialog} onOpenChange={setShowGitDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Commit Changes to Git?</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Do you want to commit these changes to the Git version history?\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter className=\"flex items-center justify-end\">\r\n            <AlertDialogCancel className=\"w-full md:w-fit\" onClick={() => setShowGitDialog(false)}>\r\n              No\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleGitCommit}\r\n              disabled={isCommitting}\r\n              className=\"h-8 w-full md:w-fit\"\r\n            >\r\n              {isCommitting ? (\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Loading className=\"size-4 animate-spin\" />\r\n                  <span>Committing...</span>\r\n                </div>\r\n              ) : (\r\n                \"Yes, Commit\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nTextEditor.displayName = \"TextEditor\";\r\n\r\nexport default TextEditor;\r\n"], "names": [], "mappings": ";;;;AAAA;AAUA;AACA;AAQA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,SAAS,CAAA,GAAA,8QAAA,CAAA,UAAO,AAAD;;;;;;IAA0C,KAAK;;AAEpE,MAAM,gBAAgB;IACpB,MAAM;IACN,SAAS;IACT,OAAO;QACL;YAAE,OAAO;YAAW,YAAY;QAAW;QAC3C;YAAE,OAAO;YAAW,YAAY;QAAS;QACzC;YAAE,OAAO;YAAU,YAAY;QAAS;QACxC;YAAE,OAAO;YAAU,YAAY;QAAS;QACxC;YAAE,OAAO;YAAY,YAAY;QAAS;QAC1C;YAAE,OAAO;YAAc,YAAY;QAAS;QAC5C;YAAE,OAAO;YAAQ,YAAY;QAAS;QACtC;YAAE,OAAO;YAAY,YAAY;QAAS;KAC3C;IACD,QAAQ;QACN,qBAAqB;QACrB,qBAAqB;QACrB,2BAA2B;QAC3B,kCAAkC;QAClC,+BAA+B;QAC/B,8BAA8B;QAC9B,sCAAsC;QACtC,gCAAgC;QAChC,uCAAuC;QACvC,kCAAkC;QAClC,iCAAiC;QACjC,6BAA6B;IAC/B;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,SAAS;IACT,OAAO;QACL;YAAE,OAAO;YAAW,YAAY;QAAS;QACzC;YAAE,OAAO;YAAW,YAAY;QAAS;QACzC;YAAE,OAAO;YAAU,YAAY;QAAS;QACxC;YAAE,OAAO;YAAU,YAAY;QAAS;QACxC;YAAE,OAAO;YAAY,YAAY;QAAS;QAC1C;YAAE,OAAO;YAAc,YAAY;QAAS;QAC5C;YAAE,OAAO;YAAQ,YAAY;QAAS;QACtC;YAAE,OAAO;YAAY,YAAY;QAAS;QAC1C;YAAE,OAAO;YAAY,YAAY;QAAS;QAC1C;YAAE,OAAO;YAAY,YAAY;QAAS;QAC1C;YAAE,OAAO;YAAU,YAAY;QAAS;QACxC;YAAE,OAAO;YAAmB,YAAY;QAAS;QACjD;YAAE,OAAO;YAA+B,YAAY;QAAS;QAC7D;YAAE,OAAO;YAAW,YAAY;QAAS;QACzC;YAAE,OAAO;YAAmB,YAAY;QAAS;KAClD;IACD,QAAQ;QACN,qBAAqB;QACrB,qBAAqB;QACrB,2BAA2B;QAC3B,kCAAkC;QAClC,+BAA+B;QAC/B,8BAA8B;QAC9B,sCAAsC;QACtC,iCAAiC;QACjC,uCAAuC;QACvC,uCAAuC;QACvC,kCAAkC;QAClC,iCAAiC;QACjC,6BAA6B;QAC7B,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;IAC7B;AACF;AAEA,MAAM,iBAAyC;IAC7C,SAAS;QAAE,SAAS;QAAO,YAAY;QAAU,MAAM;IAAe;IACtE,YAAY;IACZ,UAAU;IACV,eAAe;IACf,iBAAiB;QAAE,OAAO;IAAkC;IAC5D,aAAa;IACb,SAAS;QAAE,KAAK;QAAI,QAAQ;IAAG;IAC/B,sBAAsB;IACtB,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,SAAS;IACT,kBAAkB;IAClB,qBAAqB;IACrB,gBAAgB;IAChB,4BAA4B;IAC5B,aAAa;IACb,iBAAiB;IACjB,6BAA6B;IAC7B,uBAAuB;IACvB,UAAU;IACV,yBAAyB;QACvB,SAAS;IACX;AACF;AAEA,MAAM,8BAA8B;IAClC,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,IAAI;IACJ,KAAK;AACP;AAEA,MAAM,kBAA+B,CAAC;IACpC,OAAO,MAAM,CAAC,WAAW,CAAC,eAAe;IACzC,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB;IAE1C,IAAI;QACF,OAAO,SAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,qBAAqB,CAAC;YACnE,sBAAsB;YACtB,oBAAoB;YACpB,yBAAyB;QAC3B;QAEA,OAAO,SAAS,CAAC,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC;YAChE,sBAAsB;YACtB,SAAS;YACT,SAAS;YACT,gBAAgB;YAChB,oBAAoB;YACpB,QAAQ;YACR,QAAQ,OAAO,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM;QACzD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,oCAAoC;IACnD;AACF;AAEA,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAwC;IAChF,qBACE,6VAAC,sIAAA,CAAA,aAAU;QAAC,WAAU;kBACpB,cAAA,6VAAC,sIAAA,CAAA,iBAAc;sBACZ,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,OAAO;gBACjC,qEAAqE;gBACrE,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;oBAChC,IAAI,UAAU,MAAM,MAAM,GAAG,GAAG;wBAC9B,qBACE,6VAAC,oTAAA,CAAA,UAAK,CAAC,QAAQ;sCACb,cAAA,6VAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,6VAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;8CAA+B;;;;;;;;;;;2BAFxC;;;;;oBAMzB;oBAEA,IAAI,UAAU,GAAG;wBACf,qBACE,6VAAC,oTAAA,CAAA,UAAK,CAAC,QAAQ;;8CACb,6VAAC,sIAAA,CAAA,iBAAc;8CACb,cAAA,6VAAC,4IAAA,CAAA,eAAY;;0DACX,6VAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,6VAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,WAAU;8DAAmB;;;;;;;;;;;0DAIjE,6VAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAQ,WAAU;0DAC1C,MAAM,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,YAAY,4BACjD,6VAAC,4IAAA,CAAA,mBAAgB;kEAAoB;uDAAd;;;;;;;;;;;;;;;;;;;;;8CAK/B,6VAAC,sIAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;;2BAfZ;;;;;oBAkBzB;oBACA,OAAO;gBACT;gBAEA,qBACE,6VAAC,oTAAA,CAAA,UAAK,CAAC,QAAQ;;wBACZ,QAAQ,mBAAK,6VAAC,sIAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;;sCAC7C,6VAAC,sIAAA,CAAA,iBAAc;sCACZ,UAAU,MAAM,MAAM,GAAG,kBACxB,6VAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;0CAA+B;;;;;qDAEzD,6VAAC,sIAAA,CAAA,iBAAc;0CAAE;;;;;;;;;;;;mBANF;;;;;YAWzB;;;;;;;;;;;AAIR;AAEA,YAAY,WAAW,GAAG;AAE1B,MAAM,aAAa,CAAC;IAOlB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EACtD,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,cAAc,MAAM,YAAY;YAChC,iBAAiB,MAAM,eAAe;QACxC,CAAC;IAGH,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAc;IAEzD,MAAM,YAAY,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAuC;IAC9D,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAiB;IAC7C,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAsB;IAE9C,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QAC5C,UAAU;YAAC;YAAe,MAAM,SAAS;YAAE,MAAM,IAAI;SAAC;QACtD,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS,EAAE,MAAM,IAAI;QACzD,SAAS,CAAC,CAAC,MAAM,SAAS,IAAI,CAAC,CAAC,MAAM,IAAI;QAC1C,WAAW,OAAO;QAClB,QAAQ,OAAO;IACjB;IAEA,kCAAkC;IAClC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW,OAAO,GAAG;QAErB,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,0CAA0C;QAC1C,IAAI,aAAa,CAAC,SAAS;YACzB,UAAU,OAAO,CAAC,QAAQ,CAAC;YAC3B;QACF;QAEA,MAAM,YAAY,MAAM,IAAI,KAAK,eAAe,OAAO;QAEvD,IAAI,WAAW;YACb,eAAe,OAAO,GAAG,MAAM,IAAI;YACnC,cAAc;QAChB;QAEA,MAAM,eAAe,UAAU,OAAO,CAAC,QAAQ;QAE/C,IAAI,aAAa,iBAAiB,SAAS;YACzC,UAAU,OAAO,CAAC,QAAQ,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;QAAS,MAAM,IAAI;QAAE;KAAU;IAEnC,sBAAsB;IACtB,MAAM,uBAAgC,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjD,UAAU,OAAO,GAAG;QAEpB,IAAI,WAAW,OAAO,EAAE;YACtB,OAAO,QAAQ,CAAC,WAAW,OAAO;QACpC;IACF,GAAG,EAAE;IAEL,+BAA+B;IAC/B,MAAM,EAAE,QAAQ,WAAW,EAAE,WAAW,QAAQ,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QAC/D,YAAY,CAAC;YACX,OAAO,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS,EAAE,MAAM,IAAI,EAAE;QACjD;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAAe,MAAM,SAAS;oBAAE,MAAM,IAAI;iBAAC;YACxD;YACA,cAAc;YACd,iBAAiB;QACnB;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EACP,CAAC,qBAAqB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAEtF;IACF;IAEA,iCAAiC;IACjC,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACrE,YAAY;YACV,OAAO,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAChB,yBACA,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE;QAEnD;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EACP,CAAC,0BAA0B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAE3F;IACF;IAEA,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,UAAU,UAAU,OAAO,CAAC,QAAQ;YAC1C,YAAY;QACd;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAClC;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,YAAY,WAAW;YACzB,UAAU,OAAO,EAAE,SAAS;YAC5B,cAAc;QAChB;IACF,GAAG;QAAC;QAAS;QAAW;KAAc;IAEtC,MAAM,qBAAqB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,MAAM,eAAe,UAAU,OAAO,CAAC,QAAQ;QAC/C,MAAM,YAAY,iBAAiB,CAAC,WAAW,EAAE;QACjD,cAAc,YAAY,YAAY;IACxC,GAAG;QAAC;KAAQ;IAEZ,qBAAqB;IACrB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,OAAO;gBACzB,UAAU,OAAO,GAAG;YACtB;QACF;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,MAAM,IAAI,EAAE;QACf,qBACE,6VAAC,gIAAA,CAAA,OAAI;YACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;YAEF,QAAQ;sBAER,cAAA,6VAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,0SAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;8CAAC;;;;;;8CACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;oCAAC,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQvE;IAEA,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC;IAC3D,MAAM,WACJ,2BAA2B,CAAC,cAA0D,IACtF;IACF,MAAM,cAAc,UAAU,SAAS,gBAAgB;IACvD,MAAM,iBAAiB,MAAM,cAAc,IAAI;IAE/C,qBACE,6VAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD,MAAM,SAAS;;0BACxF,6VAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,gIAAA,CAAA,UAAI;4BAAC,OAAO,eAAe,mBAAmB;4BAAkB,MAAK;sCACpE,cAAA,6VAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAQ;gCACR,SAAS,IAAM,gBAAgB,CAAC;0CAE/B,6BACC,6VAAC,+NAAA,CAAA,wBAAqB;oCAAC,WAAU;;;;;yDAEjC,6VAAC,+NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAKjC,6VAAC;4BAAY,MAAM,MAAM,IAAI;4BAAE,UAAU,MAAM,QAAQ;;;;;;sCAEvD,6VAAC;4BAAI,WAAU;sCACZ,MAAM,cAAc,kBACnB,6VAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAW,WAAU;;kDAClC,6VAAC,mIAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAQrD,6VAAC;gBAAI,WAAU;;oBACZ,2BACC,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,2IAAA,CAAA,cAAW;oCAAC,WAAU;8CAAwB;;;;;;8CAC/C,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKzB,6VAAC;wBACC,QAAO;wBACP,UAAU;wBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe,aAAa;wBAC1C,OAAO;wBACP,SAAS;wBACT,aAAa;wBACb,UAAU;wBACV,SAAS;4BACP,GAAG,cAAc;4BACjB,UAAU;wBACZ;;;;;;;;;;;;YAIH,CAAC,MAAM,cAAc,kBACpB,6VAAC,+KAAA,CAAA,UAAe;gBACd,MAAK;gBACL,YAAY,cAAc;gBAC1B,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,UAAU,MAAM,QAAQ;;;;;;0BAI5B,6VAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAe,cAAc;0BAC9C,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;;sCACjB,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,6VAAC,2IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,6VAAC,2IAAA,CAAA,oBAAiB;oCAAC,WAAU;oCAAkB,SAAS,IAAM,iBAAiB;8CAAQ;;;;;;8CAGvF,6VAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,mIAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6VAAC;0DAAK;;;;;;;;;;;+CAGR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;AAEA,WAAW,WAAW,GAAG;uCAEV", "debugId": null}}, {"offset": {"line": 2287, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/code-editor/code-editor.tsx"], "sourcesContent": ["import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from \"@/components/ui/resizable\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { getFiles } from \"@/lib/api\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { useIsAgentRunning, useSubscribeToAgentRunning } from \"@/stores/current-thread\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { memo, useCallback, useMemo } from \"react\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport FileTree, { FileNode } from \"./file-tree\";\r\nimport TextEditor from \"./text-editor\";\r\n\r\ntype Props = {\r\n  projectId: string;\r\n  envId: string;\r\n  isIframeLoading?: boolean;\r\n};\r\n\r\nconst CodeEditor = ({ projectId, envId }: Props) => {\r\n  const isMobile = useIsMobile();\r\n\r\n  const { file, setFile, showFileTree } = useNavigateFile(\r\n    useShallow((state) => ({\r\n      file: state.file,\r\n      setFile: state.setFile,\r\n      showFileTree: state.showFileTree,\r\n    })),\r\n  );\r\n\r\n  const isAgentRunning = useIsAgentRunning();\r\n\r\n  const {\r\n    data: filesData,\r\n    isLoading: isLoadingFiles,\r\n    refetch,\r\n  } = useQuery<FileNode[]>({\r\n    queryKey: [\"files\", projectId, envId],\r\n    queryFn: async () => {\r\n      const result = await getFiles(projectId, \"/\");\r\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n      const sortedFiles = (result?.files || []).sort((a: any, b: any) => {\r\n        if (a.isDirectory && !b.isDirectory) return -1;\r\n        if (!a.isDirectory && b.isDirectory) return 1;\r\n        return a.name.localeCompare(b.name);\r\n      });\r\n      return sortedFiles;\r\n    },\r\n    enabled: !!projectId,\r\n    placeholderData: (previousData) => previousData,\r\n  });\r\n\r\n  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {\r\n    if (!isAgentRunning && wasRunning) {\r\n      debug(\"Agent streaming complete, refetching files\");\r\n      refetch();\r\n    }\r\n  });\r\n\r\n  const filesAndFolders = useMemo(() => {\r\n    return filesData || [];\r\n  }, [filesData]);\r\n\r\n  const handleFileSelect = useCallback((filepath: string) => {\r\n    setFile(filepath);\r\n  }, []);\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <div className=\"relative flex h-full grid-cols-12 flex-col overflow-hidden md:grid\">\r\n        <FileTree\r\n          className=\"md:col-span-2 md:border-r\"\r\n          projectId={projectId}\r\n          onFileSelect={handleFileSelect}\r\n          selectedFile={file}\r\n          filesAndFolders={filesAndFolders}\r\n          isLoadingFilePaths={isLoadingFiles}\r\n          allFilePaths={filesData || []}\r\n          refetchFileTree={refetch}\r\n        />\r\n\r\n        <TextEditor\r\n          isMobile\r\n          className={showFileTree ? \"md:col-span-10\" : \"md:col-span-12\"}\r\n          file={file}\r\n          isAgentRunning={isAgentRunning}\r\n          projectId={projectId}\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <ResizablePanelGroup\r\n      direction=\"horizontal\"\r\n      className=\"relative flex h-full flex-col overflow-hidden md:flex-row\"\r\n    >\r\n      {showFileTree && (\r\n        <>\r\n          <ResizablePanel id=\"file-tree\" order={1} defaultSize={20} minSize={20} maxSize={45}>\r\n            <FileTree\r\n              className=\"h-full border-r\"\r\n              projectId={projectId}\r\n              onFileSelect={handleFileSelect}\r\n              selectedFile={file}\r\n              filesAndFolders={filesAndFolders}\r\n              isLoadingFilePaths={isLoadingFiles}\r\n              allFilePaths={filesData || []}\r\n              refetchFileTree={refetch}\r\n            />\r\n          </ResizablePanel>\r\n\r\n          <ResizableHandle />\r\n        </>\r\n      )}\r\n\r\n      <ResizablePanel id=\"code-editor\" order={2} defaultSize={showFileTree ? 80 : 100}>\r\n        <TextEditor\r\n          isAgentRunning={isAgentRunning}\r\n          projectId={projectId}\r\n          className=\"h-full\"\r\n          file={file}\r\n        />\r\n      </ResizablePanel>\r\n    </ResizablePanelGroup>\r\n  );\r\n};\r\n\r\nexport default memo(CodeEditor);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAQA,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,KAAK,EAAS;IAC7C,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EACpD,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,MAAM,MAAM,IAAI;YAChB,SAAS,MAAM,OAAO;YACtB,cAAc,MAAM,YAAY;QAClC,CAAC;IAGH,MAAM,iBAAiB,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD;IAEvC,MAAM,EACJ,MAAM,SAAS,EACf,WAAW,cAAc,EACzB,OAAO,EACR,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;YAAS;YAAW;SAAM;QACrC,SAAS;YACP,MAAM,SAAS,MAAM,CAAA,GAAA,iHAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;YACzC,8DAA8D;YAC9D,MAAM,cAAc,CAAC,QAAQ,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC,GAAQ;gBACtD,IAAI,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC;gBAC7C,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,OAAO;gBAC5C,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC;YACA,OAAO;QACT;QACA,SAAS,CAAC,CAAC;QACX,iBAAiB,CAAC,eAAiB;IACrC;IAEA,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD,EAAE,CAAC,gBAAgB;QAC1C,IAAI,CAAC,kBAAkB,YAAY;YACjC,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE;YACN;QACF;IACF;IAEA,MAAM,kBAAkB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,aAAa,EAAE;IACxB,GAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,QAAQ;IACV,GAAG,EAAE;IAEL,IAAI,UAAU;QACZ,qBACE,6VAAC;YAAI,WAAU;;8BACb,6VAAC,2JAAA,CAAA,UAAQ;oBACP,WAAU;oBACV,WAAW;oBACX,cAAc;oBACd,cAAc;oBACd,iBAAiB;oBACjB,oBAAoB;oBACpB,cAAc,aAAa,EAAE;oBAC7B,iBAAiB;;;;;;8BAGnB,6VAAC,6JAAA,CAAA,UAAU;oBACT,QAAQ;oBACR,WAAW,eAAe,mBAAmB;oBAC7C,MAAM;oBACN,gBAAgB;oBAChB,WAAW;;;;;;;;;;;;IAInB;IAEA,qBACE,6VAAC,qIAAA,CAAA,sBAAmB;QAClB,WAAU;QACV,WAAU;;YAET,8BACC;;kCACE,6VAAC,qIAAA,CAAA,iBAAc;wBAAC,IAAG;wBAAY,OAAO;wBAAG,aAAa;wBAAI,SAAS;wBAAI,SAAS;kCAC9E,cAAA,6VAAC,2JAAA,CAAA,UAAQ;4BACP,WAAU;4BACV,WAAW;4BACX,cAAc;4BACd,cAAc;4BACd,iBAAiB;4BACjB,oBAAoB;4BACpB,cAAc,aAAa,EAAE;4BAC7B,iBAAiB;;;;;;;;;;;kCAIrB,6VAAC,qIAAA,CAAA,kBAAe;;;;;;;0BAIpB,6VAAC,qIAAA,CAAA,iBAAc;gBAAC,IAAG;gBAAc,OAAO;gBAAG,aAAa,eAAe,KAAK;0BAC1E,cAAA,6VAAC,6JAAA,CAAA,UAAU;oBACT,gBAAgB;oBAChB,WAAW;oBACX,WAAU;oBACV,MAAM;;;;;;;;;;;;;;;;;AAKhB;qDAEe,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}]}