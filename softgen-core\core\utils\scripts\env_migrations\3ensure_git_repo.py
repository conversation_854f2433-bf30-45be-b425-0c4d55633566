from daytona import Daytona, DaytonaConfig
import json
import csv
import re
import time
from concurrent.futures import Thr<PERSON>PoolExecutor
from datetime import datetime
import asyncio
from typing import List, Dict, <PERSON><PERSON>
import argparse
from urllib3.util import Retry
import urllib3

# Define GitHub token directly
GITHUB_TOKEN = "****************************************"

# Initialize Daytona client
config = DaytonaConfig(
    api_key="dtn_858727beec575bf6d960c089fc4e4e397008e0e6383bd205082ed5de724e1f63",
    server_url="https://daytona.work/api",
    target="us"
)
daytona = Daytona(config)

# Configure urllib3 connection pooling
urllib3.PoolManager(
    maxsize=100,  # Increase max pool size
    retries=Retry(
        total=3,
        backoff_factor=0.1,
        status_forcelist=[500, 502, 503, 504]
    )
)

def execute_in_tmux(workspace, command, session_name):
    try:
        # Create new tmux session
        workspace.process.exec(f"tmux new-session -d -s {session_name}")
        
        # Send command
        workspace.process.exec(f"tmux send-keys -t {session_name} '{command}' Enter")
        
        # Wait briefly for command to execute
        time.sleep(2)
        
        # Get output
        return workspace.process.exec(f"tmux capture-pane -t {session_name} -p")
        
    except Exception as e:
        print(f"Error in tmux execution: {str(e)}")
        return None

def get_repo_info(workspace_id: str) -> Tuple[str, str]:
    """Get matching repo info from CSV based on workspace ID number"""
    # Extract number from workspace ID (any characters followed by a dash and numbers)
    workspace_match = re.search(r'.*-(\d+)$', workspace_id)
    if not workspace_match:
        print(f"Could not extract number from workspace ID: {workspace_id}")
        return None, None
        
    workspace_num = workspace_match.group(1)
    print(f"Looking for preview-{workspace_num} in CSV")  # Debug print
    
    # Read CSV to find matching preview entry
    with open('env-migration-marko/combined-env-data.csv', 'r') as file:
        csv_reader = csv.DictReader(file)
        for row in csv_reader:
            if row['env_id'] == f"preview-{workspace_num}":
                # Extract sg-XXXXX part from github_repo URL
                repo_match = re.search(r'/([^/]+)\.git$', row['github_repo'])
                if repo_match:
                    return row['github_repo'], repo_match.group(1)
                else:
                    print(f"Could not extract repo name from URL: {row['github_repo']}")
                    return None, None
    
    print(f"No matching preview-{workspace_num} found in CSV")
    return None, None

def setup_git_repo(workspace, github_repo: str, github_token: str) -> Dict:
    """Set up correct git repository in workspace"""
    try:
        # Clean up existing repo with a separate tmux session
        cleanup_response = execute_in_tmux(
            workspace,
            'cd / && rm -rf /app/* /app/.* && ls -la /app',
            'cleanup'
        )
        
        # Wait a bit after cleanup
        time.sleep(2)
        
        # Prepare authenticated URL
        auth_url = github_repo.replace(
            "https://github.com",
            f"https://softgenprojects:{github_token}@github.com"
        )

        # Clone repository in a new session
        clone_cmd = f"""
cd /app && \
git clone {auth_url} . && \
git config --global --add safe.directory /app && \
ls -la
"""
        clone_response = execute_in_tmux(
            workspace,
            clone_cmd,
            'git_clone'
        )

        # Verify the clone worked by checking git remote -v
        verify_response = execute_in_tmux(
            workspace,
            'cd /app && git remote -v',
            'verify_git'
        )

        if verify_response and 'not a git repository' not in verify_response.result.lower():
            return {
                "success": True,
                "message": "Git repository successfully set up",
                "output": verify_response.result
            }
        else:
            return {
                "success": False,
                "error": "Git clone verification failed",
                "clone_output": clone_response.result if clone_response else "No clone output",
                "verify_output": verify_response.result if verify_response else "No verify output"
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"Setup error: {str(e)}"
        }

def verify_git_repo(workspace_id: str, ensure_correct_repo: bool = False) -> Dict:
    """Verify if workspace has correct git repo configured and optionally fix it"""
    try:
        print(f"\nVerifying git repo for workspace: {workspace_id}")
        
        workspace = daytona.get(workspace_id)
        expected_repo_url, repo_identifier = get_repo_info(workspace_id)
        
        if not expected_repo_url:
            print(f"❌ No matching repo info found for {workspace_id}")
            return {
                "workspace_id": workspace_id,
                "success": False, 
                "error": "No repo info found"
            }
            
        print(f"Expected repo: {repo_identifier}")
        
        # Just run git remote -v and check output
        result = execute_in_tmux(
            workspace,
            "cd /app && git remote -v",
            f"git-check-{workspace_id}"
        )
        
        if not result or not result.result:
            print("❌ No git remote output")
            if ensure_correct_repo:
                setup_result = setup_git_repo(workspace, expected_repo_url, GITHUB_TOKEN)
                return {
                    "workspace_id": workspace_id,
                    "success": setup_result["success"],
                    "action": "setup_attempted"
                }
            return {
                "workspace_id": workspace_id,
                "success": False,
                "error": "No git remote output"
            }
            
        # Simply check if the expected repo identifier exists in the output
        if repo_identifier in result.result:
            print(f"✅ Found correct repo: {repo_identifier}")
            return {
                "workspace_id": workspace_id,
                "success": True,
                "repo": repo_identifier
            }
        else:
            print(f"❌ Expected repo not found: {repo_identifier}")
            if ensure_correct_repo:
                setup_result = setup_git_repo(workspace, expected_repo_url, GITHUB_TOKEN)
                return {
                    "workspace_id": workspace_id,
                    "success": setup_result["success"],
                    "action": "setup_attempted"
                }
            return {
                "workspace_id": workspace_id,
                "success": False,
                "error": f"Expected repo {repo_identifier} not found"
            }
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return {
            "workspace_id": workspace_id,
            "success": False,
            "error": str(e)
        }

async def process_workspace_batch(
    workspace_ids: List[str], 
    timestamp: str, 
    batch_num: int,
    ensure_correct_repo: bool = False
) -> Tuple[List[Dict], List[Dict]]:
    """Process a batch of workspaces concurrently"""
    successful_checks = []
    failed_checks = []
    
    # Reduce max_workers to prevent connection pool exhaustion
    with ThreadPoolExecutor(max_workers=20) as executor:  # Reduced from batch size to 20
        futures = []
        for workspace_id in workspace_ids:
            # Add delay between submissions to prevent overwhelming the connection pool
            await asyncio.sleep(0.1)  # 100ms delay between submissions
            futures.append(
                executor.submit(
                    verify_git_repo, 
                    workspace_id, 
                    ensure_correct_repo
                )
            )
        
        # Process results as they complete
        for future in futures:
            result = future.result()
            if result["success"]:
                successful_checks.append(result)
            else:
                failed_checks.append(result)

    # Save batch results
    batch_suffix = f"{timestamp}_batch{batch_num}"
    
    if successful_checks:
        with open(f'env-migration-marko/git_successful_{batch_suffix}.json', 'w') as f:
            json.dump(successful_checks, f, indent=4)
            
    if failed_checks:
        with open(f'env-migration-marko/git_failed_{batch_suffix}.json', 'w') as f:
            json.dump(failed_checks, f, indent=4)
    
    return successful_checks, failed_checks

async def main():
    # Simplified command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument('--ensure-correct-repo', action='store_true', 
                       help='Attempt to fix incorrect git repositories')
    args = parser.parse_args()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # # Load workspace IDs from JSON file
    # with open('env-migration-marko/workspace_ids.json', 'r') as f:
    #     workspace_data = json.load(f)
    #     # Extract just the IDs from the workspaces
    #     all_workspace_ids = [workspace["id"] for workspace in workspace_data["single_workspaces"]]

  # Load workspace IDs from new_workspaces.json
    with open('env-migration-marko/new_workspaces.json', 'r') as f:
        workspace_data = json.load(f)
        # Extract just the workspace IDs from successful workspaces
        all_workspace_ids = [workspace["workspace_id"] for workspace in workspace_data["successful"]]

    # Manually specify workspace IDs
    # all_workspace_ids = [
    #     "8klcp3-1195",
    #     "ycitux-1197", 
    #     "op3bye-1198"
    # ]

    # Reduce batch size
    batch_size = 100
    batches = [
        all_workspace_ids[i:i + batch_size]
        for i in range(0, len(all_workspace_ids), batch_size)
    ]
    
    print(f"Processing {len(all_workspace_ids)} workspaces in {len(batches)} batches")
    
    all_successful = []
    all_failed = []
    
    # Process batches
    for batch_num, batch in enumerate(batches, 1):
        print(f"\nProcessing batch {batch_num}/{len(batches)} ({len(batch)} workspaces)")
        
        # Add approval prompt
        while True:
            approval = input(f"\nDo you want to process batch {batch_num}/{len(batches)} with {len(batch)} workspaces? (y/n): ").lower()
            if approval in ['y', 'n']:
                break
            print("Please enter 'y' for yes or 'n' for no.")
        
        if approval == 'n':
            print(f"Skipping batch {batch_num} as per user request")
            continue
            
        successful, failed = await process_workspace_batch(
            batch, 
            timestamp, 
            batch_num,
            args.ensure_correct_repo
        )
        
        # Print batch summary
        print(f"\nBatch {batch_num} complete:")
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
        
        all_successful.extend(successful)
        all_failed.extend(failed)
    
    # Save final combined results
    with open(f'env-migration-marko/3all_git_successful_{timestamp}.json', 'w') as f:
        json.dump(all_successful, f, indent=4)
    
    with open(f'env-migration-marko/3all_git_failed_{timestamp}.json', 'w') as f:
        json.dump(all_failed, f, indent=4)
    
    # Print final summary
    print("\n=== Git Verification Complete ===")
    print(f"Total workspaces: {len(all_workspace_ids)}")
    print(f"Total successful: {len(all_successful)}")
    print(f"Total failed: {len(all_failed)}")
    
    # Print file locations
    print("\nFinal results saved to:")
    print(f"All successful checks: env-migration-marko/3all_git_successful_{timestamp}.json")
    print(f"All failed checks: env-migration-marko/3all_git_failed_{timestamp}.json")

if __name__ == "__main__":
    asyncio.run(main())
