{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit] bg-transparent ring-offset-background focus:rounded-xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n));\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName;\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" && \"h-0.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n));\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName;\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6VAAC,iRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6VAAC,iRAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6VAAC;;;;;0BACD,6VAAC,iRAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,iRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6VAAC,iRAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cAAc,sDAC9B,gBAAgB,gBAAgB,wDAChC;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,iRAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,iRAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/billing.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { errorToast } from \"@/features/global/toast\";\r\nimport { createCustomerPortalSession } from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useState } from \"react\";\r\n\r\nconst SettingsBilling = () => {\r\n  const { user } = useAuth();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const createCustomerPortal = async () => {\r\n    if (user.userFromDb?.stripe_customer_id) {\r\n      setIsLoading(true);\r\n      const customerPortalSession = await createCustomerPortalSession(\r\n        user.userFromDb.stripe_customer_id,\r\n      );\r\n      window.open(customerPortalSession.url, \"_blank\");\r\n      setIsLoading(false);\r\n    } else {\r\n      errorToast(\"Failed to create customer portal session. Please try again.\");\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <div className=\"flex items-center justify-between gap-1 space-y-1\">\r\n        <div className=\"flex flex-col gap-0\">\r\n          <Typography.H5>Check Stripe Billing</Typography.H5>\r\n          <Typography.P>Check your Stripe billing information.</Typography.P>\r\n        </div>\r\n\r\n        <Button size=\"sm\" className=\"h-8\" disabled={isLoading} onClick={createCustomerPortal}>\r\n          {isLoading ? \"Navigating to Stripe...\" : \"Check Stripe Billing\"}\r\n        </Button>\r\n      </div>\r\n\r\n      {/* <Button onClick={createCustomerPortal} disabled={isLoading}>\r\n        {isLoading ? \"Navigating to Stripe...\" : \"Check Stripe Billing\"}\r\n      </Button> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsBilling;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,kBAAkB;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,uBAAuB;QAC3B,IAAI,KAAK,UAAU,EAAE,oBAAoB;YACvC,aAAa;YACb,MAAM,wBAAwB,MAAM,CAAA,GAAA,iHAAA,CAAA,8BAA2B,AAAD,EAC5D,KAAK,UAAU,CAAC,kBAAkB;YAEpC,OAAO,IAAI,CAAC,sBAAsB,GAAG,EAAE;YACvC,aAAa;QACf,OAAO;YACL,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX,aAAa;QACf;IACF;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sCAAC;;;;;;sCACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;sCAAC;;;;;;;;;;;;8BAGhB,6VAAC,kIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,WAAU;oBAAM,UAAU;oBAAW,SAAS;8BAC7D,YAAY,4BAA4B;;;;;;;;;;;;;;;;;AASnD;uCAEe", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-sidebar-accent\", className)}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-sidebar-ring transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n));\r\nProgress.displayName = ProgressPrimitive.Root.displayName;\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6VAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;QACnF,GAAG,KAAK;kBAET,cAAA,6VAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/remix-project/utils.ts"], "sourcesContent": ["import { connectRemixProjectWebSocket } from \"@/lib/api\";\r\nimport { QueryClient } from \"@tanstack/react-query\";\r\nimport { errorToast, successToast } from \"../global/toast\";\r\n\r\ninterface RemixStepProgress {\r\n  validate_access: number;\r\n  create_project: number;\r\n  create_thread: number;\r\n  finalize: number;\r\n}\r\n\r\ntype StepStatus = \"pending\" | \"completed\" | \"failed\" | \"waiting\";\r\n\r\nexport const handleRemix = async ({\r\n  projectId,\r\n  envValues,\r\n  setIsRemixing,\r\n  setRemixProgress,\r\n  setRemixStep,\r\n  setRemixMessage,\r\n  updateStepState,\r\n  setIsCompleted,\r\n  resetRemixState,\r\n  queryClient,\r\n  setRemixProjectId,\r\n}: {\r\n  projectId: string;\r\n  envValues: Record<string, string>;\r\n  setIsRemixing: (value: boolean) => void;\r\n  setRemixProgress: (value: number) => void;\r\n  setRemixStep: (value: string | null) => void;\r\n  setRemixMessage: (value: string) => void;\r\n  updateStepState: (stepId: string, status: StepStatus, message: string) => void;\r\n  setIsCompleted: (value: boolean) => void;\r\n  resetRemixState: () => void;\r\n  queryClient: QueryClient;\r\n  setRemixProjectId: (value: string | null) => void;\r\n}) => {\r\n  let socket: WebSocket | null = null;\r\n\r\n  try {\r\n    setIsRemixing(true);\r\n    setRemixProgress(0);\r\n    setRemixStep(null);\r\n    setRemixMessage(\"\");\r\n\r\n    // Use the new WebSocket-based remix method\r\n    socket = connectRemixProjectWebSocket({ projectId, envValues });\r\n\r\n    socket.onmessage = (event) => {\r\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n      let data: any = undefined;\r\n      try {\r\n        data = JSON.parse(event.data);\r\n\r\n        if (data.error) {\r\n          throw new Error(data.error);\r\n        }\r\n\r\n        // Handle progress updates\r\n        if (data.step) {\r\n          setRemixStep(data.step);\r\n\r\n          // Calculate progress based on step\r\n          const stepProgress: RemixStepProgress = {\r\n            validate_access: 25,\r\n            create_project: 50,\r\n            create_thread: 75,\r\n            finalize: 100,\r\n          };\r\n\r\n          const progress = stepProgress[data.step as keyof RemixStepProgress] || 0;\r\n          setRemixProgress(progress);\r\n\r\n          // Update step state based on status\r\n          if (data.status === \"started\" || data.status === \"in_progress\") {\r\n            updateStepState(data.step, \"pending\", data.message || \"In progress...\");\r\n            setRemixMessage(data.message || `Processing ${data.step}...`);\r\n          } else if (data.status === \"completed\") {\r\n            updateStepState(data.step, \"completed\", data.message || \"Completed successfully\");\r\n            setRemixMessage(data.message || `${data.step} completed`);\r\n          } else if (data.status === \"failed\") {\r\n            updateStepState(data.step, \"failed\", data.message || \"Failed\");\r\n            throw new Error(data.message || `${data.step} failed`);\r\n          }\r\n        }\r\n\r\n        // Handle final completion\r\n        if (data.step === \"finalize\" && data.status === \"completed\" && data.project_data) {\r\n          setRemixProgress(100);\r\n          setIsCompleted(true);\r\n          setRemixMessage(\"Project cloned successfully! Opening in new tab...\");\r\n\r\n          const newProjectId = data.project_data.project_id;\r\n          if (newProjectId) {\r\n            // Show success toast for 10 seconds\r\n            successToast(\"Project cloned successfully! Opening in new tab...\", { duration: 10000 });\r\n\r\n            // Open project in new tab\r\n            const newProjectUrl = `${window.location.origin}/project/${newProjectId}?remix=true`;\r\n            window.open(newProjectUrl, \"_blank\");\r\n\r\n            setRemixProjectId(newProjectId);\r\n            queryClient.invalidateQueries({ queryKey: [\"projects\"] });\r\n\r\n            // Reset state after showing completion\r\n            // setTimeout(() => {\r\n            //   resetRemixState();\r\n            // }, 3000);\r\n          } else {\r\n            throw new Error(\"No project ID received from remix operation\");\r\n          }\r\n\r\n          if (socket) {\r\n            socket.close();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error processing remix message:\", error);\r\n        const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n        // Update current step as failed if we know which step it was\r\n        if (data?.step) {\r\n          updateStepState(data.step, \"failed\", errorMessage);\r\n        }\r\n\r\n        setRemixMessage(`Error: ${errorMessage}`);\r\n        errorToast(errorMessage || \"Failed to remix project. Please try again.\");\r\n\r\n        if (socket) {\r\n          socket.close();\r\n        }\r\n\r\n        // Reset state after showing error\r\n        setTimeout(() => {\r\n          resetRemixState();\r\n        }, 3000);\r\n      }\r\n    };\r\n\r\n    socket.onerror = (error) => {\r\n      console.error(\"Remix WebSocket error:\", error);\r\n      setRemixMessage(\"Connection error occurred\");\r\n      errorToast(\"Failed to connect to remix service. Please try again.\");\r\n\r\n      // Reset state after showing error\r\n      setTimeout(() => {\r\n        resetRemixState();\r\n      }, 3000);\r\n    };\r\n\r\n    socket.onclose = (event) => {\r\n      if (!event.wasClean && socket) {\r\n        console.error(\"Remix WebSocket closed unexpectedly\");\r\n        setRemixMessage(\"Connection was interrupted\");\r\n        errorToast(\"Remix connection was interrupted. Please try again.\");\r\n\r\n        // Reset state after showing error\r\n        setTimeout(() => {\r\n          resetRemixState();\r\n        }, 3000);\r\n      }\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Remix error:\", error);\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n    setRemixMessage(`Error: ${errorMessage}`);\r\n    errorToast(\"Failed to remix project. Please try again.\");\r\n\r\n    if (socket) {\r\n      socket.close();\r\n    }\r\n\r\n    // Reset state after showing error\r\n    setTimeout(() => {\r\n      resetRemixState();\r\n    }, 3000);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAWO,MAAM,cAAc,OAAO,EAChC,SAAS,EACT,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,eAAe,EACf,cAAc,EACd,eAAe,EACf,WAAW,EACX,iBAAiB,EAalB;IACC,IAAI,SAA2B;IAE/B,IAAI;QACF,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,gBAAgB;QAEhB,2CAA2C;QAC3C,SAAS,CAAA,GAAA,iHAAA,CAAA,+BAA4B,AAAD,EAAE;YAAE;YAAW;QAAU;QAE7D,OAAO,SAAS,GAAG,CAAC;YAClB,8DAA8D;YAC9D,IAAI,OAAY;YAChB,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAE5B,IAAI,KAAK,KAAK,EAAE;oBACd,MAAM,IAAI,MAAM,KAAK,KAAK;gBAC5B;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,IAAI,EAAE;oBACb,aAAa,KAAK,IAAI;oBAEtB,mCAAmC;oBACnC,MAAM,eAAkC;wBACtC,iBAAiB;wBACjB,gBAAgB;wBAChB,eAAe;wBACf,UAAU;oBACZ;oBAEA,MAAM,WAAW,YAAY,CAAC,KAAK,IAAI,CAA4B,IAAI;oBACvE,iBAAiB;oBAEjB,oCAAoC;oBACpC,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK,eAAe;wBAC9D,gBAAgB,KAAK,IAAI,EAAE,WAAW,KAAK,OAAO,IAAI;wBACtD,gBAAgB,KAAK,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;oBAC9D,OAAO,IAAI,KAAK,MAAM,KAAK,aAAa;wBACtC,gBAAgB,KAAK,IAAI,EAAE,aAAa,KAAK,OAAO,IAAI;wBACxD,gBAAgB,KAAK,OAAO,IAAI,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;oBAC1D,OAAO,IAAI,KAAK,MAAM,KAAK,UAAU;wBACnC,gBAAgB,KAAK,IAAI,EAAE,UAAU,KAAK,OAAO,IAAI;wBACrD,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC;oBACvD;gBACF;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,MAAM,KAAK,eAAe,KAAK,YAAY,EAAE;oBAChF,iBAAiB;oBACjB,eAAe;oBACf,gBAAgB;oBAEhB,MAAM,eAAe,KAAK,YAAY,CAAC,UAAU;oBACjD,IAAI,cAAc;wBAChB,oCAAoC;wBACpC,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,sDAAsD;4BAAE,UAAU;wBAAM;wBAErF,0BAA0B;wBAC1B,MAAM,gBAAgB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,aAAa,WAAW,CAAC;wBACpF,OAAO,IAAI,CAAC,eAAe;wBAE3B,kBAAkB;wBAClB,YAAY,iBAAiB,CAAC;4BAAE,UAAU;gCAAC;6BAAW;wBAAC;oBAEvD,uCAAuC;oBACvC,qBAAqB;oBACrB,uBAAuB;oBACvB,YAAY;oBACd,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;oBAEA,IAAI,QAAQ;wBACV,OAAO,KAAK;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAE9D,6DAA6D;gBAC7D,IAAI,MAAM,MAAM;oBACd,gBAAgB,KAAK,IAAI,EAAE,UAAU;gBACvC;gBAEA,gBAAgB,CAAC,OAAO,EAAE,cAAc;gBACxC,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB;gBAE3B,IAAI,QAAQ;oBACV,OAAO,KAAK;gBACd;gBAEA,kCAAkC;gBAClC,WAAW;oBACT;gBACF,GAAG;YACL;QACF;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,gBAAgB;YAChB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YAEX,kCAAkC;YAClC,WAAW;gBACT;YACF,GAAG;QACL;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,IAAI,CAAC,MAAM,QAAQ,IAAI,QAAQ;gBAC7B,QAAQ,KAAK,CAAC;gBACd,gBAAgB;gBAChB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;gBAEX,kCAAkC;gBAClC,WAAW;oBACT;gBACF,GAAG;YACL;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC9D,gBAAgB,CAAC,OAAO,EAAE,cAAc;QACxC,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QAEX,IAAI,QAAQ;YACV,OAAO,KAAK;QACd;QAEA,kCAAkC;QAClC,WAAW;YACT;QACF,GAAG;IACL;AACF", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/stores/clone-state.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { subscribeWithSelector } from \"zustand/middleware\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\n\r\ntype StepStatus = \"pending\" | \"completed\" | \"failed\" | \"waiting\";\r\n\r\ntype StepState = {\r\n  status: StepStatus;\r\n  message: string;\r\n};\r\n\r\ntype EnvValues = {\r\n  [key: string]: string;\r\n};\r\n\r\ninterface CloneState {\r\n  // Remix/Clone progress state\r\n  isRemixing: boolean;\r\n  remixProgress: number;\r\n  currentRemixStep: string | null;\r\n  remixMessage: string;\r\n  stepStates: Record<string, StepState>;\r\n  isCompleted: boolean;\r\n  activeStepIndex: number;\r\n\r\n  // Environment variables state\r\n  envValues: EnvValues;\r\n  hasFirebaseKeys: boolean;\r\n}\r\n\r\ninterface CloneActions {\r\n  // Remix/Clone actions\r\n  setIsRemixing: (isRemixing: boolean) => void;\r\n  setRemixProgress: (progress: number) => void;\r\n  setCurrentRemixStep: (step: string | null) => void;\r\n  setRemixMessage: (message: string) => void;\r\n  setStepStates: (stepStates: Record<string, StepState>) => void;\r\n  updateStepState: (stepId: string, status: StepStatus, message: string) => void;\r\n  setIsCompleted: (isCompleted: boolean) => void;\r\n  setActiveStepIndex: (index: number) => void;\r\n\r\n  // Environment variables actions\r\n  setEnvValues: (envValues: EnvValues) => void;\r\n  updateEnvValue: (key: string, value: string) => void;\r\n  setHasFirebaseKeys: (hasFirebaseKeys: boolean) => void;\r\n\r\n  // Reset actions\r\n  resetRemixState: () => void;\r\n  resetAll: () => void;\r\n}\r\n\r\nconst initialState: CloneState = {\r\n  isRemixing: false,\r\n  remixProgress: 0,\r\n  currentRemixStep: null,\r\n  remixMessage: \"\",\r\n  stepStates: {},\r\n  isCompleted: false,\r\n  activeStepIndex: 0,\r\n  envValues: {},\r\n  hasFirebaseKeys: false,\r\n};\r\n\r\nexport const useCloneStore = create<CloneState & CloneActions>()(\r\n  subscribeWithSelector((set) => ({\r\n    ...initialState,\r\n\r\n    // Remix/Clone actions\r\n    setIsRemixing: (isRemixing) => set({ isRemixing }),\r\n    setRemixProgress: (remixProgress) => set({ remixProgress }),\r\n    setCurrentRemixStep: (currentRemixStep) => set({ currentRemixStep }),\r\n    setRemixMessage: (remixMessage) => set({ remixMessage }),\r\n    setStepStates: (stepStates) => set({ stepStates }),\r\n    updateStepState: (stepId, status, message) =>\r\n      set((state) => ({\r\n        stepStates: {\r\n          ...state.stepStates,\r\n          [stepId]: { status, message },\r\n        },\r\n      })),\r\n    setIsCompleted: (isCompleted) => set({ isCompleted }),\r\n    setActiveStepIndex: (activeStepIndex) => set({ activeStepIndex }),\r\n\r\n    // Environment variables actions\r\n    setEnvValues: (envValues) => set({ envValues }),\r\n    updateEnvValue: (key, value) =>\r\n      set((state) => ({\r\n        envValues: {\r\n          ...state.envValues,\r\n          [key]: value,\r\n        },\r\n      })),\r\n    setHasFirebaseKeys: (hasFirebaseKeys) => set({ hasFirebaseKeys }),\r\n\r\n    // Reset actions\r\n    resetRemixState: () =>\r\n      set({\r\n        isRemixing: false,\r\n        remixProgress: 0,\r\n        currentRemixStep: null,\r\n        remixMessage: \"\",\r\n        stepStates: {},\r\n        isCompleted: false,\r\n        activeStepIndex: 0,\r\n      }),\r\n    resetAll: () => set(initialState),\r\n  })),\r\n);\r\n\r\n// Hook to get all clone state\r\nexport const useCloneState = () => {\r\n  return useCloneStore(\r\n    useShallow((state) => ({\r\n      isRemixing: state.isRemixing,\r\n      remixProgress: state.remixProgress,\r\n      currentRemixStep: state.currentRemixStep,\r\n      remixMessage: state.remixMessage,\r\n      stepStates: state.stepStates,\r\n      isCompleted: state.isCompleted,\r\n      activeStepIndex: state.activeStepIndex,\r\n      envValues: state.envValues,\r\n      hasFirebaseKeys: state.hasFirebaseKeys,\r\n    })),\r\n  );\r\n};\r\n\r\n// Hook to get all clone actions\r\nexport const useCloneActions = () => {\r\n  return useCloneStore(\r\n    useShallow((state) => ({\r\n      setIsRemixing: state.setIsRemixing,\r\n      setRemixProgress: state.setRemixProgress,\r\n      setCurrentRemixStep: state.setCurrentRemixStep,\r\n      setRemixMessage: state.setRemixMessage,\r\n      setStepStates: state.setStepStates,\r\n      updateStepState: state.updateStepState,\r\n      setIsCompleted: state.setIsCompleted,\r\n      setActiveStepIndex: state.setActiveStepIndex,\r\n      setEnvValues: state.setEnvValues,\r\n      updateEnvValue: state.updateEnvValue,\r\n      setHasFirebaseKeys: state.setHasFirebaseKeys,\r\n      resetRemixState: state.resetRemixState,\r\n      resetAll: state.resetAll,\r\n    })),\r\n  );\r\n};\r\n\r\n// Individual hooks for specific state\r\nexport const useIsRemixing = () => useCloneStore((state) => state.isRemixing);\r\nexport const useRemixProgress = () => useCloneStore((state) => state.remixProgress);\r\nexport const useCurrentRemixStep = () => useCloneStore((state) => state.currentRemixStep);\r\nexport const useRemixMessage = () => useCloneStore((state) => state.remixMessage);\r\nexport const useStepStates = () => useCloneStore((state) => state.stepStates);\r\nexport const useIsCompleted = () => useCloneStore((state) => state.isCompleted);\r\nexport const useActiveStepIndex = () => useCloneStore((state) => state.activeStepIndex);\r\nexport const useEnvValues = () => useCloneStore((state) => state.envValues);\r\nexport const useHasFirebaseKeys = () => useCloneStore((state) => state.hasFirebaseKeys);\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAiDA,MAAM,eAA2B;IAC/B,YAAY;IACZ,eAAe;IACf,kBAAkB;IAClB,cAAc;IACd,YAAY,CAAC;IACb,aAAa;IACb,iBAAiB;IACjB,WAAW,CAAC;IACZ,iBAAiB;AACnB;AAEO,MAAM,gBAAgB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,yPAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,MAAQ,CAAC;QAC9B,GAAG,YAAY;QAEf,sBAAsB;QACtB,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;QAChD,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QACzD,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAClE,iBAAiB,CAAC,eAAiB,IAAI;gBAAE;YAAa;QACtD,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;QAChD,iBAAiB,CAAC,QAAQ,QAAQ,UAChC,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,OAAO,EAAE;4BAAE;4BAAQ;wBAAQ;oBAC9B;gBACF,CAAC;QACH,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QACnD,oBAAoB,CAAC,kBAAoB,IAAI;gBAAE;YAAgB;QAE/D,gCAAgC;QAChC,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC7C,gBAAgB,CAAC,KAAK,QACpB,IAAI,CAAC,QAAU,CAAC;oBACd,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,IAAI,EAAE;oBACT;gBACF,CAAC;QACH,oBAAoB,CAAC,kBAAoB,IAAI;gBAAE;YAAgB;QAE/D,gBAAgB;QAChB,iBAAiB,IACf,IAAI;gBACF,YAAY;gBACZ,eAAe;gBACf,kBAAkB;gBAClB,cAAc;gBACd,YAAY,CAAC;gBACb,aAAa;gBACb,iBAAiB;YACnB;QACF,UAAU,IAAM,IAAI;IACtB,CAAC;AAII,MAAM,gBAAgB;IAC3B,OAAO,cACL,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,kBAAkB,MAAM,gBAAgB;YACxC,cAAc,MAAM,YAAY;YAChC,YAAY,MAAM,UAAU;YAC5B,aAAa,MAAM,WAAW;YAC9B,iBAAiB,MAAM,eAAe;YACtC,WAAW,MAAM,SAAS;YAC1B,iBAAiB,MAAM,eAAe;QACxC,CAAC;AAEL;AAGO,MAAM,kBAAkB;IAC7B,OAAO,cACL,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,eAAe,MAAM,aAAa;YAClC,kBAAkB,MAAM,gBAAgB;YACxC,qBAAqB,MAAM,mBAAmB;YAC9C,iBAAiB,MAAM,eAAe;YACtC,eAAe,MAAM,aAAa;YAClC,iBAAiB,MAAM,eAAe;YACtC,gBAAgB,MAAM,cAAc;YACpC,oBAAoB,MAAM,kBAAkB;YAC5C,cAAc,MAAM,YAAY;YAChC,gBAAgB,MAAM,cAAc;YACpC,oBAAoB,MAAM,kBAAkB;YAC5C,iBAAiB,MAAM,eAAe;YACtC,UAAU,MAAM,QAAQ;QAC1B,CAAC;AAEL;AAGO,MAAM,gBAAgB,IAAM,cAAc,CAAC,QAAU,MAAM,UAAU;AACrE,MAAM,mBAAmB,IAAM,cAAc,CAAC,QAAU,MAAM,aAAa;AAC3E,MAAM,sBAAsB,IAAM,cAAc,CAAC,QAAU,MAAM,gBAAgB;AACjF,MAAM,kBAAkB,IAAM,cAAc,CAAC,QAAU,MAAM,YAAY;AACzE,MAAM,gBAAgB,IAAM,cAAc,CAAC,QAAU,MAAM,UAAU;AACrE,MAAM,iBAAiB,IAAM,cAAc,CAAC,QAAU,MAAM,WAAW;AACvE,MAAM,qBAAqB,IAAM,cAAc,CAAC,QAAU,MAAM,eAAe;AAC/E,MAAM,eAAe,IAAM,cAAc,CAAC,QAAU,MAAM,SAAS;AACnE,MAAM,qBAAqB,IAAM,cAAc,CAAC,QAAU,MAAM,eAAe", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/clone.tsx"], "sourcesContent": ["import BaseEmptyState from \"@/components/base-empty-state\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport {\r\n  Stepper,\r\n  StepperIndicator,\r\n  StepperItem,\r\n  StepperSeparator,\r\n  StepperTitle,\r\n  StepperTrigger,\r\n} from \"@/components/ui/stepper\";\r\nimport { TextShimmer } from \"@/components/ui/text-shimmer\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { handleRemix } from \"@/features/remix-project/utils\";\r\nimport { getEnvKeys } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { useCloneActions, useCloneState } from \"@/stores/clone-state\";\r\nimport { CheckCircleSolid, CoffeeSolid } from \"@mynaui/icons-react\";\r\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { Crown } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { memo, useEffect, useState } from \"react\";\r\nimport { LuCopy } from \"react-icons/lu\";\r\n\r\ntype StepStatus = \"pending\" | \"completed\" | \"failed\" | \"waiting\";\r\n\r\ntype StepState = {\r\n  status: StepStatus;\r\n  message: string;\r\n};\r\n\r\nconst remixSteps = [\r\n  { id: \"validate_access\", name: \"Validate Project Access\" },\r\n  { id: \"create_project\", name: \"Create Remixed Project\" },\r\n  { id: \"create_thread\", name: \"Create Initial Thread\" },\r\n  { id: \"finalize\", name: \"Finalize Remix\" },\r\n];\r\n\r\nconst SettingsClone = () => {\r\n  const { projectId } = useProject();\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [lastProjectId, setLastProjectId] = useState<string | null>(null);\r\n  const { user } = useAuth();\r\n  const queryClient = useQueryClient();\r\n  const [remixProjectId, setRemixProjectId] = useState<string | null>(null);\r\n\r\n  // Use Zustand store for clone state\r\n  const {\r\n    isRemixing,\r\n    remixProgress,\r\n    currentRemixStep,\r\n    remixMessage,\r\n    stepStates,\r\n    isCompleted,\r\n    activeStepIndex,\r\n    envValues,\r\n    hasFirebaseKeys,\r\n  } = useCloneState();\r\n\r\n  const {\r\n    setIsRemixing,\r\n    setRemixProgress,\r\n    setCurrentRemixStep,\r\n    setRemixMessage,\r\n    setStepStates,\r\n    updateStepState,\r\n    setIsCompleted,\r\n    setActiveStepIndex,\r\n    setEnvValues,\r\n    updateEnvValue,\r\n    setHasFirebaseKeys,\r\n    resetRemixState,\r\n    resetAll,\r\n  } = useCloneActions();\r\n\r\n  // Reset clone state when project changes\r\n  useEffect(() => {\r\n    if (lastProjectId && lastProjectId !== projectId) {\r\n      // Project has changed, reset all clone state\r\n      resetAll();\r\n    }\r\n    setLastProjectId(projectId);\r\n  }, [projectId, lastProjectId, resetAll]);\r\n\r\n  const { data: envKeys, isSuccess } = useQuery({\r\n    queryKey: [\"env-keys\", projectId],\r\n    queryFn: () => getEnvKeys(projectId),\r\n    enabled: !!projectId,\r\n    refetchInterval: 1000 * 60 * 5,\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (envKeys && isSuccess && !isLoading) {\r\n      const firebaseKeys = envKeys.keys.filter((key: string) =>\r\n        key.startsWith(\"NEXT_PUBLIC_FIREBASE_\"),\r\n      );\r\n      const nonFirebaseKeys = envKeys.keys.filter(\r\n        (key: string) => !key.startsWith(\"NEXT_PUBLIC_FIREBASE_\"),\r\n      );\r\n\r\n      setHasFirebaseKeys(firebaseKeys.length > 0);\r\n\r\n      // Only initialize env values if they haven't been set yet\r\n      if (Object.keys(envValues).length === 0) {\r\n        const initialValues: { [key: string]: string } = {};\r\n        nonFirebaseKeys.forEach((key: string) => {\r\n          const cleanKey = key.replace(\"=\", \"\");\r\n          initialValues[cleanKey] = \"\";\r\n        });\r\n        setEnvValues(initialValues);\r\n      }\r\n      setIsLoading(false);\r\n    }\r\n  }, [envKeys, isSuccess, isLoading, envValues, setHasFirebaseKeys, setEnvValues]);\r\n\r\n  // Initialize step states when remixing starts\r\n  useEffect(() => {\r\n    if (isRemixing && Object.keys(stepStates).length === 0) {\r\n      const initialStepStates: Record<string, StepState> = {};\r\n      remixSteps.forEach((step) => {\r\n        initialStepStates[step.id] = {\r\n          status: \"waiting\",\r\n          message: \"Waiting to start...\",\r\n        };\r\n      });\r\n      setStepStates(initialStepStates);\r\n    }\r\n  }, [isRemixing, stepStates, setStepStates]);\r\n\r\n  // Update active step index based on current remix step\r\n  useEffect(() => {\r\n    if (currentRemixStep && isRemixing) {\r\n      const stepIndex = remixSteps.findIndex((step) => step.id === currentRemixStep);\r\n      if (stepIndex !== -1) {\r\n        setActiveStepIndex(stepIndex);\r\n      }\r\n    }\r\n  }, [currentRemixStep, isRemixing, setActiveStepIndex]);\r\n\r\n  const handleEnvValueChange = (key: string, value: string) => {\r\n    updateEnvValue(key, value);\r\n  };\r\n\r\n  const handleClone = async () => {\r\n    if (!projectId) return;\r\n\r\n    setIsRemixing(true);\r\n    setIsCompleted(false);\r\n    setRemixProgress(0);\r\n    setCurrentRemixStep(null);\r\n    setRemixMessage(\"\");\r\n    setStepStates({});\r\n    setActiveStepIndex(0);\r\n\r\n    try {\r\n      await handleRemix({\r\n        projectId,\r\n        envValues,\r\n        setIsRemixing,\r\n        setRemixProgress,\r\n        setRemixStep: setCurrentRemixStep,\r\n        setRemixMessage,\r\n        updateStepState,\r\n        setIsCompleted,\r\n        resetRemixState,\r\n        queryClient,\r\n        setRemixProjectId,\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Clone error:\", error);\r\n      resetRemixState();\r\n    }\r\n  };\r\n\r\n  if (user?.userFromDb?.plan === \"free-tier\") {\r\n    return (\r\n      <BaseEmptyState\r\n        icon={<Crown className=\"size-8 text-muted-foreground\" />}\r\n        title=\"Clone is not available on the free tier\"\r\n        description=\"Please upgrade to a paid plan to clone projects.\"\r\n        buttonText=\"Upgrade\"\r\n        href=\"/pricing\"\r\n        className=\"min-h-[400px]\"\r\n      />\r\n    );\r\n  }\r\n\r\n  if (isRemixing) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"text-center\">\r\n          <div className=\"relative mb-4\">\r\n            {isCompleted ? (\r\n              <div className=\"flex items-center justify-center\">\r\n                <CheckCircleSolid className=\"size-16 text-green-500\" />\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center justify-center\">\r\n                <Loading className=\"size-12 text-primary\" />\r\n              </div>\r\n            )}\r\n          </div>\r\n          <h3 className=\"mb-2 text-lg font-medium\">\r\n            {isCompleted ? \"Project Cloned Successfully!\" : \"Cloning Project...\"}\r\n          </h3>\r\n          <Typography.P className=\"mb-3 text-muted-foreground\">\r\n            {isCompleted\r\n              ? \"Your project has been successfully cloned and opened in a new tab!\"\r\n              : \"Creating your own copy of this project\"}\r\n          </Typography.P>\r\n\r\n          {!isCompleted && (\r\n            <div className=\"mx-auto w-full rounded-lg border border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50 p-4 dark:border-amber-800 dark:from-amber-950/20 dark:to-orange-950/20\">\r\n              <div className=\"flex items-center justify-center gap-2 text-amber-700 dark:text-amber-300\">\r\n                <CoffeeSolid className=\"h-4 w-4\" />\r\n                <Typography.H5>Grab a coffee while we work our magic!</Typography.H5>\r\n              </div>\r\n              <Typography.P className=\"mt-1 text-center text-sm text-amber-600 dark:text-amber-400\">\r\n                This usually takes 1-4 minutes\r\n              </Typography.P>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between gap-2\">\r\n          <Progress value={remixProgress} className=\"h-3\" />\r\n          <span className=\"text-sm font-medium text-primary\">\r\n            {isCompleted ? \"Complete!\" : `${Math.round(remixProgress)}%`}\r\n          </span>\r\n        </div>\r\n\r\n        <div className=\"rounded-lg border border-border bg-card p-4 shadow-sm\">\r\n          <Stepper value={activeStepIndex} orientation=\"vertical\" className=\"w-full\">\r\n            {remixSteps.map((step, index) => {\r\n              const stepState = stepStates[step.id];\r\n\r\n              return (\r\n                <StepperItemMemo\r\n                  key={step.id}\r\n                  step={index}\r\n                  stepState={stepState || { status: \"waiting\", message: \"Waiting to start...\" }}\r\n                  isCurrentStep={currentRemixStep === step.id && !isCompleted}\r\n                  stepName={step.name}\r\n                  isLastStep={index === remixSteps.length - 1}\r\n                />\r\n              );\r\n            })}\r\n          </Stepper>\r\n        </div>\r\n\r\n        {remixMessage && !isCompleted && (\r\n          <div className=\"rounded-lg border border-primary/20 bg-primary/5 p-4\">\r\n            <p className=\"text-center text-sm font-medium text-primary\">{remixMessage}</p>\r\n          </div>\r\n        )}\r\n\r\n        {(isCompleted || Object.values(stepStates).some((state) => state.status === \"failed\")) && (\r\n          <div className=\"flex justify-center\">\r\n            <Button variant=\"default\" asChild className=\"mb-4\">\r\n              <Link href={`/project/${remixProjectId}?remix=true`} target=\"_blank\">\r\n                View Cloned Project\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {hasFirebaseKeys && (\r\n          <div className=\"mb-4 px-6 pt-6\">\r\n            <div className=\"rounded-lg border bg-muted p-4\">\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                ⚠️ This project requires Firebase setup. After cloning, you&apos;ll need to\r\n                configure Firebase for the app to work properly.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {envKeys && envKeys.keys && envKeys.keys.length > 0 && !isLoading && (\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex flex-col\">\r\n              <Typography.H5>Environment Variables</Typography.H5>\r\n              <Typography.P>You can set these now or after cloning the project.</Typography.P>\r\n            </div>\r\n            <ScrollArea className=\"flex h-fit max-h-[370px] flex-col gap-5\">\r\n              {envKeys.keys\r\n                .filter((key: string) => !key.startsWith(\"NEXT_PUBLIC_FIREBASE_\"))\r\n                .map((key: string) => {\r\n                  const cleanKey = key.replace(\"=\", \"\");\r\n                  return (\r\n                    <div key={cleanKey} className=\"mb-5 flex flex-col gap-3\">\r\n                      <label className=\"text-sm font-medium text-muted-foreground\">\r\n                        {cleanKey}\r\n                      </label>\r\n                      <Input\r\n                        type=\"text\"\r\n                        value={envValues[cleanKey] || \"\"}\r\n                        onChange={(e) => handleEnvValueChange(cleanKey, e.target.value)}\r\n                        placeholder={`Enter value for ${cleanKey}`}\r\n                      />\r\n                    </div>\r\n                  );\r\n                })}\r\n            </ScrollArea>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"pb-6\">\r\n          <Button onClick={handleClone} disabled={isRemixing} className=\"w-full\">\r\n            {isRemixing ? (\r\n              <>\r\n                <Loading className=\"size-4 animate-spin text-background\" />\r\n                Cloning...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <LuCopy className=\"h-4 w-4\" />\r\n                Clone Project\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nconst StepperItemMemo = memo(\r\n  ({\r\n    step,\r\n    stepState,\r\n    isCurrentStep,\r\n    stepName,\r\n    isLastStep,\r\n  }: {\r\n    step: number;\r\n    stepState: StepState;\r\n    isCurrentStep: boolean;\r\n    stepName: string;\r\n    isLastStep: boolean;\r\n  }) => {\r\n    return (\r\n      <StepperItem\r\n        key={step}\r\n        step={step}\r\n        completed={stepState.status === \"completed\"}\r\n        loading={isCurrentStep && stepState.status === \"pending\"}\r\n        disabled={stepState.status === \"failed\"}\r\n        className={cn(\"not-last:flex-1 relative w-full items-start\")}\r\n      >\r\n        <StepperTrigger className=\"w-full items-start rounded pb-8 last:pb-0\">\r\n          <StepperIndicator\r\n            className=\"group-data-loading/step:text-transparent\"\r\n            failed={stepState.status === \"failed\"}\r\n          />\r\n          <div className=\"flex w-full items-center justify-between px-2 text-left\">\r\n            <StepperTitle>{stepName}</StepperTitle>\r\n            <div className=\"ml-auto\">\r\n              {stepState.status === \"failed\" && (\r\n                <Badge variant=\"destructive\">{stepState.message || \"Failed\"}</Badge>\r\n              )}\r\n              {stepState.status === \"completed\" && (\r\n                <Badge variant=\"success\">{stepState.message || \"Completed\"}</Badge>\r\n              )}\r\n              {isCurrentStep && stepState.status === \"pending\" && (\r\n                <TextShimmer className=\"px-0 text-sm\">In progress...</TextShimmer>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </StepperTrigger>\r\n        {!isLastStep && (\r\n          <StepperSeparator className=\"absolute inset-y-0 left-3 top-[calc(1.5rem+0.125rem)] -order-1 m-0 -translate-x-1/2 group-data-[orientation=vertical]/stepper:h-[calc(100%-1.5rem-0.25rem)]\" />\r\n        )}\r\n      </StepperItem>\r\n    );\r\n  },\r\n);\r\n\r\nStepperItemMemo.displayName = \"StepperItemMemo\";\r\n\r\nexport default SettingsClone;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,IAAI;QAAmB,MAAM;IAA0B;IACzD;QAAE,IAAI;QAAkB,MAAM;IAAyB;IACvD;QAAE,IAAI;QAAiB,MAAM;IAAwB;IACrD;QAAE,IAAI;QAAY,MAAM;IAAiB;CAC1C;AAED,MAAM,gBAAgB;IACpB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,oCAAoC;IACpC,MAAM,EACJ,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,WAAW,EACX,eAAe,EACf,SAAS,EACT,eAAe,EAChB,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,EACJ,aAAa,EACb,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,aAAa,EACb,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,QAAQ,EACT,GAAG,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;IAElB,yCAAyC;IACzC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,kBAAkB,WAAW;YAChD,6CAA6C;YAC7C;QACF;QACA,iBAAiB;IACnB,GAAG;QAAC;QAAW;QAAe;KAAS;IAEvC,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QAC5C,UAAU;YAAC;YAAY;SAAU;QACjC,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE;QAC1B,SAAS,CAAC,CAAC;QACX,iBAAiB,OAAO,KAAK;IAC/B;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,aAAa,CAAC,WAAW;YACtC,MAAM,eAAe,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,MACxC,IAAI,UAAU,CAAC;YAEjB,MAAM,kBAAkB,QAAQ,IAAI,CAAC,MAAM,CACzC,CAAC,MAAgB,CAAC,IAAI,UAAU,CAAC;YAGnC,mBAAmB,aAAa,MAAM,GAAG;YAEzC,0DAA0D;YAC1D,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK,GAAG;gBACvC,MAAM,gBAA2C,CAAC;gBAClD,gBAAgB,OAAO,CAAC,CAAC;oBACvB,MAAM,WAAW,IAAI,OAAO,CAAC,KAAK;oBAClC,aAAa,CAAC,SAAS,GAAG;gBAC5B;gBACA,aAAa;YACf;YACA,aAAa;QACf;IACF,GAAG;QAAC;QAAS;QAAW;QAAW;QAAW;QAAoB;KAAa;IAE/E,8CAA8C;IAC9C,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,IAAI,CAAC,YAAY,MAAM,KAAK,GAAG;YACtD,MAAM,oBAA+C,CAAC;YACtD,WAAW,OAAO,CAAC,CAAC;gBAClB,iBAAiB,CAAC,KAAK,EAAE,CAAC,GAAG;oBAC3B,QAAQ;oBACR,SAAS;gBACX;YACF;YACA,cAAc;QAChB;IACF,GAAG;QAAC;QAAY;QAAY;KAAc;IAE1C,uDAAuD;IACvD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,oBAAoB,YAAY;YAClC,MAAM,YAAY,WAAW,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAC7D,IAAI,cAAc,CAAC,GAAG;gBACpB,mBAAmB;YACrB;QACF;IACF,GAAG;QAAC;QAAkB;QAAY;KAAmB;IAErD,MAAM,uBAAuB,CAAC,KAAa;QACzC,eAAe,KAAK;IACtB;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW;QAEhB,cAAc;QACd,eAAe;QACf,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,cAAc,CAAC;QACf,mBAAmB;QAEnB,IAAI;YACF,MAAM,CAAA,GAAA,4IAAA,CAAA,cAAW,AAAD,EAAE;gBAChB;gBACA;gBACA;gBACA;gBACA,cAAc;gBACd;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B;QACF;IACF;IAEA,IAAI,MAAM,YAAY,SAAS,aAAa;QAC1C,qBACE,6VAAC,4IAAA,CAAA,UAAc;YACb,oBAAM,6VAAC,wRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAM;YACN,aAAY;YACZ,YAAW;YACX,MAAK;YACL,WAAU;;;;;;IAGhB;IAEA,IAAI,YAAY;QACd,qBACE,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;sCACZ,4BACC,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,2UAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;qDAG9B,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAIzB,6VAAC;4BAAG,WAAU;sCACX,cAAc,iCAAiC;;;;;;sCAElD,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;4BAAC,WAAU;sCACrB,cACG,uEACA;;;;;;wBAGL,CAAC,6BACA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,iUAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sDAAC;;;;;;;;;;;;8CAEjB,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;oCAAC,WAAU;8CAA8D;;;;;;;;;;;;;;;;;;8BAO5F,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,oIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAe,WAAU;;;;;;sCAC1C,6VAAC;4BAAK,WAAU;sCACb,cAAc,cAAc,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC,CAAC;;;;;;;;;;;;8BAIhE,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,mIAAA,CAAA,UAAO;wBAAC,OAAO;wBAAiB,aAAY;wBAAW,WAAU;kCAC/D,WAAW,GAAG,CAAC,CAAC,MAAM;4BACrB,MAAM,YAAY,UAAU,CAAC,KAAK,EAAE,CAAC;4BAErC,qBACE,6VAAC;gCAEC,MAAM;gCACN,WAAW,aAAa;oCAAE,QAAQ;oCAAW,SAAS;gCAAsB;gCAC5E,eAAe,qBAAqB,KAAK,EAAE,IAAI,CAAC;gCAChD,UAAU,KAAK,IAAI;gCACnB,YAAY,UAAU,WAAW,MAAM,GAAG;+BALrC,KAAK,EAAE;;;;;wBAQlB;;;;;;;;;;;gBAIH,gBAAgB,CAAC,6BAChB,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;gBAIhE,CAAC,eAAe,OAAO,MAAM,CAAC,YAAY,IAAI,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK,SAAS,mBACnF,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,OAAO;wBAAC,WAAU;kCAC1C,cAAA,6VAAC,2QAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,SAAS,EAAE,eAAe,WAAW,CAAC;4BAAE,QAAO;sCAAS;;;;;;;;;;;;;;;;;;;;;;IAQjF;IAEA,qBACE;kBACE,cAAA,6VAAC;YAAI,WAAU;;gBACZ,iCACC,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;gBAQlD,WAAW,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,2BACtD,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;8CAAC;;;;;;8CACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;8CAAC;;;;;;;;;;;;sCAEhB,6VAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,QAAQ,IAAI,CACV,MAAM,CAAC,CAAC,MAAgB,CAAC,IAAI,UAAU,CAAC,0BACxC,GAAG,CAAC,CAAC;gCACJ,MAAM,WAAW,IAAI,OAAO,CAAC,KAAK;gCAClC,qBACE,6VAAC;oCAAmB,WAAU;;sDAC5B,6VAAC;4CAAM,WAAU;sDACd;;;;;;sDAEH,6VAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,CAAC,SAAS,IAAI;4CAC9B,UAAU,CAAC,IAAM,qBAAqB,UAAU,EAAE,MAAM,CAAC,KAAK;4CAC9D,aAAa,CAAC,gBAAgB,EAAE,UAAU;;;;;;;mCARpC;;;;;4BAYd;;;;;;;;;;;;8BAKR,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,UAAU;wBAAY,WAAU;kCAC3D,2BACC;;8CACE,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAwC;;yDAI7D;;8CACE,6VAAC,+NAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;AAS9C;AAEA,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACzB,CAAC,EACC,IAAI,EACJ,SAAS,EACT,aAAa,EACb,QAAQ,EACR,UAAU,EAOX;IACC,qBACE,6VAAC,mIAAA,CAAA,cAAW;QAEV,MAAM;QACN,WAAW,UAAU,MAAM,KAAK;QAChC,SAAS,iBAAiB,UAAU,MAAM,KAAK;QAC/C,UAAU,UAAU,MAAM,KAAK;QAC/B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;;0BAEd,6VAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;;kCACxB,6VAAC,mIAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,QAAQ,UAAU,MAAM,KAAK;;;;;;kCAE/B,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,mIAAA,CAAA,eAAY;0CAAE;;;;;;0CACf,6VAAC;gCAAI,WAAU;;oCACZ,UAAU,MAAM,KAAK,0BACpB,6VAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe,UAAU,OAAO,IAAI;;;;;;oCAEpD,UAAU,MAAM,KAAK,6BACpB,6VAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW,UAAU,OAAO,IAAI;;;;;;oCAEhD,iBAAiB,UAAU,MAAM,KAAK,2BACrC,6VAAC,2IAAA,CAAA,cAAW;wCAAC,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;YAK7C,CAAC,4BACA,6VAAC,mIAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;OA5BzB;;;;;AAgCX;AAGF,gBAAgB,WAAW,GAAG;uCAEf", "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/environment.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { getFileContent, updateFile } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { InfoCircleSolid } from \"@mynaui/icons-react\";\r\nimport { useMutation, useQuery } from \"@tanstack/react-query\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { errorToast, successToast } from \"../../global/toast\";\r\n\r\nconst SettingsEnvironment = () => {\r\n  const { projectId } = useProject();\r\n  const [content, setContent] = useState(\"\");\r\n  const [originalContent, setOriginalContent] = useState(\"\");\r\n\r\n  const { isLoading, data } = useQuery({\r\n    queryKey: [\"env-file\", projectId],\r\n    queryFn: () => getFileContent(projectId, \".env.local\"),\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (data) {\r\n      setContent(data || \"\");\r\n      setOriginalContent(data || \"\");\r\n    }\r\n  }, [data]);\r\n\r\n  const { mutate: saveEnvFile, isPending: isSaving } = useMutation({\r\n    mutationFn: () => updateFile(projectId, \".env.local\", content),\r\n    onSuccess: () => {\r\n      successToast(\"Environment variables updated successfully\");\r\n      setOriginalContent(content);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error saving .env.local content:\", error);\r\n      errorToast(\"Failed to save .env.local file. Please try again.\");\r\n    },\r\n  });\r\n\r\n  const handleSave = () => {\r\n    saveEnvFile();\r\n  };\r\n\r\n  const hasChanges = content !== originalContent;\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"env-editor\" className=\"sr-only\">\r\n          Environment Variables Editor\r\n        </Label>\r\n        <ScrollArea className=\"min-h-[350px] w-full rounded-md border bg-muted/30 md:min-h-[300px]\">\r\n          {isLoading ? (\r\n            <div className=\"flex h-full items-center justify-center\">\r\n              <Loading className=\"size-6 animate-spin\" />\r\n            </div>\r\n          ) : (\r\n            <textarea\r\n              id=\"env-editor\"\r\n              value={content}\r\n              onChange={(e) => setContent(e.target.value)}\r\n              className={cn(\r\n                \"h-[350px] w-full resize-none rounded-md border-0 bg-transparent p-4 font-mono text-sm md:h-[300px]\",\r\n                \"ring-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 dark:bg-transparent\",\r\n              )}\r\n              placeholder={`KEY=VALUE\\nANOTHER_KEY=another_value\\n# Comments start with #`}\r\n            />\r\n          )}\r\n        </ScrollArea>\r\n        <p className=\"px-1 text-xs text-muted-foreground\">\r\n          Enter variables one per line in <code className=\"text-xs\">KEY=VALUE</code> format.\r\n        </p>\r\n      </div>\r\n\r\n      <div className={cn(\"flex items-center justify-end gap-2\", hasChanges && \"justify-between\")}>\r\n        {hasChanges && (\r\n          <Badge variant=\"warning\" className=\"flex items-center gap-1.5 text-xs\">\r\n            <InfoCircleSolid className=\"h-3.5 w-3.5\" />\r\n            <span>Unsaved changes</span>\r\n          </Badge>\r\n        )}\r\n\r\n        <div className=\"flex items-center justify-end gap-2\">\r\n          <Button onClick={handleSave} disabled={isSaving}>\r\n            {isSaving && <Loading className=\"size-4 animate-spin text-background\" />}\r\n            Save Changes\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsEnvironment;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,sBAAsB;IAC1B,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU;YAAC;YAAY;SAAU;QACjC,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;IAC3C;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,WAAW,QAAQ;YACnB,mBAAmB,QAAQ;QAC7B;IACF,GAAG;QAAC;KAAK;IAET,MAAM,EAAE,QAAQ,WAAW,EAAE,WAAW,QAAQ,EAAE,GAAG,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QAC/D,YAAY,IAAM,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc;QACtD,WAAW;YACT,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;YACb,mBAAmB;QACrB;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,oCAAoC;YAClD,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,MAAM,aAAa;QACjB;IACF;IAEA,MAAM,aAAa,YAAY;IAE/B,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAa,WAAU;kCAAU;;;;;;kCAGhD,6VAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,0BACC,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,mIAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;iDAGrB,6VAAC;4BACC,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC1C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sGACA;4BAEF,aAAa,CAAC,6DAA6D,CAAC;;;;;;;;;;;kCAIlF,6VAAC;wBAAE,WAAU;;4BAAqC;0CAChB,6VAAC;gCAAK,WAAU;0CAAU;;;;;;4BAAgB;;;;;;;;;;;;;0BAI9E,6VAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC,cAAc;;oBACrE,4BACC,6VAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;;0CACjC,6VAAC,yUAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;0CAC3B,6VAAC;0CAAK;;;;;;;;;;;;kCAIV,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU;;gCACpC,0BAAY,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAyC;;;;;;;;;;;;;;;;;;;;;;;;AAOrF;uCAEe", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/github.tsx"], "sourcesContent": ["import BaseEmptyState from \"@/components/base-empty-state\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardFooter } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport TaskStatus from \"@/features/global/task-status\";\r\nimport { errorToast, successToast } from \"@/features/global/toast\";\r\nimport { addGithubCollaborator, getGithubInfo, gitForcePull, revertGithubCommit } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { BrandGithubSolid, DangerTriangleSolid } from \"@mynaui/icons-react\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { ChevronDown, ChevronUp, Crown, GitBranch } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useState } from \"react\";\r\n\r\ntype Commit = {\r\n  hash: string;\r\n  message: string;\r\n  date: string;\r\n};\r\n\r\nconst SettingsGithub = () => {\r\n  const router = useRouter();\r\n  const queryClient = useQueryClient();\r\n  const [collaboratorUsername, setCollaboratorUsername] = useState(\"\");\r\n  const [expandedCommits, setExpandedCommits] = useState<Record<string, boolean>>({});\r\n  const [confirmRevert, setConfirmRevert] = useState<string | null>(null);\r\n  const [activeTab, setActiveTab] = useState(\"history\");\r\n  const [revertType, setRevertType] = useState(\"standard\");\r\n  const [confirmText, setConfirmText] = useState(\"\");\r\n  const [showForcePullConfirm, setShowForcePullConfirm] = useState(false);\r\n  const { user } = useAuth();\r\n  const { projectId } = useProject();\r\n\r\n  const {\r\n    data: githubInfo,\r\n    isLoading,\r\n    error,\r\n  } = useQuery({\r\n    queryKey: [\"githubInfo\", projectId],\r\n    queryFn: () => getGithubInfo(projectId),\r\n    staleTime: 1000 * 60,\r\n  });\r\n\r\n  const addCollaboratorMutation = useMutation({\r\n    mutationFn: (): Promise<boolean> =>\r\n      addGithubCollaborator(projectId, collaboratorUsername, githubInfo?.github_repo),\r\n    onSuccess: (data) => {\r\n      if (data) {\r\n        successToast(`Invited ${collaboratorUsername} as a collaborator`);\r\n      } else {\r\n        errorToast(\"Invitation Failed\", {\r\n          description: \"They may already have access or the username might be incorrect.\",\r\n        });\r\n      }\r\n      setCollaboratorUsername(\"\");\r\n    },\r\n    onError: () => {\r\n      errorToast(\"Invitation Failed\", {\r\n        description:\r\n          \"Unable to invite collaborator. They may already have access or the username might be incorrect.\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const revertCommitMutation = useMutation({\r\n    mutationFn: () => revertGithubCommit(projectId, confirmRevert!, revertType === \"full\"),\r\n    onSuccess: async () => {\r\n      successToast(\"Building...\", {\r\n        description: \"Running npm build after revert...\",\r\n      });\r\n\r\n      successToast(\"Success\", {\r\n        description: `Successfully ${\r\n          revertType === \"full\" ? \"reset\" : \"reverted\"\r\n        } commit and rebuilt project`,\r\n      });\r\n\r\n      router.refresh();\r\n\r\n      queryClient.invalidateQueries({ queryKey: [\"githubInfo\", projectId] });\r\n      setConfirmRevert(null);\r\n      setRevertType(\"standard\");\r\n      setConfirmText(\"\");\r\n    },\r\n    onError: (error: Error) => {\r\n      console.error(\"Revert error:\", error);\r\n      errorToast(\"Error\", {\r\n        description: `Failed to ${\r\n          revertType === \"full\" ? \"reset\" : \"revert\"\r\n        } commit${error.message ? `: ${error.message}` : \"\"}`,\r\n      });\r\n    },\r\n  });\r\n\r\n  const forcePullMutation = useMutation({\r\n    mutationFn: () => gitForcePull(projectId),\r\n    onSuccess: () => {\r\n      successToast(\"Success\", {\r\n        description: \"Successfully pulled latest changes from main branch\",\r\n      });\r\n      setShowForcePullConfirm(false);\r\n      queryClient.invalidateQueries({ queryKey: [\"githubInfo\", projectId] });\r\n    },\r\n    onError: (error: Error) => {\r\n      errorToast(\"Error\", {\r\n        description: error.message || \"Failed to pull changes\",\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleAddCollaborator = () => {\r\n    addCollaboratorMutation.mutate();\r\n  };\r\n\r\n  const handleRevertCommit = (commitHash: string) => {\r\n    setConfirmRevert(commitHash);\r\n  };\r\n\r\n  const confirmRevertCommit = () => {\r\n    if (revertType === \"full\" && confirmText !== \"sudo reset\") {\r\n      errorToast(\"Error\", {\r\n        description: \"Please type sudo reset to proceed with full reset.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    revertCommitMutation.mutate();\r\n  };\r\n\r\n  const toggleCommitExpansion = (commitHash: string) => {\r\n    setExpandedCommits((prev) => ({\r\n      ...prev,\r\n      [commitHash]: !prev[commitHash],\r\n    }));\r\n  };\r\n\r\n  const formatCommitMessage = (message: string) => {\r\n    const [title, ...description] = message.split(\"\\n\").filter((line) => line.trim() !== \"\");\r\n    return { title, description: description.join(\"\\n\") };\r\n  };\r\n\r\n  const isFirebaseSetupCommit = (message: string) => {\r\n    return message.toLowerCase().includes(\"firebase setup completed\");\r\n  };\r\n\r\n  const isCommitRevertible = (commit: Commit, commits: Commit[]) => {\r\n    const firebaseSetupCommit = commits.find((c) => isFirebaseSetupCommit(c.message));\r\n    if (!firebaseSetupCommit) return true;\r\n\r\n    return new Date(commit.date) >= new Date(firebaseSetupCommit.date);\r\n  };\r\n\r\n  if (user?.userFromDb?.plan === \"free-tier\") {\r\n    return (\r\n      <BaseEmptyState\r\n        icon={<Crown className=\"size-8 text-muted-foreground\" />}\r\n        title=\"GitHub is not available on the free tier\"\r\n        description=\"Please upgrade to a paid plan to access GitHub version history.\"\r\n        buttonText=\"Upgrade\"\r\n        href=\"/pricing\"\r\n        className=\"min-h-[400px]\"\r\n      />\r\n    );\r\n  }\r\n\r\n  console.log(\"error\", error);\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex w-full flex-col items-start justify-start gap-3\">\r\n        <TaskStatus variant=\"error\" className=\"flex items-center text-sm\">\r\n          Repository not initialized. Please initialize the repository first.\r\n        </TaskStatus>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"-mt-4 flex flex-col gap-4\">\r\n      <div className=\"space-y-6 py-4 pt-0 lg:mt-0\">\r\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"mx-0 rounded-lg border-0\">\r\n          <TabsList className=\"flex h-auto w-full items-center justify-start gap-2 py-3\">\r\n            <TabsTrigger\r\n              value=\"history\"\r\n              className=\"w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0\"\r\n            >\r\n              Version History\r\n            </TabsTrigger>\r\n            <TabsTrigger\r\n              value=\"github\"\r\n              className=\"w-fit focus-visible:outline-none focus-visible:ring-0 data-[state=active]:border-0\"\r\n            >\r\n              GitHub\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"history\" className=\"mt-4 focus:rounded-none lg:mt-0\">\r\n            {isLoading ? (\r\n              <div className=\"flex h-[500px] items-center justify-center gap-2\">\r\n                <Loading className=\"animate-spin\" /> Loading commit history...\r\n              </div>\r\n            ) : githubInfo?.commits ? (\r\n              <ScrollArea className=\"mx-0 h-[500px] rounded-md px-0\">\r\n                <div className=\"flex w-full flex-col gap-2\">\r\n                  {githubInfo.commits.map((commit: Commit) => {\r\n                    const { title, description } = formatCommitMessage(commit.message);\r\n                    const canRevert = isCommitRevertible(commit, githubInfo.commits);\r\n\r\n                    return (\r\n                      <>\r\n                        <div\r\n                          key={commit.hash}\r\n                          className=\"flex w-full items-center justify-between gap-1 rounded-xl bg-sidebar-ring/10 p-3 px-4 last:mb-0\"\r\n                        >\r\n                          <div className=\"flex w-full items-start justify-between\">\r\n                            <div className=\"min-w-0 flex-1\">\r\n                              <Typography.P className=\"mt-0 break-words text-sm text-primary\">\r\n                                {title}\r\n                              </Typography.P>\r\n                              <Typography.P className=\"mt-1 text-xs text-muted-foreground\">\r\n                                <span className=\"font-mono\">{commit.hash.substring(0, 7)}</span> •{\" \"}\r\n                                {new Date(commit.date).toLocaleString()}\r\n                              </Typography.P>\r\n                            </div>\r\n                            <Button\r\n                              size=\"sm\"\r\n                              onClick={() => handleRevertCommit(commit.hash)}\r\n                              className=\"ml-2\"\r\n                              disabled={!canRevert}\r\n                            >\r\n                              Revert\r\n                            </Button>\r\n                          </div>\r\n                          {description && (\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              size=\"sm\"\r\n                              onClick={() => toggleCommitExpansion(commit.hash)}\r\n                              className=\"mt-1 h-auto p-0 text-xs text-muted-foreground hover:text-foreground\"\r\n                            >\r\n                              {expandedCommits[commit.hash] ? (\r\n                                <>\r\n                                  <ChevronUp className=\"mr-1 h-3 w-3\" />\r\n                                  Hide details\r\n                                </>\r\n                              ) : (\r\n                                <>\r\n                                  <ChevronDown className=\"mr-1 h-3 w-3\" />\r\n                                  Show details\r\n                                </>\r\n                              )}\r\n                            </Button>\r\n                          )}\r\n                          {expandedCommits[commit.hash] && description && (\r\n                            <Typography.P className=\"mt-2 whitespace-pre-wrap text-xs text-muted-foreground\">\r\n                              {description}\r\n                            </Typography.P>\r\n                          )}\r\n                        </div>\r\n                      </>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </ScrollArea>\r\n            ) : (\r\n              <p>No commit history available</p>\r\n            )}\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"github\" className=\"mt-0 focus:rounded-none\">\r\n            {isLoading ? (\r\n              <div className=\"flex h-[200px] items-center justify-center\">\r\n                <Loading className=\"animate-spin text-primary\" />\r\n              </div>\r\n            ) : githubInfo && githubInfo.github_repo ? (\r\n              <div className=\"flex w-full flex-col gap-4\">\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between gap-1 space-y-1 py-3\">\r\n                    <div className=\"flex flex-col gap-0\">\r\n                      <Typography.H5>Repository</Typography.H5>\r\n                      <Typography.P>Visit your codebase on GitHub.</Typography.P>\r\n                    </div>\r\n\r\n                    <Button size=\"sm\" className=\"h-8\" asChild>\r\n                      <Link href={githubInfo.github_repo} target=\"_blank\" rel=\"noopener noreferrer\">\r\n                        <BrandGithubSolid className=\"text-muted\" />\r\n                        Visit Repository\r\n                      </Link>\r\n                    </Button>\r\n                  </div>\r\n\r\n                  <div className=\"flex w-full items-center justify-between gap-1 py-3\">\r\n                    <div className=\"flex w-full flex-col items-start justify-between space-y-3 sm:space-y-0\">\r\n                      <div className=\"flex items-center gap-2 text-base text-primary\">\r\n                        <GitBranch className=\"size-4\" />\r\n                        <Typography.H5>Force Pull Main Branch</Typography.H5>\r\n                      </div>\r\n\r\n                      <Typography.P>\r\n                        Force pull latest changes from main branch. Local changes will be lost.\r\n                      </Typography.P>\r\n                    </div>\r\n                    <Button size=\"sm\" onClick={() => setShowForcePullConfirm(true)}>\r\n                      <GitBranch className=\"size-4\" />\r\n                      Force Pull Main\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex w-full flex-col items-center justify-between gap-1 space-y-2 py-3\">\r\n                  <div className=\"flex w-full flex-col gap-0.5\">\r\n                    <Typography.H5>Add Collaborator</Typography.H5>\r\n                    <Typography.P className=\"text-sm text-muted-foreground\">\r\n                      This is a private repository. You must invite collaborators before they can\r\n                      access the project.\r\n                    </Typography.P>\r\n                  </div>\r\n                  <div className=\"flex w-full items-center gap-2\">\r\n                    <Input\r\n                      id=\"collaborator-username\"\r\n                      value={collaboratorUsername}\r\n                      onChange={(e) => setCollaboratorUsername(e.target.value)}\r\n                      placeholder=\"GitHub username\"\r\n                      className=\"w-full\"\r\n                      disabled={addCollaboratorMutation.isPending}\r\n                    />\r\n                    <Button\r\n                      onClick={handleAddCollaborator}\r\n                      className=\"flex h-8 w-fit items-center gap-2\"\r\n                      disabled={addCollaboratorMutation.isPending || !collaboratorUsername.trim()}\r\n                    >\r\n                      {addCollaboratorMutation.isPending ? (\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Loading className=\"size-4 animate-spin text-background\" />\r\n                          Inviting...\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"flex items-center gap-2\">Invite</div>\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex h-[400px] flex-col items-center justify-center gap-6\">\r\n                <div className=\"flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2\">\r\n                  <DangerTriangleSolid className=\"size-8 text-muted-foreground\" />\r\n                </div>\r\n                <div className=\"flex flex-col items-center justify-center gap-2\">\r\n                  <Typography.H5>Repository not initialized</Typography.H5>\r\n                  <Typography.P className=\"mt-0 text-muted-foreground\">\r\n                    Please initialize the repository first.\r\n                  </Typography.P>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n\r\n        <AlertDialog\r\n          open={!!confirmRevert}\r\n          onOpenChange={(open) => !open && setConfirmRevert(null)}\r\n        >\r\n          <AlertDialogContent className=\"lg:max-w-xl\">\r\n            <AlertDialogHeader>\r\n              <AlertDialogTitle>Confirm Revert</AlertDialogTitle>\r\n              <AlertDialogDescription>\r\n                Choose how you want to revert to this commit:\r\n              </AlertDialogDescription>\r\n            </AlertDialogHeader>\r\n            <div className=\"\">\r\n              <RadioGroup\r\n                value={revertType}\r\n                onValueChange={(value) => setRevertType(value as \"standard\" | \"full\")}\r\n                className=\"space-y-4\"\r\n              >\r\n                <Card\r\n                  className={cn(\r\n                    \"border-none bg-transparent shadow-none\",\r\n                    revertType === \"standard\" && \"ring-2 ring-primary/20\",\r\n                  )}\r\n                >\r\n                  <CardContent className=\"pt-6\">\r\n                    <div className=\"flex cursor-pointer items-center space-x-2\">\r\n                      <RadioGroupItem value=\"standard\" id=\"standard\" />\r\n                      <Label htmlFor=\"standard\" className=\"cursor-pointer text-lg font-semibold\">\r\n                        Standard Revert\r\n                      </Label>\r\n                    </div>\r\n                    <Typography.P className=\"mt-2 pl-6 text-sm text-muted-foreground\">\r\n                      This option will:\r\n                      <ul className=\"mt-1 list-inside list-disc space-y-1\">\r\n                        <li>Create a new commit that undoes the changes</li>\r\n                        <li>Keep the entire version history</li>\r\n                        <li>Can be undone by reverting the revert commit</li>\r\n                      </ul>\r\n                    </Typography.P>\r\n                    <TaskStatus variant=\"warning\" className=\"mt-3 flex items-center text-sm\">\r\n                      This may lead to a confused project context as potentially conflicting commits\r\n                      will remain.\r\n                    </TaskStatus>\r\n                  </CardContent>\r\n                </Card>\r\n                <Card\r\n                  border={false}\r\n                  className={cn(\r\n                    \"rounded-xl border border-red-900/50 bg-transparent hover:border-red-900\",\r\n                    revertType === \"full\" && \"ring-2 ring-red-900/30\",\r\n                  )}\r\n                >\r\n                  <CardContent className=\"pt-6\">\r\n                    <div className=\"flex cursor-pointer items-center space-x-2\">\r\n                      <RadioGroupItem value=\"full\" id=\"full\" />\r\n                      <Label htmlFor=\"full\" className=\"cursor-pointer text-lg font-semibold\">\r\n                        Full Reset (Caution)\r\n                      </Label>\r\n                    </div>\r\n                    <Typography.P className=\"mt-2 pl-6 text-sm text-muted-foreground\">\r\n                      This option will:\r\n                      <ul className=\"mt-1 list-inside list-disc space-y-1\">\r\n                        <li>Clear all subsequent version history</li>\r\n                        <li>Reset the project entirely to the selected commit state</li>\r\n                        <li>Remove all commits after the selected one</li>\r\n                      </ul>\r\n                    </Typography.P>\r\n\r\n                    <TaskStatus variant=\"error\" className=\"mt-3 flex items-center text-sm\">\r\n                      This action cannot be undone. You&apos;ll lose all work after this commit.\r\n                    </TaskStatus>\r\n                  </CardContent>\r\n                  {revertType === \"full\" && (\r\n                    <CardFooter className=\"flex flex-col items-start gap-2 bg-transparent pt-4\">\r\n                      <p className=\"mt-0 text-sm font-normal text-primary/80\">\r\n                        Type <p className=\"inline font-bold text-primary\">sudo reset</p> to proceed:\r\n                      </p>\r\n                      <Input\r\n                        id=\"confirmText\"\r\n                        value={confirmText}\r\n                        onChange={(e) => setConfirmText(e.target.value)}\r\n                        placeholder=\"Type sudo reset here\"\r\n                        className=\"w-full border-red-900/50\"\r\n                      />\r\n                    </CardFooter>\r\n                  )}\r\n                </Card>\r\n              </RadioGroup>\r\n            </div>\r\n            <AlertDialogFooter className=\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\">\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"w-full sm:w-fit\"\r\n                onClick={() => setConfirmRevert(null)}\r\n                disabled={revertCommitMutation.isPending}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                className={cn(\"w-full sm:w-fit\")}\r\n                variant={revertType === \"full\" ? \"destructive\" : \"default\"}\r\n                onClick={confirmRevertCommit}\r\n                disabled={\r\n                  revertCommitMutation.isPending ||\r\n                  (revertType === \"full\" && confirmText !== \"sudo reset\")\r\n                }\r\n              >\r\n                {revertCommitMutation.isPending ? (\r\n                  <>\r\n                    <Loading className=\"size-4 animate-spin text-background\" />\r\n                    Reverting...\r\n                  </>\r\n                ) : revertType === \"full\" ? (\r\n                  \"Confirm Full Reset\"\r\n                ) : (\r\n                  \"Confirm Standard Revert\"\r\n                )}\r\n              </Button>\r\n            </AlertDialogFooter>\r\n          </AlertDialogContent>\r\n        </AlertDialog>\r\n\r\n        <AlertDialog open={showForcePullConfirm} onOpenChange={setShowForcePullConfirm}>\r\n          <AlertDialogContent className=\"bg-background px-5 py-4 text-primary\">\r\n            <AlertDialogHeader className=\"flex flex-col gap-2\">\r\n              <AlertDialogTitle>Force Pull from Main Branch</AlertDialogTitle>\r\n              <AlertDialogDescription className=\"mt-6 text-sm\">\r\n                This action will:\r\n                <ul className=\"mt-2 list-inside list-disc space-y-1 text-sm\">\r\n                  <li>Fetch the latest changes from remote</li>\r\n                  <li>Reset your local branch to match remote main</li>\r\n                  <li>Discard all local changes</li>\r\n                  <li>This action cannot be undone</li>\r\n                </ul>\r\n              </AlertDialogDescription>\r\n            </AlertDialogHeader>\r\n            <AlertDialogFooter className=\"flex items-center gap-2\">\r\n              <AlertDialogCancel onClick={() => setShowForcePullConfirm(false)}>\r\n                Cancel\r\n              </AlertDialogCancel>\r\n              <AlertDialogAction className=\"w-fit\" onClick={() => forcePullMutation.mutate()}>\r\n                Force Pull\r\n              </AlertDialogAction>\r\n            </AlertDialogFooter>\r\n          </AlertDialogContent>\r\n        </AlertDialog>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsGithub;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,MAAM,iBAAiB;IACrB,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACjF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAE/B,MAAM,EACJ,MAAM,UAAU,EAChB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU;YAAC;YAAc;SAAU;QACnC,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE;QAC7B,WAAW,OAAO;IACpB;IAEA,MAAM,0BAA0B,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,YAAY,IACV,CAAA,GAAA,iHAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW,sBAAsB,YAAY;QACrE,WAAW,CAAC;YACV,IAAI,MAAM;gBACR,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAQ,EAAE,qBAAqB,kBAAkB,CAAC;YAClE,OAAO;gBACL,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB;oBAC9B,aAAa;gBACf;YACF;YACA,wBAAwB;QAC1B;QACA,SAAS;YACP,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB;gBAC9B,aACE;YACJ;QACF;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACvC,YAAY,IAAM,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,eAAgB,eAAe;QAC/E,WAAW;YACT,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;gBAC1B,aAAa;YACf;YAEA,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,WAAW;gBACtB,aAAa,CAAC,aAAa,EACzB,eAAe,SAAS,UAAU,WACnC,2BAA2B,CAAC;YAC/B;YAEA,OAAO,OAAO;YAEd,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAc;iBAAU;YAAC;YACpE,iBAAiB;YACjB,cAAc;YACd,eAAe;QACjB;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;gBAClB,aAAa,CAAC,UAAU,EACtB,eAAe,SAAS,UAAU,SACnC,OAAO,EAAE,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE,GAAG,IAAI;YACvD;QACF;IACF;IAEA,MAAM,oBAAoB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACpC,YAAY,IAAM,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE;QAC/B,WAAW;YACT,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,WAAW;gBACtB,aAAa;YACf;YACA,wBAAwB;YACxB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAc;iBAAU;YAAC;QACtE;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;gBAClB,aAAa,MAAM,OAAO,IAAI;YAChC;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,wBAAwB,MAAM;IAChC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;IACnB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,eAAe,UAAU,gBAAgB,cAAc;YACzD,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;gBAClB,aAAa;YACf;YACA;QACF;QAEA,qBAAqB,MAAM;IAC7B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,CAAC,OAAS,CAAC;gBAC5B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,WAAW;YACjC,CAAC;IACH;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,CAAC,OAAO,GAAG,YAAY,GAAG,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,OAAO;QACrF,OAAO;YAAE;YAAO,aAAa,YAAY,IAAI,CAAC;QAAM;IACtD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,QAAQ,WAAW,GAAG,QAAQ,CAAC;IACxC;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,sBAAsB,QAAQ,IAAI,CAAC,CAAC,IAAM,sBAAsB,EAAE,OAAO;QAC/E,IAAI,CAAC,qBAAqB,OAAO;QAEjC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,oBAAoB,IAAI;IACnE;IAEA,IAAI,MAAM,YAAY,SAAS,aAAa;QAC1C,qBACE,6VAAC,4IAAA,CAAA,UAAc;YACb,oBAAM,6VAAC,wRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAM;YACN,aAAY;YACZ,YAAW;YACX,MAAK;YACL,WAAU;;;;;;IAGhB;IAEA,QAAQ,GAAG,CAAC,SAAS;IAErB,IAAI,OAAO;QACT,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC,4IAAA,CAAA,UAAU;gBAAC,SAAQ;gBAAQ,WAAU;0BAA4B;;;;;;;;;;;IAKxE;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,6VAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6VAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;8CACX;;;;;;8CAGD,6VAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;8CACX;;;;;;;;;;;;sCAKH,6VAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,0BACC,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,mIAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;uCAEpC,YAAY,wBACd,6VAAC,0IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6VAAC;oCAAI,WAAU;8CACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC;wCACvB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,oBAAoB,OAAO,OAAO;wCACjE,MAAM,YAAY,mBAAmB,QAAQ,WAAW,OAAO;wCAE/D,qBACE;sDACE,cAAA,6VAAC;gDAEC,WAAU;;kEAEV,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wEAAC,WAAU;kFACrB;;;;;;kFAEH,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wEAAC,WAAU;;0FACtB,6VAAC;gFAAK,WAAU;0FAAa,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;;;;;;4EAAU;4EAAG;4EAClE,IAAI,KAAK,OAAO,IAAI,EAAE,cAAc;;;;;;;;;;;;;0EAGzC,6VAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,IAAM,mBAAmB,OAAO,IAAI;gEAC7C,WAAU;gEACV,UAAU,CAAC;0EACZ;;;;;;;;;;;;oDAIF,6BACC,6VAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,sBAAsB,OAAO,IAAI;wDAChD,WAAU;kEAET,eAAe,CAAC,OAAO,IAAI,CAAC,iBAC3B;;8EACE,6VAAC,oSAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAiB;;yFAIxC;;8EACE,6VAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;oDAM/C,eAAe,CAAC,OAAO,IAAI,CAAC,IAAI,6BAC/B,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wDAAC,WAAU;kEACrB;;;;;;;+CA5CA,OAAO,IAAI;;;;;;oCAkDxB;;;;;;;;;;qDAIJ,6VAAC;0CAAE;;;;;;;;;;;sCAIP,6VAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACnC,0BACC,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;uCAEnB,cAAc,WAAW,WAAW,iBACtC,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;0EAAC;;;;;;0EACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;0EAAC;;;;;;;;;;;;kEAGhB,6VAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,WAAU;wDAAM,OAAO;kEACvC,cAAA,6VAAC,2QAAA,CAAA,UAAI;4DAAC,MAAM,WAAW,WAAW;4DAAE,QAAO;4DAAS,KAAI;;8EACtD,6VAAC,2UAAA,CAAA,mBAAgB;oEAAC,WAAU;;;;;;gEAAe;;;;;;;;;;;;;;;;;;0DAMjD,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,oSAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;kFACrB,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;kFAAC;;;;;;;;;;;;0EAGjB,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;0EAAC;;;;;;;;;;;;kEAIhB,6VAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAS,IAAM,wBAAwB;;0EACvD,6VAAC,oSAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAW;;;;;;;;;;;;;;;;;;;kDAMtC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;kEAAC;;;;;;kEACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wDAAC,WAAU;kEAAgC;;;;;;;;;;;;0DAK1D,6VAAC;gDAAI,WAAU;;kEACb,6VAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;wDACvD,aAAY;wDACZ,WAAU;wDACV,UAAU,wBAAwB,SAAS;;;;;;kEAE7C,6VAAC,kIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,WAAU;wDACV,UAAU,wBAAwB,SAAS,IAAI,CAAC,qBAAqB,IAAI;kEAExE,wBAAwB,SAAS,iBAChC,6VAAC;4DAAI,WAAU;;8EACb,6VAAC,mIAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAwC;;;;;;iFAI7D,6VAAC;4DAAI,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAOnD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC,iVAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;0DAAC;;;;;;0DACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gDAAC,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS/D,6VAAC,2IAAA,CAAA,cAAW;oBACV,MAAM,CAAC,CAAC;oBACR,cAAc,CAAC,OAAS,CAAC,QAAQ,iBAAiB;8BAElD,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;wBAAC,WAAU;;0CAC5B,6VAAC,2IAAA,CAAA,oBAAiB;;kDAChB,6VAAC,2IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,6VAAC,2IAAA,CAAA,yBAAsB;kDAAC;;;;;;;;;;;;0CAI1B,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,0IAAA,CAAA,aAAU;oCACT,OAAO;oCACP,eAAe,CAAC,QAAU,cAAc;oCACxC,WAAU;;sDAEV,6VAAC,gIAAA,CAAA,OAAI;4CACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,eAAe,cAAc;sDAG/B,cAAA,6VAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,0IAAA,CAAA,iBAAc;gEAAC,OAAM;gEAAW,IAAG;;;;;;0EACpC,6VAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAW,WAAU;0EAAuC;;;;;;;;;;;;kEAI7E,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wDAAC,WAAU;;4DAA0C;0EAEhE,6VAAC;gEAAG,WAAU;;kFACZ,6VAAC;kFAAG;;;;;;kFACJ,6VAAC;kFAAG;;;;;;kFACJ,6VAAC;kFAAG;;;;;;;;;;;;;;;;;;kEAGR,6VAAC,4IAAA,CAAA,UAAU;wDAAC,SAAQ;wDAAU,WAAU;kEAAiC;;;;;;;;;;;;;;;;;sDAM7E,6VAAC,gIAAA,CAAA,OAAI;4CACH,QAAQ;4CACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2EACA,eAAe,UAAU;;8DAG3B,6VAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6VAAC;4DAAI,WAAU;;8EACb,6VAAC,0IAAA,CAAA,iBAAc;oEAAC,OAAM;oEAAO,IAAG;;;;;;8EAChC,6VAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAO,WAAU;8EAAuC;;;;;;;;;;;;sEAIzE,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;4DAAC,WAAU;;gEAA0C;8EAEhE,6VAAC;oEAAG,WAAU;;sFACZ,6VAAC;sFAAG;;;;;;sFACJ,6VAAC;sFAAG;;;;;;sFACJ,6VAAC;sFAAG;;;;;;;;;;;;;;;;;;sEAIR,6VAAC,4IAAA,CAAA,UAAU;4DAAC,SAAQ;4DAAQ,WAAU;sEAAiC;;;;;;;;;;;;gDAIxE,eAAe,wBACd,6VAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,6VAAC;4DAAE,WAAU;;gEAA2C;8EACjD,6VAAC;oEAAE,WAAU;8EAAgC;;;;;;gEAAc;;;;;;;sEAElE,6VAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOtB,6VAAC,2IAAA,CAAA,oBAAiB;gCAAC,WAAU;;kDAC3B,6VAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,iBAAiB;wCAChC,UAAU,qBAAqB,SAAS;kDACzC;;;;;;kDAGD,6VAAC,kIAAA,CAAA,SAAM;wCACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;wCACd,SAAS,eAAe,SAAS,gBAAgB;wCACjD,SAAS;wCACT,UACE,qBAAqB,SAAS,IAC7B,eAAe,UAAU,gBAAgB;kDAG3C,qBAAqB,SAAS,iBAC7B;;8DACE,6VAAC,mIAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAwC;;2DAG3D,eAAe,SACjB,uBAEA;;;;;;;;;;;;;;;;;;;;;;;8BAOV,6VAAC,2IAAA,CAAA,cAAW;oBAAC,MAAM;oBAAsB,cAAc;8BACrD,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;wBAAC,WAAU;;0CAC5B,6VAAC,2IAAA,CAAA,oBAAiB;gCAAC,WAAU;;kDAC3B,6VAAC,2IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,6VAAC,2IAAA,CAAA,yBAAsB;wCAAC,WAAU;;4CAAe;0DAE/C,6VAAC;gDAAG,WAAU;;kEACZ,6VAAC;kEAAG;;;;;;kEACJ,6VAAC;kEAAG;;;;;;kEACJ,6VAAC;kEAAG;;;;;;kEACJ,6VAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAIV,6VAAC,2IAAA,CAAA,oBAAiB;gCAAC,WAAU;;kDAC3B,6VAAC,2IAAA,CAAA,oBAAiB;wCAAC,SAAS,IAAM,wBAAwB;kDAAQ;;;;;;kDAGlE,6VAAC,2IAAA,CAAA,oBAAiB;wCAAC,WAAU;wCAAQ,SAAS,IAAM,kBAAkB,MAAM;kDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F;uCAEe", "debugId": null}}, {"offset": {"line": 2498, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/terminal/index.ts"], "sourcesContent": ["import { executeCommand } from \"@/lib/api\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { errorToast, loadingToast } from \"../global/toast\";\r\n\r\nexport const useRestartServer = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async (projectId: string) => {\r\n      if (!projectId) {\r\n        throw new Error(\"Project ID is required\");\r\n      }\r\n\r\n      const restartPromise = executeCommand(projectId, \"pm2 restart all\");\r\n\r\n      loadingToast(\"Restarting server...\", restartPromise);\r\n\r\n      return restartPromise;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries();\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error restarting server:\", error);\r\n      errorToast(\"Failed to restart the server. Please try again.\");\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,OAAO;YACjB,IAAI,CAAC,WAAW;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,iBAAiB,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YAEjD,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,wBAAwB;YAErC,OAAO;QACT;QACA,WAAW;YACT,YAAY,iBAAiB;QAC/B;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;AACF", "debugId": null}}, {"offset": {"line": 2534, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/modal/delete-project-modal.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON>dalDescription,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  ModalTitle,\r\n} from \"@/components/ui/modal\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { UseMutationResult } from \"@tanstack/react-query\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { ProjectForm } from \"../settings/project\";\r\n\r\ntype Props = {\r\n  open: boolean;\r\n  setShowModal: (open: boolean) => void;\r\n  onDelete: (projectId: string) => void;\r\n  form: UseFormReturn<ProjectForm>;\r\n  mutate: UseMutationResult<void, Error, string>;\r\n};\r\n\r\nconst DeleteProjectModal = ({ open, setShowModal, onDelete, form, mutate }: Props) => {\r\n  return (\r\n    <Modal open={open} onOpenChange={setShowModal}>\r\n      <ModalContent>\r\n        <ModalHeader>\r\n          <ModalTitle>Delete Project</ModalTitle>\r\n          <ModalDescription>\r\n            This action cannot be undone. This will permanently delete your project and all of its\r\n            data.\r\n          </ModalDescription>\r\n        </ModalHeader>\r\n\r\n        <div className=\"p-6 py-2\">\r\n          <ul className=\"list-disc px-6\">\r\n            <li>\r\n              <Typography.P className=\"text-sm\">\r\n                All threads and messages will be deleted.\r\n              </Typography.P>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n\r\n        <ModalFooter className=\"pt-6\">\r\n          <Button\r\n            onClick={() => {\r\n              setShowModal(false);\r\n            }}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={() => {\r\n              const projectId = form.getValues(\"project_id\");\r\n              if (projectId) onDelete(projectId);\r\n            }}\r\n            disabled={mutate.isPending}\r\n            className=\"bg-destructive text-destructive-foreground hover:bg-destructive/90\"\r\n          >\r\n            {mutate.isPending ? \"Deleting...\" : \"Delete Project\"}\r\n          </Button>\r\n        </ModalFooter>\r\n      </ModalContent>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default DeleteProjectModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;;;;;AAaA,MAAM,qBAAqB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAS;IAC/E,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;kBAC/B,cAAA,6VAAC,iIAAA,CAAA,eAAY;;8BACX,6VAAC,iIAAA,CAAA,cAAW;;sCACV,6VAAC,iIAAA,CAAA,aAAU;sCAAC;;;;;;sCACZ,6VAAC,iIAAA,CAAA,mBAAgB;sCAAC;;;;;;;;;;;;8BAMpB,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;wBAAG,WAAU;kCACZ,cAAA,6VAAC;sCACC,cAAA,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gCAAC,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;8BAOxC,6VAAC,iIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;gCACP,aAAa;4BACf;sCACD;;;;;;sCAGD,6VAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;gCACP,MAAM,YAAY,KAAK,SAAS,CAAC;gCACjC,IAAI,WAAW,SAAS;4BAC1B;4BACA,UAAU,OAAO,SAAS;4BAC1B,WAAU;sCAET,OAAO,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMhD;uCAEe", "debugId": null}}, {"offset": {"line": 2652, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/modal/rename-project-modal.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>eader, ModalTitle } from \"@/components/ui/modal\";\r\nimport { UseMutationResult } from \"@tanstack/react-query\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { ProjectForm } from \"../settings/project\";\r\n\r\ntype Props = {\r\n  open: boolean;\r\n  setShowModal: (showModal: boolean) => void;\r\n  projectForm: UseFormReturn<ProjectForm>;\r\n  onUpdate: (data: ProjectForm) => void;\r\n  updateProjectMutation: UseMutationResult<\r\n    void,\r\n    Error,\r\n    { projectId: string; updateData: Record<string, string> }\r\n  >;\r\n};\r\n\r\nconst RenameProjectModal = ({\r\n  open,\r\n  setShowModal,\r\n  projectForm,\r\n  onUpdate,\r\n  updateProjectMutation,\r\n}: Props) => {\r\n  return (\r\n    <Modal open={open} onOpenChange={setShowModal}>\r\n      <ModalContent>\r\n        <ModalHeader>\r\n          <ModalTitle>Rename Project</ModalTitle>\r\n        </ModalHeader>\r\n        <form onSubmit={projectForm.handleSubmit(onUpdate)}>\r\n          <div className=\"p-6\">\r\n            <FormField\r\n              control={projectForm.control}\r\n              name=\"name\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Project Name</FormLabel>\r\n                  <FormControl>\r\n                    <Input placeholder=\"Enter project name\" className=\"w-full\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          </div>\r\n          <ModalFooter>\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"w-full md:w-fit\"\r\n              disabled={updateProjectMutation.isPending}\r\n            >\r\n              {updateProjectMutation.isPending ? (\r\n                <>\r\n                  <Loading className=\"size-4 text-background\" />\r\n                  Saving...\r\n                </>\r\n              ) : (\r\n                \"Save Changes\"\r\n              )}\r\n            </Button>\r\n          </ModalFooter>\r\n        </form>\r\n      </ModalContent>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default RenameProjectModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAiBA,MAAM,qBAAqB,CAAC,EAC1B,IAAI,EACJ,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,qBAAqB,EACf;IACN,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;kBAC/B,cAAA,6VAAC,iIAAA,CAAA,eAAY;;8BACX,6VAAC,iIAAA,CAAA,cAAW;8BACV,cAAA,6VAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;;;;;;8BAEd,6VAAC;oBAAK,UAAU,YAAY,YAAY,CAAC;;sCACvC,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,YAAY,OAAO;gCAC5B,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0DACP,6VAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oDAAC,aAAY;oDAAqB,WAAU;oDAAU,GAAG,KAAK;;;;;;;;;;;0DAEtE,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;sCAKpB,6VAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,6VAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU,sBAAsB,SAAS;0CAExC,sBAAsB,SAAS,iBAC9B;;sDACE,6VAAC,mIAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA2B;;mDAIhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;uCAEe", "debugId": null}}, {"offset": {"line": 2791, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/project.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { Form, FormField } from \"@/components/ui/form\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { useRestartServer } from \"@/features/terminal\";\r\nimport { deleteProject, updateProject } from \"@/lib/api\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport React, { useCallback } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { errorToast, loadingToast, successToast } from \"../../global/toast\";\r\nimport DeleteProjectModal from \"../modal/delete-project-modal\";\r\nimport RenameProjectModal from \"../modal/rename-project-modal\";\r\n\r\nconst projectFormSchema = z.object({\r\n  project_id: z.string(),\r\n  name: z.string().min(1, \"Project name is required\"),\r\n  isPublic: z.boolean(),\r\n});\r\n\r\nexport type ProjectForm = z.infer<typeof projectFormSchema>;\r\n\r\nconst SettingsProject = ({ setShowModal }: { setShowModal: (showModal: boolean) => void }) => {\r\n  const queryClient = useQueryClient();\r\n  const router = useRouter();\r\n  const [showRenameModal, setShowRenameModal] = React.useState(false);\r\n  const [showDeleteModal, setShowDeleteModal] = React.useState(false);\r\n\r\n  const { project, refetch } = useProject();\r\n  const { mutate: restartServer } = useRestartServer();\r\n\r\n  const projectForm = useForm<ProjectForm>({\r\n    resolver: zodResolver(projectFormSchema),\r\n    defaultValues: {\r\n      project_id: project?.project_id || \"\",\r\n      name: project?.name || \"\",\r\n      isPublic: project?.isPublic || false,\r\n    },\r\n  });\r\n\r\n  React.useEffect(() => {\r\n    if (project) {\r\n      projectForm.setValue(\"project_id\", project.project_id);\r\n      projectForm.setValue(\"name\", project.name);\r\n      projectForm.setValue(\"isPublic\", project.isPublic);\r\n    }\r\n  }, [project, projectForm]);\r\n\r\n  const updateProjectMutation = useMutation({\r\n    mutationFn: ({\r\n      projectId,\r\n      updateData,\r\n    }: {\r\n      projectId: string;\r\n      updateData: Record<string, string>;\r\n    }) => updateProject(projectId, updateData),\r\n    onSuccess: () => {\r\n      successToast(\"Project updated successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"get-project\", project?.project_id] });\r\n      setShowRenameModal(false);\r\n\r\n      if (refetch) {\r\n        refetch();\r\n      }\r\n    },\r\n    onError: () => {\r\n      errorToast(\"Error updating project\");\r\n    },\r\n  });\r\n\r\n  const deleteProjectMutation = useMutation({\r\n    mutationFn: (projectId: string) => deleteProject(projectId),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"get-project\", project?.project_id] });\r\n      router.push(\"/app\");\r\n    },\r\n    onError: () => {\r\n      errorToast(\"Error deleting project\");\r\n    },\r\n  });\r\n\r\n  const handleUpdateProject = async (data: ProjectForm) => {\r\n    const effectiveProjectId = data.project_id || project?.project_id;\r\n\r\n    if (!effectiveProjectId) {\r\n      errorToast(\"Project ID is missing\");\r\n      return;\r\n    }\r\n\r\n    await updateProjectMutation.mutateAsync({\r\n      projectId: effectiveProjectId,\r\n      updateData: {\r\n        name: data.name,\r\n        isPublic: data.isPublic.toString(),\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleDeleteProject = async (projectId: string) => {\r\n    if (!project) return;\r\n\r\n    loadingToast(\"Deleting project...\", deleteProjectMutation.mutateAsync(projectId)).then(() => {\r\n      projectForm.reset();\r\n      setShowDeleteModal(false);\r\n      router.push(\"/app\");\r\n    });\r\n  };\r\n\r\n  const handleRestartServer = useCallback(() => {\r\n    restartServer(project?.project_id || \"\");\r\n    setShowModal(false);\r\n  }, [project?.project_id, restartServer, setShowModal]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <Form {...projectForm}>\r\n        <div className=\"space-y-5\">\r\n          <FormField\r\n            control={projectForm.control}\r\n            name=\"isPublic\"\r\n            render={({ field }) => (\r\n              <div className=\"flex items-center justify-between gap-1 space-y-1\">\r\n                <div className=\"flex flex-col gap-0\">\r\n                  <Typography.H5>Public Project</Typography.H5>\r\n                  <Typography.P>Allow others to view and clone your project</Typography.P>\r\n                </div>\r\n\r\n                <Switch\r\n                  checked={field.value}\r\n                  onCheckedChange={(value) => {\r\n                    field.onChange(value);\r\n                    if (project?.project_id) {\r\n                      updateProjectMutation.mutate({\r\n                        projectId: project.project_id,\r\n                        updateData: {\r\n                          isPublic: value.toString(),\r\n                        },\r\n                      });\r\n                    }\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n          />\r\n\r\n          <div className=\"flex items-center justify-between gap-1 space-y-1\">\r\n            <div className=\"flex flex-col gap-0\">\r\n              <Typography.H5>Rename Project</Typography.H5>\r\n              <Typography.P>Enter a new name for your project.</Typography.P>\r\n            </div>\r\n\r\n            <Button size=\"sm\" className=\"h-8\" onClick={() => setShowRenameModal(true)}>\r\n              Rename\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between gap-1 space-y-1\">\r\n            <div className=\"flex flex-col gap-0\">\r\n              <Typography.H5>Restart Server</Typography.H5>\r\n              <Typography.P>Restart the server for your project.</Typography.P>\r\n            </div>\r\n\r\n            <Button size=\"sm\" className=\"h-8\" onClick={handleRestartServer}>\r\n              Restart Server\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between gap-1 space-y-1\">\r\n            <div className=\"flex flex-col gap-0\">\r\n              <Typography.H5>Delete Project</Typography.H5>\r\n              <Typography.P>This will permanently delete your project.</Typography.P>\r\n            </div>\r\n\r\n            <Button\r\n              variant=\"destructive\"\r\n              size=\"sm\"\r\n              className=\"h-8\"\r\n              onClick={() => setShowDeleteModal(true)}\r\n            >\r\n              Delete this Project\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <RenameProjectModal\r\n          open={showRenameModal}\r\n          setShowModal={setShowRenameModal}\r\n          projectForm={projectForm}\r\n          onUpdate={handleUpdateProject}\r\n          updateProjectMutation={updateProjectMutation}\r\n        />\r\n\r\n        <DeleteProjectModal\r\n          open={showDeleteModal}\r\n          setShowModal={setShowDeleteModal}\r\n          onDelete={handleDeleteProject}\r\n          form={projectForm}\r\n          mutate={deleteProjectMutation}\r\n        />\r\n      </Form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsProject;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,YAAY,mOAAA,CAAA,IAAC,CAAC,MAAM;IACpB,MAAM,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,UAAU,mOAAA,CAAA,IAAC,CAAC,OAAO;AACrB;AAIA,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAkD;IACvF,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,oTAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,oTAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE7D,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IACtC,MAAM,EAAE,QAAQ,aAAa,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD;IAEjD,MAAM,cAAc,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAe;QACvC,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,YAAY,SAAS,cAAc;YACnC,MAAM,SAAS,QAAQ;YACvB,UAAU,SAAS,YAAY;QACjC;IACF;IAEA,oTAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,SAAS;YACX,YAAY,QAAQ,CAAC,cAAc,QAAQ,UAAU;YACrD,YAAY,QAAQ,CAAC,QAAQ,QAAQ,IAAI;YACzC,YAAY,QAAQ,CAAC,YAAY,QAAQ,QAAQ;QACnD;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,MAAM,wBAAwB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,CAAC,EACX,SAAS,EACT,UAAU,EAIX,GAAK,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;QAC/B,WAAW;YACT,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAe,SAAS;iBAAW;YAAC;YAC/E,mBAAmB;YAEnB,IAAI,SAAS;gBACX;YACF;QACF;QACA,SAAS;YACP,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,MAAM,wBAAwB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,CAAC,YAAsB,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE;QACjD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAe,SAAS;iBAAW;YAAC;YAC/E,OAAO,IAAI,CAAC;QACd;QACA,SAAS;YACP,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,MAAM,qBAAqB,KAAK,UAAU,IAAI,SAAS;QAEvD,IAAI,CAAC,oBAAoB;YACvB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX;QACF;QAEA,MAAM,sBAAsB,WAAW,CAAC;YACtC,WAAW;YACX,YAAY;gBACV,MAAM,KAAK,IAAI;gBACf,UAAU,KAAK,QAAQ,CAAC,QAAQ;YAClC;QACF;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,SAAS;QAEd,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,uBAAuB,sBAAsB,WAAW,CAAC,YAAY,IAAI,CAAC;YACrF,YAAY,KAAK;YACjB,mBAAmB;YACnB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACtC,cAAc,SAAS,cAAc;QACrC,aAAa;IACf,GAAG;QAAC,SAAS;QAAY;QAAe;KAAa;IAErD,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,gIAAA,CAAA,OAAI;YAAE,GAAG,WAAW;;8BACnB,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,gIAAA,CAAA,YAAS;4BACR,SAAS,YAAY,OAAO;4BAC5B,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;8DAAC;;;;;;8DACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;8DAAC;;;;;;;;;;;;sDAGhB,6VAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,MAAM,KAAK;4CACpB,iBAAiB,CAAC;gDAChB,MAAM,QAAQ,CAAC;gDACf,IAAI,SAAS,YAAY;oDACvB,sBAAsB,MAAM,CAAC;wDAC3B,WAAW,QAAQ,UAAU;wDAC7B,YAAY;4DACV,UAAU,MAAM,QAAQ;wDAC1B;oDACF;gDACF;4CACF;;;;;;;;;;;;;;;;;sCAMR,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sDAAC;;;;;;sDACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;sDAAC;;;;;;;;;;;;8CAGhB,6VAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;oCAAM,SAAS,IAAM,mBAAmB;8CAAO;;;;;;;;;;;;sCAK7E,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sDAAC;;;;;;sDACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;sDAAC;;;;;;;;;;;;8CAGhB,6VAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;oCAAM,SAAS;8CAAqB;;;;;;;;;;;;sCAKlE,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sDAAC;;;;;;sDACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;sDAAC;;;;;;;;;;;;8CAGhB,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,mBAAmB;8CACnC;;;;;;;;;;;;;;;;;;8BAML,6VAAC,kKAAA,CAAA,UAAkB;oBACjB,MAAM;oBACN,cAAc;oBACd,aAAa;oBACb,UAAU;oBACV,uBAAuB;;;;;;8BAGzB,6VAAC,kKAAA,CAAA,UAAkB;oBACjB,MAAM;oBACN,cAAc;oBACd,UAAU;oBACV,MAAM;oBACN,QAAQ;;;;;;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 3174, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/purchase.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { tokenPackageCheckout } from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { Coins } from \"lucide-react\";\r\nimport { memo, useCallback, useMemo, useState } from \"react\";\r\nimport { z } from \"zod\";\r\nimport { errorToast } from \"../../global/toast\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nconst tokenSchema = z.object({\r\n  size: z.string(),\r\n  tokens: z.string(),\r\n  price: z.number().positive(),\r\n});\r\n\r\ntype TokenPackage = z.infer<typeof tokenSchema>;\r\n\r\nconst tokenPackages: TokenPackage[] = [\r\n  { size: \"2.5M\", tokens: \"2.5M\", price: 25 },\r\n  { size: \"4M\", tokens: \"4M\", price: 40 },\r\n  { size: \"6M\", tokens: \"6M\", price: 60 },\r\n  { size: \"8M\", tokens: \"8M\", price: 80 },\r\n  { size: \"10M\", tokens: \"10M\", price: 100 },\r\n  { size: \"12M\", tokens: \"12M\", price: 120 },\r\n  { size: \"15M\", tokens: \"15M\", price: 150 },\r\n  { size: \"20M\", tokens: \"20M\", price: 200 },\r\n  { size: \"25M\", tokens: \"25M\", price: 250 },\r\n  { size: \"30M\", tokens: \"30M\", price: 300 },\r\n] as const;\r\n\r\nconst SelectedPackageDisplay = memo(({ selectedPackage }: { selectedPackage: TokenPackage }) => (\r\n  <div className=\"rounded-lg bg-gradient-to-br from-primary/50 via-transparent to-transparent p-[1px]\">\r\n    <div className=\"rounded-lg bg-background p-4\">\r\n      <div className=\"mb-2 flex items-center justify-between\">\r\n        <span className=\"text-sm font-medium\">Selected Package</span>\r\n        <Coins className=\"h-5 w-5 text-primary\" />\r\n      </div>\r\n      <div className=\"flex flex-col space-y-1\">\r\n        <div className=\"flex justify-between\">\r\n          <span className=\"text-sm text-muted-foreground\">Tokens:</span>\r\n          <span className=\"font-medium\">{selectedPackage.tokens}</span>\r\n        </div>\r\n        <div className=\"flex justify-between\">\r\n          <span className=\"text-sm text-muted-foreground\">Price:</span>\r\n          <span className=\"font-medium text-primary\">${selectedPackage.price}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n));\r\n\r\nSelectedPackageDisplay.displayName = \"SelectedPackageDisplay\";\r\n\r\nconst TokenUsageBar = memo(\r\n  ({ remainingTokens, planToken }: { remainingTokens: string; planToken: number }) => {\r\n    const progressValue = useMemo(\r\n      () =>\r\n        Number(planToken) > 0\r\n          ? Math.min(100, (Number(remainingTokens) / Number(planToken)) * 100)\r\n          : 0,\r\n      [remainingTokens, planToken],\r\n    );\r\n\r\n    return (\r\n      <div className=\"flex flex-col gap-2 rounded-xl bg-sidebar-ring/10 p-3 px-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <span className=\"text-sm font-medium\">Token Usage</span>\r\n        </div>\r\n\r\n        <Progress value={progressValue} className=\"w-full\" />\r\n\r\n        <div className=\"mt-1 flex justify-between text-xs text-muted-foreground\">\r\n          <Typography.Muted>\r\n            Remaining:{\" \"}\r\n            <span className=\"font-medium text-foreground\">\r\n              {Number(remainingTokens).toLocaleString()}\r\n            </span>\r\n          </Typography.Muted>\r\n          <Typography.Muted>\r\n            Total:{\" \"}\r\n            <span className=\"font-medium text-foreground\">\r\n              {Number(planToken).toLocaleString()}\r\n            </span>\r\n          </Typography.Muted>\r\n        </div>\r\n      </div>\r\n    );\r\n  },\r\n);\r\n\r\nTokenUsageBar.displayName = \"TokenUsageBar\";\r\n\r\nconst SettingsPurchase = () => {\r\n  const { user } = useAuth();\r\n  const { planToken, remainingTokens } = useTokenInfo();\r\n  const [selectedPackage, setSelectedPackage] = useState<TokenPackage>(tokenPackages[2]);\r\n\r\n  const purchaseMutation = useMutation({\r\n    mutationFn: async () => {\r\n      if (!user.kinde_id) throw new Error(\"User not found\");\r\n      const response = await tokenPackageCheckout(selectedPackage.size, user.kinde_id);\r\n      if (!response?.url) throw new Error(\"Failed to initiate checkout\");\r\n      return response.url;\r\n    },\r\n    onSuccess: (url) => {\r\n      window.location.href = url;\r\n    },\r\n    onError: () => {\r\n      errorToast(\"Failed to initiate checkout. Please try again.\");\r\n    },\r\n  });\r\n\r\n  const handleValueChange = useCallback((value: string) => {\r\n    const selected = tokenPackages.find((pkg) => pkg.size === value);\r\n    if (selected) setSelectedPackage(selected);\r\n  }, []);\r\n\r\n  const handlePurchase = useCallback(() => {\r\n    purchaseMutation.mutate();\r\n  }, [purchaseMutation]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <TokenUsageBar remainingTokens={remainingTokens.toString()} planToken={planToken} />\r\n\r\n      <div className=\"grid gap-4\">\r\n        <div className=\"space-y-4\">\r\n          <Select\r\n            value={selectedPackage.size}\r\n            onValueChange={handleValueChange}\r\n            disabled={purchaseMutation.isPending}\r\n          >\r\n            <SelectTrigger>\r\n              <SelectValue placeholder=\"Select package size\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {tokenPackages.map((pkg) => (\r\n                <SelectItem key={pkg.size} value={pkg.size} disabled={purchaseMutation.isPending}>\r\n                  {pkg.tokens} tokens - ${pkg.price}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n\r\n          {selectedPackage && <SelectedPackageDisplay selectedPackage={selectedPackage} />}\r\n        </div>\r\n        <Button\r\n          onClick={handlePurchase}\r\n          disabled={purchaseMutation.isPending}\r\n          className=\"h-9 w-fit justify-end\"\r\n        >\r\n          {purchaseMutation.isPending ? (\r\n            <>\r\n              <Loading className=\"size-4 animate-spin text-background\" />\r\n              Purchasing...\r\n            </>\r\n          ) : (\r\n            `Purchase ${selectedPackage.tokens} Tokens for $${selectedPackage.price}`\r\n          )}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport function useTokenInfo() {\r\n  const { user } = useAuth();\r\n\r\n  const getPlanTokenLimit = useCallback((): number => {\r\n    const plan = user?.userFromDb?.plan?.toLowerCase() || \"launch\";\r\n    switch (plan) {\r\n      case \"launch\":\r\n        return 3_000_000;\r\n      case \"elite\":\r\n        return 7_000_000;\r\n      case \"business\":\r\n        return 16_000_000;\r\n      case \"entry\":\r\n        return 2_500_000;\r\n      case \"boost\":\r\n        return 6_000_000;\r\n      case \"fly\":\r\n        return 14_000_000;\r\n      case \"pro_enterprise\":\r\n        return 30_000_000;\r\n      case \"elite_enterprise\":\r\n        return 85_000_000;\r\n      case \"free-tier\":\r\n        return 50_000;\r\n      default:\r\n        return 3_000_000;\r\n    }\r\n  }, [user?.userFromDb?.plan]);\r\n\r\n  const getRemainingTokens = useCallback((): number => {\r\n    return user?.userFromDb?.free_total_token || 0;\r\n  }, [user?.userFromDb?.free_total_token]);\r\n\r\n  return useMemo(\r\n    () => ({\r\n      remainingTokens: getRemainingTokens(),\r\n      planToken: getPlanTokenLimit(),\r\n    }),\r\n    [getRemainingTokens, getPlanTokenLimit],\r\n  );\r\n}\r\n\r\nexport default memo(SettingsPurchase);\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;;AAEA,6DAA6D;AAC7D,MAAM,cAAc,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,MAAM,mOAAA,CAAA,IAAC,CAAC,MAAM;IACd,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM;IAChB,OAAO,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAIA,MAAM,gBAAgC;IACpC;QAAE,MAAM;QAAQ,QAAQ;QAAQ,OAAO;IAAG;IAC1C;QAAE,MAAM;QAAM,QAAQ;QAAM,OAAO;IAAG;IACtC;QAAE,MAAM;QAAM,QAAQ;QAAM,OAAO;IAAG;IACtC;QAAE,MAAM;QAAM,QAAQ;QAAM,OAAO;IAAG;IACtC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;CAC1C;AAED,MAAM,uCAAyB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,eAAe,EAAqC,iBACzF,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAK,WAAU;sCAAsB;;;;;;sCACtC,6VAAC,wRAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;8BAEnB,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,6VAAC;oCAAK,WAAU;8CAAe,gBAAgB,MAAM;;;;;;;;;;;;sCAEvD,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,6VAAC;oCAAK,WAAU;;wCAA2B;wCAAE,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5E,uBAAuB,WAAW,GAAG;AAErC,MAAM,8BAAgB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACvB,CAAC,EAAE,eAAe,EAAE,SAAS,EAAkD;IAC7E,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAC1B,IACE,OAAO,aAAa,IAChB,KAAK,GAAG,CAAC,KAAK,AAAC,OAAO,mBAAmB,OAAO,aAAc,OAC9D,GACN;QAAC;QAAiB;KAAU;IAG9B,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAK,WAAU;8BAAsB;;;;;;;;;;;0BAGxC,6VAAC,oIAAA,CAAA,WAAQ;gBAAC,OAAO;gBAAe,WAAU;;;;;;0BAE1C,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,KAAK;;4BAAC;4BACL;0CACX,6VAAC;gCAAK,WAAU;0CACb,OAAO,iBAAiB,cAAc;;;;;;;;;;;;kCAG3C,6VAAC,sIAAA,CAAA,UAAU,CAAC,KAAK;;4BAAC;4BACT;0CACP,6VAAC;gCAAK,WAAU;0CACb,OAAO,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAM7C;AAGF,cAAc,WAAW,GAAG;AAE5B,MAAM,mBAAmB;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAgB,aAAa,CAAC,EAAE;IAErF,MAAM,mBAAmB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY;YACV,IAAI,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAI,MAAM;YACpC,MAAM,WAAW,MAAM,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,IAAI,EAAE,KAAK,QAAQ;YAC/E,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,MAAM;YACpC,OAAO,SAAS,GAAG;QACrB;QACA,WAAW,CAAC;YACV,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QACA,SAAS;YACP,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,MAAM,WAAW,cAAc,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;QAC1D,IAAI,UAAU,mBAAmB;IACnC,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACjC,iBAAiB,MAAM;IACzB,GAAG;QAAC;KAAiB;IAErB,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAc,iBAAiB,gBAAgB,QAAQ;gBAAI,WAAW;;;;;;0BAEvE,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,gBAAgB,IAAI;gCAC3B,eAAe;gCACf,UAAU,iBAAiB,SAAS;;kDAEpC,6VAAC,kIAAA,CAAA,gBAAa;kDACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6VAAC,kIAAA,CAAA,gBAAa;kDACX,cAAc,GAAG,CAAC,CAAC,oBAClB,6VAAC,kIAAA,CAAA,aAAU;gDAAgB,OAAO,IAAI,IAAI;gDAAE,UAAU,iBAAiB,SAAS;;oDAC7E,IAAI,MAAM;oDAAC;oDAAY,IAAI,KAAK;;+CADlB,IAAI,IAAI;;;;;;;;;;;;;;;;4BAO9B,iCAAmB,6VAAC;gCAAuB,iBAAiB;;;;;;;;;;;;kCAE/D,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,iBAAiB,SAAS;wBACpC,WAAU;kCAET,iBAAiB,SAAS,iBACzB;;8CACE,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAwC;;2CAI7D,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,aAAa,EAAE,gBAAgB,KAAK,EAAE;;;;;;;;;;;;;;;;;;AAMrF;AAEO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACpC,MAAM,OAAO,MAAM,YAAY,MAAM,iBAAiB;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF,GAAG;QAAC,MAAM,YAAY;KAAK;IAE3B,MAAM,qBAAqB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,MAAM,YAAY,oBAAoB;IAC/C,GAAG;QAAC,MAAM,YAAY;KAAiB;IAEvC,OAAO,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EACX,IAAM,CAAC;YACL,iBAAiB;YACjB,WAAW;QACb,CAAC,GACD;QAAC;QAAoB;KAAkB;AAE3C;qDAEe,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3633, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/team.tsx"], "sourcesContent": ["import BaseEmptyState from \"@/components/base-empty-state\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { addTeamMember, removeTeamMember } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { Trash } from \"@mynaui/icons-react\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { Crown } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { errorToast, successToast } from \"../../global/toast\";\r\n\r\nconst formSchema = z.object({\r\n  email: z.string().email(\"Please enter a valid email address\"),\r\n});\r\n\r\ntype FormValues = z.infer<typeof formSchema>;\r\n\r\nconst SettingsTeam = () => {\r\n  const { project, refetch } = useProject();\r\n  const [emailToAdd, setEmailToAdd] = useState<string | null>(null);\r\n  const [emailToRemove, setEmailToRemove] = useState<string | null>(null);\r\n  const { user } = useAuth();\r\n  const queryClient = useQueryClient();\r\n\r\n  const form = useForm<FormValues>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      email: \"\",\r\n    },\r\n  });\r\n\r\n  const teamMembers = project?.team_emails || [];\r\n\r\n  const addTeamMemberMutation = useMutation({\r\n    mutationFn: async (email: string) => {\r\n      return addTeamMember(project?.project_id || \"\", email);\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"team-members\", project?.project_id] });\r\n      refetch();\r\n      form.reset();\r\n      setEmailToAdd(null);\r\n      successToast(\"Team member added successfully\");\r\n    },\r\n    onError: (error: Error) => {\r\n      errorToast(error.message || \"Failed to add team member\");\r\n    },\r\n  });\r\n\r\n  const removeTeamMemberMutation = useMutation({\r\n    mutationFn: async (email: string) => {\r\n      return removeTeamMember(project?.project_id || \"\", email);\r\n    },\r\n    onSuccess: () => {\r\n      refetch();\r\n      queryClient.invalidateQueries({ queryKey: [\"team-members\", project?.project_id] });\r\n      setEmailToRemove(null);\r\n      successToast(\"Team member removed successfully\");\r\n    },\r\n    onError: (error: Error) => {\r\n      errorToast(error.message || \"Failed to remove team member\");\r\n    },\r\n  });\r\n\r\n  const initiateAddTeamMember = (values: FormValues) => {\r\n    const email = values.email.trim();\r\n\r\n    if (email.toLowerCase() === user?.email?.toLowerCase()) {\r\n      errorToast(\"You cannot add yourself as a team member\");\r\n      return;\r\n    }\r\n\r\n    if (\r\n      teamMembers.some((member: unknown) =>\r\n        typeof member === \"string\"\r\n          ? member.toLowerCase() === email.toLowerCase()\r\n          : (member as { email?: string })?.email?.toLowerCase() === email.toLowerCase(),\r\n      )\r\n    ) {\r\n      errorToast(\"This email is already a team member\");\r\n      return;\r\n    }\r\n\r\n    setEmailToAdd(email);\r\n  };\r\n\r\n  const handleAddTeamMember = () => {\r\n    if (emailToAdd) {\r\n      addTeamMemberMutation.mutate(emailToAdd);\r\n    }\r\n  };\r\n\r\n  const handleRemoveTeamMember = () => {\r\n    if (emailToRemove) {\r\n      removeTeamMemberMutation.mutate(emailToRemove);\r\n    }\r\n  };\r\n\r\n  if (user?.userFromDb?.plan === \"free-tier\") {\r\n    return (\r\n      <BaseEmptyState\r\n        icon={<Crown className=\"size-8 text-muted-foreground\" />}\r\n        title=\"Team is not available on the free tier\"\r\n        description=\"Please upgrade to a paid plan to add team members.\"\r\n        buttonText=\"Upgrade\"\r\n        href=\"/pricing\"\r\n        className=\"min-h-[400px]\"\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <Form {...form}>\r\n        <form onSubmit={form.handleSubmit(initiateAddTeamMember)}>\r\n          <div className=\"space-y-3\">\r\n            <>\r\n              <FormField\r\n                control={form.control}\r\n                name=\"email\"\r\n                render={({ field }) => (\r\n                  <FormItem className=\"flex flex-col gap-1 space-y-1 py-3\">\r\n                    <FormLabel className=\"text-sm\">Add Team Member</FormLabel>\r\n                    <FormControl>\r\n                      <div className=\"flex flex-col gap-2 md:flex-row md:items-center md:justify-between\">\r\n                        <Input\r\n                          placeholder=\"Enter email\"\r\n                          className=\"max-w-full text-sm\"\r\n                          {...field}\r\n                        />\r\n                        <Button\r\n                          type=\"submit\"\r\n                          variant=\"default\"\r\n                          className=\"w-full md:w-fit\"\r\n                          disabled={addTeamMemberMutation.isPending}\r\n                        >\r\n                          {addTeamMemberMutation.isPending ? (\r\n                            <Loading className=\"size-4 animate-spin text-background\" />\r\n                          ) : (\r\n                            \"Invite\"\r\n                          )}\r\n                        </Button>\r\n                      </div>\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            </>\r\n          </div>\r\n        </form>\r\n      </Form>\r\n\r\n      <div className=\"flex flex-col\">\r\n        <div\r\n          className={cn(\r\n            \"flex items-center justify-between gap-1 rounded-xl bg-sidebar-ring/10 p-3 px-4\",\r\n            teamMembers.length >= 1 && \"rounded-b-none\",\r\n          )}\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <Avatar className=\"h-6 w-6\">\r\n              <AvatarImage src={`https://avatar.vercel.sh/${user?.email}.png`} />\r\n              <AvatarFallback>{user?.email?.charAt(0).toUpperCase()}</AvatarFallback>\r\n            </Avatar>\r\n            <Label className=\"text-sm\">{user.email}</Label>\r\n          </div>\r\n          <Badge variant=\"success\">Admin</Badge>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col\">\r\n          {teamMembers.map((member, index) => (\r\n            <div\r\n              key={member}\r\n              className={cn(\r\n                \"group flex items-center justify-between gap-1 bg-sidebar-ring/10 p-3 px-4\",\r\n                index === teamMembers.length - 1 && \"rounded-b-xl\",\r\n              )}\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <Avatar className=\"h-6 w-6\">\r\n                  <AvatarImage src={`https://avatar.vercel.sh/${member}.png`} />\r\n                  <AvatarFallback>{member?.charAt(0).toUpperCase()}</AvatarFallback>\r\n                </Avatar>\r\n                <Label className=\"text-sm\">{member}</Label>\r\n              </div>\r\n              <div className=\"flex items-center gap-1\">\r\n                <Badge variant=\"opened\">Member</Badge>\r\n                <Button\r\n                  variant=\"destructive\"\r\n                  size=\"icon\"\r\n                  className=\"m-0 hidden h-6 w-6 group-hover:flex group-hover:transition-all group-hover:duration-700 group-hover:ease-in\"\r\n                  onClick={() => setEmailToRemove(member)}\r\n                >\r\n                  <Trash />\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <AlertDialog open={!!emailToRemove} onOpenChange={() => setEmailToRemove(null)}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Remove Team Member</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to remove{\" \"}\r\n              <span className=\"font-medium text-primary\">{emailToRemove}</span> from the team? This\r\n              action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={removeTeamMemberMutation.isPending}>\r\n              Cancel\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleRemoveTeamMember}\r\n              disabled={removeTeamMemberMutation.isPending}\r\n              className=\"w-full bg-destructive text-destructive-foreground hover:bg-destructive/90 md:w-fit\"\r\n            >\r\n              {removeTeamMemberMutation.isPending ? (\r\n                <Loading className=\"size-4 animate-spin text-background\" />\r\n              ) : (\r\n                <Trash className=\"h-4 w-4\" />\r\n              )}\r\n              Remove\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      <AlertDialog open={!!emailToAdd} onOpenChange={() => setEmailToAdd(null)}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Add Team Member</AlertDialogTitle>\r\n            <AlertDialogDescription className=\"space-y-2\">\r\n              <p>\r\n                Are you sure you want to add{\" \"}\r\n                <span className=\"font-medium text-primary\">{emailToAdd}</span> to the team?\r\n              </p>\r\n              <p className=\"text-muted-foreground\">\r\n                The member will be able to view and edit the project.\r\n              </p>\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter className=\"flex items-center\">\r\n            <AlertDialogCancel disabled={addTeamMemberMutation.isPending}>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleAddTeamMember}\r\n              disabled={addTeamMemberMutation.isPending}\r\n              className=\"w-full md:w-fit\"\r\n            >\r\n              {addTeamMemberMutation.isPending ? (\r\n                <Loading className=\"size-4 animate-spin text-background\" />\r\n              ) : (\r\n                \"Add Member\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsTeam;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAUA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,aAAa,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAIA,MAAM,eAAe;IACnB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAc;QAC/B,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;QACT;IACF;IAEA,MAAM,cAAc,SAAS,eAAe,EAAE;IAE9C,MAAM,wBAAwB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,OAAO;YACjB,OAAO,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,cAAc,IAAI;QAClD;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAgB,SAAS;iBAAW;YAAC;YAChF;YACA,KAAK,KAAK;YACV,cAAc;YACd,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;QACf;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO,IAAI;QAC9B;IACF;IAEA,MAAM,2BAA2B,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,YAAY,OAAO;YACjB,OAAO,CAAA,GAAA,iHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,cAAc,IAAI;QACrD;QACA,WAAW;YACT;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAgB,SAAS;iBAAW;YAAC;YAChF,iBAAiB;YACjB,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;QACf;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO,IAAI;QAC9B;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,OAAO,KAAK,CAAC,IAAI;QAE/B,IAAI,MAAM,WAAW,OAAO,MAAM,OAAO,eAAe;YACtD,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX;QACF;QAEA,IACE,YAAY,IAAI,CAAC,CAAC,SAChB,OAAO,WAAW,WACd,OAAO,WAAW,OAAO,MAAM,WAAW,KAC1C,AAAC,QAA+B,OAAO,kBAAkB,MAAM,WAAW,KAEhF;YACA,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX;QACF;QAEA,cAAc;IAChB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,YAAY;YACd,sBAAsB,MAAM,CAAC;QAC/B;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,eAAe;YACjB,yBAAyB,MAAM,CAAC;QAClC;IACF;IAEA,IAAI,MAAM,YAAY,SAAS,aAAa;QAC1C,qBACE,6VAAC,4IAAA,CAAA,UAAc;YACb,oBAAM,6VAAC,wRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAM;YACN,aAAY;YACZ,YAAW;YACX,MAAK;YACL,WAAU;;;;;;IAGhB;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC,gIAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6VAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;8BAChC,cAAA,6VAAC;wBAAI,WAAU;kCACb,cAAA;sCACE,cAAA,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6VAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC;oDAAI,WAAU;;sEACb,6VAAC,iIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,WAAU;4DACT,GAAG,KAAK;;;;;;sEAEX,6VAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,UAAU,sBAAsB,SAAS;sEAExC,sBAAsB,SAAS,iBAC9B,6VAAC,mIAAA,CAAA,UAAO;gEAAC,WAAU;;;;;yEAEnB;;;;;;;;;;;;;;;;;0DAKR,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1B,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,YAAY,MAAM,IAAI,KAAK;;0CAG7B,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6VAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK,CAAC,yBAAyB,EAAE,MAAM,MAAM,IAAI,CAAC;;;;;;0DAC/D,6VAAC,kIAAA,CAAA,iBAAc;0DAAE,MAAM,OAAO,OAAO,GAAG;;;;;;;;;;;;kDAE1C,6VAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAW,KAAK,KAAK;;;;;;;;;;;;0CAExC,6VAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;kCAG3B,6VAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6VAAC;gCAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6EACA,UAAU,YAAY,MAAM,GAAG,KAAK;;kDAGtC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6VAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,IAAI,CAAC;;;;;;kEAC1D,6VAAC,kIAAA,CAAA,iBAAc;kEAAE,QAAQ,OAAO,GAAG;;;;;;;;;;;;0DAErC,6VAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAW;;;;;;;;;;;;kDAE9B,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAS;;;;;;0DACxB,6VAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,6VAAC,4SAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;+BArBL;;;;;;;;;;;;;;;;0BA6Bb,6VAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM,CAAC,CAAC;gBAAe,cAAc,IAAM,iBAAiB;0BACvE,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;;sCACjB,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACU;sDAChC,6VAAC;4CAAK,WAAU;sDAA4B;;;;;;wCAAqB;;;;;;;;;;;;;sCAIrE,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU,yBAAyB,SAAS;8CAAE;;;;;;8CAGjE,6VAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU,yBAAyB,SAAS;oCAC5C,WAAU;;wCAET,yBAAyB,SAAS,iBACjC,6VAAC,mIAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6VAAC,4SAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCACjB;;;;;;;;;;;;;;;;;;;;;;;;0BAOV,6VAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM,CAAC,CAAC;gBAAY,cAAc,IAAM,cAAc;0BACjE,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;;sCACjB,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,2IAAA,CAAA,yBAAsB;oCAAC,WAAU;;sDAChC,6VAAC;;gDAAE;gDAC4B;8DAC7B,6VAAC;oDAAK,WAAU;8DAA4B;;;;;;gDAAkB;;;;;;;sDAEhE,6VAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,6VAAC,2IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,6VAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU,sBAAsB,SAAS;8CAAE;;;;;;8CAC9D,6VAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU,sBAAsB,SAAS;oCACzC,WAAU;8CAET,sBAAsB,SAAS,iBAC9B,6VAAC,mIAAA,CAAA,UAAO;wCAAC,WAAU;;;;;+CAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;uCAEe", "debugId": null}}, {"offset": {"line": 4235, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/project-settings-modal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport BaseEmptyState from \"@/components/base-empty-state\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalTitle } from \"@/components/ui/modal\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollBar } from \"@/components/ui/scroll-area\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { SettingsTab, useSettingsStore } from \"@/stores/settings-tab\";\r\nimport {\r\n  BrandGithubSolid,\r\n  CogOneSolid,\r\n  CreditCardSolid,\r\n  DatabaseSolid,\r\n  Globe,\r\n  UsersSolid,\r\n} from \"@mynaui/icons-react\";\r\nimport { Crown } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useMemo, useState } from \"react\";\r\nimport { FaMoneyBill } from \"react-icons/fa\";\r\nimport { LuCopy } from \"react-icons/lu\";\r\nimport SupabaseIcon from \"../icon/supabase\";\r\nimport SoftgenIcon from \"../thread/thread-message/softgen-icon\";\r\nimport SupabaseSheet from \"./modal/supabase-sheet\";\r\nimport SettingsBilling from \"./settings/billing\";\r\nimport SettingsClone from \"./settings/clone\";\r\nimport SettingsEnvironment from \"./settings/environment\";\r\nimport SettingsGithub from \"./settings/github\";\r\nimport SettingsProject from \"./settings/project\";\r\nimport SettingsPurchase from \"./settings/purchase\";\r\nimport SettingsTeam from \"./settings/team\";\r\n\r\ntype SettingsModalProps = {\r\n  setShowModal?: (showModal: boolean) => void;\r\n};\r\n\r\nexport default function SettingsModalContent({ setShowModal }: SettingsModalProps) {\r\n  const { user } = useAuth();\r\n  const isFreeTier = useMemo(() => user?.userFromDb?.plan === \"free-tier\", [user]);\r\n  const activeTab = useSettingsStore((state) => state.settingsTab);\r\n  const setActiveTab = useSettingsStore((state) => state.setSettingsTab);\r\n\r\n  const [showSupabaseSheet, setShowSupabaseSheet] = useState(false);\r\n\r\n  const tabs: {\r\n    id: SettingsTab;\r\n    label: string;\r\n    icon: React.ReactNode;\r\n    pro?: boolean;\r\n  }[] = [\r\n    { id: \"project\", label: \"Project\", icon: <CogOneSolid />, pro: false },\r\n    { id: \"environment\", label: \"Environment\", icon: <DatabaseSolid />, pro: isFreeTier },\r\n    { id: \"supabase\", label: \"Supabase\", icon: <SupabaseIcon />, pro: isFreeTier },\r\n    { id: \"team\", label: \"Team\", icon: <UsersSolid />, pro: isFreeTier },\r\n    { id: \"github\", label: \"Github\", icon: <BrandGithubSolid />, pro: isFreeTier },\r\n    { id: \"clone\", label: \"Clone\", icon: <LuCopy />, pro: isFreeTier },\r\n    { id: \"publish\", label: \"Publish\", icon: <Globe />, pro: isFreeTier },\r\n    {\r\n      id: \"billing\",\r\n      label: \"Billing\",\r\n      icon: <CreditCardSolid />,\r\n      pro: isFreeTier,\r\n    },\r\n    { id: \"purchase\", label: \"Purchase\", icon: <FaMoneyBill />, pro: false },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <ModalContentInner className=\"w-full lg:max-w-5xl\">\r\n        <div className=\"grid h-[800px] grid-cols-12 overflow-hidden lg:h-[600px]\">\r\n          <div className=\"col-span-12 hidden flex-col border-r border-ring/15 bg-sidebar-ring/10 px-3 lg:col-span-3 lg:flex\">\r\n            <div className=\"flex flex-col gap-4 space-y-2 px-2 py-5 text-left\">\r\n              <SoftgenIcon />\r\n            </div>\r\n\r\n            <div className=\"flex flex-1 flex-col gap-1\">\r\n              {tabs\r\n                .sort((a, b) => (a.pro ? 1 : b.pro ? -1 : 0))\r\n                .filter((tab) => tab.id !== \"publish\" || isFreeTier)\r\n                .map((tab) => (\r\n                  <Button\r\n                    key={tab.id}\r\n                    variant={activeTab === tab.id ? \"default\" : \"ghost\"}\r\n                    className=\"h-fit w-full justify-between rounded-md px-3 py-2\"\r\n                    onClick={() => {\r\n                      if (tab.id === \"supabase\" && !isFreeTier) {\r\n                        setShowSupabaseSheet(true);\r\n                      } else {\r\n                        setActiveTab(tab.id);\r\n                      }\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {tab.icon}\r\n                      {tab.label}\r\n                    </div>\r\n                    {tab.pro && (\r\n                      <Badge variant=\"terminal\" className=\"px-2 py-0.5\">\r\n                        Pro\r\n                      </Badge>\r\n                    )}\r\n                  </Button>\r\n                ))}\r\n            </div>\r\n\r\n            <ModalFooter className=\"-m-2 mb-0 flex flex-col items-start justify-start rounded-none border-t-0 bg-transparent py-0 dark:bg-transparent sm:flex-col sm:justify-start md:px-1\">\r\n              {isFreeTier && (\r\n                <Button className=\"relative my-3 mb-2 h-10 w-full text-sm\">\r\n                  <Link href=\"/pricing\">Upgrade Now</Link>\r\n                </Button>\r\n              )}\r\n            </ModalFooter>\r\n          </div>\r\n\r\n          <div className=\"col-span-12 hidden lg:col-span-9 lg:block\">\r\n            <ModalHeader>\r\n              <ModalTitle className=\"text-lg\">\r\n                {tabs.find((tab) => tab.id === activeTab)?.label ||\r\n                  (activeTab && activeTab?.charAt(0).toUpperCase() + activeTab?.slice(1))}\r\n              </ModalTitle>\r\n            </ModalHeader>\r\n\r\n            <div className=\"p-4 px-6\">\r\n              {activeTab === \"supabase\" && isFreeTier && (\r\n                <BaseEmptyState\r\n                  icon={<Crown className=\"size-8 text-muted-foreground\" />}\r\n                  title=\"Supabase is not available on the free tier\"\r\n                  description=\"Please upgrade to a paid plan to use Supabase.\"\r\n                  buttonText=\"Upgrade\"\r\n                  href=\"/pricing\"\r\n                  className=\"min-h-[400px]\"\r\n                />\r\n              )}\r\n              {activeTab === \"publish\" && isFreeTier && (\r\n                <BaseEmptyState\r\n                  icon={<Crown className=\"size-8 text-muted-foreground\" />}\r\n                  title=\"Publish is not available on the free tier\"\r\n                  description=\"Please upgrade to a paid plan to publish your project.\"\r\n                  buttonText=\"Upgrade\"\r\n                  href=\"/pricing\"\r\n                  className=\"min-h-[400px]\"\r\n                />\r\n              )}\r\n              {activeTab === \"project\" && (\r\n                <SettingsProject setShowModal={setShowModal || (() => {})} />\r\n              )}\r\n              {activeTab === \"team\" && <SettingsTeam />}\r\n              {activeTab === \"github\" && <SettingsGithub />}\r\n              {activeTab === \"clone\" && <SettingsClone />}\r\n              {activeTab === \"billing\" && <SettingsBilling />}\r\n              {activeTab === \"purchase\" && <SettingsPurchase />}\r\n              {activeTab === \"environment\" && <SettingsEnvironment />}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-span-12 w-full lg:hidden\">\r\n            <ModalHeader>\r\n              <ModalTitle>\r\n                {tabs.find((tab) => tab.id === activeTab)?.label ||\r\n                  (activeTab && activeTab?.charAt(0).toUpperCase() + activeTab?.slice(1))}\r\n              </ModalTitle>\r\n            </ModalHeader>\r\n\r\n            <ScrollArea>\r\n              <div className=\"flex flex-row gap-2 overflow-x-auto p-6 py-3\">\r\n                {tabs\r\n                  .sort((a, b) => (a.pro ? 1 : b.pro ? -1 : 0))\r\n                  .filter((tab) => tab.id !== \"publish\" || isFreeTier)\r\n                  .map((tab) => (\r\n                    <Button\r\n                      key={tab.id}\r\n                      variant={activeTab === tab.id ? \"default\" : \"ghost\"}\r\n                      className=\"h-fit justify-between whitespace-nowrap rounded-md px-3 py-2\"\r\n                      onClick={() => {\r\n                        if (tab.id === \"supabase\" && !isFreeTier) {\r\n                          setShowSupabaseSheet(true);\r\n                        } else {\r\n                          setActiveTab(tab.id);\r\n                        }\r\n                      }}\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {tab.icon}\r\n                        {tab.label}\r\n                      </div>\r\n                    </Button>\r\n                  ))}\r\n              </div>\r\n              <ScrollBar orientation=\"horizontal\" />\r\n            </ScrollArea>\r\n\r\n            <div className=\"p-6 md:p-4\">\r\n              {activeTab === \"supabase\" && isFreeTier && (\r\n                <BaseEmptyState\r\n                  icon={<Crown className=\"size-8 text-muted-foreground\" />}\r\n                  title=\"Supabase is not available on the free tier\"\r\n                  description=\"Please upgrade to a paid plan to use Supabase.\"\r\n                  buttonText=\"Upgrade\"\r\n                  href=\"/pricing\"\r\n                  className=\"min-h-[400px]\"\r\n                />\r\n              )}\r\n              {activeTab === \"publish\" && isFreeTier && (\r\n                <BaseEmptyState\r\n                  icon={<Crown className=\"size-8 text-muted-foreground\" />}\r\n                  title=\"Publish is not available on the free tier\"\r\n                  description=\"Please upgrade to a paid plan to publish your project.\"\r\n                  buttonText=\"Upgrade\"\r\n                  href=\"/pricing\"\r\n                  className=\"min-h-[400px]\"\r\n                />\r\n              )}\r\n              {activeTab === \"project\" && (\r\n                <SettingsProject setShowModal={setShowModal || (() => {})} />\r\n              )}\r\n              {activeTab === \"team\" && <SettingsTeam />}\r\n              {activeTab === \"github\" && <SettingsGithub />}\r\n              {activeTab === \"clone\" && <SettingsClone />}\r\n              {activeTab === \"billing\" && <SettingsBilling />}\r\n              {activeTab === \"purchase\" && <SettingsPurchase />}\r\n              {activeTab === \"environment\" && <SettingsEnvironment />}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ModalContentInner>\r\n      <SupabaseSheet isOpen={showSupabaseSheet} onClose={() => setShowSupabaseSheet(false)} />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;;;;;;;;;;AAqCe,SAAS,qBAAqB,EAAE,YAAY,EAAsB;IAC/E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,YAAY,SAAS,aAAa;QAAC;KAAK;IAC/E,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,WAAW;IAC/D,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,cAAc;IAErE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,OAKA;QACJ;YAAE,IAAI;YAAW,OAAO;YAAW,oBAAM,6VAAC,iUAAA,CAAA,cAAW;;;;;YAAK,KAAK;QAAM;QACrE;YAAE,IAAI;YAAe,OAAO;YAAe,oBAAM,6VAAC,qUAAA,CAAA,gBAAa;;;;;YAAK,KAAK;QAAW;QACpF;YAAE,IAAI;YAAY,OAAO;YAAY,oBAAM,6VAAC,oIAAA,CAAA,UAAY;;;;;YAAK,KAAK;QAAW;QAC7E;YAAE,IAAI;YAAQ,OAAO;YAAQ,oBAAM,6VAAC,+TAAA,CAAA,aAAU;;;;;YAAK,KAAK;QAAW;QACnE;YAAE,IAAI;YAAU,OAAO;YAAU,oBAAM,6VAAC,2UAAA,CAAA,mBAAgB;;;;;YAAK,KAAK;QAAW;QAC7E;YAAE,IAAI;YAAS,OAAO;YAAS,oBAAM,6VAAC,+NAAA,CAAA,SAAM;;;;;YAAK,KAAK;QAAW;QACjE;YAAE,IAAI;YAAW,OAAO;YAAW,oBAAM,6VAAC,4SAAA,CAAA,QAAK;;;;;YAAK,KAAK;QAAW;QACpE;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,6VAAC,yUAAA,CAAA,kBAAe;;;;;YACtB,KAAK;QACP;QACA;YAAE,IAAI;YAAY,OAAO;YAAY,oBAAM,6VAAC,+NAAA,CAAA,cAAW;;;;;YAAK,KAAK;QAAM;KACxE;IAED,qBACE;;0BACE,6VAAC,iIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC3B,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,kKAAA,CAAA,UAAW;;;;;;;;;;8CAGd,6VAAC;oCAAI,WAAU;8CACZ,KACE,IAAI,CAAC,CAAC,GAAG,IAAO,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,GACzC,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,aAAa,YACxC,GAAG,CAAC,CAAC,oBACJ,6VAAC,kIAAA,CAAA,SAAM;4CAEL,SAAS,cAAc,IAAI,EAAE,GAAG,YAAY;4CAC5C,WAAU;4CACV,SAAS;gDACP,IAAI,IAAI,EAAE,KAAK,cAAc,CAAC,YAAY;oDACxC,qBAAqB;gDACvB,OAAO;oDACL,aAAa,IAAI,EAAE;gDACrB;4CACF;;8DAEA,6VAAC;oDAAI,WAAU;;wDACZ,IAAI,IAAI;wDACR,IAAI,KAAK;;;;;;;gDAEX,IAAI,GAAG,kBACN,6VAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAW,WAAU;8DAAc;;;;;;;2CAhB/C,IAAI,EAAE;;;;;;;;;;8CAwBnB,6VAAC,iIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,4BACC,6VAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAChB,cAAA,6VAAC,2QAAA,CAAA,UAAI;4CAAC,MAAK;sDAAW;;;;;;;;;;;;;;;;;;;;;;sCAM9B,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,6VAAC,iIAAA,CAAA,aAAU;wCAAC,WAAU;kDACnB,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,YAAY,SACxC,aAAa,WAAW,OAAO,GAAG,gBAAgB,WAAW,MAAM;;;;;;;;;;;8CAI1E,6VAAC;oCAAI,WAAU;;wCACZ,cAAc,cAAc,4BAC3B,6VAAC,4IAAA,CAAA,UAAc;4CACb,oBAAM,6VAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CACvB,OAAM;4CACN,aAAY;4CACZ,YAAW;4CACX,MAAK;4CACL,WAAU;;;;;;wCAGb,cAAc,aAAa,4BAC1B,6VAAC,4IAAA,CAAA,UAAc;4CACb,oBAAM,6VAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CACvB,OAAM;4CACN,aAAY;4CACZ,YAAW;4CACX,MAAK;4CACL,WAAU;;;;;;wCAGb,cAAc,2BACb,6VAAC,kJAAA,CAAA,UAAe;4CAAC,cAAc,gBAAgB,CAAC,KAAO,CAAC;;;;;;wCAEzD,cAAc,wBAAU,6VAAC,+IAAA,CAAA,UAAY;;;;;wCACrC,cAAc,0BAAY,6VAAC,iJAAA,CAAA,UAAc;;;;;wCACzC,cAAc,yBAAW,6VAAC,gJAAA,CAAA,UAAa;;;;;wCACvC,cAAc,2BAAa,6VAAC,kJAAA,CAAA,UAAe;;;;;wCAC3C,cAAc,4BAAc,6VAAC,mJAAA,CAAA,UAAgB;;;;;wCAC7C,cAAc,+BAAiB,6VAAC,sJAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;sCAIxD,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,6VAAC,iIAAA,CAAA,aAAU;kDACR,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,YAAY,SACxC,aAAa,WAAW,OAAO,GAAG,gBAAgB,WAAW,MAAM;;;;;;;;;;;8CAI1E,6VAAC,0IAAA,CAAA,aAAU;;sDACT,6VAAC;4CAAI,WAAU;sDACZ,KACE,IAAI,CAAC,CAAC,GAAG,IAAO,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,GACzC,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,aAAa,YACxC,GAAG,CAAC,CAAC,oBACJ,6VAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,cAAc,IAAI,EAAE,GAAG,YAAY;oDAC5C,WAAU;oDACV,SAAS;wDACP,IAAI,IAAI,EAAE,KAAK,cAAc,CAAC,YAAY;4DACxC,qBAAqB;wDACvB,OAAO;4DACL,aAAa,IAAI,EAAE;wDACrB;oDACF;8DAEA,cAAA,6VAAC;wDAAI,WAAU;;4DACZ,IAAI,IAAI;4DACR,IAAI,KAAK;;;;;;;mDAbP,IAAI,EAAE;;;;;;;;;;sDAkBnB,6VAAC,0IAAA,CAAA,YAAS;4CAAC,aAAY;;;;;;;;;;;;8CAGzB,6VAAC;oCAAI,WAAU;;wCACZ,cAAc,cAAc,4BAC3B,6VAAC,4IAAA,CAAA,UAAc;4CACb,oBAAM,6VAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CACvB,OAAM;4CACN,aAAY;4CACZ,YAAW;4CACX,MAAK;4CACL,WAAU;;;;;;wCAGb,cAAc,aAAa,4BAC1B,6VAAC,4IAAA,CAAA,UAAc;4CACb,oBAAM,6VAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CACvB,OAAM;4CACN,aAAY;4CACZ,YAAW;4CACX,MAAK;4CACL,WAAU;;;;;;wCAGb,cAAc,2BACb,6VAAC,kJAAA,CAAA,UAAe;4CAAC,cAAc,gBAAgB,CAAC,KAAO,CAAC;;;;;;wCAEzD,cAAc,wBAAU,6VAAC,+IAAA,CAAA,UAAY;;;;;wCACrC,cAAc,0BAAY,6VAAC,iJAAA,CAAA,UAAc;;;;;wCACzC,cAAc,yBAAW,6VAAC,gJAAA,CAAA,UAAa;;;;;wCACvC,cAAc,2BAAa,6VAAC,kJAAA,CAAA,UAAe;;;;;wCAC3C,cAAc,4BAAc,6VAAC,mJAAA,CAAA,UAAgB;;;;;wCAC7C,cAAc,+BAAiB,6VAAC,sJAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK5D,6VAAC,yJAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAmB,SAAS,IAAM,qBAAqB;;;;;;;;AAGpF", "debugId": null}}]}