const CHUNK_PUBLIC_PATH = "server/app/(main)/project/[id]/page.js";
const runtime = require("../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/f75d8_next_dist_b7f54d8e._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/src_app_62399afb._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_64f91460._.js");
runtime.loadChunk("server/chunks/ssr/src_57891588._.js");
runtime.loadChunk("server/chunks/ssr/src_app_error_tsx_2a8ad0f1._.js");
runtime.loadChunk("server/chunks/ssr/src_app_not-found_tsx_1ac69861._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_d0961efd._.js");
runtime.loadChunk("server/chunks/ssr/f75d8_next_dist_client_components_unauthorized-error_813b7e68.js");
runtime.loadChunk("server/chunks/ssr/src_app_global-error_tsx_d6ef94b5._.js");
runtime.loadChunk("server/chunks/ssr/src_app_(main)_layout_tsx_e2361b88._.js");
runtime.loadChunk("server/chunks/ssr/f75d8_next_dist_client_components_not-found-error_c592651a.js");
runtime.loadChunk("server/chunks/ssr/src_4bba836c._.js");
runtime.loadChunk("server/chunks/ssr/f75d8_next_dist_f32cc76e._.js");
runtime.loadChunk("server/chunks/ssr/_4e2407dc._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(main)/project/[id]/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/esm/build/templates/app-page.js?page=/(main)/project/[id]/page { GLOBAL_ERROR_MODULE => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", METADATA_0 => \"[project]/src/app/icon.svg.mjs { IMAGE => \\\"[project]/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/src/app/error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/src/app/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_10 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_11 => \"[project]/src/app/(main)/project/[id]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_12 => \"[project]/src/app/(main)/project/[id]/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/esm/build/templates/app-page.js?page=/(main)/project/[id]/page { GLOBAL_ERROR_MODULE => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", METADATA_0 => \"[project]/src/app/icon.svg.mjs { IMAGE => \\\"[project]/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/src/app/error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/src/app/(main)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_10 => \"[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_11 => \"[project]/src/app/(main)/project/[id]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_12 => \"[project]/src/app/(main)/project/[id]/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
