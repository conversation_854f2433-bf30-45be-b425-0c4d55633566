import logging
import httpx
import base64
import hashlib
import secrets
import json
from fastapi import APIRouter, HTTPException, Depends, Body, Query
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
import urllib.parse
import aiohttp

from core.db import Database
from core.models import Project, User, SupabaseOrganization, SupabaseConnection
from core.platform.user import get_current_active_user
from core.platform.is_admin import is_admin
from sqlalchemy import select
from core.config import settings
from core.envs.env_ops import env_ops
from core.utils import supabase_token_manager, supabase_sql_executor

# Initialize router
router = APIRouter(prefix="/supabase")
db = Database()

# Store for temporary auth states and PKCE code verifiers
auth_states = {}

class SupabaseAuthRequest(BaseModel):
    project_id: str

class SupabaseAuthorizationRequest(BaseModel):
    project_id: str

class SupabaseConnectionRequest(BaseModel):
    project_id: str  # SoftGen project ID
    organization_id: str  # Supabase organization ID (from SupabaseOrganization table)
    organization_name: str  # Supabase organization name
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    supabase_project_id: Optional[str] = None
    api_key: Optional[str] = None
    service_role_key: Optional[str] = None
    api_url: Optional[str] = None
    database_password: Optional[str] = None
    created_at: Optional[str] = None
    last_updated: Optional[str] = None

class SupabaseOrganizationRequest(BaseModel):
    user_id: int
    organization_id: str
    organization_name: str
    access_token: str
    refresh_token: str

class SupabaseService:
    def __init__(self, db_instance):
        self.db = db_instance
        # Supabase OAuth configuration - use settings from config.py
        self.client_id = settings.supabase_client_id
        self.client_secret = settings.supabase_client_secret
        self.redirect_uri = settings.supabase_redirect_uri
        self.base_url = settings.supabase_base_url
        self.auth_url = settings.supabase_auth_url
        self.token_url = settings.supabase_token_url
        self.scope = settings.supabase_scope
    
    # Add this method to generate the authorization URL
    def get_authorization_url(self, state: str) -> str:
        """Generate the Supabase OAuth authorization URL"""
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "response_type": "code",
            "scope": self.scope,
            "state": state
        }
        
        # Build the URL with query parameters
        query_string = "&".join([f"{key}={urllib.parse.quote(value)}" for key, value in params.items()])
        authorization_url = f"{self.auth_url}?{query_string}"
        
        logging.info(f"Generated authorization URL: {authorization_url}")
        return authorization_url
    
    # Add this method to exchange the code for tokens
    async def exchange_code_for_token(self, code: str) -> dict:
        """Exchange the authorization code for access and refresh tokens"""
        try:
            logging.info(f"Exchanging code for token: {code[:10]}...")
            
            # Prepare the request data
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "authorization_code",
                "code": code,
                "redirect_uri": self.redirect_uri
            }
            
            # Make the request to the token endpoint
            async with aiohttp.ClientSession() as session:
                async with session.post(self.token_url, data=data) as response:
                    response_text = await response.text()
                    
                    try:
                        # Try to parse the response as JSON
                        tokens = json.loads(response_text)
                        
                        # Check if we have an access token (success)
                        if "access_token" in tokens:
                            logging.info(f"Successfully exchanged code for token: {tokens.get('access_token')[:10]}...")
                            return tokens
                        else:
                            logging.error(f"Token response missing access_token: {response_text}")
                            return {}
                    except json.JSONDecodeError:
                        logging.error(f"Failed to parse token response as JSON: {response_text}")
                        return {}
        except Exception as e:
            logging.error(f"Error exchanging code for token: {str(e)}")
            logging.exception("Full exception details:")
            return {}
    
    # Add this method to get user info
    async def get_user_info(self, access_token: str) -> dict:
        """Get user information using the access token"""
        try:
            logging.info("Getting user info with access token")
            
            headers = {
                "Authorization": f"Bearer {access_token}"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/v1/user", headers=headers) as response:
                    if response.status == 200:
                        user_info = await response.json()
                        logging.info(f"Successfully retrieved user info: {user_info.get('email')}")
                        return user_info
                    else:
                        error_text = await response.text()
                        logging.error(f"Failed to get user info: {error_text}")
                        return {}
        except Exception as e:
            logging.error(f"Error getting user info: {str(e)}")
            logging.exception("Full exception details:")
            return {}
    

    
    # Add this method to test the connection
    async def test_connection(self, access_token: str) -> dict:
        """Test the connection to Supabase API"""
        try:
            logging.info("Testing connection with access token")
            
            # Try to get organizations as a test
            organizations = await self.get_organizations(access_token)
            
            if organizations:
                return {
                    "connected": True,
                    "organizations": organizations
                }
            else:
                return {
                    "connected": False,
                    "organizations": []
                }
        except Exception as e:
            logging.error(f"Error testing connection: {str(e)}")
            logging.exception("Full exception details:")
            return {
                "connected": False,
                "organizations": []
            }
    
    def generate_pkce_pair(self):
        """Generate a PKCE code verifier and code challenge pair"""
        code_verifier = secrets.token_urlsafe(64)
        code_verifier = code_verifier[:128]  # Truncate to max 128 chars
        
        # Generate code challenge using SHA256
        code_challenge = hashlib.sha256(code_verifier.encode()).digest()
        code_challenge = base64.urlsafe_b64encode(code_challenge).decode().rstrip('=')
        
        return code_verifier, code_challenge
    
    async def get_organization_projects(self, access_token: str, organization_id: str):
        """Get projects for a specific Supabase organization"""
        try:
            logging.info(f"Getting projects for organization {organization_id}")
            
            # The correct endpoint is /v1/projects with organization_id as query parameter
            url = f"{self.base_url}/v1/projects?organization_id={organization_id}"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }
            
            logging.info(f"Making request to: {url}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                
                logging.info(f"Projects response status: {response.status_code}")
                
                if response.status_code != 200:
                    logging.error(f"Failed to get projects: {response.text}")
                    return None
                
                projects = response.json()
                logging.info(f"Found {len(projects)} projects for organization {organization_id}")
                
                # Return simplified project data with proper API URL handling
                return [
                    {
                        "id": project.get("id"),
                        "name": project.get("name"),
                        "organization_id": project.get("organization_id"),
                        "region": project.get("region"),
                        "created_at": project.get("created_at"),
                        # Use project.get('ref') if available, otherwise use project.get('id')
                        "api_url": f"https://{project.get('ref') or project.get('id')}.supabase.co",
                        "ref": project.get("ref") or project.get("id")  # Use ID as fallback for ref
                    }
                    for project in projects
                ]
                
        except Exception as e:
            logging.error(f"Error getting organization projects: {str(e)}")
            return None
    
    async def get_organizations(self, access_token: str):
        """Get Supabase organizations for a user"""
        try:
            logging.info("Getting Supabase organizations...")
            
            url = f"{self.base_url}/v1/organizations"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }
            
            logging.info(f"Making request to: {url}")
            logging.info(f"Using access token: {access_token[:10]}...")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                
                logging.info(f"Organizations response status: {response.status_code}")
                
                if response.status_code != 200:
                    logging.error(f"Failed to get organizations: {response.text}")
                    return None
                
                organizations = response.json()
                logging.info(f"Found {len(organizations)} organizations")
                
                return organizations
                
        except Exception as e:
            logging.error(f"Error getting organizations: {str(e)}")
            return None
    
    async def save_organization(self, org_request: SupabaseOrganizationRequest) -> bool:
        """Save or update a Supabase organization in the database"""
        try:
            logging.info(f"Saving organization {org_request.organization_id} for user {org_request.user_id}")
            
            async with self.db.get_async_session() as session:
                # Check if organization already exists
                from core.models import SupabaseOrganization
                stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.organization_id == org_request.organization_id,
                    SupabaseOrganization.user_id == org_request.user_id
                )
                result = await session.execute(stmt)
                organization = result.scalars().first()
                
                if organization:
                    # Update existing organization
                    logging.info(f"Updating existing organization {org_request.organization_id}")
                    organization.organization_name = org_request.organization_name
                    organization.access_token = org_request.access_token
                    organization.refresh_token = org_request.refresh_token
                    organization.last_updated = datetime.now().isoformat()
                else:
                    # Create new organization
                    logging.info(f"Creating new organization {org_request.organization_id}")
                    organization = SupabaseOrganization(
                        user_id=org_request.user_id,
                        organization_id=org_request.organization_id,
                        organization_name=org_request.organization_name,
                        access_token=org_request.access_token,
                        refresh_token=org_request.refresh_token,
                        created_at=datetime.now().isoformat(),
                        last_updated=datetime.now().isoformat()
                    )
                    session.add(organization)
                
                await session.commit()
                logging.info(f"Successfully saved organization {org_request.organization_id}")
                return True
                
        except Exception as e:
            logging.error(f"Error saving organization: {str(e)}")
            logging.exception("Full exception details:")
            return False
    
    async def delete_organization(self, user_id: int, organization_id: str, is_admin: bool = False) -> dict:
        """Delete a Supabase organization and all its related connections"""
        try:
            logging.info(f"Deleting organization {organization_id} for user {user_id} (admin: {is_admin})")
            
            async with self.db.get_async_session() as session:
                async with session.begin():
                    # First, find all projects connected to this organization
                    from core.models import SupabaseConnection, Project
                    
                    # Get all connections for this organization
                    conn_stmt = select(SupabaseConnection).where(
                        SupabaseConnection.organization_id == organization_id
                    )
                    conn_result = await session.execute(conn_stmt)
                    connections = conn_result.scalars().all()
                    
                    project_ids_to_update = []
                    for connection in connections:
                        project_ids_to_update.append(connection.project_id)
                        # Delete the connection
                        await session.delete(connection)
                        logging.info(f"Deleted connection for project {connection.project_id}")
                    
                    # Update all affected projects to mark Supabase as disconnected
                    if project_ids_to_update:
                        project_stmt = select(Project).where(Project.project_id.in_(project_ids_to_update))
                        project_result = await session.execute(project_stmt)
                        projects = project_result.scalars().all()
                        
                        for project in projects:
                            project.isSupabaseConnected = False
                            project.last_updated_date = datetime.now().isoformat()
                            logging.info(f"Updated project {project.project_id} to mark Supabase as disconnected")
                    
                    # Finally, delete the organization
                    if is_admin:
                        # Admins can delete any organization
                        org_stmt = select(SupabaseOrganization).where(
                            SupabaseOrganization.organization_id == organization_id
                        )
                    else:
                        # Regular users can only delete their own organizations
                        org_stmt = select(SupabaseOrganization).where(
                            SupabaseOrganization.organization_id == organization_id,
                            SupabaseOrganization.user_id == user_id
                        )
                    
                    org_result = await session.execute(org_stmt)
                    organization = org_result.scalars().first()
                    
                    if not organization:
                        if is_admin:
                            logging.warning(f"Organization {organization_id} not found in database")
                            return {
                                "success": False,
                                "message": "Organization not found"
                            }
                        else:
                            logging.warning(f"Organization {organization_id} not found for user {user_id}")
                            return {
                                "success": False,
                                "message": "Organization not found or you don't have permission to delete it"
                            }
                    
                    await session.delete(organization)
                    logging.info(f"Deleted organization {organization_id}")
                    
                    await session.commit()
                    
                    return {
                        "success": True,
                        "message": f"Successfully deleted organization '{organization.organization_name}' and {len(connections)} related connections",
                        "deleted_connections": len(connections),
                        "updated_projects": len(project_ids_to_update)
                    }
                    
        except Exception as e:
            logging.error(f"Error deleting organization: {str(e)}")
            logging.exception("Full exception details:")
            return {
                "success": False,
                "message": f"Error deleting organization: {str(e)}"
            }
    
    async def save_connection(self, connection_data: SupabaseConnectionRequest):
        """Save Supabase connection details to database"""
        try:
            async with self.db.get_async_session() as session:
                async with session.begin():
                    # Check if connection already exists for this project
                    stmt = select(SupabaseConnection).where(
                        SupabaseConnection.project_id == connection_data.project_id
                    )
                    result = await session.execute(stmt)
                    existing_connection = result.scalar_one_or_none()
                    
                    current_time = datetime.now().isoformat()
                    
                    if existing_connection:
                        # Update existing connection
                        existing_connection.organization_id = connection_data.organization_id
                        existing_connection.supabase_project_id = connection_data.supabase_project_id
                        existing_connection.api_key = connection_data.api_key
                        existing_connection.service_role_key = connection_data.service_role_key
                        existing_connection.api_url = connection_data.api_url
                        existing_connection.database_password = connection_data.database_password
                        existing_connection.last_updated = current_time
                    else:
                        # Create new connection
                        new_connection = SupabaseConnection(
                            project_id=connection_data.project_id,
                            organization_id=connection_data.organization_id,
                            supabase_project_id=connection_data.supabase_project_id,
                            api_key=connection_data.api_key,
                            service_role_key=connection_data.service_role_key,
                            api_url=connection_data.api_url,
                            database_password=connection_data.database_password,
                            created_at=current_time,
                            last_updated=current_time
                        )
                        session.add(new_connection)
                    
                    await session.commit()
                    return True
                    
        except Exception as e:
            logging.error(f"Error saving Supabase connection: {str(e)}")
            return False
    
    async def get_connection(self, project_id: str):
        """Get Supabase connection details for a project"""
        try:
            async with self.db.get_async_session() as session:
                # Get the connection - use first() instead of scalar_one_or_none()
                conn_stmt = select(SupabaseConnection).where(
                    SupabaseConnection.project_id == project_id
                ).order_by(SupabaseConnection.last_updated.desc())  # Get the most recently updated one
                
                conn_result = await session.execute(conn_stmt)
                connection = conn_result.scalars().first()  # Use first() instead of scalar_one_or_none()
                
                if not connection:
                    logging.error(f"No Supabase connection found for project {project_id}")
                    return None
                
                # Get the organization - use first() instead of scalar_one_or_none()
                org_stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.organization_id == connection.organization_id
                ).order_by(SupabaseOrganization.last_updated.desc())  # Get the most recently updated one
                
                org_result = await session.execute(org_stmt)
                organization = org_result.scalars().first()  # Use first() instead of scalar_one_or_none()
                
                if not organization:
                    logging.error(f"No organization found for connection {connection.id}")
                    return None
                
                # Return connection details with organization info
                return {
                    "id": connection.id,
                    "project_id": connection.project_id,
                    "organization_id": connection.organization_id,
                    "organization_name": organization.organization_name,  # Get from organization
                    "supabase_project_id": connection.supabase_project_id,
                    "api_key": connection.api_key,
                    "service_role_key": connection.service_role_key,
                    "api_url": connection.api_url,
                    "database_password": connection.database_password,
                    "access_token": organization.access_token,  # Get from organization
                    "refresh_token": organization.refresh_token,  # Get from organization
                    "created_at": connection.created_at,
                    "last_updated": connection.last_updated
                }
                
        except Exception as e:
            logging.error(f"Error getting Supabase connection 2: {str(e)}")
            return None
    
    async def delete_connection(self, project_id: str) -> bool:
        """Delete Supabase connection for a project"""
        try:
            async with self.db.get_async_session() as session:
                async with session.begin():
                    # Check if connection exists
                    conn_stmt = select(SupabaseConnection).where(SupabaseConnection.project_id == project_id)
                    conn_result = await session.execute(conn_stmt)
                    connection = conn_result.scalar_one_or_none()
                    
                    if not connection:
                        logging.warning(f"No Supabase connection found for project: {project_id}")
                        return False
                    
                    # Delete connection
                    await session.delete(connection)
                    
                    # Update project to mark Supabase as disconnected
                    project_stmt = select(Project).where(Project.project_id == project_id)
                    project_result = await session.execute(project_stmt)
                    project = project_result.scalar_one_or_none()
                    
                    if project:
                        project.isSupabaseConnected = False
                        project.last_updated_date = datetime.now().isoformat()
                    
                    await session.commit()
                    return True
                    
        except Exception as e:
            logging.error(f"Error deleting Supabase connection: {str(e)}")
            return False
    
    async def create_supabase_files(self, workspace_id: str, api_key: str, api_url: str, database_password: str = None, project_id: str = None):
        """Create Supabase configuration files in the project workspace"""
        try:
            logging.info(f"Creating Supabase files in workspace {workspace_id}")
            
            # Create the supabase directory
            try:
                await env_ops.create_file(workspace_id, "/app/src/integrations/supabase", "", is_directory=True)
            except Exception as e:
                logging.error(f"Error creating supabase directory: {str(e)}")
            
            # Create client.ts file content
            client_content = f"""// This file is automatically generated. Do not edit it directly.
import {{ createClient }} from '@supabase/supabase-js';
import type {{ Database }} from './types';

const SUPABASE_URL = "{api_url}";
const SUPABASE_PUBLISHABLE_KEY = "{api_key}";

// Import the supabase client like this:
// import {{ supabase }} from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
"""
            # Check if client.ts exists and update or create accordingly
            client_path = "/app/src/integrations/supabase/client.ts"
            try:
                await env_ops.read_file_contents(workspace_id, client_path)
                # File exists, update it
                await env_ops.update_file(workspace_id, client_path, client_content)
                logging.info(f"Updated existing {client_path}")
            except Exception:
                # File doesn't exist, create it
                await env_ops.create_file(workspace_id, client_path, client_content)
                logging.info(f"Created new {client_path}")
            
            # Create .env.local file or update it with Supabase variables
            env_content = f"""
NEXT_PUBLIC_SUPABASE_URL={api_url}
NEXT_PUBLIC_SUPABASE_ANON_KEY={api_key}
"""
            
            if database_password:
                env_content += f"SUPABASE_DB_PASSWORD={database_password}\n"
            
            # Check if .env.local exists
            env_path = "/app/.env.local"
            try:
                existing_env = await env_ops.read_file_contents(workspace_id, env_path)
                logging.info("File existence check result: exists")
                
                # Parse existing variables and their line numbers
                existing_vars = {}
                env_lines = existing_env.split('\n')
                for i, line in enumerate(env_lines):
                    if line and '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        existing_vars[key.strip()] = {'value': value.strip(), 'line': i}
                
                # Update or append Supabase variables
                new_env_lines = env_lines.copy()
                supabase_vars = {
                    'NEXT_PUBLIC_SUPABASE_URL': api_url,
                    'NEXT_PUBLIC_SUPABASE_ANON_KEY': api_key
                }
                if database_password:
                    supabase_vars['SUPABASE_DB_PASSWORD'] = database_password
                
                updated = False
                for key, value in supabase_vars.items():
                    if key in existing_vars:
                        # Update existing variable
                        line_num = existing_vars[key]['line']
                        new_env_lines[line_num] = f"{key}={value}"
                        updated = True
                        logging.info(f"Updated existing variable: {key}")
                    else:
                        # Add new variable
                        if not updated:
                            # Add Supabase Configuration comment if this is the first new variable
                            new_env_lines.append("")
                            new_env_lines.append("# Supabase Configuration")
                            updated = True
                        new_env_lines.append(f"{key}={value}")
                        logging.info(f"Added new variable: {key}")
                
                # Join lines and update file
                updated_env = '\n'.join(new_env_lines)
                await env_ops.update_file(workspace_id, env_path, updated_env)
                logging.info(f"Updated {env_path} with Supabase configuration")

            except Exception as e:
                logging.info(f"File existence check result: not exists - {str(e)}")
                # File doesn't exist, create new with Supabase configuration comment
                env_content = "# Supabase Configuration\n" + env_content.lstrip()
                await env_ops.create_file(workspace_id, env_path, env_content)
                logging.info(f"Created new {env_path}")
            
            # Create a basic types.ts file
            types_content = """// This file is automatically generated. Do not edit it directly.
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      // Add your tables here
    }
    Views: {
      // Add your views here
    }
    Functions: {
      // Add your functions here
    }
    Enums: {
      // Add your enums here
    }
  }
}
"""
            # Check if types.ts exists and update or create accordingly
            types_path = "/app/src/integrations/supabase/types.ts"
            try:
                await env_ops.read_file_contents(workspace_id, types_path)
                # File exists, update it
                await env_ops.update_file(workspace_id, types_path, types_content)
                logging.info(f"Updated existing {types_path}")
            except Exception:
                # File doesn't exist, create it
                await env_ops.create_file(workspace_id, types_path, types_content)
                logging.info(f"Created new {types_path}")
            
            logging.info("Successfully created/updated Supabase files")
            return True
            
        except Exception as e:
            logging.error(f"Error creating Supabase files: {str(e)}")
            logging.exception("Full exception details:")
            return False

    async def get_supabase_projects(self, project_id: str):
        """Get Supabase projects for a SoftGen project"""
        try:
            logging.info(f"Getting Supabase projects for project {project_id}")
            
            # Get the connection for this project
            connection = await self.get_connection(project_id)
            
            if not connection:
                logging.error(f"No Supabase connection found for project {project_id}")
                return {"success": False, "message": "No Supabase connection found for this project"}
            
            # Extract tokens from the connection
            access_token = connection.get("access_token")
            refresh_token = connection.get("refresh_token")
            organization_id = connection.get("organization_id")
            organization_name = connection.get("organization_name")
            
            if not access_token or not refresh_token:
                logging.error("No access or refresh token found in connection")
                return {"success": False, "message": "No access or refresh token found"}
            
            # Get organizations
            organizations = await self.get_organizations(access_token)
            
            # If we couldn't get organizations, try to refresh the token
            if not organizations and refresh_token:
                logging.info("Failed to get organizations, trying to refresh token")
                logging.info(f"Refreshing with token: {refresh_token[:10]}...")
                
                # Refresh the token using the token manager
                new_tokens = await supabase_token_manager.refresh_token(
                    refresh_token=refresh_token,
                    refresh_url=f"{self.base_url}/v1/oauth/token",
                    client_id=self.client_id,
                    client_secret=self.client_secret
                )
                
                if new_tokens:
                    logging.info(f"Successfully refreshed token: {new_tokens}")
                    
                    # Save the new tokens to the database using the token manager
                    tokens_saved = await supabase_token_manager.save_tokens_to_db(
                        organization_id=organization_id,
                        new_tokens=new_tokens
                    )
                    
                    if tokens_saved:
                        logging.info("Successfully saved new tokens to database")
                        
                        # Try again with the new token
                        access_token = new_tokens.get("access_token")
                        logging.info(f"Getting organizations with new access token: {access_token[:10]}...")
                        organizations = await self.get_organizations(access_token)
                    else:
                        logging.error("Failed to save new tokens to database")
                else:
                    logging.error("Failed to refresh token")
            
            if not organizations:
                logging.error("Failed to get organizations even after token refresh")
                return {"success": False, "message": "Failed to get Supabase organizations"}
            
            # Find the organization that matches our connection
            organization = None
            for org in organizations:
                if org.get("id") == organization_id:
                    organization = org
                    break
            
            if not organization:
                logging.error(f"Organization {organization_id} not found in Supabase organizations")
                return {"success": False, "message": "Organization not found in Supabase"}
            
            # Get projects for this organization
            projects = await self.get_organization_projects(access_token, organization_id)
            
            if not projects:
                logging.warning(f"No projects found for organization {organization_id}")
                return {"success": False, "message": "No Supabase projects found for this organization"}
            
            return {
                "success": True,
                "organization": {
                    "id": organization_id,
                    "name": organization_name
                },
                "projects": projects,
                "project_id": project_id
            }
            
        except Exception as e:
            logging.error(f"Error getting Supabase projects: {str(e)}")
            logging.exception("Full exception details:")
            return {"success": False, "message": f"Error getting Supabase projects: {str(e)}"}

    async def get_project_api_details(self, access_token: str, project_id: str):
        """Get API details for a specific Supabase project"""
        try:
            logging.info(f"Getting API details for Supabase project {project_id}")
            
            # Check if access token is provided
            if not access_token or access_token.strip() == "":
                logging.error("Access token is empty or not provided")
                return None
            
            # First, get the project details
            project_url = f"{self.base_url}/v1/projects/{project_id}"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }
            
            logging.info(f"Making request to: {project_url}")
            
            async with httpx.AsyncClient() as client:
                project_response = await client.get(project_url, headers=headers)
                
                logging.info(f"Project details response status: {project_response.status_code}")
                
                # Check if we need to refresh the token
                if project_response.status_code in [401, 403]:
                    logging.warning("Authentication failed. Token may be expired.")
                    
                    # Try to find the organization for this project to get the refresh token
                    async with self.db.get_async_session() as session:
                        # First find the connection to get the organization_id
                        # Use first() instead of scalar_one_or_none() to avoid MultipleResultsFound error
                        conn_stmt = select(SupabaseConnection).where(
                            SupabaseConnection.supabase_project_id == project_id
                        ).order_by(SupabaseConnection.last_updated.desc())  # Get the most recently updated one
                        
                        conn_result = await session.execute(conn_stmt)
                        connection = conn_result.scalars().first()  # Use first() to get just one result
                        
                        if connection:
                            # Now get the organization to get the refresh token
                            org_stmt = select(SupabaseOrganization).where(
                                SupabaseOrganization.organization_id == connection.organization_id
                            )
                            org_result = await session.execute(org_stmt)
                            organization = org_result.scalars().first()
                            
                            if organization and organization.refresh_token:
                                logging.info("Found refresh token. Attempting to refresh access token.")
                                
                                # Use the token manager to refresh the token
                                new_tokens = await supabase_token_manager.refresh_token(
                                    refresh_token=organization.refresh_token,
                                    refresh_url=f"{self.base_url}/v1/oauth/token",
                                    client_id=self.client_id,
                                    client_secret=self.client_secret
                                )
                                
                                if new_tokens and new_tokens.get("access_token"):
                                    logging.info("Successfully refreshed token. Retrying request.")
                                    
                                    # Save the new tokens using the token manager
                                    await supabase_token_manager.save_tokens_to_db(
                                        organization_id=organization.organization_id,
                                        new_tokens=new_tokens
                                    )
                                    
                                    # Retry with new token
                                    access_token = new_tokens.get("access_token")
                                    headers["Authorization"] = f"Bearer {access_token}"
                                    project_response = await client.get(project_url, headers=headers)
                                    logging.info(f"Retry response status: {project_response.status_code}")
                
                if project_response.status_code != 200:
                    logging.error(f"Failed to get project details: {project_response.text}")
                    return None
                
                project_details = project_response.json()
                logging.info(f"Project details: {json.dumps(project_details, indent=2)}")
                
                # Always use the project ID for the API URL, not the name
                project_ref = project_id
                
                # Look for "ref" in the JWT token if available
                # The JWT tokens contain the correct project reference
                api_keys_url = f"{self.base_url}/v1/projects/{project_id}/api-keys"
                logging.info(f"Making request to: {api_keys_url}")
                
                api_keys_response = await client.get(api_keys_url, headers=headers)
                logging.info(f"API keys response status: {api_keys_response.status_code}")
                
                anon_key = None
                service_role_key = None
                
                if api_keys_response.status_code == 200:
                    api_keys = api_keys_response.json()
                    logging.info(f"API keys response: {json.dumps(api_keys, indent=2)}")
                    
                    # Extract API keys and try to get the ref from the JWT
                    for key in api_keys:
                        if key.get("name") == "anon":
                            anon_key = key.get("api_key")
                            logging.info(f"Found anon key: {anon_key[:10]}...")
                            
                            # Try to extract ref from JWT
                            try:
                                # JWT tokens have 3 parts separated by dots
                                jwt_parts = anon_key.split('.')
                                if len(jwt_parts) >= 2:
                                    # The middle part contains the payload
                                    payload = jwt_parts[1]
                                    # Add padding if needed
                                    payload += '=' * (4 - len(payload) % 4)
                                    # Decode base64
                                    decoded = base64.b64decode(payload).decode('utf-8')
                                    # Parse JSON
                                    jwt_data = json.loads(decoded)
                                    # Extract ref
                                    if 'ref' in jwt_data:
                                        project_ref = jwt_data['ref']
                                        logging.info(f"Extracted project reference from JWT: {project_ref}")
                            except Exception as e:
                                logging.warning(f"Could not extract ref from JWT: {str(e)}")
                            
                        elif key.get("name") == "service_role":
                            service_role_key = key.get("api_key")
                            logging.info(f"Found service role key: {service_role_key[:10]}...")
                else:
                    logging.error(f"Failed to get API keys: {api_keys_response.text}")
                
                # If we couldn't get the keys, use placeholders
                if not anon_key:
                    anon_key = "invalid_anon_key"
                    logging.info("Using placeholder for anon key")
                
                if not service_role_key:
                    service_role_key = "invalid_service_role_key"
                    logging.info("Using placeholder for service role key")
                
                # Construct the API URL using the project reference
                api_url = f"https://{project_ref}.supabase.co"
                logging.info(f"Constructed API URL: {api_url}")
                
                # Return the API details
                result = {
                    "anon_key": anon_key,
                    "service_role_key": service_role_key,
                    "api_url": api_url,
                    "ref": project_ref,
                    "project_id": project_id,
                    "note": "If API keys are placeholders, they need to be replaced with actual keys from your Supabase dashboard."
                }
                
                logging.info(f"Returning API details: {json.dumps(result, indent=2)}")
                return result
                
        except Exception as e:
            logging.error(f"Error getting project API details: {str(e)}")
            logging.exception("Full exception details:")
            return None

    async def connect_supabase_project(self, project_id: str, supabase_project_id: str, organization_id: str, organization_name: str, access_token: str = None, refresh_token: str = None, api_key: str = None, api_url: str = None, database_password: str = None):
        try:
            logging.info(f"Connecting Supabase project {supabase_project_id} to SoftGen project {project_id}")
            
            # If tokens are not provided, try to fetch them from the organization table instead
            if not access_token or not refresh_token:
                logging.info("Tokens not provided, attempting to fetch from organization")
                async with self.db.get_async_session() as session:
                    # Get the organization to get the tokens - use first() instead of scalar_one_or_none()
                    # to avoid MultipleResultsFound error when multiple rows exist
                    stmt = select(SupabaseOrganization).where(
                        SupabaseOrganization.organization_id == organization_id
                    ).order_by(SupabaseOrganization.last_updated.desc())  # Get the most recently updated one
                    
                    result = await session.execute(stmt)
                    organization = result.scalars().first()  # Use first() instead of scalar_one_or_none()
                    
                    if organization:
                        access_token = organization.access_token
                        refresh_token = organization.refresh_token
                        logging.info(f"Retrieved tokens from organization: access_token={access_token[:10] if access_token else 'None'}, refresh_token={refresh_token[:10] if refresh_token else 'None'}")
            
            # Validate tokens
            if not access_token:
                logging.error("Access token is empty or not provided")
                return {"success": False, "message": "Access token is required"}
            
            if not refresh_token:
                logging.error("Refresh token is empty or not provided")
                return {"success": False, "message": "Refresh token is required"}
            
            # Get API key and URL if not provided
            service_role_key = None
            if not api_key or not api_url:
                logging.info("API key or URL not provided, fetching automatically")
                api_details = await self.get_project_api_details(access_token, supabase_project_id)
                if api_details:
                    api_key = api_details.get("anon_key")
                    api_url = api_details.get("api_url")
                    service_role_key = api_details.get("service_role_key")
                    logging.info(f"Retrieved API details: api_key={api_key[:10] if api_key else 'None'}, api_url={api_url}")
                else:
                    logging.error("Failed to get API details")
                    # Continue anyway, but log the error
            
            # Check database password
            if not database_password:
                logging.info("No database password provided")
                # Continue anyway, but log the warning
            
            # Get the environment ID for the project
            env_id = await self.get_env_id_for_project(project_id)
            if not env_id:
                logging.error("Failed to get environment ID for project")
                return {"success": False, "message": "Failed to get environment ID for project"}
            
            logging.info(f"Found env_id {env_id} for project {project_id}")
            
            # Install Supabase JavaScript client
            logging.info("Installing Supabase JavaScript client...")
            install_cmd = "cd /app && npm install @supabase/supabase-js@^2.49.4 --save"
            install_result = await env_ops.execute_session(env_id, install_cmd)
            
            if not install_result["success"]:
                logging.warning(f"Failed to install Supabase JS client: {install_result['stdout']}")
                # Continue anyway as the files will still be created
            else:
                logging.info("Successfully installed Supabase JavaScript client")
            
            # Save connection to database - IMPORTANT: Preserve the tokens here!
            connection_data = SupabaseConnectionRequest(
                project_id=project_id,
                organization_id=organization_id,
                organization_name=organization_name,
                access_token=access_token,  # Preserve the original access token
                refresh_token=refresh_token,  # Preserve the original refresh token
                supabase_project_id=supabase_project_id,
                database_password=database_password,
                api_key=api_key,
                service_role_key=service_role_key if service_role_key else None,
                api_url=api_url,
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat()
            )
            
            # Create Supabase files in the project workspace
            files_created = False
            if api_key and api_url:
                files_created = await self.create_supabase_files(
                    workspace_id=env_id,  # Use env_id as workspace_id
                    api_key=api_key,
                    api_url=api_url,
                    database_password=database_password,
                    project_id=project_id
                )
                if not files_created:
                    logging.warning("Failed to create Supabase files in workspace")
            
            # Save connection to database
            connection_saved = await self.save_connection(connection_data)
            
            # Update project's isSupabaseConnected field
            status_updated = await self.update_project_supabase_status(project_id, True)
            
            return {
                "success": True,
                "message": "Successfully connected Supabase project",
                "files_created": files_created,
                "connection_saved": connection_saved,
                "status_updated": status_updated
            }
            
        except Exception as e:
            logging.error(f"Error connecting Supabase project: {str(e)}")
            logging.exception("Full exception details:")
            return {"success": False, "message": f"Error connecting Supabase project: {str(e)}"}

    async def update_project_supabase_status(self, project_id: str, is_connected: bool):
        """Update the project's isSupabaseConnected field and set onboarding_completed to False"""
        try:
            logging.info(f"Updating project {project_id} Supabase connection status to {is_connected}")
            
            # Use the db.get_async_session() method instead of db.session()
            async with self.db.get_async_session() as session:
                # Find the project
                stmt = select(Project).where(Project.project_id == project_id)
                result = await session.execute(stmt)
                project = result.scalars().first()
                
                if project:
                    # Update the isSupabaseConnected field
                    project.isSupabaseConnected = is_connected
                    # Set onboarding_completed to False when connecting Supabase
                    if is_connected:
                        project.onboarding_completed = False
                    await session.commit()
                    logging.info("Successfully updated project Supabase connection status and set onboarding_completed to False")
                    return True
                else:
                    logging.error(f"Project {project_id} not found")
                    return False
        except Exception as e:
            logging.error(f"Error updating project Supabase connection status: {str(e)}")
            logging.exception("Full exception details:")
            return False

    async def check_project_status(self, access_token: str, project_id: str) -> dict:
        """Check if a Supabase project is active, paused, or suspended"""
        try:
            logging.info(f"Checking status for Supabase project {project_id}")
            
            # Get project details from Supabase API
            project_url = f"{self.base_url}/v1/projects/{project_id}"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(project_url, headers=headers)
                
                if response.status_code == 200:
                    project_details = response.json()
                    logging.info(f"Project details: {json.dumps(project_details, indent=2)}")
                    
                    # Check for status indicators in the project details
                    status = "active"  # Default status
                    status_reason = None
                    
                    # Look for status indicators in the response
                    if "status" in project_details:
                        status = project_details["status"].lower()
                    elif "state" in project_details:
                        status = project_details["state"].lower()
                    
                    # Check for paused/suspended indicators
                    if "paused" in project_details and project_details["paused"]:
                        status = "paused"
                        status_reason = "Project is paused"
                    elif "suspended" in project_details and project_details["suspended"]:
                        status = "suspended"
                        status_reason = "Project is suspended"
                    elif "inactive" in project_details and project_details["inactive"]:
                        status = "inactive"
                        status_reason = "Project is inactive"
                    
                    # Check for billing status
                    if "billing" in project_details:
                        billing = project_details["billing"]
                        if billing.get("status") == "past_due":
                            status = "past_due"
                            status_reason = "Billing is past due"
                        elif billing.get("status") == "suspended":
                            status = "suspended"
                            status_reason = "Project suspended due to billing issues"
                    
                    # Handle INACTIVE status (which might be the same as paused)
                    if status == "inactive":
                        status = "paused"  # Treat INACTIVE as paused
                        status_reason = "Project is inactive (paused)"
                    
                    logging.info(f"Determined project status: {status} - {status_reason}")
                    
                    return {
                        "success": True,
                        "status": status,
                        "status_reason": status_reason,
                        "project_details": project_details
                    }
                elif response.status_code == 404:
                    return {
                        "success": False,
                        "status": "not_found",
                        "status_reason": "Project not found",
                        "error": "Project does not exist or access denied"
                    }
                elif response.status_code in [401, 403]:
                    # Token might be expired, return error to trigger refresh in the endpoint
                    return {
                        "success": False,
                        "status": "unauthorized",
                        "status_reason": "Authentication failed",
                        "error": "Invalid or expired access token",
                        "needs_token_refresh": True
                    }
                else:
                    return {
                        "success": False,
                        "status": "unknown",
                        "status_reason": f"HTTP {response.status_code}",
                        "error": response.text
                    }
                    
        except Exception as e:
            logging.error(f"Error checking project status: {str(e)}")
            return {
                "success": False,
                "status": "error",
                "status_reason": "Exception occurred",
                "error": str(e)
            }

    async def pause_project(self, access_token: str, project_id: str) -> dict:
        """Pause a Supabase project"""
        try:
            logging.info(f"Pausing Supabase project {project_id}")
            
            pause_url = f"{self.base_url}/v1/projects/{project_id}/pause"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(pause_url, headers=headers)
                
                if response.status_code == 200:
                    logging.info(f"Successfully paused project {project_id}")
                    return {
                        "success": True,
                        "message": "Project paused successfully"
                    }
                elif response.status_code == 403:
                    return {
                        "success": False,
                        "message": "Insufficient permissions to pause project"
                    }
                elif response.status_code in [401, 403]:
                    # Token might be expired, return error to trigger refresh in the endpoint
                    return {
                        "success": False,
                        "message": "Authentication failed - token may be expired",
                        "needs_token_refresh": True
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Failed to pause project: {response.text}"
                    }
                    
        except Exception as e:
            logging.error(f"Error pausing project: {str(e)}")
            return {
                "success": False,
                "message": f"Error pausing project: {str(e)}"
            }

    async def restore_project(self, access_token: str, project_id: str) -> dict:
        """Restore a paused Supabase project"""
        try:
            logging.info(f"Restoring Supabase project {project_id}")
            
            restore_url = f"{self.base_url}/v1/projects/{project_id}/restore"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(restore_url, headers=headers)
                
                if response.status_code == 200:
                    logging.info(f"Successfully restored project {project_id}")
                    return {
                        "success": True,
                        "message": "Project restored successfully"
                    }
                elif response.status_code == 403:
                    return {
                        "success": False,
                        "message": "Insufficient permissions to restore project"
                    }
                elif response.status_code in [401, 403]:
                    # Token might be expired, return error to trigger refresh in the endpoint
                    return {
                        "success": False,
                        "message": "Authentication failed - token may be expired",
                        "needs_token_refresh": True
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Failed to restore project: {response.text}"
                    }
                    
        except Exception as e:
            logging.error(f"Error restoring project: {str(e)}")
            return {
                "success": False,
                "message": f"Error restoring project: {str(e)}"
            }

    async def execute_sql_query(self, project_id: str, sql_query: str):
        """Execute SQL query on a connected Supabase project"""
        try:
            logging.info(f"Executing SQL query on Supabase project for SoftGen project {project_id}")
            
            async with db.get_async_session() as session:
                result = await supabase_sql_executor.execute_with_retry(
                    session=session,
                    project_id=project_id,
                    sql_query=sql_query,
                    supabase_service=self
                )
                
                return result
                
        except Exception as e:
            logging.error(f"Error executing SQL query: {str(e)}")
            logging.exception("Full exception details:")
            return {
                "success": False,
                "message": f"Error executing SQL query: {str(e)}"
            }

    async def get_env_id_for_project(self, project_id: str):
        """Get the environment ID for a project"""
        try:
            async with self.db.get_async_session() as session:
                # Get the environment for this project
                from core.models import Environment
                stmt = select(Environment).where(Environment.project_id == project_id)
                result = await session.execute(stmt)
                environment = result.scalar_one_or_none()
                
                if environment:
                    return environment.env_id
                else:
                    logging.error(f"No environment found for project {project_id}")
                    return None
        except Exception as e:
            logging.error(f"Error getting environment ID: {str(e)}")
            return None

    async def disconnect_supabase(self, project_id: str) -> dict:
        """Disconnect Supabase from a SoftGen project"""
        try:
            logging.info(f"Disconnecting Supabase from project {project_id}")
            
            # Delete the connection from the database
            connection_deleted = await self.delete_connection(project_id)
            
            if not connection_deleted:
                logging.error(f"Failed to delete Supabase connection for project {project_id}")
                return {
                    "success": False,
                    "message": "Failed to disconnect Supabase. Connection not found or could not be deleted."
                }
            
            # Update project's isSupabaseConnected field
            status_updated = await self.update_project_supabase_status(project_id, False)
            
            if not status_updated:
                logging.warning(f"Failed to update project Supabase status for project {project_id}")
            
            return {
                "success": True,
                "message": "Successfully disconnected Supabase from project",
                "connection_deleted": connection_deleted,
                "status_updated": status_updated
            }
            
        except Exception as e:
            logging.error(f"Error disconnecting Supabase: {str(e)}")
            logging.exception("Full exception details:")
            return {
                "success": False,
                "message": f"Error disconnecting Supabase: {str(e)}"
            }

# Initialize service
supabase_service = SupabaseService(db)

@router.post("/authorize")
async def authorize_supabase(
    request: SupabaseAuthorizationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Start the Supabase OAuth flow"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        logging.info(f"Starting Supabase authorization for project {request.project_id}")
        
        # Generate a state parameter that includes the project ID and user ID
        # This will be used to validate the callback and associate the tokens with the right project
        state = f"{request.project_id}_{current_user.id}_{secrets.token_urlsafe(16)}"
        
        # Generate the authorization URL
        supabase_service = SupabaseService(db)
        authorization_url = supabase_service.get_authorization_url(state)
        
        return {"authorization_url": authorization_url, "state": state}
    except Exception as e:
        logging.error(f"Error starting Supabase authorization: {str(e)}")
        logging.exception("Full exception details:")
        return {"detail": f"Error starting Supabase authorization: {str(e)}"}

@router.get("/callback")
async def supabase_callback(
    code: str = Query(None),
    state: str = Query(None),
    error: str = Query(None),
    error_description: str = Query(None)
):
    """Handle the callback from Supabase OAuth"""
    try:
        logging.info(f"Received Supabase callback with code: {code[:10]}... and state: {state}")
        
        if error:
            logging.error(f"OAuth error: {error} - {error_description}")
            return {"detail": f"OAuth error: {error_description}"}
        
        if not code:
            logging.error("No authorization code received")
            return {"detail": "No authorization code received"}
        
        # Validate state parameter
        if not state:
            logging.error("No state parameter received")
            return {"detail": "No state parameter received"}
        
        # Get the project ID and user ID from the state
        # The state should be in format "project_id_user_id_random_string"
        try:
            state_parts = state.split("_")
            if len(state_parts) < 3:
                logging.error(f"Invalid state format: {state}")
                return {"detail": "Invalid state parameter"}
            
            project_id = state_parts[0]
            user_id = state_parts[1]
            
            logging.info(f"Extracted project_id: {project_id} and user_id: {user_id} from state")
        except Exception as e:
            logging.error(f"Error parsing state parameter: {str(e)}")
            return {"detail": "Invalid state parameter"}
        
        # Exchange the code for tokens
        supabase_service = SupabaseService(db)
        tokens = await supabase_service.exchange_code_for_token(code)
        
        if not tokens or "access_token" not in tokens:
            logging.error(f"Failed to exchange code for token: {tokens}")
            return {"detail": "Failed to exchange code for token"}
        
        # Get organizations
        organizations = await supabase_service.get_organizations(tokens["access_token"])
        
        if not organizations:
            logging.error("No organizations found for user")
            return {"detail": "No organizations found for user"}
        
        # Save the organization and tokens to the database
        for org in organizations:
            org_request = SupabaseOrganizationRequest(
                user_id=int(user_id),
                organization_id=org["id"],
                organization_name=org["name"],
                access_token=tokens["access_token"],
                refresh_token=tokens["refresh_token"]
            )
            await supabase_service.save_organization(org_request)
        
        # Return success with redirect
        return HTMLResponse(content=f"""
        <!DOCTYPE html>
        <html>
            <head>
                <title>Supabase Connection Successful</title>
                <script>
                    window.onload = function() {{
                        window.opener.postMessage({{ type: 'supabase-auth-success', organizations: {json.dumps(organizations)} }}, '*');
                        setTimeout(function() {{ window.close(); }}, 1000);
                    }};
                </script>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding-top: 50px; }}
                    .success {{ color: green; font-size: 24px; margin-bottom: 20px; }}
                </style>
            </head>
            <body>
                <div class="success">✓ Connection Successful</div>
                <p>Your Supabase account has been connected successfully.</p>
                <p>This window will close automatically.</p>
            </body>
        </html>
        """, status_code=200)
        
    except Exception as e:
        logging.error(f"Error in Supabase callback: {str(e)}")
        logging.exception("Full exception details:")
        return {"detail": f"Error in Supabase callback: {str(e)}"}

@router.get("/projects/{project_id}")
async def get_supabase_projects(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get Supabase projects for a SoftGen project"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        logging.info(f"Getting Supabase projects for SoftGen project {project_id}")
        
        # Get Supabase projects
        result = await supabase_service.get_supabase_projects(project_id)
        
        return result
    except Exception as e:
        logging.error(f"Error getting Supabase projects: {str(e)}")
        logging.exception("Full exception details:")
        return {"detail": f"Error getting Supabase projects: {str(e)}"}

@router.post("/project/connect")
async def connect_supabase_project(
    request: SupabaseConnectionRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Connect a Supabase project to a SoftGen project"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        logging.info(f"Connecting Supabase project {request.supabase_project_id} to SoftGen project {request.project_id}")
        
        # Connect the Supabase project
        result = await supabase_service.connect_supabase_project(
            project_id=request.project_id,
            supabase_project_id=request.supabase_project_id,
            organization_id=request.organization_id,
            organization_name=request.organization_name,
            access_token=request.access_token,
            refresh_token=request.refresh_token,
            api_key=request.api_key,
            api_url=request.api_url,
            database_password=request.database_password
        )
        
        return result
    except Exception as e:
        logging.error(f"Error connecting Supabase project: {str(e)}")
        logging.exception("Full exception details:")
        return {"detail": f"Error connecting Supabase project: {str(e)}"}

@router.delete("/disconnect/{project_id}")
async def disconnect_supabase(
    project_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Disconnect Supabase from a SoftGen project"""
    try:
        logging.info(f"Disconnecting Supabase from project {project_id}")
        
        # Disconnect Supabase
        result = await supabase_service.disconnect_supabase(project_id)
        
        return result
    except Exception as e:
        logging.error(f"Error disconnecting Supabase: {str(e)}")
        logging.exception("Full exception details:")
        return {"detail": f"Error disconnecting Supabase: {str(e)}"}

@router.post("/project/{project_id}/sql")
async def execute_sql(
    project_id: str,
    request: dict = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """Execute SQL query on a connected Supabase project"""
    try:
        # Check if user has access to this project
        # This would be implemented based on your authorization logic
        logging.info(f"Executing SQL query on Supabase project {project_id}")
        
        sql_query = request.get("query")
        if not sql_query:
            return {"detail": "SQL query is required"}
        
        # Get the Supabase connection
        connection = await supabase_service.get_connection(project_id)
        if not connection:
            return {"detail": "No Supabase connection found for this project"}
        
        # Execute the SQL query
        result = await env_ops.execute_supabase_sql(
            access_token=connection.get("access_token"),
            project_ref=connection.get("supabase_project_id"),
            sql_query=sql_query
        )
        
        # Check if the query failed due to an expired token
        if not result.get("success") and result.get("status_code") in [401, 403]:
            logging.info("SQL query failed due to expired token. Attempting to refresh token.")
            
            # Try to refresh the token
            new_tokens = await supabase_token_manager.refresh_token(
                refresh_token=connection.get("refresh_token"),
                refresh_url=f"{supabase_service.base_url}/v1/oauth/token",
                client_id=supabase_service.client_id,
                client_secret=supabase_service.client_secret
            )
            
            if new_tokens:
                logging.info("Successfully refreshed token. Retrying SQL query.")

                await supabase_token_manager.save_tokens_to_db(
                    organization_id=connection.get("organization_id"),
                    new_tokens=new_tokens
                )
                
                # Retry the query with the new token
                result = await env_ops.execute_supabase_sql(
                    access_token=new_tokens.get("access_token"),
                    project_ref=connection.get("supabase_project_id"),
                    sql_query=sql_query
                )
        
        return result
        
    except Exception as e:
        logging.error(f"Error executing SQL query: {str(e)}")
        logging.exception("Full exception details:")
        return {"detail": f"Error executing SQL query: {str(e)}"}

@router.get("/organization/{organization_id}/projects")
async def get_organization_projects(
    organization_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get all Supabase projects for a specific organization"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        logging.info(f"Getting Supabase projects for organization {organization_id}")
        
        # Find the organization in the database to get the access token
        async with db.get_async_session() as session:
            # If user is admin, allow access to any organization
            if is_admin(current_user):
                stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.organization_id == organization_id
                )
            else:
                # Regular users can only access their own organizations
                stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.organization_id == organization_id,
                    SupabaseOrganization.user_id == current_user.id
                )
            
            result = await session.execute(stmt)
            organization = result.scalars().first()
            
            if not organization:
                if is_admin(current_user):
                    logging.error(f"Organization {organization_id} not found in database")
                    return {"success": False, "message": "Organization not found"}
                else:
                    logging.error(f"Organization {organization_id} not found for user {current_user.id}")
                    return {"success": False, "message": "Organization not found"}
            
            # Get the access token
            access_token = organization.access_token
            
            # Get projects for this organization
            projects = await supabase_service.get_organization_projects(access_token, organization_id)
            
            if not projects:
                logging.warning(f"No projects found for organization {organization_id}")
                return {"success": False, "message": "No Supabase projects found for this organization"}
            
            return {
                "success": True,
                "organization": {
                    "id": organization_id,
                    "name": organization.organization_name
                },
                "projects": projects
            }
            
    except Exception as e:
        logging.error(f"Error getting organization projects: {str(e)}")
        logging.exception("Full exception details:")
        return {"success": False, "message": f"Error getting organization projects: {str(e)}"}

@router.get("/organizations/{organization_id}")
async def get_organization_details(
    organization_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get details for a specific Supabase organization"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        logging.info(f"Getting details for organization {organization_id}")
        
        # Initialize the service
        supabase_service = SupabaseService(db)
        
        # Get the organization from the database
        async with db.get_async_session() as session:
            # If user is admin, allow access to any organization
            if is_admin(current_user):
                stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.organization_id == organization_id
                ).order_by(SupabaseOrganization.created_at.desc())
                result = await session.execute(stmt)
                organization = result.scalars().first()
                
                if not organization:
                    logging.error(f"Organization {organization_id} not found in database")
                    raise HTTPException(status_code=404, detail="Organization not found")
            else:
                # Regular users can only access their own organizations
                stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.organization_id == organization_id,
                    SupabaseOrganization.user_id == current_user.id
                ).order_by(SupabaseOrganization.created_at.desc())
                result = await session.execute(stmt)
                organization = result.scalars().first()
                
                if not organization:
                    logging.error(f"Organization {organization_id} not found for user {current_user.id}")
                    raise HTTPException(status_code=404, detail="Organization not found")
            
            # Test the connection with the organization's access token
            connection_test = await supabase_service.test_connection(organization.access_token)
            
            if not connection_test.get("connected", False):
                # Try to refresh the token
                logging.info(f"Connection test failed, trying to refresh token for organization {organization.organization_id}")
                
                new_tokens = await supabase_token_manager.refresh_token(
                    refresh_token=organization.refresh_token,
                    refresh_url=f"{supabase_service.base_url}/v1/oauth/token",
                    client_id=supabase_service.client_id,
                    client_secret=supabase_service.client_secret
                )
                
                if new_tokens:
                    # Update the organization with new tokens
                    organization.access_token = new_tokens.get("access_token")
                    organization.refresh_token = new_tokens.get("refresh_token")
                    organization.last_updated = datetime.now().isoformat()
                    await session.commit()
                    
                    # Test connection again with new token
                    connection_test = await supabase_service.test_connection(organization.access_token)
            
            # Return organization details
            return {
                "id": organization.organization_id,
                "name": organization.organization_name,
                "connected": connection_test.get("connected", False),
                "organizations": connection_test.get("organizations", []) if connection_test.get("connected", False) else []
            }
            
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logging.error(f"Error getting organization details: {str(e)}")
        logging.exception("Full exception details:")
        raise HTTPException(status_code=500, detail=f"Error getting organization details: {str(e)}")

@router.get("/organizations")
async def get_all_organizations(
    project_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user)
):
    """Get all Supabase organizations for the current user"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        logging.info(f"Getting all Supabase organizations for user {current_user.id}")
        
        # Initialize the service
        supabase_service = SupabaseService(db)
        
        # Get all organizations from the database
        async with db.get_async_session() as session:
            # If user is admin and project_id is provided, get the project owner's organizations
            if is_admin(current_user) and project_id:
                # First get the project to find the owner
                project_stmt = select(Project).where(Project.project_id == project_id)
                project_result = await session.execute(project_stmt)
                project = project_result.scalar_one_or_none()
                
                if not project:
                    return {"organizations": [], "message": "Project not found"}
                
                # Get the project owner's organizations
                stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.user_id == project.owner_id
                ).order_by(SupabaseOrganization.created_at.desc())
                result = await session.execute(stmt)
                organizations = result.scalars().all()
                
                if not organizations:
                    return {"organizations": []}
                
                # For admins viewing specific project owner's organizations
                return {
                    "organizations": [
                        {
                            "id": org.organization_id,
                            "name": org.organization_name,
                            "user_id": org.user_id
                        } for org in organizations
                    ]
                }
            elif is_admin(current_user):
                # If admin but no project_id, return empty (or could return all, but you said don't return all)
                return {"organizations": []}
            else:
                # Regular users can only see their own organizations
                stmt = select(SupabaseOrganization).where(
                    SupabaseOrganization.user_id == current_user.id
                ).order_by(SupabaseOrganization.created_at.desc())
                result = await session.execute(stmt)
                organizations = result.scalars().all()
                
                if not organizations:
                    # Try to get organizations from Supabase API using the most recent token
                    # This is a fallback in case we don't have any organizations in our database
                    return {"organizations": []}
            
            # Test the connection with the most recent organization's access token
            most_recent_org = organizations[0]
            connection_test = await supabase_service.test_connection(most_recent_org.access_token)
            
            if connection_test.get("connected", False):
                # Return the organizations from the API
                return {
                    "organizations": connection_test.get("organizations", [])
                }
            else:
                # Try to refresh the token
                logging.info(f"Connection test failed, trying to refresh token for organization {most_recent_org.organization_id}")
                
                new_tokens = await supabase_token_manager.refresh_token(
                    refresh_token=most_recent_org.refresh_token,
                    refresh_url=f"{supabase_service.base_url}/v1/oauth/token",
                    client_id=supabase_service.client_id,
                    client_secret=supabase_service.client_secret
                )
                
                if new_tokens:
                    # Update the organization with new tokens
                    most_recent_org.access_token = new_tokens.get("access_token")
                    most_recent_org.refresh_token = new_tokens.get("refresh_token")
                    most_recent_org.last_updated = datetime.now().isoformat()
                    await session.commit()
                    
                    # Test connection again with new token
                    connection_test = await supabase_service.test_connection(most_recent_org.access_token)
                    
                    if connection_test.get("connected", False):
                        # Return the organizations from the API
                        return {
                            "organizations": connection_test.get("organizations", [])
                        }
            
            # If we couldn't get organizations from the API, return the ones from our database
            return {
                "organizations": [
                    {
                        "id": org.organization_id,
                        "name": org.organization_name
                    } for org in organizations
                ]
            }
            
    except Exception as e:
        logging.error(f"Error getting all organizations: {str(e)}")
        logging.exception("Full exception details:")
        raise HTTPException(status_code=500, detail=f"Error getting all organizations: {str(e)}") 

@router.delete("/organizations/{organization_id}")
async def delete_supabase_organization(
    organization_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a Supabase organization and all its related connections"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        logging.info(f"Deleting Supabase organization {organization_id} for user {current_user.id}")
        
        # Delete the organization and all related connections
        result = await supabase_service.delete_organization(
            user_id=current_user.id,
            organization_id=organization_id,
            is_admin=is_admin(current_user)
        )
        
        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("message", "Failed to delete organization"))
        
        return result
        
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logging.error(f"Error deleting Supabase organization: {str(e)}")
        logging.exception("Full exception details:")
        raise HTTPException(status_code=500, detail=f"Error deleting Supabase organization: {str(e)}")

@router.post("/project/status")
async def check_supabase_project_status(
    request: dict = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """Check the status of a Supabase project directly using project ID"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        supabase_project_id = request.get("supabase_project_id")
        
        if not supabase_project_id:
            return {
                "success": False,
                "message": "supabase_project_id is required"
            }
        
        # Get the user's most recent Supabase organization to get the access token
        async with db.get_async_session() as session:
            stmt = select(SupabaseOrganization).where(
                SupabaseOrganization.user_id == current_user.id
            ).order_by(SupabaseOrganization.last_updated.desc())
            result = await session.execute(stmt)
            organization = result.scalars().first()
            
            if not organization:
                return {
                    "success": False,
                    "message": "No Supabase organization found for this user"
                }
            
            access_token = organization.access_token
        
        logging.info(f"Checking status for Supabase project {supabase_project_id}")
        
        # Check the project status directly
        status_result = await supabase_service.check_project_status(
            access_token=access_token,
            project_id=supabase_project_id
        )
        
        # Check if the request failed due to expired token
        if not status_result.get("success") and status_result.get("needs_token_refresh"):
            status_result = await handle_token_refresh_and_retry(
                organization,
                supabase_service.check_project_status,
                access_token=access_token,
                project_id=supabase_project_id
            )
        
        return status_result
        
    except Exception as e:
        logging.error(f"Error checking Supabase project status: {str(e)}")
        logging.exception("Full exception details:")
        return {
            "success": False,
            "message": f"Error checking Supabase project status: {str(e)}"
        }

@router.post("/project/restore")
async def restore_supabase_project(
    request: dict = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """Restore a paused Supabase project directly using project ID"""
    try:
        # Check if user is on free tier
        if current_user.plan == "free-tier":
            raise HTTPException(status_code=403, detail="Supabase features are not available on the free tier plan")

        supabase_project_id = request.get("supabase_project_id")
        
        if not supabase_project_id:
            return {
                "success": False,
                "message": "supabase_project_id is required"
            }
        
        # Get the user's most recent Supabase organization to get the access token
        async with db.get_async_session() as session:
            stmt = select(SupabaseOrganization).where(
                SupabaseOrganization.user_id == current_user.id
            ).order_by(SupabaseOrganization.last_updated.desc())
            result = await session.execute(stmt)
            organization = result.scalars().first()
            
            if not organization:
                return {
                    "success": False,
                    "message": "No Supabase organization found for this user"
                }
            
            access_token = organization.access_token
        
        logging.info(f"Restoring Supabase project {supabase_project_id}")
        
        # Restore the project directly
        result = await supabase_service.restore_project(
            access_token=access_token,
            project_id=supabase_project_id
        )
        
        # Check if the request failed due to expired token
        if not result.get("success") and result.get("needs_token_refresh"):
            result = await handle_token_refresh_and_retry(
                organization,
                supabase_service.restore_project,
                access_token=access_token,
                project_id=supabase_project_id
            )
        
        return result
        
    except Exception as e:
        logging.error(f"Error restoring Supabase project: {str(e)}")
        logging.exception("Full exception details:")
        return {
            "success": False,
            "message": f"Error restoring Supabase project: {str(e)}"
        } 

async def handle_token_refresh_and_retry(organization, operation_func, *args, **kwargs):
    """Helper function to handle token refresh and retry operations"""
    logging.info("Operation failed due to expired token. Attempting to refresh token.")
    
    # Try to refresh the token
    new_tokens = await supabase_token_manager.refresh_token(
        refresh_token=organization.refresh_token,
        refresh_url=f"{supabase_service.base_url}/v1/oauth/token",
        client_id=supabase_service.client_id,
        client_secret=supabase_service.client_secret
    )
    
    if new_tokens:
        logging.info("Successfully refreshed token. Retrying operation.")
        
        # Save the new tokens to the database using the token manager
        tokens_saved = await supabase_token_manager.save_tokens_to_db(
            organization_id=organization.organization_id,
            new_tokens=new_tokens
        )
        
        if tokens_saved:
            # Update kwargs to use new access token
            if 'access_token' in kwargs:
                kwargs['access_token'] = new_tokens.get("access_token")
            # Retry the operation with the new token
            return await operation_func(*args, **kwargs)
        else:
            logging.error("Failed to save new tokens to database")
            return {
                "success": False,
                "message": "Failed to refresh authentication token. Please reconnect your Supabase account."
            }
    else:
        logging.error("Failed to refresh token")
        return {
            "success": False,
            "message": "Failed to refresh authentication token. Please reconnect your Supabase account."
        } 