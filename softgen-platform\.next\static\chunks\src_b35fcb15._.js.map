{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/query-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { useState, type ReactNode } from \"react\";\r\n\r\nexport const CACHE_PARAMS = {\r\n  staleTime: 1000 * 60 * 5, // Cache for 5 minutes\r\n  gcTime: 1000 * 60 * 10, // Keep in cache for 10 minutes\r\n}\r\n\r\nexport function QueryProvider({ children }: { children: ReactNode }) {\r\n  const [queryClient] = useState(() => new QueryClient({\r\n    defaultOptions: {\r\n      queries: {\r\n        refetchOnWindowFocus: false,\r\n        refetchOnReconnect: false,\r\n      },\r\n    },\r\n  }));\r\n\r\n  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;;;AAHA;;;AAKO,MAAM,eAAe;IAC1B,WAAW,OAAO,KAAK;IACvB,QAAQ,OAAO,KAAK;AACtB;AAEO,SAAS,cAAc,EAAE,QAAQ,EAA2B;;IACjE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD;kCAAE,IAAM,IAAI,yPAAA,CAAA,cAAW,CAAC;gBACnD,gBAAgB;oBACd,SAAS;wBACP,sBAAsB;wBACtB,oBAAoB;oBACtB;gBACF;YACF;;IAEA,qBAAO,4SAAC,yRAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAAc;;;;;;AACpD;GAXgB;KAAA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/app/%28main%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AuthProvider } from \"@/providers/auth-provider\";\r\nimport { QueryProvider } from \"@/providers/query-provider\";\r\nimport NextTopLoader from \"nextjs-toploader\";\r\nimport React, { Suspense } from \"react\";\r\n\r\ntype Props = {\r\n  children: React.ReactNode;\r\n};\r\n\r\nconst Layout = (props: Props) => {\r\n  return (\r\n    <AuthProvider>\r\n      <QueryProvider>\r\n        <Suspense\r\n          fallback={\r\n            <div className=\"flex h-screen items-center justify-center\">\r\n              <div className=\"flex h-fit w-fit items-center justify-center\">\r\n                <div className=\"relative flex h-auto w-fit animate-pulse items-center justify-center\">\r\n                  <svg\r\n                    width=\"84\"\r\n                    height=\"84\"\r\n                    viewBox=\"0 0 120 120\"\r\n                    fill=\"none\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"text-primary\"\r\n                  >\r\n                    <g clipPath=\"url(#clip0_1503_305)\">\r\n                      <path\r\n                        d=\"M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z\"\r\n                        fill=\"currentColor\"\r\n                      />\r\n                      <path\r\n                        d=\"M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z\"\r\n                        fill=\"currentColor\"\r\n                      />\r\n                      <path\r\n                        d=\"M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z\"\r\n                        fill=\"currentColor\"\r\n                      />\r\n                      <path\r\n                        d=\"M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z\"\r\n                        fill=\"currentColor\"\r\n                      />\r\n                    </g>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          }\r\n        >\r\n          <NextTopLoader\r\n            color=\"#0C97B0\"\r\n            initialPosition={0.08}\r\n            crawlSpeed={200}\r\n            height={2.5}\r\n            crawl={true}\r\n            showSpinner={false}\r\n            easing=\"ease\"\r\n            speed={200}\r\n            shadow=\"0 0 15px #0C97B080, 0 0 30px #0C97B080\"\r\n            template='<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div>'\r\n            zIndex={1600}\r\n            showAtBottom={false}\r\n          />\r\n\r\n          {props.children}\r\n        </Suspense>\r\n      </QueryProvider>\r\n    </AuthProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWA,MAAM,SAAS,CAAC;IACd,qBACE,4SAAC,wIAAA,CAAA,eAAY;kBACX,cAAA,4SAAC,yIAAA,CAAA,gBAAa;sBACZ,cAAA,4SAAC,4QAAA,CAAA,WAAQ;gBACP,wBACE,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCACC,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,OAAM;gCACN,WAAU;0CAEV,cAAA,4SAAC;oCAAE,UAAS;;sDACV,4SAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,4SAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,4SAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,4SAAC;4CACC,GAAE;4CACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnB,4SAAC,6PAAA,CAAA,UAAa;wBACZ,OAAM;wBACN,iBAAiB;wBACjB,YAAY;wBACZ,QAAQ;wBACR,OAAO;wBACP,aAAa;wBACb,QAAO;wBACP,OAAO;wBACP,QAAO;wBACP,UAAS;wBACT,QAAQ;wBACR,cAAc;;;;;;oBAGf,MAAM,QAAQ;;;;;;;;;;;;;;;;;AAKzB;KA7DM;uCA+DS", "debugId": null}}]}