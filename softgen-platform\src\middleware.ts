import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  const access_token = request.cookies.get("access_token")?.value;
  const kinde_id = request.cookies.get("kinde_id")?.value;

  // Detect country from Vercel geolocation headers
  const detectedCountry = request.headers.get("x-vercel-ip-country") || "US";
  const currentCountryCookie = request.cookies.get("detected_country")?.value;

  const privatePathPatterns = [
    /^\/app($|\/.*)/,
    /^\/project($|\/.*)/,
    /^\/settings($|\/.*)/,
    /^\/profile($|\/.*)/,
    /^\/integration($|\/.*)/,
    /^\/test\/[^\/]+$/,
    /^\/admin($|\/.*)/,
  ];

  const isPrivatePath = privatePathPatterns.some((pattern) => pattern.test(path));

  if (!access_token && isPrivatePath) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  if (access_token && ["/signin", "/register"].includes(path)) {
    return NextResponse.redirect(new URL("/app", request.url));
  }

  if (access_token && isPrivatePath) {
    if (!kinde_id) {
      return NextResponse.redirect(new URL("/pricing", request.url));
    }

    try {
      const apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/kinde_user/${kinde_id}`;

      const response = await fetch(apiUrl, {
        cache: "no-store",
        headers: {
          Authorization: `Bearer ${access_token}`,
          "Content-Type": "application/json",
        },
        mode: "cors",
        credentials: "include",
      });

      if (!response.ok) {
        return NextResponse.redirect(new URL("/pricing", request.url));
      }

      const userData = await response.json();

      if (!userData.plan && userData.total_free_request <= 0) {
        return NextResponse.redirect(new URL("/pricing", request.url));
      }
    } catch (error: unknown) {
      console.error("error", error);
      // return NextResponse.redirect(new URL("/pricing", request.url));
    }
  }

  // Set country cookie if not already set or if country has changed
  const response = NextResponse.next();

  if (!currentCountryCookie || currentCountryCookie !== detectedCountry) {
    response.cookies.set("detected_country", detectedCountry, {
      httpOnly: false, // Allow client-side access
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });
  }

  return response;
}

export const config = {
  matcher: ["/((?!_next|api|static|favicon.ico).*)"],
};
