module.exports = {

"[project]/src/lib/admin.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isAdminEmail": (()=>isAdminEmail)
});
const LEGACY_ADMIN_EMAILS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
];
const ADMIN_DOMAINS = [
    "softgen.ai"
];
const isAdminEmail = (email)=>{
    if (!email) {
        return false;
    }
    const normalizedEmail = email.toLowerCase().trim();
    const isLegacyAdmin = LEGACY_ADMIN_EMAILS.some((adminEmail)=>adminEmail.toLowerCase() === normalizedEmail);
    if (isLegacyAdmin) {
        return true;
    }
    const isDomainAdmin = ADMIN_DOMAINS.some((domain)=>normalizedEmail.endsWith(`@${domain}`));
    if (isDomainAdmin) {
        return true;
    }
    return false;
};
}}),
"[project]/src/hooks/use-project-access.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useProjectAccess": (()=>useProjectAccess)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$admin$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/admin.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/auth-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
const useProjectAccess = ()=>{
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$auth$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const checkProjectAccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((project)=>{
        if (!project || !user?.userFromDb?.email) return false;
        const isTeam = project.team_emails?.includes(user.userFromDb.email) || false;
        const isUser = project.owner_id === user.userFromDb.id;
        const isAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$admin$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAdminEmail"])(user.userFromDb.email);
        return isAdmin || isTeam || isUser;
    }, [
        user
    ]);
    return {
        checkProjectAccess
    };
};
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/providers/project-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProjectProvider": (()=>ProjectProvider),
    "SandBoxState": (()=>SandBoxState),
    "useProject": (()=>useProject)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$project$2d$access$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-project-access.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nextjs$2d$toploader$40$3$2e$8$2e$16_nex_091463b2df07a4c1f7325f9db0455050$2f$node_modules$2f$nextjs$2d$toploader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/nextjs-toploader@3.8.16_nex_091463b2df07a4c1f7325f9db0455050/node_modules/nextjs-toploader/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/query-provider.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
const SandBoxState = {
    CREATING: "creating",
    RESTORING: "restoring",
    DESTROYED: "destroyed",
    DESTROYING: "destroying",
    STARTED: "started",
    STOPPED: "stopped",
    STARTING: "starting",
    STOPPING: "stopping",
    ERROR: "error",
    BUILD_FAILED: "build_failed",
    PENDING_BUILD: "pending_build",
    BUILDING_SNAPSHOT: "building_snapshot",
    UNKNOWN: "unknown",
    PULLING_SNAPSHOT: "pulling_snapshot",
    ARCHIVING: "archiving",
    ARCHIVED: "archived"
};
const ProjectContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
const ProjectProvider = ({ children, projectId })=>{
    const MAX_NO_DOMAIN_RETRIES = 15;
    const { data: rawProject, isLoading: isProjectLoading, error, refetch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "get-project",
            projectId
        ],
        queryFn: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProject"])(projectId),
        retry: (count, error)=>{
            if (error?.detail?.includes("Not authorized to access this project")) return false;
            return count < 1;
        },
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_PARAMS"],
        refetchInterval: (query)=>{
            if (query.state.data?.isRunning === 0 && query.state.dataUpdateCount < MAX_NO_DOMAIN_RETRIES) {
                return 1000 * 5;
            }
            return 30000;
        }
    });
    const loader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nextjs$2d$toploader$40$3$2e$8$2e$16_nex_091463b2df07a4c1f7325f9db0455050$2f$node_modules$2f$nextjs$2d$toploader$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTopLoader"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isProjectLoading) {
            loader.start();
        } else {
            loader.done();
        }
        return ()=>{
            loader.done();
        };
    }, [
        isProjectLoading
    ]);
    const project = rawProject ? {
        ...rawProject,
        team_emails: parseData(rawProject.team_emails),
        tech_stack_prompt: parseData(rawProject.tech_stack_prompt),
        users_prompt_Array: parseData(rawProject.users_prompt_Array)
    } : null;
    const { checkProjectAccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$project$2d$access$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useProjectAccess"])();
    const hasAccess = checkProjectAccess(project);
    const startAttemptRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const startEnvironment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCheckAndStart"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (hasAccess && rawProject?.project_id) {
            startEnvironment.mutate(rawProject.project_id, {
                onSettled: ()=>{
                    refetch().finally(()=>{
                        startAttemptRef.current = true;
                    });
                }
            });
        }
    }, [
        hasAccess,
        rawProject?.project_id
    ]);
    const { data: threads = [], isLoading: isLoadingThreads } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "threads",
            projectId
        ],
        queryFn: async ()=>{
            try {
                const threads = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProjectThreads"])(projectId);
                return threads.reverse();
            } catch (error) {
                if (error?.detail === "No threads found for this project") {
                    return [];
                }
                throw error;
            }
        },
        enabled: !isProjectLoading && hasAccess,
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$query$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CACHE_PARAMS"]
    });
    if (isProjectLoading) return null;
    const hasReadAccess = !error?.detail?.includes("Not authorized to access this project");
    const projectNotFound = error?.detail?.includes("Project not found");
    if (!hasReadAccess || projectNotFound) return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["notFound"])();
    // no start attempt or sandbox is starting or restoring
    const sandboxStarting = !startAttemptRef.current || project?.sandbox_state === SandBoxState.STARTING || project?.sandbox_state === SandBoxState.RESTORING;
    const isSandboxSleeping = startAttemptRef.current && (project?.sandbox_state === SandBoxState.STOPPED || project?.sandbox_state === SandBoxState.ARCHIVED);
    const sandboxState = sandboxStarting ? "starting" : isSandboxSleeping ? "sleeping" : project?.sandbox_state === SandBoxState.ERROR || // @ts-expect-error error type is not defined
    startEnvironment.error?.error === "workspace_unavailable" ? "unavailable" : "idle";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ProjectContext.Provider, {
        value: {
            projectId,
            isProjectLoading,
            project,
            threads,
            isLoadingThreads,
            hasAccess,
            refetch,
            sandboxState
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/project-provider.tsx",
        lineNumber: 228,
        columnNumber: 5
    }, this);
};
function parseData(data) {
    try {
        return JSON.parse(data);
    } catch (error) {
        console.error("Failed to parse data:", error);
        return [];
    }
}
const useProject = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ProjectContext);
    if (!context) {
        throw new Error("useProjectContext must be used within a ProjectProvider");
    }
    return context;
};
}}),
"[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$slot$40$1$2e$2$2e$3_$40$types$2b$react$40$19$2e$1$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$class$2d$variance$2d$authority$40$0$2e$7$2e$1$2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$class$2d$variance$2d$authority$40$0$2e$7$2e$1$2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white  hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline",
            "outline-primary": "border border-primary/20 dark:border-primary/15 bg-accent/80 hover:bg-accent/90 text-primary/80 hover:text-primary",
            success: "border border-emerald-800 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-800",
            info: "border border-blue-800 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800",
            invert: "w-fit dark:bg-[#0a0a0a] dark:hover:bg-[#0a0a0a]/95 dark:text-[#fafafa] bg-[#fafafa] hover:bg-[#fafafa]/95 text-[#0a0a0a]",
            "invert-outline-primary": "border border-background/10 bg-primary/90 hover:bg-primary text-background hover:text-background",
            transparent: "bg-transparent hover:bg-transparent text-primary"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-lg px-6 has-[>svg]:px-4",
            icon: "size-8"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$slot$40$1$2e$2$2e$3_$40$types$2b$react$40$19$2e$1$2e$8_react$40$19$2e$1$2e$0$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/src/components/ui/loading.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
const Loading = ({ className })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("size-5 animate-spin text-primary", className),
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 24 24",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                className: "opacity-25",
                cx: "12",
                cy: "12",
                r: "10",
                stroke: "currentColor",
                strokeWidth: "4"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                className: "opacity-75",
                fill: "currentColor",
                d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/loading.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Loading;
}}),
"[project]/src/features/global/toast.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "congratsToast": (()=>congratsToast),
    "copyToast": (()=>copyToast),
    "errorToast": (()=>errorToast),
    "infoToast": (()=>infoToast),
    "loadingToast": (()=>loadingToast),
    "notificationToast": (()=>notificationToast),
    "successToast": (()=>successToast),
    "warningToast": (()=>warningToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/loading.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/Bell.js [app-ssr] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Confetti$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Confetti$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/Confetti.js [app-ssr] (ecmascript) <export default as Confetti>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$X$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@mynaui+icons-react@0.3.9_react@19.1.0/node_modules/@mynaui/icons-react/dist/esm/icons/X.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@1.7.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
const DEFAULT_DURATION = 3000;
const DEFAULT_POSITION = "bottom-right";
const successToast = (message, options)=>{
    const isMobile = window.innerWidth <= 768;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].custom((t)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex grow items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "mt-0.5 text-[#56eda1]",
                                strokeWidth: 1.2,
                                "aria-hidden": "true",
                                width: "24",
                                height: "24",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                xmlns: "http://www.w3.org/2000/svg",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 48,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "m8.667 12.633 1.505 1.721a1 1 0 0 0 1.564-.073L15.333 9.3"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 49,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 35,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex grow items-center justify-between gap-12",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-base font-medium text-primary/80",
                                                children: message
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 53,
                                                columnNumber: 17
                                            }, this),
                                            options?.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-primary/70",
                                                children: options.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 55,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 52,
                                        columnNumber: 15
                                    }, this),
                                    options?.action && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "whitespace-nowrap text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: options.action.onClick,
                                                children: options.action.label
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 60,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "mx-1 text-primary/80",
                                                children: "·"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 66,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                                                children: "Dismiss"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 67,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 59,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 51,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 34,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "ghost",
                        size: "icon",
                        className: "-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0",
                        onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                        "aria-label": "Close notification",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$X$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            size: 16,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/features/global/toast.tsx",
                            lineNumber: 84,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 77,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/global/toast.tsx",
                lineNumber: 33,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 32,
            columnNumber: 7
        }, this), {
        duration: options?.duration || DEFAULT_DURATION,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION
    });
};
const loadingToast = (message, promise, options, successMessage)=>{
    const isMobile = window.innerWidth <= 768;
    const toastId = Math.random().toString(36).substring(2, 9);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].custom((t)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex grow items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "mt-0.5 size-4 animate-spin text-primary"
                            }, void 0, false, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 110,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex grow items-center justify-between gap-12",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-base font-medium text-primary/80",
                                                children: message
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 113,
                                                columnNumber: 17
                                            }, this),
                                            options?.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-primary/70",
                                                children: options.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 115,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 112,
                                        columnNumber: 15
                                    }, this),
                                    options?.action && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "whitespace-nowrap text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: options.action.onClick,
                                                children: options.action.label
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 120,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "mx-1 text-primary/80",
                                                children: "·"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 126,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                                                children: "Dismiss"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 127,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 119,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 111,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 109,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "ghost",
                        size: "icon",
                        className: "-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0",
                        onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                        "aria-label": "Close notification",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$X$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            size: 16,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/features/global/toast.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 137,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/global/toast.tsx",
                lineNumber: 108,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 107,
            columnNumber: 7
        }, this), {
        id: toastId,
        duration: Infinity,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION
    });
    return promise.then((data)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(toastId);
        successToast(successMessage || "Completed", options);
        return data;
    }, (error)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(toastId);
        errorToast(error?.message || "An error occurred", options);
        throw error;
    });
};
const errorToast = (message, options)=>{
    const isMobile = window.innerWidth <= 768;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].custom((t)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]"),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex grow items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "mt-0.5 text-red-500",
                                strokeWidth: 1.2,
                                "aria-hidden": "true",
                                width: "24",
                                height: "24",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                xmlns: "http://www.w3.org/2000/svg",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 195,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "m12 8 .01 4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 196,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M12 16h.01"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 197,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 182,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex grow items-center justify-between gap-12",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-base font-medium text-primary/80",
                                                children: message
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 201,
                                                columnNumber: 17
                                            }, this),
                                            options?.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-primary/70",
                                                children: options.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 203,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 200,
                                        columnNumber: 15
                                    }, this),
                                    options?.action && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "whitespace-nowrap text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: options.action.onClick,
                                                children: options.action.label
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 208,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "mx-1 text-primary/80",
                                                children: "·"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 214,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                                                children: "Dismiss"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 215,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 207,
                                        columnNumber: 17
                                    }, this),
                                    options?.button && options.button
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 199,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 181,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "ghost",
                        size: "icon",
                        className: "-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0",
                        onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                        "aria-label": "Close notification",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$X$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            size: 16,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/features/global/toast.tsx",
                            lineNumber: 233,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 226,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/global/toast.tsx",
                lineNumber: 180,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 175,
            columnNumber: 7
        }, this), {
        duration: options?.duration || DEFAULT_DURATION,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION
    });
};
const infoToast = (message, options)=>{
    const isMobile = window.innerWidth <= 768;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].custom((t)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex grow items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "mt-0.5 text-blue-500",
                                width: "24",
                                height: "24",
                                fill: "none",
                                stroke: "currentColor",
                                strokeWidth: "1.5",
                                viewBox: "0 0 24 24",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                xmlns: "http://www.w3.org/2000/svg",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 265,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M12 16v-5h-.5m0 5h1M12 8.5V8"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 266,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 253,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex grow items-center justify-between gap-12",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-base font-medium text-muted-foreground",
                                                children: message
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 270,
                                                columnNumber: 17
                                            }, this),
                                            options?.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-primary/70",
                                                children: options.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 272,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 269,
                                        columnNumber: 15
                                    }, this),
                                    options?.action && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "whitespace-nowrap text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: options.action.onClick,
                                                children: options.action.label
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 277,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "mx-1 text-muted-foreground",
                                                children: "·"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 283,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                                                children: "Dismiss"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 284,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 276,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 268,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 252,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "ghost",
                        size: "icon",
                        className: "-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0",
                        onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                        "aria-label": "Close notification",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$X$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            size: 16,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/features/global/toast.tsx",
                            lineNumber: 301,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 294,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/global/toast.tsx",
                lineNumber: 251,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 250,
            columnNumber: 7
        }, this), {
        duration: options?.duration || DEFAULT_DURATION,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION
    });
};
const warningToast = (message, options)=>{
    const isMobile = window.innerWidth <= 768;
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].custom((t)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full rounded-2xl border border-border/70 bg-background px-4 py-3 text-foreground shadow-lg sm:w-[var(--width)]",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex grow items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "mt-0.5 text-yellow-500",
                                width: "24",
                                height: "24",
                                fill: "none",
                                stroke: "currentColor",
                                strokeWidth: "1.5",
                                viewBox: "0 0 24 24",
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                xmlns: "http://www.w3.org/2000/svg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M12 8.5V14m0 3.247v-.5m-6.02-5.985C8.608 5.587 9.92 3 12 3s3.393 2.587 6.02 7.762l.327.644c2.182 4.3 3.274 6.45 2.287 8.022C19.648 21 17.208 21 12.327 21h-.654c-4.88 0-7.321 0-8.307-1.572s.105-3.722 2.287-8.022z"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/global/toast.tsx",
                                    lineNumber: 333,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 321,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex grow items-center justify-between gap-12",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-base font-medium text-primary/80",
                                                children: message
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 337,
                                                columnNumber: 17
                                            }, this),
                                            options?.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-primary/70",
                                                children: options.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 339,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 336,
                                        columnNumber: 15
                                    }, this),
                                    options?.action && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "whitespace-nowrap text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: options.action.onClick,
                                                children: options.action.label
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 344,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "mx-1 text-primary/80",
                                                children: "·"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 350,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "text-sm font-medium text-primary hover:underline",
                                                onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                                                children: "Dismiss"
                                            }, void 0, false, {
                                                fileName: "[project]/src/features/global/toast.tsx",
                                                lineNumber: 351,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/features/global/toast.tsx",
                                        lineNumber: 343,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/global/toast.tsx",
                                lineNumber: 335,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 320,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "ghost",
                        size: "icon",
                        className: "-my-1.5 -mr-1 h-7 w-7 shrink-0 cursor-auto p-0",
                        onClick: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].dismiss(t),
                        "aria-label": "Close notification",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$X$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                            size: 16,
                            "aria-hidden": "true"
                        }, void 0, false, {
                            fileName: "[project]/src/features/global/toast.tsx",
                            lineNumber: 368,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/global/toast.tsx",
                        lineNumber: 361,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/global/toast.tsx",
                lineNumber: 319,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 318,
            columnNumber: 7
        }, this), {
        duration: options?.duration || DEFAULT_DURATION,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION
    });
};
const copyToast = (message = "Copied to clipboard", options)=>{
    const isMobile = window.innerWidth <= 768;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"])(message, {
        description: options?.description,
        duration: options?.duration || DEFAULT_DURATION,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION,
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            width: "16",
            height: "16",
            fill: "none",
            "aria-hidden": "true",
            viewBox: "0 0 16 16",
            className: "h-5 w-5 font-semibold",
            strokeWidth: 1.2,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fill: "#10B981",
                d: "M14.548 3.488a.75.75 0 0 1-.036 1.06l-8.572 8a.75.75 0 0 1-1.023 0l-3.429-3.2a.75.75 0 0 1 1.024-1.096l2.917 2.722 8.06-7.522a.75.75 0 0 1 1.06.036Z"
            }, void 0, false, {
                fileName: "[project]/src/features/global/toast.tsx",
                lineNumber: 397,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 387,
            columnNumber: 7
        }, this),
        style: {
            backgroundColor: "hsl(var(--background))",
            color: "hsl(var(--primary))",
            border: "2px solid hsl(var(--border) / 0.7)"
        },
        className: "rounded-2xl border-2 border-border/80 py-2.5  shadow-lg flex items-center gap-2 text-sm font-medium",
        action: options?.action
    });
};
const notificationToast = (message, options)=>{
    const isMobile = window.innerWidth <= 768;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"])(message, {
        description: options?.description,
        duration: options?.duration || DEFAULT_DURATION,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION,
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Bell$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("h-5 w-5 font-semibold", options?.description && "mt-3"),
            size: 16,
            strokeWidth: 1.2
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 421,
            columnNumber: 7
        }, this),
        style: {
            backgroundColor: "rgb(88, 28, 135)",
            color: "hsl(var(--primary))",
            border: "2px solid hsl(var(--border) / 0.7)"
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("rounded-2xl shadow-lg flex items-center gap-3 py-2 text-sm font-medium justify-start", options?.description && "items-start justify-start"),
        action: options?.action
    });
};
const congratsToast = (message, options)=>{
    const isMobile = window.innerWidth <= 768;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"])(message, {
        description: options?.description,
        duration: options?.duration || DEFAULT_DURATION,
        position: isMobile ? "top-center" : options?.position || DEFAULT_POSITION,
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$mynaui$2b$icons$2d$react$40$0$2e$3$2e$9_react$40$19$2e$1$2e$0$2f$node_modules$2f40$mynaui$2f$icons$2d$react$2f$dist$2f$esm$2f$icons$2f$Confetti$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Confetti$3e$__["Confetti"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("h-5 w-5 font-semibold", options?.description && "mt-3"),
            size: 16,
            strokeWidth: 1.2
        }, void 0, false, {
            fileName: "[project]/src/features/global/toast.tsx",
            lineNumber: 447,
            columnNumber: 7
        }, this),
        style: {
            backgroundColor: "#004014",
            color: "#56eda1",
            border: "none"
        },
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("rounded-2xl py-3 px-4  shadow-lg flex items-center gap-2 text-sm font-medium", options?.description && "items-start justify-start"),
        action: options?.action
    });
};
}}),
"[project]/src/stores/current-thread.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useCurrentThread": (()=>useCurrentThread),
    "useCurrentThreadId": (()=>useCurrentThreadId),
    "useCurrentThreadStore": (()=>useCurrentThreadStore),
    "useIsAgentRunning": (()=>useIsAgentRunning),
    "useResetCurrentThread": (()=>useResetCurrentThread),
    "useSelectedSectionAction": (()=>useSelectedSectionAction),
    "useSubscribeToAgentRunning": (()=>useSubscribeToAgentRunning)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react/shallow.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
const initialState = {
    id: null,
    threadMessages: [],
    isAgentRunning: false,
    noTokenModalOpen: false,
    selectedSectionAction: null,
    threadName: "",
    isSoftgenProcessing: false
};
const useCurrentThreadStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subscribeWithSelector"])((set)=>({
        ...initialState,
        setId: (id)=>set({
                id
            }),
        addThreadMessages: (messages)=>set((state)=>({
                    threadMessages: [
                        ...state.threadMessages,
                        ...messages
                    ]
                })),
        setThreadMessages: (messages)=>set({
                threadMessages: messages
            }),
        setIsAgentRunning: (isRunning)=>set({
                isAgentRunning: isRunning
            }),
        setNoTokenModalOpen: (open)=>set({
                noTokenModalOpen: open
            }),
        setSelectedSectionAction: (action)=>set({
                selectedSectionAction: action
            }),
        setThreadName: (name)=>set({
                threadName: name
            }),
        setIsSoftgenProcessing: (isProcessing)=>set({
                isSoftgenProcessing: isProcessing
            })
    })));
const useCurrentThread = ()=>{
    const { currentThreadId, setCurrentThreadId } = useCurrentThreadStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            currentThreadId: state.id,
            setCurrentThreadId: state.setId
        })));
    return {
        currentThreadId,
        setCurrentThreadId
    };
};
const useCurrentThreadId = ()=>{
    return useCurrentThreadStore((state)=>state.id);
};
const useIsAgentRunning = ()=>{
    return useCurrentThreadStore((state)=>state.isAgentRunning);
};
const useSubscribeToAgentRunning = (callback)=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const unsubscribeAgentRunning = useCurrentThreadStore.subscribe((state)=>state.isAgentRunning, callback);
        return ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])("Unsubscribing from agent running state");
            unsubscribeAgentRunning();
        };
    }, []);
};
const useSelectedSectionAction = ()=>{
    const { selectedSectionAction, setSelectedSectionAction } = useCurrentThreadStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            selectedSectionAction: state.selectedSectionAction,
            setSelectedSectionAction: state.setSelectedSectionAction
        })));
    return {
        selectedSectionAction,
        setSelectedSectionAction
    };
};
const useResetCurrentThread = ()=>{
    const { setId, setThreadMessages } = useCurrentThreadStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            setId: state.setId,
            setThreadMessages: state.setThreadMessages
        })));
    return {
        resetCurrentThread: ()=>{
            setId(null);
            setThreadMessages([]);
        }
    };
};
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/perf_hooks [external] (perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("perf_hooks", () => require("perf_hooks"));

module.exports = mod;
}}),
"[externals]/module [external] (module, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("module", () => require("module"));

module.exports = mod;
}}),
"[externals]/worker_threads [external] (worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("worker_threads", () => require("worker_threads"));

module.exports = mod;
}}),
"[externals]/node:diagnostics_channel [external] (node:diagnostics_channel, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:diagnostics_channel", () => require("node:diagnostics_channel"));

module.exports = mod;
}}),
"[externals]/diagnostics_channel [external] (diagnostics_channel, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("diagnostics_channel", () => require("diagnostics_channel"));

module.exports = mod;
}}),
"[externals]/node:child_process [external] (node:child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:child_process", () => require("node:child_process"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:os [external] (node:os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:os", () => require("node:os"));

module.exports = mod;
}}),
"[externals]/node:path [external] (node:path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:path", () => require("node:path"));

module.exports = mod;
}}),
"[externals]/node:readline [external] (node:readline, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:readline", () => require("node:readline"));

module.exports = mod;
}}),
"[externals]/node:worker_threads [external] (node:worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:worker_threads", () => require("node:worker_threads"));

module.exports = mod;
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/async_hooks [external] (async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("async_hooks", () => require("async_hooks"));

module.exports = mod;
}}),
"[externals]/node:https [external] (node:https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:https", () => require("node:https"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:tls [external] (node:tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:tls", () => require("node:tls"));

module.exports = mod;
}}),
"[externals]/process [external] (process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("process", () => require("process"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/stores/navigate-file.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useNavigateFile": (()=>useNavigateFile),
    "useSetFile": (()=>useSetFile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
;
const initialState = {
    file: "",
    activeTab: "preview",
    showDrawer: false,
    showFileTree: true
};
const useNavigateFile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((set)=>({
        ...initialState,
        setFile: (file, openInEditor = false)=>{
            set({
                file
            });
            if (openInEditor) {
                set({
                    activeTab: "code-editor"
                });
                set({
                    showDrawer: true
                });
            }
        },
        setActiveTab: (tab)=>set({
                activeTab: tab
            }),
        setShowDrawer: (show)=>set({
                showDrawer: show
            }),
        setShowFileTree: (show)=>set({
                showFileTree: show
            }),
        reset: ()=>set(initialState)
    }));
const useSetFile = ()=>{
    return useNavigateFile((state)=>state.setFile);
};
}}),
"[project]/src/lib/websocket-manager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * WebSocketManager - A WebSocket client with batching and reconnection support
 *
 * Features:
 * - Automatic reconnection with exponential backoff
 * - Message batching for high-frequency messages
 * - Memory-efficient message handling
 * - Type-safe message passing
 */ __turbopack_context__.s({
    "WebSocketManager": (()=>WebSocketManager),
    "webSocketManager": (()=>webSocketManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@sentry+nextjs@9.29.0_@open_1e1fe85ff8ebead5e28d8250bbc32024/node_modules/@sentry/nextjs/build/cjs/index.server.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-ssr] (ecmascript)");
;
;
// Constants
const MAX_SUBSCRIBERS = 1000;
const BATCH_INTERVAL_MS = 16; // ~60fps
const MAX_RECONNECT_ATTEMPTS = 5;
const INITIAL_RECONNECT_DELAY = 1000; // 1 second
const MAX_RECONNECT_DELAY = 30000; // 30 seconds
const CONNECTION_TIMEOUT = 15000; // 15 seconds
class WebSocketManager {
    // Connection state
    socket = null;
    connectionUrl = null;
    connectionPromise = null;
    connectionTimeoutId = null;
    // State tracking for recovery
    connectionState = "disconnected";
    lastError = null;
    // Reconnection state
    reconnectAttempts = 0;
    reconnectTimeout = null;
    // Batching state
    messageBatch = [];
    batchScheduled = false;
    rafId = null;
    subscriberCallbacks = new Map();
    messageBuffer = "";
    constructor(){
        // Bind methods once in constructor to avoid creating new function references
        this.handleMessage = this.handleMessage.bind(this);
        this.handleClose = this.handleClose.bind(this);
        this.handleError = this.handleError.bind(this);
        this.processMessageBatch = this.processMessageBatch.bind(this);
    }
    /**
   * Connect to a WebSocket server
   * @param url WebSocket server URL
   * @returns Promise that resolves when connected
   */ async connect(url) {
        // Return existing connection or promise if available
        if (this.connectionPromise) return this.connectionPromise;
        if (this.socket?.readyState === WebSocket.OPEN) return Promise.resolve(this.socket);
        this.connectionUrl = url;
        this.connectionState = "connecting";
        this.lastError = null;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])(`[WebSocket] Connecting to ${url}...`);
        // Set connection timeout
        if (this.connectionTimeoutId) clearTimeout(this.connectionTimeoutId);
        this.connectionPromise = new Promise((resolve, reject)=>{
            try {
                this.socket = new WebSocket(url);
                // Set connection timeout
                this.connectionTimeoutId = setTimeout(()=>{
                    if (this.socket?.readyState !== WebSocket.OPEN) {
                        console.error("[WebSocket] Connection timeout");
                        this.lastError = new Error("Connection timeout");
                        this.connectionState = "error";
                        this.cleanup();
                        reject(new Error("Connection timeout"));
                    }
                }, CONNECTION_TIMEOUT);
                // Set up event handlers
                this.socket.onopen = ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])(`[WebSocket] Connected to ${url}`);
                    this.reconnectAttempts = 0;
                    this.connectionState = "connected";
                    if (this.connectionTimeoutId) {
                        clearTimeout(this.connectionTimeoutId);
                        this.connectionTimeoutId = null;
                    }
                    if (this.socket) resolve(this.socket);
                    // Notify subscribers about connection established
                    this.addToBatch({
                        type: "connection_status",
                        data: {
                            status: "connected"
                        },
                        timestamp: Date.now()
                    });
                };
                this.socket.onmessage = this.handleMessage;
                this.socket.onclose = this.handleClose;
                this.socket.onerror = this.handleError;
            } catch (error) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["captureException"])(error);
                this.connectionPromise = null;
                this.connectionState = "error";
                this.lastError = error;
                reject(error);
            }
        });
        return this.connectionPromise;
    }
    /**
   * Disconnect from the WebSocket server
   * @param code Close code (default: 1000)
   * @param reason Close reason (default: "Normal closure")
   */ disconnect(code = 1000, reason = "Normal closure") {
        // Clear reconnect timeout if exists
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        // Close socket if exists
        if (this.socket) {
            this.socket.onclose = null; // Prevent reconnection
            this.socket.close(code, reason);
            this.cleanup();
        }
    }
    /**
   * Send a message to the WebSocket server
   * @param message Message to send (will be stringified if not a string)
   * @returns boolean indicating if the message was sent successfully
   */ send(message) {
        if (this.socket?.readyState === WebSocket.OPEN) {
            try {
                const messageString = typeof message === "string" ? message : JSON.stringify(message);
                this.socket.send(messageString);
                return true;
            } catch (error) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["captureException"])(error);
                console.error("[WebSocket] Error sending message:", error);
                return false;
            }
        } else {
            console.warn(`[WebSocket] Cannot send message - socket not connected (state: ${this.connectionState})`);
            return false;
        }
    }
    /**
   * Check if the WebSocket is connected
   * @returns boolean indicating connection status
   */ isConnected() {
        return this.socket !== null && this.socket.readyState === WebSocket.OPEN;
    }
    /**
   * Subscribe to WebSocket messages
   * @param callback Function to call when messages are received
   * @returns Unsubscribe function
   */ subscribe(callback) {
        // Check subscriber limit
        if (this.subscriberCallbacks.size >= MAX_SUBSCRIBERS) {
            console.warn("[WebSocket] Maximum number of subscribers reached");
            return ()=>{};
        }
        // Create unique symbol as ID
        const id = Symbol();
        this.subscriberCallbacks.set(id, callback);
        // Return unsubscribe function
        return ()=>this.subscriberCallbacks.delete(id);
    }
    /**
   * Handle incoming WebSocket messages
   */ handleMessage(event) {
        try {
            // Only handle string messages
            let data = event.data;
            if (typeof data !== "string") {
                console.warn("[WebSocket] Received non-string message, ignoring");
                return;
            }
            // Try to parse directly - the most reliable way to check if JSON is complete
            try {
                const message = JSON.parse(data);
                // Handle ping messages
                if (message.type === "ping") {
                    console.debug("[WebSocket] Received ping, sending pong");
                    this.send({
                        type: "pong"
                    });
                    return;
                }
                this.addToBatch(message);
                return;
            } catch  {
            // Not complete or valid JSON, continue to buffer handling
            }
            // Check for obvious partial messages
            const firstChar = data.charAt(0);
            const lastChar = data.charAt(data.length - 1);
            const isStartOfJson = firstChar === "{" || firstChar === "[";
            const isEndOfJson = lastChar === "}" || lastChar === "]";
            // Add to buffer if it looks like the start of a JSON object/array but not the end
            if (isStartOfJson && !isEndOfJson) {
                this.messageBuffer += data;
                return;
            }
            // Add to buffer if we already have buffered content
            if (this.messageBuffer) {
                // If this chunk looks like the end of JSON, try parsing the combined content
                data = this.messageBuffer + data;
                // Try to parse the combined data
                try {
                    const message = JSON.parse(data);
                    this.addToBatch(message);
                    this.messageBuffer = ""; // Reset buffer after successful parse
                } catch (error) {
                    // Still not complete JSON
                    if (isEndOfJson) {
                        // If it ends with a brace/bracket but still can't parse, it might be malformed
                        console.error("[WebSocket] Error parsing combined JSON:", error);
                        this.messageBuffer = ""; // Reset buffer to avoid accumulating invalid data
                    } else {
                        // Otherwise keep accumulating data
                        this.messageBuffer = data;
                    }
                }
                return;
            }
            // If we get here, we have a standalone chunk that didn't parse as JSON
            // Try one more time with explicit error handling
            try {
                const message = JSON.parse(data);
                this.addToBatch(message);
            } catch (error) {
                console.error("[WebSocket] Error parsing JSON:", error);
                // Don't buffer data that doesn't look like JSON
                if (isStartOfJson) {
                    this.messageBuffer = data; // Only buffer if it looks like the start of JSON
                }
            }
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["captureException"])(error);
            console.error("[WebSocket] Error in handleMessage:", error);
            this.messageBuffer = ""; // Reset buffer on error
        }
    }
    /**
   * Add a message to the current batch and schedule processing
   */ addToBatch(message) {
        this.messageBatch.push(message);
        // Schedule batch processing if not already scheduled
        if (!this.batchScheduled) {
            this.batchScheduled = true;
            // Use requestAnimationFrame for synchronizing with UI updates
            if ("undefined" !== "undefined" && "requestAnimationFrame" in window) {
                "TURBOPACK unreachable";
            } else {
                // Fallback for environments without requestAnimationFrame
                setTimeout(this.processMessageBatch, BATCH_INTERVAL_MS);
            }
        }
    }
    /**
   * Process all messages in the current batch
   */ processMessageBatch() {
        this.batchScheduled = false;
        this.rafId = null;
        if (!this.messageBatch.length) return;
        // Get current batch and reset for next batch
        const batch = this.messageBatch;
        this.messageBatch = [];
        // Create a single batch message if needed (only once)
        const batchMessage = batch.length > 1 ? {
            type: "batch",
            batch,
            timestamp: Date.now()
        } : null;
        // Notify subscribers with batched messages
        this.subscriberCallbacks.forEach((callback, id)=>{
            try {
                if (batch.length === 1) {
                    // Single message - send as is
                    callback(batch[0]);
                } else {
                    // Multiple messages - send as batch
                    callback(batchMessage);
                }
            } catch (error) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["captureException"])(error);
                console.error("[WebSocket] Error in subscriber:", error);
                this.subscriberCallbacks.delete(id);
            }
        });
    }
    handleClose(event) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])(`[WebSocket] Connection closed: ${event.code} ${event.reason}`);
        this.connectionState = "disconnected";
        this.cleanup();
        // Notify subscribers about disconnection
        this.addToBatch({
            type: "connection_status",
            data: {
                status: "disconnected",
                code: event.code,
                reason: event.reason
            },
            timestamp: Date.now()
        });
        // Don't reconnect on normal closure (code 1000)
        if (event.code !== 1000) {
            this.attemptReconnect();
        }
    }
    handleError(error) {
        console.error("[WebSocket] Error:", error);
        this.connectionState = "error";
        this.lastError = error;
        this.cleanup();
        // Notify subscribers about error
        this.addToBatch({
            type: "connection_status",
            data: {
                status: "error",
                message: error.toString()
            },
            timestamp: Date.now()
        });
        this.attemptReconnect();
    }
    attemptReconnect() {
        // Check if we should stop reconnecting
        if (this.reconnectAttempts >= MAX_RECONNECT_ATTEMPTS || !this.connectionUrl) {
            console.warn("[WebSocket] Max reconnection attempts reached");
            return;
        }
        // Calculate exponential backoff delay
        this.reconnectAttempts++;
        const delay = Math.min(INITIAL_RECONNECT_DELAY * Math.pow(2, this.reconnectAttempts - 1), MAX_RECONNECT_DELAY);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])(`[WebSocket] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
        // Schedule reconnection
        this.reconnectTimeout = setTimeout(()=>{
            if (this.connectionUrl) {
                this.connect(this.connectionUrl).catch((error)=>{
                    console.error("[WebSocket] Reconnection failed:", error);
                    this.attemptReconnect();
                });
            }
        }, delay);
    }
    cleanup() {
        // Clear scheduled batch processing
        if (this.rafId !== null && "undefined" !== "undefined" && "cancelAnimationFrame" in window) {
            "TURBOPACK unreachable";
        }
        this.batchScheduled = false;
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        if (this.connectionTimeoutId) {
            clearTimeout(this.connectionTimeoutId);
            this.connectionTimeoutId = null;
        }
        // Clean up WebSocket
        if (this.socket) {
            // Remove event handlers
            this.socket.onopen = null;
            this.socket.onclose = null;
            this.socket.onerror = null;
            this.socket.onmessage = null;
            // Close if still open
            if (this.socket.readyState === WebSocket.OPEN) {
                this.socket.close(1000, "Cleanup");
            }
            this.socket = null;
        }
        // Reset state
        this.connectionPromise = null;
        this.messageBatch = [];
        this.messageBuffer = "";
    }
}
const webSocketManager = new WebSocketManager();
}}),
"[project]/src/providers/thread-provider/hooks.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useWebSocketManager": (()=>useWebSocketManager),
    "useWebSocketMessage": (()=>useWebSocketMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/websocket-manager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
const useWebSocketManager = (callbacks)=>{
    const connectedUrlRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const messageHandlerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const streamingContentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])("");
    const isMountedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(true);
    /**
   * Store callbacks in a ref to maintain stable references across renders.
   * This allows us to:
   * 1. Avoid including callbacks in dependency arrays of useCallback/useEffect
   * 2. Always have access to the latest callback implementations
   * 3. Prevent unnecessary effect re-runs when parent components re-render
   */ const callbacksRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(callbacks);
    // Update the ref value whenever callbacks change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        callbacksRef.current = callbacks;
    }, [
        callbacks
    ]);
    // Memoize the message handler to prevent unnecessary re-renders
    const handleSingleMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        if (!isMountedRef.current) return;
        // Handle connection status messages
        if (message.type === "connection_status") {
            const statusData = message.data;
            const status = statusData?.status;
            console.debug(`[WebSocket] Connection status changed to: ${status}`);
            if (status === "error") {
                // Notify about connection issues
                callbacksRef.current?.onError?.(message);
            }
            return;
        }
        if (message.type === "error" && message.isCompleteMessage && message.termination && typeof message.data === "string" && message.data.includes("Context window is full")) {
            callbacksRef.current?.setContextLimitReached?.(true);
            callbacksRef.current?.onErrorMessage?.(message);
            callbacksRef.current?.setIsAgentRunning(false);
            return;
        }
        switch(message.type){
            case "content":
                if (typeof message.data === "string") {
                    if (message.isCompleteMessage) {
                        callbacksRef.current?.onComplete?.(streamingContentRef.current);
                        streamingContentRef.current = "";
                        callbacks.onSoftgenProcessing?.(true);
                    } else {
                        streamingContentRef.current = message.data;
                    }
                }
                break;
            case "session_status":
                {
                    const { status } = message;
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])(`Session status changed to: ${status}`);
                    callbacksRef.current?.setIsAgentRunning(status === "running");
                    break;
                }
            case "stop_session":
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])("Session stopped");
                callbacksRef.current?.setIsAgentRunning(false);
                break;
            case "error":
                {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])("Session error");
                    callbacksRef.current?.onErrorMessage?.(message);
                    break;
                }
            default:
                callbacksRef.current?.onMessage?.(message);
        }
    }, []);
    // Handle both batched and single messages
    const handleMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        if (!isMountedRef.current) return;
        if (message.type === "batch" && message.batch) {
            message.batch.forEach(handleSingleMessage);
        } else {
            handleSingleMessage(message);
        }
    }, [
        handleSingleMessage
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        isMountedRef.current = true;
        messageHandlerRef.current = handleMessage;
        const unsubscribe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].subscribe(handleMessage);
        const currentUrl = connectedUrlRef.current;
        return ()=>{
            isMountedRef.current = false;
            console.debug("Cleaning up WebSocket message handler");
            unsubscribe();
            messageHandlerRef.current = null;
            streamingContentRef.current = "";
            if (currentUrl && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].isConnected()) {
                console.debug("Disconnecting WebSocket on unmount");
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].disconnect(1000, "Component unmounted");
                connectedUrlRef.current = null;
            }
        };
    }, [
        handleMessage
    ]);
    const connect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (url)=>{
        try {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].isConnected() && connectedUrlRef.current === url) {
                return true;
            }
            // Disconnect from previous connection if exists
            if (connectedUrlRef.current) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].disconnect(1000, "Reconnecting");
            }
            // Reset state before connecting
            streamingContentRef.current = "";
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].connect(url);
            connectedUrlRef.current = url;
            return true;
        } catch (error) {
            console.error("WebSocket connection failed:", error);
            connectedUrlRef.current = null;
            return false;
        }
    }, []);
    const disconnect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((code = 1000, reason = "Normal closure")=>{
        console.debug("Disconnecting WebSocket...");
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].disconnect(code, reason);
        if (isMountedRef.current) {
            callbacksRef.current?.setIsAgentRunning(false);
            streamingContentRef.current = ""; // Clear streaming content
            connectedUrlRef.current = null;
        }
    }, []);
    const send = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        if (!isMountedRef.current || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].isConnected()) {
            console.warn("Cannot send message: WebSocket not connected");
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].send(message);
    }, []);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            connect,
            disconnect,
            send,
            isConnected: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].isConnected()
        }), [
        connect,
        disconnect,
        send
    ]);
};
function useWebSocketMessage(messageType, callback) {
    const callbackRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(callback);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        callbackRef.current = callback;
    }, [
        callback
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleMessage = (message)=>{
            if (message.type === messageType) {
                callbackRef.current(message);
            } else if (message.type === "batch" && message.batch) {
                // Handle batched messages
                message.batch.filter((m)=>m.type === messageType).forEach((m)=>callbackRef.current(m));
            }
        };
        const unsubscribe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$websocket$2d$manager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["webSocketManager"].subscribe(handleMessage);
        return ()=>unsubscribe();
    }, [
        messageType
    ]);
}
}}),
"[project]/src/providers/thread-provider/index.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThreadContext": (()=>ThreadContext),
    "ThreadProvider": (()=>ThreadProvider),
    "useThreadContext": (()=>useThreadContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/global/toast.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/debug.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/current-thread.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@sentry+nextjs@9.29.0_@open_1e1fe85ff8ebead5e28d8250bbc32024/node_modules/@sentry/nextjs/build/cjs/index.server.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@tanstack+react-query@5.80.7_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nanoid$40$5$2e$1$2e$5$2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@babel+core@7.2_7ebbdc2cda8928e36aa43f99456fd4f1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@1.7.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@_d6656a0f81eea17aeaa3704f3dfeebbd/node_modules/zustand/esm/react/shallow.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/navigate-file.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$project$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/project-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$thread$2d$provider$2f$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/thread-provider/hooks.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const ThreadContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const getModelId = (model)=>{
    switch(model){
        case "creative":
            return "2";
        case "standard":
            return "1";
        case "plan":
            return "8";
        default:
            return "2";
    }
};
const AUTONOMOUS_ITERATIONS = 20;
const ThreadProvider = ({ projectId, children })=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const [contextLimitReached, setContextLimitReached] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { threads, refetch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$project$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useProject"])();
    const { setFile, setActiveTab } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$navigate$2d$file$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNavigateFile"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            setFile: state.setFile,
            setActiveTab: state.setActiveTab
        })));
    const { currentThreadId, setCurrentThreadId, addThreadMessages, setThreadMessages, setIsAgentRunning, setNoTokenModalOpen, setThreadName, setIsSoftgenProcessing } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCurrentThreadStore"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$_d6656a0f81eea17aeaa3704f3dfeebbd$2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])((state)=>({
            currentThreadId: state.id,
            setCurrentThreadId: state.setId,
            addThreadMessages: state.addThreadMessages,
            setThreadMessages: state.setThreadMessages,
            setIsAgentRunning: state.setIsAgentRunning,
            setNoTokenModalOpen: state.setNoTokenModalOpen,
            setThreadName: state.setThreadName,
            setIsSoftgenProcessing: state.setIsSoftgenProcessing
        })));
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$current$2d$thread$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSubscribeToAgentRunning"])((isRunning, prevWasRunning)=>{
        console.debug(`Agent running state changed: ${isRunning} (prev: ${prevWasRunning})`);
        if (!isRunning && prevWasRunning) {
            refetch();
            setActiveTab("preview");
            setFile("");
        }
    });
    const { data: currentThread, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "get-thread",
            currentThreadId
        ],
        queryFn: async ()=>{
            if (!currentThreadId) return null;
            const thread = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getThread"])(currentThreadId);
            return thread;
        },
        enabled: !!currentThreadId,
        refetchOnWindowFocus: false
    });
    // update thread messages when currentThread changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (currentThread) {
            setThreadMessages(currentThread.messages);
        }
    }, [
        currentThread
    ]);
    const onComplete = (content)=>{
        addThreadMessages([
            {
                role: "assistant",
                content,
                message_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nanoid$40$5$2e$1$2e$5$2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])()
            }
        ]);
    };
    const onMessage = (message)=>{
        if (message.type === "thread_update") {
            const { data } = message;
            if (data?.messages) {
                setThreadMessages(data.messages);
            }
        }
    };
    const onError = (error)=>{
        console.error("WebSocket error:", error);
        // Show a toast notification for WebSocket errors
        if (error.type === "connection_error") {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$4_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Connection issue detected", {
                description: "The connection to the AI agent was interrupted. You may need to refresh the page if messages stop appearing.",
                duration: 10000
            });
        }
    };
    const onErrorMessage = (errorMsg)=>{
        if (typeof errorMsg.data === "string" && errorMsg.data.includes("No tokens available")) {
            setNoTokenModalOpen(true);
        }
        setIsAgentRunning(false);
    };
    const onSoftgenProcessing = (isProcessing)=>{
        setIsSoftgenProcessing(isProcessing);
    };
    const { connect: connectWebSocket, disconnect: disconnectWebSocket, send: sendWebSocketMessage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$thread$2d$provider$2f$hooks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWebSocketManager"])({
        onComplete,
        onMessage,
        onErrorMessage,
        setIsAgentRunning,
        onError,
        onSoftgenProcessing,
        setContextLimitReached
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Clean up WebSocket when thread changes to null/undefined
        if (!currentThreadId) {
            setThreadMessages([]);
            disconnectWebSocket(1000, "Thread cleared");
            setIsAgentRunning(false);
        }
        // cleanup on component unmount
        return ()=>{
            setThreadMessages([]);
            disconnectWebSocket(1000, currentThreadId ? "Thread disconnected" : "Component unmounted");
            setIsAgentRunning(false);
        };
    }, [
        currentThreadId,
        disconnectWebSocket
    ]);
    const createThreadFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createThread"])(projectId),
        onSuccess: (newThread)=>{
            queryClient.invalidateQueries({
                queryKey: [
                    "threads",
                    projectId
                ]
            });
            setCurrentThreadId(newThread.thread_id);
            setThreadName(newThread.name);
        // return newThread;
        },
        onError: (error)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["errorToast"])(error.toString());
        }
    });
    const selectThreadId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (currentThreadId) return currentThreadId;
        console.warn("No current thread ID available, selecting latest thread");
        const latestThread = threads?.[0];
        if (latestThread) {
            setCurrentThreadId(latestThread.thread_id);
            return latestThread.thread_id;
        }
        return null;
    }, [
        currentThreadId,
        setCurrentThreadId,
        threads
    ]);
    const sendMessageFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$tanstack$2b$react$2d$query$40$5$2e$80$2e$7_react$40$19$2e$1$2e$0$2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ content, images = [], selectedPaths = [], promptSet = 10, model = "standard" })=>{
            try {
                setIsAgentRunning(true);
                let effectiveThreadId = selectThreadId();
                if (!effectiveThreadId) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])("No thread ID available, creating a new thread...");
                    try {
                        const newThread = await createThreadFn.mutateAsync();
                        effectiveThreadId = newThread.thread_id;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$debug$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["debug"])(`Created new thread with ID: ${effectiveThreadId}`);
                    } catch (error) {
                        console.error("Failed to create a new thread:", error);
                        throw new Error("Could not create a new thread to send your message. Please try again.");
                    }
                }
                const newUserMessage = {
                    role: "user",
                    content,
                    message_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$nanoid$40$5$2e$1$2e$5$2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(),
                    type: "user_message",
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    include_in_llm_message_history: true
                };
                addThreadMessages([
                    newUserMessage
                ]);
                const encodeImageAsBase64 = (file)=>{
                    return new Promise((resolve, reject)=>{
                        const reader = new FileReader();
                        reader.readAsDataURL(file);
                        reader.onload = ()=>{
                            const result = reader.result?.toString().split(",")[1];
                            resolve(result || "");
                        };
                        reader.onerror = (error)=>reject(error);
                    });
                };
                const encodedImages = await Promise.all(images.map((file)=>encodeImageAsBase64(file)));
                const messageData = {
                    action: "new_message",
                    message: content,
                    objective_images: encodedImages,
                    autonomous_iterations: AUTONOMOUS_ITERATIONS,
                    mode: "autonomous",
                    selected_paths: selectedPaths,
                    prompt_set: promptSet,
                    model_id: getModelId(model),
                    chat_mode: model === "plan"
                };
                const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])(); // Make sure to import getToken
                const wsUrl = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_BASE_URL"].replace("http", "ws")}/start_thread_websocket/${projectId}/${effectiveThreadId}?token=${token}`;
                await connectWebSocket(wsUrl);
                sendWebSocketMessage(messageData);
            } catch (error) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$9$2e$29$2e$0_$40$open_1e1fe85ff8ebead5e28d8250bbc32024$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$cjs$2f$index$2e$server$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["captureException"])(error);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["errorToast"])("Failed to send message");
            } finally{
                setIsAgentRunning(false);
            }
        },
        onError: (error)=>{
            console.error("Error sending message:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["errorToast"])(error.message);
            setIsAgentRunning(false);
        }
    });
    const stopSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            sendWebSocketMessage({
                action: "stop"
            });
            disconnectWebSocket(1000, "Session stopped by user");
            setIsAgentRunning(false);
            return true;
        } catch (error) {
            console.error("Error stopping session:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$global$2f$toast$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["errorToast"])("Failed to stop session");
            return false;
        }
    }, [
        disconnectWebSocket,
        sendWebSocketMessage
    ]);
    const selectFirstThread = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const latestThread = threads?.[0];
        if (latestThread) {
            setCurrentThreadId(latestThread.thread_id);
            return latestThread.thread_id;
        }
    }, [
        threads,
        setCurrentThreadId
    ]);
    const contextValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            currentThread,
            isLoading,
            createThreadFn,
            sendMessageFn,
            selectThreadId,
            stopSession,
            selectFirstThread,
            contextLimitReached,
            setContextLimitReached
        }), [
        currentThread,
        isLoading,
        createThreadFn,
        sendMessageFn,
        selectThreadId,
        stopSession,
        selectFirstThread,
        contextLimitReached,
        setContextLimitReached
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ThreadContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/thread-provider/index.tsx",
        lineNumber: 365,
        columnNumber: 10
    }, this);
};
const useThreadContext = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$babel$2b$core$40$7$2e$2_7ebbdc2cda8928e36aa43f99456fd4f1$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ThreadContext);
    if (context === undefined) {
        throw new Error("useThreadContext must be used within a ThreadProvider");
    }
    return context;
};
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8267d9e0._.js.map