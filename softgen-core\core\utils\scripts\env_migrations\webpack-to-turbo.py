from daytona import Daytona, DaytonaConfig
import json
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import List

# Initialize the Daytona client
config = DaytonaConfig(
    api_key="dtn_858727beec575bf6d960c089fc4e4e397008e0e6383bd205082ed5de724e1f63",
    server_url="https://daytona.work/api",
    target="us"
)
daytona = Daytona(config)

def execute_in_tmux(workspace, command, session_name, wait_for_completion=False):
    try:
        # Create new tmux session
        workspace.process.exec(f"tmux new-session -d -s {session_name}")
        
        # Create a done flag file
        if wait_for_completion:
            flag_file = f"/tmp/{session_name}.done"
            full_command = f"{command} && touch {flag_file} || touch {flag_file}.error"
        else:
            full_command = command
            
        # Send command
        workspace.process.exec(f"tmux send-keys -t {session_name} '{full_command}' Enter")
        
        if wait_for_completion:
            # Wait for the flag file to appear
            max_wait = 600  # Maximum wait time in seconds
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                # Check for error flag
                error_check = workspace.process.exec(f"test -f {flag_file}.error")
                if error_check.code == 0:
                    workspace.process.exec(f"rm -f {flag_file}.error")
                    raise Exception(f"Command failed in session {session_name}")
                
                # Check for success flag
                done_check = workspace.process.exec(f"test -f {flag_file}")
                if done_check.code == 0:
                    workspace.process.exec(f"rm -f {flag_file}")
                    break
                    
                time.sleep(2)
            else:
                raise Exception(f"Command timed out in session {session_name}")
        else:
            time.sleep(2)
        
        return workspace.process.exec(f"tmux capture-pane -t {session_name} -p")
        
    except Exception as e:
        print(f"Error in tmux execution: {str(e)}")
        return None

def update_package_json(workspace):
    try:
        # First try to replace the exact pattern with proper spacing
        result = workspace.fs.replace_in_files(
            files=["/app/package.json"],
            pattern='    "dev": "next dev --turbo",',
            new_value='    "dev": "next dev",'
        )
        
        if not result:
            print("First pattern not found, trying alternative pattern...")
            # Try to replace any existing non-turbo entries to ensure consistency
            result = workspace.fs.replace_in_files(
                files=["/app/package.json"],
                pattern='    "dev": "next dev",',
                new_value='    "dev": "next dev",'
            )
            
            if not result:
                print("No existing standard pattern found, trying basic pattern...")
                # Try one last time with a more basic pattern
                result = workspace.fs.replace_in_files(
                    files=["/app/package.json"],
                    pattern='"dev": "next dev --turbo"',
                    new_value='"dev": "next dev"'
                )
                
                if not result:
                    raise Exception("Failed to perform replacement - no matching patterns found")
            
        # Print the result of the operation
        print("Successfully updated package.json")
 
        return True

    except Exception as e:
        print(f"Error updating package.json: {str(e)}")
        return False

def process_workspace(workspace_id):
    try:
        print(f"\nProcessing workspace: {workspace_id}")
        workspace = daytona.get(workspace_id)
        print(f"Current workspace ID: {workspace.id}")
        
        # Update package.json
        success = update_package_json(workspace)
        if success:
            print(f"Successfully updated package.json for workspace {workspace_id}")
            # Try PM2 restart first
            restart_cmd = "cd /app && pm2 restart all"
            restart_response = execute_in_tmux(workspace, restart_cmd, "pm2-restart", wait_for_completion=True)
            
            if restart_response:
                print(f"Successfully restarted PM2 for workspace {workspace_id}")
            else:
                print("PM2 restart failed, trying pm2 start...")
                # Try pm2 start if restart failed
                start_cmd = "cd /app && pm2 start"
                start_response = execute_in_tmux(workspace, start_cmd, "pm2-start", wait_for_completion=True)
                
                if start_response:
                    print(f"Successfully started PM2 for workspace {workspace_id}")
                else:
                    print(f"Both PM2 restart and start failed for workspace {workspace_id}")
                    return False
            
            return True
        else:
            print(f"Failed to update package.json for workspace {workspace_id}")
            return False
        
    except Exception as e:
        print(f"Error processing workspace {workspace_id}: {str(e)}")
        return False

async def process_workspace_batch(workspace_ids: List[str], timestamp: str, batch_num: int):
    successful_ids = []
    failed_ids = []
    
    with ThreadPoolExecutor(max_workers=len(workspace_ids)) as executor:
        futures = [
            executor.submit(process_workspace, workspace_id)
            for workspace_id in workspace_ids
        ]
        
        for workspace_id, future in zip(workspace_ids, futures):
            try:
                success = future.result()
                if success:
                    successful_ids.append(workspace_id)
                else:
                    failed_ids.append(workspace_id)
            except Exception as e:
                print(f"Error processing {workspace_id}: {str(e)}")
                failed_ids.append(workspace_id)
    
    # Save batch results
    batch_suffix = f"{timestamp}_batch{batch_num}"
    if successful_ids:
        with open(f'env-migration-marko/turbo_successful_ids_{batch_suffix}.json', 'w') as f:
            json.dump(successful_ids, f, indent=4)
    if failed_ids:
        with open(f'env-migration-marko/turbo_failed_ids_{batch_suffix}.json', 'w') as f:
            json.dump(failed_ids, f, indent=4)
    
    return successful_ids, failed_ids

async def main():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # # Test workspace IDs
    # all_workspace_ids = [
    #     'sandbox-df80848a',
    #     'sandbox-8c0f237e'
    # ]

    # Load workspace IDs from JSON file
    with open('env-migration-marko/workspace_ids.json', 'r') as f:
        workspace_data = json.load(f)
        all_workspace_ids = [workspace["id"] for workspace in workspace_data["single_workspaces"]]
    
    batch_size = 1000
    batches = [
        all_workspace_ids[i:i + batch_size]
        for i in range(0, len(all_workspace_ids), batch_size)
    ]
    
    print(f"Processing {len(all_workspace_ids)} workspaces in {len(batches)} batches")
    
    all_successful = []
    all_failed = []
    
    for batch_num, batch in enumerate(batches, 1):
        print(f"\nProcessing batch {batch_num}/{len(batches)} ({len(batch)} workspaces)")
        
        while True:
            approval = input(f"\nDo you want to process batch {batch_num}/{len(batches)} with {len(batch)} workspaces? (y/n): ").lower()
            if approval in ['y', 'n']:
                break
            print("Please enter 'y' for yes or 'n' for no.")
        
        if approval == 'n':
            print(f"Skipping batch {batch_num} as per user request")
            continue
            
        successful, failed = await process_workspace_batch(batch, timestamp, batch_num)
        
        all_successful.extend(successful)
        all_failed.extend(failed)
        
        print(f"\nBatch {batch_num} complete:")
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
    
    # Save final results
    with open(f'env-migration-marko/turbo_all_successful_ids_{timestamp}.json', 'w') as f:
        json.dump(all_successful, f, indent=4)
    if all_failed:
        with open(f'env-migration-marko/turbo_all_failed_ids_{timestamp}.json', 'w') as f:
            json.dump(all_failed, f, indent=4)
    
    print("\n=== Processing Complete ===")
    print(f"Total workspaces: {len(all_workspace_ids)}")
    print(f"Total successful: {len(all_successful)}")
    print(f"Total failed: {len(all_failed)}")
    
    print("\nFinal results saved to:")
    print(f"All successful IDs: env-migration-marko/turbo_all_successful_ids_{timestamp}.json")
    if all_failed:
        print(f"All failed IDs: env-migration-marko/turbo_all_failed_ids_{timestamp}.json")

if __name__ == "__main__":
    asyncio.run(main())
