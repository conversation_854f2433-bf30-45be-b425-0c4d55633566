import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { formatTimestamp } from "@/lib/format-timestamp";
import {
  calculateGitHubCloneProgress,
  copyGitHubCloneLogs,
  GITHUB_CLONE_STEPS,
  handleGitHubClone,
  stopGitHubClone,
  type GitHubCloneLog,
  type GitHubCloneStatus,
} from "@/utils/githubCloneHandler";
import {
  AlertTriangle,
  CheckCircle,
  Copy,
  GitBranch,
  Loader2,
  Package,
  RefreshCw,
  XCircle,
} from "lucide-react";

interface Project {
  project_id: string;
  name: string;
  env_id?: string;
}

interface GitHubCloneDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProject: Project | null;
  isGitHubCloning: boolean;
  gitHubCloneStatus: GitHubCloneStatus | null;
  gitHubCloneLogs: GitHubCloneLog[];
  gitHubCloneError: string | null;
  isGitHubCloneDisconnected: boolean;
  gitHubCloneSocket: WebSocket | null;
  setIsGitHubCloning: (value: boolean) => void;
  setGitHubCloneStatus: (status: GitHubCloneStatus | null) => void;
  setGitHubCloneLogs: (
    logs: GitHubCloneLog[] | ((prev: GitHubCloneLog[]) => GitHubCloneLog[]),
  ) => void;
  setGitHubCloneError: (error: string | null) => void;
  setIsGitHubCloneDisconnected: (value: boolean) => void;
  setGitHubCloneSocket: (socket: WebSocket | null) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSuccess?: (data: any) => void;
}

export default function GitHubCloneDialog({
  isOpen,
  onClose,
  selectedProject,
  isGitHubCloning,
  gitHubCloneStatus,
  gitHubCloneLogs,
  gitHubCloneError,
  isGitHubCloneDisconnected,
  gitHubCloneSocket,
  setIsGitHubCloning,
  setGitHubCloneStatus,
  setGitHubCloneLogs,
  setGitHubCloneError,
  setIsGitHubCloneDisconnected,
  setGitHubCloneSocket,
  onSuccess,
}: GitHubCloneDialogProps) {
  // Calculate progress
  const gitHubCloneProgress = calculateGitHubCloneProgress(isGitHubCloning, gitHubCloneLogs);

  const handleStartGitHubClone = async () => {
    if (!selectedProject) return;

    await handleGitHubClone({
      projectId: selectedProject.project_id,
      projectName: selectedProject.name,
      setIsGitHubCloning,
      setGitHubCloneStatus,
      setGitHubCloneLogs,
      setGitHubCloneError,
      setIsGitHubCloneDisconnected,
      setGitHubCloneSocket,
      onSuccess,
      onError: (error) => {
        console.error("GitHub clone error:", error);
      },
    });
  };

  const handleStopGitHubClone = () => {
    stopGitHubClone({
      gitHubCloneSocket,
      setGitHubCloneSocket,
      setIsGitHubCloning,
      setGitHubCloneStatus,
      setGitHubCloneLogs,
      setGitHubCloneError,
    });
  };

  const handleCloseDialog = () => {
    handleStopGitHubClone();
    onClose();
  };

  // Get current step info
  const getCurrentStepInfo = () => {
    if (!gitHubCloneStatus) return null;

    const step = GITHUB_CLONE_STEPS.find((s) => s.id === gitHubCloneStatus.step);
    return {
      ...step,
      status: gitHubCloneStatus.status,
      message: gitHubCloneStatus.message,
      error: gitHubCloneStatus.error,
    };
  };

  const currentStep = getCurrentStepInfo();

  return (
    <Dialog open={isOpen} onOpenChange={handleCloseDialog}>
      <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5 text-blue-600" />
            Environment Recovery
          </DialogTitle>
          <DialogDescription>
            Clone your project to a fresh environment to resolve any issues.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Project Info */}
          {selectedProject && (
            <div className="rounded-lg border border-primary/10 bg-primary/5 p-4">
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                  <Package className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-primary">{selectedProject.name}</h3>
                  <p className="text-sm text-primary/70">ID: {selectedProject.project_id}</p>
                </div>
              </div>
            </div>
          )}

          {/* Warning */}
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertTitle className="text-orange-800">Important</AlertTitle>
            <AlertDescription className="text-orange-700">
              This will create a fresh environment with your latest code. The old environment will
              be preserved.
            </AlertDescription>
          </Alert>

          {/* Progress Section */}
          {isGitHubCloning && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-primary">
                    {currentStep ? currentStep.name : "Processing..."}
                  </span>
                  <span className="text-sm text-primary/70">{gitHubCloneProgress}%</span>
                </div>
                <Progress value={gitHubCloneProgress} className="h-2" />
              </div>

              {currentStep && (
                <div className="flex items-center gap-2 text-sm text-primary/80">
                  {currentStep.status === "started" && (
                    <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  )}
                  {currentStep.status === "completed" && (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                  {currentStep.status === "failed" && <XCircle className="h-4 w-4 text-red-600" />}
                  <span>{currentStep.message}</span>
                </div>
              )}

              {isGitHubCloneDisconnected && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertTitle>Connection Lost</AlertTitle>
                  <AlertDescription>
                    The process is still running. Please wait a few minutes and refresh.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Error Display - Simplified */}
          {gitHubCloneError && !isGitHubCloning && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{gitHubCloneError}</AlertDescription>
            </Alert>
          )}

          {/* Success Message */}
          {gitHubCloneStatus?.step === "finalize" && gitHubCloneStatus?.status === "completed" && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Success!</AlertTitle>
              <AlertDescription className="text-green-700">
                Your project has been successfully moved to a new environment.
                {gitHubCloneStatus.data && (
                  <div className="mt-2 text-sm">
                    <p>
                      <strong>New Environment:</strong> {gitHubCloneStatus.data.new_env_id}
                    </p>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Activity Log */}
          {gitHubCloneLogs.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-primary">Activity Log</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyGitHubCloneLogs(gitHubCloneLogs)}
                  className="text-xs"
                >
                  <Copy className="mr-1 h-3 w-3" />
                  Copy
                </Button>
              </div>
              <div className="max-h-32 overflow-y-auto rounded-lg border border-primary/10 bg-primary/5 p-3 font-mono text-xs">
                {gitHubCloneLogs.slice(-5).map((log, index) => (
                  <div key={index} className="mb-1 border-b border-primary/10 pb-1 last:border-b-0">
                    <div className="flex items-center justify-between text-primary/70">
                      <span>{formatTimestamp(log.timestamp)}</span>
                      <span
                        className={`rounded px-1 py-0.5 text-xs ${
                          log.status === "failed"
                            ? "bg-red-100 text-red-800"
                            : log.status === "completed"
                              ? "bg-green-100 text-green-800"
                              : "bg-primary/20 text-primary/80"
                        }`}
                      >
                        {log.step?.toUpperCase()}
                      </span>
                    </div>
                    <div className="mt-0.5 text-primary/90">{log.message}</div>
                    {log.error && <div className="mt-0.5 text-red-600">{log.error}</div>}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          {gitHubCloneStatus?.step === "finalize" && gitHubCloneStatus?.status === "completed" ? (
            <>
              <Button variant="outline" onClick={handleCloseDialog}>
                Close
              </Button>
              <Button
                onClick={() => {
                  if (onSuccess) {
                    onSuccess(gitHubCloneStatus.data);
                  }
                  // Close the dialog
                  handleCloseDialog();
                }}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Complete & Close
              </Button>
            </>
          ) : !isGitHubCloning ? (
            <>
              <Button variant="outline" onClick={handleCloseDialog}>
                Cancel
              </Button>
              <Button onClick={handleStartGitHubClone} disabled={!selectedProject?.env_id}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Start Recovery
              </Button>
            </>
          ) : (
            <Button variant="destructive" onClick={handleStopGitHubClone}>
              <XCircle className="mr-2 h-4 w-4" />
              Stop Process
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
