{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/facebook-pixel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Script from \"next/script\";\r\n\r\nconst FACEBOOK_PIXEL_ID = \"701451716094912\";\r\n\r\nexport function FacebookPixel() {\r\n  return (\r\n    <Script\r\n      id=\"fb-pixel\"\r\n      strategy=\"afterInteractive\"\r\n      dangerouslySetInnerHTML={{\r\n        __html: `\r\n          (function(f,b,e,v,n,t,s) {\r\n            try {\r\n              if(f.fbq)return;\r\n              n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};\r\n              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';\r\n              n.queue=[];\r\n              t=b.createElement(e);\r\n              t.async=!0;\r\n              t.src=v;\r\n              try {\r\n                s=b.getElementsByTagName(e)[0];\r\n                if (s && s.parentNode) {\r\n                  s.parentNode.insertBefore(t,s);\r\n                } else {\r\n                  console.log('Facebook Pixel: No script tag found, appending to head');\r\n                  b.head.appendChild(t);\r\n                }\r\n              } catch(insertErr) {\r\n                console.log('Facebook Pixel: Script insertion skipped', insertErr);\r\n              }\r\n              fbq('init', '${FACEBOOK_PIXEL_ID}');\r\n              fbq('track', 'PageView');\r\n            } catch (error) {\r\n              console.log('Facebook Pixel: Setup skipped', error);\r\n            }\r\n          })(window, document,'script', 'https://connect.facebook.net/en_US/fbevents.js');\r\n        `,\r\n      }}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,oBAAoB;AAEnB,SAAS;IACd,qBACE,4SAAC,gPAAA,CAAA,UAAM;QACL,IAAG;QACH,UAAS;QACT,yBAAyB;YACvB,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;2BAqBU,EAAE,kBAAkB;;;;;;QAMvC,CAAC;QACH;;;;;;AAGN;KArCgB", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\ntype PriceOptions = {\r\n  price: number;\r\n  options?: {\r\n    notation: \"compact\" | \"standard\";\r\n    currency: string;\r\n  };\r\n};\r\n\r\nexport const formatPrice = ({ price, options }: PriceOptions) => {\r\n  const { currency = \"USD\", notation = \"standard\" } = options || {};\r\n\r\n  const numericPrice = typeof price === \"string\" ? parseFloat(price) : price;\r\n\r\n  return new Intl.NumberFormat(\"en-IN\", {\r\n    style: \"currency\",\r\n    currency,\r\n    notation: numericPrice < 100000 ? undefined : notation,\r\n    maximumFractionDigits: 0,\r\n  }).format(numericPrice);\r\n};\r\n\r\nexport function formatDate(date: string) {\r\n  if (!date?.includes(\"T\")) {\r\n    date = `${date}T00:00:00`;\r\n  }\r\n\r\n  return new Date(date).toLocaleString(\"en-us\", {\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    year: \"numeric\",\r\n  });\r\n}\r\n\r\nexport const transitionVariants = {\r\n  item: {\r\n    hidden: {\r\n      opacity: 0,\r\n      filter: \"blur(20px)\",\r\n      y: 40,\r\n    },\r\n    visible: {\r\n      opacity: 1,\r\n      filter: \"blur(0px)\",\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\" as const,\r\n        bounce: 0.3,\r\n        duration: 1.5,\r\n        delay: 1.2,\r\n      },\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,OAAO,EAAgB;IAC1D,MAAM,EAAE,WAAW,KAAK,EAAE,WAAW,UAAU,EAAE,GAAG,WAAW,CAAC;IAEhE,MAAM,eAAe,OAAO,UAAU,WAAW,WAAW,SAAS;IAErE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,UAAU,eAAe,SAAS,YAAY;QAC9C,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,IAAI,CAAC,MAAM,SAAS,MAAM;QACxB,OAAO,GAAG,KAAK,SAAS,CAAC;IAC3B;IAEA,OAAO,IAAI,KAAK,MAAM,cAAc,CAAC,SAAS;QAC5C,OAAO;QACP,KAAK;QACL,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,MAAM;QACJ,QAAQ;YACN,SAAS;YACT,QAAQ;YACR,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,QAAQ;YACR,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,OAAO;YACT;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\r\nimport * as React from \"react\";\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction Tooltip({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />;\r\n}\r\n\r\nfunction TooltipTrigger({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 4,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"z-50 max-w-sm rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n          className,\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px] bg-primary fill-primary\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  );\r\n}\r\n\r\nexport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAMA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,4SAAC,gRAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,4SAAC,gRAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;MAFS;AAIT,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,4SAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAFS;AAIT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,4SAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,4SAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8WACA;YAED,GAAG,KAAK;;gBAER;8BACD,4SAAC,gRAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/posthog-tracking.ts"], "sourcesContent": ["import { AuthState } from \"@/types\";\r\nimport posthog from \"posthog-js\";\r\n\r\nexport function identify(user: AuthState) {\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  if (typeof window !== \"undefined\" && (window as any).posthog && user.kinde_id) {\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    (window as any).posthog.identify(user.kinde_id, {\r\n      email: user.email,\r\n      is_superuser: user.userFromDb?.is_superuser,\r\n      is_subscribed: user.isSubscribed,\r\n      plan: user.userFromDb?.plan,\r\n    });\r\n  }\r\n}\r\n\r\nexport function reset() {\r\n  try {\r\n    posthog.reset();\r\n  } catch (error) {\r\n    console.error(\"Error resetting PostHog:\", error);\r\n  }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport function capture(event: string, properties?: any) {\r\n  try {\r\n    posthog.capture(event, properties);\r\n  } catch (error) {\r\n    console.error(\"Error capturing event:\", error);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;AAEO,SAAS,SAAS,IAAe;IACtC,8DAA8D;IAC9D,IAAI,aAAkB,eAAe,AAAC,OAAe,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC7E,8DAA8D;QAC7D,OAAe,OAAO,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC9C,OAAO,KAAK,KAAK;YACjB,cAAc,KAAK,UAAU,EAAE;YAC/B,eAAe,KAAK,YAAY;YAChC,MAAM,KAAK,UAAU,EAAE;QACzB;IACF;AACF;AAEO,SAAS;IACd,IAAI;QACF,8MAAA,CAAA,UAAO,CAAC,KAAK;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF;AAGO,SAAS,QAAQ,KAAa,EAAE,UAAgB;IACrD,IAAI;QACF,8MAAA,CAAA,UAAO,CAAC,OAAO,CAAC,OAAO;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;IAC1C;AACF", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/hooks/use-debounce.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport const useDebounce = <T>(\r\n  value: T,\r\n  delay: number,\r\n): { debouncedValue: T; isLoading: boolean } => {\r\n  const [debouncedValue, setDebouncedValue] = useState(value);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsLoading(true);\r\n    const handler = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n      setIsLoading(false);\r\n    }, delay);\r\n\r\n    return () => {\r\n      clearTimeout(handler);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return { debouncedValue, isLoading };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIO,MAAM,cAAc,CACzB,OACA;;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;YACb,MAAM,UAAU;iDAAW;oBACzB,kBAAkB;oBAClB,aAAa;gBACf;gDAAG;YAEH;yCAAO;oBACL,aAAa;gBACf;;QACF;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;QAAE;QAAgB;IAAU;AACrC;GApBa", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/utils/auth-utils.ts"], "sourcesContent": ["type Key =\r\n  | \"access_token\"\r\n  | \"kinde_id\"\r\n  | \"email\"\r\n  | \"discount_code\"\r\n  | \"projectIdea\"\r\n  | \"newProjectIdea\"\r\n  | \"initial_plan\";\r\n\r\nexport const setItemWithExpiry = (key: Key, value: string, ttl: number) => {\r\n  // Check if we're in a browser environment\r\n  if (typeof window === \"undefined\") return;\r\n\r\n  const now = Date.now();\r\n  const item = {\r\n    value: value,\r\n    expiry: now + ttl,\r\n  };\r\n  localStorage.setItem(key, JSON.stringify(item));\r\n};\r\n\r\nexport const getItemWithExpiry = (key: Key) => {\r\n  // Check if we're in a browser environment\r\n  if (typeof window === \"undefined\") return null;\r\n\r\n  const itemStr = localStorage.getItem(key);\r\n  if (!itemStr) {\r\n    return null;\r\n  }\r\n\r\n  const item = JSON.parse(itemStr);\r\n  const now = Date.now();\r\n\r\n  if (now > item.expiry) {\r\n    localStorage.removeItem(key);\r\n    return null;\r\n  }\r\n  return item.value;\r\n};\r\n\r\nexport const removeItemWithExpiry = (key: Key) => {\r\n  // Check if we're in a browser environment\r\n  if (typeof window === \"undefined\") return;\r\n\r\n  localStorage.removeItem(key);\r\n};\r\n\r\n// Cookie utility functions\r\nexport const setCookieWithExpiry = (name: Key, value: string, ttlMs: number) => {\r\n  // Check if we're in a browser environment\r\n  if (typeof document === \"undefined\") return;\r\n\r\n  const isProd = process.env.NODE_ENV === \"production\";\r\n  const maxAge = Math.floor(ttlMs / 1000); // Convert milliseconds to seconds for cookie\r\n  document.cookie = `${name}=${value}; path=/; max-age=${maxAge}; SameSite=Lax${\r\n    isProd ? \"; Secure\" : \"\"\r\n  }`;\r\n};\r\n\r\nexport const getCookie = (name: Key): string | null => {\r\n  // Check if we're in a browser environment\r\n  if (typeof document === \"undefined\") return null;\r\n\r\n  try {\r\n    return (\r\n      document.cookie\r\n        .split(\"; \")\r\n        .find((row) => row.startsWith(`${name}=`))\r\n        ?.split(\"=\")[1] || null\r\n    );\r\n  } catch (error) {\r\n    console.error(`Error getting cookie ${name}:`, error);\r\n    return null;\r\n  }\r\n};\r\n\r\nexport const removeCookie = (name: Key) => {\r\n  // Check if we're in a browser environment\r\n  if (typeof document === \"undefined\") return;\r\n\r\n  const isProd = process.env.NODE_ENV === \"production\";\r\n  // Set multiple cookie deletion attempts with different paths to ensure complete removal\r\n  document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${\r\n    isProd ? \"; Secure\" : \"\"\r\n  }`;\r\n  document.cookie = `${name}=; path=/; domain=${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${\r\n    isProd ? \"; Secure\" : \"\"\r\n  }`;\r\n  document.cookie = `${name}=; path=/; domain=.${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:01 GMT; SameSite=Lax${\r\n    isProd ? \"; Secure\" : \"\"\r\n  }`;\r\n};\r\n\r\n/**\r\n * Clears all cookies for the current domain\r\n */\r\nexport const clearAllCookies = () => {\r\n  // Check if we're in a browser environment\r\n  if (typeof document === \"undefined\") return;\r\n\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i];\r\n    const eqPos = cookie.indexOf(\"=\");\r\n    const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();\r\n\r\n    // Skip empty names\r\n    if (!name) continue;\r\n\r\n    // Use the removeCookie function for each cookie\r\n    removeCookie(name as Key);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAoDiB;AA3CV,MAAM,oBAAoB,CAAC,KAAU,OAAe;IACzD,0CAA0C;IAC1C,uCAAmC;;IAAM;IAEzC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,OAAO;QACX,OAAO;QACP,QAAQ,MAAM;IAChB;IACA,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;AAC3C;AAEO,MAAM,oBAAoB,CAAC;IAChC,0CAA0C;IAC1C,uCAAmC;;IAAW;IAE9C,MAAM,UAAU,aAAa,OAAO,CAAC;IACrC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,OAAO,KAAK,KAAK,CAAC;IACxB,MAAM,MAAM,KAAK,GAAG;IAEpB,IAAI,MAAM,KAAK,MAAM,EAAE;QACrB,aAAa,UAAU,CAAC;QACxB,OAAO;IACT;IACA,OAAO,KAAK,KAAK;AACnB;AAEO,MAAM,uBAAuB,CAAC;IACnC,0CAA0C;IAC1C,uCAAmC;;IAAM;IAEzC,aAAa,UAAU,CAAC;AAC1B;AAGO,MAAM,sBAAsB,CAAC,MAAW,OAAe;IAC5D,0CAA0C;IAC1C,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,SAAS,oDAAyB;IACxC,MAAM,SAAS,KAAK,KAAK,CAAC,QAAQ,OAAO,6CAA6C;IACtF,SAAS,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,MAAM,kBAAkB,EAAE,OAAO,cAAc,EAC1E,6EAAsB,IACtB;AACJ;AAEO,MAAM,YAAY,CAAC;IACxB,0CAA0C;IAC1C,IAAI,OAAO,aAAa,aAAa,OAAO;IAE5C,IAAI;QACF,OACE,SAAS,MAAM,CACZ,KAAK,CAAC,MACN,IAAI,CAAC,CAAC,MAAQ,IAAI,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC,IACtC,MAAM,IAAI,CAAC,EAAE,IAAI;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC,EAAE;QAC/C,OAAO;IACT;AACF;AAEO,MAAM,eAAe,CAAC;IAC3B,0CAA0C;IAC1C,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,SAAS,oDAAyB;IACxC,wFAAwF;IACxF,SAAS,MAAM,GAAG,GAAG,KAAK,8DAA8D,EACtF,6EAAsB,IACtB;IACF,SAAS,MAAM,GAAG,GAAG,KAAK,kBAAkB,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,qDAAqD,EAC1H,6EAAsB,IACtB;IACF,SAAS,MAAM,GAAG,GAAG,KAAK,mBAAmB,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,qDAAqD,EAC3H,6EAAsB,IACtB;AACJ;AAKO,MAAM,kBAAkB;IAC7B,0CAA0C;IAC1C,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;IAEtC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,SAAS,OAAO,CAAC,EAAE;QACzB,MAAM,QAAQ,OAAO,OAAO,CAAC;QAC7B,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,SAAS,CAAC,GAAG,OAAO,IAAI,KAAK,OAAO,IAAI;QAEzE,mBAAmB;QACnB,IAAI,CAAC,MAAM;QAEX,gDAAgD;QAChD,aAAa;IACf;AACF", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/debug.ts"], "sourcesContent": ["export const debug = process.env.NODE_ENV === \"development\" ? console.log.bind(console) : () => {};\r\n"], "names": [], "mappings": ";;;AAAqB;AAAd,MAAM,QAAQ,uCAAyC,QAAQ,GAAG,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/lib/api.ts"], "sourcesContent": ["import {\r\n  clearAllC<PERSON><PERSON>,\r\n  getC<PERSON>ie,\r\n  getItemWithExpiry,\r\n  remove<PERSON><PERSON>ie,\r\n  removeItemWithExpiry,\r\n  setCookieWithExpiry,\r\n} from \"@/utils/auth-utils\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport axios, { AxiosInstance } from \"axios\";\r\nimport { debug } from \"./debug\";\r\n\r\nexport const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\";\r\n\r\nexport const getToken = () => {\r\n  if (typeof window === \"undefined\") {\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    const cookies = document.cookie.split(\"; \");\r\n    const tokenCookie = cookies.find((row) => row.startsWith(\"access_token=\"));\r\n\r\n    if (!tokenCookie) {\r\n      console.warn(\"No access_token cookie found\");\r\n      return null;\r\n    }\r\n\r\n    const token = tokenCookie.split(\"=\")[1];\r\n    if (!token) {\r\n      console.warn(\"Access token is empty\");\r\n      return null;\r\n    }\r\n\r\n    return token;\r\n  } catch (error) {\r\n    console.error(\"Error parsing access token from cookies:\", error);\r\n    return null;\r\n  }\r\n};\r\n\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  withCredentials: true,\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n});\r\n\r\napi.interceptors.request.use(\r\n  async (config) => {\r\n    let token = getCookie(\"access_token\");\r\n\r\n    if (!token) {\r\n      token = getItemWithExpiry(\"access_token\");\r\n      if (token) {\r\n        debug(\"Token found in localStorage but not in cookie, restoring cookie\");\r\n        setCookieWithExpiry(\"access_token\", token, 600 * 60 * 1000);\r\n      }\r\n    }\r\n\r\n    if (token) {\r\n      config.headers[\"Authorization\"] = `Bearer ${token}`;\r\n    } else {\r\n      console.warn(\"No valid token found for API request\");\r\n\r\n      const cookieStatus = document.cookie ? \"Cookies exist\" : \"No cookies found\";\r\n      const tokenCookie = document.cookie\r\n        .split(\"; \")\r\n        .find((row) => row.startsWith(\"access_token=\"));\r\n      console.warn(\r\n        `Cookie status: ${cookieStatus}, Access token cookie: ${tokenCookie ? \"present\" : \"missing\"}`,\r\n      );\r\n\r\n      if (\r\n        !config.url?.includes(\"/logout\") &&\r\n        !config.url?.includes(\"/kinde_user\") &&\r\n        typeof window !== \"undefined\"\r\n      ) {\r\n        debug(\"Attempting to refresh auth state before proceeding with request\");\r\n        try {\r\n          const authData = localStorage.getItem(\"auth-storage\");\r\n          if (authData) {\r\n            const parsedData = JSON.parse(authData);\r\n            if (parsedData?.state?.user?.access_token) {\r\n              token = parsedData.state.user.access_token;\r\n              debug(\"Found token in auth store, applying to request\");\r\n              setCookieWithExpiry(\"access_token\", token!, 600 * 60 * 1000);\r\n              config.headers[\"Authorization\"] = `Bearer ${token}`;\r\n            } else {\r\n              debug(\"No valid token in auth store, clearing persisted state\");\r\n              localStorage.removeItem(\"auth-storage\");\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error(\"Error trying to restore auth state:\", e);\r\n          localStorage.removeItem(\"auth-storage\");\r\n        }\r\n      }\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error(\"Error in request interceptor:\", error);\r\n    return Promise.reject(error);\r\n  },\r\n);\r\n\r\nconst addErrorHandling = (axiosInstance: AxiosInstance) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    async (error) => {\r\n      console.error(\"API Error:\", error.response);\r\n\r\n      if (error.response && error.response.status === 401) {\r\n        if (typeof window !== \"undefined\") {\r\n          localStorage.clear();\r\n          localStorage.removeItem(\"auth-storage\");\r\n        }\r\n\r\n        clearAllCookies();\r\n\r\n        removeCookie(\"access_token\");\r\n        removeCookie(\"kinde_id\");\r\n\r\n        removeItemWithExpiry(\"access_token\");\r\n        removeItemWithExpiry(\"kinde_id\");\r\n\r\n        if (typeof window !== \"undefined\") {\r\n          window.location.href = \"/\";\r\n          window.location.reload();\r\n        }\r\n      }\r\n\r\n      if (error.response && error.response.data) {\r\n        throw error.response.data;\r\n      }\r\n\r\n      throw error;\r\n    },\r\n  );\r\n};\r\n\r\naddErrorHandling(api);\r\n\r\nconst handleApiError = (error: unknown, customMessage: string) => {\r\n  console.error(customMessage, error);\r\n  throw error;\r\n};\r\n\r\nexport const createProject = async (name: string, stack?: string) => {\r\n  try {\r\n    const response = await api.post(\"/project\", null, { params: { name, stack } });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Create project error:\");\r\n  }\r\n};\r\n\r\nexport const getProject = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/project/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get project error:\");\r\n  }\r\n};\r\n\r\nexport const deleteProject = async (projectId: string) => {\r\n  try {\r\n    const response = await api.delete(`/project/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Delete project error:\");\r\n  }\r\n};\r\n\r\nexport const listProjects = async () => {\r\n  try {\r\n    const response = await api.get(\"/projects\");\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"List projects error:\");\r\n  }\r\n};\r\nexport const updateProject = async (\r\n  projectId: string,\r\n  updateData: {\r\n    name?: string | null;\r\n    onboarding_completed?: boolean | null;\r\n    isPublic?: boolean | null;\r\n    preview_image_url?: string | null;\r\n    custom_instructions?: string | null;\r\n  },\r\n) => {\r\n  try {\r\n    const response = await api.put(\r\n      `/project/${projectId}`,\r\n      {},\r\n      {\r\n        params: updateData,\r\n      },\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update project error:\");\r\n  }\r\n};\r\n\r\nexport const startAgent = async ({\r\n  threadId,\r\n  workspaceId,\r\n  projectId,\r\n  maxIterations = 5,\r\n  userInput,\r\n  agentType = \"frontend\",\r\n  selectedPaths = [],\r\n  objectiveImages = [],\r\n  token,\r\n}: {\r\n  threadId: string;\r\n  workspaceId: string;\r\n  projectId: string;\r\n  maxIterations?: number;\r\n  userInput: string;\r\n  agentType?: string;\r\n  selectedPaths?: string[];\r\n  objectiveImages?: string[];\r\n  token?: string;\r\n}) => {\r\n  try {\r\n    const response = await api.post(\"/agent/start\", {\r\n      thread_id: threadId,\r\n      workspace_id: workspaceId,\r\n      project_id: projectId,\r\n      max_iterations: maxIterations,\r\n      user_input: userInput,\r\n      agent_type: agentType,\r\n      selected_paths: selectedPaths,\r\n      objective_images: objectiveImages,\r\n      token: token,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Start agent error:\");\r\n  }\r\n};\r\n\r\nexport const stopAgent = async (threadId: string) => {\r\n  try {\r\n    const response = await api.post(`/agent/stop/${threadId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Stop session error:\");\r\n  }\r\n};\r\n\r\nexport const getProjectThreads = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/project/${projectId}/threads`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get project threads error:\");\r\n  }\r\n};\r\n\r\nexport const getThread = async (threadId: number) => {\r\n  try {\r\n    const response = await api.get(`/thread/${threadId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get thread error:\");\r\n  }\r\n};\r\n\r\nexport const createThread = async (projectId: string, pageRoute?: string) => {\r\n  try {\r\n    const response = await api.post(\"/thread\", null, {\r\n      params: {\r\n        project_id: projectId,\r\n        page_route: pageRoute,\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Create thread error:\");\r\n  }\r\n};\r\n\r\nexport const startSessionRun = async (\r\n  projectId: string,\r\n  threadId: string | null,\r\n  objective: string,\r\n  objectiveImages: File[],\r\n  forceCommunicationInterval: boolean,\r\n  mode: string = \"interactive\",\r\n  promptSet: string = \"1\",\r\n  autonomousIterations: number = 20,\r\n  selectedPaths: string[] = [],\r\n  model_id: string = \"1\",\r\n) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append(\"project_id\", projectId);\r\n    if (threadId) formData.append(\"thread_id\", threadId);\r\n    formData.append(\"objective\", objective);\r\n    formData.append(\"force_communication_interval\", forceCommunicationInterval.toString());\r\n    formData.append(\"mode\", mode);\r\n    formData.append(\"prompt_set\", promptSet);\r\n    formData.append(\"autonomous_iterations\", autonomousIterations.toString());\r\n    formData.append(\"selected_paths\", selectedPaths.join(\",\"));\r\n    formData.append(\"model_id\", model_id);\r\n\r\n    if (objectiveImages && objectiveImages.length > 0) {\r\n      objectiveImages.forEach((image) => {\r\n        formData.append(\"objective_images\", image);\r\n      });\r\n    }\r\n\r\n    const response = await api.post(\"/start_session_run\", formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Start session run error:\");\r\n  }\r\n};\r\n\r\nexport const addMessageToThread = async (\r\n  threadId: string,\r\n  content: string,\r\n  images: File[] = [],\r\n) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append(\"content\", content);\r\n\r\n    // Validate and append each image\r\n    images.forEach((image, index) => {\r\n      if (!(image instanceof File) || !image.type.startsWith(\"image/\")) {\r\n        throw new Error(`Invalid image file at index ${index}`);\r\n      }\r\n      formData.append(\"images\", image);\r\n    });\r\n\r\n    debug(\"Sending message with form data:\", {\r\n      content,\r\n      imageCount: images.length,\r\n      imageTypes: images.map((img) => img.type),\r\n    });\r\n\r\n    const response = await api.post(`/thread/${threadId}/add_message`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n      // Add timeout for large file uploads\r\n      timeout: 30000,\r\n    });\r\n\r\n    if (!response.data) {\r\n      throw new Error(\"No response data received from server\");\r\n    }\r\n\r\n    debug(\"File uploaded successfully:\", response.data);\r\n    return response.data;\r\n  } catch (error: unknown) {\r\n    const err = error as {\r\n      detail?: string;\r\n      message?: string;\r\n      response?: {\r\n        data?: unknown;\r\n      };\r\n    };\r\n\r\n    if (err.detail && err.detail.includes(\"Incomplete tool responses\")) {\r\n      throw new Error(\r\n        \"Wait a short moment. You have to wait for the Agent to complete his actions, then you can send the message.\",\r\n      );\r\n    }\r\n\r\n    console.error(\"Error adding message to thread:\", err);\r\n    throw new Error(err.detail || err.message || \"Failed to add message to thread\");\r\n  }\r\n};\r\n\r\nexport const getAgentStatus = async (threadId: string) => {\r\n  try {\r\n    const response = await api.get(`/agent/status/${threadId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get agent status error:\");\r\n  }\r\n};\r\n\r\nexport const getThreadLlmMessages = async (\r\n  threadId: string,\r\n  {\r\n    hideToolMsgs = false,\r\n    onlyLatestAssistant = false,\r\n    lastNLlmMessages,\r\n  }: {\r\n    hideToolMsgs?: boolean;\r\n    onlyLatestAssistant?: boolean;\r\n    lastNLlmMessages?: number;\r\n  } = {},\r\n) => {\r\n  try {\r\n    const params = new URLSearchParams({\r\n      ...(hideToolMsgs && { hide_tool_msgs: hideToolMsgs.toString() }),\r\n      ...(onlyLatestAssistant && { only_latest_assistant: onlyLatestAssistant.toString() }),\r\n      ...(lastNLlmMessages !== undefined && { last_n_llm_messages: lastNLlmMessages.toString() }),\r\n    });\r\n\r\n    const response = await api.get(`/threads/${threadId}/llm_history_messages?${params}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get thread LLM messages error:\");\r\n  }\r\n};\r\n\r\nexport const getThreadSessionStatus = async (threadId: string) => {\r\n  try {\r\n    const response = await api.get(`/thread_session_status/${threadId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get thread session status error:\");\r\n  }\r\n};\r\n\r\nexport const getProjectActiveSessionStatus = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/project_active_session_status/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get project active session status error:\");\r\n  }\r\n};\r\n\r\nexport const getKindeUser = async (kindeId: string, logout?: () => void) => {\r\n  try {\r\n    const response = await api.get(`/kinde_user/${kindeId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    if (logout) {\r\n      logout();\r\n    }\r\n    handleApiError(error, \"Get Kinde user error:\");\r\n  }\r\n};\r\n\r\nexport type PlanType =\r\n  | \"entry\"\r\n  | \"boost\"\r\n  | \"fly\"\r\n  | \"pro_enterprise\"\r\n  | \"elite_enterprise\"\r\n  | \"wholesale\";\r\n\r\nexport const subscriptionCheckout = async (\r\n  plan: PlanType,\r\n  kindeId: string | null = null,\r\n  isUpgrade: boolean = false,\r\n  toltReferral: string | null = null,\r\n  discountCode: string | null = null,\r\n) => {\r\n  try {\r\n    const params: Record<string, unknown> = {\r\n      plan,\r\n      kinde_id: kindeId,\r\n      is_upgrade: isUpgrade,\r\n    };\r\n\r\n    debug(\"subscriptionCheckout\", params);\r\n\r\n    if (toltReferral) {\r\n      params.tolt_referral = toltReferral;\r\n    }\r\n\r\n    if (discountCode) {\r\n      params.discount_code = discountCode;\r\n    }\r\n\r\n    const response = await api.post(\"/subscription_checkout\", null, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Subscription checkout error:\");\r\n  }\r\n};\r\n\r\nexport const oneTimePaymentCheckout = async (plan: string, kindeId: string | null = null) => {\r\n  try {\r\n    const response = await api.post(\"/one_time_payment_checkout\", null, {\r\n      params: { plan, kinde_id: kindeId },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"One-time payment checkout error:\");\r\n  }\r\n};\r\n\r\nexport const freeRequestCheckout = async (email: string, package_total_free_request: number) => {\r\n  try {\r\n    const response = await api.post(\"/free_request_checkout\", null, {\r\n      params: { email, package_total_free_request },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Free request checkout error:\");\r\n  }\r\n};\r\n\r\nexport const createCustomerPortalSession = async (customerId: string) => {\r\n  try {\r\n    const response = await api.post(\"/customer_portal_session\", null, {\r\n      params: { customer_id: customerId },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Create customer portal session error:\");\r\n  }\r\n};\r\n\r\nexport const submitFeedback = async (feedbackData: unknown) => {\r\n  try {\r\n    const response = await api.post(\"/feedback\", feedbackData);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Submit feedback error:\");\r\n  }\r\n};\r\n\r\nexport const submitAnswer = async (threadId: string, promptId: string, answer: string) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append(\"prompt_id\", promptId);\r\n    formData.append(\"answer\", answer);\r\n    const response = await api.post(`/thread/${threadId}/submit_answer`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Submit answer error:\");\r\n  }\r\n};\r\n\r\nexport const useCheckAndStart = () => {\r\n  return useMutation({\r\n    mutationFn: async (project_id: string) => {\r\n      try {\r\n        const response = await api.post(\"/check_and_start_env\", { project_id });\r\n        return response.data;\r\n      } catch (error) {\r\n        handleApiError(error, \"Check and start error:\");\r\n      }\r\n    },\r\n  });\r\n};\r\n\r\nexport const getGithubInfo = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/github-info/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get GitHub info error:\");\r\n  }\r\n};\r\n\r\nexport const addGithubCollaborator = async (\r\n  projectId: string,\r\n  username: string,\r\n  github_repo: string,\r\n) => {\r\n  try {\r\n    const response = await api.post(\r\n      `/git-add-collaborator/${projectId}`,\r\n      JSON.stringify({ username, github_repo }),\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      },\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Add GitHub collaborator error:\");\r\n  }\r\n};\r\n\r\nexport const revertGithubCommit = async (\r\n  projectId: string,\r\n  commitHash: string,\r\n  revertAndFullReset = false,\r\n) => {\r\n  try {\r\n    const response = await api.post(\r\n      `/git-revert-commit/${projectId}`,\r\n      JSON.stringify({\r\n        commit: { commit_hash: commitHash },\r\n        revert_and_full_reset: revertAndFullReset,\r\n      }),\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      },\r\n    );\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Revert GitHub commit error:\");\r\n  }\r\n};\r\n\r\n// ----------------- CODE EDITOR ROUTES -----------------\r\nexport const getFiles = async (project_id: string, path = \"/\") => {\r\n  try {\r\n    const response = await api.get(`/get-files/${project_id}`, { params: { path } });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching files:\", error);\r\n    const err = error as {\r\n      response?: {\r\n        status: number;\r\n        headers: unknown;\r\n        data?: { message?: string };\r\n      };\r\n      request?: unknown;\r\n      message?: string;\r\n    };\r\n\r\n    if (err.response) {\r\n      if (err.response.status === 404) {\r\n        console.warn(`No files found for project ${project_id} at path ${path}`);\r\n        return { files: [] };\r\n      }\r\n      console.error(\"Response status:\", err.response.status);\r\n      console.error(\"Response headers:\", err.response.headers);\r\n      throw new Error(\r\n        `Server error: ${err.response.status} - ${err.response.data?.message || \"Unknown error\"}`,\r\n      );\r\n    } else if (err.request) {\r\n      throw new Error(\"No response received from server. Please check your network connection.\");\r\n    } else {\r\n      throw new Error(`Error setting up request: ${err.message}`);\r\n    }\r\n  }\r\n};\r\n\r\nexport const getFileContent = async (project_id: string, filePath: string) => {\r\n  try {\r\n    const response = await api.get(`/get-file-content/${project_id}`, {\r\n      params: { file_path: filePath },\r\n      responseType: \"arraybuffer\",\r\n    });\r\n\r\n    const contentType = response.headers[\"content-type\"];\r\n    const fileName = filePath.split(\"/\").pop() || \"\";\r\n\r\n    if (\r\n      contentType.startsWith(\"text/\") ||\r\n      contentType === \"application/json\" ||\r\n      fileName.startsWith(\".\")\r\n    ) {\r\n      const textContent = new TextDecoder().decode(response.data);\r\n\r\n      if (contentType === \"application/json\") {\r\n        return JSON.parse(textContent);\r\n      }\r\n      return textContent;\r\n    } else {\r\n      const blob = new Blob([response.data], { type: contentType });\r\n      return URL.createObjectURL(blob);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error fetching file content:\", error);\r\n    const err = error as {\r\n      response?: { status: number };\r\n      request?: unknown;\r\n      message?: string;\r\n    };\r\n\r\n    if (err.response) {\r\n      throw new Error(`Failed to fetch file content. Server returned ${err.response.status}`);\r\n    } else if (err.request) {\r\n      throw new Error(\"No response received from server. Please check your network connection.\");\r\n    } else {\r\n      throw new Error(`Error setting up request: ${err.message}`);\r\n    }\r\n  }\r\n};\r\n\r\nexport const createFile = async (\r\n  project_id: string,\r\n  filePath: string,\r\n  content = \"\",\r\n  isDirectory = false,\r\n) => {\r\n  try {\r\n    const response = await api.post(\"/create-file\", {\r\n      project_id,\r\n      path: filePath,\r\n      content,\r\n      isDirectory,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Create file error:\");\r\n  }\r\n};\r\n\r\nexport const updateFile = async (project_id: string, filePath: string, content: string) => {\r\n  try {\r\n    const response = await api.put(\"/update-file\", { project_id, path: filePath, content });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Update file error:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteFile = async (project_id: string, filePath: string) => {\r\n  try {\r\n    const response = await api.delete(\"/delete-file\", {\r\n      params: { project_id, path: filePath },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Delete file error:\");\r\n  }\r\n};\r\n\r\nexport const executeCommand = async (project_id: string, command: string) => {\r\n  try {\r\n    const response = await api.post(\"/execute-command\", { project_id, command });\r\n    return response.data;\r\n  } catch (error) {\r\n    const err = error as {\r\n      response?: {\r\n        status?: number;\r\n        data?: unknown;\r\n      };\r\n      message?: string;\r\n    };\r\n\r\n    if (err.response?.status === 422) {\r\n      console.error(\"Validation error during command execution:\", err.response.data);\r\n      const errorDetail =\r\n        err.response.data && typeof err.response.data === \"object\" && \"detail\" in err.response.data\r\n          ? Array.isArray(err.response.data.detail)\r\n            ? (err.response.data.detail as { msg: string }[]).map((e) => e.msg).join(\", \")\r\n            : String(err.response.data.detail)\r\n          : \"Invalid command parameters\";\r\n      throw new Error(`Command execution validation failed: ${errorDetail}`);\r\n    }\r\n\r\n    handleApiError(error, \"Execute command error:\");\r\n  }\r\n};\r\n\r\nexport const gitCommit = async (project_id: string, commit_message: string) => {\r\n  try {\r\n    const response = await api.post(\"/git-commit\", { project_id, commit_message });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Git commit error:\");\r\n  }\r\n};\r\n\r\nexport async function uploadFile({\r\n  projectId,\r\n  file,\r\n  relativePath,\r\n  onProgress,\r\n}: {\r\n  projectId: string;\r\n  file: File;\r\n  relativePath: string;\r\n  onProgress?: (progress: number) => void;\r\n}) {\r\n  try {\r\n    if (!(file instanceof File)) {\r\n      throw new Error(\"Invalid file object provided\");\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"file\", file);\r\n    formData.append(\"relativePath\", relativePath || \"\");\r\n\r\n    debug(\"Uploading file:\", {\r\n      name: file.name,\r\n      type: file.type,\r\n      size: file.size,\r\n      path: relativePath,\r\n    });\r\n\r\n    const response = await api.post(`/upload-file`, formData, {\r\n      params: { project_id: projectId },\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n      timeout: 60000,\r\n      onUploadProgress: (progressEvent) => {\r\n        if (progressEvent.total && onProgress) {\r\n          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\r\n          onProgress(percentCompleted);\r\n        }\r\n      },\r\n    });\r\n\r\n    if (!response.data) {\r\n      throw new Error(\"No data received from server\");\r\n    }\r\n\r\n    debug(\"File uploaded successfully:\", response.data);\r\n    return response.data;\r\n  } catch (error) {\r\n    const err = error as {\r\n      response?: {\r\n        data?: unknown;\r\n        status?: number;\r\n      };\r\n      message?: string;\r\n    };\r\n\r\n    console.error(\"Error uploading file:\", {\r\n      error: err.response?.data || err.message,\r\n      status: err.response?.status,\r\n    });\r\n\r\n    if (err.response?.status === 422) {\r\n      const validationError = err.response.data as { detail?: Array<{ msg: string }> | string };\r\n      const errorMessage = validationError.detail\r\n        ? Array.isArray(validationError.detail)\r\n          ? validationError.detail.map((e) => e.msg).join(\", \")\r\n          : String(validationError.detail)\r\n        : \"Validation error during file upload\";\r\n      throw new Error(errorMessage);\r\n    }\r\n\r\n    if (err.response?.status === 413) {\r\n      throw new Error(\"File size too large. Please try a smaller file.\");\r\n    } else if (err.response?.status === 415) {\r\n      throw new Error(\"Unsupported file type. Please check the file format.\");\r\n    } else if (err.response?.status === 408) {\r\n      throw new Error(\"Upload timeout. Please try again.\");\r\n    }\r\n\r\n    throw new Error(err.message || \"Failed to upload file. Please try again.\");\r\n  }\r\n}\r\n\r\nexport const moveFile = async (project_id: string, sourcePath: string, targetPath: string) => {\r\n  try {\r\n    const response = await api.post(\"/move-file\", { project_id, sourcePath, targetPath });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Move file error:\");\r\n  }\r\n};\r\n\r\n{\r\n  /*\r\n    ===========================\r\n        Code Editor Route\r\n    ===========================\r\n*/\r\n}\r\n\r\nexport const getTemplateByIdentifier = async (identifier: string) => {\r\n  try {\r\n    const response = await api.get(`/templates/${identifier}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    const err = error as {\r\n      response?: { status: number };\r\n      message?: string;\r\n    };\r\n    console.error(`Error fetching template with id ${identifier}:`, err);\r\n    if (err.response?.status === 404) {\r\n      throw new Error(\"Template not found\");\r\n    }\r\n    throw new Error(\"Failed to fetch template\");\r\n  }\r\n};\r\n\r\nexport const setupVercelCLI = async (\r\n  projectId: string,\r\n  projectName: string,\r\n  vercelToken: string,\r\n) => {\r\n  try {\r\n    const response = await api.post(\"/vercel/setup-cli\", {\r\n      project_id: projectId,\r\n      project_name: projectName,\r\n      vercel_token: vercelToken,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Setup Vercel CLI error:\");\r\n  }\r\n};\r\n\r\nexport const vercelLogin = async (projectId: string, projectName: string, vercelToken: string) => {\r\n  try {\r\n    const response = await api.post(\"/vercel/login\", {\r\n      project_id: projectId,\r\n      project_name: projectName,\r\n      vercel_token: vercelToken,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Vercel login error:\");\r\n  }\r\n};\r\n\r\nexport const vercelLink = async (projectId: string, projectName: string, vercelToken: string) => {\r\n  try {\r\n    const response = await api.post(\"/vercel/link\", {\r\n      project_id: projectId,\r\n      project_name: projectName,\r\n      vercel_token: vercelToken,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Vercel link error:\");\r\n  }\r\n};\r\n\r\nexport const vercelEnvPush = async (\r\n  projectId: string,\r\n  projectName: string,\r\n  vercelToken: string,\r\n) => {\r\n  try {\r\n    const response = await api.post(\"/vercel/env-push\", {\r\n      project_id: projectId,\r\n      project_name: projectName,\r\n      vercel_token: vercelToken,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Vercel env push error:\");\r\n  }\r\n};\r\n\r\nexport const vercelDeploy = async (projectId: string, projectName: string, vercelToken: string) => {\r\n  try {\r\n    const response = await api.post(\"/vercel/deploy\", {\r\n      project_id: projectId,\r\n      project_name: projectName,\r\n      vercel_token: vercelToken,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Vercel deploy error:\");\r\n  }\r\n};\r\n\r\nexport const activateUser = async (kindeId: string) => {\r\n  try {\r\n    const response = await api.post(`/activate_user/${kindeId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Activate user error:\");\r\n  }\r\n};\r\n\r\nexport const startSessionRunWithWebSocket = async (\r\n  projectId: string,\r\n  threadId: string | null,\r\n  objective: string,\r\n  objectiveImages: File[],\r\n  forceCommunicationInterval: boolean,\r\n  mode: string = \"interactive\",\r\n  promptSet: string = \"1\",\r\n  autonomousIterations: number = 20,\r\n  selectedPaths: string[] = [],\r\n  model_id: string = \"1\",\r\n) => {\r\n  try {\r\n    const token = getToken();\r\n    const wsUrl = `${API_BASE_URL.replace(\"http\", \"ws\")}/start_session_run?token=${token}`;\r\n    const socket = new WebSocket(wsUrl);\r\n\r\n    socket.onopen = () => {\r\n      debug(\"WebSocket connection established for session run\");\r\n\r\n      const encodeImageAsBase64 = (file: File) => {\r\n        return new Promise<string>((resolve, reject) => {\r\n          const reader = new FileReader();\r\n          reader.readAsDataURL(file);\r\n          reader.onload = () => resolve((reader.result as string).split(\",\")[1]);\r\n          reader.onerror = (error) => reject(error);\r\n        });\r\n      };\r\n\r\n      Promise.all(objectiveImages.map((file) => encodeImageAsBase64(file)))\r\n        .then((encodedImages) => {\r\n          const message = JSON.stringify({\r\n            project_id: projectId,\r\n            thread_id: threadId,\r\n            objective: objective,\r\n            objective_images: encodedImages,\r\n            force_communication_interval: forceCommunicationInterval,\r\n            mode: mode,\r\n            prompt_set: parseInt(promptSet, 10),\r\n            autonomous_iterations: autonomousIterations,\r\n            selected_paths: selectedPaths,\r\n            model_id: model_id,\r\n          });\r\n          socket.send(message);\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Error encoding images:\", error);\r\n          handleApiError(error, \"Start session run with WebSocket error:\");\r\n        });\r\n    };\r\n\r\n    socket.onerror = (error) => {\r\n      console.error(\"WebSocket error:\", error);\r\n    };\r\n\r\n    socket.onclose = (event) => {\r\n      if (!event.wasClean) {\r\n        console.warn(`WebSocket closed unexpectedly, code: ${event.code}`);\r\n      } else {\r\n        debug(\"WebSocket connection closed\");\r\n      }\r\n    };\r\n\r\n    return socket;\r\n  } catch (error) {\r\n    handleApiError(error, \"Start session run with WebSocket error:\");\r\n  }\r\n};\r\n\r\nexport const connectTerminalSession = (project_id: string) => {\r\n  if (!project_id) {\r\n    console.error(\"No project_id provided to connectTerminalSession\");\r\n    return null;\r\n  }\r\n\r\n  const token = getToken();\r\n  const wsUrl = `${API_BASE_URL.replace(\"http\", \"ws\")}/terminal_session?token=${token}&project_id=${project_id}&session_id=${project_id || \"2\"}`;\r\n  const socket = new WebSocket(wsUrl);\r\n\r\n  socket.onopen = () => {\r\n    debug(\"Terminal WebSocket connection established\");\r\n  };\r\n\r\n  socket.onerror = (error) => {\r\n    console.error(\"Terminal WebSocket error:\", error);\r\n  };\r\n\r\n  socket.onclose = (event) => {\r\n    if (!event.wasClean) {\r\n      console.warn(`Terminal WebSocket closed unexpectedly, code: ${event.code}`);\r\n    } else {\r\n      debug(\"Terminal WebSocket connection closed\");\r\n    }\r\n  };\r\n\r\n  return socket;\r\n};\r\n\r\nexport const getFilePaths = async (projectId: string, fullPath: string, depth = 4) => {\r\n  try {\r\n    const response = await api.post(\"/get-file-paths\", {\r\n      project_id: projectId,\r\n      full_path: fullPath,\r\n      depth: depth,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get file paths error:\");\r\n  }\r\n};\r\n\r\nexport const enhancePrompt = async (prompt: string) => {\r\n  try {\r\n    const response = await api.post(\"/project/enhance-prompt\", { prompt });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Enhance prompt error:\");\r\n  }\r\n};\r\n\r\nexport const vercelUnlink = async (projectId: string) => {\r\n  try {\r\n    const response = await api.post(\"/vercel/unlink\", { project_id: projectId });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Vercel unlink error:\");\r\n  }\r\n};\r\n\r\nexport const createBlog = async (\r\n  headline: string,\r\n  metaDescription: string,\r\n  image: string,\r\n  categoryTitle: string,\r\n  tags: string[],\r\n  mdx: string,\r\n) => {\r\n  try {\r\n    const blogData = {\r\n      headline,\r\n      meta_description: metaDescription,\r\n      image,\r\n      category_title: categoryTitle,\r\n      tags,\r\n      mdx,\r\n    };\r\n    const response = await api.post(\"/blogs\", blogData);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Create blog error:\");\r\n  }\r\n};\r\n\r\nexport const updateBlog = async (\r\n  blogId: string,\r\n  headline: string,\r\n  metaDescription: string,\r\n  image: string,\r\n  categoryTitle: string,\r\n  tags: string[],\r\n  mdx: string,\r\n) => {\r\n  try {\r\n    const blogData = {\r\n      headline,\r\n      meta_description: metaDescription,\r\n      image,\r\n      category_title: categoryTitle,\r\n      tags,\r\n      mdx,\r\n    };\r\n    const updateData = Object.fromEntries(\r\n      Object.entries(blogData).filter(([, value]) => value !== undefined),\r\n    );\r\n\r\n    const response = await api.put(`/blogs/${blogId}`, updateData);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update blog error:\");\r\n  }\r\n};\r\n\r\nexport const listBlogs = async (limit = null, offset = null) => {\r\n  try {\r\n    const response = await api.get(\"/blogs\");\r\n\r\n    const allBlogs = Array.isArray(response.data) ? response.data : response.data?.items || [];\r\n    const total = allBlogs.length;\r\n\r\n    let paginatedBlogs = allBlogs;\r\n    if (limit !== null && offset !== null) {\r\n      paginatedBlogs = allBlogs.slice(offset, offset + limit);\r\n    }\r\n\r\n    return {\r\n      items: paginatedBlogs,\r\n      total: total,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"List blogs error:\", error);\r\n    return {\r\n      items: [],\r\n      total: 0,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getBlog = async (blogId: string) => {\r\n  try {\r\n    const response = await api.get(`/blogs/${blogId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get blog error:\");\r\n  }\r\n};\r\n\r\nexport const getBlogBySlug = async (slug: string) => {\r\n  try {\r\n    const response = await api.get(`/blogs/slug/${slug}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get blog by slug error:\");\r\n  }\r\n};\r\n\r\nexport const deleteBlog = async (blogId: string) => {\r\n  try {\r\n    const response = await api.delete(`/blogs/${blogId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Delete blog error:\");\r\n  }\r\n};\r\n\r\nexport const getTokenUsage = async (customerId: string, tokenEventName: string) => {\r\n  try {\r\n    const response = await api.get(`/token_usage/${customerId}`, {\r\n      params: { token_event_name: tokenEventName },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get token usage error:\");\r\n  }\r\n};\r\n\r\nexport const remixProject = async (projectId: string, envValues: Record<string, string>) => {\r\n  try {\r\n    const response = await api.post(`/project/${projectId}/remix`, {\r\n      env_values: envValues,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Remix project error:\");\r\n  }\r\n};\r\n\r\nexport const addTeamMember = async (projectId: string, teamEmail: string) => {\r\n  try {\r\n    const response = await api.post(`/project/${projectId}/team`, {\r\n      team_email: teamEmail,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Add team member error:\");\r\n  }\r\n};\r\n\r\nexport const removeTeamMember = async (projectId: string, teamEmail: string) => {\r\n  try {\r\n    const response = await api.delete(`/project/${projectId}/team/${teamEmail}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Remove team member error:\");\r\n  }\r\n};\r\n\r\nexport const getEnvKeys = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/get-env-keys/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get environment keys error:\");\r\n  }\r\n};\r\n\r\nexport const submitPrize = async (\r\n  url: string,\r\n  projectId: string,\r\n  userId: string,\r\n  platform: string,\r\n) => {\r\n  try {\r\n    const response = await api.post(\"/prize-submission\", {\r\n      url,\r\n      project_id: projectId,\r\n      user_id: userId,\r\n      description: \"project-social-share\",\r\n      prize_amount: 500000,\r\n      platform,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Prize submission error:\");\r\n  }\r\n};\r\n\r\nexport const renameThread = async (projectId: string, threadId: number, newName: string) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append(\"new_name\", newName);\r\n\r\n    const response = await api.patch(`/thread/${projectId}/${threadId}/rename`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Rename thread error:\");\r\n  }\r\n};\r\n\r\nexport const deleteThread = async (projectId: string, threadId: string) => {\r\n  try {\r\n    const response = await api.delete(`/thread/${projectId}/${threadId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Delete thread error:\");\r\n  }\r\n};\r\n\r\nexport const tokenPackageCheckout = async (packageSize: string, kindeId: string) => {\r\n  try {\r\n    const response = await api.post(\"/token_package_checkout\", null, {\r\n      params: {\r\n        package_size: packageSize,\r\n        kinde_id: kindeId,\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Token package checkout error:\");\r\n  }\r\n};\r\n\r\nexport const gitForcePull = async (projectId: string) => {\r\n  try {\r\n    const response = await api.post(`/git-force-pull/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Git force pull error:\");\r\n  }\r\n};\r\n\r\nexport const updateTechStack = async (\r\n  projectId: string,\r\n  techStackArray: Array<{ key: string; value: string }>,\r\n) => {\r\n  try {\r\n    const response = await api.post(`/project/${projectId}/tech-stack`, {\r\n      tech_stack_array: techStackArray,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update tech stack error:\");\r\n  }\r\n};\r\n\r\nexport const updateCustomInstructions = async (projectId: string, customInstructions: string) => {\r\n  try {\r\n    const response = await api.post(`/project/${projectId}/custom-instructions`, {\r\n      custom_instructions: customInstructions,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update custom instructions error:\");\r\n  }\r\n};\r\n\r\nexport const vercelConnectDomain = async (\r\n  projectId: string,\r\n  domain: string,\r\n  vercelToken: string | null,\r\n) => {\r\n  try {\r\n    const response = await api.post(\"/vercel-domain-connect\", {\r\n      project_id: projectId,\r\n      domain,\r\n      vercel_token: vercelToken,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Vercel domain connect error:\", error);\r\n\r\n    handleApiError(error, \"Vercel domain connect error:\");\r\n  }\r\n};\r\n\r\nexport const vercelDomainStatus = async (\r\n  projectId: string,\r\n  domain: string,\r\n  vercelToken: string | null,\r\n) => {\r\n  try {\r\n    const response = await api.post(\"/vercel/domain-status\", {\r\n      project_id: projectId,\r\n      domain,\r\n      vercel_token: vercelToken,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Vercel domain status check error:\");\r\n  }\r\n};\r\n\r\nexport const getTeamMembers = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/project/${projectId}/team`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get team members error:\");\r\n  }\r\n};\r\n\r\n// Admin API functions\r\nexport const getUsersByAdmin = async ({\r\n  skip = 0,\r\n  limit = 100,\r\n  search = \"\",\r\n}: {\r\n  skip?: number;\r\n  limit?: number;\r\n  search?: string;\r\n}) => {\r\n  try {\r\n    const params: {\r\n      skip: number;\r\n      limit: number;\r\n      search?: string;\r\n    } = {\r\n      skip,\r\n      limit,\r\n    };\r\n\r\n    if (search) params.search = search;\r\n\r\n    debug(\"Requesting users with params:\", params);\r\n\r\n    const response = await api.get(\"/admin/users\", { params });\r\n\r\n    debug(\"API Response structure:\", {\r\n      hasUsers: !!response.data.users,\r\n      usersCount: response.data.users?.length || 0,\r\n      total: response.data.total || 0,\r\n      isArray: Array.isArray(response.data),\r\n    });\r\n\r\n    // Handle array response (list of users without pagination info)\r\n    if (Array.isArray(response.data)) {\r\n      debug(\"Converting array response to paginated format\");\r\n      return {\r\n        users: response.data,\r\n        total: response.data.length,\r\n      };\r\n    }\r\n\r\n    // If the API already returns the structured format with pagination, use it\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Get users by admin error:\", error);\r\n    // Return empty result instead of throwing to prevent UI from breaking\r\n    return { users: [], total: 0 };\r\n  }\r\n};\r\n\r\nexport const getUserByEmailAdmin = async (email: string) => {\r\n  try {\r\n    const response = await api.get(`/admin/users/${email}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get user by email admin error:\");\r\n  }\r\n};\r\n\r\nexport const updateUserByAdmin = async (kindeId: string, userData: unknown) => {\r\n  try {\r\n    debug(\"Updating user with ID:\", kindeId);\r\n    debug(\"Update data:\", userData);\r\n\r\n    const response = await api.put(`/admin/users/${kindeId}`, userData);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update user by admin error:\");\r\n  }\r\n};\r\n\r\nexport const getUserProjectsByAdmin = async (email: string) => {\r\n  try {\r\n    const response = await api.get(`/admin/users/${email}/projects`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get user projects by admin error:\");\r\n  }\r\n};\r\n\r\nexport const getProjectByAdmin = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/admin/projects/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get project by admin error:\");\r\n  }\r\n};\r\n\r\nexport const updateProjectByAdmin = async (projectId: string, projectData: unknown) => {\r\n  try {\r\n    const response = await api.put(`/admin/projects/${projectId}`, projectData);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update project by admin error:\");\r\n  }\r\n};\r\n\r\nexport const updateDeploymentByAdmin = async (projectId: string, deploymentData: unknown) => {\r\n  try {\r\n    const response = await api.put(`/admin/projects/${projectId}/deployment`, deploymentData);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update deployment by admin error:\");\r\n  }\r\n};\r\n\r\nexport const getBillingDetailsByAdmin = async (customerId: string) => {\r\n  try {\r\n    const response = await api.get(`/admin/billing/customer/${customerId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get billing details by admin error:\");\r\n  }\r\n};\r\n\r\nexport const downloadStripeAnalyticsByAdmin = async () => {\r\n  try {\r\n    // This will return a blob for download\r\n    const response = await api.get(\"/admin/billing/analytics/download\", {\r\n      responseType: \"blob\",\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Download stripe analytics by admin error:\");\r\n  }\r\n};\r\n\r\nexport const authorizeSupabase = async (projectId: string) => {\r\n  try {\r\n    const response = await api.post(\"/supabase/authorize\", { project_id: projectId });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Supabase authorization error:\");\r\n  }\r\n};\r\n\r\nexport const deleteSupabaseOrganization = async (organizationId: string) => {\r\n  try {\r\n    const response = await api.delete(`/supabase/organizations/${organizationId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Delete Supabase organization error:\");\r\n  }\r\n};\r\n\r\nexport const getSupabaseOrganizations = async (projectId?: string | null) => {\r\n  try {\r\n    const params = projectId ? { project_id: projectId } : {};\r\n    const response = await api.get(\"/supabase/organizations\", { params });\r\n    return response.data;\r\n  } catch (error: unknown) {\r\n    console.error(\r\n      \"Get Supabase organizations error:\",\r\n      error instanceof Error\r\n        ? error.message\r\n        : // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n          (error as any)?.response?.data || \"Unknown error\",\r\n    );\r\n    // Return a structured error response instead of throwing\r\n    return { success: false, organizations: [] };\r\n  }\r\n};\r\n\r\nexport const getSupabaseProjects = async (projectId: string, organizationId: string) => {\r\n  try {\r\n    const response = await api.get(`/supabase/organization/${organizationId}/projects`);\r\n    return response.data;\r\n  } catch (error: unknown) {\r\n    console.error(\r\n      \"Get Supabase projects error:\",\r\n      error instanceof Error\r\n        ? error.message\r\n        : // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n          (error as any)?.response?.data || \"Unknown error\",\r\n    );\r\n    // Return a structured error response instead of throwing\r\n    return { success: false, projects: [] };\r\n  }\r\n};\r\n\r\nexport const testSqlQuery = async (projectId: string, query: string) => {\r\n  try {\r\n    const response = await api.post(`/supabase/project/${projectId}/sql`, { query });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Test SQL query error:\");\r\n  }\r\n};\r\n\r\nexport const connectSupabaseProject = async (\r\n  projectId: string,\r\n  supabaseProjectId: string,\r\n  organizationId: string,\r\n  organizationName: string,\r\n  accessToken: string | null,\r\n  refreshToken: string | null,\r\n  apiKey: string,\r\n  apiUrl: string,\r\n  databasePassword: string | null,\r\n) => {\r\n  try {\r\n    const response = await api.post(\"/supabase/project/connect\", {\r\n      project_id: projectId,\r\n      supabase_project_id: supabaseProjectId,\r\n      organization_id: organizationId,\r\n      organization_name: organizationName,\r\n      access_token: accessToken,\r\n      refresh_token: refreshToken,\r\n      api_key: apiKey,\r\n      api_url: apiUrl,\r\n      database_password: databasePassword,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Connect Supabase project error:\");\r\n  }\r\n};\r\n\r\nexport const disconnectSupabase = async (projectId: string) => {\r\n  try {\r\n    const response = await api.delete(`/supabase/disconnect/${projectId}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Disconnect Supabase error:\");\r\n  }\r\n};\r\n\r\nexport const deleteUserByAdmin = async (email: string, confirm: boolean = false) => {\r\n  try {\r\n    const response = await api.post(\"/admin/users/delete\", {\r\n      email,\r\n      confirm,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Delete user error:\");\r\n  }\r\n};\r\n\r\nexport const updateProjectPrompts = async (\r\n  projectId: string,\r\n  promptsArray: Array<{ key: string; value: string }>,\r\n) => {\r\n  try {\r\n    const response = await api.post(`/project/${projectId}/prompts`, {\r\n      prompts_array: promptsArray,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update project prompts error:\");\r\n  }\r\n};\r\n\r\nexport const getProjectPrompts = async (projectId: string) => {\r\n  try {\r\n    const response = await api.get(`/project/${projectId}/prompts`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get project prompts error:\");\r\n  }\r\n};\r\n\r\nexport const deleteProjectPrompt = async (projectId: string, promptKey: string) => {\r\n  try {\r\n    const response = await api.delete(`/project/${projectId}/prompts/${promptKey}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Delete project prompt error:\");\r\n  }\r\n};\r\n\r\nexport const updateProjectPrompt = async (\r\n  projectId: string,\r\n  promptKey: string,\r\n  promptValue: string,\r\n) => {\r\n  try {\r\n    const response = await api.put(`/project/${projectId}/prompts/${promptKey}`, {\r\n      key: promptKey,\r\n      value: promptValue,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Update project prompt error:\");\r\n  }\r\n};\r\n\r\nexport const connectVercelDeployWebSocket = ({\r\n  projectId,\r\n  vercelToken,\r\n  startStep,\r\n}: {\r\n  projectId: string;\r\n  vercelToken: string | null;\r\n  startStep: number;\r\n}) => {\r\n  try {\r\n    const token = getToken();\r\n    const wsUrl = `${API_BASE_URL.replace(\"http\", \"ws\")}/vercel-deploy?token=${token}&project_id=${projectId}&start_step=${startStep}${vercelToken ? `&vercel_token=${vercelToken}` : \"\"}`;\r\n    const socket = new WebSocket(wsUrl);\r\n\r\n    socket.onopen = () => {\r\n      debug(\"Vercel deployment WebSocket connection established\");\r\n    };\r\n\r\n    socket.onmessage = (event) => {\r\n      try {\r\n        const data = JSON.parse(event.data);\r\n        return data;\r\n      } catch (error) {\r\n        console.error(\"Error handling WebSocket message:\", error);\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    socket.onerror = (error) => {\r\n      console.error(\"Vercel deployment WebSocket error:\", error);\r\n    };\r\n\r\n    socket.onclose = (event) => {\r\n      debug(`Vercel deployment WebSocket closed: ${event.code} ${event.reason}`);\r\n    };\r\n\r\n    return socket;\r\n  } catch (error) {\r\n    handleApiError(error, \"Vercel deployment WebSocket connection error:\");\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getOpenFiles = async (project_id: string) => {\r\n  try {\r\n    const response = await api.get(`/get-open-files/${project_id}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching open files:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const openFilesInEditor = async (project_id: string, files: string[]) => {\r\n  try {\r\n    const response = await api.post(`/open-files-in-editor/${project_id}`, { files });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error opening files:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const closeFilesInEditor = async (project_id: string, files: string[]) => {\r\n  try {\r\n    const response = await api.post(`/close-files-in-editor/${project_id}`, { files });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error closing files:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const connectRemixProjectWebSocket = ({\r\n  projectId,\r\n  envValues = null,\r\n}: {\r\n  projectId: string;\r\n  envValues: Record<string, string> | null;\r\n}) => {\r\n  try {\r\n    const token = getToken();\r\n    let wsUrl = `${API_BASE_URL.replace(\"http\", \"ws\")}/project/${projectId}/remix?token=${token}`;\r\n\r\n    // Add env_values as query parameter if provided\r\n    if (envValues) {\r\n      const envValuesString = JSON.stringify(envValues);\r\n      wsUrl += `&env_values=${encodeURIComponent(envValuesString)}`;\r\n    }\r\n\r\n    const socket = new WebSocket(wsUrl);\r\n\r\n    socket.onopen = () => {\r\n      console.log(\"Remix project WebSocket connection established\");\r\n    };\r\n\r\n    socket.onmessage = (event) => {\r\n      try {\r\n        const data = JSON.parse(event.data);\r\n        return data;\r\n      } catch (error) {\r\n        console.error(\"Error handling WebSocket message:\", error);\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    socket.onerror = (error) => {\r\n      console.error(\"Remix project WebSocket error:\", error);\r\n    };\r\n\r\n    socket.onclose = (event) => {\r\n      console.log(`Remix project WebSocket closed: ${event.code} ${event.reason}`);\r\n    };\r\n\r\n    return socket;\r\n  } catch (error) {\r\n    handleApiError(error, \"Remix project WebSocket connection error:\");\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const banUserByAdmin = async ({ email }: { email: string }) => {\r\n  try {\r\n    const response = await api.post(\"/admin/users/ban\", {\r\n      email,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Ban user error:\");\r\n  }\r\n};\r\n\r\nexport const unbanUserByAdmin = async ({ email }: { email: string }) => {\r\n  try {\r\n    const response = await api.post(\"/admin/users/unban\", {\r\n      email,\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Unban user error:\");\r\n  }\r\n};\r\n\r\nexport const getSupabaseProjectStatus = async ({\r\n  supabaseProjectId,\r\n}: {\r\n  supabaseProjectId: string;\r\n}) => {\r\n  try {\r\n    const response = await api.post(\"/supabase/project/status\", {\r\n      supabase_project_id: supabaseProjectId,\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get Supabase project status error:\");\r\n  }\r\n};\r\n\r\nexport const restoreSupabaseProject = async ({\r\n  supabaseProjectId,\r\n}: {\r\n  supabaseProjectId: string;\r\n}) => {\r\n  try {\r\n    const response = await api.post(\"/supabase/project/restore\", {\r\n      supabase_project_id: supabaseProjectId,\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Restore Supabase project error:\");\r\n  }\r\n};\r\n\r\n// Missing Admin Functions for parity with sg-web\r\nexport const searchProjectByAdmin = async (searchTerm: string) => {\r\n  try {\r\n    const response = await api.get(`/admin/projects/search`, {\r\n      params: { search_term: searchTerm },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Search project by admin error:\");\r\n  }\r\n};\r\n\r\nexport const getUserTransactionsByAdmin = async (email: string) => {\r\n  try {\r\n    const response = await api.get(`/admin/users/${email}/transactions`);\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get user transactions by admin error:\");\r\n  }\r\n};\r\n\r\n// GitHub Clone WebSocket connection for admin functionality\r\nexport const connectGitHubCloneWebSocket = (projectId: string) => {\r\n  try {\r\n    const token = getToken();\r\n    const wsUrl = `${API_BASE_URL.replace(\"http\", \"ws\")}/admin/github-clone/${projectId}?token=${token}`;\r\n    const socket = new WebSocket(wsUrl);\r\n\r\n    socket.onopen = () => {\r\n      console.log(\"GitHub clone WebSocket connection established\");\r\n    };\r\n\r\n    socket.onmessage = (event) => {\r\n      try {\r\n        const data = JSON.parse(event.data);\r\n        return data;\r\n      } catch (error) {\r\n        console.error(\"Error handling GitHub clone WebSocket message:\", error);\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    socket.onerror = (error) => {\r\n      console.error(\"GitHub clone WebSocket error:\", error);\r\n    };\r\n\r\n    socket.onclose = (event) => {\r\n      console.log(`GitHub clone WebSocket closed: ${event.code} ${event.reason}`);\r\n    };\r\n\r\n    return socket;\r\n  } catch (error) {\r\n    handleApiError(error, \"GitHub clone WebSocket connection error:\");\r\n    throw error;\r\n  }\r\n};\r\n\r\n// ----------------- WALLET ROUTES -----------------\r\n\r\nexport const getWallet = async () => {\r\n  try {\r\n    const response = await api.get(\"/wallet\");\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get wallet error:\");\r\n  }\r\n};\r\n\r\nexport const getWalletTransactions = async ({ pageParam = undefined }: { pageParam?: number }) => {\r\n  try {\r\n    const response = await api.get(\"/wallet/transactions\", {\r\n      params: {\r\n        cursor: pageParam,\r\n        limit: 10,\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Get wallet transactions error:\");\r\n  }\r\n};\r\n\r\nexport const topUpWallet = async (amount: number) => {\r\n  try {\r\n    const response = await api.post(\"/wallet/topup\", { amount });\r\n    return response.data;\r\n  } catch (error) {\r\n    handleApiError(error, \"Top up wallet error:\");\r\n  }\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY4B;AAZ5B;AAQA;AACA;AACA;;;;;;AAEO,MAAM,eAAe,6DAAwC;AAE7D,MAAM,WAAW;IACtB,uCAAmC;;IAEnC;IAEA,IAAI;QACF,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;QACtC,MAAM,cAAc,QAAQ,IAAI,CAAC,CAAC,MAAQ,IAAI,UAAU,CAAC;QAEzD,IAAI,CAAC,aAAa;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,MAAM,QAAQ,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE;QACvC,IAAI,CAAC,OAAO;YACV,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;IACT;AACF;AAEA,MAAM,MAAM,2LAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,iBAAiB;IACjB,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,OAAO;IACL,IAAI,QAAQ,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD,EAAE;IAEtB,IAAI,CAAC,OAAO;QACV,QAAQ,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC1B,IAAI,OAAO;YACT,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YACN,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,OAAO,MAAM,KAAK;QACxD;IACF;IAEA,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD,OAAO;QACL,QAAQ,IAAI,CAAC;QAEb,MAAM,eAAe,SAAS,MAAM,GAAG,kBAAkB;QACzD,MAAM,cAAc,SAAS,MAAM,CAChC,KAAK,CAAC,MACN,IAAI,CAAC,CAAC,MAAQ,IAAI,UAAU,CAAC;QAChC,QAAQ,IAAI,CACV,CAAC,eAAe,EAAE,aAAa,uBAAuB,EAAE,cAAc,YAAY,WAAW;QAG/F,IACE,CAAC,OAAO,GAAG,EAAE,SAAS,cACtB,CAAC,OAAO,GAAG,EAAE,SAAS,kBACtB,aAAkB,aAClB;YACA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YACN,IAAI;gBACF,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,IAAI,UAAU;oBACZ,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,IAAI,YAAY,OAAO,MAAM,cAAc;wBACzC,QAAQ,WAAW,KAAK,CAAC,IAAI,CAAC,YAAY;wBAC1C,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACN,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,OAAQ,MAAM,KAAK;wBACvD,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;oBACrD,OAAO;wBACL,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACN,aAAa,UAAU,CAAC;oBAC1B;gBACF;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,aAAa,UAAU,CAAC;YAC1B;QACF;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,iCAAiC;IAC/C,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,MAAM,mBAAmB,CAAC;IACxB,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG;gCACrC,CAAC,WAAa;;gCACd,OAAO;YACL,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ;YAE1C,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACnD,wCAAmC;oBACjC,aAAa,KAAK;oBAClB,aAAa,UAAU,CAAC;gBAC1B;gBAEA,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;gBAEd,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;gBACb,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;gBAEb,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACrB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;gBAErB,wCAAmC;oBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACvB,OAAO,QAAQ,CAAC,MAAM;gBACxB;YACF;YAEA,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;gBACzC,MAAM,MAAM,QAAQ,CAAC,IAAI;YAC3B;YAEA,MAAM;QACR;;AAEJ;AAEA,iBAAiB;AAEjB,MAAM,iBAAiB,CAAC,OAAgB;IACtC,QAAQ,KAAK,CAAC,eAAe;IAC7B,MAAM;AACR;AAEO,MAAM,gBAAgB,OAAO,MAAc;IAChD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,YAAY,MAAM;YAAE,QAAQ;gBAAE;gBAAM;YAAM;QAAE;QAC5E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW;QACtD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW;QACzD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AACO,MAAM,gBAAgB,OAC3B,WACA;IAQA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAC5B,CAAC,SAAS,EAAE,WAAW,EACvB,CAAC,GACD;YACE,QAAQ;QACV;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OAAO,EAC/B,QAAQ,EACR,WAAW,EACX,SAAS,EACT,gBAAgB,CAAC,EACjB,SAAS,EACT,YAAY,UAAU,EACtB,gBAAgB,EAAE,EAClB,kBAAkB,EAAE,EACpB,KAAK,EAWN;IACC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,gBAAgB;YAC9C,WAAW;YACX,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,kBAAkB;YAClB,OAAO;QACT;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,YAAY,OAAO;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU;QACzD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,QAAQ,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,YAAY,OAAO;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU;QACpD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO,WAAmB;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW,MAAM;YAC/C,QAAQ;gBACN,YAAY;gBACZ,YAAY;YACd;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,kBAAkB,OAC7B,WACA,UACA,WACA,iBACA,4BACA,OAAe,aAAa,EAC5B,YAAoB,GAAG,EACvB,uBAA+B,EAAE,EACjC,gBAA0B,EAAE,EAC5B,WAAmB,GAAG;IAEtB,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,cAAc;QAC9B,IAAI,UAAU,SAAS,MAAM,CAAC,aAAa;QAC3C,SAAS,MAAM,CAAC,aAAa;QAC7B,SAAS,MAAM,CAAC,gCAAgC,2BAA2B,QAAQ;QACnF,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,yBAAyB,qBAAqB,QAAQ;QACtE,SAAS,MAAM,CAAC,kBAAkB,cAAc,IAAI,CAAC;QACrD,SAAS,MAAM,CAAC,YAAY;QAE5B,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;YACjD,gBAAgB,OAAO,CAAC,CAAC;gBACvB,SAAS,MAAM,CAAC,oBAAoB;YACtC;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,sBAAsB,UAAU;YAC9D,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,qBAAqB,OAChC,UACA,SACA,SAAiB,EAAE;IAEnB,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,WAAW;QAE3B,iCAAiC;QACjC,OAAO,OAAO,CAAC,CAAC,OAAO;YACrB,IAAI,CAAC,CAAC,iBAAiB,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;gBAChE,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,OAAO;YACxD;YACA,SAAS,MAAM,CAAC,UAAU;QAC5B;QAEA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,mCAAmC;YACvC;YACA,YAAY,OAAO,MAAM;YACzB,YAAY,OAAO,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI;QAC1C;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,YAAY,CAAC,EAAE,UAAU;YAC3E,SAAS;gBACP,gBAAgB;YAClB;YACA,qCAAqC;YACrC,SAAS;QACX;QAEA,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,+BAA+B,SAAS,IAAI;QAClD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAgB;QACvB,MAAM,MAAM;QAQZ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,8BAA8B;YAClE,MAAM,IAAI,MACR;QAEJ;QAEA,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI,OAAO,IAAI;IAC/C;AACF;AAEO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,UAAU;QAC1D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,uBAAuB,OAClC,UACA,EACE,eAAe,KAAK,EACpB,sBAAsB,KAAK,EAC3B,gBAAgB,EAKjB,GAAG,CAAC,CAAC;IAEN,IAAI;QACF,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAI,gBAAgB;gBAAE,gBAAgB,aAAa,QAAQ;YAAG,CAAC;YAC/D,GAAI,uBAAuB;gBAAE,uBAAuB,oBAAoB,QAAQ;YAAG,CAAC;YACpF,GAAI,qBAAqB,aAAa;gBAAE,qBAAqB,iBAAiB,QAAQ;YAAG,CAAC;QAC5F;QAEA,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,sBAAsB,EAAE,QAAQ;QACpF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,yBAAyB,OAAO;IAC3C,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;QACnE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,gCAAgC,OAAO;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,+BAA+B,EAAE,WAAW;QAC5E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO,SAAiB;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,QAAQ;YACV;QACF;QACA,eAAe,OAAO;IACxB;AACF;AAUO,MAAM,uBAAuB,OAClC,MACA,UAAyB,IAAI,EAC7B,YAAqB,KAAK,EAC1B,eAA8B,IAAI,EAClC,eAA8B,IAAI;IAElC,IAAI;QACF,MAAM,SAAkC;YACtC;YACA,UAAU;YACV,YAAY;QACd;QAEA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,wBAAwB;QAE9B,IAAI,cAAc;YAChB,OAAO,aAAa,GAAG;QACzB;QAEA,IAAI,cAAc;YAChB,OAAO,aAAa,GAAG;QACzB;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,0BAA0B,MAAM;YAAE;QAAO;QACzE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,yBAAyB,OAAO,MAAc,UAAyB,IAAI;IACtF,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,8BAA8B,MAAM;YAClE,QAAQ;gBAAE;gBAAM,UAAU;YAAQ;QACpC;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,sBAAsB,OAAO,OAAe;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,0BAA0B,MAAM;YAC9D,QAAQ;gBAAE;gBAAO;YAA2B;QAC9C;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,8BAA8B,OAAO;IAChD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,4BAA4B,MAAM;YAChE,QAAQ;gBAAE,aAAa;YAAW;QACpC;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO,UAAkB,UAAkB;IACrE,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,aAAa;QAC7B,SAAS,MAAM,CAAC,UAAU;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC,EAAE,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,mBAAmB;;IAC9B,OAAO,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,IAAI;oBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,wBAAwB;wBAAE;oBAAW;oBACrE,OAAO,SAAS,IAAI;gBACtB,EAAE,OAAO,OAAO;oBACd,eAAe,OAAO;gBACxB;YACF;;IACF;AACF;GAXa;;QACJ,iRAAA,CAAA,cAAW;;;AAYb,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,WAAW;QAC1D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,wBAAwB,OACnC,WACA,UACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAC7B,CAAC,sBAAsB,EAAE,WAAW,EACpC,KAAK,SAAS,CAAC;YAAE;YAAU;QAAY,IACvC;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,qBAAqB,OAChC,WACA,YACA,qBAAqB,KAAK;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAC7B,CAAC,mBAAmB,EAAE,WAAW,EACjC,KAAK,SAAS,CAAC;YACb,QAAQ;gBAAE,aAAa;YAAW;YAClC,uBAAuB;QACzB,IACA;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAGO,MAAM,WAAW,OAAO,YAAoB,OAAO,GAAG;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE;YAAE,QAAQ;gBAAE;YAAK;QAAE;QAC9E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,MAAM;QAUZ,IAAI,IAAI,QAAQ,EAAE;YAChB,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,KAAK;gBAC/B,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,WAAW,SAAS,EAAE,MAAM;gBACvE,OAAO;oBAAE,OAAO,EAAE;gBAAC;YACrB;YACA,QAAQ,KAAK,CAAC,oBAAoB,IAAI,QAAQ,CAAC,MAAM;YACrD,QAAQ,KAAK,CAAC,qBAAqB,IAAI,QAAQ,CAAC,OAAO;YACvD,MAAM,IAAI,MACR,CAAC,cAAc,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,WAAW,iBAAiB;QAE7F,OAAO,IAAI,IAAI,OAAO,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB,OAAO;YACL,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,IAAI,OAAO,EAAE;QAC5D;IACF;AACF;AAEO,MAAM,iBAAiB,OAAO,YAAoB;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,YAAY,EAAE;YAChE,QAAQ;gBAAE,WAAW;YAAS;YAC9B,cAAc;QAChB;QAEA,MAAM,cAAc,SAAS,OAAO,CAAC,eAAe;QACpD,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;QAE9C,IACE,YAAY,UAAU,CAAC,YACvB,gBAAgB,sBAChB,SAAS,UAAU,CAAC,MACpB;YACA,MAAM,cAAc,IAAI,cAAc,MAAM,CAAC,SAAS,IAAI;YAE1D,IAAI,gBAAgB,oBAAoB;gBACtC,OAAO,KAAK,KAAK,CAAC;YACpB;YACA,OAAO;QACT,OAAO;YACL,MAAM,OAAO,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC,EAAE;gBAAE,MAAM;YAAY;YAC3D,OAAO,IAAI,eAAe,CAAC;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM,MAAM;QAMZ,IAAI,IAAI,QAAQ,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8CAA8C,EAAE,IAAI,QAAQ,CAAC,MAAM,EAAE;QACxF,OAAO,IAAI,IAAI,OAAO,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB,OAAO;YACL,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,IAAI,OAAO,EAAE;QAC5D;IACF;AACF;AAEO,MAAM,aAAa,OACxB,YACA,UACA,UAAU,EAAE,EACZ,cAAc,KAAK;IAEnB,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,gBAAgB;YAC9C;YACA,MAAM;YACN;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OAAO,YAAoB,UAAkB;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,gBAAgB;YAAE;YAAY,MAAM;YAAU;QAAQ;QACrF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF;AAEO,MAAM,aAAa,OAAO,YAAoB;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,gBAAgB;YAChD,QAAQ;gBAAE;gBAAY,MAAM;YAAS;QACvC;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,iBAAiB,OAAO,YAAoB;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;YAAE;YAAY;QAAQ;QAC1E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM,MAAM;QAQZ,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;YAChC,QAAQ,KAAK,CAAC,8CAA8C,IAAI,QAAQ,CAAC,IAAI;YAC7E,MAAM,cACJ,IAAI,QAAQ,CAAC,IAAI,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,YAAY,IAAI,QAAQ,CAAC,IAAI,GACvF,MAAM,OAAO,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,IACpC,AAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAuB,GAAG,CAAC,CAAC,IAAM,EAAE,GAAG,EAAE,IAAI,CAAC,QACvE,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,IACjC;YACN,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,aAAa;QACvE;QAEA,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,YAAY,OAAO,YAAoB;IAClD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,eAAe;YAAE;YAAY;QAAe;QAC5E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,eAAe,WAAW,EAC/B,SAAS,EACT,IAAI,EACJ,YAAY,EACZ,UAAU,EAMX;IACC,IAAI;QACF,IAAI,CAAC,CAAC,gBAAgB,IAAI,GAAG;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,gBAAgB,gBAAgB;QAEhD,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,mBAAmB;YACvB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM;QACR;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,EAAE,UAAU;YACxD,QAAQ;gBAAE,YAAY;YAAU;YAChC,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;YACT,kBAAkB,CAAC;gBACjB,IAAI,cAAc,KAAK,IAAI,YAAY;oBACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBACtF,WAAW;gBACb;YACF;QACF;QAEA,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,+BAA+B,SAAS,IAAI;QAClD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM,MAAM;QAQZ,QAAQ,KAAK,CAAC,yBAAyB;YACrC,OAAO,IAAI,QAAQ,EAAE,QAAQ,IAAI,OAAO;YACxC,QAAQ,IAAI,QAAQ,EAAE;QACxB;QAEA,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;YAChC,MAAM,kBAAkB,IAAI,QAAQ,CAAC,IAAI;YACzC,MAAM,eAAe,gBAAgB,MAAM,GACvC,MAAM,OAAO,CAAC,gBAAgB,MAAM,IAClC,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,GAAG,EAAE,IAAI,CAAC,QAC9C,OAAO,gBAAgB,MAAM,IAC/B;YACJ,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;YAChC,MAAM,IAAI,MAAM;QAClB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,IAAI,MAAM,IAAI,OAAO,IAAI;IACjC;AACF;AAEO,MAAM,WAAW,OAAO,YAAoB,YAAoB;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;YAAE;YAAY;YAAY;QAAW;QACnF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEA;AACE;;;;AAIF,GACA,CAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;QACzD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM,MAAM;QAIZ,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,EAAE;QAChE,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;YAChC,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,iBAAiB,OAC5B,WACA,aACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;YACnD,YAAY;YACZ,cAAc;YACd,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,cAAc,OAAO,WAAmB,aAAqB;IACxE,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,iBAAiB;YAC/C,YAAY;YACZ,cAAc;YACd,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OAAO,WAAmB,aAAqB;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,gBAAgB;YAC9C,YAAY;YACZ,cAAc;YACd,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,gBAAgB,OAC3B,WACA,aACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;YAClD,YAAY;YACZ,cAAc;YACd,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO,WAAmB,aAAqB;IACzE,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,kBAAkB;YAChD,YAAY;YACZ,cAAc;YACd,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,SAAS;QAC3D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,+BAA+B,OAC1C,WACA,UACA,WACA,iBACA,4BACA,OAAe,aAAa,EAC5B,YAAoB,GAAG,EACvB,uBAA+B,EAAE,EACjC,gBAA0B,EAAE,EAC5B,WAAmB,GAAG;IAEtB,IAAI;QACF,MAAM,QAAQ;QACd,MAAM,QAAQ,GAAG,aAAa,OAAO,CAAC,QAAQ,MAAM,yBAAyB,EAAE,OAAO;QACtF,MAAM,SAAS,IAAI,UAAU;QAE7B,OAAO,MAAM,GAAG;YACd,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YAEN,MAAM,sBAAsB,CAAC;gBAC3B,OAAO,IAAI,QAAgB,CAAC,SAAS;oBACnC,MAAM,SAAS,IAAI;oBACnB,OAAO,aAAa,CAAC;oBACrB,OAAO,MAAM,GAAG,IAAM,QAAQ,AAAC,OAAO,MAAM,CAAY,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrE,OAAO,OAAO,GAAG,CAAC,QAAU,OAAO;gBACrC;YACF;YAEA,QAAQ,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,OAAS,oBAAoB,QAC3D,IAAI,CAAC,CAAC;gBACL,MAAM,UAAU,KAAK,SAAS,CAAC;oBAC7B,YAAY;oBACZ,WAAW;oBACX,WAAW;oBACX,kBAAkB;oBAClB,8BAA8B;oBAC9B,MAAM;oBACN,YAAY,SAAS,WAAW;oBAChC,uBAAuB;oBACvB,gBAAgB;oBAChB,UAAU;gBACZ;gBACA,OAAO,IAAI,CAAC;YACd,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,eAAe,OAAO;YACxB;QACJ;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,KAAK,CAAC,oBAAoB;QACpC;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,IAAI,CAAC,MAAM,QAAQ,EAAE;gBACnB,QAAQ,IAAI,CAAC,CAAC,qCAAqC,EAAE,MAAM,IAAI,EAAE;YACnE,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YACR;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,IAAI,CAAC,YAAY;QACf,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;IAEA,MAAM,QAAQ;IACd,MAAM,QAAQ,GAAG,aAAa,OAAO,CAAC,QAAQ,MAAM,wBAAwB,EAAE,MAAM,YAAY,EAAE,WAAW,YAAY,EAAE,cAAc,KAAK;IAC9I,MAAM,SAAS,IAAI,UAAU;IAE7B,OAAO,MAAM,GAAG;QACd,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;IACR;IAEA,OAAO,OAAO,GAAG,CAAC;QAChB,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,QAAQ,IAAI,CAAC,CAAC,8CAA8C,EAAE,MAAM,IAAI,EAAE;QAC5E,OAAO;YACL,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;QACR;IACF;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,OAAO,WAAmB,UAAkB,QAAQ,CAAC;IAC/E,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;YACjD,YAAY;YACZ,WAAW;YACX,OAAO;QACT;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;YAAE;QAAO;QACpE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,kBAAkB;YAAE,YAAY;QAAU;QAC1E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OACxB,UACA,iBACA,OACA,eACA,MACA;IAEA,IAAI;QACF,MAAM,WAAW;YACf;YACA,kBAAkB;YAClB;YACA,gBAAgB;YAChB;YACA;QACF;QACA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,UAAU;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OACxB,QACA,UACA,iBACA,OACA,eACA,MACA;IAEA,IAAI;QACF,MAAM,WAAW;YACf;YACA,kBAAkB;YAClB;YACA,gBAAgB;YAChB;YACA;QACF;QACA,MAAM,aAAa,OAAO,WAAW,CACnC,OAAO,OAAO,CAAC,UAAU,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU;QAG3D,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,YAAY,OAAO,QAAQ,IAAI,EAAE,SAAS,IAAI;IACzD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAE/B,MAAM,WAAW,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,GAAG,SAAS,IAAI,EAAE,SAAS,EAAE;QAC1F,MAAM,QAAQ,SAAS,MAAM;QAE7B,IAAI,iBAAiB;QACrB,IAAI,UAAU,QAAQ,WAAW,MAAM;YACrC,iBAAiB,SAAS,KAAK,CAAC,QAAQ,SAAS;QACnD;QAEA,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO;YACL,OAAO,EAAE;YACT,OAAO;QACT;IACF;AACF;AAEO,MAAM,UAAU,OAAO;IAC5B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM;QACpD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,gBAAgB,OAAO,YAAoB;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE;YAC3D,QAAQ;gBAAE,kBAAkB;YAAe;QAC7C;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO,WAAmB;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,MAAM,CAAC,EAAE;YAC7D,YAAY;QACd;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,gBAAgB,OAAO,WAAmB;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,KAAK,CAAC,EAAE;YAC5D,YAAY;QACd;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,mBAAmB,OAAO,WAAmB;IACxD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,MAAM,EAAE,WAAW;QAC3E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,WAAW;QAC3D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,cAAc,OACzB,KACA,WACA,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;YACnD;YACA,YAAY;YACZ,SAAS;YACT,aAAa;YACb,cAAc;YACd;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO,WAAmB,UAAkB;IACtE,IAAI;QACF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY;QAE5B,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,SAAS,OAAO,CAAC,EAAE,UAAU;YACpF,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO,WAAmB;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,UAAU;QACpE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,uBAAuB,OAAO,aAAqB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B,MAAM;YAC/D,QAAQ;gBACN,cAAc;gBACd,UAAU;YACZ;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE,WAAW;QAC9D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,kBAAkB,OAC7B,WACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,WAAW,CAAC,EAAE;YAClE,kBAAkB;QACpB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,2BAA2B,OAAO,WAAmB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,oBAAoB,CAAC,EAAE;YAC3E,qBAAqB;QACvB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,sBAAsB,OACjC,WACA,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,0BAA0B;YACxD,YAAY;YACZ;YACA,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,qBAAqB,OAChC,WACA,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB;YACvD,YAAY;YACZ;YACA,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,KAAK,CAAC;QAC3D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAGO,MAAM,kBAAkB,OAAO,EACpC,OAAO,CAAC,EACR,QAAQ,GAAG,EACX,SAAS,EAAE,EAKZ;IACC,IAAI;QACF,MAAM,SAIF;YACF;YACA;QACF;QAEA,IAAI,QAAQ,OAAO,MAAM,GAAG;QAE5B,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,iCAAiC;QAEvC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,gBAAgB;YAAE;QAAO;QAExD,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,2BAA2B;YAC/B,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK;YAC/B,YAAY,SAAS,IAAI,CAAC,KAAK,EAAE,UAAU;YAC3C,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI;YAC9B,SAAS,MAAM,OAAO,CAAC,SAAS,IAAI;QACtC;QAEA,gEAAgE;QAChE,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YAChC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YACN,OAAO;gBACL,OAAO,SAAS,IAAI;gBACpB,OAAO,SAAS,IAAI,CAAC,MAAM;YAC7B;QACF;QAEA,2EAA2E;QAC3E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,sEAAsE;QACtE,OAAO;YAAE,OAAO,EAAE;YAAE,OAAO;QAAE;IAC/B;AACF;AAEO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO;QACtD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,oBAAoB,OAAO,SAAiB;IACvD,IAAI;QACF,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,0BAA0B;QAChC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,gBAAgB;QAEtB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS,EAAE;QAC1D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,yBAAyB,OAAO;IAC3C,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,SAAS,CAAC;QAC/D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,WAAW;QAC7D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,uBAAuB,OAAO,WAAmB;IAC5D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,WAAW,EAAE;QAC/D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,0BAA0B,OAAO,WAAmB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU,WAAW,CAAC,EAAE;QAC1E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,wBAAwB,EAAE,YAAY;QACtE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,iCAAiC;IAC5C,IAAI;QACF,uCAAuC;QACvC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,qCAAqC;YAClE,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;YAAE,YAAY;QAAU;QAC/E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,wBAAwB,EAAE,gBAAgB;QAC7E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,MAAM,SAAS,YAAY;YAAE,YAAY;QAAU,IAAI,CAAC;QACxD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;YAAE;QAAO;QACnE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CACX,qCACA,iBAAiB,QACb,MAAM,OAAO,GAEb,AAAC,OAAe,UAAU,QAAQ;QAExC,yDAAyD;QACzD,OAAO;YAAE,SAAS;YAAO,eAAe,EAAE;QAAC;IAC7C;AACF;AAEO,MAAM,sBAAsB,OAAO,WAAmB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe,SAAS,CAAC;QAClF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CACX,gCACA,iBAAiB,QACb,MAAM,OAAO,GAEb,AAAC,OAAe,UAAU,QAAQ;QAExC,yDAAyD;QACzD,OAAO;YAAE,SAAS;YAAO,UAAU,EAAE;QAAC;IACxC;AACF;AAEO,MAAM,eAAe,OAAO,WAAmB;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,IAAI,CAAC,EAAE;YAAE;QAAM;QAC9E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,yBAAyB,OACpC,WACA,mBACA,gBACA,kBACA,aACA,cACA,QACA,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,6BAA6B;YAC3D,YAAY;YACZ,qBAAqB;YACrB,iBAAiB;YACjB,mBAAmB;YACnB,cAAc;YACd,eAAe;YACf,SAAS;YACT,SAAS;YACT,mBAAmB;QACrB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,qBAAqB,EAAE,WAAW;QACrE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,oBAAoB,OAAO,OAAe,UAAmB,KAAK;IAC7E,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;YACrD;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,uBAAuB,OAClC,WACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,QAAQ,CAAC,EAAE;YAC/D,eAAe;QACjB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,QAAQ,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,sBAAsB,OAAO,WAAmB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU,SAAS,EAAE,WAAW;QAC9E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,sBAAsB,OACjC,WACA,WACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,SAAS,EAAE,WAAW,EAAE;YAC3E,KAAK;YACL,OAAO;QACT;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,+BAA+B,CAAC,EAC3C,SAAS,EACT,WAAW,EACX,SAAS,EAKV;IACC,IAAI;QACF,MAAM,QAAQ;QACd,MAAM,QAAQ,GAAG,aAAa,OAAO,CAAC,QAAQ,MAAM,qBAAqB,EAAE,MAAM,YAAY,EAAE,UAAU,YAAY,EAAE,YAAY,cAAc,CAAC,cAAc,EAAE,aAAa,GAAG,IAAI;QACtL,MAAM,SAAS,IAAI,UAAU;QAE7B,OAAO,MAAM,GAAG;YACd,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;QACR;QAEA,OAAO,SAAS,GAAG,CAAC;YAClB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAClC,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,MAAM;YACR;QACF;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,oCAAoC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAC3E;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;QACtB,MAAM;IACR;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,YAAY;QAC9D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB,OAAO,YAAoB;IAC1D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,sBAAsB,EAAE,YAAY,EAAE;YAAE;QAAM;QAC/E,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB,OAAO,YAAoB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,YAAY,EAAE;YAAE;QAAM;QAChF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAEO,MAAM,+BAA+B,CAAC,EAC3C,SAAS,EACT,YAAY,IAAI,EAIjB;IACC,IAAI;QACF,MAAM,QAAQ;QACd,IAAI,QAAQ,GAAG,aAAa,OAAO,CAAC,QAAQ,MAAM,SAAS,EAAE,UAAU,aAAa,EAAE,OAAO;QAE7F,gDAAgD;QAChD,IAAI,WAAW;YACb,MAAM,kBAAkB,KAAK,SAAS,CAAC;YACvC,SAAS,CAAC,YAAY,EAAE,mBAAmB,kBAAkB;QAC/D;QAEA,MAAM,SAAS,IAAI,UAAU;QAE7B,OAAO,MAAM,GAAG;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,SAAS,GAAG,CAAC;YAClB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAClC,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,MAAM;YACR;QACF;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,KAAK,CAAC,kCAAkC;QAClD;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAC7E;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;QACtB,MAAM;IACR;AACF;AAEO,MAAM,iBAAiB,OAAO,EAAE,KAAK,EAAqB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;YAClD;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,mBAAmB,OAAO,EAAE,KAAK,EAAqB;IACjE,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,sBAAsB;YACpD;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,2BAA2B,OAAO,EAC7C,iBAAiB,EAGlB;IACC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,4BAA4B;YAC1D,qBAAqB;QACvB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,yBAAyB,OAAO,EAC3C,iBAAiB,EAGlB;IACC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,6BAA6B;YAC3D,qBAAqB;QACvB;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,sBAAsB,CAAC,EAAE;YACvD,QAAQ;gBAAE,aAAa;YAAW;QACpC;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,aAAa,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAGO,MAAM,8BAA8B,CAAC;IAC1C,IAAI;QACF,MAAM,QAAQ;QACd,MAAM,QAAQ,GAAG,aAAa,OAAO,CAAC,QAAQ,MAAM,oBAAoB,EAAE,UAAU,OAAO,EAAE,OAAO;QACpG,MAAM,SAAS,IAAI,UAAU;QAE7B,OAAO,MAAM,GAAG;YACd,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,SAAS,GAAG,CAAC;YAClB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAClC,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kDAAkD;gBAChE,MAAM;YACR;QACF;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QAEA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAC5E;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;QACtB,MAAM;IACR;AACF;AAIO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,wBAAwB,OAAO,EAAE,YAAY,SAAS,EAA0B;IAC3F,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,wBAAwB;YACrD,QAAQ;gBACN,QAAQ;gBACR,OAAO;YACT;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,iBAAiB;YAAE;QAAO;QAC1D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,eAAe,OAAO;IACxB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 2095, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/auth-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useDebounce } from \"@/hooks/use-debounce\";\r\nimport { getKindeUser } from \"@/lib/api\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { AuthState, UserFromDb } from \"@/types\";\r\nimport {\r\n  getCookie,\r\n  getItemWithExpiry,\r\n  removeCookie,\r\n  removeItemWithExpiry,\r\n  setCookieWithExpiry,\r\n  setItemWithExpiry,\r\n} from \"@/utils/auth-utils\";\r\nimport { AxiosError } from \"axios\";\r\nimport React, { useCallback, useEffect, useRef } from \"react\";\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\n\r\ntype AuthStore = {\r\n  user: AuthState;\r\n  login: (access_token: string, email: string, kinde_id: string) => Promise<void>;\r\n  logout: () => void;\r\n  checkSubscription: (kinde_id: string, initialUserData?: UserFromDb) => Promise<UserFromDb | null>;\r\n  initializeAuth: () => Promise<void>;\r\n  lastCheckTimestamp: number;\r\n  setLastCheckTimestamp: (timestamp: number) => void;\r\n  syncTokens: () => boolean;\r\n};\r\n\r\nconst ttl = 60 * 24 * 7 * 60 * 1000; // 7 days in milliseconds (matching backend expiry)\r\n\r\n/**\r\n * Clears the persisted auth state from localStorage\r\n * This prevents stale auth state from persisting when tokens are expired\r\n */\r\nconst clearPersistedAuthState = () => {\r\n  if (typeof window !== \"undefined\") {\r\n    localStorage.removeItem(\"auth-storage\");\r\n  }\r\n};\r\n\r\nexport const useAuth = create<AuthStore>()(\r\n  persist(\r\n    (set, get) => ({\r\n      user: {\r\n        access_token: null,\r\n        email: null,\r\n        kinde_id: null,\r\n        userFromDb: null,\r\n        customer_id: null,\r\n        isSubscribed: false,\r\n        isLoading: true,\r\n        showPricingDialog: false,\r\n        cookie: {\r\n          access_token: null,\r\n          kinde_id: null,\r\n        },\r\n      },\r\n      lastCheckTimestamp: 0,\r\n      setLastCheckTimestamp: (timestamp) => set({ lastCheckTimestamp: timestamp }),\r\n\r\n      initializeAuth: async () => {\r\n        const cookieAccessToken = getCookie(\"access_token\");\r\n        const cookieKindeId = getCookie(\"kinde_id\");\r\n        const localAccessToken = getItemWithExpiry(\"access_token\");\r\n        const localKindeId = getItemWithExpiry(\"kinde_id\");\r\n\r\n        debug(\"Auth state check:\", {\r\n          cookieTokenExists: !!cookieAccessToken,\r\n          localTokenExists: !!localAccessToken,\r\n          cookieKindeExists: !!cookieKindeId,\r\n          localKindeExists: !!localKindeId,\r\n        });\r\n\r\n        if (localAccessToken && localKindeId && (!cookieAccessToken || !cookieKindeId)) {\r\n          debug(\"Syncing tokens from localStorage to cookies\");\r\n          setCookieWithExpiry(\"access_token\", localAccessToken, ttl);\r\n          setCookieWithExpiry(\"kinde_id\", localKindeId, ttl);\r\n        }\r\n\r\n        if (cookieAccessToken && cookieKindeId && (!localAccessToken || !localKindeId)) {\r\n          debug(\"Syncing tokens from cookies to localStorage\");\r\n          setItemWithExpiry(\"access_token\", cookieAccessToken, ttl);\r\n          setItemWithExpiry(\"kinde_id\", cookieKindeId, ttl);\r\n        }\r\n\r\n        if (!cookieAccessToken && !cookieKindeId && !localAccessToken && !localKindeId) {\r\n          debug(\"No auth tokens found, clearing persisted state and setting isLoading to false\");\r\n          clearPersistedAuthState();\r\n          set((state) => ({ user: { ...state.user, isLoading: false } }));\r\n          return;\r\n        }\r\n\r\n        const effectiveToken = cookieAccessToken || localAccessToken;\r\n        const effectiveKindeId = cookieKindeId || localKindeId;\r\n\r\n        if (!effectiveToken || !effectiveKindeId) {\r\n          debug(\r\n            \"No effective tokens found, clearing persisted state and setting isLoading to false\",\r\n          );\r\n          clearPersistedAuthState();\r\n          set((state) => ({ user: { ...state.user, isLoading: false } }));\r\n          return;\r\n        }\r\n\r\n        if (effectiveToken && effectiveKindeId) {\r\n          setCookieWithExpiry(\"access_token\", effectiveToken, ttl);\r\n          setCookieWithExpiry(\"kinde_id\", effectiveKindeId, ttl);\r\n          setItemWithExpiry(\"access_token\", effectiveToken, ttl);\r\n          setItemWithExpiry(\"kinde_id\", effectiveKindeId, ttl);\r\n        }\r\n\r\n        try {\r\n          const userFromDb = await getKindeUser(effectiveKindeId);\r\n          if (!userFromDb) {\r\n            debug(\"No user found in DB, clearing persisted state and setting isLoading to false\");\r\n            clearPersistedAuthState();\r\n            set((state) => ({ user: { ...state.user, isLoading: false } }));\r\n            return;\r\n          }\r\n\r\n          debug(\"User data retrieved successfully\");\r\n          set((state) => ({\r\n            user: {\r\n              ...state.user,\r\n              access_token: effectiveToken,\r\n              kinde_id: effectiveKindeId,\r\n              email: userFromDb.email,\r\n              userFromDb,\r\n              customer_id: userFromDb.stripe_customer_id || null,\r\n              isSubscribed: userFromDb.isSubscribed || false,\r\n              isLoading: false,\r\n              cookie: {\r\n                access_token: effectiveToken,\r\n                kinde_id: effectiveKindeId,\r\n              },\r\n            },\r\n          }));\r\n        } catch (error) {\r\n          console.error(\"Error during auth initialization:\", error);\r\n          if (error instanceof AxiosError && error.response?.status === 401) {\r\n            debug(\"401 error during auth, clearing tokens and persisted state\");\r\n            removeCookie(\"access_token\");\r\n            removeCookie(\"kinde_id\");\r\n            removeItemWithExpiry(\"access_token\");\r\n            removeItemWithExpiry(\"kinde_id\");\r\n            clearPersistedAuthState();\r\n\r\n            set((state) => ({\r\n              user: {\r\n                ...state.user,\r\n                access_token: null,\r\n                email: null,\r\n                kinde_id: null,\r\n                userFromDb: null,\r\n                customer_id: null,\r\n                isSubscribed: false,\r\n                isLoading: false,\r\n                cookie: {\r\n                  access_token: null,\r\n                  kinde_id: null,\r\n                },\r\n              },\r\n            }));\r\n          } else {\r\n            debug(\r\n              \"Non-401 error during auth, clearing persisted state and setting isLoading to false\",\r\n            );\r\n            clearPersistedAuthState();\r\n            set((state) => ({ user: { ...state.user, isLoading: false } }));\r\n          }\r\n        }\r\n      },\r\n\r\n      syncTokens: () => {\r\n        const cookieAccessToken = getCookie(\"access_token\");\r\n        const cookieKindeId = getCookie(\"kinde_id\");\r\n        const localAccessToken = getItemWithExpiry(\"access_token\");\r\n        const localKindeId = getItemWithExpiry(\"kinde_id\");\r\n\r\n        const effectiveToken = cookieAccessToken || localAccessToken;\r\n        const effectiveKindeId = cookieKindeId || localKindeId;\r\n\r\n        if (effectiveToken && effectiveKindeId) {\r\n          setCookieWithExpiry(\"access_token\", effectiveToken, ttl);\r\n          setCookieWithExpiry(\"kinde_id\", effectiveKindeId, ttl);\r\n          setItemWithExpiry(\"access_token\", effectiveToken, ttl);\r\n          setItemWithExpiry(\"kinde_id\", effectiveKindeId, ttl);\r\n\r\n          set((state) => ({\r\n            user: {\r\n              ...state.user,\r\n              access_token: effectiveToken,\r\n              kinde_id: effectiveKindeId,\r\n              cookie: {\r\n                access_token: effectiveToken,\r\n                kinde_id: effectiveKindeId,\r\n              },\r\n            },\r\n          }));\r\n          return true;\r\n        }\r\n        return false;\r\n      },\r\n\r\n      login: async (access_token, email, kinde_id) => {\r\n        try {\r\n          debug(\"Login called with token and kinde_id\");\r\n          set((state) => ({ user: { ...state.user, isLoading: true } }));\r\n\r\n          setCookieWithExpiry(\"access_token\", access_token, ttl);\r\n          setCookieWithExpiry(\"kinde_id\", kinde_id, ttl);\r\n          setItemWithExpiry(\"access_token\", access_token, ttl);\r\n          setItemWithExpiry(\"kinde_id\", kinde_id, ttl);\r\n          setItemWithExpiry(\"email\", email, ttl);\r\n\r\n          const cookieToken = getCookie(\"access_token\");\r\n          const localToken = getItemWithExpiry(\"access_token\");\r\n\r\n          debug(\"Token verification after login:\", {\r\n            cookieTokenSet: !!cookieToken,\r\n            localTokenSet: !!localToken,\r\n            tokensMatch: cookieToken === access_token && localToken === access_token,\r\n          });\r\n\r\n          if (!cookieToken || !localToken) {\r\n            debug(\"Re-attempting to set tokens\");\r\n            setTimeout(() => {\r\n              setCookieWithExpiry(\"access_token\", access_token, ttl);\r\n              setCookieWithExpiry(\"kinde_id\", kinde_id, ttl);\r\n              setItemWithExpiry(\"access_token\", access_token, ttl);\r\n              setItemWithExpiry(\"kinde_id\", kinde_id, ttl);\r\n            }, 100);\r\n          }\r\n\r\n          const userFromDb = await getKindeUser(kinde_id);\r\n          debug(\"User data retrieved during login\");\r\n\r\n          set({\r\n            user: {\r\n              access_token,\r\n              email,\r\n              kinde_id,\r\n              userFromDb,\r\n              customer_id: userFromDb?.stripe_customer_id || null,\r\n              isSubscribed: userFromDb?.isSubscribed || false,\r\n              isLoading: false,\r\n              showPricingDialog: false,\r\n              cookie: {\r\n                access_token,\r\n                kinde_id,\r\n              },\r\n            },\r\n          });\r\n        } catch (error) {\r\n          console.error(\"Error during login:\", error);\r\n          if (error instanceof AxiosError && error.response?.status === 401) {\r\n            removeCookie(\"access_token\");\r\n            removeCookie(\"kinde_id\");\r\n            removeItemWithExpiry(\"access_token\");\r\n            removeItemWithExpiry(\"kinde_id\");\r\n            removeItemWithExpiry(\"email\");\r\n            clearPersistedAuthState();\r\n\r\n            set((state) => ({\r\n              user: {\r\n                ...state.user,\r\n                access_token: null,\r\n                email: null,\r\n                kinde_id: null,\r\n                userFromDb: null,\r\n                customer_id: null,\r\n                isSubscribed: false,\r\n                isLoading: false,\r\n                cookie: {\r\n                  access_token: null,\r\n                  kinde_id: null,\r\n                },\r\n              },\r\n            }));\r\n          } else {\r\n            clearPersistedAuthState();\r\n            set((state) => ({ user: { ...state.user, isLoading: false } }));\r\n          }\r\n        }\r\n      },\r\n\r\n      logout: () => {\r\n        set({\r\n          user: {\r\n            access_token: null,\r\n            email: null,\r\n            kinde_id: null,\r\n            userFromDb: null,\r\n            customer_id: null,\r\n            isSubscribed: false,\r\n            isLoading: false,\r\n            showPricingDialog: false,\r\n            cookie: {\r\n              access_token: null,\r\n              kinde_id: null,\r\n            },\r\n          },\r\n        });\r\n\r\n        removeCookie(\"access_token\");\r\n        removeCookie(\"kinde_id\");\r\n\r\n        removeItemWithExpiry(\"access_token\");\r\n        removeItemWithExpiry(\"kinde_id\");\r\n        removeItemWithExpiry(\"email\");\r\n\r\n        clearPersistedAuthState();\r\n        localStorage.clear();\r\n\r\n        const postLogoutRedirect = encodeURIComponent(process.env.NEXT_PUBLIC_APP_URL!);\r\n        const logoutUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/logout?post_logout_redirect_uri=${postLogoutRedirect}`;\r\n        window.location.href = logoutUrl;\r\n      },\r\n\r\n      checkSubscription: async (kinde_id, initialUserData) => {\r\n        const now = Date.now();\r\n        const lastCheck = get().lastCheckTimestamp;\r\n\r\n        if (now - lastCheck < 60000 && initialUserData) {\r\n          if (initialUserData && !initialUserData.plan && initialUserData.total_free_request <= 0) {\r\n            window.location.href = \"/pricing\";\r\n          }\r\n          return initialUserData;\r\n        }\r\n\r\n        try {\r\n          const userFromDb = initialUserData || (await getKindeUser(kinde_id));\r\n\r\n          if (!userFromDb?.plan && userFromDb?.total_free_request <= 0) {\r\n            window.location.href = \"/pricing\";\r\n            return userFromDb;\r\n          }\r\n\r\n          if (userFromDb?.is_creating_project) {\r\n            const checkInterval = setInterval(async () => {\r\n              const currentTime = Date.now();\r\n              const lastCheckTime = get().lastCheckTimestamp;\r\n\r\n              if (currentTime - lastCheckTime < 1000) {\r\n                return;\r\n              }\r\n\r\n              try {\r\n                const updatedUser = await getKindeUser(kinde_id);\r\n                set((state) => ({\r\n                  user: {\r\n                    ...state.user,\r\n                    userFromDb: updatedUser,\r\n                    isSubscribed: updatedUser?.isSubscribed || false,\r\n                  },\r\n                  lastCheckTimestamp: currentTime,\r\n                }));\r\n\r\n                if (!updatedUser.is_creating_project) {\r\n                  clearInterval(checkInterval);\r\n                }\r\n              } catch (error) {\r\n                console.error(\"Error checking project status:\", error);\r\n              }\r\n            }, 1000);\r\n\r\n            setTimeout(() => clearInterval(checkInterval), 5 * 60 * 1000);\r\n          }\r\n\r\n          set((state) => ({\r\n            user: {\r\n              ...state.user,\r\n              userFromDb,\r\n              isSubscribed: userFromDb?.isSubscribed || false,\r\n              isLoading: false,\r\n            },\r\n            lastCheckTimestamp: now,\r\n          }));\r\n\r\n          return userFromDb;\r\n        } catch (error) {\r\n          console.error(\"Error checking subscription:\", error);\r\n          set((state) => ({ user: { ...state.user, isLoading: false } }));\r\n          return null;\r\n        }\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n      partialize: (state) => ({\r\n        user: {\r\n          access_token: state.user.access_token,\r\n          email: state.user.email,\r\n          kinde_id: state.user.kinde_id,\r\n          customer_id: state.user.customer_id,\r\n          isSubscribed: state.user.isSubscribed,\r\n          showPricingDialog: state.user.showPricingDialog,\r\n          userFromDb: state.user.userFromDb,\r\n        },\r\n        lastCheckTimestamp: state.lastCheckTimestamp,\r\n      }),\r\n    },\r\n  ),\r\n);\r\n\r\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\r\n  const initializeAuth = useAuth((state) => state.initializeAuth);\r\n  const syncTokens = useAuth((state) => state.syncTokens);\r\n  const user = useAuth((state) => state.user);\r\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\r\n  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const { debouncedValue: debouncedUser } = useDebounce(user, 300);\r\n\r\n  const handleVisibilityChange = useCallback(() => {\r\n    if (document.visibilityState === \"visible\") {\r\n      debug(\"Tab is now active, refreshing auth state\");\r\n      syncTokens();\r\n      initializeAuth().catch((error) => {\r\n        console.error(\"Error refreshing auth state on visibility change:\", error);\r\n      });\r\n    }\r\n  }, [initializeAuth, syncTokens]);\r\n\r\n  useEffect(() => {\r\n    debug(\"Checking auth\");\r\n\r\n    if (!user.kinde_id) {\r\n      initializeAuth().then(() => {\r\n        debug(\"Auth initialized\");\r\n      });\r\n    }\r\n\r\n    if (!intervalRef.current && user.kinde_id) {\r\n      intervalRef.current = setInterval(\r\n        () => {\r\n          debug(\"Refreshing auth state\");\r\n          initializeAuth();\r\n        },\r\n        30 * 60 * 1000,\r\n      );\r\n    }\r\n\r\n    if (!syncIntervalRef.current && user.kinde_id) {\r\n      syncIntervalRef.current = setInterval(\r\n        () => {\r\n          debug(\"Syncing tokens\");\r\n          syncTokens();\r\n        },\r\n        5 * 60 * 1000,\r\n      );\r\n    }\r\n\r\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n\r\n    return () => {\r\n      if (intervalRef.current) {\r\n        clearInterval(intervalRef.current);\r\n        intervalRef.current = null;\r\n      }\r\n      if (syncIntervalRef.current) {\r\n        clearInterval(syncIntervalRef.current);\r\n        syncIntervalRef.current = null;\r\n      }\r\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n    };\r\n  }, [initializeAuth, syncTokens, handleVisibilityChange, debouncedUser.kinde_id]);\r\n\r\n  return children;\r\n};\r\n"], "names": [], "mappings": ";;;;AA4TsD;AA1TtD;AACA;AACA;AAEA;AAQA;AACA;AACA;AACA;;AAjBA;;;;;;;;;AA8BA,MAAM,MAAM,KAAK,KAAK,IAAI,KAAK,MAAM,mDAAmD;AAExF;;;CAGC,GACD,MAAM,0BAA0B;IAC9B,wCAAmC;QACjC,aAAa,UAAU,CAAC;IAC1B;AACF;AAEO,MAAM,UAAU,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,4PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;YACJ,cAAc;YACd,OAAO;YACP,UAAU;YACV,YAAY;YACZ,aAAa;YACb,cAAc;YACd,WAAW;YACX,mBAAmB;YACnB,QAAQ;gBACN,cAAc;gBACd,UAAU;YACZ;QACF;QACA,oBAAoB;QACpB,uBAAuB,CAAC,YAAc,IAAI;gBAAE,oBAAoB;YAAU;QAE1E,gBAAgB;YACd,MAAM,oBAAoB,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD,EAAE;YACpC,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD,EAAE;YAChC,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;YAC3C,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;YAEvC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,qBAAqB;gBACzB,mBAAmB,CAAC,CAAC;gBACrB,kBAAkB,CAAC,CAAC;gBACpB,mBAAmB,CAAC,CAAC;gBACrB,kBAAkB,CAAC,CAAC;YACtB;YAEA,IAAI,oBAAoB,gBAAgB,CAAC,CAAC,qBAAqB,CAAC,aAAa,GAAG;gBAC9E,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBACN,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,kBAAkB;gBACtD,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,cAAc;YAChD;YAEA,IAAI,qBAAqB,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,YAAY,GAAG;gBAC9E,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBACN,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,mBAAmB;gBACrD,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,eAAe;YAC/C;YAEA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,cAAc;gBAC9E,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBACN;gBACA,IAAI,CAAC,QAAU,CAAC;wBAAE,MAAM;4BAAE,GAAG,MAAM,IAAI;4BAAE,WAAW;wBAAM;oBAAE,CAAC;gBAC7D;YACF;YAEA,MAAM,iBAAiB,qBAAqB;YAC5C,MAAM,mBAAmB,iBAAiB;YAE1C,IAAI,CAAC,kBAAkB,CAAC,kBAAkB;gBACxC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EACF;gBAEF;gBACA,IAAI,CAAC,QAAU,CAAC;wBAAE,MAAM;4BAAE,GAAG,MAAM,IAAI;4BAAE,WAAW;wBAAM;oBAAE,CAAC;gBAC7D;YACF;YAEA,IAAI,kBAAkB,kBAAkB;gBACtC,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,gBAAgB;gBACpD,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,kBAAkB;gBAClD,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,gBAAgB;gBAClD,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,kBAAkB;YAClD;YAEA,IAAI;gBACF,MAAM,aAAa,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;gBACtC,IAAI,CAAC,YAAY;oBACf,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;oBACN;oBACA,IAAI,CAAC,QAAU,CAAC;4BAAE,MAAM;gCAAE,GAAG,MAAM,IAAI;gCAAE,WAAW;4BAAM;wBAAE,CAAC;oBAC7D;gBACF;gBAEA,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBACN,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb,cAAc;4BACd,UAAU;4BACV,OAAO,WAAW,KAAK;4BACvB;4BACA,aAAa,WAAW,kBAAkB,IAAI;4BAC9C,cAAc,WAAW,YAAY,IAAI;4BACzC,WAAW;4BACX,QAAQ;gCACN,cAAc;gCACd,UAAU;4BACZ;wBACF;oBACF,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,IAAI,iBAAiB,oMAAA,CAAA,aAAU,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBACjE,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;oBACN,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;oBACb,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;oBACb,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBACrB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBACrB;oBAEA,IAAI,CAAC,QAAU,CAAC;4BACd,MAAM;gCACJ,GAAG,MAAM,IAAI;gCACb,cAAc;gCACd,OAAO;gCACP,UAAU;gCACV,YAAY;gCACZ,aAAa;gCACb,cAAc;gCACd,WAAW;gCACX,QAAQ;oCACN,cAAc;oCACd,UAAU;gCACZ;4BACF;wBACF,CAAC;gBACH,OAAO;oBACL,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EACF;oBAEF;oBACA,IAAI,CAAC,QAAU,CAAC;4BAAE,MAAM;gCAAE,GAAG,MAAM,IAAI;gCAAE,WAAW;4BAAM;wBAAE,CAAC;gBAC/D;YACF;QACF;QAEA,YAAY;YACV,MAAM,oBAAoB,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD,EAAE;YACpC,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD,EAAE;YAChC,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;YAC3C,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;YAEvC,MAAM,iBAAiB,qBAAqB;YAC5C,MAAM,mBAAmB,iBAAiB;YAE1C,IAAI,kBAAkB,kBAAkB;gBACtC,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,gBAAgB;gBACpD,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,kBAAkB;gBAClD,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,gBAAgB;gBAClD,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,kBAAkB;gBAEhD,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb,cAAc;4BACd,UAAU;4BACV,QAAQ;gCACN,cAAc;gCACd,UAAU;4BACZ;wBACF;oBACF,CAAC;gBACD,OAAO;YACT;YACA,OAAO;QACT;QAEA,OAAO,OAAO,cAAc,OAAO;YACjC,IAAI;gBACF,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBACN,IAAI,CAAC,QAAU,CAAC;wBAAE,MAAM;4BAAE,GAAG,MAAM,IAAI;4BAAE,WAAW;wBAAK;oBAAE,CAAC;gBAE5D,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,cAAc;gBAClD,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,UAAU;gBAC1C,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,cAAc;gBAChD,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,UAAU;gBACxC,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,OAAO;gBAElC,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD,EAAE;gBAC9B,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE;gBAErC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,mCAAmC;oBACvC,gBAAgB,CAAC,CAAC;oBAClB,eAAe,CAAC,CAAC;oBACjB,aAAa,gBAAgB,gBAAgB,eAAe;gBAC9D;gBAEA,IAAI,CAAC,eAAe,CAAC,YAAY;oBAC/B,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;oBACN,WAAW;wBACT,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,cAAc;wBAClD,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,UAAU;wBAC1C,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,cAAc;wBAChD,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,UAAU;oBAC1C,GAAG;gBACL;gBAEA,MAAM,aAAa,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;gBACtC,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBAEN,IAAI;oBACF,MAAM;wBACJ;wBACA;wBACA;wBACA;wBACA,aAAa,YAAY,sBAAsB;wBAC/C,cAAc,YAAY,gBAAgB;wBAC1C,WAAW;wBACX,mBAAmB;wBACnB,QAAQ;4BACN;4BACA;wBACF;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,IAAI,iBAAiB,oMAAA,CAAA,aAAU,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBACjE,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;oBACb,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;oBACb,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBACrB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBACrB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBACrB;oBAEA,IAAI,CAAC,QAAU,CAAC;4BACd,MAAM;gCACJ,GAAG,MAAM,IAAI;gCACb,cAAc;gCACd,OAAO;gCACP,UAAU;gCACV,YAAY;gCACZ,aAAa;gCACb,cAAc;gCACd,WAAW;gCACX,QAAQ;oCACN,cAAc;oCACd,UAAU;gCACZ;4BACF;wBACF,CAAC;gBACH,OAAO;oBACL;oBACA,IAAI,CAAC,QAAU,CAAC;4BAAE,MAAM;gCAAE,GAAG,MAAM,IAAI;gCAAE,WAAW;4BAAM;wBAAE,CAAC;gBAC/D;YACF;QACF;QAEA,QAAQ;YACN,IAAI;gBACF,MAAM;oBACJ,cAAc;oBACd,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,aAAa;oBACb,cAAc;oBACd,WAAW;oBACX,mBAAmB;oBACnB,QAAQ;wBACN,cAAc;wBACd,UAAU;oBACZ;gBACF;YACF;YAEA,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;YACb,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;YAEb,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;YACrB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;YACrB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;YAErB;YACA,aAAa,KAAK;YAElB,MAAM,qBAAqB;YAC3B,MAAM,YAAY,6DAAwC,iCAAiC,EAAE,oBAAoB;YACjH,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QAEA,mBAAmB,OAAO,UAAU;YAClC,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,YAAY,MAAM,kBAAkB;YAE1C,IAAI,MAAM,YAAY,SAAS,iBAAiB;gBAC9C,IAAI,mBAAmB,CAAC,gBAAgB,IAAI,IAAI,gBAAgB,kBAAkB,IAAI,GAAG;oBACvF,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;gBACA,OAAO;YACT;YAEA,IAAI;gBACF,MAAM,aAAa,mBAAoB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;gBAE1D,IAAI,CAAC,YAAY,QAAQ,YAAY,sBAAsB,GAAG;oBAC5D,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACvB,OAAO;gBACT;gBAEA,IAAI,YAAY,qBAAqB;oBACnC,MAAM,gBAAgB,YAAY;wBAChC,MAAM,cAAc,KAAK,GAAG;wBAC5B,MAAM,gBAAgB,MAAM,kBAAkB;wBAE9C,IAAI,cAAc,gBAAgB,MAAM;4BACtC;wBACF;wBAEA,IAAI;4BACF,MAAM,cAAc,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE;4BACvC,IAAI,CAAC,QAAU,CAAC;oCACd,MAAM;wCACJ,GAAG,MAAM,IAAI;wCACb,YAAY;wCACZ,cAAc,aAAa,gBAAgB;oCAC7C;oCACA,oBAAoB;gCACtB,CAAC;4BAED,IAAI,CAAC,YAAY,mBAAmB,EAAE;gCACpC,cAAc;4BAChB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kCAAkC;wBAClD;oBACF,GAAG;oBAEH,WAAW,IAAM,cAAc,gBAAgB,IAAI,KAAK;gBAC1D;gBAEA,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb;4BACA,cAAc,YAAY,gBAAgB;4BAC1C,WAAW;wBACb;wBACA,oBAAoB;oBACtB,CAAC;gBAED,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,IAAI,CAAC,QAAU,CAAC;wBAAE,MAAM;4BAAE,GAAG,MAAM,IAAI;4BAAE,WAAW;wBAAM;oBAAE,CAAC;gBAC7D,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM;gBACJ,cAAc,MAAM,IAAI,CAAC,YAAY;gBACrC,OAAO,MAAM,IAAI,CAAC,KAAK;gBACvB,UAAU,MAAM,IAAI,CAAC,QAAQ;gBAC7B,aAAa,MAAM,IAAI,CAAC,WAAW;gBACnC,cAAc,MAAM,IAAI,CAAC,YAAY;gBACrC,mBAAmB,MAAM,IAAI,CAAC,iBAAiB;gBAC/C,YAAY,MAAM,IAAI,CAAC,UAAU;YACnC;YACA,oBAAoB,MAAM,kBAAkB;QAC9C,CAAC;AACH;AAIG,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;;IACtE,MAAM,iBAAiB;gDAAQ,CAAC,QAAU,MAAM,cAAc;;IAC9D,MAAM,aAAa;4CAAQ,CAAC,QAAU,MAAM,UAAU;;IACtD,MAAM,OAAO;sCAAQ,CAAC,QAAU,MAAM,IAAI;;IAC1C,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAyB;IAEtD,MAAM,EAAE,gBAAgB,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,MAAM;IAE5D,MAAM,yBAAyB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;4DAAE;YACzC,IAAI,SAAS,eAAe,KAAK,WAAW;gBAC1C,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;gBACN;gBACA,iBAAiB,KAAK;wEAAC,CAAC;wBACtB,QAAQ,KAAK,CAAC,qDAAqD;oBACrE;;YACF;QACF;2DAAG;QAAC;QAAgB;KAAW;IAE/B,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;kCAAE;YACR,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YAEN,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,iBAAiB,IAAI;8CAAC;wBACpB,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;oBACR;;YACF;YAEA,IAAI,CAAC,YAAY,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACzC,YAAY,OAAO,GAAG;8CACpB;wBACE,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACN;oBACF;6CACA,KAAK,KAAK;YAEd;YAEA,IAAI,CAAC,gBAAgB,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC7C,gBAAgB,OAAO,GAAG;8CACxB;wBACE,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;wBACN;oBACF;6CACA,IAAI,KAAK;YAEb;YAEA,SAAS,gBAAgB,CAAC,oBAAoB;YAE9C;0CAAO;oBACL,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;wBACjC,YAAY,OAAO,GAAG;oBACxB;oBACA,IAAI,gBAAgB,OAAO,EAAE;wBAC3B,cAAc,gBAAgB,OAAO;wBACrC,gBAAgB,OAAO,GAAG;oBAC5B;oBACA,SAAS,mBAAmB,CAAC,oBAAoB;gBACnD;;QACF;iCAAG;QAAC;QAAgB;QAAY;QAAwB,cAAc,QAAQ;KAAC;IAE/E,OAAO;AACT;GAhEa;;QACY;QACJ;QACN;QAI6B,kIAAA,CAAA,cAAW;;;KAP1C", "debugId": null}}, {"offset": {"line": 2579, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/posthog/posthog-identify.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { identify } from \"@/lib/posthog-tracking\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useEffect } from \"react\";\r\n\r\nexport function PostHogIdentify() {\r\n  const user = useAuth((state) => state.user);\r\n\r\n  useEffect(() => {\r\n    if (user?.kinde_id && user?.email) {\r\n      identify(user);\r\n    }\r\n  }, [user?.kinde_id, user?.email]);\r\n\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,OAAO,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;yCAAE,CAAC,QAAU,MAAM,IAAI;;IAE1C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,MAAM,YAAY,MAAM,OAAO;gBACjC,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD,EAAE;YACX;QACF;oCAAG;QAAC,MAAM;QAAU,MAAM;KAAM;IAEhC,OAAO;AACT;GAVgB;;QACD,wIAAA,CAAA,UAAO;;;KADN", "debugId": null}}, {"offset": {"line": 2624, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/posthog/posthog-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport posthog from \"posthog-js\";\r\nimport { PostHog<PERSON>rovider as PostHogProviderBase } from \"posthog-js/react\";\r\nimport { useEffect } from \"react\";\r\n\r\nexport const PostHogProvider = ({ children }: { children: React.ReactNode }) => {\r\n  useEffect(() => {\r\n    if (!process.env.NEXT_PUBLIC_POSTHOG_KEY) return;\r\n\r\n    posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {\r\n      api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || \"https://us.i.posthog.com\",\r\n      person_profiles: \"identified_only\",\r\n      loaded: (posthog) => {\r\n        if (process.env.NODE_ENV === \"development\") posthog.debug();\r\n      },\r\n    });\r\n  }, []);\r\n\r\n  return <PostHogProviderBase client={posthog}>{children}</PostHogProviderBase>;\r\n};\r\n"], "names": [], "mappings": ";;;AAQS;;AANT;AACA;AACA;;;AAJA;;;;AAMO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAiC;;IACzE,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,uCAA0C;;YAAM;YAEhD,8MAAA,CAAA,UAAO,CAAC,IAAI,sFAAsC;gBAChD,UAAU,wCAAwC;gBAClD,iBAAiB;gBACjB,MAAM;iDAAE,CAAC;wBACP,wCAA4C,QAAQ,KAAK;oBAC3D;;YACF;QACF;oCAAG,EAAE;IAEL,qBAAO,4SAAC,6NAAA,CAAA,kBAAmB;QAAC,QAAQ,8MAAA,CAAA,UAAO;kBAAG;;;;;;AAChD;GAda;KAAA", "debugId": null}}, {"offset": {"line": 2678, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\r\nimport * as React from \"react\";\r\n\r\nexport function ThemeProvider({\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof NextThemesProvider>) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,4SAAC,4PAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KALgB", "debugId": null}}]}