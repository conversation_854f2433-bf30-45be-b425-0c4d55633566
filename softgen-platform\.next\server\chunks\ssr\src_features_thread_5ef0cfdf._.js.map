{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/utils/parse-error-details.ts"], "sourcesContent": ["import { AppError } from \"@/features/project/desktop-view\";\r\n\r\nconst ERROR_DETAILS_REGEX = /<error_details errors=(.*?) \\/>/;\r\nconst NETWORK_ERRORS_REGEX = /<network_errors errors=(.*?) \\/>/;\r\nconst VERCEL_ERROR_REGEX = /<vercel_error error=\"([^\"]*)\" \\/>/;\r\n\r\nexport const parseErrorDetails = (\r\n  details: string,\r\n): {\r\n  errorDetails: AppError[] | null;\r\n  networkErrors: AppError[] | null;\r\n  cleanedContent: string;\r\n  vercelError: string | null;\r\n} => {\r\n  const errorDetailsMatch = details.match(ERROR_DETAILS_REGEX)?.[1];\r\n  const networkErrorsMatch = details.match(NETWORK_ERRORS_REGEX)?.[1];\r\n  const vercelErrorMatch = details.match(VERCEL_ERROR_REGEX)?.[1];\r\n\r\n  const cleanedContent = details\r\n    .replace(ERROR_DETAILS_REGEX, \"\")\r\n    .replace(NETWORK_ERRORS_REGEX, \"\")\r\n    .replace(VERCEL_ERROR_REGEX, \"\")\r\n    .trim();\r\n\r\n  let errorDetails: AppError[] | null = null;\r\n  let networkErrors: AppError[] | null = null;\r\n  let vercelError: string | null = null;\r\n\r\n  try {\r\n    if (errorDetailsMatch) {\r\n      errorDetails = JSON.parse(errorDetailsMatch);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Failed to parse error details:\", error);\r\n  }\r\n\r\n  try {\r\n    if (networkErrorsMatch) {\r\n      networkErrors = JSON.parse(networkErrorsMatch);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Failed to parse network errors:\", error);\r\n  }\r\n\r\n  try {\r\n    if (vercelErrorMatch) {\r\n      vercelError = vercelErrorMatch\r\n        .replace(/&quot;/g, '\"')\r\n        .replace(/\\\\n/g, \"\\n\")\r\n        .replace(/\\\\r/g, \"\\r\");\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Failed to parse vercel error:\", error);\r\n  }\r\n\r\n  return {\r\n    errorDetails,\r\n    networkErrors,\r\n    cleanedContent,\r\n    vercelError,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA,MAAM,sBAAsB;AAC5B,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAEpB,MAAM,oBAAoB,CAC/B;IAOA,MAAM,oBAAoB,QAAQ,KAAK,CAAC,sBAAsB,CAAC,EAAE;IACjE,MAAM,qBAAqB,QAAQ,KAAK,CAAC,uBAAuB,CAAC,EAAE;IACnE,MAAM,mBAAmB,QAAQ,KAAK,CAAC,qBAAqB,CAAC,EAAE;IAE/D,MAAM,iBAAiB,QACpB,OAAO,CAAC,qBAAqB,IAC7B,OAAO,CAAC,sBAAsB,IAC9B,OAAO,CAAC,oBAAoB,IAC5B,IAAI;IAEP,IAAI,eAAkC;IACtC,IAAI,gBAAmC;IACvC,IAAI,cAA6B;IAEjC,IAAI;QACF,IAAI,mBAAmB;YACrB,eAAe,KAAK,KAAK,CAAC;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;IAEA,IAAI;QACF,IAAI,oBAAoB;YACtB,gBAAgB,KAAK,KAAK,CAAC;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;IACnD;IAEA,IAAI;QACF,IAAI,kBAAkB;YACpB,cAAc,iBACX,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,QAAQ,MAChB,OAAO,CAAC,QAAQ;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/message-input.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"@/components/ui/collapsible\";\r\nimport { Form, FormControl, FormField, FormItem } from \"@/components/ui/form\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { LazyModal } from \"@/components/ui/modal\";\r\nimport { Preview, PreviewContent, PreviewTrigger } from \"@/components/ui/preview\";\r\nimport {\r\n  PreviewImage,\r\n  PreviewImageContent,\r\n  PreviewImageTrigger,\r\n} from \"@/components/ui/preview-image\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { getFileContent, getFilePaths, uploadFile } from \"@/lib/api\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { useAgentInput, useSubscribeToAgentInput } from \"@/stores/agent-input\";\r\nimport { useCurrentThreadStore, useSubscribeToAgentRunning } from \"@/stores/current-thread\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport {\r\n  ArrowUp,\r\n  CogOneSolid,\r\n  DangerTriangleSolid,\r\n  FileTextSolid,\r\n  LightningSolid,\r\n  MicrophoneSlashSolid,\r\n  Paperclip,\r\n  Sparkles,\r\n  Square,\r\n  Upload,\r\n  X,\r\n} from \"@mynaui/icons-react\";\r\nimport { useMutation, useQuery } from \"@tanstack/react-query\";\r\nimport { BrainIcon, Mic, Zap } from \"lucide-react\";\r\nimport { AnimatePresence, motion } from \"motion/react\";\r\nimport { lazy, useCallback, useEffect, useMemo, useRef, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport SpeechRecognition, { useSpeechRecognition } from \"react-speech-recognition\";\r\nimport { z } from \"zod\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport Hint from \"../../components/ui/hint\";\r\nimport {\r\n  MAX_IMAGES_PER_MESSAGE,\r\n  validateImage,\r\n  validateImageCount,\r\n} from \"../../constants/image-validation\";\r\nimport { useTokenInfo } from \"../../hooks/use-token-info\";\r\nimport { errorToast, successToast } from \"../global/toast\";\r\nimport { parseErrorDetails } from \"./utils/parse-error-details\";\r\n\r\nconst FileUploadModalContent = lazy(() => import(\"../project/modal/file-upload-modal\"));\r\nconst AgentSettingModalContent = lazy(() => import(\"./agent-setting-modal\"));\r\n\r\nconst messageSchema = z.object({\r\n  content: z\r\n    .string()\r\n    .min(1, \"Message cannot be empty\")\r\n    .max(10000, \"Message cannot be longer than 10000 characters\"),\r\n  selectedModel: z.enum([\"creative\", \"standard\", \"plan\"]).default(\"creative\"),\r\n  files: z.array(z.custom<File>()).default([]),\r\n  selectedPaths: z.array(z.string()).default([]),\r\n  rawErrors: z\r\n    .array(\r\n      z.object({\r\n        id: z.string().optional(),\r\n        timestamp: z.union([z.string(), z.date()]).optional(),\r\n        pageUrl: z.string().optional(),\r\n        name: z.string(),\r\n        message: z.string(),\r\n        type: z.string().optional(),\r\n        stack: z.string().optional(),\r\n        details: z.any().optional(),\r\n        url: z.string().optional(),\r\n        status: z.number().optional(),\r\n        method: z.string().optional(),\r\n      }),\r\n    )\r\n    .optional(),\r\n  networkErrors: z\r\n    .array(\r\n      z.object({\r\n        id: z.string().optional(),\r\n        timestamp: z.union([z.string(), z.date()]).optional(),\r\n        pageUrl: z.string().optional(),\r\n        name: z.string(),\r\n        message: z.string(),\r\n        type: z.string().optional(),\r\n        stack: z.string().optional(),\r\n        details: z.any().optional(),\r\n        url: z.string().optional(),\r\n        status: z.number().optional(),\r\n        method: z.string().optional(),\r\n      }),\r\n    )\r\n    .optional(),\r\n  contentWithErrors: z.string().optional(),\r\n});\r\n\r\ntype MessageFormData = z.infer<typeof messageSchema>;\r\n\r\nconst FilePathPreview = ({\r\n  path,\r\n  projectId,\r\n  onRemove,\r\n}: {\r\n  path: string;\r\n  projectId: string;\r\n  onRemove: (path: string) => void;\r\n}) => {\r\n  const { data: content, isLoading } = useQuery({\r\n    queryKey: [\"fileContent\", projectId, path],\r\n    queryFn: () => getFileContent(projectId, path),\r\n    enabled: !!projectId && !!path,\r\n    notifyOnChangeProps: [\"data\", \"isLoading\"],\r\n  });\r\n\r\n  return (\r\n    <Preview>\r\n      <PreviewTrigger asChild>\r\n        <div className=\"group relative\">\r\n          <div\r\n            className={cn(\r\n              \"flex h-fit w-fit items-center gap-2 rounded-xl border border-primary/10 p-1 text-sm font-medium leading-none shadow-sm transition-all duration-300\",\r\n              \"border border-primary/20 bg-accent/80 text-primary/80 hover:bg-accent/90 hover:text-primary dark:border-primary/15\",\r\n            )}\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-md border border-border/60 bg-sidebar-primary/80 p-1 shadow-sm dark:bg-sidebar-primary/40\">\r\n                <FileTextSolid className=\"h-full w-full text-background/90\" />\r\n              </div>\r\n              <div className=\"flex flex-col gap-0 pr-4\">\r\n                <span className=\"truncate font-medium text-primary/90\">\r\n                  {path.split(\"/\").pop()}\r\n                </span>\r\n                <span className=\"text-left text-xs font-normal text-muted-foreground\">File</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <Button\r\n            type=\"button\"\r\n            size=\"icon\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onRemove(path);\r\n            }}\r\n            className=\"absolute -right-1.5 -top-1.5 z-[100] size-4 rounded-full p-0 opacity-0 shadow-md ring-1 ring-border/50 group-hover:opacity-100\"\r\n          >\r\n            <X className=\"size-3.5\" />\r\n          </Button>\r\n        </div>\r\n      </PreviewTrigger>\r\n\r\n      <PreviewContent\r\n        fileContent={isLoading ? \"Loading content...\" : content || \"\"}\r\n        fileName={path}\r\n        border\r\n      />\r\n    </Preview>\r\n  );\r\n};\r\n\r\nconst MessageInput = () => {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const dropAreaRef = useRef<HTMLDivElement>(null);\r\n  const selectedItemRef = useRef<HTMLDivElement>(null);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n  const [selectedImages, setSelectedImages] = useState<File[]>([]);\r\n  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\r\n  const [showFileSelector, setShowFileSelector] = useState(false);\r\n  const [showAgentSettings, setShowAgentSettings] = useState(false);\r\n  const [selectedFileIndex, setSelectedFileIndex] = useState(0);\r\n  const [cursorPosition, setCursorPosition] = useState(0);\r\n  const [isStoppingSession, setIsStoppingSession] = useState(false);\r\n  const userHasSelectedModel = useRef(false);\r\n  const [showFileUploadModal, setShowFileUploadModal] = useState(false);\r\n  const [fileQueue, setFileQueue] = useState<File[]>([]);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [fileSearchQuery, setFileSearchQuery] = useState(\"\");\r\n  const [uploadProgress, setUploadProgress] = useState<{\r\n    [key: string]: {\r\n      status: \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n      progress: number;\r\n      error?: string;\r\n    };\r\n  }>({});\r\n\r\n  // Speech recognition state\r\n  const messageBeforeListening = useRef(\"\");\r\n  const isListeningRef = useRef(false);\r\n\r\n  const setActiveTab = useNavigateFile((state) => state.setActiveTab);\r\n  const { inputContent, mode, setMode, setInputContent, reset } = useAgentInput();\r\n  const { getTokenDisplayInfo } = useTokenInfo();\r\n  const tokenInfo = getTokenDisplayInfo();\r\n  const { user } = useAuth();\r\n\r\n  const [animateKey, setAnimateKey] = useState(0);\r\n  const [isTextareaFocused, setIsTextareaFocused] = useState(false);\r\n  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);\r\n\r\n  const {\r\n    isLoading: isThreadLoading,\r\n    sendMessage,\r\n    stopSession,\r\n    contextLimitReached,\r\n    currentThread,\r\n  } = useThread();\r\n\r\n  const { isAgentRunning, setNoTokenModalOpen } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      isAgentRunning: state.isAgentRunning,\r\n      setNoTokenModalOpen: state.setNoTokenModalOpen,\r\n    })),\r\n  );\r\n\r\n  const { projectId } = useProject();\r\n  const isMobile = useIsMobile();\r\n\r\n  const scrollTextareaIntoView = useCallback(() => {\r\n    const textarea = textareaRef.current;\r\n    if (!textarea || !(textarea instanceof HTMLTextAreaElement)) return;\r\n\r\n    try {\r\n      if (typeof textarea.scrollIntoView === \"function\") {\r\n        textarea.scrollIntoView({\r\n          behavior: \"smooth\",\r\n          block: \"center\",\r\n          inline: \"nearest\",\r\n        });\r\n      } else {\r\n        // Fallback for older browsers or when scrollIntoView is not available\r\n        try {\r\n          const rect = textarea.getBoundingClientRect();\r\n          if (!rect) {\r\n            // If getBoundingClientRect fails, fall back to simple scroll\r\n            window.scrollTo({\r\n              top: document.body.scrollHeight,\r\n              behavior: \"smooth\",\r\n            });\r\n            return;\r\n          }\r\n\r\n          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n          const targetScroll = rect.top + scrollTop - window.innerHeight / 2;\r\n\r\n          window.scrollTo({\r\n            top: targetScroll,\r\n            behavior: \"smooth\",\r\n          });\r\n        } catch (error) {\r\n          console.error(\"Error in scrollTextareaIntoView:\", error);\r\n          // If getBoundingClientRect throws, fall back to simple scroll\r\n          window.scrollTo({\r\n            top: document.body.scrollHeight,\r\n            behavior: \"smooth\",\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in scrollTextareaIntoView:\", error);\r\n      // Final fallback\r\n      window.scrollTo({\r\n        top: document.body.scrollHeight,\r\n        behavior: \"smooth\",\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  const setFormContent = (content: string) => {\r\n    form.setValue(\"content\", content);\r\n  };\r\n\r\n  const handleTextareaFocus = useCallback(() => {\r\n    setIsTextareaFocused(true);\r\n    if (isMobile && textareaRef.current) {\r\n      setTimeout(() => {\r\n        const textarea = textareaRef.current;\r\n        if (!textarea || !(textarea instanceof HTMLTextAreaElement)) return;\r\n\r\n        try {\r\n          const rect = textarea.getBoundingClientRect();\r\n          if (!rect) return;\r\n\r\n          const windowHeight = window.innerHeight;\r\n          const keyboardHeight = windowHeight * 0.4;\r\n          const safeAreaBottom = parseInt(\r\n            getComputedStyle(document.documentElement).getPropertyValue(\"--sat\") || \"0\",\r\n          );\r\n\r\n          if (rect.bottom > windowHeight - keyboardHeight - safeAreaBottom) {\r\n            scrollTextareaIntoView();\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error in handleTextareaFocus:\", error);\r\n        }\r\n      }, 150);\r\n    }\r\n  }, [isMobile, scrollTextareaIntoView]);\r\n\r\n  const handleTextareaClick = useCallback(() => {\r\n    if (isMobile) {\r\n      setTimeout(() => {\r\n        scrollTextareaIntoView();\r\n      }, 100);\r\n    }\r\n  }, [isMobile, scrollTextareaIntoView]);\r\n\r\n  const handleTextareaBlur = useCallback(() => {\r\n    setIsTextareaFocused(false);\r\n    setIsKeyboardVisible(false);\r\n    if (isMobile) {\r\n      setTimeout(() => {\r\n        window.scrollTo({\r\n          top: 0,\r\n          behavior: \"smooth\",\r\n        });\r\n      }, 100);\r\n    }\r\n  }, [isMobile]);\r\n\r\n  useEffect(() => {\r\n    setAnimateKey((prevKey) => prevKey + 1);\r\n  }, [tokenInfo.remainingTokens, tokenInfo.effectiveTotalTokens]);\r\n\r\n  const form = useForm<MessageFormData>({\r\n    resolver: zodResolver(messageSchema),\r\n    defaultValues: {\r\n      content: inputContent || \"\",\r\n      files: [],\r\n      selectedPaths: [],\r\n    },\r\n  });\r\n\r\n  const emptyThread = currentThread?.messages?.length === 0;\r\n\r\n  useEffect(() => {\r\n    if (!isThreadLoading && !userHasSelectedModel.current && emptyThread) {\r\n      setMode(\"plan\");\r\n    }\r\n  }, [emptyThread, isThreadLoading]);\r\n\r\n  useSubscribeToAgentInput((content) => {\r\n    if (content) {\r\n      form.setValue(\"content\", content);\r\n      setInputContent(null);\r\n    }\r\n  });\r\n\r\n  useEffect(() => {\r\n    // reset agent input on unmount\r\n    return () => {\r\n      console.debug(\"[MessageInput] Unmounting message input\");\r\n      reset();\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    form.setFocus(\"content\");\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isMobile) {\r\n      const viewport = document.querySelector('meta[name=\"viewport\"]');\r\n      if (!viewport) {\r\n        const meta = document.createElement(\"meta\");\r\n        meta.name = \"viewport\";\r\n        meta.content =\r\n          \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover\";\r\n        document.head.appendChild(meta);\r\n      }\r\n      let initialHeight = window.innerHeight;\r\n      let resizeTimeout: NodeJS.Timeout;\r\n      const visualViewport = window.visualViewport;\r\n\r\n      const handleResize = () => {\r\n        clearTimeout(resizeTimeout);\r\n        resizeTimeout = setTimeout(() => {\r\n          const currentHeight = window.innerHeight;\r\n          const heightDifference = initialHeight - currentHeight;\r\n\r\n          if (heightDifference > 150) {\r\n            setIsKeyboardVisible(true);\r\n            if (isTextareaFocused) {\r\n              setTimeout(() => {\r\n                scrollTextareaIntoView();\r\n              }, 100);\r\n            }\r\n          } else {\r\n            setIsKeyboardVisible(false);\r\n          }\r\n\r\n          initialHeight = currentHeight;\r\n        }, 100);\r\n      };\r\n\r\n      const handleVisualViewportChange = () => {\r\n        if (visualViewport) {\r\n          const heightDifference = initialHeight - visualViewport.height;\r\n\r\n          if (heightDifference > 150) {\r\n            setIsKeyboardVisible(true);\r\n            if (isTextareaFocused) {\r\n              setTimeout(() => {\r\n                scrollTextareaIntoView();\r\n              }, 100);\r\n            }\r\n          } else {\r\n            setIsKeyboardVisible(false);\r\n          }\r\n        }\r\n      };\r\n\r\n      window.addEventListener(\"resize\", handleResize);\r\n\r\n      if (visualViewport) {\r\n        visualViewport.addEventListener(\"resize\", handleVisualViewportChange);\r\n      }\r\n\r\n      return () => {\r\n        window.removeEventListener(\"resize\", handleResize);\r\n        if (visualViewport) {\r\n          visualViewport.removeEventListener(\"resize\", handleVisualViewportChange);\r\n        }\r\n        clearTimeout(resizeTimeout);\r\n      };\r\n    }\r\n  }, [isMobile, isTextareaFocused]);\r\n\r\n  const selectedPaths = form.watch(\"selectedPaths\");\r\n  const content = form.watch(\"content\");\r\n  const rawErrors = form.watch(\"rawErrors\");\r\n  const formNetworkErrors = form.watch(\"networkErrors\");\r\n\r\n  const addMessageMutation = useMutation({\r\n    mutationFn: async ({ content, selectedPaths }: MessageFormData) => {\r\n      debug(\"Sending message with content:\", content);\r\n\r\n      if (contextLimitReached) {\r\n        errorToast(\"Context window is full. Please start a new thread/task.\");\r\n        throw new Error(\"Context window is full. Please start a new thread/task.\");\r\n      }\r\n\r\n      if (content.trim().length === 0) {\r\n        errorToast(\"Message cannot be empty\");\r\n        throw new Error(\"Message cannot be empty\");\r\n      }\r\n\r\n      const validImages = selectedImages.filter(\r\n        (file) => file instanceof File && file.type.startsWith(\"image/\"),\r\n      );\r\n\r\n      validateImageCount(validImages.length);\r\n\r\n      for (const image of validImages) {\r\n        validateImage(image);\r\n      }\r\n\r\n      const formattedPaths = selectedPaths.map((path) => {\r\n        return `attached_file_${path}`;\r\n      });\r\n\r\n      const contentWithErrors = reconstructErrorDetails(content);\r\n      const finalContent =\r\n        selectedPaths.length > 0\r\n          ? formattedPaths.join(\" \") + \" \" + contentWithErrors\r\n          : contentWithErrors;\r\n\r\n      try {\r\n        await sendMessage({\r\n          content: finalContent,\r\n          options: {\r\n            images: validImages,\r\n            selectedPaths,\r\n            model: mode,\r\n          },\r\n        });\r\n        return { success: true };\r\n      } catch (error: unknown) {\r\n        console.error(\"Error sending message:\", error);\r\n        const errorMessage = error instanceof Error ? error.message : \"Failed to send message\";\r\n        errorToast(errorMessage);\r\n        throw error;\r\n      }\r\n    },\r\n    onSuccess: () => {\r\n      form.setValue(\"content\", \"\");\r\n      form.setValue(\"files\", []);\r\n      form.setValue(\"selectedPaths\", []);\r\n      form.setValue(\"contentWithErrors\", \"\");\r\n      form.setValue(\"rawErrors\", []);\r\n      form.setValue(\"networkErrors\", []);\r\n      setSelectedImages([]);\r\n      setSelectedFiles([]);\r\n      setInputContent(null);\r\n      successToast(\"Message sent successfully\");\r\n    },\r\n    onError: (error: unknown) => {\r\n      errorToast(error instanceof Error ? error.message : \"Failed to send message\");\r\n    },\r\n  });\r\n\r\n  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = Array.from(e.target.files || []);\r\n    if (files.length > 0) {\r\n      try {\r\n        setFileQueue(files);\r\n        setShowFileUploadModal(true);\r\n      } catch (error) {\r\n        errorToast(error instanceof Error ? error.message : \"Failed to add images\");\r\n        e.target.value = \"\";\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleFileDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(false);\r\n\r\n    if (!e.dataTransfer.files || e.dataTransfer.files.length === 0) return;\r\n\r\n    const droppedFiles = Array.from(e.dataTransfer.files);\r\n    if (droppedFiles.length > 0) {\r\n      const imageFiles = droppedFiles.filter((file) => file.type.startsWith(\"image/\"));\r\n\r\n      try {\r\n        if (imageFiles.length > 0) {\r\n          validateImageCount(selectedImages.length, imageFiles.length);\r\n        }\r\n\r\n        setFileQueue(droppedFiles);\r\n        setShowFileUploadModal(true);\r\n      } catch (error) {\r\n        errorToast(error instanceof Error ? error.message : \"Failed to add images\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(false);\r\n  };\r\n\r\n  const handleFileUploadConfirm = async (files: File[], action: \"attach\" | \"upload\" | \"both\") => {\r\n    try {\r\n      let progressInterval: NodeJS.Timeout | undefined;\r\n\r\n      if (action === \"upload\" || action === \"both\") {\r\n        // Initialize upload progress for all files\r\n        files.forEach((file) => {\r\n          setUploadProgress((prev) => ({\r\n            ...prev,\r\n            [file.name]: {\r\n              status: \"uploading\",\r\n              progress: 0,\r\n              fileSize: file.size,\r\n              fileName: file.name,\r\n              fileType: file.type,\r\n            },\r\n          }));\r\n        });\r\n\r\n        // Simulate progress updates\r\n        progressInterval = setInterval(() => {\r\n          setUploadProgress((prev) => {\r\n            const newProgress = { ...prev };\r\n            let hasActiveUploads = false;\r\n\r\n            files.forEach((file) => {\r\n              const currentProgress = prev[file.name];\r\n              if (\r\n                currentProgress &&\r\n                currentProgress.status === \"uploading\" &&\r\n                currentProgress.progress < 90\r\n              ) {\r\n                newProgress[file.name] = {\r\n                  ...currentProgress,\r\n                  progress: Math.min(currentProgress.progress + Math.random() * 15, 90),\r\n                };\r\n                hasActiveUploads = true;\r\n              }\r\n            });\r\n\r\n            // Clear interval if no active uploads\r\n            if (!hasActiveUploads && progressInterval) {\r\n              clearInterval(progressInterval);\r\n            }\r\n\r\n            return newProgress;\r\n          });\r\n        }, 300);\r\n      }\r\n\r\n      if (action === \"attach\" || action === \"both\") {\r\n        const imageFiles = files.filter((file) => file.type.startsWith(\"image/\"));\r\n        if (imageFiles.length > 0) {\r\n          validateImageCount(selectedImages.length, imageFiles.length);\r\n        }\r\n\r\n        files.forEach((file) => {\r\n          if (file.type.startsWith(\"image/\")) {\r\n            validateImage(file);\r\n            setSelectedImages((prev) => [...prev, file]);\r\n          } else {\r\n            setSelectedFiles((prev) => [...prev, file]);\r\n          }\r\n        });\r\n\r\n        if (action === \"attach\") {\r\n          files.forEach((file) => {\r\n            appendFileReferenceToMessage(file, \"attachment\");\r\n          });\r\n\r\n          // Remove files from queue immediately\r\n          setFileQueue((prev) => {\r\n            const remainingFiles = prev.filter((f) => !files.includes(f));\r\n            if (remainingFiles.length === 0) {\r\n              setShowFileUploadModal(false);\r\n            }\r\n            return remainingFiles;\r\n          });\r\n          return;\r\n        }\r\n      }\r\n\r\n      if (action === \"upload\" || action === \"both\") {\r\n        try {\r\n          // Upload files sequentially with proper progress tracking\r\n          for (const file of files) {\r\n            const uploadPath = `/public/${file.name}`;\r\n\r\n            // Update progress to show upload starting\r\n            setUploadProgress((prev) => ({\r\n              ...prev,\r\n              [file.name]: {\r\n                ...prev[file.name],\r\n                status: \"uploading\",\r\n                progress: 95,\r\n              },\r\n            }));\r\n\r\n            await uploadFile({\r\n              projectId,\r\n              file,\r\n              relativePath: uploadPath,\r\n              onProgress: (progress) => {\r\n                setUploadProgress((prev) => ({\r\n                  ...prev,\r\n                  [file.name]: {\r\n                    ...prev[file.name],\r\n                    progress: Math.min(progress, 95),\r\n                  },\r\n                }));\r\n              },\r\n            });\r\n\r\n            // Mark as successful and remove from queue\r\n            setUploadProgress((prev) => ({\r\n              ...prev,\r\n              [file.name]: {\r\n                ...prev[file.name],\r\n                status: \"success\",\r\n                progress: 100,\r\n              },\r\n            }));\r\n\r\n            // Remove from file queue immediately after successful upload\r\n            setFileQueue((prev) => prev.filter((f) => f.name !== file.name));\r\n\r\n            appendFileReferenceToMessage(file, \"public\", uploadPath);\r\n\r\n            // Remove from upload progress after a short delay to show success\r\n            setTimeout(() => {\r\n              setUploadProgress((prev) => {\r\n                const newProgress = { ...prev };\r\n                delete newProgress[file.name];\r\n                return newProgress;\r\n              });\r\n            }, 2000);\r\n          }\r\n\r\n          if (progressInterval) {\r\n            clearInterval(progressInterval);\r\n          }\r\n\r\n          successToast(\"Files uploaded successfully\");\r\n        } catch (error) {\r\n          console.error(\"Error uploading files:\", error);\r\n          const errorMessage = error instanceof Error ? error.message : \"Failed to upload files\";\r\n          errorToast(errorMessage);\r\n\r\n          if (progressInterval) {\r\n            clearInterval(progressInterval);\r\n          }\r\n\r\n          files.forEach((file) => {\r\n            setUploadProgress((prev) => ({\r\n              ...prev,\r\n              [file.name]: {\r\n                ...prev[file.name],\r\n                status: \"error\",\r\n                progress: 0,\r\n                error: errorMessage,\r\n              },\r\n            }));\r\n          });\r\n\r\n          if (action === \"both\") {\r\n            files.forEach((file) => {\r\n              appendFileReferenceToMessage(file, \"public\", `/public/${file.name}`);\r\n            });\r\n          } else {\r\n            return;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Update remaining files in queue\r\n      setFileQueue((prev) => {\r\n        const remainingFiles = prev.filter((f) => !files.includes(f));\r\n        if (remainingFiles.length === 0) {\r\n          setShowFileUploadModal(false);\r\n        }\r\n        return remainingFiles;\r\n      });\r\n\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = \"\";\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error handling files:\", error);\r\n      const errorMessage = error instanceof Error ? error.message : \"Failed to process files\";\r\n      errorToast(errorMessage);\r\n\r\n      if (action !== \"attach\") {\r\n        files.forEach((file) => {\r\n          setUploadProgress((prev) => ({\r\n            ...prev,\r\n            [file.name]: {\r\n              ...prev[file.name],\r\n              status: \"error\",\r\n              progress: 0,\r\n              error: errorMessage,\r\n            },\r\n          }));\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const appendFileReferenceToMessage = (\r\n    file: File,\r\n    type: \"attachment\" | \"public\",\r\n    uploadPath?: string,\r\n  ) => {\r\n    const currentMessage = form.getValues(\"content\");\r\n    let markdownReference = \"\";\r\n\r\n    if (file.type.startsWith(\"image/\")) {\r\n      if (type === \"public\" && uploadPath) {\r\n        markdownReference = `\\n![${file.name}](${uploadPath})\\n`;\r\n      } else {\r\n        return;\r\n      }\r\n    } else {\r\n      if (type === \"public\" && uploadPath) {\r\n        markdownReference = `\\n[${file.name}](${uploadPath})\\n`;\r\n      } else {\r\n        markdownReference = `\\n*File attached: ${file.name}*\\n`;\r\n      }\r\n    }\r\n\r\n    form.setValue(\r\n      \"content\",\r\n      currentMessage.trim().trimStart().trimEnd() + markdownReference.trim().trimStart().trimEnd(),\r\n    );\r\n  };\r\n\r\n  const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {\r\n    const items = Array.from(e.clipboardData.items);\r\n    const imageItems = items.filter(\r\n      (item) => item.kind === \"file\" && item.type.startsWith(\"image/\"),\r\n    );\r\n\r\n    if (imageItems.length === 0) {\r\n      return;\r\n    }\r\n\r\n    e.preventDefault();\r\n\r\n    try {\r\n      validateImageCount(selectedImages.length, imageItems.length);\r\n\r\n      imageItems.forEach((item) => {\r\n        const file = item.getAsFile();\r\n        if (!file) return;\r\n\r\n        validateImage(file);\r\n\r\n        const timestamp = Date.now();\r\n        const fileName = `pasted-image-${timestamp}.png`;\r\n        const blob = file.slice(0, file.size, file.type);\r\n        const renamedFile = new File([blob], fileName, { type: file.type });\r\n\r\n        setSelectedImages((prev) => [...prev, renamedFile]);\r\n      });\r\n    } catch (error) {\r\n      errorToast(error instanceof Error ? error.message : \"Failed to add pasted image\");\r\n    }\r\n  };\r\n\r\n  const removeFile = (file: File) => {\r\n    setSelectedFiles((prev) => prev.filter((f) => f !== file));\r\n  };\r\n\r\n  const removeImage = (file: File) => {\r\n    setSelectedImages((prev) => {\r\n      const updatedImages = prev.filter((f) => f !== file);\r\n      return updatedImages;\r\n    });\r\n  };\r\n\r\n  const files = [...selectedImages, ...selectedFiles];\r\n\r\n  const { data: availableFiles = [], refetch: refetchFilePaths } = useQuery({\r\n    queryKey: [\"project-filePaths\", projectId],\r\n    queryFn: async () => {\r\n      const result = await getFilePaths(projectId, \"/app/\", 999);\r\n      return result?.paths || [];\r\n    },\r\n    enabled: !!projectId,\r\n    staleTime: 1000 * 60,\r\n  });\r\n\r\n  useSubscribeToAgentRunning((isAgentRunning, wasRunning) => {\r\n    if (!isAgentRunning && wasRunning) {\r\n      refetchFilePaths();\r\n    }\r\n  });\r\n\r\n  const insertFilePath = (file: string) => {\r\n    const textBeforeCursor = (content || \"\").substring(0, cursorPosition);\r\n    const lastAtIndex = textBeforeCursor.lastIndexOf(\"@\");\r\n    const textAfterCursor = (content || \"\").substring(cursorPosition);\r\n\r\n    const newContent = textBeforeCursor.substring(0, lastAtIndex) + textAfterCursor;\r\n    form.setValue(\"content\", newContent);\r\n    form.setValue(\"selectedPaths\", [...(selectedPaths || []), file]);\r\n    setShowFileSelector(false);\r\n    setFileSearchQuery(\"\");\r\n    form.setFocus(\"content\");\r\n  };\r\n\r\n  const filteredFiles = useMemo(() => {\r\n    if (!showFileSelector) return [];\r\n\r\n    const textBeforeCursor = (content || \"\").substring(0, cursorPosition);\r\n    const lastAtIndex = textBeforeCursor.lastIndexOf(\"@\");\r\n    const searchText = fileSearchQuery || textBeforeCursor.substring(lastAtIndex + 1).toLowerCase();\r\n\r\n    return availableFiles.filter(\r\n      (file: string) =>\r\n        file.toLowerCase().includes(searchText.toLowerCase()) &&\r\n        !(selectedPaths || []).includes(file),\r\n    );\r\n  }, [content, cursorPosition, availableFiles, selectedPaths, showFileSelector, fileSearchQuery]);\r\n\r\n  useEffect(() => {\r\n    if (showFileSelector) {\r\n      setSelectedFileIndex(0);\r\n      setFileSearchQuery(\"\");\r\n    }\r\n  }, [showFileSelector]);\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === \"Enter\" && !e.shiftKey) {\r\n      if (showFileSelector) {\r\n        e.preventDefault();\r\n        if (filteredFiles.length > 0) {\r\n          insertFilePath(filteredFiles[selectedFileIndex]);\r\n        }\r\n        return;\r\n      }\r\n\r\n      e.preventDefault();\r\n      form.handleSubmit((data) => addMessageMutation.mutate(data))();\r\n      return;\r\n    }\r\n\r\n    if (!showFileSelector) return;\r\n\r\n    if (e.key === \"ArrowDown\" || e.key === \"ArrowUp\") {\r\n      e.preventDefault();\r\n\r\n      const newIndex =\r\n        e.key === \"ArrowDown\"\r\n          ? selectedFileIndex < filteredFiles.length - 1\r\n            ? selectedFileIndex + 1\r\n            : selectedFileIndex\r\n          : selectedFileIndex > 0\r\n            ? selectedFileIndex - 1\r\n            : selectedFileIndex;\r\n\r\n      setSelectedFileIndex(newIndex);\r\n\r\n      setTimeout(() => {\r\n        const selectedItem = document.querySelector(`[data-file-index=\"${newIndex}\"]`);\r\n        selectedItem?.scrollIntoView({ block: \"nearest\", behavior: \"smooth\" });\r\n      }, 0);\r\n    } else if (e.key === \"Escape\") {\r\n      e.preventDefault();\r\n      setShowFileSelector(false);\r\n      setFileSearchQuery(\"\");\r\n    }\r\n  };\r\n\r\n  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    const value = e.target.value;\r\n    const cursorPos = e.target.selectionStart;\r\n\r\n    // If user starts typing while listening, stop speech recognition\r\n    if (isListeningRef.current && listening) {\r\n      stopListening();\r\n    }\r\n\r\n    // Don't update form content if we're currently listening to speech\r\n    if (!isListeningRef.current) {\r\n      form.setValue(\"content\", value);\r\n    }\r\n    setCursorPosition(cursorPos);\r\n\r\n    const textBeforeCursor = value.substring(0, cursorPos);\r\n    const lastAtIndex = textBeforeCursor.lastIndexOf(\"@\");\r\n\r\n    if (lastAtIndex !== -1) {\r\n      const searchText = textBeforeCursor.substring(lastAtIndex + 1);\r\n      const hasSpaceAfterAt = /\\s/.test(searchText);\r\n\r\n      if (!hasSpaceAfterAt) {\r\n        setShowFileSelector(true);\r\n        setSelectedFileIndex(0);\r\n        return;\r\n      }\r\n    }\r\n\r\n    setShowFileSelector(false);\r\n    setFileSearchQuery(\"\");\r\n  };\r\n\r\n  const handleStopSession = async () => {\r\n    try {\r\n      setIsStoppingSession(true);\r\n      const result = await stopSession();\r\n      if (result) {\r\n        successToast(\"Session stopped\");\r\n      }\r\n      setActiveTab(\"preview\");\r\n    } catch (error) {\r\n      console.error(\"Error in handleStopSession:\", error);\r\n    } finally {\r\n      setIsStoppingSession(false);\r\n    }\r\n  };\r\n\r\n  const { errorDetails, networkErrors, cleanedContent } = useMemo(() => {\r\n    const { errorDetails, networkErrors, cleanedContent } = parseErrorDetails(content || \"\");\r\n    return { errorDetails, networkErrors, cleanedContent };\r\n  }, [content]);\r\n\r\n  useEffect(() => {\r\n    if (errorDetails && !form.getValues(\"rawErrors\")?.length) {\r\n      form.setValue(\"rawErrors\", errorDetails);\r\n    }\r\n\r\n    if (networkErrors && !form.getValues(\"networkErrors\")?.length) {\r\n      form.setValue(\"networkErrors\", networkErrors);\r\n    }\r\n\r\n    if (cleanedContent && (errorDetails || networkErrors)) {\r\n      const currentContent = form.getValues(\"content\");\r\n      if (currentContent !== cleanedContent) {\r\n        form.setValue(\"content\", cleanedContent);\r\n      }\r\n    }\r\n  }, [errorDetails, networkErrors, cleanedContent]);\r\n\r\n  const removeErrorDetails = useCallback(() => {\r\n    form.setValue(\"rawErrors\", []);\r\n    form.setValue(\"contentWithErrors\", \"\");\r\n  }, [form]);\r\n\r\n  const removeNetworkErrors = useCallback(() => {\r\n    form.setValue(\"networkErrors\", []);\r\n    form.setValue(\"contentWithErrors\", \"\");\r\n  }, [form]);\r\n\r\n  const reconstructErrorDetails = useCallback(\r\n    (content: string): string => {\r\n      let finalContent = content;\r\n\r\n      const rawErrors = form.getValues(\"rawErrors\");\r\n      const networkErrors = form.getValues(\"networkErrors\");\r\n\r\n      if (rawErrors && rawErrors.length > 0) {\r\n        const errorDetailsTag = `<error_details errors=${JSON.stringify(rawErrors)} />`;\r\n        finalContent = finalContent + \" \" + errorDetailsTag;\r\n      }\r\n\r\n      if (networkErrors && networkErrors.length > 0) {\r\n        const networkErrorsTag = `<network_errors errors=${JSON.stringify(networkErrors)} />`;\r\n        finalContent = finalContent + \" \" + networkErrorsTag;\r\n      }\r\n\r\n      return finalContent;\r\n    },\r\n    [form],\r\n  );\r\n\r\n  // Speech recognition hook\r\n  const {\r\n    transcript,\r\n    listening,\r\n    resetTranscript,\r\n    browserSupportsSpeechRecognition,\r\n    isMicrophoneAvailable,\r\n  } = useSpeechRecognition({\r\n    commands: [\r\n      {\r\n        command: \"send message\",\r\n        callback: () => {\r\n          const currentContent = form.getValues(\"content\");\r\n          if (currentContent.trim()) {\r\n            // Stop listening before sending the message\r\n            stopListening();\r\n            form.handleSubmit((data) => addMessageMutation.mutate(data))();\r\n          }\r\n        },\r\n        matchInterim: true,\r\n      },\r\n      {\r\n        command: \"clear\",\r\n        callback: () => {\r\n          form.setValue(\"content\", \"\");\r\n          resetTranscript();\r\n        },\r\n        matchInterim: true,\r\n      },\r\n    ],\r\n  });\r\n\r\n  // Update speech transcript when transcript changes\r\n  useEffect(() => {\r\n    if (listening && transcript && isListeningRef.current) {\r\n      const separator = messageBeforeListening.current && transcript ? \" \" : \"\";\r\n      const newContent = messageBeforeListening.current + separator + transcript;\r\n      form.setValue(\"content\", newContent);\r\n    }\r\n  }, [transcript, listening, form]);\r\n\r\n  // Update listening ref when speech recognition state changes\r\n  useEffect(() => {\r\n    isListeningRef.current = listening;\r\n  }, [listening]);\r\n\r\n  // Speech recognition control functions\r\n  const startListening = () => {\r\n    if (!browserSupportsSpeechRecognition) {\r\n      errorToast(\"Speech Recognition Not Supported\", {\r\n        description: \"Your browser does not support speech recognition.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!isMicrophoneAvailable) {\r\n      errorToast(\"Microphone Access Required\", {\r\n        description: \"Please allow microphone access to use speech recognition.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Store the current content before starting\r\n      messageBeforeListening.current = form.getValues(\"content\");\r\n      isListeningRef.current = true;\r\n      resetTranscript();\r\n      SpeechRecognition.startListening({\r\n        continuous: true,\r\n        language: \"en-US\",\r\n      });\r\n      successToast(\"Listening...\", {\r\n        description: \"Speak your message. Say 'send message' to send or 'clear' to clear.\",\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error starting speech recognition:\", error);\r\n      isListeningRef.current = false;\r\n      errorToast(\"Error\", { description: \"Failed to start speech recognition.\" });\r\n    }\r\n  };\r\n\r\n  const stopListening = () => {\r\n    try {\r\n      SpeechRecognition.stopListening();\r\n      // The transcript will be preserved in the form content\r\n      // and the hook will handle the state transition\r\n    } catch (error) {\r\n      console.error(\"Error stopping speech recognition:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div\r\n        className={cn(\r\n          \"relative bottom-0 left-0 right-0 z-30 flex w-full flex-col items-center bg-transparent transition-all duration-300\",\r\n          isMobile ? \"pb-4\" : \"pb-2\",\r\n          isMobile && isTextareaFocused && \"pb-6\",\r\n          isMobile && isKeyboardVisible && \"pb-8\",\r\n          isMobile && \"min-h-[80px]\",\r\n        )}\r\n        style={\r\n          isMobile\r\n            ? {\r\n                paddingBottom: `calc(1rem + env(safe-area-inset-bottom))`,\r\n              }\r\n            : undefined\r\n        }\r\n      >\r\n        <AnimatePresence>\r\n          <motion.div\r\n            key={animateKey}\r\n            className=\"absolute inset-0 z-0 w-full\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: 10 }}\r\n            transition={{ duration: 0.3 }}\r\n          >\r\n            <div\r\n              className={cn(\r\n                \"group absolute inset-0 -top-6 z-0 mx-auto h-10 w-[90%] rounded-t-2xl px-4 md:w-[95%]\",\r\n                contextLimitReached ? \"bg-destructive\" : \"bg-[#E1E1E1] dark:bg-[#29292D]\",\r\n              )}\r\n            >\r\n              <div\r\n                className={cn(\r\n                  \"flex h-[60%] w-full items-center justify-between\",\r\n                  contextLimitReached && \"h-[140%] items-start pt-[0.14rem]\",\r\n                )}\r\n              >\r\n                {contextLimitReached ? (\r\n                  <Label className=\"text-sm text-background\">\r\n                    Context window is full. Please start a new thread/task.\r\n                  </Label>\r\n                ) : (\r\n                  <Label className=\"text-xs text-primary\">\r\n                    {tokenInfo.remainingTokens} Tokens left\r\n                  </Label>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </AnimatePresence>\r\n\r\n        <div\r\n          className={cn(\r\n            \"max-w-(--breakpoint-md) z-20 flex w-full gap-3\",\r\n            isMobile ? \"px-3\" : \"px-2\",\r\n            isMobile && \"min-h-[60px]\",\r\n          )}\r\n        >\r\n          <Form {...form}>\r\n            <form\r\n              onSubmit={form.handleSubmit((data) => {\r\n                if (\r\n                  !user.userFromDb?.isRequestBased &&\r\n                  user.userFromDb?.free_total_token !== undefined &&\r\n                  user.userFromDb.free_total_token <= 0 &&\r\n                  user.userFromDb?.token_event_name === null\r\n                ) {\r\n                  setNoTokenModalOpen(true);\r\n                  return;\r\n                }\r\n\r\n                addMessageMutation.mutate(data);\r\n              })}\r\n              className=\"mx-auto h-fit w-full\"\r\n            >\r\n              <div\r\n                ref={dropAreaRef}\r\n                className={cn(\r\n                  \"relative rounded-2xl border-[1px] border-primary/15 bg-background shadow-md transition-colors duration-300 ease-in-out\",\r\n                  isDragging && \"border-2 border-primary/50 bg-primary/5\",\r\n                  contextLimitReached && \"border-destructive\",\r\n                )}\r\n                onDragOver={handleDragOver}\r\n                onDragLeave={handleDragLeave}\r\n                onDrop={handleFileDrop}\r\n              >\r\n                {isDragging && (\r\n                  <div className=\"absolute inset-0 z-10 flex flex-col items-center justify-center rounded-2xl bg-primary/5 backdrop-blur-sm\">\r\n                    <Upload className=\"mb-2 h-10 w-10 text-primary/70\" />\r\n                    <p className=\"text-sm font-medium text-primary/70\">Drop files to upload</p>\r\n                  </div>\r\n                )}\r\n                <div className=\"flex w-full flex-col p-2\">\r\n                  {(rawErrors?.length ?? 0) > 0 && (\r\n                    <div className=\"flex flex-wrap gap-2 pb-2 transition-all duration-300\">\r\n                      <div className=\"relative w-full max-w-md\">\r\n                        <Collapsible className=\"group\">\r\n                          <CollapsibleTrigger className=\"flex w-full items-center justify-between\">\r\n                            <Button\r\n                              variant=\"destructive\"\r\n                              size=\"sm\"\r\n                              className=\"w-full justify-between gap-2 pr-1.5 group-data-[state=open]:rounded-b-none\"\r\n                              type=\"button\"\r\n                            >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <DangerTriangleSolid className=\"size-4 text-primary\" />\r\n                                <span>Error</span>\r\n                              </div>\r\n                              <Button\r\n                                type=\"button\"\r\n                                variant=\"ghost\"\r\n                                size=\"sm\"\r\n                                onClick={removeErrorDetails}\r\n                                className=\"size-6\"\r\n                              >\r\n                                <X className=\"size-3\" />\r\n                              </Button>\r\n                            </Button>\r\n                          </CollapsibleTrigger>\r\n                          <CollapsibleContent className=\"w-full\">\r\n                            <div className=\"flex flex-col gap-2 overflow-x-auto rounded-lg rounded-t-none border border-destructive/20 bg-destructive/5 p-3 py-1\">\r\n                              {rawErrors?.map((error) => (\r\n                                <Typography.P\r\n                                  key={error.message}\r\n                                  className=\"mt-0 font-mono text-sm text-destructive\"\r\n                                >\r\n                                  {error.message} on{\" \"}\r\n                                  {error.pageUrl === \"/\" ? \"index.tsx\" : error.pageUrl}\r\n                                </Typography.P>\r\n                              ))}\r\n                            </div>\r\n                          </CollapsibleContent>\r\n                        </Collapsible>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {(formNetworkErrors?.length ?? 0) > 0 && (\r\n                    <div className=\"flex flex-wrap gap-2 pb-2 transition-all duration-300\">\r\n                      <div className=\"relative w-full max-w-md\">\r\n                        <Collapsible className=\"group\">\r\n                          <CollapsibleTrigger className=\"flex w-full items-center justify-between\">\r\n                            <Button\r\n                              variant=\"destructive\"\r\n                              size=\"sm\"\r\n                              className=\"w-full justify-between gap-2 pr-1.5 group-data-[state=open]:rounded-b-none\"\r\n                              type=\"button\"\r\n                            >\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <DangerTriangleSolid className=\"size-4 text-primary\" />\r\n                                <span>Network Error</span>\r\n                              </div>\r\n                              <Button\r\n                                type=\"button\"\r\n                                variant=\"ghost\"\r\n                                size=\"sm\"\r\n                                onClick={removeNetworkErrors}\r\n                                className=\"size-6\"\r\n                              >\r\n                                <X className=\"size-3\" />\r\n                              </Button>\r\n                            </Button>\r\n                          </CollapsibleTrigger>\r\n                          <CollapsibleContent className=\"w-full\">\r\n                            <div className=\"flex flex-col gap-2 overflow-x-auto rounded-lg rounded-t-none border border-destructive/20 bg-destructive/5 p-3 py-1\">\r\n                              {formNetworkErrors?.map((error) => (\r\n                                <Typography.P\r\n                                  key={error.message}\r\n                                  className=\"mt-0 font-mono text-sm text-destructive\"\r\n                                >\r\n                                  {error.message} on {error.details?.url} with status{\" \"}\r\n                                  {error.details?.status} and method {error.details?.method}\r\n                                </Typography.P>\r\n                              ))}\r\n                            </div>\r\n                          </CollapsibleContent>\r\n                        </Collapsible>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {files.length > 0 && (\r\n                    <div className=\"flex flex-col gap-2 pb-2 pt-1 transition-all duration-300\">\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <span\r\n                          className={cn(\r\n                            \"text-xs\",\r\n                            selectedImages.length > MAX_IMAGES_PER_MESSAGE\r\n                              ? \"text-destructive\"\r\n                              : \"text-muted-foreground\",\r\n                          )}\r\n                        >\r\n                          {selectedImages.length} of {MAX_IMAGES_PER_MESSAGE} images\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex cursor-pointer flex-wrap gap-2\">\r\n                        {files.map((file, index) =>\r\n                          file.type.startsWith(\"image/\") ? (\r\n                            <PreviewImage key={`image-${index}`}>\r\n                              <PreviewImageTrigger asChild>\r\n                                <div className=\"relative\">\r\n                                  <div\r\n                                    className={cn(\r\n                                      \"h-16 w-16 cursor-pointer overflow-hidden rounded-md\",\r\n                                    )}\r\n                                  >\r\n                                    <img\r\n                                      src={URL.createObjectURL(file) || \"/placeholder.svg\"}\r\n                                      alt=\"User uploaded image\"\r\n                                      className=\"h-full w-full rounded-md border border-border object-cover\"\r\n                                      loading=\"lazy\"\r\n                                    />\r\n\r\n                                    <Button\r\n                                      type=\"button\"\r\n                                      size=\"icon\"\r\n                                      onClick={(e) => {\r\n                                        e.stopPropagation();\r\n                                        removeImage(file);\r\n                                      }}\r\n                                      className=\"absolute -right-1.5 -top-1.5 z-[100] h-5 w-5 rounded-full border border-border bg-background p-0 text-primary shadow-md hover:bg-secondary/90 hover:text-destructive\"\r\n                                    >\r\n                                      <X className=\"size-4\" />\r\n                                    </Button>\r\n                                  </div>\r\n                                </div>\r\n                              </PreviewImageTrigger>\r\n                              <PreviewImageContent\r\n                                fileContent={URL.createObjectURL(file)}\r\n                                file={URL.createObjectURL(file)}\r\n                                fileType=\"image\"\r\n                                fullscreen\r\n                              />\r\n                            </PreviewImage>\r\n                          ) : (\r\n                            <Preview key={`file-${index}`}>\r\n                              <PreviewTrigger asChild>\r\n                                <div className=\"relative\">\r\n                                  <div\r\n                                    className={cn(\r\n                                      \"flex h-fit w-fit items-center gap-2 rounded-xl border border-primary/10 p-1 text-sm font-medium leading-none shadow-sm transition-all duration-300\",\r\n                                      \"border border-primary/20 bg-accent/80 text-primary/80 hover:bg-accent/90 hover:text-primary dark:border-primary/15\",\r\n                                    )}\r\n                                    key={`user-mentioned-file-${index}`}\r\n                                  >\r\n                                    <div className=\"flex items-center gap-3\">\r\n                                      <div className=\"flex h-8 w-8 items-center justify-center rounded-md border border-border/60 bg-sidebar-primary/80 p-1 shadow-sm\">\r\n                                        <FileTextSolid className=\"h-full w-full text-background dark:text-primary/80\" />\r\n                                      </div>\r\n                                      <div className=\"flex flex-col gap-0 pr-4\">\r\n                                        <span className=\"truncate font-medium text-primary/90\">\r\n                                          {file.name.split(\"/\").pop()}\r\n                                        </span>\r\n                                        <span className=\"text-left text-xs font-normal text-muted-foreground\">\r\n                                          File\r\n                                        </span>\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                                  <Button\r\n                                    type=\"button\"\r\n                                    size=\"icon\"\r\n                                    onClick={(e) => {\r\n                                      e.stopPropagation();\r\n                                      removeFile(file);\r\n                                    }}\r\n                                    className=\"absolute -right-1.5 -top-1.5 z-[100] h-5 w-5 rounded-full bg-background p-0 shadow-md ring-1 ring-border/50 hover:bg-secondary/90 hover:text-destructive\"\r\n                                  >\r\n                                    <X className=\"size-3.5\" />\r\n                                  </Button>\r\n                                </div>\r\n                              </PreviewTrigger>\r\n                              <PreviewContent file={file} fileName={file.name} border />\r\n                            </Preview>\r\n                          ),\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {(selectedPaths || []).length > 0 && (\r\n                    <div className=\"flex cursor-pointer flex-wrap gap-2 pb-2 pt-1 transition-all duration-300\">\r\n                      {(selectedPaths || []).map((path, index) => (\r\n                        <FilePathPreview\r\n                          key={index}\r\n                          path={path}\r\n                          projectId={projectId}\r\n                          onRemove={(path) => {\r\n                            form.setValue(\r\n                              \"selectedPaths\",\r\n                              (selectedPaths || []).filter((p) => p !== path),\r\n                            );\r\n                          }}\r\n                        />\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"content\"\r\n                    render={({ field }) => (\r\n                      <FormItem dir=\"ltr\" className=\"relative mb-2\">\r\n                        <FormControl>\r\n                          <div className=\"relative\">\r\n                            <Textarea\r\n                              className={cn(\r\n                                \"border-default placeholder:text-placeholder relative m-0 box-border flex h-12 h-fit w-full resize-none overflow-y-auto rounded-md border-0 bg-background px-3 py-2 pl-2 pt-1.5 text-[0.9rem] outline-none ring-offset-background transition-all duration-200 focus-visible:outline-0 focus-visible:ring-0 focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50\",\r\n                              )}\r\n                              placeholder={\r\n                                \"Tell softgen what to do, @ to mention specific files to edit\"\r\n                              }\r\n                              minHeight={22}\r\n                              maxHeight={256}\r\n                              {...field}\r\n                              onChange={handleInput}\r\n                              onKeyDown={handleKeyDown}\r\n                              onPaste={handlePaste}\r\n                              onFocus={handleTextareaFocus}\r\n                              onBlur={handleTextareaBlur}\r\n                              onClick={handleTextareaClick}\r\n                              ref={(e) => {\r\n                                field.ref(e);\r\n                                textareaRef.current = e as unknown as HTMLTextAreaElement;\r\n                              }}\r\n                              aria-label=\"Message input\"\r\n                              aria-multiline=\"true\"\r\n                              role=\"textbox\"\r\n                            />\r\n\r\n                            {showFileSelector && (\r\n                              <div\r\n                                ref={dropdownRef}\r\n                                className=\"absolute bottom-8 left-2 z-[100] w-80 rounded-lg border border-border bg-background shadow-lg backdrop-blur-sm\"\r\n                              >\r\n                                <div className=\"flex flex-col\">\r\n                                  <div className=\"border-b border-border p-3 py-1\">\r\n                                    <Label>Code</Label>\r\n                                  </div>\r\n\r\n                                  <div className=\"max-h-64 overflow-y-auto\">\r\n                                    {filteredFiles.length > 0 ? (\r\n                                      filteredFiles.map((file: string, index: number) => {\r\n                                        const fileName = file.split(\"/\").pop();\r\n                                        const displayName =\r\n                                          fileName === \"index.tsx\"\r\n                                            ? `${file.split(\"/\").slice(0, -1).pop()}/index.tsx`\r\n                                            : fileName;\r\n\r\n                                        return (\r\n                                          <div\r\n                                            key={file}\r\n                                            ref={\r\n                                              index === selectedFileIndex ? selectedItemRef : null\r\n                                            }\r\n                                            data-file-index={index}\r\n                                            className={cn(\r\n                                              \"flex cursor-pointer items-center gap-3 p-2 text-sm transition-colors hover:bg-sidebar-ring/20\",\r\n                                              selectedFileIndex === index && \"bg-sidebar-ring/20\",\r\n                                            )}\r\n                                            onClick={() => insertFilePath(file)}\r\n                                          >\r\n                                            <div className=\"flex size-6 items-center justify-center rounded-lg border-[1px] border-border/60 bg-sidebar-primary/80 p-1\">\r\n                                              <FileTextSolid className=\"h-full w-full text-background/90\" />\r\n                                            </div>\r\n                                            <div className=\"flex w-full items-center justify-between gap-0.5 overflow-hidden\">\r\n                                              <span className=\"truncate font-medium text-foreground\">\r\n                                                {displayName}\r\n                                              </span>\r\n                                            </div>\r\n                                          </div>\r\n                                        );\r\n                                      })\r\n                                    ) : (\r\n                                      <div className=\"px-3 py-4 text-center text-sm text-muted-foreground\">\r\n                                        No files found\r\n                                      </div>\r\n                                    )}\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </FormControl>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <div className=\"flex items-center justify-between gap-2 pt-2\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Select\r\n                        name=\"selectedModel\"\r\n                        value={mode}\r\n                        onValueChange={(value: \"creative\" | \"standard\" | \"plan\") => {\r\n                          if (value) setMode(value);\r\n                        }}\r\n                      >\r\n                        <SelectTrigger className=\"h-0 w-fit gap-1.5 rounded-lg border-0 bg-transparent py-4 text-xs hover:bg-secondary/50 focus:ring-1 focus:ring-primary/20 [&>span]:flex [&>span]:items-center [&>span]:gap-2 [&>span_svg]:shrink-0 [&>span_svg]:text-muted-foreground/80\">\r\n                          <SelectValue>\r\n                            {mode === \"creative\" ? (\r\n                              <div className=\"flex items-center gap-1.5 text-muted-foreground\">\r\n                                <Sparkles className=\"h-3.5 w-3.5 fill-purple-500 stroke-primary\" />\r\n                                <span className=\"font-medium\">Creative</span>\r\n                              </div>\r\n                            ) : mode === \"standard\" ? (\r\n                              <div className=\"flex items-center gap-1.5 text-muted-foreground\">\r\n                                <BrainIcon className=\"h-3.5 w-3.5 fill-blue-500 stroke-primary\" />\r\n                                <span className=\"font-medium\">Standard</span>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"flex items-center gap-1.5 text-muted-foreground\">\r\n                                <Zap className=\"h-3.5 w-3.5 fill-amber-500 stroke-primary\" />\r\n                                <span className=\"font-medium\">Plan</span>\r\n                              </div>\r\n                            )}\r\n                          </SelectValue>\r\n                        </SelectTrigger>\r\n                        <SelectContent className=\"min-w-72 max-w-3xl [&_*[role=option]>span>svg]:shrink-0 [&_*[role=option]>span>svg]:text-muted-foreground/80 [&_*[role=option]>span]:end-2 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:flex [&_*[role=option]>span]:items-center [&_*[role=option]>span]:gap-2 [&_*[role=option]]:pe-8 [&_*[role=option]]:ps-2\">\r\n                          <SelectItem value=\"creative\" className=\"max-w-sm py-2 text-xs\">\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <Sparkles className=\"h-4 w-4 fill-purple-500 stroke-primary\" />\r\n                              <div className=\"flex flex-col gap-0.5\">\r\n                                <Label className=\"text-sm font-medium\">Creative</Label>\r\n                                <p className=\"text-xs text-muted-foreground\">\r\n                                  More creative, better for complex changes\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                          </SelectItem>\r\n                          <SelectItem value=\"standard\" className=\"py-2 text-xs\">\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <BrainIcon className=\"h-4 w-4 fill-blue-500 stroke-primary\" />\r\n                              <div className=\"flex flex-col gap-0.5\">\r\n                                <Label className=\"text-sm font-medium\">Standard</Label>\r\n                                <p className=\"text-xs text-muted-foreground\">\r\n                                  Better instruction following, more focused\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                          </SelectItem>\r\n                          <SelectItem value=\"plan\" className=\"py-2 text-xs\">\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <LightningSolid className=\"h-4 w-4 fill-amber-500 stroke-primary\" />\r\n                              <div className=\"flex flex-col gap-0.5\">\r\n                                <Label className=\"text-sm font-medium\">Plan</Label>\r\n                                <p className=\"text-xs text-muted-foreground\">\r\n                                  Best for planning and high-level tasks\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                          </SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n\r\n                      <Hint label={\"Attach files\"} side=\"top\">\r\n                        <div data-tooltip={\"Attach files\"}>\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            className={cn(\r\n                              \"flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg transition-colors duration-200\",\r\n                            )}\r\n                            onClick={() => {\r\n                              fileInputRef.current?.click();\r\n                            }}\r\n                            aria-label=\"Attach files\"\r\n                          >\r\n                            <input\r\n                              type=\"file\"\r\n                              multiple\r\n                              accept=\"image/*\"\r\n                              onChange={handleFileUpload}\r\n                              className=\"hidden\"\r\n                              id=\"file-upload\"\r\n                              ref={fileInputRef}\r\n                              aria-label=\"File upload input\"\r\n                            />\r\n                            <Paperclip className=\"text-primary\" />\r\n                          </Button>\r\n                        </div>\r\n                      </Hint>\r\n\r\n                      <Hint\r\n                        label={\r\n                          !browserSupportsSpeechRecognition\r\n                            ? \"Voice input not supported in this browser. Try Chrome for the best experience.\"\r\n                            : listening\r\n                              ? \"Stop listening\"\r\n                              : \"Start voice input\"\r\n                        }\r\n                        side=\"top\"\r\n                        className={cn(!browserSupportsSpeechRecognition && \"max-w-52\")}\r\n                      >\r\n                        <div data-tooltip={listening ? \"Stop listening\" : \"Start voice input\"}>\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant={\r\n                              !isMicrophoneAvailable || !browserSupportsSpeechRecognition\r\n                                ? \"destructive\"\r\n                                : \"ghost\"\r\n                            }\r\n                            size=\"icon\"\r\n                            className={cn(\r\n                              \"flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg transition-colors duration-200\",\r\n                              listening &&\r\n                                \"bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400\",\r\n                            )}\r\n                            onClick={listening ? stopListening : startListening}\r\n                            disabled={\r\n                              !browserSupportsSpeechRecognition ||\r\n                              !isMicrophoneAvailable ||\r\n                              isAgentRunning\r\n                            }\r\n                            aria-label={listening ? \"Stop voice input\" : \"Start voice input\"}\r\n                          >\r\n                            {!isMicrophoneAvailable || !browserSupportsSpeechRecognition ? (\r\n                              <MicrophoneSlashSolid className=\"text-primary\" />\r\n                            ) : listening ? (\r\n                              <MicrophoneSlashSolid className=\"text-destructive\" />\r\n                            ) : (\r\n                              <Mic className=\"text-primary\" />\r\n                            )}\r\n                          </Button>\r\n                        </div>\r\n                      </Hint>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Hint label=\"Settings\" side=\"top\">\r\n                        <div data-tooltip=\"Settings\">\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            className=\"flex size-8 cursor-pointer items-center justify-center rounded-lg\"\r\n                            onClick={() => setShowAgentSettings(true)}\r\n                          >\r\n                            <CogOneSolid className=\"text-primary\" />\r\n                          </Button>\r\n                        </div>\r\n                      </Hint>\r\n                      <div\r\n                        data-tooltip={\r\n                          isAgentRunning\r\n                            ? \"Stop AI\"\r\n                            : addMessageMutation.isPending\r\n                              ? \"Sending...\"\r\n                              : \"Send message (Ctrl+Enter)\"\r\n                        }\r\n                      >\r\n                        <Button\r\n                          type={isAgentRunning ? \"button\" : \"submit\"}\r\n                          size=\"icon\"\r\n                          className={cn(\r\n                            \"flex size-7 items-center justify-center transition-all duration-500\",\r\n                          )}\r\n                          disabled={addMessageMutation.isPending || isStoppingSession}\r\n                          onClick={isAgentRunning ? handleStopSession : undefined}\r\n                        >\r\n                          {isAgentRunning ? (\r\n                            isStoppingSession ? (\r\n                              <Loading className=\"size-6 animate-spin text-background\" />\r\n                            ) : (\r\n                              <Square\r\n                                className=\"size-4 rounded-md text-background\"\r\n                                strokeWidth={2.5}\r\n                              />\r\n                            )\r\n                          ) : (\r\n                            <ArrowUp className=\"size-4 text-background\" strokeWidth={3} />\r\n                          )}\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Microphone Access Required Message */}\r\n                  {!isMicrophoneAvailable && browserSupportsSpeechRecognition && (\r\n                    <div className=\"px-3 pb-1 pt-2\">\r\n                      <div className=\"rounded-lg border border-orange-200 bg-orange-50 p-2 dark:border-orange-800 dark:bg-orange-950/20\">\r\n                        <p className=\"text-xs text-orange-700 dark:text-orange-300\">\r\n                          🎤 Microphone access is required for voice input. Please allow microphone\r\n                          permissions.\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {listening && (\r\n                    <div className=\"my-1 flex w-full items-center justify-between rounded-lg border border-yellow-200 bg-yellow-50 px-3 py-1 dark:border-yellow-800 dark:bg-yellow-950/20\">\r\n                      <Typography.P className=\"mt-0 text-sm text-primary\">\r\n                        Listening\r\n                        <span className=\"jumping-dots\">\r\n                          <span className=\"dot-1\">.</span>\r\n                          <span className=\"dot-2\">.</span>\r\n                          <span className=\"dot-3\">.</span>\r\n                        </span>\r\n                      </Typography.P>\r\n\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <div className=\"h-2 w-2 animate-pulse rounded-full bg-red-500\"></div>\r\n                        <span className=\"text-xs font-medium text-blue-600 dark:text-blue-400\">\r\n                          LIVE\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </form>\r\n          </Form>\r\n        </div>\r\n      </div>\r\n      <div className=\"pointer-events-none absolute inset-0 -top-24 bottom-10 mx-2 bg-gradient-to-b from-transparent via-background/50 to-background to-100%\" />\r\n\r\n      {isMobile && isKeyboardVisible && (\r\n        <div className=\"fixed bottom-0 left-0 right-0 z-40 h-1 bg-gradient-to-r from-primary/20 via-primary/40 to-primary/20\" />\r\n      )}\r\n\r\n      <LazyModal\r\n        open={showAgentSettings}\r\n        onOpenChange={(open) => {\r\n          setShowAgentSettings(open);\r\n        }}\r\n      >\r\n        <AgentSettingModalContent\r\n          projectId={projectId}\r\n          setModal={(modal) => {\r\n            setShowAgentSettings(modal);\r\n          }}\r\n          setFormContent={setFormContent}\r\n          formContent={form.watch(\"content\") || \"\"}\r\n          availableFiles={availableFiles}\r\n        />\r\n      </LazyModal>\r\n\r\n      <LazyModal open={showFileUploadModal} onOpenChange={setShowFileUploadModal}>\r\n        <FileUploadModalContent\r\n          file={fileQueue[0]}\r\n          onConfirm={handleFileUploadConfirm}\r\n          uploadProgress={uploadProgress}\r\n          fileQueue={fileQueue}\r\n          totalAttachments={selectedImages.length}\r\n        />\r\n      </LazyModal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default MessageInput;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAKA;AACA;AACA;;;;;AA/DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA,MAAM,uCAAyB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AACpC,MAAM,yCAA2B,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;AAEtC,MAAM,gBAAgB,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,SAAS,mOAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG,2BACP,GAAG,CAAC,OAAO;IACd,eAAe,mOAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAY;KAAO,EAAE,OAAO,CAAC;IAChE,OAAO,mOAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mOAAA,CAAA,IAAC,CAAC,MAAM,IAAU,OAAO,CAAC,EAAE;IAC3C,eAAe,mOAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mOAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IAC7C,WAAW,mOAAA,CAAA,IAAC,CACT,KAAK,CACJ,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,IAAI,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACvB,WAAW,mOAAA,CAAA,IAAC,CAAC,KAAK,CAAC;YAAC,mOAAA,CAAA,IAAC,CAAC,MAAM;YAAI,mOAAA,CAAA,IAAC,CAAC,IAAI;SAAG,EAAE,QAAQ;QACnD,SAAS,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,MAAM,mOAAA,CAAA,IAAC,CAAC,MAAM;QACd,SAAS,mOAAA,CAAA,IAAC,CAAC,MAAM;QACjB,MAAM,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mOAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;QACzB,KAAK,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxB,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,IAED,QAAQ;IACX,eAAe,mOAAA,CAAA,IAAC,CACb,KAAK,CACJ,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,IAAI,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACvB,WAAW,mOAAA,CAAA,IAAC,CAAC,KAAK,CAAC;YAAC,mOAAA,CAAA,IAAC,CAAC,MAAM;YAAI,mOAAA,CAAA,IAAC,CAAC,IAAI;SAAG,EAAE,QAAQ;QACnD,SAAS,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,MAAM,mOAAA,CAAA,IAAC,CAAC,MAAM;QACd,SAAS,mOAAA,CAAA,IAAC,CAAC,MAAM;QACjB,MAAM,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mOAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;QACzB,KAAK,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxB,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,IAED,QAAQ;IACX,mBAAmB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACxC;AAIA,MAAM,kBAAkB,CAAC,EACvB,IAAI,EACJ,SAAS,EACT,QAAQ,EAKT;IACC,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QAC5C,UAAU;YAAC;YAAe;YAAW;SAAK;QAC1C,SAAS,IAAM,CAAA,GAAA,iHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;QACzC,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC;QAC1B,qBAAqB;YAAC;YAAQ;SAAY;IAC5C;IAEA,qBACE,6VAAC,mIAAA,CAAA,UAAO;;0BACN,6VAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;sCAGF,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAK,WAAU;0DACb,KAAK,KAAK,CAAC,KAAK,GAAG;;;;;;0DAEtB,6VAAC;gDAAK,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;sCAI5E,6VAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,SAAS;4BACX;4BACA,WAAU;sCAEV,cAAA,6VAAC,oSAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAKnB,6VAAC,mIAAA,CAAA,iBAAc;gBACb,aAAa,YAAY,uBAAuB,WAAW;gBAC3D,UAAU;gBACV,MAAM;;;;;;;;;;;;AAId;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,kBAAkB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,uBAAuB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAMhD,CAAC;IAEJ,2BAA2B;IAC3B,MAAM,yBAAyB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAE;IACtC,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,MAAM,eAAe,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,CAAC,QAAU,MAAM,YAAY;IAClE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IAC5E,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IAC3C,MAAM,YAAY;IAClB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,EACJ,WAAW,eAAe,EAC1B,WAAW,EACX,WAAW,EACX,mBAAmB,EACnB,aAAa,EACd,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IAEZ,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClE,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,gBAAgB,MAAM,cAAc;YACpC,qBAAqB,MAAM,mBAAmB;QAChD,CAAC;IAGH,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC/B,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,yBAAyB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACzC,MAAM,WAAW,YAAY,OAAO;QACpC,IAAI,CAAC,YAAY,CAAC,CAAC,oBAAoB,mBAAmB,GAAG;QAE7D,IAAI;YACF,IAAI,OAAO,SAAS,cAAc,KAAK,YAAY;gBACjD,SAAS,cAAc,CAAC;oBACtB,UAAU;oBACV,OAAO;oBACP,QAAQ;gBACV;YACF,OAAO;gBACL,sEAAsE;gBACtE,IAAI;oBACF,MAAM,OAAO,SAAS,qBAAqB;oBAC3C,IAAI,CAAC,MAAM;wBACT,6DAA6D;wBAC7D,OAAO,QAAQ,CAAC;4BACd,KAAK,SAAS,IAAI,CAAC,YAAY;4BAC/B,UAAU;wBACZ;wBACA;oBACF;oBAEA,MAAM,YAAY,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,SAAS;oBAC1E,MAAM,eAAe,KAAK,GAAG,GAAG,YAAY,OAAO,WAAW,GAAG;oBAEjE,OAAO,QAAQ,CAAC;wBACd,KAAK;wBACL,UAAU;oBACZ;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,8DAA8D;oBAC9D,OAAO,QAAQ,CAAC;wBACd,KAAK,SAAS,IAAI,CAAC,YAAY;wBAC/B,UAAU;oBACZ;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,iBAAiB;YACjB,OAAO,QAAQ,CAAC;gBACd,KAAK,SAAS,IAAI,CAAC,YAAY;gBAC/B,UAAU;YACZ;QACF;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,KAAK,QAAQ,CAAC,WAAW;IAC3B;IAEA,MAAM,sBAAsB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACtC,qBAAqB;QACrB,IAAI,YAAY,YAAY,OAAO,EAAE;YACnC,WAAW;gBACT,MAAM,WAAW,YAAY,OAAO;gBACpC,IAAI,CAAC,YAAY,CAAC,CAAC,oBAAoB,mBAAmB,GAAG;gBAE7D,IAAI;oBACF,MAAM,OAAO,SAAS,qBAAqB;oBAC3C,IAAI,CAAC,MAAM;oBAEX,MAAM,eAAe,OAAO,WAAW;oBACvC,MAAM,iBAAiB,eAAe;oBACtC,MAAM,iBAAiB,SACrB,iBAAiB,SAAS,eAAe,EAAE,gBAAgB,CAAC,YAAY;oBAG1E,IAAI,KAAK,MAAM,GAAG,eAAe,iBAAiB,gBAAgB;wBAChE;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;gBACjD;YACF,GAAG;QACL;IACF,GAAG;QAAC;QAAU;KAAuB;IAErC,MAAM,sBAAsB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,UAAU;YACZ,WAAW;gBACT;YACF,GAAG;QACL;IACF,GAAG;QAAC;QAAU;KAAuB;IAErC,MAAM,qBAAqB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACrC,qBAAqB;QACrB,qBAAqB;QACrB,IAAI,UAAU;YACZ,WAAW;gBACT,OAAO,QAAQ,CAAC;oBACd,KAAK;oBACL,UAAU;gBACZ;YACF,GAAG;QACL;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,CAAC,UAAY,UAAU;IACvC,GAAG;QAAC,UAAU,eAAe;QAAE,UAAU,oBAAoB;KAAC;IAE9D,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,SAAS,gBAAgB;YACzB,OAAO,EAAE;YACT,eAAe,EAAE;QACnB;IACF;IAEA,MAAM,cAAc,eAAe,UAAU,WAAW;IAExD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,OAAO,IAAI,aAAa;YACpE,QAAQ;QACV;IACF,GAAG;QAAC;QAAa;KAAgB;IAEjC,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE,CAAC;QACxB,IAAI,SAAS;YACX,KAAK,QAAQ,CAAC,WAAW;YACzB,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,OAAO;YACL,QAAQ,KAAK,CAAC;YACd;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,KAAK,QAAQ,CAAC;IAChB,GAAG,EAAE;IAEL,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,IAAI,CAAC,UAAU;gBACb,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,OAAO,GACV;gBACF,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;YACA,IAAI,gBAAgB,OAAO,WAAW;YACtC,IAAI;YACJ,MAAM,iBAAiB,OAAO,cAAc;YAE5C,MAAM,eAAe;gBACnB,aAAa;gBACb,gBAAgB,WAAW;oBACzB,MAAM,gBAAgB,OAAO,WAAW;oBACxC,MAAM,mBAAmB,gBAAgB;oBAEzC,IAAI,mBAAmB,KAAK;wBAC1B,qBAAqB;wBACrB,IAAI,mBAAmB;4BACrB,WAAW;gCACT;4BACF,GAAG;wBACL;oBACF,OAAO;wBACL,qBAAqB;oBACvB;oBAEA,gBAAgB;gBAClB,GAAG;YACL;YAEA,MAAM,6BAA6B;gBACjC,IAAI,gBAAgB;oBAClB,MAAM,mBAAmB,gBAAgB,eAAe,MAAM;oBAE9D,IAAI,mBAAmB,KAAK;wBAC1B,qBAAqB;wBACrB,IAAI,mBAAmB;4BACrB,WAAW;gCACT;4BACF,GAAG;wBACL;oBACF,OAAO;wBACL,qBAAqB;oBACvB;gBACF;YACF;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC,IAAI,gBAAgB;gBAClB,eAAe,gBAAgB,CAAC,UAAU;YAC5C;YAEA,OAAO;gBACL,OAAO,mBAAmB,CAAC,UAAU;gBACrC,IAAI,gBAAgB;oBAClB,eAAe,mBAAmB,CAAC,UAAU;gBAC/C;gBACA,aAAa;YACf;QACF;IACF,GAAG;QAAC;QAAU;KAAkB;IAEhC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,MAAM,UAAU,KAAK,KAAK,CAAC;IAC3B,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,MAAM,oBAAoB,KAAK,KAAK,CAAC;IAErC,MAAM,qBAAqB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACrC,YAAY,OAAO,EAAE,OAAO,EAAE,aAAa,EAAmB;YAC5D,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,iCAAiC;YAEvC,IAAI,qBAAqB;gBACvB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;gBAC/B,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc,eAAe,MAAM,CACvC,CAAC,OAAS,gBAAgB,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;YAGzD,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,MAAM;YAErC,KAAK,MAAM,SAAS,YAAa;gBAC/B,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD,EAAE;YAChB;YAEA,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAC;gBACxC,OAAO,CAAC,cAAc,EAAE,MAAM;YAChC;YAEA,MAAM,oBAAoB,wBAAwB;YAClD,MAAM,eACJ,cAAc,MAAM,GAAG,IACnB,eAAe,IAAI,CAAC,OAAO,MAAM,oBACjC;YAEN,IAAI;gBACF,MAAM,YAAY;oBAChB,SAAS;oBACT,SAAS;wBACP,QAAQ;wBACR;wBACA,OAAO;oBACT;gBACF;gBACA,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAgB;gBACvB,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;gBACX,MAAM;YACR;QACF;QACA,WAAW;YACT,KAAK,QAAQ,CAAC,WAAW;YACzB,KAAK,QAAQ,CAAC,SAAS,EAAE;YACzB,KAAK,QAAQ,CAAC,iBAAiB,EAAE;YACjC,KAAK,QAAQ,CAAC,qBAAqB;YACnC,KAAK,QAAQ,CAAC,aAAa,EAAE;YAC7B,KAAK,QAAQ,CAAC,iBAAiB,EAAE;YACjC,kBAAkB,EAAE;YACpB,iBAAiB,EAAE;YACnB,gBAAgB;YAChB,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;QACf;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACtD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,IAAI;gBACF,aAAa;gBACb,uBAAuB;YACzB,EAAE,OAAO,OAAO;gBACd,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACpD,EAAE,MAAM,CAAC,KAAK,GAAG;YACnB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;QAEhE,MAAM,eAAe,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QACpD,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,MAAM,aAAa,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,CAAC,UAAU,CAAC;YAEtE,IAAI;gBACF,IAAI,WAAW,MAAM,GAAG,GAAG;oBACzB,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,MAAM,EAAE,WAAW,MAAM;gBAC7D;gBAEA,aAAa;gBACb,uBAAuB;YACzB,EAAE,OAAO,OAAO;gBACd,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACtD;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,0BAA0B,OAAO,OAAe;QACpD,IAAI;YACF,IAAI;YAEJ,IAAI,WAAW,YAAY,WAAW,QAAQ;gBAC5C,2CAA2C;gBAC3C,MAAM,OAAO,CAAC,CAAC;oBACb,kBAAkB,CAAC,OAAS,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,KAAK,IAAI,CAAC,EAAE;gCACX,QAAQ;gCACR,UAAU;gCACV,UAAU,KAAK,IAAI;gCACnB,UAAU,KAAK,IAAI;gCACnB,UAAU,KAAK,IAAI;4BACrB;wBACF,CAAC;gBACH;gBAEA,4BAA4B;gBAC5B,mBAAmB,YAAY;oBAC7B,kBAAkB,CAAC;wBACjB,MAAM,cAAc;4BAAE,GAAG,IAAI;wBAAC;wBAC9B,IAAI,mBAAmB;wBAEvB,MAAM,OAAO,CAAC,CAAC;4BACb,MAAM,kBAAkB,IAAI,CAAC,KAAK,IAAI,CAAC;4BACvC,IACE,mBACA,gBAAgB,MAAM,KAAK,eAC3B,gBAAgB,QAAQ,GAAG,IAC3B;gCACA,WAAW,CAAC,KAAK,IAAI,CAAC,GAAG;oCACvB,GAAG,eAAe;oCAClB,UAAU,KAAK,GAAG,CAAC,gBAAgB,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI;gCACpE;gCACA,mBAAmB;4BACrB;wBACF;wBAEA,sCAAsC;wBACtC,IAAI,CAAC,oBAAoB,kBAAkB;4BACzC,cAAc;wBAChB;wBAEA,OAAO;oBACT;gBACF,GAAG;YACL;YAEA,IAAI,WAAW,YAAY,WAAW,QAAQ;gBAC5C,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,CAAC,UAAU,CAAC;gBAC/D,IAAI,WAAW,MAAM,GAAG,GAAG;oBACzB,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,MAAM,EAAE,WAAW,MAAM;gBAC7D;gBAEA,MAAM,OAAO,CAAC,CAAC;oBACb,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;wBAClC,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD,EAAE;wBACd,kBAAkB,CAAC,OAAS;mCAAI;gCAAM;6BAAK;oBAC7C,OAAO;wBACL,iBAAiB,CAAC,OAAS;mCAAI;gCAAM;6BAAK;oBAC5C;gBACF;gBAEA,IAAI,WAAW,UAAU;oBACvB,MAAM,OAAO,CAAC,CAAC;wBACb,6BAA6B,MAAM;oBACrC;oBAEA,sCAAsC;oBACtC,aAAa,CAAC;wBACZ,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,MAAM,QAAQ,CAAC;wBAC1D,IAAI,eAAe,MAAM,KAAK,GAAG;4BAC/B,uBAAuB;wBACzB;wBACA,OAAO;oBACT;oBACA;gBACF;YACF;YAEA,IAAI,WAAW,YAAY,WAAW,QAAQ;gBAC5C,IAAI;oBACF,0DAA0D;oBAC1D,KAAK,MAAM,QAAQ,MAAO;wBACxB,MAAM,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;wBAEzC,0CAA0C;wBAC1C,kBAAkB,CAAC,OAAS,CAAC;gCAC3B,GAAG,IAAI;gCACP,CAAC,KAAK,IAAI,CAAC,EAAE;oCACX,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;oCAClB,QAAQ;oCACR,UAAU;gCACZ;4BACF,CAAC;wBAED,MAAM,CAAA,GAAA,iHAAA,CAAA,aAAU,AAAD,EAAE;4BACf;4BACA;4BACA,cAAc;4BACd,YAAY,CAAC;gCACX,kBAAkB,CAAC,OAAS,CAAC;wCAC3B,GAAG,IAAI;wCACP,CAAC,KAAK,IAAI,CAAC,EAAE;4CACX,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;4CAClB,UAAU,KAAK,GAAG,CAAC,UAAU;wCAC/B;oCACF,CAAC;4BACH;wBACF;wBAEA,2CAA2C;wBAC3C,kBAAkB,CAAC,OAAS,CAAC;gCAC3B,GAAG,IAAI;gCACP,CAAC,KAAK,IAAI,CAAC,EAAE;oCACX,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;oCAClB,QAAQ;oCACR,UAAU;gCACZ;4BACF,CAAC;wBAED,6DAA6D;wBAC7D,aAAa,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,KAAK,IAAI;wBAE9D,6BAA6B,MAAM,UAAU;wBAE7C,kEAAkE;wBAClE,WAAW;4BACT,kBAAkB,CAAC;gCACjB,MAAM,cAAc;oCAAE,GAAG,IAAI;gCAAC;gCAC9B,OAAO,WAAW,CAAC,KAAK,IAAI,CAAC;gCAC7B,OAAO;4BACT;wBACF,GAAG;oBACL;oBAEA,IAAI,kBAAkB;wBACpB,cAAc;oBAChB;oBAEA,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAC9D,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;oBAEX,IAAI,kBAAkB;wBACpB,cAAc;oBAChB;oBAEA,MAAM,OAAO,CAAC,CAAC;wBACb,kBAAkB,CAAC,OAAS,CAAC;gCAC3B,GAAG,IAAI;gCACP,CAAC,KAAK,IAAI,CAAC,EAAE;oCACX,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;oCAClB,QAAQ;oCACR,UAAU;oCACV,OAAO;gCACT;4BACF,CAAC;oBACH;oBAEA,IAAI,WAAW,QAAQ;wBACrB,MAAM,OAAO,CAAC,CAAC;4BACb,6BAA6B,MAAM,UAAU,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;wBACrE;oBACF,OAAO;wBACL;oBACF;gBACF;YACF;YAEA,kCAAkC;YAClC,aAAa,CAAC;gBACZ,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,MAAM,QAAQ,CAAC;gBAC1D,IAAI,eAAe,MAAM,KAAK,GAAG;oBAC/B,uBAAuB;gBACzB;gBACA,OAAO;YACT;YAEA,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YAEX,IAAI,WAAW,UAAU;gBACvB,MAAM,OAAO,CAAC,CAAC;oBACb,kBAAkB,CAAC,OAAS,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,KAAK,IAAI,CAAC,EAAE;gCACX,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;gCAClB,QAAQ;gCACR,UAAU;gCACV,OAAO;4BACT;wBACF,CAAC;gBACH;YACF;QACF;IACF;IAEA,MAAM,+BAA+B,CACnC,MACA,MACA;QAEA,MAAM,iBAAiB,KAAK,SAAS,CAAC;QACtC,IAAI,oBAAoB;QAExB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAClC,IAAI,SAAS,YAAY,YAAY;gBACnC,oBAAoB,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,WAAW,GAAG,CAAC;YAC1D,OAAO;gBACL;YACF;QACF,OAAO;YACL,IAAI,SAAS,YAAY,YAAY;gBACnC,oBAAoB,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,WAAW,GAAG,CAAC;YACzD,OAAO;gBACL,oBAAoB,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;YACzD;QACF;QAEA,KAAK,QAAQ,CACX,WACA,eAAe,IAAI,GAAG,SAAS,GAAG,OAAO,KAAK,kBAAkB,IAAI,GAAG,SAAS,GAAG,OAAO;IAE9F;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,aAAa,CAAC,KAAK;QAC9C,MAAM,aAAa,MAAM,MAAM,CAC7B,CAAC,OAAS,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;QAGzD,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B;QACF;QAEA,EAAE,cAAc;QAEhB,IAAI;YACF,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,MAAM,EAAE,WAAW,MAAM;YAE3D,WAAW,OAAO,CAAC,CAAC;gBAClB,MAAM,OAAO,KAAK,SAAS;gBAC3B,IAAI,CAAC,MAAM;gBAEX,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD,EAAE;gBAEd,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,CAAC;gBAChD,MAAM,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI,EAAE,KAAK,IAAI;gBAC/C,MAAM,cAAc,IAAI,KAAK;oBAAC;iBAAK,EAAE,UAAU;oBAAE,MAAM,KAAK,IAAI;gBAAC;gBAEjE,kBAAkB,CAAC,OAAS;2BAAI;wBAAM;qBAAY;YACpD;QACF,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACtD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;IACtD;IAEA,MAAM,cAAc,CAAC;QACnB,kBAAkB,CAAC;YACjB,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,QAAQ;WAAI;WAAmB;KAAc;IAEnD,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,SAAS,gBAAgB,EAAE,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACxE,UAAU;YAAC;YAAqB;SAAU;QAC1C,SAAS;YACP,MAAM,SAAS,MAAM,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,SAAS;YACtD,OAAO,QAAQ,SAAS,EAAE;QAC5B;QACA,SAAS,CAAC,CAAC;QACX,WAAW,OAAO;IACpB;IAEA,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD,EAAE,CAAC,gBAAgB;QAC1C,IAAI,CAAC,kBAAkB,YAAY;YACjC;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,GAAG;QACtD,MAAM,cAAc,iBAAiB,WAAW,CAAC;QACjD,MAAM,kBAAkB,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC;QAElD,MAAM,aAAa,iBAAiB,SAAS,CAAC,GAAG,eAAe;QAChE,KAAK,QAAQ,CAAC,WAAW;QACzB,KAAK,QAAQ,CAAC,iBAAiB;eAAK,iBAAiB,EAAE;YAAG;SAAK;QAC/D,oBAAoB;QACpB,mBAAmB;QACnB,KAAK,QAAQ,CAAC;IAChB;IAEA,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAEhC,MAAM,mBAAmB,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,GAAG;QACtD,MAAM,cAAc,iBAAiB,WAAW,CAAC;QACjD,MAAM,aAAa,mBAAmB,iBAAiB,SAAS,CAAC,cAAc,GAAG,WAAW;QAE7F,OAAO,eAAe,MAAM,CAC1B,CAAC,OACC,KAAK,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClD,CAAC,CAAC,iBAAiB,EAAE,EAAE,QAAQ,CAAC;IAEtC,GAAG;QAAC;QAAS;QAAgB;QAAgB;QAAe;QAAkB;KAAgB;IAE9F,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB;YACpB,qBAAqB;YACrB,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,IAAI,kBAAkB;gBACpB,EAAE,cAAc;gBAChB,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,eAAe,aAAa,CAAC,kBAAkB;gBACjD;gBACA;YACF;YAEA,EAAE,cAAc;YAChB,KAAK,YAAY,CAAC,CAAC,OAAS,mBAAmB,MAAM,CAAC;YACtD;QACF;QAEA,IAAI,CAAC,kBAAkB;QAEvB,IAAI,EAAE,GAAG,KAAK,eAAe,EAAE,GAAG,KAAK,WAAW;YAChD,EAAE,cAAc;YAEhB,MAAM,WACJ,EAAE,GAAG,KAAK,cACN,oBAAoB,cAAc,MAAM,GAAG,IACzC,oBAAoB,IACpB,oBACF,oBAAoB,IAClB,oBAAoB,IACpB;YAER,qBAAqB;YAErB,WAAW;gBACT,MAAM,eAAe,SAAS,aAAa,CAAC,CAAC,kBAAkB,EAAE,SAAS,EAAE,CAAC;gBAC7E,cAAc,eAAe;oBAAE,OAAO;oBAAW,UAAU;gBAAS;YACtE,GAAG;QACL,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,EAAE,cAAc;YAChB,oBAAoB;YACpB,mBAAmB;QACrB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,MAAM,YAAY,EAAE,MAAM,CAAC,cAAc;QAEzC,iEAAiE;QACjE,IAAI,eAAe,OAAO,IAAI,WAAW;YACvC;QACF;QAEA,mEAAmE;QACnE,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,KAAK,QAAQ,CAAC,WAAW;QAC3B;QACA,kBAAkB;QAElB,MAAM,mBAAmB,MAAM,SAAS,CAAC,GAAG;QAC5C,MAAM,cAAc,iBAAiB,WAAW,CAAC;QAEjD,IAAI,gBAAgB,CAAC,GAAG;YACtB,MAAM,aAAa,iBAAiB,SAAS,CAAC,cAAc;YAC5D,MAAM,kBAAkB,KAAK,IAAI,CAAC;YAElC,IAAI,CAAC,iBAAiB;gBACpB,oBAAoB;gBACpB,qBAAqB;gBACrB;YACF;QACF;QAEA,oBAAoB;QACpB,mBAAmB;IACrB;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,qBAAqB;YACrB,MAAM,SAAS,MAAM;YACrB,IAAI,QAAQ;gBACV,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;YACf;YACA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC9D,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QACrF,OAAO;YAAE;YAAc;YAAe;QAAe;IACvD,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,CAAC,KAAK,SAAS,CAAC,cAAc,QAAQ;YACxD,KAAK,QAAQ,CAAC,aAAa;QAC7B;QAEA,IAAI,iBAAiB,CAAC,KAAK,SAAS,CAAC,kBAAkB,QAAQ;YAC7D,KAAK,QAAQ,CAAC,iBAAiB;QACjC;QAEA,IAAI,kBAAkB,CAAC,gBAAgB,aAAa,GAAG;YACrD,MAAM,iBAAiB,KAAK,SAAS,CAAC;YACtC,IAAI,mBAAmB,gBAAgB;gBACrC,KAAK,QAAQ,CAAC,WAAW;YAC3B;QACF;IACF,GAAG;QAAC;QAAc;QAAe;KAAe;IAEhD,MAAM,qBAAqB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACrC,KAAK,QAAQ,CAAC,aAAa,EAAE;QAC7B,KAAK,QAAQ,CAAC,qBAAqB;IACrC,GAAG;QAAC;KAAK;IAET,MAAM,sBAAsB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACtC,KAAK,QAAQ,CAAC,iBAAiB,EAAE;QACjC,KAAK,QAAQ,CAAC,qBAAqB;IACrC,GAAG;QAAC;KAAK;IAET,MAAM,0BAA0B,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EACxC,CAAC;QACC,IAAI,eAAe;QAEnB,MAAM,YAAY,KAAK,SAAS,CAAC;QACjC,MAAM,gBAAgB,KAAK,SAAS,CAAC;QAErC,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;YACrC,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,KAAK,SAAS,CAAC,WAAW,GAAG,CAAC;YAC/E,eAAe,eAAe,MAAM;QACtC;QAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;YAC7C,MAAM,mBAAmB,CAAC,uBAAuB,EAAE,KAAK,SAAS,CAAC,eAAe,GAAG,CAAC;YACrF,eAAe,eAAe,MAAM;QACtC;QAEA,OAAO;IACT,GACA;QAAC;KAAK;IAGR,0BAA0B;IAC1B,MAAM,EACJ,UAAU,EACV,SAAS,EACT,eAAe,EACf,gCAAgC,EAChC,qBAAqB,EACtB,GAAG,CAAA,GAAA,gQAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB,UAAU;YACR;gBACE,SAAS;gBACT,UAAU;oBACR,MAAM,iBAAiB,KAAK,SAAS,CAAC;oBACtC,IAAI,eAAe,IAAI,IAAI;wBACzB,4CAA4C;wBAC5C;wBACA,KAAK,YAAY,CAAC,CAAC,OAAS,mBAAmB,MAAM,CAAC;oBACxD;gBACF;gBACA,cAAc;YAChB;YACA;gBACE,SAAS;gBACT,UAAU;oBACR,KAAK,QAAQ,CAAC,WAAW;oBACzB;gBACF;gBACA,cAAc;YAChB;SACD;IACH;IAEA,mDAAmD;IACnD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,cAAc,eAAe,OAAO,EAAE;YACrD,MAAM,YAAY,uBAAuB,OAAO,IAAI,aAAa,MAAM;YACvE,MAAM,aAAa,uBAAuB,OAAO,GAAG,YAAY;YAChE,KAAK,QAAQ,CAAC,WAAW;QAC3B;IACF,GAAG;QAAC;QAAY;QAAW;KAAK;IAEhC,6DAA6D;IAC7D,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe,OAAO,GAAG;IAC3B,GAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,kCAAkC;YACrC,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,oCAAoC;gBAC7C,aAAa;YACf;YACA;QACF;QAEA,IAAI,CAAC,uBAAuB;YAC1B,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,8BAA8B;gBACvC,aAAa;YACf;YACA;QACF;QAEA,IAAI;YACF,4CAA4C;YAC5C,uBAAuB,OAAO,GAAG,KAAK,SAAS,CAAC;YAChD,eAAe,OAAO,GAAG;YACzB;YACA,gQAAA,CAAA,UAAiB,CAAC,cAAc,CAAC;gBAC/B,YAAY;gBACZ,UAAU;YACZ;YACA,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;gBAC3B,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,eAAe,OAAO,GAAG;YACzB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;gBAAE,aAAa;YAAsC;QAC3E;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,gQAAA,CAAA,UAAiB,CAAC,aAAa;QAC/B,uDAAuD;QACvD,gDAAgD;QAClD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,qBACE;;0BACE,6VAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sHACA,WAAW,SAAS,QACpB,YAAY,qBAAqB,QACjC,YAAY,qBAAqB,QACjC,YAAY;gBAEd,OACE,WACI;oBACE,eAAe,CAAC,wCAAwC,CAAC;gBAC3D,IACA;;kCAGN,6VAAC,mVAAA,CAAA,kBAAe;kCACd,cAAA,6VAAC,oVAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC1B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6VAAC;gCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,sBAAsB,mBAAmB;0CAG3C,cAAA,6VAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA,uBAAuB;8CAGxB,oCACC,6VAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAA0B;;;;;6DAI3C,6VAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;;4CACd,UAAU,eAAe;4CAAC;;;;;;;;;;;;;;;;;2BAzB9B;;;;;;;;;;kCAiCT,6VAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kDACA,WAAW,SAAS,QACpB,YAAY;kCAGd,cAAA,6VAAC,gIAAA,CAAA,OAAI;4BAAE,GAAG,IAAI;sCACZ,cAAA,6VAAC;gCACC,UAAU,KAAK,YAAY,CAAC,CAAC;oCAC3B,IACE,CAAC,KAAK,UAAU,EAAE,kBAClB,KAAK,UAAU,EAAE,qBAAqB,aACtC,KAAK,UAAU,CAAC,gBAAgB,IAAI,KACpC,KAAK,UAAU,EAAE,qBAAqB,MACtC;wCACA,oBAAoB;wCACpB;oCACF;oCAEA,mBAAmB,MAAM,CAAC;gCAC5B;gCACA,WAAU;0CAEV,cAAA,6VAAC;oCACC,KAAK;oCACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0HACA,cAAc,2CACd,uBAAuB;oCAEzB,YAAY;oCACZ,aAAa;oCACb,QAAQ;;wCAEP,4BACC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,8SAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6VAAC;oDAAE,WAAU;8DAAsC;;;;;;;;;;;;sDAGvD,6VAAC;4CAAI,WAAU;;gDACZ,CAAC,WAAW,UAAU,CAAC,IAAI,mBAC1B,6VAAC;oDAAI,WAAU;8DACb,cAAA,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC,uIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6VAAC,uIAAA,CAAA,qBAAkB;oEAAC,WAAU;8EAC5B,cAAA,6VAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,MAAK;;0FAEL,6VAAC;gFAAI,WAAU;;kGACb,6VAAC,iVAAA,CAAA,sBAAmB;wFAAC,WAAU;;;;;;kGAC/B,6VAAC;kGAAK;;;;;;;;;;;;0FAER,6VAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS;gFACT,WAAU;0FAEV,cAAA,6VAAC,oSAAA,CAAA,IAAC;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8EAInB,6VAAC,uIAAA,CAAA,qBAAkB;oEAAC,WAAU;8EAC5B,cAAA,6VAAC;wEAAI,WAAU;kFACZ,WAAW,IAAI,CAAC,sBACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gFAEX,WAAU;;oFAET,MAAM,OAAO;oFAAC;oFAAI;oFAClB,MAAM,OAAO,KAAK,MAAM,cAAc,MAAM,OAAO;;+EAJ/C,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAcjC,CAAC,mBAAmB,UAAU,CAAC,IAAI,mBAClC,6VAAC;oDAAI,WAAU;8DACb,cAAA,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC,uIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6VAAC,uIAAA,CAAA,qBAAkB;oEAAC,WAAU;8EAC5B,cAAA,6VAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,MAAK;;0FAEL,6VAAC;gFAAI,WAAU;;kGACb,6VAAC,iVAAA,CAAA,sBAAmB;wFAAC,WAAU;;;;;;kGAC/B,6VAAC;kGAAK;;;;;;;;;;;;0FAER,6VAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS;gFACT,WAAU;0FAEV,cAAA,6VAAC,oSAAA,CAAA,IAAC;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8EAInB,6VAAC,uIAAA,CAAA,qBAAkB;oEAAC,WAAU;8EAC5B,cAAA,6VAAC;wEAAI,WAAU;kFACZ,mBAAmB,IAAI,CAAC,sBACvB,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gFAEX,WAAU;;oFAET,MAAM,OAAO;oFAAC;oFAAK,MAAM,OAAO,EAAE;oFAAI;oFAAa;oFACnD,MAAM,OAAO,EAAE;oFAAO;oFAAa,MAAM,OAAO,EAAE;;+EAJ9C,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAcjC,MAAM,MAAM,GAAG,mBACd,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAI,WAAU;sEACb,cAAA,6VAAC;gEACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WACA,eAAe,MAAM,GAAG,uIAAA,CAAA,yBAAsB,GAC1C,qBACA;;oEAGL,eAAe,MAAM;oEAAC;oEAAK,uIAAA,CAAA,yBAAsB;oEAAC;;;;;;;;;;;;sEAGvD,6VAAC;4DAAI,WAAU;sEACZ,MAAM,GAAG,CAAC,CAAC,MAAM,QAChB,KAAK,IAAI,CAAC,UAAU,CAAC,0BACnB,6VAAC,4IAAA,CAAA,eAAY;;sFACX,6VAAC,4IAAA,CAAA,sBAAmB;4EAAC,OAAO;sFAC1B,cAAA,6VAAC;gFAAI,WAAU;0FACb,cAAA,6VAAC;oFACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;sGAGF,6VAAC;4FACC,KAAK,IAAI,eAAe,CAAC,SAAS;4FAClC,KAAI;4FACJ,WAAU;4FACV,SAAQ;;;;;;sGAGV,6VAAC,kIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,MAAK;4FACL,SAAS,CAAC;gGACR,EAAE,eAAe;gGACjB,YAAY;4FACd;4FACA,WAAU;sGAEV,cAAA,6VAAC,oSAAA,CAAA,IAAC;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sFAKrB,6VAAC,4IAAA,CAAA,sBAAmB;4EAClB,aAAa,IAAI,eAAe,CAAC;4EACjC,MAAM,IAAI,eAAe,CAAC;4EAC1B,UAAS;4EACT,UAAU;;;;;;;mEAjCK,CAAC,MAAM,EAAE,OAAO;;;;yFAqCnC,6VAAC,mIAAA,CAAA,UAAO;;sFACN,6VAAC,mIAAA,CAAA,iBAAc;4EAAC,OAAO;sFACrB,cAAA,6VAAC;gFAAI,WAAU;;kGACb,6VAAC;wFACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;kGAIF,cAAA,6VAAC;4FAAI,WAAU;;8GACb,6VAAC;oGAAI,WAAU;8GACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wGAAC,WAAU;;;;;;;;;;;8GAE3B,6VAAC;oGAAI,WAAU;;sHACb,6VAAC;4GAAK,WAAU;sHACb,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;;;;;;sHAE3B,6VAAC;4GAAK,WAAU;sHAAsD;;;;;;;;;;;;;;;;;;uFAVrE,CAAC,oBAAoB,EAAE,OAAO;;;;;kGAgBrC,6VAAC,kIAAA,CAAA,SAAM;wFACL,MAAK;wFACL,MAAK;wFACL,SAAS,CAAC;4FACR,EAAE,eAAe;4FACjB,WAAW;wFACb;wFACA,WAAU;kGAEV,cAAA,6VAAC,oSAAA,CAAA,IAAC;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sFAInB,6VAAC,mIAAA,CAAA,iBAAc;4EAAC,MAAM;4EAAM,UAAU,KAAK,IAAI;4EAAE,MAAM;;;;;;;mEArC3C,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;gDA6CtC,CAAC,iBAAiB,EAAE,EAAE,MAAM,GAAG,mBAC9B,6VAAC;oDAAI,WAAU;8DACZ,CAAC,iBAAiB,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,sBAChC,6VAAC;4DAEC,MAAM;4DACN,WAAW;4DACX,UAAU,CAAC;gEACT,KAAK,QAAQ,CACX,iBACA,CAAC,iBAAiB,EAAE,EAAE,MAAM,CAAC,CAAC,IAAM,MAAM;4DAE9C;2DARK;;;;;;;;;;8DAcb,6VAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;4DAAC,KAAI;4DAAM,WAAU;sEAC5B,cAAA,6VAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,6VAAC;oEAAI,WAAU;;sFACb,6VAAC,oIAAA,CAAA,WAAQ;4EACP,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;4EAEF,aACE;4EAEF,WAAW;4EACX,WAAW;4EACV,GAAG,KAAK;4EACT,UAAU;4EACV,WAAW;4EACX,SAAS;4EACT,SAAS;4EACT,QAAQ;4EACR,SAAS;4EACT,KAAK,CAAC;gFACJ,MAAM,GAAG,CAAC;gFACV,YAAY,OAAO,GAAG;4EACxB;4EACA,cAAW;4EACX,kBAAe;4EACf,MAAK;;;;;;wEAGN,kCACC,6VAAC;4EACC,KAAK;4EACL,WAAU;sFAEV,cAAA,6VAAC;gFAAI,WAAU;;kGACb,6VAAC;wFAAI,WAAU;kGACb,cAAA,6VAAC,iIAAA,CAAA,QAAK;sGAAC;;;;;;;;;;;kGAGT,6VAAC;wFAAI,WAAU;kGACZ,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,MAAc;4FAC/B,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG;4FACpC,MAAM,cACJ,aAAa,cACT,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,GACjD;4FAEN,qBACE,6VAAC;gGAEC,KACE,UAAU,oBAAoB,kBAAkB;gGAElD,mBAAiB;gGACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,sBAAsB,SAAS;gGAEjC,SAAS,IAAM,eAAe;;kHAE9B,6VAAC;wGAAI,WAAU;kHACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;4GAAC,WAAU;;;;;;;;;;;kHAE3B,6VAAC;wGAAI,WAAU;kHACb,cAAA,6VAAC;4GAAK,WAAU;sHACb;;;;;;;;;;;;+FAhBA;;;;;wFAqBX,mBAEA,6VAAC;4FAAI,WAAU;sGAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAczF,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAI,WAAU;;8EACb,6VAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,OAAO;oEACP,eAAe,CAAC;wEACd,IAAI,OAAO,QAAQ;oEACrB;;sFAEA,6VAAC,kIAAA,CAAA,gBAAa;4EAAC,WAAU;sFACvB,cAAA,6VAAC,kIAAA,CAAA,cAAW;0FACT,SAAS,2BACR,6VAAC;oFAAI,WAAU;;sGACb,6VAAC,kTAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;sGACpB,6VAAC;4FAAK,WAAU;sGAAc;;;;;;;;;;;2FAE9B,SAAS,2BACX,6VAAC;oFAAI,WAAU;;sGACb,6VAAC,4RAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;sGACrB,6VAAC;4FAAK,WAAU;sGAAc;;;;;;;;;;;yGAGhC,6VAAC;oFAAI,WAAU;;sGACb,6VAAC,oRAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;sGACf,6VAAC;4FAAK,WAAU;sGAAc;;;;;;;;;;;;;;;;;;;;;;sFAKtC,6VAAC,kIAAA,CAAA,gBAAa;4EAAC,WAAU;;8FACvB,6VAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;oFAAW,WAAU;8FACrC,cAAA,6VAAC;wFAAI,WAAU;;0GACb,6VAAC,kTAAA,CAAA,WAAQ;gGAAC,WAAU;;;;;;0GACpB,6VAAC;gGAAI,WAAU;;kHACb,6VAAC,iIAAA,CAAA,QAAK;wGAAC,WAAU;kHAAsB;;;;;;kHACvC,6VAAC;wGAAE,WAAU;kHAAgC;;;;;;;;;;;;;;;;;;;;;;;8FAMnD,6VAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;oFAAW,WAAU;8FACrC,cAAA,6VAAC;wFAAI,WAAU;;0GACb,6VAAC,4RAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;0GACrB,6VAAC;gGAAI,WAAU;;kHACb,6VAAC,iIAAA,CAAA,QAAK;wGAAC,WAAU;kHAAsB;;;;;;kHACvC,6VAAC;wGAAE,WAAU;kHAAgC;;;;;;;;;;;;;;;;;;;;;;;8FAMnD,6VAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;oFAAO,WAAU;8FACjC,cAAA,6VAAC;wFAAI,WAAU;;0GACb,6VAAC,uUAAA,CAAA,iBAAc;gGAAC,WAAU;;;;;;0GAC1B,6VAAC;gGAAI,WAAU;;kHACb,6VAAC,iIAAA,CAAA,QAAK;wGAAC,WAAU;kHAAsB;;;;;;kHACvC,6VAAC;wGAAE,WAAU;kHAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EASvD,6VAAC,gIAAA,CAAA,UAAI;oEAAC,OAAO;oEAAgB,MAAK;8EAChC,cAAA,6VAAC;wEAAI,gBAAc;kFACjB,cAAA,6VAAC,kIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;4EAEF,SAAS;gFACP,aAAa,OAAO,EAAE;4EACxB;4EACA,cAAW;;8FAEX,6VAAC;oFACC,MAAK;oFACL,QAAQ;oFACR,QAAO;oFACP,UAAU;oFACV,WAAU;oFACV,IAAG;oFACH,KAAK;oFACL,cAAW;;;;;;8FAEb,6VAAC,oTAAA,CAAA,YAAS;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8EAK3B,6VAAC,gIAAA,CAAA,UAAI;oEACH,OACE,CAAC,mCACG,mFACA,YACE,mBACA;oEAER,MAAK;oEACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,oCAAoC;8EAEnD,cAAA,6VAAC;wEAAI,gBAAc,YAAY,mBAAmB;kFAChD,cAAA,6VAAC,kIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SACE,CAAC,yBAAyB,CAAC,mCACvB,gBACA;4EAEN,MAAK;4EACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qGACA,aACE;4EAEJ,SAAS,YAAY,gBAAgB;4EACrC,UACE,CAAC,oCACD,CAAC,yBACD;4EAEF,cAAY,YAAY,qBAAqB;sFAE5C,CAAC,yBAAyB,CAAC,iDAC1B,6VAAC,mVAAA,CAAA,uBAAoB;gFAAC,WAAU;;;;;uFAC9B,0BACF,6VAAC,mVAAA,CAAA,uBAAoB;gFAAC,WAAU;;;;;qGAEhC,6VAAC,oRAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAOzB,6VAAC;4DAAI,WAAU;;8EACb,6VAAC,gIAAA,CAAA,UAAI;oEAAC,OAAM;oEAAW,MAAK;8EAC1B,cAAA,6VAAC;wEAAI,gBAAa;kFAChB,cAAA,6VAAC,kIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,WAAU;4EACV,SAAS,IAAM,qBAAqB;sFAEpC,cAAA,6VAAC,iUAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;8EAI7B,6VAAC;oEACC,gBACE,iBACI,YACA,mBAAmB,SAAS,GAC1B,eACA;8EAGR,cAAA,6VAAC,kIAAA,CAAA,SAAM;wEACL,MAAM,iBAAiB,WAAW;wEAClC,MAAK;wEACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;wEAEF,UAAU,mBAAmB,SAAS,IAAI;wEAC1C,SAAS,iBAAiB,oBAAoB;kFAE7C,iBACC,kCACE,6VAAC,mIAAA,CAAA,UAAO;4EAAC,WAAU;;;;;iGAEnB,6VAAC,8SAAA,CAAA,SAAM;4EACL,WAAU;4EACV,aAAa;;;;;iGAIjB,6VAAC,gTAAA,CAAA,UAAO;4EAAC,WAAU;4EAAyB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAQlE,CAAC,yBAAyB,kDACzB,6VAAC;oDAAI,WAAU;8DACb,cAAA,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC;4DAAE,WAAU;sEAA+C;;;;;;;;;;;;;;;;gDAQjE,2BACC,6VAAC;oDAAI,WAAU;;sEACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;4DAAC,WAAU;;gEAA4B;8EAElD,6VAAC;oEAAK,WAAU;;sFACd,6VAAC;4EAAK,WAAU;sFAAQ;;;;;;sFACxB,6VAAC;4EAAK,WAAU;sFAAQ;;;;;;sFACxB,6VAAC;4EAAK,WAAU;sFAAQ;;;;;;;;;;;;;;;;;;sEAI5B,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAI,WAAU;;;;;;8EACf,6VAAC;oEAAK,WAAU;8EAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYzF,6VAAC;gBAAI,WAAU;;;;;;YAEd,YAAY,mCACX,6VAAC;gBAAI,WAAU;;;;;;0BAGjB,6VAAC,iIAAA,CAAA,YAAS;gBACR,MAAM;gBACN,cAAc,CAAC;oBACb,qBAAqB;gBACvB;0BAEA,cAAA,6VAAC;oBACC,WAAW;oBACX,UAAU,CAAC;wBACT,qBAAqB;oBACvB;oBACA,gBAAgB;oBAChB,aAAa,KAAK,KAAK,CAAC,cAAc;oBACtC,gBAAgB;;;;;;;;;;;0BAIpB,6VAAC,iIAAA,CAAA,YAAS;gBAAC,MAAM;gBAAqB,cAAc;0BAClD,cAAA,6VAAC;oBACC,MAAM,SAAS,CAAC,EAAE;oBAClB,WAAW;oBACX,gBAAgB;oBAChB,WAAW;oBACX,kBAAkB,eAAe,MAAM;;;;;;;;;;;;;AAKjD;uCAEe", "debugId": null}}, {"offset": {"line": 2535, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-actions-dropdown.tsx"], "sourcesContent": ["import {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/classic/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { deleteThread } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useCurrentThreadStore } from \"@/stores/current-thread\";\r\nimport { Thread } from \"@/types/thread-message\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { ChevronDown, DotsVertical, Edit, Trash } from \"@mynaui/icons-react\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { memo } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { errorToast, loadingToast } from \"../global/toast\";\r\n\r\nconst renameThreadSchema = z.object({\r\n  newName: z.string().min(1, \"Thread name is required\").max(255, \"Thread name is too long\"),\r\n});\r\n\r\ntype RenameThreadFormValues = z.infer<typeof renameThreadSchema>;\r\n\r\nconst ThreadActionsDropdown = memo(\r\n  ({\r\n    thread,\r\n    projectId,\r\n    icon,\r\n    setThreadToRename,\r\n  }: {\r\n    thread: Thread;\r\n    projectId: string;\r\n    icon?: React.ReactNode | null;\r\n    setThreadToRename: (thread: { id: number; name: string } | null) => void;\r\n  }) => {\r\n    const queryClient = useQueryClient();\r\n    const setCurrentThreadId = useCurrentThreadStore((state) => state.setId);\r\n    const { threadName } = useCurrentThreadStore();\r\n\r\n    const renameForm = useForm<RenameThreadFormValues>({\r\n      resolver: zodResolver(renameThreadSchema),\r\n      defaultValues: {\r\n        newName: \"\",\r\n      },\r\n    });\r\n\r\n    const openRenameModal = (e: React.MouseEvent) => {\r\n      e.stopPropagation();\r\n      renameForm.setValue(\"newName\", thread.name || \"\");\r\n      setThreadToRename({ id: thread.thread_id, name: thread.name });\r\n    };\r\n\r\n    const deleteThreadMutation = useMutation({\r\n      mutationFn: async (threadId: string) => {\r\n        loadingToast(\"Deleting thread...\", deleteThread(projectId, threadId)).then(() => {\r\n          setCurrentThreadId(null);\r\n          queryClient.invalidateQueries({ queryKey: [\"threads\", projectId] });\r\n        });\r\n      },\r\n      onError: (error) => {\r\n        console.error(\"[Thread] Failed to delete thread:\", error);\r\n        errorToast(\"Failed to delete thread. Please try again.\");\r\n      },\r\n    });\r\n\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size={icon ? \"default\" : \"icon\"}\r\n            className={cn(\"group\", icon ? \"h-fit w-fit px-2 py-1\" : \"size-7 hover:bg-accent\")}\r\n            onClick={(e) => e.stopPropagation()}\r\n          >\r\n            {!icon ? (\r\n              <DotsVertical className=\"size-4\" strokeWidth={2} />\r\n            ) : (\r\n              <div className=\"flex items-center gap-2\">\r\n                <span className=\"max-w-28 truncate\">\r\n                  {icon && (threadName || thread.name || \"Untitled\")}\r\n                </span>\r\n                <ChevronDown\r\n                  className={cn(\r\n                    \"size-4 transition-transform duration-200\",\r\n                    \"group-data-[state=open]:rotate-90\",\r\n                  )}\r\n                  strokeWidth={2}\r\n                />\r\n              </div>\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\" className=\"min-w-[11rem]\">\r\n          <DropdownMenuItem\r\n            className=\"flex cursor-pointer items-center justify-between gap-2 px-3 py-2 text-sm\"\r\n            onClick={openRenameModal}\r\n          >\r\n            <span>Rename Thread</span>\r\n            <Edit className=\"size-4 text-primary/90 hover:text-primary\" strokeWidth={2} />\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem\r\n            className=\"flex cursor-pointer items-center justify-between gap-2 px-3 py-2 text-sm text-destructive/90 hover:text-destructive\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              deleteThreadMutation.mutate(thread.thread_id.toString());\r\n            }}\r\n          >\r\n            <span className=\"text-destructive/90 hover:text-destructive\">Delete Thread</span>\r\n            <Trash className=\"size-4 text-destructive/90 hover:text-destructive\" strokeWidth={2} />\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  },\r\n);\r\n\r\nThreadActionsDropdown.displayName = \"ThreadActionsDropdown\";\r\n\r\nexport { ThreadActionsDropdown };\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;;AAEA,MAAM,qBAAqB,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,SAAS,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,KAAK;AACjE;AAIA,MAAM,sCAAwB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAC/B,CAAC,EACC,MAAM,EACN,SAAS,EACT,IAAI,EACJ,iBAAiB,EAMlB;IACC,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,qBAAqB,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,QAAU,MAAM,KAAK;IACvE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;IAE3C,MAAM,aAAa,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAA0B;QACjD,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,eAAe;QACjB,WAAW,QAAQ,CAAC,WAAW,OAAO,IAAI,IAAI;QAC9C,kBAAkB;YAAE,IAAI,OAAO,SAAS;YAAE,MAAM,OAAO,IAAI;QAAC;IAC9D;IAEA,MAAM,uBAAuB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACvC,YAAY,OAAO;YACjB,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,sBAAsB,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE,WAAW,WAAW,IAAI,CAAC;gBACzE,mBAAmB;gBACnB,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW;qBAAU;gBAAC;YACnE;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,qCAAqC;YACnD,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,qBACE,6VAAC,iJAAA,CAAA,eAAY;;0BACX,6VAAC,iJAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6VAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAM,OAAO,YAAY;oBACzB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,OAAO,0BAA0B;oBACxD,SAAS,CAAC,IAAM,EAAE,eAAe;8BAEhC,CAAC,qBACA,6VAAC,0TAAA,CAAA,eAAY;wBAAC,WAAU;wBAAS,aAAa;;;;;6CAE9C,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAK,WAAU;0CACb,QAAQ,CAAC,cAAc,OAAO,IAAI,IAAI,UAAU;;;;;;0CAEnD,6VAAC,wTAAA,CAAA,cAAW;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA;gCAEF,aAAa;;;;;;;;;;;;;;;;;;;;;;0BAMvB,6VAAC,iJAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,6VAAC,iJAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,6VAAC;0CAAK;;;;;;0CACN,6VAAC,0SAAA,CAAA,OAAI;gCAAC,WAAU;gCAA4C,aAAa;;;;;;;;;;;;kCAE3E,6VAAC,iJAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB,qBAAqB,MAAM,CAAC,OAAO,SAAS,CAAC,QAAQ;wBACvD;;0CAEA,6VAAC;gCAAK,WAAU;0CAA6C;;;;;;0CAC7D,6VAAC,4SAAA,CAAA,QAAK;gCAAC,WAAU;gCAAoD,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAK5F;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2737, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/continue-message.tsx"], "sourcesContent": ["import { BlurFade } from \"@/components/ui/blur\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { Message } from \"@mynaui/icons-react\";\r\n\r\ninterface ContinueMessageProps {\r\n  text?: string;\r\n  onContinue: () => void;\r\n}\r\n\r\nexport const ContinueMessage = ({\r\n  text = \"Continue or type your next instruction\",\r\n  onContinue,\r\n}: ContinueMessageProps) => {\r\n  return (\r\n    <div className=\"mb-4 flex w-full items-center justify-start py-3\">\r\n      <BlurFade className=\"flex w-full flex-col items-start\">\r\n        <div className=\"relative flex w-full max-w-md items-center justify-between gap-3 rounded-xl border border-chart-2/70 bg-chart-2/20 px-4 py-3\">\r\n          <div className=\"flex items-start gap-2\">\r\n            <Message className=\"size-5 text-primary/70\" />\r\n            <div className=\"flex flex-col\">\r\n              <Typography.H6 className=\"font-medium text-primary\">Softgen is ready</Typography.H6>\r\n              <Typography.P className=\"mt-0 text-sm\">{text}</Typography.P>\r\n            </div>\r\n          </div>\r\n          <Button onClick={onContinue} className=\"text-sm\" size=\"sm\">\r\n            Continue\r\n          </Button>\r\n        </div>\r\n      </BlurFade>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAOO,MAAM,kBAAkB,CAAC,EAC9B,OAAO,wCAAwC,EAC/C,UAAU,EACW;IACrB,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,gIAAA,CAAA,WAAQ;YAAC,WAAU;sBAClB,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,gTAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;wCAAC,WAAU;kDAA2B;;;;;;kDACpD,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wCAAC,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAG5C,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,WAAU;wBAAU,MAAK;kCAAK;;;;;;;;;;;;;;;;;;;;;;AAOrE", "debugId": null}}, {"offset": {"line": 2832, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/softgen-icon.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst SoftgenIcon = ({ className }: { className?: string }) => {\r\n  return (\r\n    <div className={cn(\"flex w-fit items-center justify-center\", className)}>\r\n      <div className=\"h-auto w-fit\">\r\n        <svg\r\n          width=\"80\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 482 109\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          className=\"text-primary\"\r\n        >\r\n          <path\r\n            d=\"M156.645 81.4936C150.337 81.4936 145.103 80.0845 140.943 77.2661C136.782 74.4477 134.232 70.1866 133.293 64.4827L142.956 62.1676C143.493 64.9189 144.399 67.0998 145.674 68.7103C146.949 70.3208 148.526 71.4616 150.405 72.1326C152.351 72.8036 154.431 73.1392 156.645 73.1392C159.933 73.1392 162.45 72.5017 164.194 71.2267C166.006 69.9517 166.912 68.3077 166.912 66.2945C166.912 64.2814 166.073 62.8051 164.396 61.8657C162.718 60.9262 160.235 60.1545 156.947 59.5506L153.525 58.9466C150.17 58.3427 147.116 57.4703 144.365 56.3296C141.614 55.1888 139.433 53.6118 137.823 51.5987C136.212 49.5856 135.407 47.0021 135.407 43.8482C135.407 39.1509 137.151 35.5272 140.641 32.9773C144.13 30.3602 148.76 29.0517 154.531 29.0517C160.101 29.0517 164.664 30.3267 168.221 32.8766C171.844 35.3595 174.193 38.7482 175.267 43.0429L165.604 45.7606C165 42.7409 163.725 40.6272 161.779 39.4193C159.833 38.1443 157.417 37.5068 154.531 37.5068C151.713 37.5068 149.499 38.0436 147.888 39.1173C146.278 40.1239 145.472 41.5666 145.472 43.4455C145.472 45.4587 146.244 46.935 147.787 47.8744C149.398 48.8139 151.545 49.5185 154.229 49.9882L157.752 50.5921C161.309 51.1961 164.564 52.0349 167.516 53.1086C170.469 54.1822 172.784 55.7256 174.461 57.7387C176.206 59.7519 177.078 62.436 177.078 65.7912C177.078 70.757 175.233 74.6155 171.542 77.3667C167.852 80.118 162.886 81.4936 156.645 81.4936Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M207.415 81.4936C202.45 81.4936 198.021 80.4871 194.129 78.474C190.304 76.3937 187.284 73.4411 185.07 69.6162C182.855 65.7913 181.748 61.2617 181.748 56.0276V54.5177C181.748 49.2836 182.855 44.7876 185.07 41.0298C187.284 37.2048 190.304 34.2523 194.129 32.172C198.021 30.0918 202.45 29.0517 207.415 29.0517C212.381 29.0517 216.81 30.0918 220.702 32.172C224.594 34.2523 227.647 37.2048 229.862 41.0298C232.076 44.7876 233.183 49.2836 233.183 54.5177V56.0276C233.183 61.2617 232.076 65.7913 229.862 69.6162C227.647 73.4411 224.594 76.3937 220.702 78.474C216.81 80.4871 212.381 81.4936 207.415 81.4936ZM207.415 72.2333C211.978 72.2333 215.669 70.7905 218.488 67.905C221.373 64.9524 222.816 60.8926 222.816 55.7256V54.8197C222.816 49.6527 221.407 45.6264 218.588 42.7409C215.77 39.7884 212.046 38.3121 207.415 38.3121C202.919 38.3121 199.229 39.7884 196.343 42.7409C193.525 45.6264 192.116 49.6527 192.116 54.8197V55.7256C192.116 60.8926 193.525 64.9524 196.343 67.905C199.229 70.7905 202.919 72.2333 207.415 72.2333Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M248.149 80.0845V39.218H234.258V30.4609H248.149V19.59C248.149 16.5703 249.055 14.1545 250.867 12.3427C252.678 10.5309 255.094 9.625 258.114 9.625H270.897V18.3821H261.436C259.49 18.3821 258.517 19.3887 258.517 21.4018V30.4609H272.81V39.218H258.517V80.0845H248.149Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M295.735 80.0845C292.715 80.0845 290.3 79.1785 288.488 77.3667C286.743 75.5549 285.871 73.1392 285.871 70.1195V39.218H272.181V30.4609H285.871V14.0539H296.238V30.4609H311.035V39.218H296.238V68.3077C296.238 70.3208 297.178 71.3274 299.057 71.3274H309.424V80.0845H295.735Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M313.997 55.5243V54.0145C313.997 48.7803 315.037 44.3179 317.117 40.6272C319.265 36.9364 322.083 34.0845 325.573 32.0714C329.062 30.0582 332.887 29.0517 337.047 29.0517C341.879 29.0517 345.57 29.9576 348.12 31.7694C350.737 33.5812 352.649 35.5272 353.857 37.6075H355.467V30.4609H365.533V90.2507C365.533 93.2704 364.627 95.6862 362.815 97.498C361.071 99.3098 358.655 100.216 355.568 100.216H322.15V91.1567H352.347C354.293 91.1567 355.266 90.1501 355.266 88.137V72.3339H353.656C352.918 73.5418 351.877 74.7832 350.535 76.0582C349.193 77.3332 347.415 78.3733 345.201 79.1785C343.053 79.9838 340.335 80.3864 337.047 80.3864C332.887 80.3864 329.028 79.4134 325.472 77.4674C321.982 75.4543 319.198 72.6023 317.117 68.9116C315.037 65.1538 313.997 60.6913 313.997 55.5243ZM339.866 71.3274C344.362 71.3274 348.052 69.9182 350.938 67.0998C353.891 64.2143 355.367 60.2552 355.367 55.2223V54.3164C355.367 49.1494 353.924 45.1903 351.039 42.439C348.153 39.6206 344.429 38.2114 339.866 38.2114C335.437 38.2114 331.746 39.6206 328.794 42.439C325.908 45.1903 324.465 49.1494 324.465 54.3164V55.2223C324.465 60.2552 325.908 64.2143 328.794 67.0998C331.746 69.9182 335.437 71.3274 339.866 71.3274Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M398.513 81.4936C393.48 81.4936 389.085 80.4535 385.327 78.3733C381.569 76.226 378.617 73.2398 376.469 69.4149C374.389 65.5228 373.349 61.0269 373.349 55.9269V54.7191C373.349 49.552 374.389 45.056 376.469 41.2311C378.549 37.339 381.435 34.3529 385.126 32.2727C388.884 30.1253 393.212 29.0517 398.11 29.0517C402.875 29.0517 407.035 30.1253 410.592 32.2727C414.215 34.3529 417.034 37.2719 419.047 41.0298C421.06 44.7876 422.067 49.183 422.067 54.2158V58.1414H383.918C384.052 62.5031 385.495 65.9926 388.246 68.6096C391.064 71.1596 394.554 72.4346 398.714 72.4346C402.606 72.4346 405.525 71.5622 407.471 69.8175C409.485 68.0728 411.028 66.0597 412.102 63.7781L420.657 68.207C419.718 70.0859 418.342 72.0655 416.53 74.1457C414.786 76.226 412.471 77.9707 409.585 79.3799C406.7 80.789 403.009 81.4936 398.513 81.4936ZM384.018 50.1895H411.498C411.229 46.4317 409.887 43.5126 407.471 41.4324C405.056 39.2851 401.902 38.2114 398.01 38.2114C394.118 38.2114 390.93 39.2851 388.447 41.4324C386.032 43.5126 384.555 46.4317 384.018 50.1895Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M429.514 80.0845V30.4609H439.68V37.9094H441.29C442.23 35.8963 443.907 34.0174 446.323 32.2727C448.739 30.528 452.329 29.6556 457.093 29.6556C460.851 29.6556 464.173 30.4944 467.058 32.172C470.011 33.8496 472.326 36.2318 474.004 39.3186C475.681 42.3383 476.52 45.9955 476.52 50.2902V80.0845H466.153V51.0954C466.153 46.8008 465.079 43.6469 462.932 41.6337C460.784 39.5535 457.832 38.5134 454.074 38.5134C449.779 38.5134 446.323 39.9226 443.706 42.7409C441.156 45.5593 439.881 49.6527 439.881 55.021V80.0845H429.514Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <g clipPath=\"url(#clip0_1503_289)\">\r\n            800%\r\n            <path\r\n              d=\"M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n            <path\r\n              d=\"M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n            <path\r\n              d=\"M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n            <path\r\n              d=\"M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n          </g>\r\n          <defs>\r\n            <clipPath id=\"clip0_1503_289\">\r\n              <rect width=\"109\" height=\"109\" fill=\"currentColor\" />\r\n            </clipPath>\r\n          </defs>\r\n        </svg>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SoftgenIcon;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,cAAc,CAAC,EAAE,SAAS,EAA0B;IACxD,qBACE,6VAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;kBAC3D,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;gBACN,WAAU;;kCAEV,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBAAE,UAAS;;4BAAuB;0CAEjC,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;;kCAGT,6VAAC;kCACC,cAAA,6VAAC;4BAAS,IAAG;sCACX,cAAA,6VAAC;gCAAK,OAAM;gCAAM,QAAO;gCAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD;uCAEe", "debugId": null}}, {"offset": {"line": 2997, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/collapsible-content/utils.ts"], "sourcesContent": ["import {\r\n  ActionError,\r\n  ActionResult,\r\n  MessageActions,\r\n  ParsedCommand,\r\n  ParsedSection,\r\n} from \"@/types/thread-message\";\r\n\r\ninterface CommandPattern {\r\n  type: string;\r\n  regex: RegExp;\r\n}\r\n\r\nexport interface ParsedToolResult {\r\n  type: \"tool_result\";\r\n  name: string;\r\n  success: boolean;\r\n  output: string;\r\n  index: number;\r\n}\r\n\r\nconst getFileName = (path: string): string => {\r\n  if (!path) return \"\";\r\n  return path;\r\n};\r\n\r\nexport function parseFailedActions(actions?: ActionResult[], failedActions?: ActionError[]) {\r\n  const failedActionsMap = new Map<string, { error: string; isServerError: boolean }>();\r\n  let buildCheckError = \"\";\r\n  let buildFailedFiles: string[] = [];\r\n\r\n  // Create a map of explicitly failed actions from failedActions array\r\n  const explicitlyFailedActions = new Map<string, { error: string; isServerError: boolean }>();\r\n  if (failedActions && failedActions.length > 0) {\r\n    failedActions.forEach((action) => {\r\n      const errorMessage = action.error || \"Action failed\";\r\n      const isServerError =\r\n        errorMessage.includes(\"Gateway\") ||\r\n        errorMessage.includes(\"500:\") ||\r\n        errorMessage.includes(\"502:\") ||\r\n        errorMessage.includes(\"Failed to execute command\");\r\n\r\n      if (action.function === \"check_for_errors\") {\r\n        buildCheckError = action.error || \"Build check failed\";\r\n        explicitlyFailedActions.set(action.function, { error: errorMessage, isServerError });\r\n\r\n        if (action.error) {\r\n          const tsFileMatches = action.error.match(/src\\/[\\w\\/-]+\\.\\w+/g);\r\n          const lintFileMatches = action.error.match(/\\.\\/src\\/[\\w\\/-]+\\.\\w+/g);\r\n\r\n          if (tsFileMatches) {\r\n            buildFailedFiles = [...buildFailedFiles, ...tsFileMatches];\r\n          }\r\n\r\n          if (lintFileMatches) {\r\n            const cleanedPaths = lintFileMatches.map((path) => path.replace(/^\\.\\//, \"\"));\r\n            buildFailedFiles = [...buildFailedFiles, ...cleanedPaths];\r\n          }\r\n        }\r\n      } else if (action.function === \"send_terminal_command\") {\r\n        explicitlyFailedActions.set(action.function, {\r\n          error: errorMessage,\r\n          isServerError,\r\n        });\r\n      } else if (action.function) {\r\n        explicitlyFailedActions.set(action.function, {\r\n          error: errorMessage,\r\n          isServerError,\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  // Process actions array to identify failed actions\r\n  if (actions && actions.length > 0) {\r\n    actions.forEach((action) => {\r\n      if (\"error\" in action) {\r\n        const errorMessage = action.error || \"Action failed\";\r\n        const isServerError =\r\n          errorMessage.includes(\"Gateway\") ||\r\n          errorMessage.includes(\"500:\") ||\r\n          errorMessage.includes(\"502:\") ||\r\n          errorMessage.includes(\"Failed to execute command\");\r\n\r\n        if (action.function === \"check_for_errors\") {\r\n          buildCheckError = action.error || \"Build check failed\";\r\n          explicitlyFailedActions.set(action.function, { error: errorMessage, isServerError });\r\n\r\n          if (action.error) {\r\n            const tsFileMatches = action.error.match(/src\\/[\\w\\/-]+\\.\\w+/g);\r\n            const lintFileMatches = action.error.match(/\\.\\/src\\/[\\w\\/-]+\\.\\w+/g);\r\n\r\n            if (tsFileMatches) {\r\n              buildFailedFiles = [...buildFailedFiles, ...tsFileMatches];\r\n            }\r\n\r\n            if (lintFileMatches) {\r\n              const cleanedPaths = lintFileMatches.map((path) => path.replace(/^\\.\\//, \"\"));\r\n              buildFailedFiles = [...buildFailedFiles, ...cleanedPaths];\r\n            }\r\n          }\r\n        } else if (action.function === \"send_terminal_command\") {\r\n          explicitlyFailedActions.set(action.function, {\r\n            error: errorMessage,\r\n            isServerError,\r\n          });\r\n        } else if (action.args && \"file_path\" in action.args) {\r\n          const path = action.args.file_path as string;\r\n          explicitlyFailedActions.set(path, {\r\n            error: errorMessage,\r\n            isServerError,\r\n          });\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // Add build failed files to the failed actions map\r\n  buildFailedFiles.forEach((filePath) => {\r\n    if (!explicitlyFailedActions.has(filePath)) {\r\n      explicitlyFailedActions.set(filePath, {\r\n        error: \"File has linting or type errors\",\r\n        isServerError: false,\r\n      });\r\n    }\r\n  });\r\n\r\n  // Merge all failed actions into the final map\r\n  explicitlyFailedActions.forEach((value, key) => {\r\n    failedActionsMap.set(key, value);\r\n  });\r\n\r\n  return { fileErrors: failedActionsMap, buildCheckError, buildFailedFiles };\r\n}\r\n\r\nexport const getUserFriendlyActionType = (type: string): string => {\r\n  switch (type) {\r\n    case \"actions\":\r\n      return \"Changes Made\";\r\n    case \"tool_result\":\r\n      return \"System Check Results\";\r\n    default:\r\n      return type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, \" \");\r\n  }\r\n};\r\n\r\nexport const getActionTitle = (selectedFileInModal: string): string => {\r\n  switch (selectedFileInModal) {\r\n    case \"send_terminal_command\":\r\n      return \"Terminal Command\";\r\n    case \"check_for_errors\":\r\n      return \"Code Analysis\";\r\n    case \"reset_next_server\":\r\n      return \"Restart Server\";\r\n    case \"create_file\":\r\n      return \"Create File\";\r\n    case \"update_file\":\r\n      return \"Update File\";\r\n    case \"full_file_rewrite\":\r\n      return \"Update File\";\r\n    case \"update_file_sections\":\r\n      return \"Update File\";\r\n    case \"delete_file\":\r\n      return \"Delete File\";\r\n    case \"open_files_in_editor\":\r\n      return \"Open Files\";\r\n    case \"close_files_in_editor\":\r\n      return \"Close Files\";\r\n    case \"stop_session\":\r\n      return \"Stop Session\";\r\n    case \"execute_sql_query\":\r\n      return \"SQL Query\";\r\n    case \"get_database_schema\":\r\n      return \"Database Schema\";\r\n    default:\r\n      return getFileName(selectedFileInModal);\r\n  }\r\n};\r\n\r\nexport const getActionDisplayName = (action: ParsedCommand): string => {\r\n  switch (action.type) {\r\n    case \"send_terminal_command\":\r\n      return \"Terminal Command\";\r\n    case \"check_for_errors\":\r\n      return \"Code Analysis\";\r\n    case \"reset_next_server\":\r\n      return \"Restart Server\";\r\n    case \"open_files_in_editor\":\r\n      return \"Open Files\";\r\n    case \"close_files_in_editor\":\r\n      return \"Close Files\";\r\n    case \"create_file\":\r\n    case \"update_file\":\r\n    case \"full_file_rewrite\":\r\n    case \"update_file_sections\":\r\n    case \"delete_file\":\r\n      return getFileName(action.path || \"Unknown File\");\r\n    case \"execute_sql_query\":\r\n      return \"SQL Query\";\r\n    default:\r\n      return action.type.replace(/_/g, \" \").replace(/\\b\\w/g, (l) => l.toUpperCase());\r\n  }\r\n};\r\n\r\nexport const getActionIdentifier = (action: ParsedCommand): string => {\r\n  if (\r\n    action.type === \"send_terminal_command\" ||\r\n    action.type === \"check_for_errors\" ||\r\n    action.type === \"reset_next_server\" ||\r\n    action.type === \"open_files_in_editor\" ||\r\n    action.type === \"close_files_in_editor\"\r\n  ) {\r\n    return action.type;\r\n  }\r\n  return action.path || action.type;\r\n};\r\n\r\nconst commandPatterns: CommandPattern[] = [\r\n  {\r\n    type: \"create_file\",\r\n    regex: /<create_file file_path=\"([^\"]+)\">([\\s\\S]*?)<\\/create_file>/g,\r\n  },\r\n  {\r\n    type: \"update_file\",\r\n    regex: /<update_file file_path=\"([^\"]+)\">([\\s\\S]*?)<\\/update_file>/g,\r\n  },\r\n  {\r\n    type: \"full_file_rewrite\",\r\n    regex: /<full_file_rewrite file_path=\"([^\"]+)\">([\\s\\S]*?)<\\/full_file_rewrite>/g,\r\n  },\r\n  {\r\n    type: \"update_file_sections\",\r\n    regex:\r\n      /<update_file_sections file_path=\"([^\"]+)\"(?:\\s+file_updates=\"([^\"]+)\")?>(?:([\\s\\S]*?)(?:<\\/update_file_sections>))?/g,\r\n  },\r\n  {\r\n    type: \"check_for_errors\",\r\n    regex:\r\n      /<check_for_errors(?:\\s+with_build=\"(true|false)\")?\\s*?>(.*?)<\\/check_for_errors>|<check_for_errors(?:\\s+with_build=\"(true|false)\")?\\s*?\\/>/g,\r\n  },\r\n  {\r\n    type: \"open_files_in_editor\",\r\n    regex: /<open_files_in_editor files=\\[([^\\]]+)\\]><\\/open_files_in_editor>/g,\r\n  },\r\n  {\r\n    type: \"close_files_in_editor\",\r\n    regex: /<close_files_in_editor files=\\[([^\\]]+)\\]><\\/close_files_in_editor>/g,\r\n  },\r\n  {\r\n    type: \"stop_session\",\r\n    regex: /<stop_session message=\"([^\"]+)\"><\\/stop_session>/g,\r\n  },\r\n  {\r\n    type: \"reset_next_server\",\r\n    regex: /<reset_next_server><\\/reset_next_server>/g,\r\n  },\r\n  {\r\n    type: \"send_terminal_command\",\r\n    regex: /<send_terminal_command(?:\\s+command=\"([^\"]+)\")?\\s*\\/?>(.*?)<\\/send_terminal_command>/gs,\r\n  },\r\n  {\r\n    type: \"execute_sql_query\",\r\n    regex: /<execute_sql_query(?:\\s+query=\"([^\"]+)\")?\\s*\\/?>(.*?)<\\/execute_sql_query>/gs,\r\n  },\r\n  {\r\n    type: \"delete_file\",\r\n    regex: /<delete_file file_path=\"([^\"]+)\"><\\/delete_file>/g,\r\n  },\r\n];\r\n\r\nconst SUCCESS_RESULT_REGEX = /Result: (\\[[\\s\\S]*\\])/;\r\nconst ERROR_MESSAGE_REGEX = /ERROR:\\s+[^:]+:[\\s\\S]*?(?=\\n|$)/;\r\n\r\nconst parseActionCommands = (\r\n  actionContent: string,\r\n  actions?: ActionResult[],\r\n  failedActions?: ActionError[],\r\n): ParsedCommand[] => {\r\n  const commands: ParsedCommand[] = [];\r\n  const { fileErrors, buildCheckError } = parseFailedActions(actions, failedActions);\r\n\r\n  // Create a map of successful actions for quick lookup\r\n  const successfulActions = new Map<string, boolean>();\r\n  if (actions) {\r\n    actions.forEach((action) => {\r\n      if (!(\"error\" in action)) {\r\n        if (action.args && \"file_path\" in action.args) {\r\n          successfulActions.set(action.args.file_path as string, true);\r\n        } else if (action.function) {\r\n          successfulActions.set(action.function, true);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  commandPatterns.forEach((pattern) => {\r\n    let match;\r\n    while ((match = pattern.regex.exec(actionContent)) !== null) {\r\n      if (\r\n        pattern.type === \"create_file\" ||\r\n        pattern.type === \"update_file\" ||\r\n        pattern.type === \"full_file_rewrite\"\r\n      ) {\r\n        const path = match[1];\r\n        const isFailed = fileErrors.has(path);\r\n        const failedDetails = isFailed ? fileErrors.get(path) : undefined;\r\n        const isSuccess = successfulActions.has(path);\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          path,\r\n          content: match[2] || undefined,\r\n          failed: isFailed && !isSuccess,\r\n          error: failedDetails?.error,\r\n          isServerError: failedDetails?.isServerError,\r\n        });\r\n      } else if (pattern.type === \"update_file_sections\") {\r\n        const path = match[1];\r\n        const isFailed = fileErrors.has(path);\r\n        const failedDetails = isFailed ? fileErrors.get(path) : undefined;\r\n        const isSuccess = successfulActions.has(path);\r\n\r\n        const fileUpdates = match[2] || match[3] || \"\";\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          path,\r\n          file_updates: fileUpdates,\r\n          content: fileUpdates,\r\n          failed: isFailed && !isSuccess,\r\n          error: failedDetails?.error,\r\n          isServerError: failedDetails?.isServerError,\r\n        });\r\n      } else if (pattern.type === \"check_for_errors\") {\r\n        const hasBuildError = buildCheckError !== \"\";\r\n        const isSuccess = successfulActions.has(\"check_for_errors\");\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          with_build: match[1] === \"true\",\r\n          failed: hasBuildError && !isSuccess,\r\n          buildError: hasBuildError ? buildCheckError : undefined,\r\n          status: hasBuildError ? \"failed\" : isSuccess ? \"completed\" : \"pending\",\r\n        });\r\n      } else if (\r\n        pattern.type === \"open_files_in_editor\" ||\r\n        pattern.type === \"close_files_in_editor\"\r\n      ) {\r\n        const filesStr = match[1];\r\n        const files = filesStr\r\n          .split(\",\")\r\n          .map((file) => file.trim().replace(/\"/g, \"\"))\r\n          .filter(Boolean);\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          files,\r\n        });\r\n      } else if (pattern.type === \"send_terminal_command\") {\r\n        const command = match[1] || match[2].trim();\r\n        const isFailed = fileErrors.has(\"send_terminal_command\");\r\n        const failedDetails = isFailed ? fileErrors.get(\"send_terminal_command\") : undefined;\r\n        const isSuccess = successfulActions.has(\"send_terminal_command\");\r\n\r\n        const matchingAction = actions?.find(\r\n          (a) => \"function\" in a && a.function === \"send_terminal_command\",\r\n        );\r\n\r\n        let content = \"\";\r\n        if (matchingAction && \"result\" in matchingAction) {\r\n          content = extractToolResultOutput(matchingAction.result);\r\n        }\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          message: command,\r\n          content,\r\n          failed: isFailed && !isSuccess,\r\n          error: failedDetails?.error,\r\n          isServerError: failedDetails?.isServerError,\r\n        });\r\n      } else if (pattern.type === \"execute_sql_query\") {\r\n        const command = match[1] || match[2].trim();\r\n        const isFailed = fileErrors.has(\"execute_sql_query\");\r\n        const failedDetails = isFailed ? fileErrors.get(\"execute_sql_query\") : undefined;\r\n        const isSuccess = successfulActions.has(\"execute_sql_query\");\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          message: command,\r\n          failed: isFailed && !isSuccess,\r\n          error: failedDetails?.error,\r\n          isServerError: failedDetails?.isServerError,\r\n        });\r\n      } else if (pattern.type === \"stop_session\") {\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          message: match[1],\r\n        });\r\n      } else if (pattern.type === \"delete_file\") {\r\n        const path = match[1];\r\n        const isFailed = fileErrors.has(path);\r\n        const failedDetails = isFailed ? fileErrors.get(path) : undefined;\r\n        const isSuccess = successfulActions.has(path);\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          path,\r\n          failed: isFailed && !isSuccess,\r\n          error: failedDetails?.error,\r\n          isServerError: failedDetails?.isServerError,\r\n        });\r\n      } else if (pattern.type === \"reset_next_server\") {\r\n        const isFailed = fileErrors.has(\"reset_next_server\");\r\n        const failedDetails = isFailed ? fileErrors.get(\"reset_next_server\") : undefined;\r\n        const isSuccess = successfulActions.has(\"reset_next_server\");\r\n\r\n        commands.push({\r\n          type: pattern.type as MessageActions,\r\n          failed: isFailed && !isSuccess,\r\n          error: failedDetails?.error,\r\n          isServerError: failedDetails?.isServerError,\r\n        });\r\n      }\r\n    }\r\n  });\r\n\r\n  // Also parse from actions array if available\r\n  if (actions && actions.length > 0) {\r\n    actions.forEach((action) => {\r\n      if (action.function === \"update_file_sections\" && action.args?.file_path) {\r\n        const path = action.args.file_path as string;\r\n\r\n        // Fix: Check for various possible properties and fall back safely\r\n        let fileUpdates = \"\";\r\n        if (action.args && typeof action.args === \"object\") {\r\n          if (\"file_updates\" in action.args) {\r\n            fileUpdates = action.args.file_updates as string;\r\n          } else if (\"file_contents\" in action.args) {\r\n            fileUpdates = action.args.file_contents as string;\r\n          }\r\n        }\r\n\r\n        const isFailed = \"error\" in action;\r\n        const isSuccess = successfulActions.has(path);\r\n\r\n        commands.push({\r\n          type: action.function as MessageActions,\r\n          path,\r\n          file_updates: fileUpdates,\r\n          content: fileUpdates,\r\n          failed: isFailed && !isSuccess,\r\n          error: isFailed ? action.error : undefined,\r\n          isServerError: isFailed ? action.error?.includes(\"Gateway\") || false : false,\r\n        });\r\n      } else if (action.function === \"execute_sql_query\") {\r\n        const query = action.args.sql_query as string;\r\n        const isFailed = \"error\" in action;\r\n        const isSuccess = successfulActions.has(\"execute_sql_query\");\r\n        let message = isFailed ? action.error : action.result;\r\n        let content = \"\";\r\n\r\n        // Process the message/error content\r\n        if (message) {\r\n          message = extractToolResultOutput(message);\r\n\r\n          if (!isFailed) {\r\n            const resultMatch = SUCCESS_RESULT_REGEX.exec(message);\r\n            content = resultMatch ? resultMatch[1] : formatToolOutput(message);\r\n          } else {\r\n            const errorMatch = ERROR_MESSAGE_REGEX.exec(message);\r\n            content = errorMatch ? errorMatch[0].trim() : formatToolOutput(message);\r\n          }\r\n        }\r\n\r\n        commands.push({\r\n          type: action.function as MessageActions,\r\n          message: query,\r\n          content,\r\n          failed: isFailed && !isSuccess,\r\n        });\r\n      } else if (action.function === \"get_database_schema\") {\r\n        const isFailed = \"error\" in action;\r\n        const isSuccess = successfulActions.has(\"get_database_schema\");\r\n        let message = isFailed ? action.error : action.result;\r\n        let content = \"\";\r\n\r\n        // Process the message/error content\r\n        if (message) {\r\n          message = extractToolResultOutput(message);\r\n\r\n          if (!isFailed) {\r\n            const resultMatch = SUCCESS_RESULT_REGEX.exec(message);\r\n            content = resultMatch ? resultMatch[1] : formatToolOutput(message);\r\n          } else {\r\n            const errorMatch = ERROR_MESSAGE_REGEX.exec(message);\r\n            content = errorMatch ? errorMatch[0].trim() : formatToolOutput(message);\r\n          }\r\n        }\r\n\r\n        commands.push({\r\n          type: action.function as MessageActions,\r\n          content,\r\n          failed: isFailed && !isSuccess,\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  return commands;\r\n};\r\n\r\nconst patterns: { type: string; regex: RegExp }[] = [\r\n  { type: \"communication\", regex: /<communication>([\\s\\S]*?)<\\/communication>/g },\r\n  { type: \"actions\", regex: /<actions>([\\s\\S]*?)<\\/actions>/g },\r\n  {\r\n    type: \"continue_instructions\",\r\n    regex: /<continue_instructions>([\\s\\S]*?)<\\/continue_instructions>/g,\r\n  },\r\n];\r\n\r\nexport function parseContent(\r\n  content: string,\r\n  actions?: ActionResult[],\r\n  failedActions?: ActionError[],\r\n): (ParsedSection | ParsedToolResult)[] {\r\n  const sections: (ParsedSection | ParsedToolResult)[] = [];\r\n  let index = 0;\r\n\r\n  patterns.forEach(({ type, regex }) => {\r\n    let match;\r\n\r\n    while ((match = regex.exec(content)) !== null) {\r\n      if (type === \"actions\") {\r\n        const actionContent = match[1].trim();\r\n        const actionCommands = parseActionCommands(actionContent, actions, failedActions);\r\n\r\n        sections.push({\r\n          type: \"actions\",\r\n          content: actionContent,\r\n          commands: actionCommands,\r\n          index: index++,\r\n        });\r\n      } else {\r\n        sections.push({\r\n          type: type as \"communication\" | \"continue_instructions\" | \"thoughts\",\r\n          content: match[1].trim(),\r\n          index: index++,\r\n        });\r\n      }\r\n    }\r\n  });\r\n\r\n  return sections.sort((a, b) => {\r\n    if (a.type === \"tool_result\" && b.type === \"tool_result\") {\r\n      return content.indexOf(`${a.name}: ToolResult`) - content.indexOf(`${b.name}: ToolResult`);\r\n    } else if (a.type === \"tool_result\") {\r\n      const toolPos = content.indexOf(`${a.name}: ToolResult`);\r\n      const tagPos =\r\n        b.type === \"actions\" ? content.indexOf(`<${b.type}>`) : content.indexOf(`<${b.type}>`);\r\n      return toolPos - tagPos;\r\n    } else if (b.type === \"tool_result\") {\r\n      const tagPos =\r\n        a.type === \"actions\" ? content.indexOf(`<${a.type}>`) : content.indexOf(`<${a.type}>`);\r\n      const toolPos = content.indexOf(`${b.name}: ToolResult`);\r\n      return tagPos - toolPos;\r\n    }\r\n\r\n    const aPos = content.indexOf(`<${a.type}>`);\r\n    const bPos = content.indexOf(`<${b.type}>`);\r\n    return aPos - bPos;\r\n  });\r\n}\r\n\r\nconst TOOL_RESULT_REGEX = /ToolResult\\([^)]*output=('([^']|\\\\')*')\\)/;\r\n\r\n/**\r\n * Extracts the output from a ToolResult string if present, otherwise returns the input\r\n * Handles both raw output and ToolResult format\r\n */\r\nexport function extractToolResultOutput(output: string): string {\r\n  // If it's already just the output, return it as is\r\n  if (!output.includes(\"ToolResult(\")) {\r\n    return output;\r\n  }\r\n\r\n  // Extract the inner error message if it's in ToolResult format\r\n  // This regex matches the entire ToolResult string and captures the output part\r\n  const toolResultMatch = TOOL_RESULT_REGEX.exec(output);\r\n  if (toolResultMatch && toolResultMatch[1]) {\r\n    // Remove the surrounding quotes and handle escaped characters\r\n    const content = toolResultMatch[1].slice(1, -1); // Remove the outer quotes\r\n    return content\r\n      .replace(/\\\\\\\\/g, \"\\\\\") // Replace double backslashes with single backslash\r\n      .replace(/\\\\n/g, \"\\n\") // Replace \\n with newline\r\n      .replace(/\\\\'/g, \"'\") // Replace \\' with single quote\r\n      .replace(/\\\\\"/g, '\"'); // Replace \\\" with double quote\r\n  }\r\n\r\n  return output;\r\n}\r\nconst TOOL_RESULT_REGEX_SUCCESS = /(\\w+):\\s*ToolResult\\(success=(\\w+),\\s*output=('[^']*')\\)/;\r\n\r\nexport function extractToolResult(\r\n  output: string,\r\n): { type: string; success: boolean; output: string } | null {\r\n  const match = TOOL_RESULT_REGEX_SUCCESS.exec(output);\r\n  if (!match) return null;\r\n\r\n  const outputString = extractToolResultOutput(output);\r\n\r\n  return {\r\n    type: match[1],\r\n    success: match[2].toLowerCase() === \"true\",\r\n    output: outputString,\r\n  };\r\n}\r\n\r\nexport const formatToolOutput = (output: string): string => {\r\n  try {\r\n    if (output.trim().startsWith(\"{\") && output.trim().endsWith(\"}\")) {\r\n      const jsonObj = JSON.parse(output);\r\n      return JSON.stringify(jsonObj, null, 2);\r\n    }\r\n\r\n    return output;\r\n  } catch (e) {\r\n    console.error(e);\r\n    return output;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAqBA,MAAM,cAAc,CAAC;IACnB,IAAI,CAAC,MAAM,OAAO;IAClB,OAAO;AACT;AAEO,SAAS,mBAAmB,OAAwB,EAAE,aAA6B;IACxF,MAAM,mBAAmB,IAAI;IAC7B,IAAI,kBAAkB;IACtB,IAAI,mBAA6B,EAAE;IAEnC,qEAAqE;IACrE,MAAM,0BAA0B,IAAI;IACpC,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;QAC7C,cAAc,OAAO,CAAC,CAAC;YACrB,MAAM,eAAe,OAAO,KAAK,IAAI;YACrC,MAAM,gBACJ,aAAa,QAAQ,CAAC,cACtB,aAAa,QAAQ,CAAC,WACtB,aAAa,QAAQ,CAAC,WACtB,aAAa,QAAQ,CAAC;YAExB,IAAI,OAAO,QAAQ,KAAK,oBAAoB;gBAC1C,kBAAkB,OAAO,KAAK,IAAI;gBAClC,wBAAwB,GAAG,CAAC,OAAO,QAAQ,EAAE;oBAAE,OAAO;oBAAc;gBAAc;gBAElF,IAAI,OAAO,KAAK,EAAE;oBAChB,MAAM,gBAAgB,OAAO,KAAK,CAAC,KAAK,CAAC;oBACzC,MAAM,kBAAkB,OAAO,KAAK,CAAC,KAAK,CAAC;oBAE3C,IAAI,eAAe;wBACjB,mBAAmB;+BAAI;+BAAqB;yBAAc;oBAC5D;oBAEA,IAAI,iBAAiB;wBACnB,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO,CAAC,SAAS;wBACzE,mBAAmB;+BAAI;+BAAqB;yBAAa;oBAC3D;gBACF;YACF,OAAO,IAAI,OAAO,QAAQ,KAAK,yBAAyB;gBACtD,wBAAwB,GAAG,CAAC,OAAO,QAAQ,EAAE;oBAC3C,OAAO;oBACP;gBACF;YACF,OAAO,IAAI,OAAO,QAAQ,EAAE;gBAC1B,wBAAwB,GAAG,CAAC,OAAO,QAAQ,EAAE;oBAC3C,OAAO;oBACP;gBACF;YACF;QACF;IACF;IAEA,mDAAmD;IACnD,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;QACjC,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,WAAW,QAAQ;gBACrB,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,MAAM,gBACJ,aAAa,QAAQ,CAAC,cACtB,aAAa,QAAQ,CAAC,WACtB,aAAa,QAAQ,CAAC,WACtB,aAAa,QAAQ,CAAC;gBAExB,IAAI,OAAO,QAAQ,KAAK,oBAAoB;oBAC1C,kBAAkB,OAAO,KAAK,IAAI;oBAClC,wBAAwB,GAAG,CAAC,OAAO,QAAQ,EAAE;wBAAE,OAAO;wBAAc;oBAAc;oBAElF,IAAI,OAAO,KAAK,EAAE;wBAChB,MAAM,gBAAgB,OAAO,KAAK,CAAC,KAAK,CAAC;wBACzC,MAAM,kBAAkB,OAAO,KAAK,CAAC,KAAK,CAAC;wBAE3C,IAAI,eAAe;4BACjB,mBAAmB;mCAAI;mCAAqB;6BAAc;wBAC5D;wBAEA,IAAI,iBAAiB;4BACnB,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO,CAAC,SAAS;4BACzE,mBAAmB;mCAAI;mCAAqB;6BAAa;wBAC3D;oBACF;gBACF,OAAO,IAAI,OAAO,QAAQ,KAAK,yBAAyB;oBACtD,wBAAwB,GAAG,CAAC,OAAO,QAAQ,EAAE;wBAC3C,OAAO;wBACP;oBACF;gBACF,OAAO,IAAI,OAAO,IAAI,IAAI,eAAe,OAAO,IAAI,EAAE;oBACpD,MAAM,OAAO,OAAO,IAAI,CAAC,SAAS;oBAClC,wBAAwB,GAAG,CAAC,MAAM;wBAChC,OAAO;wBACP;oBACF;gBACF;YACF;QACF;IACF;IAEA,mDAAmD;IACnD,iBAAiB,OAAO,CAAC,CAAC;QACxB,IAAI,CAAC,wBAAwB,GAAG,CAAC,WAAW;YAC1C,wBAAwB,GAAG,CAAC,UAAU;gBACpC,OAAO;gBACP,eAAe;YACjB;QACF;IACF;IAEA,8CAA8C;IAC9C,wBAAwB,OAAO,CAAC,CAAC,OAAO;QACtC,iBAAiB,GAAG,CAAC,KAAK;IAC5B;IAEA,OAAO;QAAE,YAAY;QAAkB;QAAiB;IAAiB;AAC3E;AAEO,MAAM,4BAA4B,CAAC;IACxC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IACtE;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,YAAY;IACvB;AACF;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,YAAY,OAAO,IAAI,IAAI;QACpC,KAAK;YACH,OAAO;QACT;YACE,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC,IAAM,EAAE,WAAW;IAC/E;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,IACE,OAAO,IAAI,KAAK,2BAChB,OAAO,IAAI,KAAK,sBAChB,OAAO,IAAI,KAAK,uBAChB,OAAO,IAAI,KAAK,0BAChB,OAAO,IAAI,KAAK,yBAChB;QACA,OAAO,OAAO,IAAI;IACpB;IACA,OAAO,OAAO,IAAI,IAAI,OAAO,IAAI;AACnC;AAEA,MAAM,kBAAoC;IACxC;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OACE;IACJ;IACA;QACE,MAAM;QACN,OACE;IACJ;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,uBAAuB;AAC7B,MAAM,sBAAsB;AAE5B,MAAM,sBAAsB,CAC1B,eACA,SACA;IAEA,MAAM,WAA4B,EAAE;IACpC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,mBAAmB,SAAS;IAEpE,sDAAsD;IACtD,MAAM,oBAAoB,IAAI;IAC9B,IAAI,SAAS;QACX,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,CAAC,CAAC,WAAW,MAAM,GAAG;gBACxB,IAAI,OAAO,IAAI,IAAI,eAAe,OAAO,IAAI,EAAE;oBAC7C,kBAAkB,GAAG,CAAC,OAAO,IAAI,CAAC,SAAS,EAAY;gBACzD,OAAO,IAAI,OAAO,QAAQ,EAAE;oBAC1B,kBAAkB,GAAG,CAAC,OAAO,QAAQ,EAAE;gBACzC;YACF;QACF;IACF;IAEA,gBAAgB,OAAO,CAAC,CAAC;QACvB,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,KAAK,CAAC,IAAI,CAAC,cAAc,MAAM,KAAM;YAC3D,IACE,QAAQ,IAAI,KAAK,iBACjB,QAAQ,IAAI,KAAK,iBACjB,QAAQ,IAAI,KAAK,qBACjB;gBACA,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,WAAW,WAAW,GAAG,CAAC;gBAChC,MAAM,gBAAgB,WAAW,WAAW,GAAG,CAAC,QAAQ;gBACxD,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB;oBACA,SAAS,KAAK,CAAC,EAAE,IAAI;oBACrB,QAAQ,YAAY,CAAC;oBACrB,OAAO,eAAe;oBACtB,eAAe,eAAe;gBAChC;YACF,OAAO,IAAI,QAAQ,IAAI,KAAK,wBAAwB;gBAClD,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,WAAW,WAAW,GAAG,CAAC;gBAChC,MAAM,gBAAgB,WAAW,WAAW,GAAG,CAAC,QAAQ;gBACxD,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,MAAM,cAAc,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;gBAE5C,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB;oBACA,cAAc;oBACd,SAAS;oBACT,QAAQ,YAAY,CAAC;oBACrB,OAAO,eAAe;oBACtB,eAAe,eAAe;gBAChC;YACF,OAAO,IAAI,QAAQ,IAAI,KAAK,oBAAoB;gBAC9C,MAAM,gBAAgB,oBAAoB;gBAC1C,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB,YAAY,KAAK,CAAC,EAAE,KAAK;oBACzB,QAAQ,iBAAiB,CAAC;oBAC1B,YAAY,gBAAgB,kBAAkB;oBAC9C,QAAQ,gBAAgB,WAAW,YAAY,cAAc;gBAC/D;YACF,OAAO,IACL,QAAQ,IAAI,KAAK,0BACjB,QAAQ,IAAI,KAAK,yBACjB;gBACA,MAAM,WAAW,KAAK,CAAC,EAAE;gBACzB,MAAM,QAAQ,SACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI,GAAG,OAAO,CAAC,MAAM,KACxC,MAAM,CAAC;gBAEV,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB;gBACF;YACF,OAAO,IAAI,QAAQ,IAAI,KAAK,yBAAyB;gBACnD,MAAM,UAAU,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI;gBACzC,MAAM,WAAW,WAAW,GAAG,CAAC;gBAChC,MAAM,gBAAgB,WAAW,WAAW,GAAG,CAAC,2BAA2B;gBAC3E,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,MAAM,iBAAiB,SAAS,KAC9B,CAAC,IAAM,cAAc,KAAK,EAAE,QAAQ,KAAK;gBAG3C,IAAI,UAAU;gBACd,IAAI,kBAAkB,YAAY,gBAAgB;oBAChD,UAAU,wBAAwB,eAAe,MAAM;gBACzD;gBAEA,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB,SAAS;oBACT;oBACA,QAAQ,YAAY,CAAC;oBACrB,OAAO,eAAe;oBACtB,eAAe,eAAe;gBAChC;YACF,OAAO,IAAI,QAAQ,IAAI,KAAK,qBAAqB;gBAC/C,MAAM,UAAU,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI;gBACzC,MAAM,WAAW,WAAW,GAAG,CAAC;gBAChC,MAAM,gBAAgB,WAAW,WAAW,GAAG,CAAC,uBAAuB;gBACvE,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB,SAAS;oBACT,QAAQ,YAAY,CAAC;oBACrB,OAAO,eAAe;oBACtB,eAAe,eAAe;gBAChC;YACF,OAAO,IAAI,QAAQ,IAAI,KAAK,gBAAgB;gBAC1C,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB,SAAS,KAAK,CAAC,EAAE;gBACnB;YACF,OAAO,IAAI,QAAQ,IAAI,KAAK,eAAe;gBACzC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,WAAW,WAAW,GAAG,CAAC;gBAChC,MAAM,gBAAgB,WAAW,WAAW,GAAG,CAAC,QAAQ;gBACxD,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB;oBACA,QAAQ,YAAY,CAAC;oBACrB,OAAO,eAAe;oBACtB,eAAe,eAAe;gBAChC;YACF,OAAO,IAAI,QAAQ,IAAI,KAAK,qBAAqB;gBAC/C,MAAM,WAAW,WAAW,GAAG,CAAC;gBAChC,MAAM,gBAAgB,WAAW,WAAW,GAAG,CAAC,uBAAuB;gBACvE,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,SAAS,IAAI,CAAC;oBACZ,MAAM,QAAQ,IAAI;oBAClB,QAAQ,YAAY,CAAC;oBACrB,OAAO,eAAe;oBACtB,eAAe,eAAe;gBAChC;YACF;QACF;IACF;IAEA,6CAA6C;IAC7C,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;QACjC,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,OAAO,QAAQ,KAAK,0BAA0B,OAAO,IAAI,EAAE,WAAW;gBACxE,MAAM,OAAO,OAAO,IAAI,CAAC,SAAS;gBAElC,kEAAkE;gBAClE,IAAI,cAAc;gBAClB,IAAI,OAAO,IAAI,IAAI,OAAO,OAAO,IAAI,KAAK,UAAU;oBAClD,IAAI,kBAAkB,OAAO,IAAI,EAAE;wBACjC,cAAc,OAAO,IAAI,CAAC,YAAY;oBACxC,OAAO,IAAI,mBAAmB,OAAO,IAAI,EAAE;wBACzC,cAAc,OAAO,IAAI,CAAC,aAAa;oBACzC;gBACF;gBAEA,MAAM,WAAW,WAAW;gBAC5B,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBAExC,SAAS,IAAI,CAAC;oBACZ,MAAM,OAAO,QAAQ;oBACrB;oBACA,cAAc;oBACd,SAAS;oBACT,QAAQ,YAAY,CAAC;oBACrB,OAAO,WAAW,OAAO,KAAK,GAAG;oBACjC,eAAe,WAAW,OAAO,KAAK,EAAE,SAAS,cAAc,QAAQ;gBACzE;YACF,OAAO,IAAI,OAAO,QAAQ,KAAK,qBAAqB;gBAClD,MAAM,QAAQ,OAAO,IAAI,CAAC,SAAS;gBACnC,MAAM,WAAW,WAAW;gBAC5B,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBACxC,IAAI,UAAU,WAAW,OAAO,KAAK,GAAG,OAAO,MAAM;gBACrD,IAAI,UAAU;gBAEd,oCAAoC;gBACpC,IAAI,SAAS;oBACX,UAAU,wBAAwB;oBAElC,IAAI,CAAC,UAAU;wBACb,MAAM,cAAc,qBAAqB,IAAI,CAAC;wBAC9C,UAAU,cAAc,WAAW,CAAC,EAAE,GAAG,iBAAiB;oBAC5D,OAAO;wBACL,MAAM,aAAa,oBAAoB,IAAI,CAAC;wBAC5C,UAAU,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK,iBAAiB;oBACjE;gBACF;gBAEA,SAAS,IAAI,CAAC;oBACZ,MAAM,OAAO,QAAQ;oBACrB,SAAS;oBACT;oBACA,QAAQ,YAAY,CAAC;gBACvB;YACF,OAAO,IAAI,OAAO,QAAQ,KAAK,uBAAuB;gBACpD,MAAM,WAAW,WAAW;gBAC5B,MAAM,YAAY,kBAAkB,GAAG,CAAC;gBACxC,IAAI,UAAU,WAAW,OAAO,KAAK,GAAG,OAAO,MAAM;gBACrD,IAAI,UAAU;gBAEd,oCAAoC;gBACpC,IAAI,SAAS;oBACX,UAAU,wBAAwB;oBAElC,IAAI,CAAC,UAAU;wBACb,MAAM,cAAc,qBAAqB,IAAI,CAAC;wBAC9C,UAAU,cAAc,WAAW,CAAC,EAAE,GAAG,iBAAiB;oBAC5D,OAAO;wBACL,MAAM,aAAa,oBAAoB,IAAI,CAAC;wBAC5C,UAAU,aAAa,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK,iBAAiB;oBACjE;gBACF;gBAEA,SAAS,IAAI,CAAC;oBACZ,MAAM,OAAO,QAAQ;oBACrB;oBACA,QAAQ,YAAY,CAAC;gBACvB;YACF;QACF;IACF;IAEA,OAAO;AACT;AAEA,MAAM,WAA8C;IAClD;QAAE,MAAM;QAAiB,OAAO;IAA8C;IAC9E;QAAE,MAAM;QAAW,OAAO;IAAkC;IAC5D;QACE,MAAM;QACN,OAAO;IACT;CACD;AAEM,SAAS,aACd,OAAe,EACf,OAAwB,EACxB,aAA6B;IAE7B,MAAM,WAAiD,EAAE;IACzD,IAAI,QAAQ;IAEZ,SAAS,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;QAC/B,IAAI;QAEJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,MAAM,KAAM;YAC7C,IAAI,SAAS,WAAW;gBACtB,MAAM,gBAAgB,KAAK,CAAC,EAAE,CAAC,IAAI;gBACnC,MAAM,iBAAiB,oBAAoB,eAAe,SAAS;gBAEnE,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,SAAS;oBACT,UAAU;oBACV,OAAO;gBACT;YACF,OAAO;gBACL,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,SAAS,KAAK,CAAC,EAAE,CAAC,IAAI;oBACtB,OAAO;gBACT;YACF;QACF;IACF;IAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG;QACvB,IAAI,EAAE,IAAI,KAAK,iBAAiB,EAAE,IAAI,KAAK,eAAe;YACxD,OAAO,QAAQ,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,QAAQ,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC;QAC3F,OAAO,IAAI,EAAE,IAAI,KAAK,eAAe;YACnC,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC;YACvD,MAAM,SACJ,EAAE,IAAI,KAAK,YAAY,QAAQ,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;YACvF,OAAO,UAAU;QACnB,OAAO,IAAI,EAAE,IAAI,KAAK,eAAe;YACnC,MAAM,SACJ,EAAE,IAAI,KAAK,YAAY,QAAQ,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;YACvF,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC;YACvD,OAAO,SAAS;QAClB;QAEA,MAAM,OAAO,QAAQ,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1C,MAAM,OAAO,QAAQ,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1C,OAAO,OAAO;IAChB;AACF;AAEA,MAAM,oBAAoB;AAMnB,SAAS,wBAAwB,MAAc;IACpD,mDAAmD;IACnD,IAAI,CAAC,OAAO,QAAQ,CAAC,gBAAgB;QACnC,OAAO;IACT;IAEA,+DAA+D;IAC/D,+EAA+E;IAC/E,MAAM,kBAAkB,kBAAkB,IAAI,CAAC;IAC/C,IAAI,mBAAmB,eAAe,CAAC,EAAE,EAAE;QACzC,8DAA8D;QAC9D,MAAM,UAAU,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,0BAA0B;QAC3E,OAAO,QACJ,OAAO,CAAC,SAAS,MAAM,mDAAmD;SAC1E,OAAO,CAAC,QAAQ,MAAM,0BAA0B;SAChD,OAAO,CAAC,QAAQ,KAAK,+BAA+B;SACpD,OAAO,CAAC,QAAQ,MAAM,+BAA+B;IAC1D;IAEA,OAAO;AACT;AACA,MAAM,4BAA4B;AAE3B,SAAS,kBACd,MAAc;IAEd,MAAM,QAAQ,0BAA0B,IAAI,CAAC;IAC7C,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,eAAe,wBAAwB;IAE7C,OAAO;QACL,MAAM,KAAK,CAAC,EAAE;QACd,SAAS,KAAK,CAAC,EAAE,CAAC,WAAW,OAAO;QACpC,QAAQ;IACV;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI;QACF,IAAI,OAAO,IAAI,GAAG,UAAU,CAAC,QAAQ,OAAO,IAAI,GAAG,QAAQ,CAAC,MAAM;YAChE,MAAM,UAAU,KAAK,KAAK,CAAC;YAC3B,OAAO,KAAK,SAAS,CAAC,SAAS,MAAM;QACvC;QAEA,OAAO;IACT,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 3546, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/collapsible-content/sections/action-section.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Disclosure, DisclosureContent, DisclosureTrigger } from \"@/components/ui/disclosure\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useCurrentThreadStore } from \"@/stores/current-thread\";\r\nimport { ParsedCommand, ParsedSection } from \"@/types/thread-message\";\r\nimport { CheckCircleSolid, Circle, DangerTriangleSolid, FileTextSolid } from \"@mynaui/icons-react\";\r\nimport { memo, useCallback, useMemo } from \"react\";\r\nimport { getActionDisplayName, getActionIdentifier } from \"../utils\";\r\n\r\nconst ActionBadge = memo(\r\n  ({\r\n    actionType,\r\n    failed,\r\n    status,\r\n  }: {\r\n    actionType: ParsedCommand[\"type\"];\r\n    failed: boolean;\r\n    status: ParsedCommand[\"status\"];\r\n  }) => {\r\n    switch (actionType) {\r\n      case \"create_file\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : \"success\"}>\r\n            {failed ? \"Failed\" : \"Created\"}\r\n          </Badge>\r\n        );\r\n      case \"update_file\":\r\n      case \"full_file_rewrite\":\r\n      case \"update_file_sections\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : \"update\"}>{failed ? \"Failed\" : \"Updated\"}</Badge>\r\n        );\r\n      case \"delete_file\":\r\n        return <Badge variant=\"destructive\">Deleted</Badge>;\r\n      case \"open_files_in_editor\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : \"opened\"}>{failed ? \"Failed\" : \"Opened\"}</Badge>\r\n        );\r\n      case \"close_files_in_editor\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : \"closed\"}>{failed ? \"Failed\" : \"Closed\"}</Badge>\r\n        );\r\n      case \"send_terminal_command\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : \"terminal\"}>\r\n            {failed ? \"Failed\" : \"Terminal\"}\r\n          </Badge>\r\n        );\r\n      case \"reset_next_server\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : \"reset\"}>{failed ? \"Failed\" : \"Reset\"}</Badge>\r\n        );\r\n      case \"check_for_errors\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : status === \"pending\" ? \"loading\" : \"success\"}>\r\n            {failed ? \"Failed\" : status === \"pending\" ? \"Running\" : \"Passed\"}\r\n          </Badge>\r\n        );\r\n      case \"execute_sql_query\":\r\n      case \"get_database_schema\":\r\n        return (\r\n          <Badge variant={failed ? \"destructive\" : \"success\"}>\r\n            {failed ? \"Failed\" : \"Executed\"}\r\n          </Badge>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  },\r\n);\r\n\r\nActionBadge.displayName = \"ActionBadge\";\r\n\r\nconst getSummaryText = (uniqueCommands: ParsedCommand[]): string => {\r\n  const totalActions = uniqueCommands?.length;\r\n  const failedActions = uniqueCommands?.filter((cmd) => cmd.failed).length;\r\n  const serverErrors = uniqueCommands?.filter((cmd) => cmd.isServerError).length;\r\n  const pendingActions = uniqueCommands?.filter((cmd) => cmd.status === \"pending\").length;\r\n\r\n  if (pendingActions > 0) {\r\n    return `${totalActions} task${totalActions > 1 ? \"s\" : \"\"} (${pendingActions} pending)`;\r\n  }\r\n\r\n  if (serverErrors > 0) {\r\n    return `${serverErrors} server error${serverErrors > 1 ? \"s\" : \"\"}`;\r\n  }\r\n\r\n  if (failedActions > 0) {\r\n    return `${totalActions} task${totalActions > 1 ? \"s\" : \"\"} (${failedActions} failed)`;\r\n  }\r\n\r\n  return `${totalActions} task${totalActions > 1 ? \"s\" : \"\"} completed`;\r\n};\r\n\r\nconst ActionSection = ({\r\n  section,\r\n  hasFailedActions,\r\n}: {\r\n  section: ParsedSection;\r\n  hasFailedActions: boolean;\r\n}) => {\r\n  const setSelectedSectionAction = useCurrentThreadStore((state) => state.setSelectedSectionAction);\r\n\r\n  const onActionClick = useCallback(\r\n    (action: ParsedCommand) => {\r\n      setSelectedSectionAction({\r\n        section,\r\n        action,\r\n      });\r\n    },\r\n    [section],\r\n  );\r\n\r\n  const commands = useMemo(() => {\r\n    const seenCommands = new Set();\r\n\r\n    return section.commands\r\n      ?.filter(\r\n        (action) =>\r\n          action.type === \"create_file\" ||\r\n          action.type === \"update_file\" ||\r\n          action.type === \"delete_file\" ||\r\n          action.type === \"send_terminal_command\" ||\r\n          action.type === \"full_file_rewrite\" ||\r\n          action.type === \"open_files_in_editor\" ||\r\n          action.type === \"close_files_in_editor\" ||\r\n          action.type === \"update_file_sections\" ||\r\n          action.type === \"check_for_errors\" ||\r\n          action.type === \"reset_next_server\" ||\r\n          action.type === \"stop_session\" ||\r\n          action.type === \"execute_sql_query\" ||\r\n          action.type === \"get_database_schema\",\r\n      )\r\n      .reduce<ParsedCommand[]>((unique, action) => {\r\n        const key =\r\n          action.type === \"execute_sql_query\" || action.type === \"get_database_schema\"\r\n            ? `${action.type}-${action.message}`\r\n            : getActionIdentifier(action);\r\n\r\n        if (!seenCommands.has(key)) {\r\n          seenCommands.add(key);\r\n          unique.push(action);\r\n        }\r\n        return unique;\r\n      }, []);\r\n  }, [section.commands]);\r\n\r\n  if (!commands) return null;\r\n\r\n  const hasPendingActions = commands.some((action) => action.status === \"pending\");\r\n\r\n  return (\r\n    <Disclosure className=\"relative\">\r\n      {commands.length > 0 && (\r\n        <DisclosureTrigger className=\"z-40 mt-3 w-full min-w-20\">\r\n          <Card className={cn(\"w-full max-w-[26rem] shadow-sm\")}>\r\n            <CardContent className=\"flex w-full items-center justify-center gap-2 p-2 text-sm\">\r\n              <div className=\"flex w-full items-center justify-between gap-3 px-3 pr-1\">\r\n                <div className=\"flex items-center justify-start gap-2\">\r\n                  <div className=\"flex items-center justify-start gap-2\">\r\n                    <FileTextSolid className=\"size-5 text-primary\" />\r\n                    <span className=\"text-base font-medium\">View Actions</span>\r\n                  </div>\r\n                </div>\r\n                <Badge\r\n                  variant={\r\n                    hasFailedActions ? \"destructive\" : hasPendingActions ? \"loading\" : \"success\"\r\n                  }\r\n                  className=\"ml-2\"\r\n                >\r\n                  {getSummaryText(commands)}\r\n                </Badge>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </DisclosureTrigger>\r\n      )}\r\n\r\n      <DisclosureContent className=\"z-10 w-full max-w-[26rem]\">\r\n        <div className=\"pointer-events-none absolute left-8 top-0 -z-10 size-10 translate-y-[75%] border-l-2 border-primary/40 bg-background lg:left-4\" />\r\n\r\n        <Card\r\n          className={cn(\r\n            \"mt-2 w-full max-w-[26rem] border-primary/5 shadow-none focus-visible:ring-primary/10\",\r\n          )}\r\n        >\r\n          <CardContent className=\"flex w-full items-center justify-center gap-2 p-0 text-sm shadow-none\">\r\n            <div className=\"flex w-full flex-col items-start justify-between gap-2 p-2\">\r\n              {commands.map((command, idx) => (\r\n                <div\r\n                  key={`action-${idx}`}\r\n                  className=\"flex w-full cursor-pointer items-center justify-between gap-2 rounded-md p-1.5 px-2 transition-colors hover:bg-sidebar-accent/80\"\r\n                  onClick={() => onActionClick(command)}\r\n                >\r\n                  <div className=\"flex items-center gap-2 overflow-hidden\">\r\n                    {command.failed ? (\r\n                      <DangerTriangleSolid className=\"h-4 w-4 text-destructive\" />\r\n                    ) : command.status === \"pending\" ? (\r\n                      <Circle className=\"h-4 w-4 text-primary\" />\r\n                    ) : (\r\n                      <CheckCircleSolid className=\"h-4 w-4 text-emerald-500\" />\r\n                    )}\r\n                    <div className=\"flex items-center gap-2 overflow-hidden\">\r\n                      <p className=\"max-w-64 truncate font-medium\">\r\n                        {getActionDisplayName(command)}\r\n                      </p>\r\n                      {(command.type === \"close_files_in_editor\" ||\r\n                        command.type === \"open_files_in_editor\") && (\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          {` (${command.files?.length} files)`}\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex flex-col items-end\">\r\n                    <ActionBadge\r\n                      actionType={command.type}\r\n                      failed={!!command.failed}\r\n                      status={command.status}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </DisclosureContent>\r\n    </Disclosure>\r\n  );\r\n};\r\n\r\nActionSection.displayName = \"ActionSection\";\r\n\r\nexport { ActionSection };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;AAEA,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACrB,CAAC,EACC,UAAU,EACV,MAAM,EACN,MAAM,EAKP;IACC,OAAQ;QACN,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB;0BACtC,SAAS,WAAW;;;;;;QAG3B,KAAK;QACL,KAAK;QACL,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB;0BAAW,SAAS,WAAW;;;;;;QAE5E,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAc;;;;;;QACtC,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB;0BAAW,SAAS,WAAW;;;;;;QAE5E,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB;0BAAW,SAAS,WAAW;;;;;;QAE5E,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB;0BACtC,SAAS,WAAW;;;;;;QAG3B,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB;0BAAU,SAAS,WAAW;;;;;;QAE3E,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB,WAAW,YAAY,YAAY;0BACzE,SAAS,WAAW,WAAW,YAAY,YAAY;;;;;;QAG9D,KAAK;QACL,KAAK;YACH,qBACE,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,SAAS,gBAAgB;0BACtC,SAAS,WAAW;;;;;;QAG3B;YACE,OAAO;IACX;AACF;AAGF,YAAY,WAAW,GAAG;AAE1B,MAAM,iBAAiB,CAAC;IACtB,MAAM,eAAe,gBAAgB;IACrC,MAAM,gBAAgB,gBAAgB,OAAO,CAAC,MAAQ,IAAI,MAAM,EAAE;IAClE,MAAM,eAAe,gBAAgB,OAAO,CAAC,MAAQ,IAAI,aAAa,EAAE;IACxE,MAAM,iBAAiB,gBAAgB,OAAO,CAAC,MAAQ,IAAI,MAAM,KAAK,WAAW;IAEjF,IAAI,iBAAiB,GAAG;QACtB,OAAO,GAAG,aAAa,KAAK,EAAE,eAAe,IAAI,MAAM,GAAG,EAAE,EAAE,eAAe,SAAS,CAAC;IACzF;IAEA,IAAI,eAAe,GAAG;QACpB,OAAO,GAAG,aAAa,aAAa,EAAE,eAAe,IAAI,MAAM,IAAI;IACrE;IAEA,IAAI,gBAAgB,GAAG;QACrB,OAAO,GAAG,aAAa,KAAK,EAAE,eAAe,IAAI,MAAM,GAAG,EAAE,EAAE,cAAc,QAAQ,CAAC;IACvF;IAEA,OAAO,GAAG,aAAa,KAAK,EAAE,eAAe,IAAI,MAAM,GAAG,UAAU,CAAC;AACvE;AAEA,MAAM,gBAAgB,CAAC,EACrB,OAAO,EACP,gBAAgB,EAIjB;IACC,MAAM,2BAA2B,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,QAAU,MAAM,wBAAwB;IAEhG,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,yBAAyB;YACvB;YACA;QACF;IACF,GACA;QAAC;KAAQ;IAGX,MAAM,WAAW,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QACvB,MAAM,eAAe,IAAI;QAEzB,OAAO,QAAQ,QAAQ,EACnB,OACA,CAAC,SACC,OAAO,IAAI,KAAK,iBAChB,OAAO,IAAI,KAAK,iBAChB,OAAO,IAAI,KAAK,iBAChB,OAAO,IAAI,KAAK,2BAChB,OAAO,IAAI,KAAK,uBAChB,OAAO,IAAI,KAAK,0BAChB,OAAO,IAAI,KAAK,2BAChB,OAAO,IAAI,KAAK,0BAChB,OAAO,IAAI,KAAK,sBAChB,OAAO,IAAI,KAAK,uBAChB,OAAO,IAAI,KAAK,kBAChB,OAAO,IAAI,KAAK,uBAChB,OAAO,IAAI,KAAK,uBAEnB,OAAwB,CAAC,QAAQ;YAChC,MAAM,MACJ,OAAO,IAAI,KAAK,uBAAuB,OAAO,IAAI,KAAK,wBACnD,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,GAClC,CAAA,GAAA,6LAAA,CAAA,sBAAmB,AAAD,EAAE;YAE1B,IAAI,CAAC,aAAa,GAAG,CAAC,MAAM;gBAC1B,aAAa,GAAG,CAAC;gBACjB,OAAO,IAAI,CAAC;YACd;YACA,OAAO;QACT,GAAG,EAAE;IACT,GAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,oBAAoB,SAAS,IAAI,CAAC,CAAC,SAAW,OAAO,MAAM,KAAK;IAEtE,qBACE,6VAAC,sIAAA,CAAA,aAAU;QAAC,WAAU;;YACnB,SAAS,MAAM,GAAG,mBACjB,6VAAC,sIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC3B,cAAA,6VAAC,gIAAA,CAAA,OAAI;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;8BAClB,cAAA,6VAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,qUAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6VAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;8CAG5C,6VAAC,iIAAA,CAAA,QAAK;oCACJ,SACE,mBAAmB,gBAAgB,oBAAoB,YAAY;oCAErE,WAAU;8CAET,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,6VAAC,sIAAA,CAAA,oBAAiB;gBAAC,WAAU;;kCAC3B,6VAAC;wBAAI,WAAU;;;;;;kCAEf,6VAAC,gIAAA,CAAA,OAAI;wBACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kCAGF,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6VAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,oBACtB,6VAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,6VAAC;gDAAI,WAAU;;oDACZ,QAAQ,MAAM,iBACb,6VAAC,iVAAA,CAAA,sBAAmB;wDAAC,WAAU;;;;;+DAC7B,QAAQ,MAAM,KAAK,0BACrB,6VAAC,8SAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6VAAC,2UAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;kEAE9B,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,6LAAA,CAAA,uBAAoB,AAAD,EAAE;;;;;;4DAEvB,CAAC,QAAQ,IAAI,KAAK,2BACjB,QAAQ,IAAI,KAAK,sBAAsB,mBACvC,6VAAC;gEAAE,WAAU;0EACV,CAAC,EAAE,EAAE,QAAQ,KAAK,EAAE,OAAO,OAAO,CAAC;;;;;;;;;;;;;;;;;;0DAK5C,6VAAC;gDAAI,WAAU;0DACb,cAAA,6VAAC;oDACC,YAAY,QAAQ,IAAI;oDACxB,QAAQ,CAAC,CAAC,QAAQ,MAAM;oDACxB,QAAQ,QAAQ,MAAM;;;;;;;;;;;;uCA5BrB,CAAC,OAAO,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCtC;AAEA,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3906, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/collapsible-content/sections/section.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\";\r\nimport { Disclosure, DisclosureContent, DisclosureTrigger } from \"@/components/ui/disclosure\";\r\nimport { MessageMarkdown } from \"@/components/ui/message-markdown\";\r\nimport { ParsedSection } from \"@/types/thread-message\";\r\nimport { CheckCircleSolid, ChevronRight, DangerCircleSolid } from \"@mynaui/icons-react\";\r\nimport { formatToolOutput, getUserFriendlyActionType, ParsedToolResult } from \"../utils\";\r\nimport { ActionSection } from \"./action-section\";\r\n\r\nexport const Section = ({\r\n  section,\r\n  hasFailedActions,\r\n}: {\r\n  section: ParsedSection | ParsedToolResult;\r\n  hasFailedActions: boolean;\r\n}) => {\r\n  if (section.type === \"continue_instructions\") {\r\n    return null;\r\n  }\r\n\r\n  if (section.type === \"assistant\") {\r\n    return (\r\n      <div className=\"w-full rounded-lg\">\r\n        <MessageMarkdown>{section.content}</MessageMarkdown>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (section.type === \"tool_result\") {\r\n    return (\r\n      <Disclosure>\r\n        <div className=\"my-2 mt-3 flex w-full min-w-20 flex-col justify-between rounded-xl border border-primary/10 bg-sidebar-accent/95 transition-all duration-200 hover:border-primary/10 hover:bg-sidebar-accent md:max-w-[26rem]\">\r\n          <div className=\"flex items-center px-3\">\r\n            <DisclosureTrigger className=\"group flex w-full items-center justify-start rounded-xl\">\r\n              <ChevronRight className=\"h-4 w-4 text-muted-foreground transition-transform duration-200 group-data-[state=open]:rotate-90\" />\r\n              <div className=\"flex w-full items-center justify-between gap-2 bg-transparent px-2 py-1 pr-0 hover:bg-transparent group-data-[state=open]:pb-1.5\">\r\n                <span className=\"font-medium\">{getUserFriendlyActionType(section.type)}</span>\r\n                <Badge variant={section.success ? \"success\" : \"destructive\"}>\r\n                  {section.success ? \"Success\" : \"Failed\"}\r\n                </Badge>\r\n              </div>\r\n            </DisclosureTrigger>\r\n          </div>\r\n          <DisclosureContent className=\"w-full rounded-b-lg border-t border-none border-primary/10 bg-background px-3 md:max-w-[26rem]\">\r\n            <div className=\"flex items-center gap-4 py-1\">\r\n              <div className=\"flex flex-1 items-center gap-2\">\r\n                {section.success ? (\r\n                  <CheckCircleSolid className=\"h-4 w-4 text-muted-foreground\" />\r\n                ) : (\r\n                  <DangerCircleSolid className=\"h-4 w-4 text-destructive\" />\r\n                )}\r\n                <div className=\"max-w-64 overflow-x-auto whitespace-pre-wrap text-sm text-primary/80 hover:text-primary\">\r\n                  {formatToolOutput(section.output)}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </DisclosureContent>\r\n        </div>\r\n      </Disclosure>\r\n    );\r\n  }\r\n\r\n  if (section.type === \"actions\" && section?.commands) {\r\n    return <ActionSection section={section} hasFailedActions={hasFailedActions} />;\r\n  }\r\n\r\n  // keep to debug\r\n  return <div data-content=\"Section content can't be displayed\"></div>;\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;;;;;;;;AAEO,MAAM,UAAU,CAAC,EACtB,OAAO,EACP,gBAAgB,EAIjB;IACC,IAAI,QAAQ,IAAI,KAAK,yBAAyB;QAC5C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa;QAChC,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC,+IAAA,CAAA,kBAAe;0BAAE,QAAQ,OAAO;;;;;;;;;;;IAGvC;IAEA,IAAI,QAAQ,IAAI,KAAK,eAAe;QAClC,qBACE,6VAAC,sIAAA,CAAA,aAAU;sBACT,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,sIAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,6VAAC,0TAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAK,WAAU;sDAAe,CAAA,GAAA,6LAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,IAAI;;;;;;sDACrE,6VAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,QAAQ,OAAO,GAAG,YAAY;sDAC3C,QAAQ,OAAO,GAAG,YAAY;;;;;;;;;;;;;;;;;;;;;;;kCAKvC,6VAAC,sIAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;oCACZ,QAAQ,OAAO,iBACd,6VAAC,2UAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;6DAE5B,6VAAC,6UAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;kDAE/B,6VAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQhD;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,SAAS,UAAU;QACnD,qBAAO,6VAAC,sNAAA,CAAA,gBAAa;YAAC,SAAS;YAAS,kBAAkB;;;;;;IAC5D;IAEA,gBAAgB;IAChB,qBAAO,6VAAC;QAAI,gBAAa;;;;;;AAC3B", "debugId": null}}, {"offset": {"line": 4079, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/collapsible-content/content/tool-content.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\";\r\nimport { Disclosure, DisclosureContent, DisclosureTrigger } from \"@/components/ui/disclosure\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronRight } from \"@mynaui/icons-react\";\r\nimport { memo, useMemo } from \"react\";\r\nimport { formatToolOutput, ParsedToolResult } from \"../utils\";\r\n\r\n// Pre-compiled regex for better performance\r\nconst TOOL_RESULT_REGEX = /(\\w+): ToolResult\\(success=(True|False), output='([^']+)'\\)/g;\r\n\r\nconst unescapeOutput = (str: string) =>\r\n  str.replace(/\\\\([n'\"])/g, (_, char) => (char === \"n\" ? \"\\n\" : char));\r\n\r\nconst ToolContent = ({ content }: { content: string }) => {\r\n  const toolResults = useMemo(() => {\r\n    const results: ParsedToolResult[] = [];\r\n    let index = 0;\r\n    let toolMatch;\r\n\r\n    while ((toolMatch = TOOL_RESULT_REGEX.exec(content)) !== null) {\r\n      const toolName = toolMatch[1];\r\n      const success = toolMatch[2] === \"True\";\r\n      const output = unescapeOutput(toolMatch[3]);\r\n\r\n      results.push({\r\n        type: \"tool_result\",\r\n        name: toolName,\r\n        success,\r\n        output,\r\n        index: index++,\r\n      });\r\n    }\r\n\r\n    return results;\r\n  }, [content]);\r\n\r\n  return (\r\n    <div className=\"w-full space-y-2 text-[0.9rem]\">\r\n      {toolResults.map((result) => (\r\n        <Disclosure key={`tool-result-${result.index}`}>\r\n          <div className=\"my-2 flex w-full min-w-20 flex-col justify-between rounded-lg border border-primary/10 bg-sidebar-accent/80 ring-1 ring-primary/10 transition-all duration-200 hover:border-primary/10 hover:bg-sidebar-accent/95 md:max-w-[26rem]\">\r\n            <div className=\"flex items-center px-3\">\r\n              <DisclosureTrigger className=\"group flex w-full items-center justify-start rounded-sm focus:outline-none\">\r\n                <ChevronRight className=\"h-4 w-4 text-muted-foreground transition-transform duration-200 group-data-[state=open]:rotate-90\" />\r\n                <div className=\"flex w-full items-center justify-between gap-2 bg-transparent px-2 py-1 pr-0 hover:bg-transparent group-data-[state=open]:pb-1.5\">\r\n                  <span className=\"font-medium\">{result.name}</span>\r\n                  <Badge variant={result.success ? \"success\" : \"destructive\"}>\r\n                    {result.success ? \"Success\" : \"Failed\"}\r\n                  </Badge>\r\n                </div>\r\n              </DisclosureTrigger>\r\n            </div>\r\n            <DisclosureContent\r\n              className={cn(\r\n                \"rounded-b-lg border-t border-none border-primary/10 bg-background px-3\",\r\n                \"w-full md:max-w-[26rem]\",\r\n              )}\r\n            >\r\n              <div className=\"space-y-2 py-2\">\r\n                <div className=\"whitespace-pre-wrap text-sm text-muted-foreground\">\r\n                  {formatToolOutput(result.output)}\r\n                </div>\r\n              </div>\r\n            </DisclosureContent>\r\n          </div>\r\n        </Disclosure>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(ToolContent);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,4CAA4C;AAC5C,MAAM,oBAAoB;AAE1B,MAAM,iBAAiB,CAAC,MACtB,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,OAAU,SAAS,MAAM,OAAO;AAEhE,MAAM,cAAc,CAAC,EAAE,OAAO,EAAuB;IACnD,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,MAAM,UAA8B,EAAE;QACtC,IAAI,QAAQ;QACZ,IAAI;QAEJ,MAAO,CAAC,YAAY,kBAAkB,IAAI,CAAC,QAAQ,MAAM,KAAM;YAC7D,MAAM,WAAW,SAAS,CAAC,EAAE;YAC7B,MAAM,UAAU,SAAS,CAAC,EAAE,KAAK;YACjC,MAAM,SAAS,eAAe,SAAS,CAAC,EAAE;YAE1C,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,MAAM;gBACN;gBACA;gBACA,OAAO;YACT;QACF;QAEA,OAAO;IACT,GAAG;QAAC;KAAQ;IAEZ,qBACE,6VAAC;QAAI,WAAU;kBACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6VAAC,sIAAA,CAAA,aAAU;0BACT,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,sIAAA,CAAA,oBAAiB;gCAAC,WAAU;;kDAC3B,6VAAC,0TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAK,WAAU;0DAAe,OAAO,IAAI;;;;;;0DAC1C,6VAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,OAAO,OAAO,GAAG,YAAY;0DAC1C,OAAO,OAAO,GAAG,YAAY;;;;;;;;;;;;;;;;;;;;;;;sCAKtC,6VAAC,sIAAA,CAAA,oBAAiB;4BAChB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA;sCAGF,cAAA,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;eArBxB,CAAC,YAAY,EAAE,OAAO,KAAK,EAAE;;;;;;;;;;AA8BtD;qDAEe,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 4220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/collapsible-content/content/content.tsx"], "sourcesContent": ["import { ActionError, ActionResult } from \"@/types/thread-message\";\r\nimport { useMemo } from \"react\";\r\nimport { Section } from \"../sections/section\";\r\nimport { parseContent } from \"../utils\";\r\nimport ToolContent from \"./tool-content\";\r\n\r\nexport const CollapsibleXmlContent = ({\r\n  content,\r\n  failedActions,\r\n  actions,\r\n}: {\r\n  content: string;\r\n  failedActions?: ActionError[];\r\n  actions?: ActionResult[];\r\n}) => {\r\n  const parsedSections = useMemo(() => {\r\n    return parseContent(content, actions, failedActions);\r\n  }, [actions, content, failedActions]);\r\n\r\n  const hasXMLTags = parsedSections.some(\r\n    (section) => section.type === \"actions\" || section.type === \"tool_result\",\r\n  );\r\n\r\n  const hasToolResults = content.includes(\"ToolResult(success=\") && !hasXMLTags;\r\n\r\n  if (hasToolResults) {\r\n    return <ToolContent content={content} />;\r\n  }\r\n\r\n  const hasFailedActions =\r\n    (!!failedActions && failedActions.length > 0) ||\r\n    (!!actions && actions.some((action) => \"error\" in action));\r\n\r\n  return (\r\n    <div className=\"w-full space-y-2 text-[0.9rem]\">\r\n      {parsedSections.map((section) => (\r\n        <Section\r\n          key={`section-${section.index}`}\r\n          section={section}\r\n          hasFailedActions={hasFailedActions}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,wBAAwB,CAAC,EACpC,OAAO,EACP,aAAa,EACb,OAAO,EAKR;IACC,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,CAAA,GAAA,6LAAA,CAAA,eAAY,AAAD,EAAE,SAAS,SAAS;IACxC,GAAG;QAAC;QAAS;QAAS;KAAc;IAEpC,MAAM,aAAa,eAAe,IAAI,CACpC,CAAC,UAAY,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK;IAG9D,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,0BAA0B,CAAC;IAEnE,IAAI,gBAAgB;QAClB,qBAAO,6VAAC,mNAAA,CAAA,UAAW;YAAC,SAAS;;;;;;IAC/B;IAEA,MAAM,mBACJ,AAAC,CAAC,CAAC,iBAAiB,cAAc,MAAM,GAAG,KAC1C,CAAC,CAAC,WAAW,QAAQ,IAAI,CAAC,CAAC,SAAW,WAAW;IAEpD,qBACE,6VAAC;QAAI,WAAU;kBACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,6VAAC,4MAAA,CAAA,UAAO;gBAEN,SAAS;gBACT,kBAAkB;eAFb,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 4275, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/reasoning-content.tsx"], "sourcesContent": ["import {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { ThinkingMarkdown } from \"@/components/ui/thinking-markdown\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronRight } from \"@mynaui/icons-react\";\r\nimport { Brain } from \"lucide-react\";\r\n\r\ninterface ReasoningContentProps {\r\n  collapsed?: boolean;\r\n  content: string;\r\n  isInProgress?: boolean;\r\n}\r\n\r\nexport default function ReasoningContent({\r\n  collapsed: initialCollapsed = true,\r\n  content,\r\n  isInProgress = false,\r\n}: ReasoningContentProps) {\r\n  return (\r\n    <div className=\"mb-3 w-full\">\r\n      <Accordion className=\"relative\" expandedValue={initialCollapsed ? undefined : \"item-1\"}>\r\n        <AccordionItem\r\n          value=\"item-1\"\r\n          className={cn(\r\n            \"mt-3 w-full rounded-md border border-input\",\r\n            isInProgress && \"border-chart-2/50 ring-2 ring-chart-2/30\",\r\n          )}\r\n        >\r\n          <AccordionTrigger className=\"w-full min-w-20 border-none bg-transparent shadow-none\">\r\n            <Card\r\n              className={cn(\"w-full border-none bg-transparent p-3 shadow-none\")}\r\n              border={false}\r\n            >\r\n              <div className=\"group flex items-center justify-start gap-2 transition-all duration-700 ease-in-out\">\r\n                <ChevronRight className=\"hidden size-4 text-primary transition-all duration-300 ease-in-out group-hover:flex group-data-[expanded]:rotate-90\" />\r\n                <Brain\r\n                  className={cn(\r\n                    \"size-4\",\r\n                    isInProgress\r\n                      ? \"animate-pulse text-primary\"\r\n                      : \"flex text-muted-foreground group-hover:hidden\",\r\n                  )}\r\n                />\r\n                <span className=\"text-sm font-medium text-primary\">\r\n                  {isInProgress ? \"Thinking...\" : \"Thinking\"}\r\n                </span>\r\n              </div>\r\n            </Card>\r\n          </AccordionTrigger>\r\n\r\n          <AccordionContent className=\"ml-9\">\r\n            <div className=\"relative\">\r\n              <Card\r\n                className={cn(\"relative w-[95%] border-none bg-transparent pb-2 shadow-none\")}\r\n                border={false}\r\n              >\r\n                <CardContent\r\n                  className=\"flex w-full items-start justify-start gap-2 overflow-y-auto p-0 text-sm shadow-none\"\r\n                  style={{\r\n                    scrollbarWidth: \"none\",\r\n                  }}\r\n                >\r\n                  <ThinkingMarkdown>{content}</ThinkingMarkdown>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </Accordion>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;AACA;AACA;AACA;AACA;;;;;;;;AAQe,SAAS,iBAAiB,EACvC,WAAW,mBAAmB,IAAI,EAClC,OAAO,EACP,eAAe,KAAK,EACE;IACtB,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,qIAAA,CAAA,YAAS;YAAC,WAAU;YAAW,eAAe,mBAAmB,YAAY;sBAC5E,cAAA,6VAAC,qIAAA,CAAA,gBAAa;gBACZ,OAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,gBAAgB;;kCAGlB,6VAAC,qIAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAC1B,cAAA,6VAAC,gIAAA,CAAA,OAAI;4BACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;4BACd,QAAQ;sCAER,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,0TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6VAAC,wRAAA,CAAA,QAAK;wCACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,UACA,eACI,+BACA;;;;;;kDAGR,6VAAC;wCAAK,WAAU;kDACb,eAAe,gBAAgB;;;;;;;;;;;;;;;;;;;;;;kCAMxC,6VAAC,qIAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAC1B,cAAA,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,gIAAA,CAAA,OAAI;gCACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;gCACd,QAAQ;0CAER,cAAA,6VAAC,gIAAA,CAAA,cAAW;oCACV,WAAU;oCACV,OAAO;wCACL,gBAAgB;oCAClB;8CAEA,cAAA,6VAAC,gJAAA,CAAA,mBAAgB;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC", "debugId": null}}, {"offset": {"line": 4410, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/supabase-status-card.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport SupabaseIcon from \"@/features/icon/supabase\";\r\nimport SupabaseSheet from \"@/features/project/modal/supabase-sheet\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useSettingsStore } from \"@/stores/settings-tab\";\r\nimport { Suspense, useMemo, useState } from \"react\";\r\n\r\nconst SupabaseStatusCard = () => {\r\n  const { user } = useAuth();\r\n  const isFreeTier = useMemo(() => user?.userFromDb?.plan === \"free-tier\", [user]);\r\n  const [showSupabaseSheet, setShowSupabaseSheet] = useState(false);\r\n\r\n  const setSettingsTab = useSettingsStore((state) => state.setSettingsTab);\r\n\r\n  return (\r\n    <Card\r\n      className={cn(\r\n        \"my-1.5 mt-2 flex h-12 w-full max-w-[26rem] items-center justify-center bg-primary px-4 pr-2 shadow-sm\",\r\n      )}\r\n    >\r\n      <CardContent className=\"flex w-full items-center justify-center gap-2 p-3 px-1 text-sm\">\r\n        <div className=\"relative flex h-full w-full items-center justify-start gap-2 overflow-hidden\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <SupabaseIcon className=\"size-5\" />\r\n          </div>\r\n          <div className=\"flex w-full flex-col items-start justify-start gap-1 pt-0\">\r\n            <div className=\"flex w-full items-center justify-between gap-2\">\r\n              <span className=\"text-base font-medium text-background\">Setup Supabase</span>\r\n\r\n              {isFreeTier ? (\r\n                <Button\r\n                  className=\"bg-background/95 text-primary hover:bg-background\"\r\n                  onClick={() => setSettingsTab(\"supabase\")}\r\n                >\r\n                  Upgrade Now\r\n                </Button>\r\n              ) : (\r\n                <Button\r\n                  className=\"bg-background/95 text-primary hover:bg-background\"\r\n                  onClick={() => setShowSupabaseSheet(true)}\r\n                >\r\n                  Connect\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n\r\n      {!isFreeTier && (\r\n        <Suspense fallback={null}>\r\n          <SupabaseSheet isOpen={showSupabaseSheet} onClose={() => setShowSupabaseSheet(false)} />\r\n        </Suspense>\r\n      )}\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default SupabaseStatusCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,qBAAqB;IACzB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,YAAY,SAAS,aAAa;QAAC;KAAK;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,cAAc;IAEvE,qBACE,6VAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;0BAGF,6VAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,oIAAA,CAAA,UAAY;gCAAC,WAAU;;;;;;;;;;;sCAE1B,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAK,WAAU;kDAAwC;;;;;;oCAEvD,2BACC,6VAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe;kDAC/B;;;;;6DAID,6VAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS,IAAM,qBAAqB;kDACrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASV,CAAC,4BACA,6VAAC,oTAAA,CAAA,WAAQ;gBAAC,UAAU;0BAClB,cAAA,6VAAC,yJAAA,CAAA,UAAa;oBAAC,QAAQ;oBAAmB,SAAS,IAAM,qBAAqB;;;;;;;;;;;;;;;;;AAKxF;uCAEe", "debugId": null}}, {"offset": {"line": 4541, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/tool-result.tsx"], "sourcesContent": ["import { Label } from \"@/components/ui/label\";\r\nimport TaskStatus from \"@/features/global/task-status\";\r\nimport { ChevronRightSquareSolid } from \"@mynaui/icons-react\";\r\n\r\ntype Props = {\r\n  toolresult: { type: string; success: boolean; output: string };\r\n};\r\n\r\nconst ToolResult = ({ toolresult: { success, output } }: Props) => (\r\n  <TaskStatus variant={success ? \"success\" : \"error\"}>\r\n    <div className=\"flex items-center gap-2\">\r\n      <ChevronRightSquareSolid className=\"size-5\" />\r\n      <Label className=\"text-base font-medium\">{output}</Label>\r\n    </div>\r\n  </TaskStatus>\r\n);\r\n\r\nexport default ToolResult;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAMA,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,EAAS,iBAC5D,6VAAC,4IAAA,CAAA,UAAU;QAAC,SAAS,UAAU,YAAY;kBACzC,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC,yVAAA,CAAA,0BAAuB;oBAAC,WAAU;;;;;;8BACnC,6VAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAyB;;;;;;;;;;;;;;;;;uCAKjC", "debugId": null}}, {"offset": {"line": 4590, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/assistant-message.tsx"], "sourcesContent": ["import {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/classic/dropdown-menu\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Hint from \"@/components/ui/hint\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { MessageMarkdown } from \"@/components/ui/message-markdown\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { errorToast, loadingToast, successToast } from \"@/features/global/toast\";\r\nimport { useCopy } from \"@/hooks/use-copy\";\r\nimport { revertGithubCommit } from \"@/lib/api\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { parseCommunication } from \"@/lib/xml-parser\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { DotsVertical, Refresh } from \"@mynaui/icons-react\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { memo, useCallback, useMemo, useState } from \"react\";\r\nimport { LuCopy } from \"react-icons/lu\";\r\nimport { formatTimestamp } from \"../../../../lib/format-timestamp\";\r\nimport SoftgenIcon from \"../softgen-icon\";\r\nimport { CollapsibleXmlContent } from \"./collapsible-content/content/content\";\r\nimport { extractToolResult, parseContent } from \"./collapsible-content/utils\";\r\nimport ReasoningContent from \"./reasoning-content\";\r\nimport SupabaseStatusCard from \"./supabase-status-card\";\r\nimport ToolResult from \"./tool-result\";\r\nimport { MessageProps } from \"./types\";\r\n\r\nconst TOOL_CALL_REGEX = /<(tool_call|function_call)/;\r\n\r\nconst TOKEN_THRESHOLDS = [\r\n  {\r\n    min: 0,\r\n    max: 80000,\r\n    color: \"bg-green-500\",\r\n    label: \"Safe\",\r\n    message: \"Context is in the safe range.\",\r\n  },\r\n  {\r\n    min: 80000,\r\n    max: 99000,\r\n    color: \"bg-yellow-500\",\r\n    label: \"Warning!\",\r\n    message: \"High context usage - think about closing files, or preparing to start a new thread\",\r\n  },\r\n  {\r\n    min: 99000,\r\n    max: 119000,\r\n    color: \"bg-orange-500\",\r\n    label: \"Alert!\",\r\n    message:\r\n      \"Context pressure rising; AI output quality may degrade. Close unnecessary files now, or start a new thread\",\r\n  },\r\n  {\r\n    min: 119000,\r\n    max: Infinity,\r\n    color: \"bg-red-500\",\r\n    label: \"Danger\",\r\n    message:\r\n      \"Context window likely too full — AI may hallucinate or break. Strongly recommend closing files or starting a new task/thread.\",\r\n  },\r\n] as const;\r\n\r\nconst TokenIndicator = memo(\r\n  ({ tokens, hasFailedActions }: { tokens: number; hasFailedActions: boolean }) => {\r\n    const indicator = useMemo(() => {\r\n      if (hasFailedActions) {\r\n        return {\r\n          color: \"bg-green-500\",\r\n          label: \"Auto Fix\",\r\n          message: \"No tokens charged due to failed actions.\",\r\n        };\r\n      }\r\n      return (\r\n        TOKEN_THRESHOLDS.find((threshold) => tokens >= threshold.min && tokens <= threshold.max) ||\r\n        TOKEN_THRESHOLDS[TOKEN_THRESHOLDS.length - 1]\r\n      );\r\n    }, [tokens, hasFailedActions]);\r\n\r\n    const formattedMessage = useMemo(() => {\r\n      if (hasFailedActions) {\r\n        return `${indicator.label} - ${indicator.message}`;\r\n      }\r\n      const separator = indicator.label === \"Safe\" ? \" - \" : \" \";\r\n      return `${indicator.label}${separator}${indicator.message}`;\r\n    }, [indicator, hasFailedActions]);\r\n\r\n    return (\r\n      <Hint\r\n        side=\"bottom\"\r\n        sideOffset={-2}\r\n        align=\"end\"\r\n        content={\r\n          <div className=\"flex flex-col gap-2\">\r\n            <Typography.P className=\"mt-0 text-sm text-inherit\">{formattedMessage}</Typography.P>\r\n          </div>\r\n        }\r\n      >\r\n        <div className=\"flex h-7 cursor-default items-center gap-1.5 text-sm font-medium text-primary\">\r\n          <div className=\"relative\">\r\n            <div className={`size-2.5 rounded-full ${indicator.color} animate-pulse`} />\r\n            <div\r\n              className={`absolute inset-0 rounded-full ${indicator.color} opacity-50 blur-sm`}\r\n            />\r\n          </div>\r\n          <p className=\"text-sm font-medium text-primary transition-all duration-300 ease-in-out group-hover:text-primary/80\">\r\n            {hasFailedActions ? \"Free\" : tokens === 0 ? \"Auto Fix\" : tokens.toString()}\r\n          </p>\r\n        </div>\r\n      </Hint>\r\n    );\r\n  },\r\n);\r\n\r\nTokenIndicator.displayName = \"TokenIndicator\";\r\n\r\nconst RevertBtn = ({\r\n  message,\r\n  actions,\r\n}: {\r\n  message: MessageProps[\"message\"];\r\n  actions: MessageProps[\"actions\"];\r\n}) => {\r\n  const { copy } = useCopy();\r\n  const { projectId } = useProject();\r\n  const [showDeleteAlert, setShowDeleteAlert] = useState(false);\r\n\r\n  const parsedSections = useMemo(() => {\r\n    return parseContent(message.content as string, actions, message.failed_actions);\r\n  }, [actions, message.content, message.failed_actions]);\r\n\r\n  const revertCommitFn = useMutation({\r\n    mutationFn: async (): Promise<void> => {\r\n      await loadingToast(\r\n        \"Reverting commit...\",\r\n        revertGithubCommit(projectId, message.commit_hash || \"\"),\r\n        {},\r\n        \"Commit reverted\",\r\n      );\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error reverting commit\", error);\r\n      errorToast(\"Failed to revert commit\", {\r\n        description: error.message,\r\n      });\r\n    },\r\n  });\r\n\r\n  const onRevertClick = useCallback(() => {\r\n    setShowDeleteAlert(false);\r\n    revertCommitFn.mutate();\r\n  }, [revertCommitFn]);\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"ghost\" size=\"icon\" className=\"size-7\">\r\n          <DotsVertical className=\"size-6 rotate-90\" />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\">\r\n        <DropdownMenuItem\r\n          className=\"group flex items-center justify-between gap-2 px-2.5\"\r\n          onClick={() =>\r\n            copy(parsedSections[0]?.type === \"communication\" ? parsedSections[0].content : \"\")\r\n              .then(() => {\r\n                successToast(\"Copied to clipboard\");\r\n              })\r\n              .catch(() => {\r\n                errorToast(\"Failed to copy\");\r\n              })\r\n          }\r\n        >\r\n          Copy\r\n          <LuCopy className=\"h-4 w-4 text-primary/85 group-hover:text-primary\" />\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem\r\n          onClick={() => setShowDeleteAlert(true)}\r\n          className=\"group flex items-center justify-between gap-2 px-2.5\"\r\n        >\r\n          Revert\r\n          <Refresh className=\"h-4 w-4 text-primary/85 group-hover:text-primary\" />\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Are you sure you want to revert this commit?</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              This action cannot be undone. This will revert the commit to the previous state.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter className=\"flex w-full flex-row gap-2 md:gap-0\">\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"w-full md:w-fit\"\r\n              disabled={revertCommitFn.isPending}\r\n              onClick={() => setShowDeleteAlert(false)}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              variant=\"destructive\"\r\n              className=\"w-full md:w-fit\"\r\n              onClick={onRevertClick}\r\n              disabled={revertCommitFn.isPending}\r\n            >\r\n              {revertCommitFn.isPending ? (\r\n                <>\r\n                  <Loading className=\"size-4 animate-spin\" />\r\n                  Reverting...\r\n                </>\r\n              ) : (\r\n                \"Revert\"\r\n              )}\r\n            </Button>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </DropdownMenu>\r\n  );\r\n};\r\n\r\nexport const AssistantMessage = ({ actions, message }: MessageProps) => {\r\n  const { project } = useProject();\r\n  const hasToolCalls =\r\n    message.hasToolCalls === true ||\r\n    (typeof message.content === \"string\" && TOOL_CALL_REGEX.test(message.content));\r\n\r\n  const toolresult = useMemo(() => extractToolResult(message.content as string), [message.content]);\r\n\r\n  const communication = useMemo(\r\n    () => parseCommunication(message.content as string)?.communication,\r\n    [message.content],\r\n  );\r\n\r\n  return (\r\n    <div className=\"group flex w-full flex-col space-y-2 pb-10 pt-0\">\r\n      <div className=\"flex w-full flex-col items-start\">\r\n        <div className=\"mb-1.5 flex w-full items-center justify-between gap-2\">\r\n          <div className=\"flex items-center gap-4\">\r\n            <SoftgenIcon />\r\n\r\n            {message.timestamp && (\r\n              <p className=\"hidden text-sm text-muted-foreground transition-all duration-1000 ease-in-out group-hover:flex\">\r\n                {formatTimestamp(message.timestamp)}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-3\">\r\n            <div className=\" \">\r\n              {message.total_tokens !== undefined && (\r\n                <TokenIndicator\r\n                  tokens={message.total_tokens}\r\n                  hasFailedActions={!!(message.failed_actions && message.failed_actions.length > 0)}\r\n                />\r\n              )}\r\n            </div>\r\n\r\n            {message.commit_hash && <RevertBtn message={message} actions={actions} />}\r\n          </div>\r\n        </div>\r\n\r\n        <div\r\n          className={cn(\r\n            \"prose dark:prose-invert prose-p:whitespace-break-spaces prose-code:m-0 prose-code:whitespace-break-spaces prose-pre:m-0 prose-pre:w-full prose-pre:p-0 dark:prose-pre:bg-secondary w-full max-w-full select-text gap-1 overflow-x-visible text-wrap break-words py-0.5 font-normal\",\r\n            message.isStreaming && \"animate-pulse\",\r\n            hasToolCalls && \"bg-muted/50\",\r\n          )}\r\n        >\r\n          {message.reasoning_content && <ReasoningContent content={message.reasoning_content} />}\r\n\r\n          {typeof message.content === \"string\" && (\r\n            <>\r\n              {message.content.includes(\"<\") && message.content.includes(\">\") ? (\r\n                <>\r\n                  {communication && <MessageMarkdown>{communication}</MessageMarkdown>}\r\n\r\n                  <CollapsibleXmlContent\r\n                    content={message.content}\r\n                    actions={actions}\r\n                    failedActions={message.failed_actions}\r\n                  />\r\n                </>\r\n              ) : toolresult ? (\r\n                <>\r\n                  <ToolResult toolresult={toolresult} />\r\n                </>\r\n              ) : (\r\n                <MessageMarkdown>{message.content}</MessageMarkdown>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {!project?.isSupabaseConnected &&\r\n            typeof message.content === \"string\" &&\r\n            message.content.toLowerCase().includes(\"<supabase_connected>\") && (\r\n              <SupabaseStatusCard />\r\n            )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,kBAAkB;AAExB,MAAM,mBAAmB;IACvB;QACE,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,SAAS;IACX;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,SAAS;IACX;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,SACE;IACJ;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;QACP,OAAO;QACP,SACE;IACJ;CACD;AAED,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACxB,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAiD;IAC1E,MAAM,YAAY,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,kBAAkB;YACpB,OAAO;gBACL,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;QACF;QACA,OACE,iBAAiB,IAAI,CAAC,CAAC,YAAc,UAAU,UAAU,GAAG,IAAI,UAAU,UAAU,GAAG,KACvF,gBAAgB,CAAC,iBAAiB,MAAM,GAAG,EAAE;IAEjD,GAAG;QAAC;QAAQ;KAAiB;IAE7B,MAAM,mBAAmB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,kBAAkB;YACpB,OAAO,GAAG,UAAU,KAAK,CAAC,GAAG,EAAE,UAAU,OAAO,EAAE;QACpD;QACA,MAAM,YAAY,UAAU,KAAK,KAAK,SAAS,QAAQ;QACvD,OAAO,GAAG,UAAU,KAAK,GAAG,YAAY,UAAU,OAAO,EAAE;IAC7D,GAAG;QAAC;QAAW;KAAiB;IAEhC,qBACE,6VAAC,gIAAA,CAAA,UAAI;QACH,MAAK;QACL,YAAY,CAAC;QACb,OAAM;QACN,uBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gBAAC,WAAU;0BAA6B;;;;;;;;;;;kBAIzD,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAW,CAAC,sBAAsB,EAAE,UAAU,KAAK,CAAC,cAAc,CAAC;;;;;;sCACxE,6VAAC;4BACC,WAAW,CAAC,8BAA8B,EAAE,UAAU,KAAK,CAAC,mBAAmB,CAAC;;;;;;;;;;;;8BAGpF,6VAAC;oBAAE,WAAU;8BACV,mBAAmB,SAAS,WAAW,IAAI,aAAa,OAAO,QAAQ;;;;;;;;;;;;;;;;;AAKlF;AAGF,eAAe,WAAW,GAAG;AAE7B,MAAM,YAAY,CAAC,EACjB,OAAO,EACP,OAAO,EAIR;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC/B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,CAAA,GAAA,6LAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO,EAAY,SAAS,QAAQ,cAAc;IAChF,GAAG;QAAC;QAAS,QAAQ,OAAO;QAAE,QAAQ,cAAc;KAAC;IAErD,MAAM,iBAAiB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY;YACV,MAAM,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EACf,uBACA,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,QAAQ,WAAW,IAAI,KACrD,CAAC,GACD;QAEJ;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;YACxC,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,2BAA2B;gBACpC,aAAa,MAAM,OAAO;YAC5B;QACF;IACF;IAEA,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAChC,mBAAmB;QACnB,eAAe,MAAM;IACvB,GAAG;QAAC;KAAe;IAEnB,qBACE,6VAAC,iJAAA,CAAA,eAAY;;0BACX,6VAAC,iJAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6VAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAO,WAAU;8BAC5C,cAAA,6VAAC,0TAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG5B,6VAAC,iJAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6VAAC,iJAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS,IACP,KAAK,cAAc,CAAC,EAAE,EAAE,SAAS,kBAAkB,cAAc,CAAC,EAAE,CAAC,OAAO,GAAG,IAC5E,IAAI,CAAC;gCACJ,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;4BACf,GACC,KAAK,CAAC;gCACL,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;4BACb;;4BAEL;0CAEC,6VAAC,+NAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;kCAEpB,6VAAC,iJAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,mBAAmB;wBAClC,WAAU;;4BACX;0CAEC,6VAAC,gTAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;;0BAGvB,6VAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAiB,cAAc;0BAChD,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;;sCACjB,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,6VAAC,2IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,UAAU,eAAe,SAAS;oCAClC,SAAS,IAAM,mBAAmB;8CACnC;;;;;;8CAGD,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;oCACT,UAAU,eAAe,SAAS;8CAEjC,eAAe,SAAS,iBACvB;;0DACE,6VAAC,mIAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAwB;;uDAI7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;AAEO,MAAM,mBAAmB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAgB;IACjE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,eACJ,QAAQ,YAAY,KAAK,QACxB,OAAO,QAAQ,OAAO,KAAK,YAAY,gBAAgB,IAAI,CAAC,QAAQ,OAAO;IAE9E,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,6LAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,OAAO,GAAa;QAAC,QAAQ,OAAO;KAAC;IAEhG,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAC1B,IAAM,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,OAAO,GAAa,eACrD;QAAC,QAAQ,OAAO;KAAC;IAGnB,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,kKAAA,CAAA,UAAW;;;;;gCAEX,QAAQ,SAAS,kBAChB,6VAAC;oCAAE,WAAU;8CACV,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;sCAKxC,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;8CACZ,QAAQ,YAAY,KAAK,2BACxB,6VAAC;wCACC,QAAQ,QAAQ,YAAY;wCAC5B,kBAAkB,CAAC,CAAC,CAAC,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,CAAC;;;;;;;;;;;gCAKrF,QAAQ,WAAW,kBAAI,6VAAC;oCAAU,SAAS;oCAAS,SAAS;;;;;;;;;;;;;;;;;;8BAIlE,6VAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sRACA,QAAQ,WAAW,IAAI,iBACvB,gBAAgB;;wBAGjB,QAAQ,iBAAiB,kBAAI,6VAAC,mLAAA,CAAA,UAAgB;4BAAC,SAAS,QAAQ,iBAAiB;;;;;;wBAEjF,OAAO,QAAQ,OAAO,KAAK,0BAC1B;sCACG,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,QAAQ,OAAO,CAAC,QAAQ,CAAC,qBACzD;;oCACG,+BAAiB,6VAAC,+IAAA,CAAA,kBAAe;kDAAE;;;;;;kDAEpC,6VAAC,2MAAA,CAAA,wBAAqB;wCACpB,SAAS,QAAQ,OAAO;wCACxB,SAAS;wCACT,eAAe,QAAQ,cAAc;;;;;;;+CAGvC,2BACF;0CACE,cAAA,6VAAC,6KAAA,CAAA,UAAU;oCAAC,YAAY;;;;;;8DAG1B,6VAAC,+IAAA,CAAA,kBAAe;0CAAE,QAAQ,OAAO;;;;;;;wBAKtC,CAAC,SAAS,uBACT,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,yCACrC,6VAAC,yLAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 5106, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/collapsible-content/content/no-tags-content.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"@/components/ui/collapsible\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { parseErrorDetails } from \"@/features/thread/utils/parse-error-details\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useSetFile } from \"@/stores/navigate-file\";\r\nimport { DangerTriangleSolid, FileTextSolid } from \"@mynaui/icons-react\";\r\nimport { memo, useCallback, useEffect, useMemo, useState } from \"react\";\r\nimport { HiChevronUpDown } from \"react-icons/hi2\";\r\n\r\nexport const NoTagsContent = ({\r\n  content,\r\n  imagesLength,\r\n}: {\r\n  content: string;\r\n  imagesLength?: number;\r\n}) => {\r\n  const [userMentionedFiles, setUserMentionedFiles] = useState<string[]>([]);\r\n  const setFile = useSetFile();\r\n\r\n  const extractFiles = useCallback((text: string) => {\r\n    const contentCopy = text.trimStart().trimEnd();\r\n    const extractedFiles: string[] = [];\r\n\r\n    const attachedFileRegex = /attached_file_([^\\s\\n]+)/g;\r\n    let match;\r\n\r\n    while ((match = attachedFileRegex.exec(contentCopy)) !== null) {\r\n      const filePath = match[1];\r\n      if (filePath?.includes(\".\") && !extractedFiles.includes(filePath)) {\r\n        extractedFiles.push(filePath);\r\n      }\r\n    }\r\n\r\n    return extractedFiles;\r\n  }, []);\r\n\r\n  const processDisplayContent = useCallback((text: string) => {\r\n    let displayContent = text.trimStart().trimEnd();\r\n\r\n    displayContent = displayContent.replace(/attached_file_[^\\s\\n]+/g, \"\").trim();\r\n\r\n    displayContent = displayContent.replace(/[ \\t]+/g, \" \").trim();\r\n\r\n    return displayContent;\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const extractedFiles = extractFiles(content);\r\n    setUserMentionedFiles(extractedFiles);\r\n  }, [content, extractFiles]);\r\n\r\n  const displayContent = useMemo(\r\n    () => processDisplayContent(content),\r\n    [content, processDisplayContent],\r\n  );\r\n\r\n  const handleFileClick = useCallback(\r\n    (file: string) => {\r\n      setFile(file, true);\r\n    },\r\n    [setFile],\r\n  );\r\n\r\n  const contentClassName = useMemo(\r\n    () =>\r\n      cn(\r\n        \"prose dark:prose-invert prose-p:whitespace-break-spaces prose-code:m-0 prose-code:whitespace-break-spaces prose-pre:m-0 prose-pre:w-full prose-pre:p-0 dark:prose-pre:bg-secondary-50 w-fit max-w-[80%] select-text gap-1 overflow-auto overflow-x-auto whitespace-pre-wrap text-wrap break-words rounded-2xl bg-primary/10 p-3 px-3 py-2.5 text-[0.9rem] font-medium leading-[22px] text-primary/95\",\r\n        imagesLength && imagesLength > 0 && \"rounded-se\",\r\n        userMentionedFiles.length > 0 && \"rounded-se\",\r\n      ),\r\n    [imagesLength, userMentionedFiles.length],\r\n  );\r\n\r\n  const { errorDetails, networkErrors, cleanedContent, vercelError } = useMemo(\r\n    () => parseErrorDetails(displayContent),\r\n    [displayContent],\r\n  );\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-end justify-center\">\r\n      {userMentionedFiles.length > 0 && (\r\n        <div className=\"mb-2 ml-2 flex flex-row items-center gap-2\">\r\n          {userMentionedFiles.map((file, index) => (\r\n            <Button\r\n              variant=\"outline-primary\"\r\n              className={cn(\r\n                \"flex h-fit w-fit items-center gap-2 rounded-xl border border-primary/10 p-1 text-sm font-medium leading-none shadow-sm transition-all duration-300\",\r\n              )}\r\n              key={`user-mentioned-file-${index}`}\r\n              onClick={() => handleFileClick(file)}\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <div className=\"flex h-8 w-8 items-center justify-center rounded-md border border-border/60 bg-sidebar-primary/80 p-1 shadow-sm dark:bg-sidebar-primary/40\">\r\n                  <FileTextSolid className=\"size-4 h-full w-full text-background/90\" />\r\n                </div>\r\n                <div className=\"flex flex-col gap-0 pr-4\">\r\n                  <span className=\"truncate font-medium text-primary/90\">\r\n                    {file.split(\"/\").pop()}\r\n                  </span>\r\n                  <span className=\"text-left text-xs font-normal text-muted-foreground\">File</span>\r\n                </div>\r\n              </div>\r\n            </Button>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {cleanedContent && errorDetails && errorDetails.length > 0 ? (\r\n        <div className=\"flex w-full max-w-[80%] flex-wrap items-center justify-end gap-2 pb-2 pt-1 transition-all duration-300\">\r\n          <div className=\"relative w-full max-w-md\">\r\n            <Collapsible className=\"group\">\r\n              <CollapsibleTrigger className=\"flex w-full items-center justify-start\">\r\n                <Button\r\n                  variant=\"destructive\"\r\n                  size=\"sm\"\r\n                  className=\"w-full justify-between gap-2 group-data-[state=open]:rounded-b-none\"\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <DangerTriangleSolid className=\"size-4 text-primary\" />\r\n                    <span>Error</span>\r\n                  </div>\r\n                  <HiChevronUpDown className=\"size-4\" />\r\n                </Button>\r\n              </CollapsibleTrigger>\r\n              <CollapsibleContent className=\"w-full\">\r\n                <div className=\"flex flex-col gap-2 overflow-x-auto rounded-lg rounded-t-none border border-destructive/20 bg-destructive/5 p-3 py-1\">\r\n                  {errorDetails.map((error) => (\r\n                    <Typography.P\r\n                      key={error.message}\r\n                      className=\"mt-0 font-mono text-sm text-destructive\"\r\n                    >\r\n                      {error.message} on{\" \"}\r\n                      <span className=\"font-bold\">\r\n                        {error.pageUrl === \"/\" ? \"index.tsx\" : error.pageUrl}\r\n                      </span>\r\n                    </Typography.P>\r\n                  ))}\r\n                </div>\r\n              </CollapsibleContent>\r\n            </Collapsible>\r\n          </div>\r\n        </div>\r\n      ) : null}\r\n\r\n      {cleanedContent && networkErrors && networkErrors.length > 0 ? (\r\n        <div className=\"flex w-full max-w-[80%] flex-wrap items-center justify-end gap-2 pb-2 pt-1 transition-all duration-300\">\r\n          <div className=\"relative w-full max-w-md\">\r\n            <Collapsible className=\"group\">\r\n              <CollapsibleTrigger className=\"flex w-full items-center justify-start\">\r\n                <Button\r\n                  variant=\"destructive\"\r\n                  size=\"sm\"\r\n                  className=\"w-full justify-between gap-2 group-data-[state=open]:rounded-b-none\"\r\n                  type=\"button\"\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <DangerTriangleSolid className=\"size-4 text-primary\" />\r\n                    <span>Network Error</span>\r\n                  </div>\r\n                  <HiChevronUpDown className=\"size-4\" />\r\n                </Button>\r\n              </CollapsibleTrigger>\r\n              <CollapsibleContent className=\"w-full\">\r\n                <div className=\"flex flex-col gap-2 overflow-x-auto rounded-lg rounded-t-none border border-destructive/20 bg-destructive/5 p-3 py-1\">\r\n                  {networkErrors?.map((error) => (\r\n                    <Typography.P\r\n                      key={error.message}\r\n                      className=\"mt-0 font-mono text-sm text-destructive\"\r\n                    >\r\n                      {error.message} on <span className=\"font-bold\">{error.details?.url}</span>{\" \"}\r\n                      with status <span className=\"font-bold\">{error.details?.status}</span> and\r\n                      method <span className=\"font-bold\">{error.details?.method}</span>\r\n                    </Typography.P>\r\n                  ))}\r\n                </div>\r\n              </CollapsibleContent>\r\n            </Collapsible>\r\n          </div>\r\n        </div>\r\n      ) : null}\r\n\r\n      {cleanedContent && vercelError ? (\r\n        <div className=\"flex w-full max-w-[80%] flex-wrap items-center justify-end gap-2 pb-2 pt-1 transition-all duration-300\">\r\n          <div className=\"relative w-full max-w-md\">\r\n            <Collapsible className=\"group\">\r\n              <CollapsibleTrigger className=\"flex w-full items-center justify-start\">\r\n                <Button\r\n                  variant=\"destructive\"\r\n                  size=\"sm\"\r\n                  className=\"w-full justify-between gap-2 group-data-[state=open]:rounded-b-none\"\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <DangerTriangleSolid className=\"size-4 text-primary\" />\r\n                    <span>Vercel Error</span>\r\n                  </div>\r\n                  <HiChevronUpDown className=\"size-4\" />\r\n                </Button>\r\n              </CollapsibleTrigger>\r\n              <CollapsibleContent className=\"w-full\">\r\n                <div className=\"flex max-h-40 flex-col gap-2 overflow-hidden overflow-x-auto overflow-y-auto rounded-lg rounded-t-none border border-destructive/20 bg-destructive/5 p-3 py-1\">\r\n                  <Typography.P className=\"mt-0 font-mono text-sm text-destructive\">\r\n                    {vercelError}\r\n                  </Typography.P>\r\n                </div>\r\n              </CollapsibleContent>\r\n            </Collapsible>\r\n          </div>\r\n        </div>\r\n      ) : null}\r\n\r\n      <div className={contentClassName}>\r\n        {cleanedContent ? <div>{cleanedContent}</div> : <div>{displayContent}</div>}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(NoTagsContent);\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAVA;;;;;;;;;;;AAYO,MAAM,gBAAgB,CAAC,EAC5B,OAAO,EACP,YAAY,EAIb;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAEzB,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,MAAM,cAAc,KAAK,SAAS,GAAG,OAAO;QAC5C,MAAM,iBAA2B,EAAE;QAEnC,MAAM,oBAAoB;QAC1B,IAAI;QAEJ,MAAO,CAAC,QAAQ,kBAAkB,IAAI,CAAC,YAAY,MAAM,KAAM;YAC7D,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,IAAI,UAAU,SAAS,QAAQ,CAAC,eAAe,QAAQ,CAAC,WAAW;gBACjE,eAAe,IAAI,CAAC;YACtB;QACF;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,IAAI,iBAAiB,KAAK,SAAS,GAAG,OAAO;QAE7C,iBAAiB,eAAe,OAAO,CAAC,2BAA2B,IAAI,IAAI;QAE3E,iBAAiB,eAAe,OAAO,CAAC,WAAW,KAAK,IAAI;QAE5D,OAAO;IACT,GAAG,EAAE;IAEL,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,aAAa;QACpC,sBAAsB;IACxB,GAAG;QAAC;QAAS;KAAa;IAE1B,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAC3B,IAAM,sBAAsB,UAC5B;QAAC;QAAS;KAAsB;IAGlC,MAAM,kBAAkB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,QAAQ,MAAM;IAChB,GACA;QAAC;KAAQ;IAGX,MAAM,mBAAmB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAC7B,IACE,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACC,wYACA,gBAAgB,eAAe,KAAK,cACpC,mBAAmB,MAAM,GAAG,KAAK,eAErC;QAAC;QAAc,mBAAmB,MAAM;KAAC;IAG3C,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EACzE,IAAM,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,iBACxB;QAAC;KAAe;IAGlB,qBACE,6VAAC;QAAI,WAAU;;YACZ,mBAAmB,MAAM,GAAG,mBAC3B,6VAAC;gBAAI,WAAU;0BACZ,mBAAmB,GAAG,CAAC,CAAC,MAAM,sBAC7B,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;wBAGF,SAAS,IAAM,gBAAgB;kCAE/B,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,qUAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,KAAK,GAAG;;;;;;sDAEtB,6VAAC;4CAAK,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;uBAXrE,CAAC,oBAAoB,EAAE,OAAO;;;;;;;;;;YAmB1C,kBAAkB,gBAAgB,aAAa,MAAM,GAAG,kBACvD,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,uIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC,uIAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAC5B,cAAA,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iVAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;8DAC/B,6VAAC;8DAAK;;;;;;;;;;;;sDAER,6VAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG/B,6VAAC,uIAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAC5B,cAAA,6VAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;4CAEX,WAAU;;gDAET,MAAM,OAAO;gDAAC;gDAAI;8DACnB,6VAAC;oDAAK,WAAU;8DACb,MAAM,OAAO,KAAK,MAAM,cAAc,MAAM,OAAO;;;;;;;2CALjD,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAc9B;YAEH,kBAAkB,iBAAiB,cAAc,MAAM,GAAG,kBACzD,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,uIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC,uIAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAC5B,cAAA,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,MAAK;;sDAEL,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iVAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;8DAC/B,6VAAC;8DAAK;;;;;;;;;;;;sDAER,6VAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG/B,6VAAC,uIAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAC5B,cAAA,6VAAC;oCAAI,WAAU;8CACZ,eAAe,IAAI,CAAC,sBACnB,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;4CAEX,WAAU;;gDAET,MAAM,OAAO;gDAAC;8DAAI,6VAAC;oDAAK,WAAU;8DAAa,MAAM,OAAO,EAAE;;;;;;gDAAY;gDAAI;8DACnE,6VAAC;oDAAK,WAAU;8DAAa,MAAM,OAAO,EAAE;;;;;;gDAAc;8DAC/D,6VAAC;oDAAK,WAAU;8DAAa,MAAM,OAAO,EAAE;;;;;;;2CAL9C,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAa9B;YAEH,kBAAkB,4BACjB,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,uIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC,uIAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAC5B,cAAA,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iVAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;8DAC/B,6VAAC;8DAAK;;;;;;;;;;;;sDAER,6VAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG/B,6VAAC,uIAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAC5B,cAAA,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wCAAC,WAAU;kDACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAOX;0BAEJ,6VAAC;gBAAI,WAAW;0BACb,+BAAiB,6VAAC;8BAAK;;;;;yCAAwB,6VAAC;8BAAK;;;;;;;;;;;;;;;;;AAI9D;qDAEe,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 5600, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/message-content.tsx"], "sourcesContent": ["import {\r\n  PreviewImage,\r\n  PreviewImageContent,\r\n  PreviewImageTrigger,\r\n} from \"@/components/ui/preview-image\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NoTagsContent } from \"./collapsible-content/content/no-tags-content\";\r\nimport { MessageProps } from \"./types\";\r\n\r\nexport const MessageContent = ({ message }: MessageProps) => {\r\n  if (typeof message.content === \"string\") {\r\n    return <NoTagsContent content={message.content} imagesLength={0} />;\r\n  }\r\n\r\n  if (Array.isArray(message.content)) {\r\n    const textItems = message.content.filter((item) => item.type === \"text\");\r\n    const imageItems = message.content.filter(\r\n      (item) => item.type === \"image_url\" && item.image_url,\r\n    );\r\n\r\n    return (\r\n      <div className={cn(\"flex flex-col gap-4\", imageItems.length > 0 && \"gap-0\")}>\r\n        {imageItems.length === 1 ? (\r\n          <div className=\"flex h-auto flex-row flex-wrap items-center justify-end gap-2\">\r\n            {imageItems.map((item, index) => (\r\n              <PreviewImage key={`image-${index}`}>\r\n                <PreviewImageTrigger asChild>\r\n                  <div\r\n                    className={cn(\r\n                      \"max-h-52 min-h-fit w-auto max-w-sm cursor-pointer overflow-hidden rounded-md border bg-center\",\r\n                    )}\r\n                  >\r\n                    <img\r\n                      src={item.image_url.url || \"/placeholder.svg\"}\r\n                      alt=\"User uploaded image\"\r\n                      className=\"h-auto w-full bg-center object-cover\"\r\n                      loading=\"lazy\"\r\n                    />\r\n                  </div>\r\n                </PreviewImageTrigger>\r\n                <PreviewImageContent\r\n                  fileContent={item.image_url.url}\r\n                  file={item.image_url.url}\r\n                  fileType=\"image\"\r\n                  fullscreen\r\n                />\r\n              </PreviewImage>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          imageItems.length > 0 && (\r\n            <div className=\"mb-2 flex h-auto flex-row flex-wrap items-center justify-end gap-2\">\r\n              {imageItems.map((item, index) => (\r\n                <PreviewImage key={`image-${index}`}>\r\n                  <PreviewImageTrigger asChild>\r\n                    <div\r\n                      className={cn(\r\n                        \"h-28 w-28 max-w-sm cursor-pointer overflow-hidden rounded-md border\",\r\n                      )}\r\n                    >\r\n                      <img\r\n                        src={item.image_url.url || \"/placeholder.svg\"}\r\n                        alt=\"User uploaded image\"\r\n                        className=\"h-full w-full object-cover\"\r\n                        loading=\"lazy\"\r\n                      />\r\n                    </div>\r\n                  </PreviewImageTrigger>\r\n                  <PreviewImageContent\r\n                    fileContent={item.image_url.url}\r\n                    file={item.image_url.url}\r\n                    fileType=\"image\"\r\n                    fullscreen\r\n                  />\r\n                </PreviewImage>\r\n              ))}\r\n            </div>\r\n          )\r\n        )}\r\n\r\n        {textItems.map((item, index) => (\r\n          <div key={`text-${index}`}>\r\n            <NoTagsContent content={item.text} imagesLength={imageItems.length} />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (typeof message.data === \"string\") {\r\n    return <NoTagsContent content={message.data} imagesLength={0} />;\r\n  }\r\n\r\n  return JSON.stringify(message.content || message.data, null, 2);\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAKA;AACA;;;;;AAGO,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAgB;IACtD,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;QACvC,qBAAO,6VAAC,yNAAA,CAAA,gBAAa;YAAC,SAAS,QAAQ,OAAO;YAAE,cAAc;;;;;;IAChE;IAEA,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,GAAG;QAClC,MAAM,YAAY,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;QACjE,MAAM,aAAa,QAAQ,OAAO,CAAC,MAAM,CACvC,CAAC,OAAS,KAAK,IAAI,KAAK,eAAe,KAAK,SAAS;QAGvD,qBACE,6VAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,WAAW,MAAM,GAAG,KAAK;;gBAChE,WAAW,MAAM,KAAK,kBACrB,6VAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6VAAC,4IAAA,CAAA,eAAY;;8CACX,6VAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6VAAC;wCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kDAGF,cAAA,6VAAC;4CACC,KAAK,KAAK,SAAS,CAAC,GAAG,IAAI;4CAC3B,KAAI;4CACJ,WAAU;4CACV,SAAQ;;;;;;;;;;;;;;;;8CAId,6VAAC,4IAAA,CAAA,sBAAmB;oCAClB,aAAa,KAAK,SAAS,CAAC,GAAG;oCAC/B,MAAM,KAAK,SAAS,CAAC,GAAG;oCACxB,UAAS;oCACT,UAAU;;;;;;;2BAnBK,CAAC,MAAM,EAAE,OAAO;;;;;;;;;2BAyBvC,WAAW,MAAM,GAAG,mBAClB,6VAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6VAAC,4IAAA,CAAA,eAAY;;8CACX,6VAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6VAAC;wCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kDAGF,cAAA,6VAAC;4CACC,KAAK,KAAK,SAAS,CAAC,GAAG,IAAI;4CAC3B,KAAI;4CACJ,WAAU;4CACV,SAAQ;;;;;;;;;;;;;;;;8CAId,6VAAC,4IAAA,CAAA,sBAAmB;oCAClB,aAAa,KAAK,SAAS,CAAC,GAAG;oCAC/B,MAAM,KAAK,SAAS,CAAC,GAAG;oCACxB,UAAS;oCACT,UAAU;;;;;;;2BAnBK,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;gBA2B1C,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6VAAC;kCACC,cAAA,6VAAC,yNAAA,CAAA,gBAAa;4BAAC,SAAS,KAAK,IAAI;4BAAE,cAAc,WAAW,MAAM;;;;;;uBAD1D,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;IAMjC;IAEA,IAAI,OAAO,QAAQ,IAAI,KAAK,UAAU;QACpC,qBAAO,6VAAC,yNAAA,CAAA,gBAAa;YAAC,SAAS,QAAQ,IAAI;YAAE,cAAc;;;;;;IAC7D;IAEA,OAAO,KAAK,SAAS,CAAC,QAAQ,OAAO,IAAI,QAAQ,IAAI,EAAE,MAAM;AAC/D", "debugId": null}}, {"offset": {"line": 5764, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/thread-update.tsx"], "sourcesContent": ["import { MessageContent } from \"./message-content\";\r\nimport { MessageProps } from \"./types\";\r\n\r\nexport const ThreadUpdate = (props: MessageProps) => {\r\n  return (\r\n    <div className=\"flex flex-col space-y-2\">\r\n      <div className=\"flex items-start\">\r\n        <div className=\"w-full rounded-lg bg-background p-3\">\r\n          <div className=\"mb-2 text-sm font-medium text-primary\">Thread Update</div>\r\n          <MessageContent {...props} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGO,MAAM,eAAe,CAAC;IAC3B,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,6VAAC,iLAAA,CAAA,iBAAc;wBAAE,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 5817, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/tool-message.tsx"], "sourcesContent": ["import { CollapsibleXmlContent } from \"./collapsible-content/content/content\";\r\nimport { MessageProps } from \"./types\";\r\n\r\nexport const ToolMessage = ({ actions, message }: MessageProps) => {\r\n  return (\r\n    <div className=\"w-full\">\r\n      <CollapsibleXmlContent\r\n        content={\r\n          typeof message.content === \"string\" ? message.content : JSON.stringify(message.content)\r\n        }\r\n        actions={actions}\r\n        failedActions={message.failed_actions}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGO,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,OAAO,EAAgB;IAC5D,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,2MAAA,CAAA,wBAAqB;YACpB,SACE,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GAAG,KAAK,SAAS,CAAC,QAAQ,OAAO;YAExF,SAAS;YACT,eAAe,QAAQ,cAAc;;;;;;;;;;;AAI7C", "debugId": null}}, {"offset": {"line": 5848, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/user-message.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport Hint from \"@/components/ui/hint\";\r\nimport { errorToast, successToast } from \"@/features/global/toast\";\r\nimport { useCopy } from \"@/hooks/use-copy\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ActionResult } from \"@/types/thread-message\";\r\nimport { Check, Refresh } from \"@mynaui/icons-react\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { LuCopy } from \"react-icons/lu\";\r\nimport { MessageContent } from \"./message-content\";\r\nimport { MessageType } from \"./types\";\r\n\r\nexport const UserMessage = ({\r\n  actions,\r\n  message,\r\n}: {\r\n  actions: ActionResult[];\r\n  message: MessageType;\r\n}) => {\r\n  const { copied, copy } = useCopy(900);\r\n  const { sendMessage } = useThread();\r\n\r\n  const resendMessageFn = useMutation({\r\n    mutationFn: async (message: string) => {\r\n      await sendMessage({ content: message });\r\n    },\r\n    onSuccess: () => {\r\n      successToast(\"Successfully resend message\");\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error resending message\", error);\r\n      errorToast(\"Failed to resend message\", {\r\n        description: error.message,\r\n      });\r\n    },\r\n  });\r\n\r\n  return (\r\n    <div className={cn(\"my-8 mb-4 ml-auto mt-2 flex max-w-[80%] flex-col justify-end gap-1.5\")}>\r\n      <MessageContent actions={actions} message={message} />\r\n      {/* <div className=\"flex items-center justify-end\"> */}\r\n      <div className=\"flex items-center justify-end gap-0.5 pt-0.5\">\r\n        <Hint label=\"Retry Message\" side=\"bottom\" className=\"isolate\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={() => resendMessageFn.mutate(message.content as string)}\r\n            disabled={resendMessageFn.isPending}\r\n            className=\"rounded-md\"\r\n          >\r\n            <Refresh className=\"size-4\" />\r\n          </Button>\r\n        </Hint>\r\n        <Hint label=\"Copy Message\" side=\"bottom\" className=\"isolate\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={() =>\r\n              copy(message.content as string).then(() => successToast(\"Copied to clipboard\"))\r\n            }\r\n            className=\"rounded-md\"\r\n          >\r\n            {copied ? <Check className=\"size-4\" /> : <LuCopy className=\"size-4\" />}\r\n          </Button>\r\n        </Hint>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;AAGO,MAAM,cAAc,CAAC,EAC1B,OAAO,EACP,OAAO,EAIR;IACC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD,EAAE;IACjC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IAEhC,MAAM,kBAAkB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,OAAO;YACjB,MAAM,YAAY;gBAAE,SAAS;YAAQ;QACvC;QACA,WAAW;YACT,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;QACf;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;YACzC,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,4BAA4B;gBACrC,aAAa,MAAM,OAAO;YAC5B;QACF;IACF;IAEA,qBACE,6VAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;;0BACjB,6VAAC,iLAAA,CAAA,iBAAc;gBAAC,SAAS;gBAAS,SAAS;;;;;;0BAE3C,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,gIAAA,CAAA,UAAI;wBAAC,OAAM;wBAAgB,MAAK;wBAAS,WAAU;kCAClD,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,gBAAgB,MAAM,CAAC,QAAQ,OAAO;4BACrD,UAAU,gBAAgB,SAAS;4BACnC,WAAU;sCAEV,cAAA,6VAAC,gTAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGvB,6VAAC,gIAAA,CAAA,UAAI;wBAAC,OAAM;wBAAe,MAAK;wBAAS,WAAU;kCACjD,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IACP,KAAK,QAAQ,OAAO,EAAY,IAAI,CAAC,IAAM,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;4BAE1D,WAAU;sCAET,uBAAS,6VAAC,4SAAA,CAAA,QAAK;gCAAC,WAAU;;;;;qDAAc,6VAAC,+NAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvE", "debugId": null}}, {"offset": {"line": 5985, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages.tsx"], "sourcesContent": ["import { ActionError, ActionSuccess } from \"@/types/thread-message\";\r\nimport { AssistantMessage } from \"./messages/assistant-message\";\r\nimport { ThreadUpdate } from \"./messages/thread-update\";\r\nimport { ToolMessage } from \"./messages/tool-message\";\r\nimport { MessageType } from \"./messages/types\";\r\nimport { UserMessage } from \"./messages/user-message\";\r\n\r\nconst shouldFilter = (message: MessageType, isToolResult: boolean) => {\r\n  if (typeof message.content === \"string\") {\r\n    if (message.content.includes(\"<continue_instructions>\")) {\r\n      return true;\r\n    }\r\n\r\n    if (\r\n      (isToolResult || message.role === \"assistant\") &&\r\n      (message.content.includes(\"create_file: ToolResult\") ||\r\n        message.content.includes(\"full_file_rewrite: ToolResult\") ||\r\n        message.content.includes(\"send_terminal_command: ToolResult\") ||\r\n        message.content.includes(\"check_for_errors: ToolResult\") ||\r\n        message.content.includes(\"stop_session: ToolResult\") ||\r\n        message.content.includes(\"close_files_in_editor: ToolResult\") ||\r\n        message.content.includes(\"open_files_in_editor: ToolResult\") ||\r\n        message.content.startsWith(\"execute_sql_query: ToolResult\") ||\r\n        message.content.startsWith(\"get_database_schema: ToolResult\") ||\r\n        message.content.match(/ToolResult\\(success=(True|False), output=\".*\"\\)/) ||\r\n        message.content.match(\r\n          /ToolResult\\(success=True, output=\"File '.+' (created|updated) successfully\"/,\r\n        ) ||\r\n        message.content.match(\r\n          /ToolResult\\(success=False, output=\"File '.+' (created|updated) successfully\"/,\r\n        ))\r\n    ) {\r\n      return true;\r\n    }\r\n  }\r\n  return false;\r\n};\r\n\r\nexport const Message = ({ message }: { message: MessageType }) => {\r\n  const isToolResult =\r\n    message.type === \"content\" &&\r\n    typeof message.content === \"string\" &&\r\n    message.content.includes(\"ToolResult\");\r\n\r\n  const isAssistant = message.role === \"assistant\" || message.type === \"assistant_message\";\r\n\r\n  if (shouldFilter(message, isToolResult)) {\r\n    return null;\r\n  }\r\n\r\n  const actions: (ActionError | ActionSuccess)[] = [\r\n    ...((message.failed_actions?.map((action) => ({\r\n      ...action,\r\n      type: \"error\",\r\n    })) as ActionError[]) || []),\r\n    ...((message.success_actions?.map((action) => ({\r\n      ...action,\r\n      type: \"success\",\r\n    })) as ActionSuccess[]) || []),\r\n  ];\r\n\r\n  if (message.type === \"thread_update\") {\r\n    return <ThreadUpdate actions={actions} message={message} />;\r\n  }\r\n\r\n  const isUser = message.role === \"user\" || message.type === \"user_message\";\r\n\r\n  if (isUser) {\r\n    return <UserMessage actions={actions} message={message} />;\r\n  }\r\n\r\n  if (isToolResult) {\r\n    return <ToolMessage actions={actions} message={message} />;\r\n  }\r\n\r\n  if (isAssistant) {\r\n    return <AssistantMessage actions={actions} message={message} />;\r\n  }\r\n\r\n  return <div data-content=\"Message content can't be displayed\"></div>;\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,eAAe,CAAC,SAAsB;IAC1C,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;QACvC,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,4BAA4B;YACvD,OAAO;QACT;QAEA,IACE,CAAC,gBAAgB,QAAQ,IAAI,KAAK,WAAW,KAC7C,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,8BACxB,QAAQ,OAAO,CAAC,QAAQ,CAAC,oCACzB,QAAQ,OAAO,CAAC,QAAQ,CAAC,wCACzB,QAAQ,OAAO,CAAC,QAAQ,CAAC,mCACzB,QAAQ,OAAO,CAAC,QAAQ,CAAC,+BACzB,QAAQ,OAAO,CAAC,QAAQ,CAAC,wCACzB,QAAQ,OAAO,CAAC,QAAQ,CAAC,uCACzB,QAAQ,OAAO,CAAC,UAAU,CAAC,oCAC3B,QAAQ,OAAO,CAAC,UAAU,CAAC,sCAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,sDACtB,QAAQ,OAAO,CAAC,KAAK,CACnB,kFAEF,QAAQ,OAAO,CAAC,KAAK,CACnB,+EACD,GACH;YACA,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEO,MAAM,UAAU,CAAC,EAAE,OAAO,EAA4B;IAC3D,MAAM,eACJ,QAAQ,IAAI,KAAK,aACjB,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,CAAC,QAAQ,CAAC;IAE3B,MAAM,cAAc,QAAQ,IAAI,KAAK,eAAe,QAAQ,IAAI,KAAK;IAErE,IAAI,aAAa,SAAS,eAAe;QACvC,OAAO;IACT;IAEA,MAAM,UAA2C;WAC3C,AAAC,QAAQ,cAAc,EAAE,IAAI,CAAC,SAAW,CAAC;gBAC5C,GAAG,MAAM;gBACT,MAAM;YACR,CAAC,MAAwB,EAAE;WACvB,AAAC,QAAQ,eAAe,EAAE,IAAI,CAAC,SAAW,CAAC;gBAC7C,GAAG,MAAM;gBACT,MAAM;YACR,CAAC,MAA0B,EAAE;KAC9B;IAED,IAAI,QAAQ,IAAI,KAAK,iBAAiB;QACpC,qBAAO,6VAAC,+KAAA,CAAA,eAAY;YAAC,SAAS;YAAS,SAAS;;;;;;IAClD;IAEA,MAAM,SAAS,QAAQ,IAAI,KAAK,UAAU,QAAQ,IAAI,KAAK;IAE3D,IAAI,QAAQ;QACV,qBAAO,6VAAC,8KAAA,CAAA,cAAW;YAAC,SAAS;YAAS,SAAS;;;;;;IACjD;IAEA,IAAI,cAAc;QAChB,qBAAO,6VAAC,8KAAA,CAAA,cAAW;YAAC,SAAS;YAAS,SAAS;;;;;;IACjD;IAEA,IAAI,aAAa;QACf,qBAAO,6VAAC,mLAAA,CAAA,mBAAgB;YAAC,SAAS;YAAS,SAAS;;;;;;IACtD;IAEA,qBAAO,6VAAC;QAAI,gBAAa;;;;;;AAC3B", "debugId": null}}, {"offset": {"line": 6080, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/messages/wrap-up-summary.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { WrapUpSummaryMessage } from \"@/providers/thread-provider/types\";\r\nimport { FC } from \"react\";\r\n\r\ninterface WrapUpSummaryProps {\r\n  wrapUpSummary: WrapUpSummaryMessage[\"data\"];\r\n  handleSuggestionClick: (suggestion: string) => void;\r\n}\r\n\r\nexport const WrapUpSummary: FC<WrapUpSummaryProps> = ({ wrapUpSummary, handleSuggestionClick }) => {\r\n  return (\r\n    <div className=\"my-4 rounded-lg border bg-secondary/30 p-4 shadow-sm\">\r\n      <p className=\"mb-4 text-sm text-foreground\">{wrapUpSummary.summary}</p>\r\n      <div className=\"flex flex-col gap-2\">\r\n        {wrapUpSummary.suggestions.map((suggestion, index) => (\r\n          <Button\r\n            key={index}\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => handleSuggestionClick(suggestion)}\r\n            className=\"h-auto w-full whitespace-normal px-3 py-1.5 text-left text-xs sm:w-auto\"\r\n          >\r\n            {suggestion}\r\n          </Button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AASO,MAAM,gBAAwC,CAAC,EAAE,aAAa,EAAE,qBAAqB,EAAE;IAC5F,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAE,WAAU;0BAAgC,cAAc,OAAO;;;;;;0BAClE,6VAAC;gBAAI,WAAU;0BACZ,cAAc,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBAC1C,6VAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,sBAAsB;wBACrC,WAAU;kCAET;uBANI;;;;;;;;;;;;;;;;AAYjB", "debugId": null}}, {"offset": {"line": 6130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/streaming-message/actions.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { useSetFile } from \"@/stores/navigate-file\";\r\nimport { ActionResult, ParsedCommand } from \"@/types/thread-message\";\r\nimport {\r\n  CheckCircleSolid,\r\n  DangerTriangleSolid,\r\n  EyeSolid,\r\n  LockSolid,\r\n  TerminalSolid,\r\n} from \"@mynaui/icons-react\";\r\nimport { memo, useEffect, useMemo, useState } from \"react\";\r\nimport { ParsedToolResult } from \"../messages/collapsible-content/utils\";\r\nimport { ParsedSection } from \"../thread-xml-parser\";\r\n\r\nconst ActionBadge = memo(({ type }: { type: string }) => {\r\n  switch (type) {\r\n    case \"create_file\":\r\n      return <Badge variant=\"success\">Created</Badge>;\r\n    case \"update_file\":\r\n    case \"full_file_rewrite\":\r\n    case \"update_file_sections\":\r\n      return <Badge variant=\"update\">Updated</Badge>;\r\n    case \"delete_file\":\r\n      return <Badge variant=\"destructive\">Deleted</Badge>;\r\n    case \"open_files_in_editor\":\r\n      return <Badge variant=\"opened\">Opened</Badge>;\r\n    case \"close_files_in_editor\":\r\n      return <Badge variant=\"closed\">Closed</Badge>;\r\n    case \"send_terminal_command\":\r\n      return <Badge variant=\"terminal\">Terminal</Badge>;\r\n    case \"reset_next_server\":\r\n      return <Badge variant=\"reset\">Reset</Badge>;\r\n    case \"check_for_errors\":\r\n      return <Badge variant=\"passed\">Passed</Badge>;\r\n    default:\r\n      return null;\r\n  }\r\n});\r\n\r\nActionBadge.displayName = \"ActionBadge\";\r\n\r\nconst CommandDetails = memo(({ section }: { section: ParsedCommand }) => {\r\n  const setFile = useSetFile();\r\n\r\n  return (\r\n    <div className=\"flex w-full flex-col items-center justify-start gap-2\">\r\n      {section.message && <div className=\"text-sm text-muted-foreground\">{section.message}</div>}\r\n\r\n      {section.type === \"check_for_errors\" && (\r\n        <div className=\"text-sm text-muted-foreground\">Build Check</div>\r\n      )}\r\n\r\n      {section.type === \"reset_next_server\" && (\r\n        <div className=\"text-sm text-muted-foreground\">Reset Next.js server</div>\r\n      )}\r\n\r\n      {(section.type === \"open_files_in_editor\" || section.type === \"close_files_in_editor\") &&\r\n        section.files?.map((file: string, fileIndex: number) => (\r\n          <div\r\n            key={`file-${fileIndex}`}\r\n            className=\"flex w-full cursor-pointer items-center justify-between gap-2 rounded-md p-1.5 px-2 transition-colors hover:bg-sidebar-accent/80\"\r\n            onClick={() => {\r\n              if (file) setFile(file, true);\r\n            }}\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              {section.status === \"completed\" && <CheckCircleSolid className=\"size-4\" />}\r\n              {section.status === \"failed\" && (\r\n                <DangerTriangleSolid className=\"size-4 text-destructive\" />\r\n              )}\r\n              {section.status === \"pending\" && <Loading className=\"size-4\" />}\r\n              <span className=\"max-w-80 truncate text-sm font-medium text-primary\">{file}</span>\r\n            </div>\r\n\r\n            <ActionBadge type={section.type} />\r\n          </div>\r\n        ))}\r\n    </div>\r\n  );\r\n});\r\n\r\nCommandDetails.displayName = \"CommandDetails\";\r\n\r\nexport const CheckForErrors = memo(({ section }: { section: ParsedCommand }) => {\r\n  return (\r\n    <Card className=\"my-1 w-full max-w-[26rem] shadow-sm\">\r\n      <CardContent className=\"flex items-center gap-2 p-3\">\r\n        {section.status === \"pending\" && <Loading className=\"size-4\" />}\r\n        {section.status === \"completed\" && <CheckCircleSolid className=\"size-4\" />}\r\n        {section.status === \"failed\" && <DangerTriangleSolid className=\"size-4 text-destructive\" />}\r\n        <span className=\"text-base font-medium\">Checking for errors...</span>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n});\r\n\r\nCheckForErrors.displayName = \"CheckForErrors\";\r\n\r\nexport const ResetNextServer = memo(({ section }: { section: ParsedCommand }) => {\r\n  return (\r\n    <Card className=\"my-1 w-full max-w-[26rem] shadow-sm\">\r\n      <CardContent className=\"flex items-center gap-2 p-3\">\r\n        {section.status === \"pending\" && <Loading className=\"size-4\" />}\r\n        {section.status === \"completed\" && <CheckCircleSolid className=\"size-4\" />}\r\n        {section.status === \"failed\" && <DangerTriangleSolid className=\"size-4 text-destructive\" />}\r\n        <span className=\"text-sm font-medium\">Restarting server...</span>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n});\r\n\r\nResetNextServer.displayName = \"ResetNextServer\";\r\n\r\nexport const SendTerminalCommand = memo(({ section }: { section: ParsedCommand }) => {\r\n  const isUninstalling = useMemo(() => {\r\n    return section.command?.includes(\"npm uninstall\");\r\n  }, [section.command]);\r\n\r\n  const packageName = section.command\r\n    ?.replaceAll(\"npm install\", \"\")\r\n    .replaceAll(\"npm uninstall\", \"\")\r\n    .trim();\r\n\r\n  return (\r\n    <Card className=\"my-1 w-full max-w-[26rem] shadow-sm\">\r\n      <CardContent className=\"flex items-center justify-between p-3\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <TerminalSolid className=\"size-4 text-primary\" />\r\n          <span className=\"text-base font-medium\">\r\n            {isUninstalling ? \"Uninstalling\" : \"Installing\"} {packageName}\r\n          </span>\r\n        </div>\r\n        <Loading className=\"size-4\" />\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n});\r\n\r\nSendTerminalCommand.displayName = \"SendTerminalCommand\";\r\n\r\nconst OpenFilesInEditor = memo(({ section }: { section: ParsedCommand }) => {\r\n  const [currentOperation, setCurrentOperation] = useState<string>(\"\");\r\n  const [currentOperationIndex, setCurrentOperationIndex] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    if (section.files && section.files.length > 0) {\r\n      setCurrentOperationIndex(0);\r\n      setCurrentOperation(section.files[0]);\r\n    }\r\n  }, [section.files]);\r\n\r\n  useEffect(() => {\r\n    const checkOperationStatus = () => {\r\n      const currentOp = section.files?.[currentOperationIndex];\r\n\r\n      if (currentOp && currentOperationIndex < (section.files?.length || 0) - 1) {\r\n        const nextIndex = currentOperationIndex + 1;\r\n        setCurrentOperationIndex(nextIndex);\r\n        setCurrentOperation(section.files?.[nextIndex] || \"\");\r\n      }\r\n    };\r\n\r\n    const interval = setInterval(checkOperationStatus, 1000);\r\n    return () => clearInterval(interval);\r\n  }, [currentOperationIndex, section.files]);\r\n\r\n  const currentOp = section.files?.[currentOperationIndex];\r\n\r\n  return (\r\n    <Card className=\"my-1 w-full max-w-[26rem] shadow-sm\">\r\n      <CardContent className=\"flex items-center justify-between p-3\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <EyeSolid className=\"size-4 text-primary\" />\r\n          <span className=\"max-w-64 truncate text-base font-medium\">{currentOperation}</span>\r\n        </div>\r\n        <div className=\"flex items-center gap-1\">\r\n          {currentOp && section.status === \"failed\" && <Badge variant=\"destructive\">Failed</Badge>}\r\n          {currentOp && section.status === \"pending\" && <Loading className=\"size-4\" />}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n});\r\n\r\nOpenFilesInEditor.displayName = \"OpenFilesInEditor\";\r\n\r\nconst CloseFilesInEditor = memo(({ section }: { section: ParsedCommand }) => {\r\n  const [currentOperation, setCurrentOperation] = useState<string>(\"\");\r\n  const [currentOperationIndex, setCurrentOperationIndex] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    if (section.files && section.files.length > 0) {\r\n      setCurrentOperationIndex(0);\r\n      setCurrentOperation(section.files[0]);\r\n    }\r\n  }, [section.files]);\r\n\r\n  useEffect(() => {\r\n    const checkOperationStatus = () => {\r\n      const currentOp = section.files?.[currentOperationIndex];\r\n\r\n      if (currentOp && currentOperationIndex < (section.files?.length || 0) - 1) {\r\n        const nextIndex = currentOperationIndex + 1;\r\n        setCurrentOperationIndex(nextIndex);\r\n        setCurrentOperation(section.files?.[nextIndex] || \"\");\r\n      }\r\n    };\r\n\r\n    const interval = setInterval(checkOperationStatus, 1000);\r\n    return () => clearInterval(interval);\r\n  }, [currentOperationIndex, section.files]);\r\n\r\n  const currentOp = section.files?.[currentOperationIndex];\r\n\r\n  return (\r\n    <Card className=\"my-1 w-full max-w-[26rem] shadow-sm\">\r\n      <CardContent className=\"flex items-center justify-between p-3\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <LockSolid className=\"size-4 text-primary\" />\r\n          <span className=\"max-w-64 truncate text-base font-medium\">{currentOperation}</span>\r\n        </div>\r\n        <div className=\"flex items-center gap-1\">\r\n          {currentOp && section.status === \"completed\" && <Badge variant=\"success\">Closed</Badge>}\r\n          {currentOp && section.status === \"failed\" && <Badge variant=\"destructive\">Failed</Badge>}\r\n          {currentOp && section.status === \"pending\" && <Loading className=\"size-4\" />}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n});\r\n\r\nCloseFilesInEditor.displayName = \"CloseFilesInEditor\";\r\n\r\nexport const Action = memo(\r\n  ({ section }: { section: ParsedCommand | ParsedToolResult | ParsedSection }) => {\r\n    switch (section.type) {\r\n      case \"check_for_errors\":\r\n        return <CheckForErrors section={section} />;\r\n      case \"reset_next_server\":\r\n        return <ResetNextServer section={section} />;\r\n      case \"send_terminal_command\":\r\n        return <SendTerminalCommand section={section} />;\r\n      case \"open_files_in_editor\":\r\n        return <OpenFilesInEditor section={section} />;\r\n      case \"close_files_in_editor\":\r\n        return <CloseFilesInEditor section={section} />;\r\n      case \"create_file\":\r\n        return null;\r\n      case \"update_file\":\r\n        return null;\r\n      case \"delete_file\":\r\n        return null;\r\n      case \"full_file_rewrite\":\r\n        return null;\r\n      case \"execute_sql_query\":\r\n        return (\r\n          <Card className=\"my-1 w-full max-w-[26rem] shadow-sm\">\r\n            <CardContent className=\"flex items-center gap-2 p-3\">\r\n              <Loading className=\"size-4\" />\r\n              <span className=\"text-sm font-medium\">Executing SQL Query</span>\r\n            </CardContent>\r\n          </Card>\r\n        );\r\n      case \"get_database_schema\":\r\n        return (\r\n          <Card className=\"my-1 w-full max-w-[26rem] shadow-sm\">\r\n            <CardContent className=\"flex items-center gap-2 p-3\">\r\n              <Loading className=\"size-4\" />\r\n              <span className=\"text-sm font-medium\">Getting Database Schema</span>\r\n            </CardContent>\r\n          </Card>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  },\r\n);\r\n\r\nAction.displayName = \"Action\";\r\n\r\nexport const Actions = ({ actions }: { actions: ActionResult[] }) => {\r\n  return actions.map((action, idx) => (\r\n    <Action section={action as unknown as ParsedCommand} key={`action-${action.function}-${idx}`} />\r\n  ));\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAOA;;;;;;;;AAIA,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,IAAI,EAAoB;IAClD,OAAQ;QACN,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAU;;;;;;QAClC,KAAK;QACL,KAAK;QACL,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAS;;;;;;QACjC,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAc;;;;;;QACtC,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAS;;;;;;QACjC,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAS;;;;;;QACjC,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAW;;;;;;QACnC,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAQ;;;;;;QAChC,KAAK;YACH,qBAAO,6VAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAS;;;;;;QACjC;YACE,OAAO;IACX;AACF;AAEA,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAA8B;IAClE,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAEzB,qBACE,6VAAC;QAAI,WAAU;;YACZ,QAAQ,OAAO,kBAAI,6VAAC;gBAAI,WAAU;0BAAiC,QAAQ,OAAO;;;;;;YAElF,QAAQ,IAAI,KAAK,oCAChB,6VAAC;gBAAI,WAAU;0BAAgC;;;;;;YAGhD,QAAQ,IAAI,KAAK,qCAChB,6VAAC;gBAAI,WAAU;0BAAgC;;;;;;YAGhD,CAAC,QAAQ,IAAI,KAAK,0BAA0B,QAAQ,IAAI,KAAK,uBAAuB,KACnF,QAAQ,KAAK,EAAE,IAAI,CAAC,MAAc,0BAChC,6VAAC;oBAEC,WAAU;oBACV,SAAS;wBACP,IAAI,MAAM,QAAQ,MAAM;oBAC1B;;sCAEA,6VAAC;4BAAI,WAAU;;gCACZ,QAAQ,MAAM,KAAK,6BAAe,6VAAC,2UAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;gCAC9D,QAAQ,MAAM,KAAK,0BAClB,6VAAC,iVAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAEhC,QAAQ,MAAM,KAAK,2BAAa,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACpD,6VAAC;oCAAK,WAAU;8CAAsD;;;;;;;;;;;;sCAGxE,6VAAC;4BAAY,MAAM,QAAQ,IAAI;;;;;;;mBAf1B,CAAC,KAAK,EAAE,WAAW;;;;;;;;;;;AAoBpC;AAEA,eAAe,WAAW,GAAG;AAEtB,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAA8B;IACzE,qBACE,6VAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;gBACpB,QAAQ,MAAM,KAAK,2BAAa,6VAAC,mIAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBACnD,QAAQ,MAAM,KAAK,6BAAe,6VAAC,2UAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAC9D,QAAQ,MAAM,KAAK,0BAAY,6VAAC,iVAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;8BAC/D,6VAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAIhD;AAEA,eAAe,WAAW,GAAG;AAEtB,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAA8B;IAC1E,qBACE,6VAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;gBACpB,QAAQ,MAAM,KAAK,2BAAa,6VAAC,mIAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBACnD,QAAQ,MAAM,KAAK,6BAAe,6VAAC,2UAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAC9D,QAAQ,MAAM,KAAK,0BAAY,6VAAC,iVAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;8BAC/D,6VAAC;oBAAK,WAAU;8BAAsB;;;;;;;;;;;;;;;;;AAI9C;AAEA,gBAAgB,WAAW,GAAG;AAEvB,MAAM,oCAAsB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAA8B;IAC9E,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,QAAQ,OAAO,EAAE,SAAS;IACnC,GAAG;QAAC,QAAQ,OAAO;KAAC;IAEpB,MAAM,cAAc,QAAQ,OAAO,EAC/B,WAAW,eAAe,IAC3B,WAAW,iBAAiB,IAC5B;IAEH,qBACE,6VAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,qUAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6VAAC;4BAAK,WAAU;;gCACb,iBAAiB,iBAAiB;gCAAa;gCAAE;;;;;;;;;;;;;8BAGtD,6VAAC,mIAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI3B;AAEA,oBAAoB,WAAW,GAAG;AAElC,MAAM,kCAAoB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAA8B;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IAE3E,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;YAC7C,yBAAyB;YACzB,oBAAoB,QAAQ,KAAK,CAAC,EAAE;QACtC;IACF,GAAG;QAAC,QAAQ,KAAK;KAAC;IAElB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,MAAM,YAAY,QAAQ,KAAK,EAAE,CAAC,sBAAsB;YAExD,IAAI,aAAa,wBAAwB,CAAC,QAAQ,KAAK,EAAE,UAAU,CAAC,IAAI,GAAG;gBACzE,MAAM,YAAY,wBAAwB;gBAC1C,yBAAyB;gBACzB,oBAAoB,QAAQ,KAAK,EAAE,CAAC,UAAU,IAAI;YACpD;QACF;QAEA,MAAM,WAAW,YAAY,sBAAsB;QACnD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAuB,QAAQ,KAAK;KAAC;IAEzC,MAAM,YAAY,QAAQ,KAAK,EAAE,CAAC,sBAAsB;IAExD,qBACE,6VAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,2TAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6VAAC;4BAAK,WAAU;sCAA2C;;;;;;;;;;;;8BAE7D,6VAAC;oBAAI,WAAU;;wBACZ,aAAa,QAAQ,MAAM,KAAK,0BAAY,6VAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAc;;;;;;wBACzE,aAAa,QAAQ,MAAM,KAAK,2BAAa,6VAAC,mIAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK3E;AAEA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAA8B;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IAE3E,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;YAC7C,yBAAyB;YACzB,oBAAoB,QAAQ,KAAK,CAAC,EAAE;QACtC;IACF,GAAG;QAAC,QAAQ,KAAK;KAAC;IAElB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,MAAM,YAAY,QAAQ,KAAK,EAAE,CAAC,sBAAsB;YAExD,IAAI,aAAa,wBAAwB,CAAC,QAAQ,KAAK,EAAE,UAAU,CAAC,IAAI,GAAG;gBACzE,MAAM,YAAY,wBAAwB;gBAC1C,yBAAyB;gBACzB,oBAAoB,QAAQ,KAAK,EAAE,CAAC,UAAU,IAAI;YACpD;QACF;QAEA,MAAM,WAAW,YAAY,sBAAsB;QACnD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAuB,QAAQ,KAAK;KAAC;IAEzC,MAAM,YAAY,QAAQ,KAAK,EAAE,CAAC,sBAAsB;IAExD,qBACE,6VAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,6TAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6VAAC;4BAAK,WAAU;sCAA2C;;;;;;;;;;;;8BAE7D,6VAAC;oBAAI,WAAU;;wBACZ,aAAa,QAAQ,MAAM,KAAK,6BAAe,6VAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAU;;;;;;wBACxE,aAAa,QAAQ,MAAM,KAAK,0BAAY,6VAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAc;;;;;;wBACzE,aAAa,QAAQ,MAAM,KAAK,2BAAa,6VAAC,mIAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK3E;AAEA,mBAAmB,WAAW,GAAG;AAE1B,MAAM,uBAAS,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACvB,CAAC,EAAE,OAAO,EAAiE;IACzE,OAAQ,QAAQ,IAAI;QAClB,KAAK;YACH,qBAAO,6VAAC;gBAAe,SAAS;;;;;;QAClC,KAAK;YACH,qBAAO,6VAAC;gBAAgB,SAAS;;;;;;QACnC,KAAK;YACH,qBAAO,6VAAC;gBAAoB,SAAS;;;;;;QACvC,KAAK;YACH,qBAAO,6VAAC;gBAAkB,SAAS;;;;;;QACrC,KAAK;YACH,qBAAO,6VAAC;gBAAmB,SAAS;;;;;;QACtC,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,qBACE,6VAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC,mIAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6VAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;;;;;;;QAI9C,KAAK;YACH,qBACE,6VAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC,mIAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6VAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;;;;;;;QAI9C;YACE,OAAO;IACX;AACF;AAGF,OAAO,WAAW,GAAG;AAEd,MAAM,UAAU,CAAC,EAAE,OAAO,EAA+B;IAC9D,OAAO,QAAQ,GAAG,CAAC,CAAC,QAAQ,oBAC1B,6VAAC;YAAO,SAAS;WAAyC,CAAC,OAAO,EAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,KAAK;;;;;AAEhG", "debugId": null}}, {"offset": {"line": 6814, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/streaming-message/file-operations.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { parseFileOperations, parseStreamContent } from \"@/lib/xml-parser\";\r\nimport { Ban, CheckCircleSolid, EditSolid, PlusSolid, X } from \"@mynaui/icons-react\";\r\nimport { memo, useMemo } from \"react\";\r\nimport { Action } from \"./actions\";\r\n\r\nconst getFileOperationStatus = (status: string) => {\r\n  switch (status) {\r\n    case \"pending\":\r\n      return <Loading className=\"size-4\" />;\r\n    case \"completed\":\r\n      return <CheckCircleSolid className=\"size-4\" />;\r\n    case \"failed\":\r\n      return <X className=\"size-4\" />;\r\n    case \"stopped\":\r\n      return <Ban className=\"size-4 text-red-500\" />;\r\n  }\r\n};\r\n\r\nexport const FileOperations = memo(({ content }: { content: string }) => {\r\n  const { actions } = useMemo(() => parseStreamContent(content), [content]);\r\n\r\n  const allOperations = useMemo(() => {\r\n    if (!content) return [];\r\n    return parseFileOperations(content);\r\n  }, [content]);\r\n\r\n  return (\r\n    <>\r\n      {allOperations.map((currentOperation, idx) => (\r\n        <div className=\"mb-3 text-sm\" key={`file-operation-${idx}`}>\r\n          {currentOperation.filePath && (\r\n            <Card\r\n              className={cn(\r\n                \"my-1.5 flex h-12 w-full max-w-[26rem] items-center justify-center px-4 shadow-sm\",\r\n              )}\r\n            >\r\n              <CardContent className=\"flex w-full items-center justify-center gap-2 p-3 px-1 text-sm\">\r\n                <div className=\"relative flex h-full w-full items-center justify-start gap-3 overflow-hidden\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <EditSolid className=\"size-4\" />\r\n                  </div>\r\n                  <div className=\"flex w-full flex-col items-start justify-start gap-1 overflow-hidden pt-0\">\r\n                    <div className=\"flex w-full items-center justify-between gap-2\">\r\n                      <span className=\"max-w-80 truncate text-base font-medium text-primary\">\r\n                        {currentOperation.filePath}\r\n                      </span>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {currentOperation.length && currentOperation.status === \"pending\" && (\r\n                          <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\r\n                            <PlusSolid className=\"size-3 animate-pulse\" strokeWidth={4} />\r\n                            <span>{currentOperation.length}</span>\r\n                          </div>\r\n                        )}\r\n                        {getFileOperationStatus(currentOperation.status) ?? (\r\n                          <Loading className=\"size-4\" />\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          )}\r\n        </div>\r\n      ))}\r\n\r\n      {actions.map((action, idx) => (\r\n        <Action section={action} key={`action-${action.type}-${idx}`} />\r\n      ))}\r\n    </>\r\n  );\r\n});\r\n\r\nFileOperations.displayName = \"FileOperations\";\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;AAEA,MAAM,yBAAyB,CAAC;IAC9B,OAAQ;QACN,KAAK;YACH,qBAAO,6VAAC,mIAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B,KAAK;YACH,qBAAO,6VAAC,2UAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;QACrC,KAAK;YACH,qBAAO,6VAAC,oSAAA,CAAA,IAAC;gBAAC,WAAU;;;;;;QACtB,KAAK;YACH,qBAAO,6VAAC,wSAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;IAC1B;AACF;AAEO,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAAuB;IAClE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;QAAC;KAAQ;IAExE,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS,OAAO,EAAE;QACvB,OAAO,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE;IAC7B,GAAG;QAAC;KAAQ;IAEZ,qBACE;;YACG,cAAc,GAAG,CAAC,CAAC,kBAAkB,oBACpC,6VAAC;oBAAI,WAAU;8BACZ,iBAAiB,QAAQ,kBACxB,6VAAC,gIAAA,CAAA,OAAI;wBACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kCAGF,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC,6TAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DACb,iBAAiB,QAAQ;;;;;;8DAE5B,6VAAC;oDAAI,WAAU;;wDACZ,iBAAiB,MAAM,IAAI,iBAAiB,MAAM,KAAK,2BACtD,6VAAC;4DAAI,WAAU;;8EACb,6VAAC,6TAAA,CAAA,YAAS;oEAAC,WAAU;oEAAuB,aAAa;;;;;;8EACzD,6VAAC;8EAAM,iBAAiB,MAAM;;;;;;;;;;;;wDAGjC,uBAAuB,iBAAiB,MAAM,mBAC7C,6VAAC,mIAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBAzBF,CAAC,eAAe,EAAE,KAAK;;;;;YAqC3D,QAAQ,GAAG,CAAC,CAAC,QAAQ,oBACpB,6VAAC,kLAAA,CAAA,SAAM;oBAAC,SAAS;mBAAa,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,KAAK;;;;;;;AAIpE;AAEA,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 7010, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/streaming-message/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { MessageMarkdown } from \"@/components/ui/message-markdown\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { parseCommunication } from \"@/lib/xml-parser\";\r\nimport { useWebSocketMessage } from \"@/providers/thread-provider/hooks\";\r\nimport { ContentMessage } from \"@/providers/thread-provider/types\";\r\nimport { useCurrentThreadStore } from \"@/stores/current-thread\";\r\nimport { memo, useCallback, useEffect, useMemo, useState } from \"react\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport ReasoningContent from \"../messages/reasoning-content\";\r\nimport SoftgenIcon from \"../softgen-icon\";\r\nimport { FileOperations } from \"./file-operations\";\r\n\r\nconst StreamingMessage = memo(() => {\r\n  const { isAgentRunning } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      isAgentRunning: state.isAgentRunning,\r\n    })),\r\n  );\r\n\r\n  const [content, setContent] = useState(\"\");\r\n  const [reasoningContent, setReasoningContent] = useState(\"\");\r\n  const [isStreaming, setIsStreaming] = useState(false);\r\n  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());\r\n  const [sessionComplete, setSessionComplete] = useState(false);\r\n\r\n  const handleContentMessage = useCallback((message: ContentMessage) => {\r\n    if (message.isCompleteMessage) {\r\n      setContent(\"\");\r\n      setReasoningContent(\"\");\r\n      setIsStreaming(false);\r\n      setSessionComplete(true);\r\n      // Reset session complete after a delay\r\n      setTimeout(() => setSessionComplete(false), 2000);\r\n    } else {\r\n      setContent(message.data);\r\n      setIsStreaming(true);\r\n      setLastUpdateTime(Date.now());\r\n      setSessionComplete(false);\r\n    }\r\n  }, []);\r\n\r\n  // Detect when streaming has stopped (no updates for a while)\r\n  useEffect(() => {\r\n    if (!isStreaming) return;\r\n\r\n    const timer = setTimeout(() => {\r\n      setIsStreaming(false);\r\n    }, 1000); // Consider streaming stopped after 1 second of no updates\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [lastUpdateTime, isStreaming]);\r\n\r\n  // Reset session state when agent stops running\r\n  useEffect(() => {\r\n    if (!isAgentRunning) {\r\n      setSessionComplete(false);\r\n      setIsStreaming(false);\r\n      setContent(\"\");\r\n    }\r\n  }, [isAgentRunning]);\r\n\r\n  const handleReasoningMessage = useCallback((message: ContentMessage) => {\r\n    setReasoningContent((prev) => prev + message.data);\r\n  }, []);\r\n\r\n  useWebSocketMessage<ContentMessage>(\"content\", handleContentMessage);\r\n  useWebSocketMessage<ContentMessage>(\"reasoning\", handleReasoningMessage);\r\n\r\n  const hasActionsXML = useMemo(() => (content ? content.includes(\"<actions\") : false), [content]);\r\n\r\n  const communication = useMemo(\r\n    () => parseCommunication(content as string)?.communication,\r\n    [content],\r\n  );\r\n\r\n  // Early return to avoid rendering empty component\r\n  if (!content && !reasoningContent) return null;\r\n\r\n  debug(\"StreamingMessage\", {\r\n    content,\r\n    isStreaming,\r\n    hasActionsXML,\r\n    sessionComplete,\r\n    isAgentRunning,\r\n  });\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex w-full flex-col space-y-2 rounded-md\">\r\n        <div className=\"flex w-full flex-col items-start\">\r\n          <div className=\"mb-2 flex w-full flex-row items-start justify-start gap-2\">\r\n            <div className=\"flex h-fit w-fit items-center justify-center\">\r\n              <SoftgenIcon />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"w-full\">\r\n            {reasoningContent && (\r\n              <ReasoningContent isInProgress collapsed={false} content={reasoningContent} />\r\n            )}\r\n\r\n            {communication && (\r\n              <div className=\"mb-3\">\r\n                <MessageMarkdown>\r\n                  {communication\r\n                    .replaceAll(\"<communication>\", \"\")\r\n                    .replaceAll(\"</communication>\", \"\")}\r\n                </MessageMarkdown>\r\n              </div>\r\n            )}\r\n\r\n            {content && <FileOperations content={content} />}\r\n\r\n            {!hasActionsXML && !communication && content && (\r\n              <div className=\"whitespace-pre-wrap text-[0.95rem]\">{content}</div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n});\r\n\r\nStreamingMessage.displayName = \"StreamingMessage\";\r\nexport default StreamingMessage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAcA,MAAM,iCAAmB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE;IAC5B,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAC7C,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,gBAAgB,MAAM,cAAc;QACtC,CAAC;IAGH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,uBAAuB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,IAAI,QAAQ,iBAAiB,EAAE;YAC7B,WAAW;YACX,oBAAoB;YACpB,eAAe;YACf,mBAAmB;YACnB,uCAAuC;YACvC,WAAW,IAAM,mBAAmB,QAAQ;QAC9C,OAAO;YACL,WAAW,QAAQ,IAAI;YACvB,eAAe;YACf,kBAAkB,KAAK,GAAG;YAC1B,mBAAmB;QACrB;IACF,GAAG,EAAE;IAEL,6DAA6D;IAC7D,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;QAElB,MAAM,QAAQ,WAAW;YACvB,eAAe;QACjB,GAAG,OAAO,0DAA0D;QAEpE,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAgB;KAAY;IAEhC,+CAA+C;IAC/C,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,gBAAgB;YACnB,mBAAmB;YACnB,eAAe;YACf,WAAW;QACb;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,yBAAyB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,oBAAoB,CAAC,OAAS,OAAO,QAAQ,IAAI;IACnD,GAAG,EAAE;IAEL,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAkB,WAAW;IAC/C,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAkB,aAAa;IAEjD,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE,IAAO,UAAU,QAAQ,QAAQ,CAAC,cAAc,OAAQ;QAAC;KAAQ;IAE/F,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAC1B,IAAM,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAoB,eAC7C;QAAC;KAAQ;IAGX,kDAAkD;IAClD,IAAI,CAAC,WAAW,CAAC,kBAAkB,OAAO;IAE1C,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,oBAAoB;QACxB;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE;kBACE,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,kKAAA,CAAA,UAAW;;;;;;;;;;;;;;;kCAIhB,6VAAC;wBAAI,WAAU;;4BACZ,kCACC,6VAAC,mLAAA,CAAA,UAAgB;gCAAC,YAAY;gCAAC,WAAW;gCAAO,SAAS;;;;;;4BAG3D,+BACC,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,+IAAA,CAAA,kBAAe;8CACb,cACE,UAAU,CAAC,mBAAmB,IAC9B,UAAU,CAAC,oBAAoB;;;;;;;;;;;4BAKvC,yBAAW,6VAAC,6LAAA,CAAA,iBAAc;gCAAC,SAAS;;;;;;4BAEpC,CAAC,iBAAiB,CAAC,iBAAiB,yBACnC,6VAAC;gCAAI,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;;;AAOnE;AAEA,iBAAiB,WAAW,GAAG;uCAChB", "debugId": null}}, {"offset": {"line": 7194, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/thread-messages.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { LazyModal } from \"@/components/ui/modal\";\r\nimport { TextShimmer } from \"@/components/ui/text-shimmer\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useWebSocketMessage } from \"@/providers/thread-provider/hooks\";\r\nimport { WrapUpSummaryMessage } from \"@/providers/thread-provider/types\";\r\nimport { useAgentInput } from \"@/stores/agent-input\";\r\nimport {\r\n  useCurrentThreadStore,\r\n  useSelectedSectionAction,\r\n  useSubscribeToAgentRunning,\r\n} from \"@/stores/current-thread\";\r\nimport { ThreadMessage } from \"@/types/thread-message\";\r\nimport { ChevronDown } from \"@mynaui/icons-react\";\r\nimport { useVirtualizer, VirtualItem } from \"@tanstack/react-virtual\";\r\nimport { AnimatePresence, motion } from \"motion/react\";\r\nimport { lazy, memo, useCallback, useEffect, useMemo, useRef, useState } from \"react\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport { ContinueMessage } from \"./continue-message\";\r\nimport { Message } from \"./messages\";\r\nimport { WrapUpSummary } from \"./messages/wrap-up-summary\";\r\nimport StreamingMessage from \"./streaming-message\";\r\n\r\nconst SectionModalContent = lazy(\r\n  () => import(\"./messages/collapsible-content/modal/section-modal\"),\r\n);\r\n\r\ntype PhaseLabel = {\r\n  label: string;\r\n  time: number;\r\n};\r\n\r\ntype PhaseLabels = {\r\n  [key: string]: PhaseLabel;\r\n};\r\n\r\nconst duringStreamingLabels: PhaseLabels = {\r\n  generating: { label: \"generating…\", time: 7000 },\r\n  thinking: { label: \"thinking…\", time: 5000 },\r\n  working: { label: \"working…\", time: 4000 },\r\n  creating: { label: \"creating…\", time: 3000 },\r\n  crafting: { label: \"crafting…\", time: 3000 },\r\n  writing: { label: \"writing…\", time: 3000 },\r\n};\r\n\r\nconst postStreamingLabels: PhaseLabels = {\r\n  processing: { label: \"processing actions…\", time: 3000 },\r\n  ready: { label: \"ready to process…\", time: 5000 },\r\n  updating: { label: \"updating files…\", time: 3000 },\r\n  analyzing: { label: \"analyzing…\", time: 2000 },\r\n  linting: { label: \"linting…\", time: 5000 },\r\n  formatting: { label: \"formatting…\", time: 4000 },\r\n  refactoring: { label: \"refactoring…\", time: 4000 },\r\n  optimizing: { label: \"optimizing code…\", time: 5000 },\r\n  checking: { label: \"checking syntax…\", time: 8000 },\r\n  reviewing: { label: \"reviewing code…\", time: 10000 },\r\n  cleaning: { label: \"cleaning up…\", time: 5000 },\r\n  building: { label: \"building…\", time: 10000 },\r\n};\r\n\r\nfunction SelectedSectionActionModal() {\r\n  const { selectedSectionAction, setSelectedSectionAction } = useSelectedSectionAction();\r\n\r\n  return (\r\n    <LazyModal\r\n      open={!!selectedSectionAction?.section || !!selectedSectionAction?.action}\r\n      onOpenChange={(open) => {\r\n        if (!open) setSelectedSectionAction({ section: null, action: null });\r\n      }}\r\n    >\r\n      <SectionModalContent />\r\n    </LazyModal>\r\n  );\r\n}\r\n\r\nconst VirtualMessageItem = memo(\r\n  ({\r\n    virtualItem,\r\n    message,\r\n    measureElement,\r\n  }: {\r\n    virtualItem: VirtualItem;\r\n    message: ThreadMessage;\r\n    measureElement: (element: Element | null) => void;\r\n  }) => (\r\n    <div\r\n      data-index={virtualItem.index}\r\n      ref={measureElement}\r\n      style={{\r\n        position: \"absolute\",\r\n        top: 0,\r\n        left: 0,\r\n        width: \"100%\",\r\n        transform: `translateY(${virtualItem.start}px)`,\r\n      }}\r\n    >\r\n      <Message message={message} />\r\n    </div>\r\n  ),\r\n);\r\n\r\nVirtualMessageItem.displayName = \"VirtualMessageItem\";\r\n\r\nconst AgentStatusMessage = () => {\r\n  const { isSoftgenProcessing } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      isSoftgenProcessing: state.isSoftgenProcessing,\r\n    })),\r\n  );\r\n\r\n  const currentPhaseLabels = isSoftgenProcessing ? postStreamingLabels : duringStreamingLabels;\r\n  const phaseKeys = Object.keys(currentPhaseLabels);\r\n\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const phaseState = phaseKeys[currentIndex];\r\n\r\n  const updateBadge = useCallback(() => {\r\n    setCurrentIndex((prevIndex: number) => (prevIndex + 1) % phaseKeys.length);\r\n  }, [phaseKeys.length]);\r\n\r\n  useEffect(() => {\r\n    setCurrentIndex(0);\r\n  }, [isSoftgenProcessing]);\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(updateBadge, currentPhaseLabels[phaseState]?.time || 1000);\r\n    return () => clearInterval(timer);\r\n  }, [updateBadge]);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 flex items-center justify-start rounded-lg bg-transparent pb-8 text-sm\">\r\n      <TextShimmer className=\"font-medium\">\r\n        softgen is{\" \"}\r\n        <AnimatePresence mode=\"popLayout\" initial={false}>\r\n          <motion.span\r\n            key={`${isSoftgenProcessing}-${phaseState}`}\r\n            transition={{\r\n              bounce: 0,\r\n              duration: 0.3,\r\n              type: \"spring\",\r\n            }}\r\n            initial={{ y: 25, opacity: 0 }}\r\n            animate={{ y: 0, opacity: 1 }}\r\n            exit={{ y: 25, opacity: 0 }}\r\n          >\r\n            {currentPhaseLabels[phaseState]?.label || \"working…\"}\r\n          </motion.span>\r\n        </AnimatePresence>\r\n      </TextShimmer>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ScrollToBottomButton = ({ onClick }: { onClick: () => void }) => {\r\n  return (\r\n    <Card\r\n      className={cn(\r\n        \"my-1.5 flex w-fit items-center justify-center rounded-4xl bg-primary shadow-sm\",\r\n        \"absolute bottom-4 left-1/2 z-50 flex -translate-x-1/2\",\r\n      )}\r\n      border={false}\r\n    >\r\n      <CardContent className=\"flex w-full items-center justify-center gap-2 p-0 text-sm\">\r\n        <Button\r\n          onClick={onClick}\r\n          size=\"sm\"\r\n          className=\"flex items-center justify-center rounded-4xl text-primary-foreground transition-all\"\r\n          aria-label=\"Scroll to bottom\"\r\n        >\r\n          <div className=\"flex items-center gap-1.5\">\r\n            <span className=\"text-sm font-medium text-background\">Scroll</span>\r\n            <ChevronDown className=\"size-5 font-medium text-background\" />\r\n          </div>\r\n        </Button>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nconst ThreadMessages = () => {\r\n  const { isAgentRunning, threadMessages } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      isAgentRunning: state.isAgentRunning,\r\n      threadMessages: state.threadMessages,\r\n    })),\r\n  );\r\n\r\n  const { isLoading, sendMessage } = useThread();\r\n\r\n  const handleContinueMessage = (mode: \"creative\" | \"standard\") => {\r\n    sendMessage({\r\n      content: \"continue\",\r\n      options: {\r\n        model: mode,\r\n      },\r\n    });\r\n    setWrapUpSummary(null);\r\n  };\r\n\r\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\r\n  const [showScrollButton, setShowScrollButton] = useState(false);\r\n  const [userScrolledUp, setUserScrolledUp] = useState(false);\r\n  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const isScrollingProgrammaticallyRef = useRef(false);\r\n  const [wrapUpSummary, setWrapUpSummary] = useState<WrapUpSummaryMessage[\"data\"] | null>(null);\r\n\r\n  const virtualizer = useVirtualizer({\r\n    count: threadMessages.length,\r\n    getScrollElement: () => scrollContainerRef.current,\r\n    estimateSize: useCallback(\r\n      (index) => {\r\n        const message = threadMessages[index];\r\n        if (!message) return 100;\r\n\r\n        if (typeof message.content === \"string\") {\r\n          const contentLength = message.content.length;\r\n          return Math.max(80, Math.min(400, 80 + Math.floor(contentLength / 100) * 20));\r\n        }\r\n\r\n        return 120;\r\n      },\r\n      [threadMessages],\r\n    ),\r\n    overscan: 5,\r\n    measureElement: (element) => element?.getBoundingClientRect().height,\r\n  });\r\n\r\n  const isNearBottom = useCallback(() => {\r\n    if (!scrollContainerRef.current) return true;\r\n    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;\r\n    return scrollHeight - scrollTop - clientHeight <= 30;\r\n  }, []);\r\n\r\n  const scrollToBottom = useCallback(() => {\r\n    if (!scrollContainerRef.current) return;\r\n\r\n    scrollContainerRef.current.scrollTo({\r\n      top: scrollContainerRef.current.scrollHeight,\r\n      behavior: \"smooth\",\r\n    });\r\n\r\n    setUserScrolledUp(false);\r\n  }, []);\r\n\r\n  const handleScroll = useCallback(() => {\r\n    if (!scrollContainerRef.current) return;\r\n\r\n    const nearBottom = isNearBottom();\r\n\r\n    const shouldShowButton = !nearBottom && (isAgentRunning || threadMessages.length > 0);\r\n    setShowScrollButton(shouldShowButton);\r\n\r\n    setUserScrolledUp(!nearBottom);\r\n  }, [isNearBottom, isAgentRunning, threadMessages.length]);\r\n\r\n  useEffect(() => {\r\n    if (isAgentRunning && !userScrolledUp) {\r\n      const interval = setInterval(() => {\r\n        if (\r\n          scrollContainerRef.current &&\r\n          !userScrolledUp &&\r\n          !isScrollingProgrammaticallyRef.current\r\n        ) {\r\n          isScrollingProgrammaticallyRef.current = true;\r\n          scrollContainerRef.current.scrollTo({\r\n            top: scrollContainerRef.current.scrollHeight,\r\n            behavior: \"auto\",\r\n          });\r\n          setTimeout(() => {\r\n            isScrollingProgrammaticallyRef.current = false;\r\n          }, 50);\r\n        }\r\n      }, 100);\r\n\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [isAgentRunning, userScrolledUp]);\r\n\r\n  useEffect(() => {\r\n    if (threadMessages.length > 0 && !userScrolledUp) {\r\n      setTimeout(() => scrollToBottom(), 50);\r\n    }\r\n  }, [threadMessages.length, userScrolledUp, scrollToBottom]);\r\n\r\n  useSubscribeToAgentRunning((isRunning, wasRunning) => {\r\n    if (isRunning && !wasRunning) {\r\n      setUserScrolledUp(false);\r\n      setShowScrollButton(false);\r\n      setTimeout(() => scrollToBottom(), 100);\r\n    }\r\n  });\r\n\r\n  useEffect(() => {\r\n    const container = scrollContainerRef.current;\r\n    if (!container) return;\r\n\r\n    container.addEventListener(\"scroll\", handleScroll, { passive: true });\r\n\r\n    return () => {\r\n      container.removeEventListener(\"scroll\", handleScroll);\r\n      if (scrollTimeoutRef.current) {\r\n        clearTimeout(scrollTimeoutRef.current);\r\n      }\r\n    };\r\n  }, [handleScroll]);\r\n\r\n  const virtualItems = virtualizer.getVirtualItems();\r\n\r\n  const handleWrapUpSummary = useCallback((message: WrapUpSummaryMessage) => {\r\n    setWrapUpSummary(message.data);\r\n  }, []);\r\n\r\n  useWebSocketMessage<WrapUpSummaryMessage>(\"wrap_up_summary\", handleWrapUpSummary);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"relative flex h-full flex-col\">\r\n        <div className=\"flex h-full items-center justify-center\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent\" />\r\n            <p className=\"text-sm text-muted-foreground\">Loading messages...</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handleSuggestionClick = (suggestion: string) => {\r\n    sendMessage({\r\n      content: suggestion,\r\n      options: {\r\n        model: \"standard\",\r\n      },\r\n    });\r\n    setWrapUpSummary(null);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative flex h-full flex-col\">\r\n      <div\r\n        ref={scrollContainerRef}\r\n        className=\"mb-0 w-full px-2 py-0 pt-2 md:pt-0\"\r\n        style={{\r\n          height: \"100%\",\r\n          overflow: \"auto\",\r\n        }}\r\n      >\r\n        <div className=\"w-full py-2 md:py-0\">\r\n          {threadMessages.length === 0 && !isAgentRunning ? (\r\n            <div className=\"flex flex-col items-center justify-center py-8 text-primary\">\r\n              <Typography.P className=\"leading-1 text-center\">No messages yet.</Typography.P>\r\n              <Typography.P className=\"mt-0 text-center leading-none\">\r\n                Start a conversation!\r\n              </Typography.P>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div\r\n                style={{\r\n                  height: virtualizer.getTotalSize(),\r\n                  width: \"100%\",\r\n                  position: \"relative\",\r\n                }}\r\n              >\r\n                {virtualItems.map((virtualItem) => {\r\n                  const message = threadMessages[virtualItem.index];\r\n                  return (\r\n                    <VirtualMessageItem\r\n                      key={virtualItem.key}\r\n                      virtualItem={virtualItem}\r\n                      message={message}\r\n                      measureElement={virtualizer.measureElement}\r\n                    />\r\n                  );\r\n                })}\r\n              </div>\r\n\r\n              <StreamingMessage />\r\n\r\n              {!wrapUpSummary && !isAgentRunning && (\r\n                <ContinueMessageWrapper handleContinueMessage={handleContinueMessage} />\r\n              )}\r\n\r\n              {wrapUpSummary && !isAgentRunning && (\r\n                <WrapUpSummary\r\n                  wrapUpSummary={wrapUpSummary}\r\n                  handleSuggestionClick={handleSuggestionClick}\r\n                />\r\n              )}\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {isAgentRunning && <AgentStatusMessage />}\r\n      {showScrollButton && <ScrollToBottomButton onClick={scrollToBottom} />}\r\n\r\n      <SelectedSectionActionModal />\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction ContinueMessageWrapper({\r\n  handleContinueMessage,\r\n}: {\r\n  handleContinueMessage: (mode: \"creative\" | \"standard\") => void;\r\n}) {\r\n  const { setMode } = useAgentInput();\r\n  const { isAgentRunning, threadMessages } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      isAgentRunning: state.isAgentRunning,\r\n      threadMessages: state.threadMessages,\r\n    })),\r\n  );\r\n\r\n  const continueProps = useMemo(() => {\r\n    if (!isAgentRunning && threadMessages.length > 0) {\r\n      const latestMessage = threadMessages[threadMessages.length - 1];\r\n\r\n      if (latestMessage.role === \"assistant\" && latestMessage.content) {\r\n        const switchModeMatch = latestMessage.content.match(/(creative|standard) mode/i);\r\n\r\n        if (switchModeMatch && switchModeMatch[1]) {\r\n          const modeToSwitch = switchModeMatch[1].toLowerCase() as \"creative\" | \"standard\";\r\n          return {\r\n            text: `Switch to ${\r\n              modeToSwitch.charAt(0).toUpperCase() + modeToSwitch.slice(1)\r\n            } and Continue`,\r\n            onContinue: () => {\r\n              setMode(modeToSwitch);\r\n              handleContinueMessage(modeToSwitch);\r\n            },\r\n          };\r\n        }\r\n\r\n        const showStandardContinue =\r\n          latestMessage.content.includes(\"<continue>\") ||\r\n          latestMessage.content.includes(\"send_terminal_command: ToolResult\") ||\r\n          latestMessage.content.includes(\"close_files_in_editor: ToolResult\");\r\n\r\n        if (showStandardContinue) {\r\n          return { onContinue: () => handleContinueMessage(\"creative\") };\r\n        }\r\n      }\r\n    }\r\n    return undefined;\r\n  }, [isAgentRunning, threadMessages, handleContinueMessage, setMode]);\r\n\r\n  if (!continueProps) return null;\r\n\r\n  return <ContinueMessage {...continueProps} />;\r\n}\r\n\r\nThreadMessages.displayName = \"ThreadMessages\";\r\n\r\nexport default ThreadMessages;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAMA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA;;;;;;;;;;;;;;;;;;;;;AA4BA,MAAM,oCAAsB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAC7B;AAYF,MAAM,wBAAqC;IACzC,YAAY;QAAE,OAAO;QAAe,MAAM;IAAK;IAC/C,UAAU;QAAE,OAAO;QAAa,MAAM;IAAK;IAC3C,SAAS;QAAE,OAAO;QAAY,MAAM;IAAK;IACzC,UAAU;QAAE,OAAO;QAAa,MAAM;IAAK;IAC3C,UAAU;QAAE,OAAO;QAAa,MAAM;IAAK;IAC3C,SAAS;QAAE,OAAO;QAAY,MAAM;IAAK;AAC3C;AAEA,MAAM,sBAAmC;IACvC,YAAY;QAAE,OAAO;QAAuB,MAAM;IAAK;IACvD,OAAO;QAAE,OAAO;QAAqB,MAAM;IAAK;IAChD,UAAU;QAAE,OAAO;QAAmB,MAAM;IAAK;IACjD,WAAW;QAAE,OAAO;QAAc,MAAM;IAAK;IAC7C,SAAS;QAAE,OAAO;QAAY,MAAM;IAAK;IACzC,YAAY;QAAE,OAAO;QAAe,MAAM;IAAK;IAC/C,aAAa;QAAE,OAAO;QAAgB,MAAM;IAAK;IACjD,YAAY;QAAE,OAAO;QAAoB,MAAM;IAAK;IACpD,UAAU;QAAE,OAAO;QAAoB,MAAM;IAAK;IAClD,WAAW;QAAE,OAAO;QAAmB,MAAM;IAAM;IACnD,UAAU;QAAE,OAAO;QAAgB,MAAM;IAAK;IAC9C,UAAU;QAAE,OAAO;QAAa,MAAM;IAAM;AAC9C;AAEA,SAAS;IACP,MAAM,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,2BAAwB,AAAD;IAEnF,qBACE,6VAAC,iIAAA,CAAA,YAAS;QACR,MAAM,CAAC,CAAC,uBAAuB,WAAW,CAAC,CAAC,uBAAuB;QACnE,cAAc,CAAC;YACb,IAAI,CAAC,MAAM,yBAAyB;gBAAE,SAAS;gBAAM,QAAQ;YAAK;QACpE;kBAEA,cAAA,6VAAC;;;;;;;;;;AAGP;AAEA,MAAM,mCAAqB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAC5B,CAAC,EACC,WAAW,EACX,OAAO,EACP,cAAc,EAKf,iBACC,6VAAC;QACC,cAAY,YAAY,KAAK;QAC7B,KAAK;QACL,OAAO;YACL,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,WAAW,CAAC,WAAW,EAAE,YAAY,KAAK,CAAC,GAAG,CAAC;QACjD;kBAEA,cAAA,6VAAC,2JAAA,CAAA,UAAO;YAAC,SAAS;;;;;;;;;;;AAKxB,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qBAAqB;IACzB,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClD,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,qBAAqB,MAAM,mBAAmB;QAChD,CAAC;IAGH,MAAM,qBAAqB,sBAAsB,sBAAsB;IACvE,MAAM,YAAY,OAAO,IAAI,CAAC;IAE9B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,SAAS,CAAC,aAAa;IAE1C,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,gBAAgB,CAAC,YAAsB,CAAC,YAAY,CAAC,IAAI,UAAU,MAAM;IAC3E,GAAG;QAAC,UAAU,MAAM;KAAC;IAErB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;KAAoB;IAExB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY,aAAa,kBAAkB,CAAC,WAAW,EAAE,QAAQ;QAC/E,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAY;IAEhB,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,2IAAA,CAAA,cAAW;YAAC,WAAU;;gBAAc;gBACxB;8BACX,6VAAC,mVAAA,CAAA,kBAAe;oBAAC,MAAK;oBAAY,SAAS;8BACzC,cAAA,6VAAC,oVAAA,CAAA,SAAM,CAAC,IAAI;wBAEV,YAAY;4BACV,QAAQ;4BACR,UAAU;4BACV,MAAM;wBACR;wBACA,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG;4BAAI,SAAS;wBAAE;kCAEzB,kBAAkB,CAAC,WAAW,EAAE,SAAS;uBAVrC,GAAG,oBAAoB,CAAC,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;AAgBvD;AAEA,MAAM,uBAAuB,CAAC,EAAE,OAAO,EAA2B;IAChE,qBACE,6VAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA;QAEF,QAAQ;kBAER,cAAA,6VAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6VAAC,kIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,MAAK;gBACL,WAAU;gBACV,cAAW;0BAEX,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAK,WAAU;sCAAsC;;;;;;sCACtD,6VAAC,wTAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnC;AAEA,MAAM,iBAAiB;IACrB,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAC7D,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,gBAAgB,MAAM,cAAc;YACpC,gBAAgB,MAAM,cAAc;QACtC,CAAC;IAGH,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IAE3C,MAAM,wBAAwB,CAAC;QAC7B,YAAY;YACV,SAAS;YACT,SAAS;gBACP,OAAO;YACT;QACF;QACA,iBAAiB;IACnB;IAEA,MAAM,qBAAqB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,mBAAmB,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,iCAAiC,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAE;IAC9C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAuC;IAExF,MAAM,cAAc,CAAA,GAAA,6RAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,OAAO,eAAe,MAAM;QAC5B,kBAAkB,IAAM,mBAAmB,OAAO;QAClD,cAAc,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EACtB,CAAC;YACC,MAAM,UAAU,cAAc,CAAC,MAAM;YACrC,IAAI,CAAC,SAAS,OAAO;YAErB,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;gBACvC,MAAM,gBAAgB,QAAQ,OAAO,CAAC,MAAM;gBAC5C,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,CAAC,gBAAgB,OAAO;YAC3E;YAEA,OAAO;QACT,GACA;YAAC;SAAe;QAElB,UAAU;QACV,gBAAgB,CAAC,UAAY,SAAS,wBAAwB;IAChE;IAEA,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,mBAAmB,OAAO,EAAE,OAAO;QACxC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,mBAAmB,OAAO;QAC5E,OAAO,eAAe,YAAY,gBAAgB;IACpD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,mBAAmB,OAAO,CAAC,QAAQ,CAAC;YAClC,KAAK,mBAAmB,OAAO,CAAC,YAAY;YAC5C,UAAU;QACZ;QAEA,kBAAkB;IACpB,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,aAAa;QAEnB,MAAM,mBAAmB,CAAC,cAAc,CAAC,kBAAkB,eAAe,MAAM,GAAG,CAAC;QACpF,oBAAoB;QAEpB,kBAAkB,CAAC;IACrB,GAAG;QAAC;QAAc;QAAgB,eAAe,MAAM;KAAC;IAExD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,CAAC,gBAAgB;YACrC,MAAM,WAAW,YAAY;gBAC3B,IACE,mBAAmB,OAAO,IAC1B,CAAC,kBACD,CAAC,+BAA+B,OAAO,EACvC;oBACA,+BAA+B,OAAO,GAAG;oBACzC,mBAAmB,OAAO,CAAC,QAAQ,CAAC;wBAClC,KAAK,mBAAmB,OAAO,CAAC,YAAY;wBAC5C,UAAU;oBACZ;oBACA,WAAW;wBACT,+BAA+B,OAAO,GAAG;oBAC3C,GAAG;gBACL;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAgB;KAAe;IAEnC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,MAAM,GAAG,KAAK,CAAC,gBAAgB;YAChD,WAAW,IAAM,kBAAkB;QACrC;IACF,GAAG;QAAC,eAAe,MAAM;QAAE;QAAgB;KAAe;IAE1D,CAAA,GAAA,kIAAA,CAAA,6BAA0B,AAAD,EAAE,CAAC,WAAW;QACrC,IAAI,aAAa,CAAC,YAAY;YAC5B,kBAAkB;YAClB,oBAAoB;YACpB,WAAW,IAAM,kBAAkB;QACrC;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,mBAAmB,OAAO;QAC5C,IAAI,CAAC,WAAW;QAEhB,UAAU,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAEnE,OAAO;YACL,UAAU,mBAAmB,CAAC,UAAU;YACxC,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,eAAe,YAAY,eAAe;IAEhD,MAAM,sBAAsB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,iBAAiB,QAAQ,IAAI;IAC/B,GAAG,EAAE;IAEL,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAwB,mBAAmB;IAE7D,IAAI,WAAW;QACb,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;;;;;sCACf,6VAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;IAKvD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY;YACV,SAAS;YACT,SAAS;gBACP,OAAO;YACT;QACF;QACA,iBAAiB;IACnB;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,QAAQ;oBACR,UAAU;gBACZ;0BAEA,cAAA,6VAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,KAAK,CAAC,+BAC/B,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gCAAC,WAAU;0CAAwB;;;;;;0CAChD,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gCAAC,WAAU;0CAAgC;;;;;;;;;;;6CAK1D;;0CACE,6VAAC;gCACC,OAAO;oCACL,QAAQ,YAAY,YAAY;oCAChC,OAAO;oCACP,UAAU;gCACZ;0CAEC,aAAa,GAAG,CAAC,CAAC;oCACjB,MAAM,UAAU,cAAc,CAAC,YAAY,KAAK,CAAC;oCACjD,qBACE,6VAAC;wCAEC,aAAa;wCACb,SAAS;wCACT,gBAAgB,YAAY,cAAc;uCAHrC,YAAY,GAAG;;;;;gCAM1B;;;;;;0CAGF,6VAAC,gLAAA,CAAA,UAAgB;;;;;4BAEhB,CAAC,iBAAiB,CAAC,gCAClB,6VAAC;gCAAuB,uBAAuB;;;;;;4BAGhD,iBAAiB,CAAC,gCACjB,6VAAC,oLAAA,CAAA,gBAAa;gCACZ,eAAe;gCACf,uBAAuB;;;;;;;;;;;;;;;;;;YAQlC,gCAAkB,6VAAC;;;;;YACnB,kCAAoB,6VAAC;gBAAqB,SAAS;;;;;;0BAEpD,6VAAC;;;;;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,qBAAqB,EAGtB;IACC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;IAChC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAC7D,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,gBAAgB,MAAM,cAAc;YACpC,gBAAgB,MAAM,cAAc;QACtC,CAAC;IAGH,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,kBAAkB,eAAe,MAAM,GAAG,GAAG;YAChD,MAAM,gBAAgB,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;YAE/D,IAAI,cAAc,IAAI,KAAK,eAAe,cAAc,OAAO,EAAE;gBAC/D,MAAM,kBAAkB,cAAc,OAAO,CAAC,KAAK,CAAC;gBAEpD,IAAI,mBAAmB,eAAe,CAAC,EAAE,EAAE;oBACzC,MAAM,eAAe,eAAe,CAAC,EAAE,CAAC,WAAW;oBACnD,OAAO;wBACL,MAAM,CAAC,UAAU,EACf,aAAa,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,KAAK,CAAC,GAC3D,aAAa,CAAC;wBACf,YAAY;4BACV,QAAQ;4BACR,sBAAsB;wBACxB;oBACF;gBACF;gBAEA,MAAM,uBACJ,cAAc,OAAO,CAAC,QAAQ,CAAC,iBAC/B,cAAc,OAAO,CAAC,QAAQ,CAAC,wCAC/B,cAAc,OAAO,CAAC,QAAQ,CAAC;gBAEjC,IAAI,sBAAsB;oBACxB,OAAO;wBAAE,YAAY,IAAM,sBAAsB;oBAAY;gBAC/D;YACF;QACF;QACA,OAAO;IACT,GAAG;QAAC;QAAgB;QAAgB;QAAuB;KAAQ;IAEnE,IAAI,CAAC,eAAe,OAAO;IAE3B,qBAAO,6VAAC,sKAAA,CAAA,kBAAe;QAAE,GAAG,aAAa;;;;;;AAC3C;AAEA,eAAe,WAAW,GAAG;uCAEd", "debugId": null}}, {"offset": {"line": 7823, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { useCurrentThreadStore, useResetCurrentThread } from \"@/stores/current-thread\";\r\nimport { useNavigateFile } from \"@/stores/navigate-file\";\r\nimport { ChevronDown } from \"@mynaui/icons-react\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { Undo2 } from \"lucide-react\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\nimport { ThreadActionsDropdown } from \"./thread-actions-dropdown\";\r\nimport ThreadMessages from \"./thread-message/thread-messages\";\r\n\r\nconst Thread = ({\r\n  setThreadToRename,\r\n}: {\r\n  setThreadToRename: (thread: { id: number; name: string } | null) => void;\r\n}) => {\r\n  const { setActiveTab, setShowDrawer } = useNavigateFile(\r\n    useShallow((state) => ({\r\n      setActiveTab: state.setActiveTab,\r\n      setShowDrawer: state.setShowDrawer,\r\n    })),\r\n  );\r\n\r\n  const queryClient = useQueryClient();\r\n\r\n  const { isAgentRunning, currentThreadId, setCurrentThreadId } = useCurrentThreadStore(\r\n    useShallow((state) => ({\r\n      isAgentRunning: state.isAgentRunning,\r\n      currentThreadId: state.id,\r\n      setCurrentThreadId: state.setId,\r\n    })),\r\n  );\r\n\r\n  const { currentThread, isLoading, setContextLimitReached } = useThread();\r\n  const { resetCurrentThread } = useResetCurrentThread();\r\n\r\n  const { projectId } = useProject();\r\n\r\n  if (isLoading)\r\n    return (\r\n      <div className=\"flex h-full flex-col overflow-hidden\">\r\n        <div className=\"flex-1 overflow-hidden px-1.5\">\r\n          <div className=\"flex h-full items-center justify-center gap-2\">\r\n            <Loading className=\"size-4 animate-spin\" />\r\n            <p className=\"text-sm text-muted-foreground\">Connecting to agent...</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  if (!currentThread) return;\r\n\r\n  return (\r\n    <div className=\"flex h-full flex-col overflow-hidden\">\r\n      <div className=\"flex items-center justify-between border-b border-primary/10 px-2 py-1 md:hidden\">\r\n        <ThreadActionsDropdown\r\n          thread={currentThread}\r\n          projectId={projectId}\r\n          icon={<ChevronDown className=\"h-4 w-4\" />}\r\n          setThreadToRename={setThreadToRename}\r\n        />\r\n\r\n        {currentThreadId && (\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"mr-0 hover:bg-transparent md:mr-1 md:hover:bg-sidebar-accent\"\r\n              disabled={isAgentRunning}\r\n              onClick={() => {\r\n                queryClient.invalidateQueries({ queryKey: [\"get-thread\", currentThreadId] });\r\n                resetCurrentThread();\r\n                setCurrentThreadId(null);\r\n                setContextLimitReached(false);\r\n              }}\r\n            >\r\n              <Undo2 className=\"h-4 w-4\" strokeWidth={2} />\r\n            </Button>\r\n            <Button\r\n              variant=\"default\"\r\n              className=\"flex h-7 w-fit md:hidden\"\r\n              onClick={() => {\r\n                setShowDrawer(true);\r\n                setActiveTab(\"preview\");\r\n              }}\r\n            >\r\n              Preview\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className={cn(\"flex-1 overflow-hidden px-1.5 pl-2.5\")}>\r\n        <ThreadMessages />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Thread;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEA,MAAM,SAAS,CAAC,EACd,iBAAiB,EAGlB;IACC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EACpD,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,cAAc,MAAM,YAAY;YAChC,eAAe,MAAM,aAAa;QACpC,CAAC;IAGH,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD,EAClF,CAAA,GAAA,+PAAA,CAAA,aAAU,AAAD,EAAE,CAAC,QAAU,CAAC;YACrB,gBAAgB,MAAM,cAAc;YACpC,iBAAiB,MAAM,EAAE;YACzB,oBAAoB,MAAM,KAAK;QACjC,CAAC;IAGH,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IACrE,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;IAEnD,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAE/B,IAAI,WACF,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,mIAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6VAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;;;;;;IAMvD,IAAI,CAAC,eAAe;IAEpB,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,2JAAA,CAAA,wBAAqB;wBACpB,QAAQ;wBACR,WAAW;wBACX,oBAAM,6VAAC,wTAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC7B,mBAAmB;;;;;;oBAGpB,iCACC,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,UAAU;gCACV,SAAS;oCACP,YAAY,iBAAiB,CAAC;wCAAE,UAAU;4CAAC;4CAAc;yCAAgB;oCAAC;oCAC1E;oCACA,mBAAmB;oCACnB,uBAAuB;gCACzB;0CAEA,cAAA,6VAAC,4RAAA,CAAA,QAAK;oCAAC,WAAU;oCAAU,aAAa;;;;;;;;;;;0CAE1C,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;oCACP,cAAc;oCACd,aAAa;gCACf;0CACD;;;;;;;;;;;;;;;;;;0BAOP,6VAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;0BACjB,cAAA,6VAAC,qKAAA,CAAA,UAAc;;;;;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 8012, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-list-card.tsx"], "sourcesContent": ["import { Card } from \"@/components/ui/card\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useCurrentThread, useCurrentThreadStore } from \"@/stores/current-thread\";\r\nimport { Thread } from \"@/types/thread-message\";\r\nimport { Calendar, Hash } from \"@mynaui/icons-react\";\r\nimport { memo } from \"react\";\r\nimport { formatTimestamp } from \"../../lib/format-timestamp\";\r\nimport { ThreadActionsDropdown } from \"./thread-actions-dropdown\";\r\n\r\nconst truncateText = (text: string, maxLength: number) => {\r\n  if (!text) return \"New Thread\";\r\n  if (text.length <= maxLength) return text;\r\n  return text.slice(0, maxLength) + \"...\";\r\n};\r\n\r\nconst ThreadListCard = memo(\r\n  ({\r\n    thread,\r\n    projectId,\r\n    className,\r\n    index,\r\n    setThreadToRename,\r\n  }: {\r\n    thread: Thread;\r\n    projectId: string;\r\n    className?: string;\r\n    index?: number;\r\n    setThreadToRename: (thread: { id: number; name: string } | null) => void;\r\n  }) => {\r\n    const { currentThreadId, setCurrentThreadId } = useCurrentThread();\r\n    const { setThreadName } = useCurrentThreadStore();\r\n    const isActive = currentThreadId === thread.thread_id;\r\n\r\n    return (\r\n      <div className=\"w-full\" key={thread.thread_id}>\r\n        <Card\r\n          border={false}\r\n          className={cn(\r\n            \"mb-2 cursor-pointer overflow-hidden border-primary/10 bg-background/90 pt-4 transition-all duration-200 hover:bg-foreground/10\",\r\n            isActive && \"border-primary/50 bg-accent/60 shadow-sm\",\r\n            className,\r\n          )}\r\n        >\r\n          <div className=\"grid h-full flex-col space-y-1\">\r\n            <div className=\"flex items-center justify-between px-4\">\r\n              <div\r\n                onClick={() => {\r\n                  setCurrentThreadId(thread.thread_id);\r\n                  setThreadName(thread.name);\r\n                }}\r\n                className=\"flex w-full items-center gap-2\"\r\n              >\r\n                <div className=\"flex h-8 w-8 items-center justify-center rounded-lg border border-primary/10 text-primary\">\r\n                  <svg\r\n                    width=\"16\"\r\n                    height=\"16\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    strokeWidth=\"2\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      d=\"M8 10.5H16M8 14.5H13.5M7.8 19H16.2C17.8802 19 18.7202 19 19.362 18.673C19.9265 18.3854 20.3854 17.9265 20.673 17.362C21 16.7202 21 15.8802 21 14.2V9.8C21 8.11984 21 7.27976 20.673 6.63803C20.3854 6.07354 19.9265 5.6146 19.362 5.32698C18.7202 5 17.8802 5 16.2 5H7.8C6.11984 5 5.27976 5 4.63803 5.32698C4.07354 5.6146 3.6146 6.07354 3.32698 6.63803C3 7.27976 3 8.11984 3 9.8V14.2C3 15.8802 3 16.7202 3.32698 17.362C3.6146 17.9265 4.07354 18.3854 4.63803 18.673C5.27976 19 6.11984 19 7.8 19Z\"\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <div className=\"flex flex-col\">\r\n                  <h3 className=\"line-clamp-1 text-sm font-medium text-foreground/90 transition-all duration-200 group-hover:text-primary group-hover:underline\">\r\n                    {truncateText(thread.name || \"New Thread\", 30)}\r\n                  </h3>\r\n                  <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\r\n                    <Hash className=\"h-3 w-3\" strokeWidth={2} />\r\n                    <span>{index}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"thread-actions flex items-center gap-2\">\r\n                <ThreadActionsDropdown\r\n                  thread={thread}\r\n                  projectId={projectId}\r\n                  setThreadToRename={setThreadToRename}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              className=\"flex items-center justify-between p-4 text-xs text-muted-foreground\"\r\n              onClick={() => {\r\n                setCurrentThreadId(thread.thread_id);\r\n              }}\r\n            >\r\n              <div className=\"flex items-center gap-1\">\r\n                <Calendar className=\"h-3 w-3\" strokeWidth={2} />\r\n                <span>{formatTimestamp(thread.last_updated_date, \"lastChat\")}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  },\r\n);\r\n\r\nThreadListCard.displayName = \"ThreadListCard\";\r\n\r\nexport { ThreadListCard };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,eAAe,CAAC,MAAc;IAClC,IAAI,CAAC,MAAM,OAAO;IAClB,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEA,MAAM,+BAAiB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACxB,CAAC,EACC,MAAM,EACN,SAAS,EACT,SAAS,EACT,KAAK,EACL,iBAAiB,EAOlB;IACC,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD;IAC/D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;IAC9C,MAAM,WAAW,oBAAoB,OAAO,SAAS;IAErD,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,gIAAA,CAAA,OAAI;YACH,QAAQ;YACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kIACA,YAAY,4CACZ;sBAGF,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCACC,SAAS;oCACP,mBAAmB,OAAO,SAAS;oCACnC,cAAc,OAAO,IAAI;gCAC3B;gCACA,WAAU;;kDAEV,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,OAAM;4CACN,aAAY;4CACZ,QAAO;sDAEP,cAAA,6VAAC;gDACC,GAAE;gDACF,eAAc;gDACd,gBAAe;;;;;;;;;;;;;;;;kDAIrB,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,IAAI,IAAI,cAAc;;;;;;0DAE7C,6VAAC;gDAAI,WAAU;;kEACb,6VAAC,0SAAA,CAAA,OAAI;wDAAC,WAAU;wDAAU,aAAa;;;;;;kEACvC,6VAAC;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAKb,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,2JAAA,CAAA,wBAAqB;oCACpB,QAAQ;oCACR,WAAW;oCACX,mBAAmB;;;;;;;;;;;;;;;;;kCAKzB,6VAAC;wBACC,WAAU;wBACV,SAAS;4BACP,mBAAmB,OAAO,SAAS;wBACrC;kCAEA,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,kTAAA,CAAA,WAAQ;oCAAC,WAAU;oCAAU,aAAa;;;;;;8CAC3C,6VAAC;8CAAM,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,iBAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA/D9B,OAAO,SAAS;;;;;AAsEjD;AAGF,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 8216, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-container.tsx"], "sourcesContent": ["import BaseEmptyState from \"@/components/base-empty-state\";\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { useCurrentThread } from \"@/stores/current-thread\";\r\nimport { useInputPrompt } from \"@/stores/input-prompt\";\r\nimport { Thread as ThreadType } from \"@/types/thread-message\";\r\nimport { PlusSquare } from \"@mynaui/icons-react\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\nimport Thread from \"./thread\";\r\nimport { ThreadListCard } from \"./thread-list-card\";\r\n\r\nconst ThreadContainer = ({\r\n  setThreadToRename,\r\n}: {\r\n  setThreadToRename: (thread: { id: number; name: string } | null) => void;\r\n}) => {\r\n  const searchParams = useSearchParams();\r\n  const action = searchParams.get(\"action\");\r\n\r\n  const { prompt, setPrompt } = useInputPrompt();\r\n  const { currentThreadId, setCurrentThreadId } = useCurrentThread();\r\n  const { sendMessage } = useThread();\r\n  const { projectId, threads, isLoadingThreads } = useProject();\r\n\r\n  // send landing prompt to agent\r\n  useEffect(() => {\r\n    if (prompt && !isLoadingThreads) {\r\n      sendMessage({ content: prompt });\r\n      setPrompt(\"\");\r\n    }\r\n  }, [prompt, isLoadingThreads]);\r\n\r\n  // open thread if action is chat and there is no landing prompt\r\n  useEffect(() => {\r\n    if (\r\n      action === \"chat\" &&\r\n      !prompt &&\r\n      !isLoadingThreads &&\r\n      threads.length > 0 &&\r\n      !currentThreadId\r\n    ) {\r\n      setCurrentThreadId(threads[0].thread_id);\r\n      const newUrl = new URL(window.location.href);\r\n      newUrl.searchParams.delete(\"action\");\r\n      window.history.replaceState({}, \"\", newUrl);\r\n    }\r\n  }, [threads, setCurrentThreadId, currentThreadId, action, isLoadingThreads]);\r\n\r\n  if (isLoadingThreads) {\r\n    return (\r\n      <div className=\"h-full w-full space-y-2 p-3 pt-1\">\r\n        {[1, 2, 3].map((i) => (\r\n          <Card border={false} key={i} className=\"bg-transparent p-4\">\r\n            <div className=\"flex h-full flex-col\">\r\n              <div className=\"flex-grow space-y-2\">\r\n                <div className=\"flex items-center justify-between gap-2\">\r\n                  <Skeleton className=\"h-6 w-3/4 py-0\" />\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Skeleton className=\"h-6 w-10 py-0\" />\r\n                    <Skeleton className=\"h-6 w-8 rounded-md py-0\" />\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <Skeleton className=\"h-4 w-24\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {currentThreadId ? (\r\n        <Thread setThreadToRename={setThreadToRename} />\r\n      ) : (\r\n        <div className=\"flex h-full flex-col overflow-y-auto p-3 pt-3 md:pt-1\">\r\n          <div className=\"w-full space-y-2\">\r\n            {threads.length > 0 ? (\r\n              threads.map((thread: ThreadType, index: number) => (\r\n                <ThreadListCard\r\n                  key={thread.thread_id}\r\n                  thread={thread}\r\n                  projectId={projectId}\r\n                  index={threads.length - index}\r\n                  setThreadToRename={setThreadToRename}\r\n                />\r\n              ))\r\n            ) : (\r\n              <div className=\"flex h-[calc(100vh-10rem)] w-full items-center justify-center\">\r\n                <BaseEmptyState\r\n                  icon={<PlusSquare className=\"size-8 text-muted-foreground\" />}\r\n                  title=\"No threads found\"\r\n                  description=\"Please create a thread to start chatting.\"\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ThreadContainer;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,kBAAkB,CAAC,EACvB,iBAAiB,EAGlB;IACC,MAAM,eAAe,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;IAC3C,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD;IAC/D,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IAChC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAE1D,+BAA+B;IAC/B,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,CAAC,kBAAkB;YAC/B,YAAY;gBAAE,SAAS;YAAO;YAC9B,UAAU;QACZ;IACF,GAAG;QAAC;QAAQ;KAAiB;IAE7B,+DAA+D;IAC/D,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,WAAW,UACX,CAAC,UACD,CAAC,oBACD,QAAQ,MAAM,GAAG,KACjB,CAAC,iBACD;YACA,mBAAmB,OAAO,CAAC,EAAE,CAAC,SAAS;YACvC,MAAM,SAAS,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;YAC3C,OAAO,YAAY,CAAC,MAAM,CAAC;YAC3B,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;QACtC;IACF,GAAG;QAAC;QAAS;QAAoB;QAAiB;QAAQ;KAAiB;IAE3E,IAAI,kBAAkB;QACpB,qBACE,6VAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6VAAC,gIAAA,CAAA,OAAI;oBAAC,QAAQ;oBAAe,WAAU;8BACrC,cAAA,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6VAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAGxB,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;mBAXF;;;;;;;;;;IAmBlC;IAEA,qBACE;kBACG,gCACC,6VAAC,oIAAA,CAAA,UAAM;YAAC,mBAAmB;;;;;iCAE3B,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;0BACZ,QAAQ,MAAM,GAAG,IAChB,QAAQ,GAAG,CAAC,CAAC,QAAoB,sBAC/B,6VAAC,oJAAA,CAAA,iBAAc;wBAEb,QAAQ;wBACR,WAAW;wBACX,OAAO,QAAQ,MAAM,GAAG;wBACxB,mBAAmB;uBAJd,OAAO,SAAS;;;;8CAQzB,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,4IAAA,CAAA,UAAc;wBACb,oBAAM,6VAAC,sTAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAC5B,OAAM;wBACN,aAAY;;;;;;;;;;;;;;;;;;;;;;AAS9B;uCAEe", "debugId": null}}, {"offset": {"line": 8430, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-history.tsx"], "sourcesContent": ["import {\r\n  <PERSON>ertD<PERSON>og,\r\n  AlertDialog<PERSON>ontent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON>, CardContent, CardFooter } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { getGithubInfo, revertGithubCommit } from \"@/lib/api\";\r\nimport { formatTimestamp } from \"@/lib/format-timestamp\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useMutation, useQuery } from \"@tanstack/react-query\";\r\nimport { ChevronDown, ChevronUp, Clock, Crown } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport TaskStatus from \"../global/task-status\";\r\nimport { errorToast, successToast } from \"../global/toast\";\r\n\r\ntype Props = {\r\n  id: string;\r\n  refreshIframe?: () => void;\r\n  restartServer?: () => Promise<void>;\r\n  isActive?: boolean;\r\n};\r\n\r\nconst revertFormSchema = z.object({\r\n  revertType: z.enum([\"standard\", \"full\"]),\r\n  confirmText: z.string().optional(),\r\n});\r\n\r\ntype RevertFormValues = z.infer<typeof revertFormSchema>;\r\n\r\nconst ThreadHistory = ({ id, refreshIframe, restartServer, isActive }: Props) => {\r\n  const [expandedCommits, setExpandedCommits] = useState<Record<string, boolean>>({});\r\n  const [confirmRevert, setConfirmRevert] = useState<string | null>(null);\r\n  const { user } = useAuth();\r\n\r\n  const form = useForm<RevertFormValues>({\r\n    resolver: zodResolver(revertFormSchema),\r\n    defaultValues: {\r\n      revertType: \"standard\",\r\n      confirmText: \"\",\r\n    },\r\n  });\r\n\r\n  const revertType = form.watch(\"revertType\");\r\n  const confirmText = form.watch(\"confirmText\");\r\n\r\n  const { data: githubInfo, isLoading } = useQuery({\r\n    queryKey: [\"githubInfo\", id],\r\n    queryFn: async () => {\r\n      try {\r\n        return await getGithubInfo(id);\r\n      } catch (error: unknown) {\r\n        throw error;\r\n      }\r\n    },\r\n    enabled: !!id && user.userFromDb?.plan !== \"free-tier\" && isActive,\r\n    staleTime: 1000 * 60,\r\n  });\r\n\r\n  const revertCommitMutation = useMutation({\r\n    mutationFn: async ({ commitHash, fullReset }: { commitHash: string; fullReset: boolean }) => {\r\n      const result = await revertGithubCommit(id, commitHash, fullReset);\r\n      if (restartServer) {\r\n        await restartServer();\r\n      }\r\n      if (refreshIframe) {\r\n        refreshIframe();\r\n      }\r\n      return result;\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      successToast(\r\n        `Successfully ${variables.fullReset ? \"reset\" : \"reverted\"} commit and rebuilt project`,\r\n      );\r\n      setConfirmRevert(null);\r\n      form.reset({\r\n        revertType: \"standard\",\r\n        confirmText: \"\",\r\n      });\r\n\r\n      window.location.reload();\r\n    },\r\n    onError: (error, variables) => {\r\n      console.error(\"Revert error:\", error);\r\n      errorToast(\r\n        `Failed to ${variables.fullReset ? \"reset\" : \"revert\"} commit${error instanceof Error ? `: ${error.message}` : \"\"}`,\r\n      );\r\n    },\r\n  });\r\n\r\n  const handleRevertCommit = (commitHash: string) => {\r\n    setConfirmRevert(commitHash);\r\n  };\r\n\r\n  const confirmRevertCommit = async () => {\r\n    const values = form.getValues();\r\n    const revertAndFullReset = values.revertType === \"full\";\r\n    if (revertAndFullReset && values.confirmText !== \"sudo reset\") {\r\n      errorToast(\"Please type 'sudo reset' to proceed with full reset.\");\r\n      return;\r\n    }\r\n\r\n    if (confirmRevert) {\r\n      revertCommitMutation.mutate({\r\n        commitHash: confirmRevert,\r\n        fullReset: revertAndFullReset,\r\n      });\r\n    }\r\n  };\r\n\r\n  const toggleCommitExpansion = (commitHash: string) => {\r\n    setExpandedCommits((prev) => ({\r\n      ...prev,\r\n      [commitHash]: !prev[commitHash],\r\n    }));\r\n  };\r\n\r\n  const formatCommitMessage = (message: string) => {\r\n    const [title, ...description] = message.split(\"\\n\").filter((line) => line.trim() !== \"\");\r\n    return { title, description: description.join(\"\\n\") };\r\n  };\r\n\r\n  const isFirebaseSetupCommit = (message: string) => {\r\n    return message.toLowerCase().includes(\"firebase setup completed\");\r\n  };\r\n\r\n  //eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  const isCommitRevertible = (commit: any, commits: any[]) => {\r\n    const firebaseSetupCommit = commits.find((c) => isFirebaseSetupCommit(c.message));\r\n    if (!firebaseSetupCommit) return true;\r\n\r\n    return new Date(commit.date) >= new Date(firebaseSetupCommit.date);\r\n  };\r\n\r\n  if (user.userFromDb?.plan === \"free-tier\") {\r\n    return (\r\n      <div className=\"flex h-full w-full flex-1 flex-col items-center justify-center overflow-y-auto\">\r\n        <Card\r\n          className={cn(\r\n            \"flex h-full w-full flex-col items-center justify-center rounded-none border-none bg-transparent shadow-none\",\r\n          )}\r\n          border={false}\r\n        >\r\n          <CardContent className=\"flex w-full max-w-xl flex-col items-center justify-center space-y-6 p-6\">\r\n            <div className=\"flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2\">\r\n              <Crown className=\"size-8 text-muted-foreground\" />\r\n            </div>\r\n            <div className=\"flex items-center gap-4\">\r\n              <div className=\"space-y-1.5 text-center\">\r\n                <Typography.H4>You are on the free tier.</Typography.H4>\r\n                <Typography.P className=\"mt-0 text-balance leading-normal\">\r\n                  Please upgrade to the pro tier to view your version history.\r\n                </Typography.P>\r\n              </div>\r\n            </div>\r\n\r\n            <Button asChild className=\"w-full max-w-xs\" variant=\"default\" size=\"lg\">\r\n              <Link href=\"/pricing\" target=\"_blank\">\r\n                Upgrade\r\n              </Link>\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isLoading || !githubInfo) {\r\n    return (\r\n      <div className=\"h-full w-full space-y-2 p-3 pt-1\">\r\n        {[1, 2, 3].map((i) => (\r\n          <Card border={false} key={i} className=\"bg-transparent p-4\">\r\n            <div className=\"flex h-full flex-col\">\r\n              <div className=\"flex-grow space-y-2\">\r\n                <div className=\"flex items-center justify-between gap-2\">\r\n                  <Skeleton className=\"h-6 w-3/4 py-0\" />\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Skeleton className=\"h-6 w-10 py-0\" />\r\n                    <Skeleton className=\"h-6 w-8 rounded-md py-0\" />\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <Skeleton className=\"h-4 w-24\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-full w-full flex-1 flex-col overflow-y-auto p-3 pt-1\">\r\n      <div className=\"flex-1 overflow-y-auto pb-8\">\r\n        <div className=\"space-y-2\">\r\n          {\r\n            //eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            githubInfo.commits.map((commit: any) => {\r\n              const { title, description } = formatCommitMessage(commit.message);\r\n              const canRevert = isCommitRevertible(commit, githubInfo.commits);\r\n              return (\r\n                <Card\r\n                  key={commit.hash}\r\n                  border={false}\r\n                  className=\"mb-2 cursor-pointer overflow-hidden border-primary/10 bg-background/90 p-4 transition-all duration-200 hover:bg-accent/15\"\r\n                >\r\n                  <div className=\"flex h-full flex-col\">\r\n                    <div className=\"flex-grow space-y-1.5\">\r\n                      <div className=\"flex items-center justify-between gap-2\">\r\n                        <h3 className=\"line-clamp-2 text-sm font-medium text-foreground/90\">\r\n                          {title}\r\n                        </h3>\r\n                        <span className=\"flex-shrink-0 rounded-md bg-muted px-2 py-1 text-xs font-medium text-muted-foreground\">\r\n                          {commit.hash.substring(0, 7)}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex items-center justify-between text-xs\">\r\n                        <div className=\"flex items-center text-muted-foreground/80\">\r\n                          <Clock className=\"mr-1 h-3 w-3\" />\r\n                          {/* <span>{new Date(commit.date).toLocaleString()}</span> */}\r\n                          <span>{formatTimestamp(commit.date, \"commit-history\")}</span>\r\n                        </div>\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleRevertCommit(commit.hash);\r\n                          }}\r\n                          disabled={!canRevert}\r\n                          title={\r\n                            !canRevert\r\n                              ? \"Cannot revert before Firebase setup\"\r\n                              : \"Revert to this commit\"\r\n                          }\r\n                          className=\"h-7 bg-background text-sm hover:bg-accent/50\"\r\n                        >\r\n                          Revert\r\n                        </Button>\r\n                      </div>\r\n                      {description && (\r\n                        <>\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              toggleCommitExpansion(commit.hash);\r\n                            }}\r\n                            className=\"mt-1 h-auto p-0 text-xs text-muted-foreground hover:text-foreground\"\r\n                          >\r\n                            {expandedCommits[commit.hash] ? (\r\n                              <>\r\n                                <ChevronUp className=\"mr-1 h-3 w-3\" />\r\n                                Hide details\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <ChevronDown className=\"mr-1 h-3 w-3\" />\r\n                                Show details\r\n                              </>\r\n                            )}\r\n                          </Button>\r\n                          {expandedCommits[commit.hash] && (\r\n                            <p className=\"mt-2 whitespace-pre-wrap text-xs text-muted-foreground\">\r\n                              {description}\r\n                            </p>\r\n                          )}\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </Card>\r\n              );\r\n            })\r\n          }\r\n        </div>\r\n      </div>\r\n\r\n      <AlertDialog open={!!confirmRevert} onOpenChange={(open) => !open && setConfirmRevert(null)}>\r\n        <AlertDialogContent className=\"lg:max-w-xl\">\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Confirm Revert</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Choose how you want to revert to this commit:\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <div className=\"\">\r\n            <RadioGroup\r\n              value={revertType}\r\n              onValueChange={(value) => form.setValue(\"revertType\", value as \"standard\" | \"full\")}\r\n              className=\"space-y-4\"\r\n            >\r\n              <Card\r\n                className={cn(\r\n                  \"border-none bg-transparent shadow-none\",\r\n                  revertType === \"standard\" && \"ring-2 ring-primary/20\",\r\n                )}\r\n              >\r\n                <CardContent className=\"pt-6\">\r\n                  <div className=\"flex cursor-pointer items-center space-x-2\">\r\n                    <RadioGroupItem value=\"standard\" id=\"standard\" />\r\n                    <Label htmlFor=\"standard\" className=\"cursor-pointer text-lg font-semibold\">\r\n                      Standard Revert\r\n                    </Label>\r\n                  </div>\r\n                  <Typography.P className=\"mt-2 pl-6 text-sm text-muted-foreground\">\r\n                    This option will:\r\n                    <ul className=\"mt-1 list-inside list-disc space-y-1\">\r\n                      <li>Create a new commit that undoes the changes</li>\r\n                      <li>Keep the entire version history</li>\r\n                      <li>Can be undone by reverting the revert commit</li>\r\n                    </ul>\r\n                  </Typography.P>\r\n                  <TaskStatus variant=\"warning\" className=\"mt-3 flex items-center text-sm\">\r\n                    This may lead to a confused project context as potentially conflicting commits\r\n                    will remain.\r\n                  </TaskStatus>\r\n                </CardContent>\r\n              </Card>\r\n              <Card\r\n                border={false}\r\n                className={cn(\r\n                  \"rounded-xl border border-red-900/50 bg-transparent hover:border-red-900\",\r\n                  revertType === \"full\" && \"ring-2 ring-red-900/30\",\r\n                )}\r\n              >\r\n                <CardContent className=\"pt-6\">\r\n                  <div className=\"flex cursor-pointer items-center space-x-2\">\r\n                    <RadioGroupItem value=\"full\" id=\"full\" />\r\n                    <Label htmlFor=\"full\" className=\"cursor-pointer text-lg font-semibold\">\r\n                      Full Reset (Caution)\r\n                    </Label>\r\n                  </div>\r\n                  <Typography.P className=\"mt-2 pl-6 text-sm text-muted-foreground\">\r\n                    This option will:\r\n                    <ul className=\"mt-1 list-inside list-disc space-y-1\">\r\n                      <li>Clear all subsequent version history</li>\r\n                      <li>Reset the project entirely to the selected commit state</li>\r\n                      <li>Remove all commits after the selected one</li>\r\n                    </ul>\r\n                  </Typography.P>\r\n\r\n                  <TaskStatus variant=\"error\" className=\"mt-3 flex items-center text-sm\">\r\n                    This action cannot be undone. You&apos;ll lose all work after this commit.\r\n                  </TaskStatus>\r\n                </CardContent>\r\n                {revertType === \"full\" && (\r\n                  <CardFooter className=\"flex flex-col items-start gap-2 bg-transparent pt-4\">\r\n                    <p className=\"mt-0 text-sm font-normal text-primary/80\">\r\n                      Type <p className=\"inline font-bold text-primary\">sudo reset</p> to proceed:\r\n                    </p>\r\n                    <Input\r\n                      id=\"confirmText\"\r\n                      value={confirmText}\r\n                      onChange={(e) => form.setValue(\"confirmText\", e.target.value)}\r\n                      placeholder=\"Type sudo reset here\"\r\n                      className=\"w-full border-red-900/50\"\r\n                    />\r\n                  </CardFooter>\r\n                )}\r\n              </Card>\r\n            </RadioGroup>\r\n          </div>\r\n          <AlertDialogFooter className=\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\">\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"w-full sm:w-fit\"\r\n              onClick={() => setConfirmRevert(null)}\r\n              disabled={revertCommitMutation.isPending}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              className={cn(\"w-full sm:w-fit\")}\r\n              variant={revertType === \"full\" ? \"destructive\" : \"default\"}\r\n              onClick={confirmRevertCommit}\r\n              disabled={\r\n                revertCommitMutation.isPending ||\r\n                (revertType === \"full\" && confirmText !== \"sudo reset\")\r\n              }\r\n            >\r\n              {revertCommitMutation.isPending ? (\r\n                <>\r\n                  <Loading className=\"size-4 animate-spin text-background\" />\r\n                  Reverting...\r\n                </>\r\n              ) : revertType === \"full\" ? (\r\n                \"Confirm Full Reset\"\r\n              ) : (\r\n                \"Confirm Standard Revert\"\r\n              )}\r\n            </Button>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ThreadHistory;\r\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AASA,MAAM,mBAAmB,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,YAAY,mOAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;KAAO;IACvC,aAAa,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAClC;AAIA,MAAM,gBAAgB,CAAC,EAAE,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAS;IAC1E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACjF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,YAAY;YACZ,aAAa;QACf;IACF;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,MAAM,cAAc,KAAK,KAAK,CAAC;IAE/B,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,UAAU;YAAC;YAAc;SAAG;QAC5B,SAAS;YACP,IAAI;gBACF,OAAO,MAAM,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD,EAAE;YAC7B,EAAE,OAAO,OAAgB;gBACvB,MAAM;YACR;QACF;QACA,SAAS,CAAC,CAAC,MAAM,KAAK,UAAU,EAAE,SAAS,eAAe;QAC1D,WAAW,OAAO;IACpB;IAEA,MAAM,uBAAuB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACvC,YAAY,OAAO,EAAE,UAAU,EAAE,SAAS,EAA8C;YACtF,MAAM,SAAS,MAAM,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,YAAY;YACxD,IAAI,eAAe;gBACjB,MAAM;YACR;YACA,IAAI,eAAe;gBACjB;YACF;YACA,OAAO;QACT;QACA,WAAW,CAAC,GAAG;YACb,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EACT,CAAC,aAAa,EAAE,UAAU,SAAS,GAAG,UAAU,WAAW,2BAA2B,CAAC;YAEzF,iBAAiB;YACjB,KAAK,KAAK,CAAC;gBACT,YAAY;gBACZ,aAAa;YACf;YAEA,OAAO,QAAQ,CAAC,MAAM;QACxB;QACA,SAAS,CAAC,OAAO;YACf,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EACP,CAAC,UAAU,EAAE,UAAU,SAAS,GAAG,UAAU,SAAS,OAAO,EAAE,iBAAiB,QAAQ,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE,GAAG,IAAI;QAEvH;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;IACnB;IAEA,MAAM,sBAAsB;QAC1B,MAAM,SAAS,KAAK,SAAS;QAC7B,MAAM,qBAAqB,OAAO,UAAU,KAAK;QACjD,IAAI,sBAAsB,OAAO,WAAW,KAAK,cAAc;YAC7D,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX;QACF;QAEA,IAAI,eAAe;YACjB,qBAAqB,MAAM,CAAC;gBAC1B,YAAY;gBACZ,WAAW;YACb;QACF;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,CAAC,OAAS,CAAC;gBAC5B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,WAAW;YACjC,CAAC;IACH;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,CAAC,OAAO,GAAG,YAAY,GAAG,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,OAAO;QACrF,OAAO;YAAE;YAAO,aAAa,YAAY,IAAI,CAAC;QAAM;IACtD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,QAAQ,WAAW,GAAG,QAAQ,CAAC;IACxC;IAEA,6DAA6D;IAC7D,MAAM,qBAAqB,CAAC,QAAa;QACvC,MAAM,sBAAsB,QAAQ,IAAI,CAAC,CAAC,IAAM,sBAAsB,EAAE,OAAO;QAC/E,IAAI,CAAC,qBAAqB,OAAO;QAEjC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,oBAAoB,IAAI;IACnE;IAEA,IAAI,KAAK,UAAU,EAAE,SAAS,aAAa;QACzC,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC,gIAAA,CAAA,OAAI;gBACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;gBAEF,QAAQ;0BAER,cAAA,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,wRAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;kDAAC;;;;;;kDACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wCAAC,WAAU;kDAAmC;;;;;;;;;;;;;;;;;sCAM/D,6VAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,WAAU;4BAAkB,SAAQ;4BAAU,MAAK;sCACjE,cAAA,6VAAC,2QAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,QAAO;0CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQlD;IAEA,IAAI,aAAa,CAAC,YAAY;QAC5B,qBACE,6VAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6VAAC,gIAAA,CAAA,OAAI;oBAAC,QAAQ;oBAAe,WAAU;8BACrC,cAAA,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6VAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAGxB,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;mBAXF;;;;;;;;;;IAmBlC;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;8BAGX,AADA,6DAA6D;oBAC7D,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC;wBACtB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,oBAAoB,OAAO,OAAO;wBACjE,MAAM,YAAY,mBAAmB,QAAQ,WAAW,OAAO;wBAC/D,qBACE,6VAAC,gIAAA,CAAA,OAAI;4BAEH,QAAQ;4BACR,WAAU;sCAEV,cAAA,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAG,WAAU;8DACX;;;;;;8DAEH,6VAAC;oDAAK,WAAU;8DACb,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;;;;;;;;;;;;sDAG9B,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;;sEACb,6VAAC,wRAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEAEjB,6VAAC;sEAAM,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,IAAI,EAAE;;;;;;;;;;;;8DAEtC,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,mBAAmB,OAAO,IAAI;oDAChC;oDACA,UAAU,CAAC;oDACX,OACE,CAAC,YACG,wCACA;oDAEN,WAAU;8DACX;;;;;;;;;;;;wCAIF,6BACC;;8DACE,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,sBAAsB,OAAO,IAAI;oDACnC;oDACA,WAAU;8DAET,eAAe,CAAC,OAAO,IAAI,CAAC,iBAC3B;;0EACE,6VAAC,oSAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;qFAIxC;;0EACE,6VAAC,wSAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;gDAK7C,eAAe,CAAC,OAAO,IAAI,CAAC,kBAC3B,6VAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;;;;;;;;2BA/DR,OAAO,IAAI;;;;;oBAwEtB;;;;;;;;;;;0BAKN,6VAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM,CAAC,CAAC;gBAAe,cAAc,CAAC,OAAS,CAAC,QAAQ,iBAAiB;0BACpF,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;oBAAC,WAAU;;sCAC5B,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,0IAAA,CAAA,aAAU;gCACT,OAAO;gCACP,eAAe,CAAC,QAAU,KAAK,QAAQ,CAAC,cAAc;gCACtD,WAAU;;kDAEV,6VAAC,gIAAA,CAAA,OAAI;wCACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,eAAe,cAAc;kDAG/B,cAAA,6VAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6VAAC;oDAAI,WAAU;;sEACb,6VAAC,0IAAA,CAAA,iBAAc;4DAAC,OAAM;4DAAW,IAAG;;;;;;sEACpC,6VAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAW,WAAU;sEAAuC;;;;;;;;;;;;8DAI7E,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;oDAAC,WAAU;;wDAA0C;sEAEhE,6VAAC;4DAAG,WAAU;;8EACZ,6VAAC;8EAAG;;;;;;8EACJ,6VAAC;8EAAG;;;;;;8EACJ,6VAAC;8EAAG;;;;;;;;;;;;;;;;;;8DAGR,6VAAC,4IAAA,CAAA,UAAU;oDAAC,SAAQ;oDAAU,WAAU;8DAAiC;;;;;;;;;;;;;;;;;kDAM7E,6VAAC,gIAAA,CAAA,OAAI;wCACH,QAAQ;wCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2EACA,eAAe,UAAU;;0DAG3B,6VAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,0IAAA,CAAA,iBAAc;gEAAC,OAAM;gEAAO,IAAG;;;;;;0EAChC,6VAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAO,WAAU;0EAAuC;;;;;;;;;;;;kEAIzE,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wDAAC,WAAU;;4DAA0C;0EAEhE,6VAAC;gEAAG,WAAU;;kFACZ,6VAAC;kFAAG;;;;;;kFACJ,6VAAC;kFAAG;;;;;;kFACJ,6VAAC;kFAAG;;;;;;;;;;;;;;;;;;kEAIR,6VAAC,4IAAA,CAAA,UAAU;wDAAC,SAAQ;wDAAQ,WAAU;kEAAiC;;;;;;;;;;;;4CAIxE,eAAe,wBACd,6VAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,6VAAC;wDAAE,WAAU;;4DAA2C;0EACjD,6VAAC;gEAAE,WAAU;0EAAgC;;;;;;4DAAc;;;;;;;kEAElE,6VAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,KAAK,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC5D,aAAY;wDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtB,6VAAC,2IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,iBAAiB;oCAChC,UAAU,qBAAqB,SAAS;8CACzC;;;;;;8CAGD,6VAAC,kIAAA,CAAA,SAAM;oCACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;oCACd,SAAS,eAAe,SAAS,gBAAgB;oCACjD,SAAS;oCACT,UACE,qBAAqB,SAAS,IAC7B,eAAe,UAAU,gBAAgB;8CAG3C,qBAAqB,SAAS,iBAC7B;;0DACE,6VAAC,mIAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAwC;;uDAG3D,eAAe,SACjB,uBAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;uCAEe", "debugId": null}}]}