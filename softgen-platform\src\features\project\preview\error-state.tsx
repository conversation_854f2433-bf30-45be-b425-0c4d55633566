import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AlertCircle, RefreshCw } from "lucide-react";

interface PreviewErrorStateProps {
  onRetry: () => void;
  isLoading: boolean;
}

export function PreviewErrorState({ onRetry, isLoading }: PreviewErrorStateProps) {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-4 p-4 text-center">
      <AlertCircle className="h-12 w-12 text-destructive" />
      <h3 className="text-lg font-medium">Preview Unavailable</h3>
      <Button variant="default" onClick={onRetry} disabled={isLoading} className="gap-2">
        <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
        {isLoading ? "Restarting..." : "Retry"}
      </Button>
      <p className="mt-4 text-sm text-muted-foreground">
        If the problem persists, please contact support.
      </p>
    </div>
  );
}
