import asyncio
import logging
import re
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from core.envs.env_utils import get_env_id_and_verify_access
from core.utils.unit import Unit, ToolResult
from core.db import Database
from core.envs.env_ops import env_ops

logging.basicConfig(level=logging.INFO)

@dataclass
class TerminalTool(Unit):
    db: Database
    project_id: str

    def __init__(self, db: Database, project_id: str):
        super().__init__()
        self.db = db
        self.project_id = project_id

    def clean_terminal_output(self, output: str) -> str:
        """
        Clean terminal output by filtering empty lines and hiding sensitive information like GitHub tokens.
        """
        # Split by newlines and filter out empty lines
        lines = [line for line in output.splitlines() if line.strip()]

        # Hide GitHub tokens in each line
        cleaned_lines = []
        for line in lines:
            # Check for GitHub token pattern (ghp_)
            if 'ghp_' in line:
                # Replace ghp_ and all characters that follow until next special character
                line = re.sub(r'ghp_[a-zA-Z0-9]+', 'xx', line)
            cleaned_lines.append(line)

        # Join with newlines
        return '\n'.join(cleaned_lines)

    async def send_terminal_command(self, command: str) -> ToolResult:
        """
        Sends a command to the terminal and waits for its completion.
        """
        try:
            # Check for forbidden commands
            forbidden_commands = {
                "npm run dev": "The development server is already running. It's FORBIDDEN to start another development server.",
                "npm start": "The development server is already running. It's FORBIDDEN to start another development server.",
                "npm run build && npm start": "The development server is already running. It's FORBIDDEN to start another development server.",
            }

            for forbidden, message in forbidden_commands.items():
                if forbidden in command.lower():
                    return self.fail_response(message)

            # Ensure we're in the app directory before running the command
            if command.startswith("cd /app"):
                full_command = command
            else:
                full_command = f"cd /app && {command}"

            workspace_id = await self._get_env_id()
            result = await env_ops.execute_session(workspace_id, full_command)

            if not result["success"]:
                cleaned_output = self.clean_terminal_output(result.get("stdout", ""))
                return self.fail_response(cleaned_output)

            cleaned_output = self.clean_terminal_output(result.get("stdout", ""))

            # Check if the command was npm run build or npm install or npm i and restart PM2 if successful
            if ("npm run build" in command.lower() or "npm install" in command.lower() or "npm i" in command.lower()) and result["success"]:
                logging.info(f"npm install completed successfully, restarting PM2 processes for workspace: {workspace_id}")
                try:
                    # Restart all PM2 processes to load new packages
                    restart_result = await env_ops.execute_command(workspace_id, "pm2 restart all")
                    if not restart_result["success"]:
                        logging.warning(f"Failed to restart PM2 processes: {restart_result.get('stdout', '')}")
                    else:
                        logging.info("PM2 processes restarted successfully after npm install")
                except Exception as e:
                    logging.error(f"Failed to restart PM2 processes after npm install: {str(e)}")

            return self.success_response(cleaned_output)

        except Exception as e:
            logging.error(f"Failed to execute command: {str(e)}")
            return self.fail_response(f"Failed to execute command: {str(e)}")

    async def run_npm_build(self, capture_session_id: Optional[str] = None) -> ToolResult:
        """
        Runs 'npm run build' using execute_session and captures PM2 logs.
        Stops all PM2 processes before build and restarts them afterward.
        """
        try:
            env_id = await self._get_env_id()

            # Kill any existing npm build processes
            logging.info(f"Cleaning up any existing build processes for env id: {env_id}")
            kill_cmd = "pkill -f 'npm run build' || true; pkill -f 'next build' || true"
            await env_ops.execute_command(env_id, kill_cmd)

            # Get initial PM2 logs
            pm2_logs = await env_ops.execute_command(env_id, "pm2 logs --nostream --raw --lines 16")
            full_pm2_logs = self.clean_terminal_output(pm2_logs.get('stdout', ''))

            # Stop all PM2 processes before build
            logging.info(f"Stopping all PM2 processes for env id: {env_id}")
            stop_all = await env_ops.execute_command(env_id, "pm2 stop all")
            if not stop_all["success"]:
                logging.warning(f"Failed to stop PM2 processes: {stop_all.get('stdout', '')}")

            # Run the build command
            logging.info(f"Starting npm build for env id: {env_id}")
            build_result = await env_ops.execute_session(
                env_id,
                "cd /app && NODE_ENV=production npm run build"
            )
            logging.info(f"Build completed with code: {build_result['code']} for env id: {env_id}")

            # Start all PM2 processes regardless of build success
            logging.info(f"Starting all PM2 processes for env id: {env_id}")
            start_all = await env_ops.execute_command(env_id, "pm2 start all")
            if not start_all["success"]:
                logging.warning(f"Failed to start PM2 processes: {start_all.get('stdout', '')}")

            await env_ops.execute_command(env_id, "pm2 flush")

            if build_result["code"] != 0:
                error_msg = f"Build failed with code {build_result['code']} for env id: {env_id}"
                logging.warning(f"{error_msg}: {build_result['stdout']}")
                return self.fail_response({
                    "error": error_msg,
                    "build_logs": build_result["stdout"],
                    "pm2_logs": full_pm2_logs
                })

            logging.info("Build succeeded")
            return self.success_response({
                "message": "Build command executed successfully",
                "build_logs": build_result["stdout"],
                "pm2_logs": full_pm2_logs
            })

        except Exception as e:
            error_msg = f"Failed to execute build command: {str(e)}"
            try:
                start_all = await env_ops.execute_command(env_id, "pm2 start all")
                if not start_all["success"]:
                    logging.warning(f"Failed to start PM2 processes: {start_all.get('stdout', '')}")
            except Exception as e:
                logging.error(f"Failed to start PM2 processes: {str(e)}")

            logging.error(error_msg, exc_info=True)
            return self.fail_response({
                "error": error_msg,
                "build_logs": "No logs available due to execution failure",
                "pm2_logs": "No PM2 logs available due to execution failure"
            })

    async def check_for_errors(self, with_build: bool = False) -> ToolResult:
        """
        Runs TypeScript type checking and ESLint to check for errors in the codebase.
        If with_build=True, also runs npm build.
        """
        try:
            import asyncio
            env_id = await self._get_env_id()

            # NOTE: should be used only when vercel deployment fails
            if with_build:
                # Run npm build
                build_result = await self.run_npm_build()

                # Restart next.js to revert to development mode
                await env_ops.execute_command(env_id, "pm2 restart all")

                if not build_result.success:
                    return build_result
                return self.success_response({
                    "message": "Build completed successfully",
                    "build_output": build_result.output
                })

            await env_ops.execute_command(env_id, "pm2 flush")

            # Add timeout protection to prevent hanging
            logging.info("🔍 CODE ANALYSIS STARTING:")
            logging.info(f"  📁 Environment ID: {env_id}")
            logging.info("  🔧 Running lint check...")

            try:
                start_time = time.time()
                lint_result = await asyncio.wait_for(
                    env_ops.execute_command(env_id, "npm run lint"),
                    timeout=60.0  # 60 second timeout
                )
                lint_duration = time.time() - start_time
                logging.info(f"  ✅ Lint completed in {lint_duration:.1f}s - Success: {lint_result.get('success', False)}")
            except asyncio.TimeoutError:
                logging.warning("  ⏰ LINT TIMEOUT after 60s")
                lint_result = {
                    "success": False,
                    "stdout": "Lint operation timed out after 60 seconds",
                    "stderr": "Timeout error"
                }

            logging.info("  🔧 Running TypeScript check...")
            try:
                start_time = time.time()
                tsc_result = await asyncio.wait_for(
                    env_ops.execute_command(env_id, "npx tsc --noEmit"),
                    timeout=90.0  # 90 second timeout for TypeScript
                )
                tsc_duration = time.time() - start_time
                logging.info(f"  ✅ TypeScript completed in {tsc_duration:.1f}s - Success: {tsc_result.get('success', False)}")
            except asyncio.TimeoutError:
                logging.warning("  ⏰ TYPESCRIPT TIMEOUT after 90s")
                tsc_result = {
                    "success": False,
                    "stdout": "TypeScript check timed out after 90 seconds",
                    "stderr": "Timeout error"
                }

            await env_ops.execute_command(env_id, "pm2 flush")

            # Handle timeout cases gracefully
            lint_timed_out = "timed out" in lint_result.get("stdout", "").lower()
            tsc_timed_out = "timed out" in tsc_result.get("stdout", "").lower()

            logging.info("🏁 CODE ANALYSIS RESULTS:")
            logging.info(f"  📊 Lint success: {lint_result.get('success', False)} (timed out: {lint_timed_out})")
            logging.info(f"  📊 TypeScript success: {tsc_result.get('success', False)} (timed out: {tsc_timed_out})")

            if lint_timed_out or tsc_timed_out:
                logging.warning("  ⚠️  ANALYSIS TIMEOUTS - Continuing session gracefully")
                return self.success_response({
                    "message": "Code analysis completed with timeouts - continuing session",
                    "lint_output": lint_result.get("stdout", ""),
                    "typescript_output": tsc_result.get("stdout", ""),
                    "warning": "Some checks timed out but session continues"
                })

            if not lint_result["success"] or not tsc_result["success"]:
                logging.info("  ❌ ANALYSIS ERRORS FOUND - Returning failure response")
                return self.fail_response({
                    "lint_output": lint_result.get("stdout", ""),
                    "typescript_output": tsc_result.get("stdout", ""),
                    "error": "Found linting or type checking errors"
                })

            logging.info("  ✅ ALL ANALYSIS PASSED - No errors found")
            return self.success_response({
                "message": "No linting or type checking errors found",
                "lint_output": lint_result.get("stdout", ""),
                "typescript_output": tsc_result.get("stdout", "")
            })

        except Exception as e:
            error_msg = f"Failed to check for errors: {str(e)}"
            logging.error(error_msg, exc_info=True)
            return self.fail_response({
                "error": error_msg,
                "lint_output": "No lint output available due to execution failure",
                "typescript_output": "No TypeScript output available due to execution failure"
            })

    async def reset_next_server(self) -> ToolResult:
        """
        Completely resets the Next.js server by:
        1. Stopping and deleting all PM2 processes
        2. Removing node_modules and .next directories
        3. Reinstalling dependencies
        4. Restarting PM2 with ecosystem config
        """
        try:
            env_id = await self._get_env_id()

            # Stop and delete all PM2 processes
            await env_ops.execute_session(
                env_id,
                "cd /app && pm2 delete all"
            )

            # Remove node_modules and .next directories
            await env_ops.execute_session(
                env_id,
                "cd /app && rm -rf node_modules .next"
            )

            # Reinstall dependencies
            install_result = await env_ops.execute_session(
                env_id,
                "cd /app && npm install"
            )

            if not install_result["success"]:
                return self.fail_response(f"Failed to reinstall dependencies: {install_result.get('stdout', '')}")

            # Start PM2 with ecosystem config
            start_result = await env_ops.execute_session(
                env_id,
                "cd /app && pm2 start ecosystem.config.js"
            )

            if not start_result["success"]:
                return self.fail_response(f"Failed to start PM2: {start_result.get('stdout', '')}")

            return self.success_response("Successfully reset and restarted Next.js server")

        except Exception as e:
            error_msg = f"Failed to reset Next.js server: {str(e)}"
            logging.error(error_msg, exc_info=True)
            return self.fail_response(error_msg)

    async def _get_env_id(self) -> str:
        env_id = await get_env_id_and_verify_access(self.project_id)
        if not env_id:
            raise ValueError(f"No environment found for project ID: {self.project_id}")
        return env_id

    @staticmethod
    def schema() -> List[Dict[str, Any]]:
        """
        Returns the OpenAPI JSON schema for function calls.
        """
        return [
            {
                "type": "function",
                "function": {
                    "name": TerminalTool.run_npm_build.__name__,
                    "description": TerminalTool.run_npm_build.__doc__,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": [],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": TerminalTool.send_terminal_command.__name__,
                    "description": TerminalTool.send_terminal_command.__doc__,
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "command": {
                                "type": "string",
                                "description": "The command to execute in the terminal session.",
                            }
                        },
                        "required": ["command"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": TerminalTool.check_for_errors.__name__,
                    "description": TerminalTool.check_for_errors.__doc__,
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "with_build": {
                                "type": "boolean",
                                "description": "Whether to run npm build before checking for errors.",
                            }
                        },
                        "required": ["with_build"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": TerminalTool.reset_next_server.__name__,
                    "description": TerminalTool.reset_next_server.__doc__,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": [],
                    },
                },
            },
        ]



async def test_build():
    """
    Test the npm build functionality on a specific project.
    """
    try:
        db = Database()
        project_id = "9a1be0b6-4f38-4281-9d34-df77f3f50e9f"

        print(f"\nProcessing project: {project_id}")
        terminal_tool = TerminalTool(db, project_id)

        # Step 1: Run npm install
        print("\nStarting npm install...")
        install_result = await terminal_tool.send_terminal_command("cd /app && npm i")
        if not install_result.success:
            print("npm install failed:", install_result.output)
            return
        print("npm install output:", install_result.output)
        print("npm install completed successfully")

        # Step 2: Run npm build
        print("\nStarting npm build...")
        build_result = await terminal_tool.run_npm_build()

        print("\nBuild Process Results:")
        print("Success:", build_result.success)

        if not build_result.success:
            print("Build failed with error:", build_result.output)
            return

        print("\nBuild Output:")
        print(build_result.output)
        print("\nBuild process completed")

    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
        raise

async def test_check_for_errors():
    """
    Test the check_for_errors functionality on a specific project.
    """
    try:
        db = Database()
        project_id = "715b7241-5ebc-40f3-bbf5-53544d6015e0"  # Using the project ID from the error message
        workspace_id = "sandbox-e7933e50"  # Using the provided workspace ID

        print(f"\nTesting check_for_errors on project: {project_id} with workspace: {workspace_id}")
        terminal_tool = TerminalTool(db, project_id)

        # Override the _get_env_id method with an async function that returns our workspace ID
        async def mock_get_env_id():
            return workspace_id

        terminal_tool._get_env_id = mock_get_env_id

        # Test without build first
        print("\nRunning check_for_errors without build...")
        result = await terminal_tool.check_for_errors(with_build=False)

        print("\nCheck for Errors Results:")
        print("Success:", result.success)

        if not result.success:
            # For failure case, output is a dictionary
            error_output = result.output
            print("Check failed with error:", error_output.get("error", "Unknown error"))
            print("\nLint Output:")
            print(error_output.get("lint_output", "No lint output"))
            print("\nTypeScript Output:")
            print(error_output.get("typescript_output", "No TypeScript output"))
        else:
            # For success case, output might be a dictionary or string depending on implementation
            if isinstance(result.output, dict):
                print("\nLint Output:")
                print(result.output.get("lint_output", "No lint output"))
                print("\nTypeScript Output:")
                print(result.output.get("typescript_output", "No TypeScript output"))
            else:
                print("\nOutput:")
                print(result.output)

        # Uncomment to test with build
        # print("\nRunning check_for_errors with build...")
        # build_result = await terminal_tool.check_for_errors(with_build=True)

        # print("\nCheck for Errors with Build Results:")
        # print("Success:", build_result.success)

        # if not build_result.success:
        #     if isinstance(build_result.output, dict):
        #         print("Check with build failed with error:", build_result.output.get("error", "Unknown error"))
        #     else:
        #         print("Check with build failed with error:", build_result.output)
        # else:
        #     if isinstance(build_result.output, dict):
        #         print("Build output:", build_result.output.get("build_output", "No build output"))
        #     else:
        #         print("Build output:", build_result.output)

    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        # Uncomment the test you want to run
        # asyncio.run(test_build())
        asyncio.run(test_check_for_errors())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
        raise
