import { Button } from "@/components/ui/button";
import { WrapUpSummaryMessage } from "@/providers/thread-provider/types";
import { FC } from "react";

interface WrapUpSummaryProps {
  wrapUpSummary: WrapUpSummaryMessage["data"];
  handleSuggestionClick: (suggestion: string) => void;
}

export const WrapUpSummary: FC<WrapUpSummaryProps> = ({ wrapUpSummary, handleSuggestionClick }) => {
  return (
    <div className="my-4 rounded-lg border bg-secondary/30 p-4 shadow-sm">
      <p className="mb-4 text-sm text-foreground">{wrapUpSummary.summary}</p>
      <div className="flex flex-col gap-2">
        {wrapUpSummary.suggestions.map((suggestion, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => handleSuggestionClick(suggestion)}
            className="h-auto w-full whitespace-normal px-3 py-1.5 text-left text-xs sm:w-auto"
          >
            {suggestion}
          </Button>
        ))}
      </div>
    </div>
  );
};
