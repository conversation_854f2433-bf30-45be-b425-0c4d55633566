"use client";

import { useDebounce } from "@/hooks/use-debounce";
import { getKindeUser } from "@/lib/api";
import { debug } from "@/lib/debug";
import { AuthState, UserFromDb } from "@/types";
import {
  getCookie,
  getItemWithExpiry,
  removeCookie,
  removeItemWithExpiry,
  setCookieWithExpiry,
  setItemWithExpiry,
} from "@/utils/auth-utils";
import { AxiosError } from "axios";
import React, { useCallback, useEffect, useRef } from "react";
import { create } from "zustand";
import { persist } from "zustand/middleware";

type AuthStore = {
  user: AuthState;
  login: (access_token: string, email: string, kinde_id: string) => Promise<void>;
  logout: () => void;
  checkSubscription: (kinde_id: string, initialUserData?: UserFromDb) => Promise<UserFromDb | null>;
  initializeAuth: () => Promise<void>;
  lastCheckTimestamp: number;
  setLastCheckTimestamp: (timestamp: number) => void;
  syncTokens: () => boolean;
};

const ttl = 60 * 24 * 7 * 60 * 1000; // 7 days in milliseconds (matching backend expiry)

/**
 * Clears the persisted auth state from localStorage
 * This prevents stale auth state from persisting when tokens are expired
 */
const clearPersistedAuthState = () => {
  if (typeof window !== "undefined") {
    localStorage.removeItem("auth-storage");
  }
};

export const useAuth = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: {
        access_token: null,
        email: null,
        kinde_id: null,
        userFromDb: null,
        customer_id: null,
        isSubscribed: false,
        isLoading: true,
        showPricingDialog: false,
        cookie: {
          access_token: null,
          kinde_id: null,
        },
      },
      lastCheckTimestamp: 0,
      setLastCheckTimestamp: (timestamp) => set({ lastCheckTimestamp: timestamp }),

      initializeAuth: async () => {
        const cookieAccessToken = getCookie("access_token");
        const cookieKindeId = getCookie("kinde_id");
        const localAccessToken = getItemWithExpiry("access_token");
        const localKindeId = getItemWithExpiry("kinde_id");

        debug("Auth state check:", {
          cookieTokenExists: !!cookieAccessToken,
          localTokenExists: !!localAccessToken,
          cookieKindeExists: !!cookieKindeId,
          localKindeExists: !!localKindeId,
        });

        if (localAccessToken && localKindeId && (!cookieAccessToken || !cookieKindeId)) {
          debug("Syncing tokens from localStorage to cookies");
          setCookieWithExpiry("access_token", localAccessToken, ttl);
          setCookieWithExpiry("kinde_id", localKindeId, ttl);
        }

        if (cookieAccessToken && cookieKindeId && (!localAccessToken || !localKindeId)) {
          debug("Syncing tokens from cookies to localStorage");
          setItemWithExpiry("access_token", cookieAccessToken, ttl);
          setItemWithExpiry("kinde_id", cookieKindeId, ttl);
        }

        if (!cookieAccessToken && !cookieKindeId && !localAccessToken && !localKindeId) {
          debug("No auth tokens found, clearing persisted state and setting isLoading to false");
          clearPersistedAuthState();
          set((state) => ({ user: { ...state.user, isLoading: false } }));
          return;
        }

        const effectiveToken = cookieAccessToken || localAccessToken;
        const effectiveKindeId = cookieKindeId || localKindeId;

        if (!effectiveToken || !effectiveKindeId) {
          debug(
            "No effective tokens found, clearing persisted state and setting isLoading to false",
          );
          clearPersistedAuthState();
          set((state) => ({ user: { ...state.user, isLoading: false } }));
          return;
        }

        if (effectiveToken && effectiveKindeId) {
          setCookieWithExpiry("access_token", effectiveToken, ttl);
          setCookieWithExpiry("kinde_id", effectiveKindeId, ttl);
          setItemWithExpiry("access_token", effectiveToken, ttl);
          setItemWithExpiry("kinde_id", effectiveKindeId, ttl);
        }

        try {
          const userFromDb = await getKindeUser(effectiveKindeId);
          if (!userFromDb) {
            debug("No user found in DB, clearing persisted state and setting isLoading to false");
            clearPersistedAuthState();
            set((state) => ({ user: { ...state.user, isLoading: false } }));
            return;
          }

          const wholesalePlan =
            !userFromDb.plan || !userFromDb.plan.startsWith("wholesale")
              ? undefined
              : userFromDb.plan === "wholesale_free"
                ? "free"
                : "paid";

          debug("User data retrieved successfully");
          set((state) => ({
            user: {
              ...state.user,
              access_token: effectiveToken,
              kinde_id: effectiveKindeId,
              email: userFromDb.email,
              userFromDb,
              wholesalePlan,
              customer_id: userFromDb.stripe_customer_id || null,
              isSubscribed: userFromDb.isSubscribed || false,
              isLoading: false,
              cookie: {
                access_token: effectiveToken,
                kinde_id: effectiveKindeId,
              },
            },
          }));
        } catch (error) {
          console.error("Error during auth initialization:", error);
          if (error instanceof AxiosError && error.response?.status === 401) {
            debug("401 error during auth, clearing tokens and persisted state");
            removeCookie("access_token");
            removeCookie("kinde_id");
            removeItemWithExpiry("access_token");
            removeItemWithExpiry("kinde_id");
            clearPersistedAuthState();

            set((state) => ({
              user: {
                ...state.user,
                access_token: null,
                email: null,
                kinde_id: null,
                userFromDb: null,
                customer_id: null,
                isSubscribed: false,
                isLoading: false,
                cookie: {
                  access_token: null,
                  kinde_id: null,
                },
              },
            }));
          } else {
            debug(
              "Non-401 error during auth, clearing persisted state and setting isLoading to false",
            );
            clearPersistedAuthState();
            set((state) => ({ user: { ...state.user, isLoading: false } }));
          }
        }
      },

      syncTokens: () => {
        const cookieAccessToken = getCookie("access_token");
        const cookieKindeId = getCookie("kinde_id");
        const localAccessToken = getItemWithExpiry("access_token");
        const localKindeId = getItemWithExpiry("kinde_id");

        const effectiveToken = cookieAccessToken || localAccessToken;
        const effectiveKindeId = cookieKindeId || localKindeId;

        if (effectiveToken && effectiveKindeId) {
          setCookieWithExpiry("access_token", effectiveToken, ttl);
          setCookieWithExpiry("kinde_id", effectiveKindeId, ttl);
          setItemWithExpiry("access_token", effectiveToken, ttl);
          setItemWithExpiry("kinde_id", effectiveKindeId, ttl);

          set((state) => ({
            user: {
              ...state.user,
              access_token: effectiveToken,
              kinde_id: effectiveKindeId,
              cookie: {
                access_token: effectiveToken,
                kinde_id: effectiveKindeId,
              },
            },
          }));
          return true;
        }
        return false;
      },

      login: async (access_token, email, kinde_id) => {
        try {
          debug("Login called with token and kinde_id");
          set((state) => ({ user: { ...state.user, isLoading: true } }));

          setCookieWithExpiry("access_token", access_token, ttl);
          setCookieWithExpiry("kinde_id", kinde_id, ttl);
          setItemWithExpiry("access_token", access_token, ttl);
          setItemWithExpiry("kinde_id", kinde_id, ttl);
          setItemWithExpiry("email", email, ttl);

          const cookieToken = getCookie("access_token");
          const localToken = getItemWithExpiry("access_token");

          debug("Token verification after login:", {
            cookieTokenSet: !!cookieToken,
            localTokenSet: !!localToken,
            tokensMatch: cookieToken === access_token && localToken === access_token,
          });

          if (!cookieToken || !localToken) {
            debug("Re-attempting to set tokens");
            setTimeout(() => {
              setCookieWithExpiry("access_token", access_token, ttl);
              setCookieWithExpiry("kinde_id", kinde_id, ttl);
              setItemWithExpiry("access_token", access_token, ttl);
              setItemWithExpiry("kinde_id", kinde_id, ttl);
            }, 100);
          }

          const userFromDb = await getKindeUser(kinde_id);
          debug("User data retrieved during login");

          set({
            user: {
              access_token,
              email,
              kinde_id,
              userFromDb,
              customer_id: userFromDb?.stripe_customer_id || null,
              isSubscribed: userFromDb?.isSubscribed || false,
              isLoading: false,
              showPricingDialog: false,
              cookie: {
                access_token,
                kinde_id,
              },
            },
          });
        } catch (error) {
          console.error("Error during login:", error);
          if (error instanceof AxiosError && error.response?.status === 401) {
            removeCookie("access_token");
            removeCookie("kinde_id");
            removeItemWithExpiry("access_token");
            removeItemWithExpiry("kinde_id");
            removeItemWithExpiry("email");
            clearPersistedAuthState();

            set((state) => ({
              user: {
                ...state.user,
                access_token: null,
                email: null,
                kinde_id: null,
                userFromDb: null,
                customer_id: null,
                isSubscribed: false,
                isLoading: false,
                cookie: {
                  access_token: null,
                  kinde_id: null,
                },
              },
            }));
          } else {
            clearPersistedAuthState();
            set((state) => ({ user: { ...state.user, isLoading: false } }));
          }
        }
      },

      logout: () => {
        set({
          user: {
            access_token: null,
            email: null,
            kinde_id: null,
            userFromDb: null,
            customer_id: null,
            isSubscribed: false,
            isLoading: false,
            showPricingDialog: false,
            cookie: {
              access_token: null,
              kinde_id: null,
            },
          },
        });

        removeCookie("access_token");
        removeCookie("kinde_id");

        removeItemWithExpiry("access_token");
        removeItemWithExpiry("kinde_id");
        removeItemWithExpiry("email");

        clearPersistedAuthState();
        localStorage.clear();

        const postLogoutRedirect = encodeURIComponent(process.env.NEXT_PUBLIC_APP_URL!);
        const logoutUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/logout?post_logout_redirect_uri=${postLogoutRedirect}`;
        window.location.href = logoutUrl;
      },

      checkSubscription: async (kinde_id, initialUserData) => {
        const now = Date.now();
        const lastCheck = get().lastCheckTimestamp;

        if (now - lastCheck < 60000 && initialUserData) {
          if (initialUserData && !initialUserData.plan && initialUserData.total_free_request <= 0) {
            window.location.href = "/pricing";
          }
          return initialUserData;
        }

        try {
          const userFromDb = initialUserData || (await getKindeUser(kinde_id));

          if (!userFromDb?.plan && userFromDb?.total_free_request <= 0) {
            window.location.href = "/pricing";
            return userFromDb;
          }

          if (userFromDb?.is_creating_project) {
            const checkInterval = setInterval(async () => {
              const currentTime = Date.now();
              const lastCheckTime = get().lastCheckTimestamp;

              if (currentTime - lastCheckTime < 1000) {
                return;
              }

              try {
                const updatedUser = await getKindeUser(kinde_id);
                set((state) => ({
                  user: {
                    ...state.user,
                    userFromDb: updatedUser,
                    isSubscribed: updatedUser?.isSubscribed || false,
                  },
                  lastCheckTimestamp: currentTime,
                }));

                if (!updatedUser.is_creating_project) {
                  clearInterval(checkInterval);
                }
              } catch (error) {
                console.error("Error checking project status:", error);
              }
            }, 1000);

            setTimeout(() => clearInterval(checkInterval), 5 * 60 * 1000);
          }

          set((state) => ({
            user: {
              ...state.user,
              userFromDb,
              isSubscribed: userFromDb?.isSubscribed || false,
              isLoading: false,
            },
            lastCheckTimestamp: now,
          }));

          return userFromDb;
        } catch (error) {
          console.error("Error checking subscription:", error);
          set((state) => ({ user: { ...state.user, isLoading: false } }));
          return null;
        }
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: {
          access_token: state.user.access_token,
          email: state.user.email,
          kinde_id: state.user.kinde_id,
          customer_id: state.user.customer_id,
          isSubscribed: state.user.isSubscribed,
          showPricingDialog: state.user.showPricingDialog,
          userFromDb: state.user.userFromDb,
        },
        lastCheckTimestamp: state.lastCheckTimestamp,
      }),
    },
  ),
);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const initializeAuth = useAuth((state) => state.initializeAuth);
  const syncTokens = useAuth((state) => state.syncTokens);
  const user = useAuth((state) => state.user);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const { debouncedValue: debouncedUser } = useDebounce(user, 300);

  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === "visible") {
      debug("Tab is now active, refreshing auth state");
      syncTokens();
      initializeAuth().catch((error) => {
        console.error("Error refreshing auth state on visibility change:", error);
      });
    }
  }, [initializeAuth, syncTokens]);

  useEffect(() => {
    debug("Checking auth");

    if (!user.kinde_id) {
      initializeAuth().then(() => {
        debug("Auth initialized");
      });
    }

    if (!intervalRef.current && user.kinde_id) {
      intervalRef.current = setInterval(
        () => {
          debug("Refreshing auth state");
          initializeAuth();
        },
        30 * 60 * 1000,
      );
    }

    if (!syncIntervalRef.current && user.kinde_id) {
      syncIntervalRef.current = setInterval(
        () => {
          debug("Syncing tokens");
          syncTokens();
        },
        5 * 60 * 1000,
      );
    }

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [initializeAuth, syncTokens, handleVisibilityChange, debouncedUser.kinde_id]);

  return children;
};
