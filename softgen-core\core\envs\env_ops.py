import logging
from typing import Optional, Dict, Any, List
import os
from fastapi import HTTP<PERSON>x<PERSON>, UploadFile
import uuid
import asyncio
import aiohttp
from datetime import datetime
from core.config import settings
from daytona import (
    AsyncDaytona, AsyncSandbox, DaytonaConfig, DaytonaError,
    SessionExecuteRequest, CreateSandboxFromSnapshotParams, SandboxState,
)
from core.utils.file_utils import normalize_path, escape_path_for_shell

# Create a module-specific logger
logger = logging.getLogger("env_ops")
logger.setLevel(logging.INFO)

# Prevent propagation to the root logger to avoid duplicate logs
logger.propagate = False

# Only add handler if not already added to avoid duplicate logs
if not logger.handlers:
    # Create handler with timestamp format
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s.%(msecs)03d %(levelname)s [%(name)s]: %(message)s',
                                 datefmt='%Y-%m-%d %H:%M:%S')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

DAYTONA_TIMEOUT = 45

DEFAULT_SANDBOX_PARAMS = {
    "language": "python",
    "snapshot": settings.image,
    "env_vars": {
        "PORT": "3000",
        "LOG_LEVEL": "debug",
    },
    "public": True,
}

class EnvOps:
    def __init__(self):
        """
        Initialize EnvOps with Daytona configuration.
        """
        self.config = DaytonaConfig(
            api_key=settings.daytona_api_key,
            server_url=settings.daytona_server_url,
            target=settings.daytona_target
        )
        self.daytona = AsyncDaytona(self.config)
        self._sandbox_locks = {}

    async def read_file_contents(self, workspace_id: str, file_path: str) -> str:
        """
        Reads the contents of a file in the workspace.

        Args:
            workspace_id (str): The workspace ID
            file_path (str): Path to the file

        Returns:
            str: Contents of the file
        """
        try:
            # Check if file path contains .git and prevent access
            if '.git' in file_path:
                raise HTTPException(status_code=403, detail="Access to .git files is not allowed")

            workspace = await self.get_started_sandbox(workspace_id)

            normalized_path = normalize_path(file_path)
            escaped_path = escape_path_for_shell(normalized_path)

            exists = await self._path_exists(workspace, escaped_path)
            if not exists:
                raise HTTPException(status_code=404, detail=f"File not found: {file_path}")

            # Read file contents
            result = await workspace.fs.download_file(normalized_path)

            # Try to decode as UTF-8 text, return a message for binary files
            try:
                return result.decode('utf-8')
            except UnicodeDecodeError:
                # This is likely a binary file (image, etc.)
                logger.warning(f"File {file_path} appears to be binary and cannot be displayed as text")
                return "Binary file content - cannot be displayed as text"

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error reading file contents: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def read_directory(self, workspace_id: str, path: str) -> Dict[str, Any]:
        """
        Read directory contents with specified depth.

        Args:
            workspace_id (str): The workspace ID
            path (str): Directory path to read

        Returns:
            Dict[str, Any]: Directory structure
        """
        try:
            # Check if path contains .git and prevent access
            if '.git' in path:
                raise HTTPException(status_code=403, detail="Access to .git directories is not allowed")

            workspace = await self.get_started_sandbox(workspace_id)

            # Normalize path
            normalized_path = normalize_path(path)
            escaped_path = escape_path_for_shell(normalized_path)

            # logger.info(f"Reading directory at path: {escaped_path}")

            # First check if directory exists
            exists = await self._path_exists(workspace, escaped_path)
            if not exists:
                raise HTTPException(status_code=404, detail=f"Directory not found: {normalized_path}")

            result = await workspace.fs.list_files(normalized_path)

            files = []

            for file in result:
                name = file.name

                # Skip .git directories and files
                if name == '.git' or '.git' in name:
                    continue

                item = {
                    "name": name,
                    "isDirectory": file.is_dir,
                }

                if file.is_dir:
                    item["children"] = []

                files.append(item)

            return { "files": files }

        except HTTPException:
            raise

        except Exception as e:
            err = f"Error reading directory: {str(e)}"
            logger.exception(err)
            raise HTTPException(status_code=500, detail=err)

    async def get_file_tree(self, workspace_id: str, directory_path: str = '/app', depth: int = 4) -> Dict:
        try:
            # Now run find command from /app directory
            # Prune common transient/build directories for efficiency and redirect stderr to ignore race condition errors.
            cmd = "cd /app && find . \( -path './node_modules' -o -path './.next' -o -path './.git' \) -prune -o -type f -exec wc -l {} + 2>/dev/null"

            result = await self.execute_command(workspace_id, cmd)

            if not result["success"]:
                raise HTTPException(status_code=500, detail=f"Failed to get file tree: {result['stdout']}")

            file_tree = {}
            large_files = []

            # Process each line
            for line in result["stdout"].splitlines():
                if not line.strip():
                    continue

                try:
                    parts = line.strip().split(None, 1)
                    if len(parts) != 2:
                        continue

                    count = int(parts[0])
                    path = parts[1].replace('./', '', 1)

                    if path.endswith(('.woff', '.woff2', '.ttf', '.ico', '.png', '.jpg', '.jpeg')):
                        continue

                    if count > 300 and '/ui/' not in path:
                        large_files.append((path, count))

                    # Add file to tree
                    current = file_tree
                    path_parts = path.split('/')

                    # Create parent directories
                    for part in path_parts[:-1]:
                        if part not in current:
                            current[part] = {'type': 'directory', 'children': {}}
                        current = current[part]['children']

                    # Add the file
                    current[path_parts[-1]] = {
                        'type': 'file',
                        'lines': count,
                        'needs_split': count > 350 and '/ui/' not in path
                    }

                except (ValueError, IndexError) as e:
                    print(f"Error processing line: {line}, error: {str(e)}")
                    continue

            # Filter out system files and get actual large source files
            relevant_large_files = [
                (path, lines) for path, lines in large_files
                if not any(excluded in path for excluded in [
                    'node_modules/',
                    'package-lock.json',
                    '.next/',
                    'dist/',
                    'build/',
                    'out/'
                ])
            ]

            # Only show suggestion if we have actual large source files
            suggestion = ""
            if relevant_large_files:
                suggestion = """
                    Some files in your project are quite large and could benefit from being split into smaller modules:
                    {}

                    Recommendations:
                    - Break down large files into smaller, focused modules
                    - Keep related functionality together
                    - Consider extracting reusable utilities
                    """.format("\n".join(
                        f"- {path} ({lines} lines)"
                        for path, lines in relevant_large_files
                    ))

            return {
                "fileTree": file_tree,
                "largeFiles": relevant_large_files,
                "suggestion": suggestion
            }

        except HTTPException as http_ex:
            # Handle specific HTTP exceptions (like invalid sandbox state)
            if http_ex.status_code == 404 and "Invalid sandbox state" in str(http_ex.detail):
                logger.warning(f"Sandbox {workspace_id} is in an invalid state, returning empty file tree")
                return {
                    "fileTree": {},
                    "largeFiles": [],
                    "suggestion": "Workspace is currently unavailable. Please try again later or contact support if the issue persists.",
                    "error": "workspace_unavailable"
                }
            else:
                # Re-raise other HTTP exceptions
                raise
        except Exception as e:
            logger.error(f"Error generating file tree: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def create_file(self, workspace_id: str, path: str, content: str = '', is_directory: bool = False) -> Dict[str, Any]:
        """Create a file or directory in the workspace with retry mechanism."""
        max_retries = 3
        retry_delay = 1  # seconds
        last_exception = None

        for attempt in range(1, max_retries + 1):
            try:
                logger.info(f"Attempt {attempt}/{max_retries} to create {'directory' if is_directory else 'file'}: {path}")

                workspace = await self.get_started_sandbox(workspace_id)

                normalized_path = normalize_path(path)
                escaped_path = escape_path_for_shell(normalized_path)

                logger.info(f"Creating {'directory' if is_directory else 'file'} at path: {escaped_path}")

                if is_directory:
                    await workspace.fs.create_folder(escaped_path, mode='755')
                    return {"message": "Directory created successfully"}

                # Create parent directories if they don't exist
                parent_dir = os.path.dirname(escaped_path)
                if parent_dir:
                    await workspace.fs.create_folder(parent_dir, mode='755')

                try:
                    # Encode content to bytes
                    content_bytes = content.encode('utf-8')

                    await workspace.fs.upload_file(content_bytes, escaped_path)
                    logger.info(f"File created successfully (attempt {attempt})")

                    if not await self._verify_file(workspace, escaped_path, len(content_bytes)):
                        raise HTTPException(status_code=500, detail="File verification failed")

                    return {"message": "File created successfully"}

                except Exception as e:
                    logger.error(f"Error in file creation (attempt {attempt}): {str(e)}")
                    raise e

            except Exception as e:
                last_exception = e
                logger.error(f"Attempt {attempt}/{max_retries} failed: {str(e)}")

                if attempt < max_retries:
                    logger.info(f"Waiting {retry_delay} seconds before retry...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(f"All {max_retries} attempts to create file/directory failed")

        # If we get here, all retries failed
        if isinstance(last_exception, HTTPException):
            raise last_exception

        raise HTTPException(status_code=500, detail=f"Failed to create file/directory after {max_retries} attempts: {str(last_exception)}")

    async def update_file(self, workspace_id: str, path: str, content: str) -> Dict[str, Any]:
        """Update a file's content in the workspace with retry mechanism."""
        
        # Special handling for .env.local files
        if '.env.local' in path:
            return await self._update_env_local_file(workspace_id, content)
        
        max_retries = 3
        retry_delay = 1  # seconds
        last_exception = None

        for attempt in range(1, max_retries + 1):
            try:
                logger.info(f"Attempt {attempt}/{max_retries} to update file: {path}")

                workspace = await self.get_started_sandbox(workspace_id)

                normalized_path = normalize_path(path)
                escaped_path = escape_path_for_shell(normalized_path)

                # Check if file exists
                exists = await self._path_exists(workspace, escaped_path)
                if not exists:
                    raise HTTPException(status_code=404, detail="File not found")

                try:
                    # Encode content to bytes
                    content_bytes = content.encode('utf-8')

                    await workspace.fs.upload_file(content_bytes, escaped_path)
                    logger.info(f"File updated (attempt {attempt})")

                    if not await self._verify_file(workspace, escaped_path, len(content_bytes)):
                        raise HTTPException(status_code=500, detail="File verification failed")

                    return {"message": "File updated successfully"}

                except Exception as e:
                    logger.error(f"Error in file update (attempt {attempt}): {str(e)}")
                    raise e

            except Exception as e:
                last_exception = e
                logger.error(f"Attempt {attempt}/{max_retries} failed: {str(e)}")

                if attempt < max_retries:
                    logger.info(f"Waiting {retry_delay} seconds before retry...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(f"All {max_retries} attempts to update file failed")

        # If we get here, all retries failed
        if isinstance(last_exception, HTTPException):
            raise last_exception

        raise HTTPException(status_code=500, detail=f"Failed to update file after {max_retries} attempts: {str(last_exception)}")

    async def _update_env_local_file(self, workspace_id: str, new_content: str) -> Dict[str, Any]:
        """
        Special handler for updating .env.local files by reading existing content first,
        then updating existing variables or appending new ones.
        
        Args:
            workspace_id (str): The workspace ID
            new_content (str): New environment variables content to parse and merge
            
        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            env_path = "/app/.env.local"
            existing_vars = {}
            env_lines = []
            
            # Try to read existing .env.local file
            try:
                existing_env = await self.read_file_contents(workspace_id, env_path)
                logger.info(f"Found existing .env.local file, parsing {len(existing_env)} characters")
                
                # Parse existing variables and their line numbers
                env_lines = existing_env.split('\n')
                for i, line in enumerate(env_lines):
                    line = line.strip()
                    if line and '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        existing_vars[key.strip()] = {'value': value.strip(), 'line': i}
                
            except HTTPException as e:
                if e.status_code == 404:
                    logger.info("No existing .env.local file found, will create new one")
                    env_lines = []
                else:
                    raise
            
            # Parse new content to extract variables
            new_vars = {}
            new_content_lines = new_content.split('\n')
            for line in new_content_lines:
                line = line.strip()
                if line and '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    new_vars[key.strip()] = value.strip()
            
            # Update or append new variables
            new_env_lines = env_lines.copy()
            updated = False
            
            for key, value in new_vars.items():
                if key in existing_vars:
                    # Update existing variable
                    line_num = existing_vars[key]['line']
                    new_env_lines[line_num] = f"{key}={value}"
                    updated = True
                    logger.info(f"Updated existing variable: {key}")
                else:
                    # Add new variable
                    if not updated:
                        # Add a separator if this is the first new variable
                        new_env_lines.append("")
                        updated = True
                    new_env_lines.append(f"{key}={value}")
                    logger.info(f"Added new variable: {key}")
            
            # Join lines and update file
            updated_env = '\n'.join(new_env_lines)
            
            if env_lines:  # File existed, update it
                # Use the regular update_file method but with the special path check disabled
                workspace = await self.get_started_sandbox(workspace_id)
                normalized_path = normalize_path(env_path)
                escaped_path = escape_path_for_shell(normalized_path)
                
                content_bytes = updated_env.encode('utf-8')
                await workspace.fs.upload_file(content_bytes, escaped_path)
                logger.info(f"Updated {env_path} with merged variables")
            else:  # File didn't exist, create it
                await self.create_file(workspace_id, env_path, updated_env)
                logger.info(f"Created new {env_path} with variables")
            
            return {"message": "Environment file updated successfully"}
            
        except Exception as e:
            logger.error(f"Error updating .env.local file: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def delete_file(self, workspace_id: str, path: str) -> Dict[str, Any]:
        """
        Delete a file or directory from the workspace.

        Args:
            workspace_id (str): The workspace ID
            path (str): Path to the file or directory to delete

        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            workspace = await self.get_started_sandbox(workspace_id)

            # Normalize path
            normalized_path = normalize_path(path)
            escaped_path = escape_path_for_shell(normalized_path)

            # logger.info(f"Attempting to delete: {escaped_path}")

            # Check if path exists
            exists = await self._path_exists(workspace, escaped_path)
            if not exists:
                raise HTTPException(status_code=404, detail=f"File or directory not found: {path}")

            # Delete the file/directory
            # use shell cmd since daytona SDK can't handle directories
            cmd = f'rm -rf "{escaped_path}"'
            result = await workspace.process.exec(cmd)

            if result.exit_code != 0:
                raise HTTPException(status_code=500, detail=f"Failed to delete: {result.result}")

            # logger.info(f"Delete command result: code={result.exit_code}, output={result.result}")

            # Verify deletion
            exists = await self._path_exists(workspace, escaped_path)
            if exists:
                raise HTTPException(status_code=500, detail="Failed to verify deletion")

            return {"message": "File/directory deleted successfully"}

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting file: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def move_file(self, workspace_id: str, source_path: str, destination_path: str) -> Dict[str, Any]:
        """
        Move a file or directory from source to destination path.

        Args:
            workspace_id (str): The workspace ID
            source_path (str): Source file/directory path
            destination_path (str): Destination path

        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            workspace = await self.get_started_sandbox(workspace_id)

            # Normalize paths
            source_path = normalize_path(source_path)
            destination_path = normalize_path(destination_path)

            escaped_source = escape_path_for_shell(source_path)
            escaped_dest = escape_path_for_shell(destination_path)

            # logger.info(f"Moving from {escaped_source} to {escaped_dest}")

            # Check if source exists
            exists = await self._path_exists(workspace, escaped_source)
            if not exists:
                raise HTTPException(status_code=404, detail=f"Source file or folder not found: {source_path}")

            # Create destination directory if it doesn't exist
            dest_dir = os.path.dirname(escaped_dest)
            if dest_dir:
                await workspace.fs.create_folder(dest_dir, mode='755')

            # Move the file/directory
            # use shell cmd since it handles destintation file name automatically
            cmd = f'mv "{escaped_source}" "{escaped_dest}"'
            result = await workspace.process.exec(cmd)

            if result.exit_code != 0:
                raise HTTPException(status_code=500, detail=f"Failed to move file/directory: {result.result}")

            # Verify the move
            if not await self._path_exists(workspace, escaped_dest):
                raise HTTPException(status_code=500, detail="Failed to verify moved file/directory")

            return {"message": "File/directory moved successfully"}

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error moving file/directory: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def upload_file(self, workspace_id: str, file: UploadFile, relative_path: str, is_file: bool = False) -> Dict[str, Any]:
        """
        Upload a file to the workspace using Daytona SDK's fs.upload_file method.

        Args:
            workspace_id (str): The workspace ID
            file (UploadFile): The file to upload
            relative_path (str): Target path relative to /app
            is_file (bool): Whether to use file upload method (default: False)

        Returns:
            Dict[str, Any]: Operation result
        """
        try:
            workspace = await self.get_started_sandbox(workspace_id)

            # Ensure path starts with /app/
            relative_path = normalize_path(relative_path)
            escaped_path = escape_path_for_shell(relative_path)

            # Create parent directories if they don't exist
            parent_dir = os.path.dirname(escaped_path)
            if parent_dir:
                await workspace.fs.create_folder(parent_dir, mode='755')

            # Read file content
            content = await file.read()
            if not content:
                raise HTTPException(status_code=400, detail="File content is empty")

            await workspace.fs.upload_file(content, escaped_path)

            # Verify file exists
            exists = await self._path_exists(workspace, escaped_path)
            if not exists:
                raise HTTPException(status_code=500, detail="Failed to verify file creation")

            return {"message": "File uploaded successfully", "path": relative_path}

        except Exception as e:
            logger.error(f"Error uploading file: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            await file.seek(0)

    async def get_file_paths(self, workspace_id: str, directory_path: str = '/app', depth: int = 4) -> Dict[str, Any]:
        """
        Get all file paths recursively up to a specified depth, excluding certain directories and paths.

        Args:
            workspace_id (str): The workspace ID
            directory_path (str): Base directory path (default: '/app')
            depth (int): Maximum depth to traverse (default: 4)

        Returns:
            Dict[str, Any]: Dictionary containing list of file paths
        """
        # Define excluded directories and paths
        EXCLUDED_DIRS = {'.git', '.next', 'node_modules'}
        EXCLUDED_PATHS = {'src/components/ui'}

        try:
            workspace = await self.get_started_sandbox(workspace_id)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting workspace: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

        def is_excluded_path(relative_path: str) -> bool:
            return any(
                relative_path.startswith(excluded) or f'/{excluded}' in relative_path
                for excluded in EXCLUDED_PATHS
            )

        async def get_paths_recursive(current_path: str, current_depth: int) -> List[str]:
            if current_depth > depth:
                return []

            file_paths = []
            try:
                escaped_path = escape_path_for_shell(current_path)
                cmd = f'ls -la "{escaped_path}"'
                result = await workspace.process.exec(cmd)

                if result.exit_code != 0:
                    logger.error(f"Error reading directory {current_path}: {result.result}")
                    return []

                # Process each line of the output
                for line in result.result.splitlines()[1:]:  # Skip the first line (total)
                    parts = line.split()
                    if len(parts) < 9:
                        continue

                    name = ' '.join(parts[8:])
                    # Skip . and .. entries and excluded directories
                    if name in {'.', '..'} or name in EXCLUDED_DIRS:
                        continue

                    is_directory = parts[0].startswith('d')
                    full_path = os.path.join(current_path, name)

                    # Calculate relative path
                    try:
                        relative_path = os.path.relpath(full_path, directory_path)
                    except ValueError:
                        continue

                    # Skip if any parent directory is in excluded list
                    if any(part in EXCLUDED_DIRS for part in relative_path.split(os.sep)):
                        continue

                    # Skip excluded paths
                    if is_excluded_path(relative_path):
                        continue

                    if not is_directory:
                        file_paths.append(relative_path)
                    else:
                        # Add the directory itself to the paths
                        file_paths.append(relative_path)
                        # Recursively get paths for subdirectories
                        sub_paths = await get_paths_recursive(full_path, current_depth + 1)
                        file_paths.extend(sub_paths)

            except Exception as e:
                logger.error(f"Error processing directory {current_path}: {str(e)}")

            return file_paths

        try:
            # Normalize the directory path
            directory_path = normalize_path(directory_path)

            paths = await get_paths_recursive(directory_path, 0)
            return {"paths": paths}
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting file paths: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def ensure_project_files(self, workspace_id: str) -> None:
        """
        Check if essential project files exist and create them if they don't.
        Creates .env.local and .gitignore files.

        Args:
            workspace_id (str): The workspace ID
        """
        try:
            workspace = await self.get_started_sandbox(workspace_id)

            # Check and create .env.local
            env_check_cmd = 'sh -c \'[ -f "/app/.env.local" ] && echo "exists" || echo "not exists"\''
            env_check_result = await workspace.process.exec(env_check_cmd)

            if env_check_result.result.strip() != "exists":
                logger.info("Creating .env.local file")
                create_env_cmd = 'touch /app/.env.local'
                env_result = await workspace.process.exec(create_env_cmd)

                if env_result.exit_code != 0:
                    logger.error(f"Failed to create .env.local: {env_result.result}")

            # Check and create .gitignore
            gitignore_check_cmd = 'sh -c \'[ -f "/app/.gitignore" ] && echo "exists" || echo "not exists"\''
            gitignore_check_result = await workspace.process.exec(gitignore_check_cmd)

            if gitignore_check_result.result.strip() != "exists":
                logger.info("Creating .gitignore file")
                
                # Default .gitignore content
                gitignore_content = """# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# firebase
firebase-service-account.json
"""
                
                # Create .gitignore file with content
                await self.create_file(workspace_id, ".gitignore", gitignore_content)
                logger.info("Successfully created .gitignore file")

        except Exception as e:
            logger.error(f"Error ensuring project files exist: {str(e)}")



    async def execute_command(self, workspace_id: str, command: str, timeout: Optional[int] = None) -> Dict[str, Any]:
        """Execute a command in the workspace with retry mechanism and timeout protection."""
        max_retries = 3
        retry_delay = 1  # seconds
        last_exception = None

        # Set default timeout based on command type
        if timeout is None:
            if any(cmd in command.lower() for cmd in ['npm run lint', 'tsc', 'build', 'test']):
                timeout = 120  # 2 minutes for build/lint operations
            elif any(cmd in command.lower() for cmd in ['npm install', 'yarn install', 'pip install']):
                timeout = 300  # 5 minutes for package installations
            else:
                timeout = 60   # 1 minute for general commands

        for attempt in range(1, max_retries + 1):
            try:
                logger.info(f"⏱️  ENV_OPS EXECUTE [{attempt}/{max_retries}]: {command} (timeout: {timeout}s)")

                sandbox = await self.get_started_sandbox(workspace_id)

                # Execute with timeout protection
                try:
                    result = await asyncio.wait_for(
                        sandbox.process.exec(command, cwd='/app'),
                        timeout=timeout
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"🚨 ENV_OPS TIMEOUT: Command '{command}' timed out after {timeout}s")
                    return {
                        "success": False,
                        "message": f"Command timed out after {timeout} seconds",
                        "stdout": f"Command '{command}' exceeded timeout of {timeout} seconds",
                        "code": 124,  # Standard timeout exit code
                        "timeout": True
                    }

                logger.info(f"✅ ENV_OPS RESULT [{attempt}]: exit_code={result.exit_code}")

                return {
                    "success": result.exit_code == 0,
                    "message": "Command executed successfully" if result.exit_code == 0 else "Command execution failed",
                    "stdout": result.result,
                    "code": result.exit_code,
                    "timeout": False
                }

            except Exception as e:
                last_exception = e
                logger.error(f"Command execution attempt {attempt}/{max_retries} failed: {str(e)}")

                if attempt < max_retries:
                    logger.info(f"Waiting {retry_delay} seconds before retry...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff

        # If we get here, all retries failed
        if isinstance(last_exception, HTTPException):
            raise last_exception

        raise HTTPException(status_code=500, detail=f"Failed to execute command after {max_retries} attempts: {str(last_exception)}")

    async def execute_session(self, workspace_id: str, command: str, timeout: int = 120) -> Dict[str, Any]:
        """Execute a command in the workspace using execute_session for interactive processes with timeout protection.

        Args:
            workspace_id (str): The workspace ID
            command (str): Command to execute
            timeout (int): Timeout in seconds (default: 120)

        Returns:
            Dict[str, Any]: Operation result containing success, message, stdout, and code
        """
        try:
            workspace = await self.get_started_sandbox(workspace_id)

            # Generate a unique session ID
            session_id = str(uuid.uuid4())

            try:
                logger.info(f"⏱️  ENV_OPS SESSION: {command} (timeout: {timeout}s)")

                # Create a new session with timeout
                await asyncio.wait_for(
                    workspace.process.create_session(session_id),
                    timeout=30  # 30 seconds to create session
                )

                # Execute command in the session with timeout
                exec_result = await asyncio.wait_for(
                    workspace.process.execute_session_command(
                        session_id,
                        SessionExecuteRequest(command=command)
                    ),
                    timeout=timeout
                )

                # Get command logs with timeout
                logs = await asyncio.wait_for(
                    workspace.process.get_session_command_logs(session_id, exec_result.cmd_id),
                    timeout=10  # 10 seconds to get logs
                )

                if exec_result.exit_code != 0:
                    logger.warning(f"❌ ENV_OPS SESSION ERROR: {exec_result.exit_code} {exec_result.output}")
                    return {
                        "success": False,
                        "message": f"Command failed with exit code {exec_result.exit_code}",
                        "stdout": exec_result.output,
                        "code": exec_result.exit_code,
                        "logs": logs,
                        "timeout": False
                    }

                logger.info(f"✅ ENV_OPS SESSION SUCCESS: {exec_result.exit_code}")
                return {
                    "success": True,
                    "message": "Command executed successfully",
                    "stdout": exec_result.output,
                    "code": exec_result.exit_code,
                    "logs": logs,
                    "timeout": False
                }

            except asyncio.TimeoutError:
                logger.error(f"🚨 ENV_OPS SESSION TIMEOUT: Command '{command}' timed out after {timeout}s")
                return {
                    "success": False,
                    "message": f"Session command timed out after {timeout} seconds",
                    "stdout": f"Command '{command}' exceeded timeout of {timeout} seconds",
                    "code": 124,  # Standard timeout exit code
                    "logs": [],
                    "timeout": True
                }

            finally:
                # Always clean up the session
                try:
                    await workspace.process.delete_session(session_id)
                except Exception as e:
                    logger.warning(f"Failed to delete session {session_id}: {str(e)}")
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error executing session command: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    async def execute_supabase_sql(self, access_token: str, project_ref: str, sql_query: str, project_id: str = None) -> Dict[str, Any]:
        """
        Execute SQL query against a Supabase database using the Management API.

        Args:
            access_token (str): Supabase access token
            project_ref (str): Supabase project reference ID
            sql_query (str): SQL query to execute
            project_id (str, optional): Project ID for token refresh if needed

        Returns:
            Dict[str, Any]: Query result
        """
        try:
            logger.info(f"Executing SQL query on Supabase project {project_ref}")

            # Management API endpoint for SQL queries
            url = f"https://api.supabase.com/v1/projects/{project_ref}/database/query"

            # Prepare headers with access token
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # Prepare request body
            data = {
                "query": sql_query
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status == 201:
                        result = await response.json()
                        logger.info("SQL query executed successfully")
                        return {
                            "success": True,
                            "data": result,
                            "status_code": response.status
                        }
                    elif response.status in [401, 403] and project_id:
                        # Token might be expired, try to refresh
                        logger.info("Authentication error executing SQL query. Attempting to refresh token.")

                        # Get the connection and organization details
                        from core.db import Database
                        from core.models import SupabaseConnection, SupabaseOrganization
                        from sqlalchemy import select
                        from core.platform.supabase import supabase_service
                        from core.utils import supabase_token_manager

                        db = Database()
                        async with db.get_async_session() as db_session:
                            # Get the connection
                            conn_stmt = select(SupabaseConnection).where(
                                SupabaseConnection.project_id == project_id
                            )
                            conn_result = await db_session.execute(conn_stmt)
                            connection = conn_result.scalar_one_or_none()

                            if not connection:
                                logger.error(f"No connection found for project {project_id}")
                                return {
                                    "success": False,
                                    "error": "No Supabase connection found for this project",
                                    "status_code": response.status
                                }

                            # Get the organization using organization_id string
                            org_stmt = select(SupabaseOrganization).where(
                                SupabaseOrganization.organization_id == connection.organization_id
                            )
                            org_result = await db_session.execute(org_stmt)
                            organization = org_result.scalar_one_or_none()

                            if not organization:
                                logger.error(f"No organization found for connection with organization_id {connection.organization_id}")
                                return {
                                    "success": False,
                                    "error": "No Supabase organization found for this connection",
                                    "status_code": response.status
                                }

                            # Try to refresh the token
                            new_tokens = await supabase_token_manager.refresh_token(
                                refresh_token=organization.refresh_token,
                                refresh_url=f"{supabase_service.base_url}/v1/oauth/token",
                                client_id=supabase_service.client_id,
                                client_secret=supabase_service.client_secret
                            )

                            if new_tokens:
                                logger.info("Successfully refreshed token. Updating in database and retrying SQL query.")

                                # Update the organization in the database
                                organization.access_token = new_tokens.get("access_token")
                                organization.refresh_token = new_tokens.get("refresh_token")
                                organization.last_updated = datetime.now().isoformat()
                                await db_session.commit()

                                # Retry the query with the new token
                                return await self.execute_supabase_sql(
                                    access_token=new_tokens.get("access_token"),
                                    project_ref=project_ref,
                                    sql_query=sql_query
                                )
                            else:
                                logger.error("Failed to refresh token")
                                return {
                                    "success": False,
                                    "error": "Failed to refresh authentication token",
                                    "status_code": response.status
                                }
                    else:
                        error_text = await response.text()
                        logger.warning(f"Failed to execute SQL query: {error_text}")
                        return {
                            "success": False,
                            "error": error_text,
                            "status_code": response.status
                        }

        except Exception as e:
            logger.error(f"Error executing Supabase SQL query: {str(e)}")
            logger.exception("Full exception details:")
            return {
                "success": False,
                "error": str(e),
                "status_code": 500
            }

    async def get_started_sandbox(self, sandbox_id: str):
        # Generate a unique ID for this request to track it through logs
        request_id = uuid.uuid4().hex[:8]

        logger.info(f"[{request_id}] Attempting to get sandbox {sandbox_id}")

        # First check if the sandbox exists and its state without acquiring the lock
        # This allows multiple requests to check workspace state concurrently
        try:
            sandbox = await self.daytona.get(sandbox_id)
            if not sandbox:
                raise HTTPException(status_code=404, detail="Sandbox not found")

            current_state = sandbox.state
            logger.debug(f"Sandbox {sandbox_id} current state: {current_state}")

            if current_state == SandboxState.STARTED:
                logger.info(f"[{request_id}] Sandbox {sandbox_id} is already started")
                return sandbox

            logger.info(f"[{request_id}] Need to modify sandbox state from {current_state}, acquiring lock")

            # Get or create a lock for this specific sandbox
            if sandbox_id not in self._sandbox_locks:
                self._sandbox_locks[sandbox_id] = asyncio.Lock()
                logger.info(f"[{request_id}] Created new lock for sandbox {sandbox_id}")
            else:
                logger.info(f"[{request_id}] Using existing lock for sandbox {sandbox_id}, locked: {self._sandbox_locks[sandbox_id].locked()}")

            try:
                logger.info(f"[{request_id}] Waiting to acquire lock for sandbox {sandbox_id}")

                async with asyncio.timeout(DAYTONA_TIMEOUT):
                    # Acquire the lock to prevent concurrent modifications to the same workspace
                    async with self._sandbox_locks[sandbox_id]:
                        logger.info(f"[{request_id}] Acquired lock for sandbox {sandbox_id}")

                        sandbox = await self.daytona.get(sandbox_id)

                        if not sandbox:
                            raise HTTPException(status_code=404, detail="Sandbox not found")

                        current_state = sandbox.state
                        logger.debug(f"Sandbox {sandbox_id} current state: {current_state}")

                        if current_state != SandboxState.STARTED:
                            if current_state in [SandboxState.DESTROYED, SandboxState.DESTROYING, SandboxState.ERROR, SandboxState.UNKNOWN]:
                                raise HTTPException(status_code=404, detail="Invalid sandbox state")

                            if current_state in [SandboxState.STOPPING, SandboxState.ARCHIVING]:
                                # Wait for transitional states to complete
                                logger.info(f"Waiting for sandbox {sandbox_id} to finish {current_state}")
                                await sandbox.wait_for_sandbox_stop(DAYTONA_TIMEOUT)

                            if current_state in [SandboxState.STOPPED, SandboxState.ARCHIVED]:
                                # These states require starting the workspace
                                logger.info(f"Starting sandbox {sandbox_id} from state {current_state}")

                                try:
                                    await sandbox.start(DAYTONA_TIMEOUT)
                                except DaytonaError as de:
                                    error_message = str(de)

                                    if "State change in progress" in error_message:
                                        logger.warning(f"State change already in progress for {sandbox_id}, waiting for completion")
                                    elif "is not in valid state" in error_message:
                                        logger.warning(f"Workspace is not in valid state for {sandbox_id}, waiting for completion")
                                    else:
                                        raise

                            logger.info(f"Waiting for sandbox {sandbox_id} to start")
                            await sandbox.wait_for_sandbox_start(DAYTONA_TIMEOUT)

                            await sandbox.refresh_data()

                            if sandbox.state != SandboxState.STARTED:
                                logger.error(f"Sandbox {sandbox_id} failed to start, current state: {sandbox.state}")
                                raise HTTPException(status_code=500, detail=f"Sandbox failed to start, current state: {sandbox.state}")
                            else:
                                logger.info(f"Sandbox {sandbox_id} successfully started")

                        return sandbox
            except asyncio.TimeoutError:
                # Handle the case where lock acquisition timed out
                logger.warning(f"Timed out waiting to acquire lock for sandbox {sandbox_id}")
                raise HTTPException(status_code=503, detail="Service busy, please try again later")
        except HTTPException:
            raise
        except DaytonaError:
            logger.exception(f"Timeout waiting for sandbox {sandbox_id} to start")
            raise HTTPException(status_code=504, detail="Timeout waiting for sandbox to start")
        except Exception as e:
            logger.error(f"Error getting sandbox: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            # Clean up the lock if it's no longer in use
            # This helps prevent memory leaks if many workspaces are created and destroyed
            if sandbox_id in self._sandbox_locks and not self._sandbox_locks[sandbox_id].locked():
                logger.info(f"[{request_id}] Cleaning up unused lock for sandbox {sandbox_id}")
                del self._sandbox_locks[sandbox_id]

    async def create_sandbox(self, params: dict = {}):
        """
        Create a new Daytona sandbox environment with the provided parameters.

        This method creates a new sandbox by merging the provided parameters with
        default sandbox parameters and then calling the Daytona SDK's create_async method.

        Args:
            params (dict): Custom parameters to override the defaults.
                           Can include keys like 'language', 'image', 'env_vars', etc.

        Returns:
            AsyncSandbox: A newly created sandbox instance that can be used to
                          execute commands, manage files, and perform other operations.

        Note:
            Default parameters include Python language, the configured image from settings,
            and environment variables for PORT and LOG_LEVEL.
        """
        sandbox_params = CreateSandboxFromSnapshotParams(**{**DEFAULT_SANDBOX_PARAMS, **params})
        sandbox = await self.daytona.create(sandbox_params)
        return sandbox

    async def remove_sandbox(self, sandbox: AsyncSandbox):
        await self.daytona.delete(sandbox)

    async def _verify_file(self, workspace: AsyncSandbox, path: str, content_size: int):
        """
        Verify file was updated correctly using get_file_info
        """

        try:
            # Get file info to check if file exists and has content
            file_info = await workspace.fs.get_file_info(path)

            # Check if file exists and has expected size
            if not file_info or not hasattr(file_info, 'size'):
                logger.error("File verification failed: file info not available")
                return False

            # Verify file size is greater than 0 (quick check)
            if file_info.size <= 0 and content_size > 0:
                logger.error("File verification failed: file size is 0")
                return False

            # TODO: add file content verification if needed here

            logger.info("File verification successful")
        except Exception:
            logger.exception("File verification failed")
            return False

        return True

    async def _path_exists(self, workspace: AsyncSandbox, path: str):
        """
        Check if a path exists in the workspace.

        Args:
            workspace (AsyncSandbox): The workspace to check the path in
            path (str): Path to check

        Returns:
            bool: True if the path exists, False otherwise
        """
        try:
            # Check if path exists using sh -c
            check_cmd = f"sh -c '[ -e \"{path}\" ] && echo \"exists\"'"
            check_result = await workspace.process.exec(check_cmd)
            logger.info(f"Path existence check result: {check_result.result.strip()}")

            return check_result.result.strip() == "exists"
        except Exception:
            logger.exception("Path existence check failed")
            return False

env_ops = EnvOps()

if __name__ == "__main__":
    import asyncio

    async def main():
        result = await env_ops.execute_command(
            workspace_id="sandbox-6a857185",
            # workspace_id="sandbox-bd79762a",
            command="ls"
        )
        print("Command Output:")
        print(result["stdout"])
        print(f"\nExit Code: {result['code']}")
        print(f"Success: {result['success']}")

    asyncio.run(main())

