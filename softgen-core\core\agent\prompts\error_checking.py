
ERROR_CHECKING_RULES = """<checking_errors_rules>

 
- Make sure to frequently call check_for_errors tool in order to fix any errors

 full round of modification operations (create_file, full_file_rewrite, update_file_sections) to complete a signficanttask. ENSURE THERE ARE NO ERRORS! VALIDATION IS VERY IMPORTANT.

– Always include as the final call in your multiple tool calls to validate progress.

- EXPORT/IMPORT ERROR VALIDATION:
  When encountering the error "Element type is invalid: expected a string... but got: undefined":
  1. Immediately call check_for_errors tool to identify the specific component:
     <check_for_errors />
  2. Check the following in the identified file:
     - Component is properly exported with named export
     - Import path is correct
     - Component name matches exactly in both files
     - Verify correct export names from lucide-react
  3. After each fix, call check_for_errors tool again to verify:
     <check_for_errors />

- VERIFICATION CONFIRMATION RULES:
   - ALWAYS explicitly communicate check_for_errors results to the user
   - When no errors found: "No errors detected. What would you like to work on next?"
   - When errors found: "I found [X] errors that need to be fixed: [brief description]"
   - NEVER run check_for_errors silently without reporting the outcome  
   - Whenever you find errors, immediately Continue to fix them

- XML CONTAMINATION PREVENTION:
   - NEVER write XML action tags (<actions>, <create_file>, <update_file_sections>, etc.) inside file contents
   - XML action commands ONLY belong in the <actions></actions> block at the response level
   - When writing React/JSX/TSX files, use standard HTML/JSX syntax only: <div>, <button>, <Link>, etc.
   - If you catch yourself writing action XML inside file content, STOP and restructure your response
   - File contents must be pure code - no XML action commands ever

</checking_errors_rules>"""
