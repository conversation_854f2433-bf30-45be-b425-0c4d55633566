import logging
import asyncio
import time
import jwt
from collections import defaultdict
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState
from sqlalchemy import select
from core.db import Database
from core.models import Project, User, Environment
from core.config import settings
from core.envs.env_ops import env_ops
from core.envs.env_manager import EnvManager
from core.platform.is_admin import is_admin

# Initialize database
db = Database()

# --- GitHub Clone background task management ---
# project_id -> { 'task': asyncio.Task, 'listeners': set(), 'last_ping': float }
_GITHUB_CLONE_STATE = defaultdict(lambda: {
    'task': None,
    'listeners': set(),
    'last_ping': 0.0,
    'is_active': False
})

async def _broadcast_github_clone_update(project_id, update):
    """Broadcast update directly to all connected clients for GitHub clone."""
    if project_id not in _GITHUB_CLONE_STATE:
        logging.warning(f"No GitHub clone state found for project {project_id}")
        return

    state = _GITHUB_CLONE_STATE[project_id]

    # Log the update being broadcast
    logging.info(f"Broadcasting GitHub clone update for project {project_id}: {update}")

    # Create a task for each WebSocket to send messages concurrently
    send_tasks = []
    disconnected = set()

    for websocket in state['listeners']:
        try:
            if websocket.client_state == WebSocketState.CONNECTED and not getattr(websocket, 'is_closing', False):
                # Create a task for sending the message
                send_task = asyncio.create_task(websocket.send_json(update))
                send_tasks.append(send_task)
            else:
                disconnected.add(websocket)
        except Exception:
            logging.exception("Error creating send task")
            disconnected.add(websocket)

    # Wait for all send tasks to complete
    if send_tasks:
        try:
            await asyncio.gather(*send_tasks)
        except Exception:
            logging.exception("Error sending messages")

    # Clean up disconnected listeners
    if disconnected:
        state['listeners'] -= disconnected
        logging.info(f"Removed {len(disconnected)} disconnected listeners for project {project_id}")

async def _run_github_clone_background(project_id: str, current_user: User):
    """Run GitHub clone process in background with improved error handling."""
    logging.info("=== GITHUB CLONE PROCESS STARTED ===")
    logging.info(f"Project ID: {project_id}")
    logging.info(f"User: {current_user.email}")
    
    state = _GITHUB_CLONE_STATE[project_id]
    state['is_active'] = True
    state['last_ping'] = time.time()

    try:
        # Step 1: Get project details
        logging.info("=== STEP 1: GET PROJECT DETAILS ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "get_project",
            "status": "started",
            "message": "Getting project details..."
        })

        async with db.get_async_session() as session:
            result = await session.execute(select(Project).where(Project.project_id == project_id))
            project = result.scalar_one_or_none()

            if not project:
                logging.error(f"Project not found: {project_id}")
                await _broadcast_github_clone_update(project_id, {
                    "step": "get_project",
                    "status": "failed",
                    "error": "Project not found"
                })
                return

            logging.info("Project found:")
            logging.info(f"  - Project ID: {project.project_id}")
            logging.info(f"  - Project Name: {project.name}")
            logging.info(f"  - Current Env ID: {project.env_id}")
            logging.info(f"  - Owner ID: {project.owner_id}")
            logging.info(f"  - Created: {project.creation_date}")
            logging.info(f"  - Last Updated: {project.last_updated_date}")

            old_env_id = project.env_id
            if not old_env_id:
                logging.error("Project has no environment ID")
                await _broadcast_github_clone_update(project_id, {
                    "step": "get_project",
                    "status": "failed",
                    "error": "Project has no environment ID"
                })
                return

        await _broadcast_github_clone_update(project_id, {
            "step": "get_project",
            "status": "completed",
            "message": "Project details retrieved successfully"
        })

        # Step 2: Get unassigned environment
        logging.info("=== STEP 2: GET UNASSIGNED ENVIRONMENT ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "get_unassigned_env",
            "status": "started",
            "message": "Finding unassigned environment..."
        })

        env_manager = EnvManager()
        
        # Get the old environment details first
        async with db.get_async_session() as session:
            result = await session.execute(
                select(Environment).where(Environment.project_id == project_id)
            )
            old_env_row = result.scalar_one_or_none()
            
            if not old_env_row:
                logging.error("Project has no existing environment to replace")
                await _broadcast_github_clone_update(project_id, {
                    "step": "get_unassigned_env",
                    "status": "failed",
                    "error": "Project has no existing environment to replace"
                })
                return
            
            old_env_id = old_env_row.env_id
            logging.info("Found existing environment:")
            logging.info(f"  - Old Env ID: {old_env_id}")
            logging.info(f"  - GitHub Repo: {old_env_row.github_repo}")
            logging.info(f"  - Template: {old_env_row.template}")
            logging.info(f"  - Status: {old_env_row.status}")
            logging.info(f"  - Assigned: {old_env_row.assigned}")
            logging.info(f"  - Project ID: {old_env_row.project_id}")
            
            # Temporarily change old environment's project_id to avoid unique constraint violation
            temp_project_id = f"temp_{int(time.time())}"
            logging.info(f"Temporarily changing old environment project_id to: {temp_project_id}")
            old_env_row.project_id = temp_project_id
            await session.commit()
            logging.info("Successfully changed old environment project_id to temporary value")
            
            # Now assign a new environment (different from the old one)
            logging.info("Attempting to assign new environment...")
            env, needs_creation = await env_manager.env_db_service.assign_or_create_environment(project_id)
            
            if needs_creation:
                logging.error("No unassigned environments available")
                await _broadcast_github_clone_update(project_id, {
                    "step": "get_unassigned_env",
                    "status": "failed",
                    "error": "No unassigned environments available"
                })
                return
            
            new_env_id = env.env_id
            logging.info("Successfully assigned new environment:")
            logging.info(f"  - New Env ID: {new_env_id}")
            logging.info(f"  - GitHub Repo: {env.github_repo}")
            logging.info(f"  - Template: {env.template}")
            logging.info(f"  - Status: {env.status}")
            logging.info(f"  - Assigned: {env.assigned}")
            logging.info(f"  - Project ID: {env.project_id}")
            
            # Verify we got a different environment
            if new_env_id == old_env_id:
                logging.error(f"Error: Got the same environment ID {new_env_id} - this should not happen")
                await _broadcast_github_clone_update(project_id, {
                    "step": "get_unassigned_env",
                    "status": "failed",
                    "error": "Failed to get a different environment"
                })
                return

        await _broadcast_github_clone_update(project_id, {
            "step": "get_unassigned_env",
            "status": "completed",
            "message": f"Found environments - Old: {old_env_id}, New: {new_env_id}"
        })

        # Step 3: Get source environment git info from database
        logging.info("=== STEP 3: GET GIT INFORMATION ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "get_git_info",
            "status": "started",
            "message": "Getting source environment git information from database..."
        })

        async with db.get_async_session() as session:
            # Get the old environment's GitHub info from database
            result = await session.execute(
                select(Environment).where(Environment.env_id == old_env_id)
            )
            old_env = result.scalar_one_or_none()

            if not old_env or not old_env.github_repo:
                logging.error(f"No GitHub repository found for source environment {old_env_id}")
                await _broadcast_github_clone_update(project_id, {
                    "step": "get_git_info",
                    "status": "failed",
                    "error": f"No GitHub repository found for source environment {old_env_id}"
                })
                return

            source_git_info = {
                'remote_url': old_env.github_repo
            }
            
            logging.info("Retrieved git information:")
            logging.info(f"  - Source GitHub Repo: {source_git_info['remote_url']}")
            logging.info(f"  - Source Env ID: {old_env_id}")

        await _broadcast_github_clone_update(project_id, {
            "step": "get_git_info",
            "status": "completed",
            "message": "Git information retrieved successfully from database"
        })

        # Step 4: Setup new environment
        logging.info("=== STEP 4: SETUP NEW ENVIRONMENT ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "setup_new_env",
            "status": "started",
            "message": "Setting up new environment..."
        })

        logging.info(f"Getting started sandbox for environment: {new_env_id}")
        sandbox = await env_ops.get_started_sandbox(new_env_id)
        if not sandbox:
            logging.error(f"Failed to get started sandbox for environment {new_env_id}")
            await _broadcast_github_clone_update(project_id, {
                "step": "setup_new_env",
                "status": "failed",
                "error": f"Failed to get started sandbox for environment {new_env_id}"
            })
            return

        logging.info(f"Successfully got sandbox for environment: {new_env_id}")

        # Set git remote URL and do hard reset instead of cleanup
        remote_url = source_git_info['remote_url']
        auth_url = remote_url.replace(
            "https://github.com",
            f"https://softgenprojects:{settings.github_token}@github.com"
        )
        
        logging.info("Git configuration:")
        logging.info(f"  - Original Remote URL: {remote_url}")
        logging.info(f"  - Authenticated URL: https://softgenprojects:***@github.com{remote_url.replace('https://github.com', '')}")
        logging.info(f"  - GitHub Token: {settings.github_token[:10]}..." if settings.github_token else "No token")

        # Set the git remote origin
        set_remote_command = f"cd /app && git remote set-url origin {auth_url}"
        logging.info(f"Executing command: {set_remote_command}")
        set_remote_result = await sandbox.process.exec(set_remote_command)
        logging.info("Set remote result:")
        logging.info(f"  - Exit Code: {set_remote_result.exit_code}")
        logging.info(f"  - Output: {set_remote_result.result}")
        
        if set_remote_result.exit_code != 0:
            logging.error(f"Failed to set git remote: {set_remote_result.result}")
            await _broadcast_github_clone_update(project_id, {
                "step": "setup_new_env",
                "status": "failed",
                "error": f"Failed to set git remote: {set_remote_result.result}"
            })
            return

        # Fetch and hard reset to main branch
        fetch_reset_command = "cd /app && git fetch origin && git reset --hard origin/main"
        logging.info(f"Executing command: {fetch_reset_command}")
        fetch_reset_result = await sandbox.process.exec(fetch_reset_command)
        logging.info("Fetch and reset result:")
        logging.info(f"  - Exit Code: {fetch_reset_result.exit_code}")
        logging.info(f"  - Output: {fetch_reset_result.result}")
        
        if fetch_reset_result.exit_code != 0:
            logging.error(f"Failed to fetch and reset: {fetch_reset_result.result}")
            await _broadcast_github_clone_update(project_id, {
                "step": "setup_new_env",
                "status": "failed",
                "error": f"Failed to fetch and reset: {fetch_reset_result.result}"
            })
            return

        await _broadcast_github_clone_update(project_id, {
            "step": "setup_new_env",
            "status": "completed",
            "message": "New environment setup completed - git remote set and hard reset done"
        })

        # Step 5: Install dependencies
        logging.info("=== STEP 5: INSTALL DEPENDENCIES ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "install_deps",
            "status": "started",
            "message": "Installing dependencies..."
        })

        # Check if package.json exists first
        check_package_command = "cd /app && ls -la package.json"
        logging.info(f"Checking for package.json: {check_package_command}")
        check_result = await sandbox.process.exec(check_package_command)
        logging.info("Package.json check result:")
        logging.info(f"  - Exit Code: {check_result.exit_code}")
        logging.info(f"  - Output: {check_result.result}")

        # Run npm install directly with timeout
        npm_command = "cd /app && npm install"
        logging.info(f"Executing command: {npm_command}")
        logging.info("Timeout: 300 seconds (5 minutes)")
        npm_result = await sandbox.process.exec(npm_command, timeout=300)  # 5 minute timeout
        logging.info("NPM install result:")
        logging.info(f"  - Exit Code: {npm_result.exit_code}")
        logging.info(f"  - Output: {npm_result.result}")
        
        if npm_result.exit_code != 0:
            logging.error(f"npm install failed: {npm_result.result}")
            await _broadcast_github_clone_update(project_id, {
                "step": "install_deps",
                "status": "failed",
                "error": f"npm install failed: {npm_result.result}"
            })
            return

        await _broadcast_github_clone_update(project_id, {
            "step": "install_deps",
            "status": "completed",
            "message": "Dependencies installed successfully"
        })

        # Step 6: Start PM2
        logging.info("=== STEP 6: START PM2 ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "start_pm2",
            "status": "started",
            "message": "Starting PM2..."
        })

        # Check if ecosystem.config.js exists
        check_ecosystem_command = "cd /app && ls -la ecosystem.config.js"
        logging.info(f"Checking for ecosystem.config.js: {check_ecosystem_command}")
        ecosystem_check = await sandbox.process.exec(check_ecosystem_command)
        logging.info("Ecosystem config check result:")
        logging.info(f"  - Exit Code: {ecosystem_check.exit_code}")
        logging.info(f"  - Output: {ecosystem_check.result}")

        pm2_command = "cd /app && pm2 start ecosystem.config.js"
        logging.info(f"Executing command: {pm2_command}")
        pm2_result = await sandbox.process.exec(pm2_command)
        logging.info("PM2 start result:")
        logging.info(f"  - Exit Code: {pm2_result.exit_code}")
        logging.info(f"  - Output: {pm2_result.result}")
        
        if pm2_result.exit_code != 0:
            logging.error(f"Failed to start PM2: {pm2_result.result}")
            await _broadcast_github_clone_update(project_id, {
                "step": "start_pm2",
                "status": "failed",
                "error": "Failed to start PM2"
            })
            return

        await _broadcast_github_clone_update(project_id, {
            "step": "start_pm2",
            "status": "completed",
            "message": "PM2 started successfully"
        })

        # Step 7: Update database with environment swapping
        logging.info("=== STEP 7: UPDATE DATABASE ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "update_database",
            "status": "started",
            "message": "Updating database with environment swap..."
        })

        try:
            async with db.get_async_session() as session:
                # Get the project from the session
                result = await session.execute(
                    select(Project).where(Project.project_id == project_id)
                )
                project_row = result.scalar_one_or_none()
                
                if not project_row:
                    logging.error(f"Project row not found: {project_id}")
                    await _broadcast_github_clone_update(project_id, {
                        "step": "update_database",
                        "status": "failed",
                        "error": "Project row not found"
                    })
                    return
                
                # Get the old environment row
                result = await session.execute(
                    select(Environment).where(Environment.env_id == old_env_id)
                )
                old_env_row = result.scalar_one_or_none()
                
                if not old_env_row:
                    logging.error(f"Old environment row not found: {old_env_id}")
                    await _broadcast_github_clone_update(project_id, {
                        "step": "update_database",
                        "status": "failed",
                        "error": "Old environment row not found"
                    })
                    return
                
                # Get the new environment row
                result = await session.execute(
                    select(Environment).where(Environment.env_id == new_env_id)
                )
                new_env_row = result.scalar_one_or_none()
                
                if not new_env_row:
                    logging.error(f"New environment row not found: {new_env_id}")
                    await _broadcast_github_clone_update(project_id, {
                        "step": "update_database",
                        "status": "failed",
                        "error": "New environment row not found"
                    })
                    return
                
                logging.info("Database update strategy:")
                logging.info(f"  - Delete new environment row: {new_env_id}")
                logging.info(f"  - Update old environment row {old_env_id} with new ID: {new_env_id}")
                logging.info(f"  - Update project to point to: {new_env_id}")
                
                # Delete the new environment row
                logging.info(f"Deleting new environment row: {new_env_id}")
                await session.delete(new_env_row)
                await session.commit()
                logging.info(f"Successfully deleted new environment row: {new_env_id}")
                
                # Update the old environment row with the new environment ID
                logging.info(f"Updating old environment row {old_env_id} with new ID: {new_env_id}")
                old_env_row.env_id = new_env_id
                old_env_row.project_id = project_id
                old_env_row.assigned = True
                # Keep the original GitHub repo and template from the old environment
                logging.info("Preserving original environment data:")
                logging.info(f"  - GitHub Repo: {old_env_row.github_repo}")
                logging.info(f"  - Template: {old_env_row.template}")
                logging.info(f"  - Status: {old_env_row.status}")
                
                # Update project to point to the new environment ID
                logging.info(f"Updating project row env_id from {project_row.env_id} to {new_env_id}")
                project_row.env_id = new_env_id
                project_row.last_updated_date = datetime.now().isoformat()
                logging.info(f"Updated project to point to new environment ID: {new_env_id}")
                
                await session.commit()
                logging.info("Database update completed successfully")
                
                # Verify the update
                await session.refresh(project_row)
                logging.info(f"Verification - Project env_id after update: {project_row.env_id}")

        except Exception as e:
            logging.error(f"Database update failed: {str(e)}")
            await _broadcast_github_clone_update(project_id, {
                "step": "update_database",
                "status": "failed",
                "error": f"Database update failed: {str(e)}"
            })
            return

        await _broadcast_github_clone_update(project_id, {
            "step": "update_database",
            "status": "completed",
            "message": "Database updated successfully - new environment deleted, old environment updated"
        })

        # Step 8: Finalize
        logging.info("=== STEP 8: FINALIZE ===")
        await _broadcast_github_clone_update(project_id, {
            "step": "finalize",
            "status": "completed",
            "message": "GitHub clone completed successfully - Environment replaced",
            "data": {
                "old_env_id": old_env_id,  # The original environment ID
                "new_env_id": new_env_id,  # The new environment ID (now assigned to the old environment row)
                "github_repo": old_env_row.github_repo,  # Preserve the original GitHub repo
                "environment_replaced": True
            }
        })
        
        logging.info("=== GITHUB CLONE PROCESS COMPLETED SUCCESSFULLY ===")
        logging.info("Final state:")
        logging.info(f"  - Project ID: {project_id}")
        logging.info(f"  - Working Environment ID: {new_env_id}")
        logging.info(f"  - Original Environment ID: {old_env_id} (deleted)")
        logging.info(f"  - GitHub Repo: {old_env_row.github_repo}")

    except Exception as e:
        logging.error("=== GITHUB CLONE PROCESS FAILED ===")
        logging.error(f"Error in GitHub clone process: {str(e)}")
        
        # Rollback: Restore old environment if we have the old_env_id
        if 'old_env_id' in locals() and 'project_row' in locals():
            try:
                logging.info(f"Attempting rollback to old environment: {old_env_id}")
                async with db.get_async_session() as session:
                    # Get the project row from session
                    result = await session.execute(
                        select(Project).where(Project.project_id == project_id)
                    )
                    project_row = result.scalar_one_or_none()
                    
                    if project_row:
                        # Restore project to point to old environment
                        project_row.env_id = old_env_id
                        project_row.last_updated_date = datetime.now().isoformat()
                        logging.info(f"Restored project to point to old environment: {old_env_id}")
                    
                    # If we created a new environment, unassign it
                    if 'new_env_id' in locals():
                        result = await session.execute(
                            select(Environment).where(Environment.env_id == new_env_id)
                        )
                        new_env_row = result.scalar_one_or_none()
                        if new_env_row:
                            new_env_row.project_id = None
                            new_env_row.assigned = False
                            logging.info(f"Unassigned new environment: {new_env_id}")
                    
                    # Reassign the old environment to the project (restore from temporary project_id)
                    result = await session.execute(
                        select(Environment).where(Environment.env_id == old_env_id)
                    )
                    old_env_row = result.scalar_one_or_none()
                    if old_env_row:
                        old_env_row.project_id = project_id
                        old_env_row.assigned = True
                        logging.info(f"Reassigned old environment to project: {old_env_id}")
                    
                    await session.commit()
                    logging.info(f"Rollback completed - restored project to old environment: {old_env_id}")
                    
                    await _broadcast_github_clone_update(project_id, {
                        "error": f"Process failed: {str(e)}. Rolled back to original environment.",
                        "status": "failed",
                        "rollback": True
                    })
            except Exception as rollback_error:
                logging.error(f"Rollback failed: {str(rollback_error)}")
                await _broadcast_github_clone_update(project_id, {
                    "error": f"Process failed: {str(e)}. Rollback also failed: {str(rollback_error)}",
                    "status": "failed",
                    "rollback": False
                })
        else:
            await _broadcast_github_clone_update(project_id, {
                "error": str(e),
                "status": "failed"
            })
    finally:
        # Clean up state after done
        if project_id in _GITHUB_CLONE_STATE:
            # Clean up all listeners
            for websocket in _GITHUB_CLONE_STATE[project_id]['listeners'].copy():
                try:
                    if websocket.client_state == WebSocketState.CONNECTED:
                        await websocket.close()
                except Exception as e:
                    logging.error(f"Error closing websocket: {str(e)}")

            # Remove the entire project state
            del _GITHUB_CLONE_STATE[project_id]
            logging.info(f"Cleaned up GitHub clone state for project {project_id}")

async def send_github_clone_ping(websocket: WebSocket, project_id: str):
    """Send periodic ping messages to keep the WebSocket connection alive for GitHub clone."""
    try:
        while True:
            await asyncio.sleep(10)  # Send ping every 10 seconds
            try:
                if websocket.client_state == WebSocketState.CONNECTED and not getattr(websocket, 'is_closing', False):
                    timestamp = time.time()
                    await websocket.send_json({"type": "ping", "timestamp": timestamp})
                    # Update last ping time
                    if project_id in _GITHUB_CLONE_STATE:
                        _GITHUB_CLONE_STATE[project_id]['last_ping'] = timestamp
                else:
                    # Break the loop if WebSocket is not connected or is closing
                    logging.info(f"WebSocket not connected for project {project_id}, stopping ping loop")
                    break
            except Exception:
                logging.exception("Error sending ping")
                break
    except asyncio.CancelledError:
        logging.info(f"Ping task cancelled for project {project_id}")
        pass

async def github_clone_websocket_handler(
    websocket: WebSocket,
    project_id: str,
    token: str,
):
    """WebSocket handler for GitHub clone functionality."""
    logging.info("=== WEBSOCKET HANDLER STARTED ===")
    logging.info(f"Project ID: {project_id}")
    logging.info(f"Token: {token[:20]}...")  # Log first 20 chars of token
    
    await websocket.accept()
    logging.info(f"GitHub Clone WebSocket connection accepted for project {project_id}")

    try:
        # Authenticate user using the token
        try:
            logging.info("Attempting to decode JWT token...")
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            email = payload.get("sub")
            logging.info(f"JWT payload email: {email}")
            
            if email is None:
                logging.error("No email found in JWT payload")
                await websocket.send_json({"error": "Could not validate credentials"})
                return

            logging.info(f"Looking up user with email: {email}")
            async with db.get_async_session() as session:
                result = await session.execute(select(User).where(User.email == email))
                current_user = result.scalar_one_or_none()

            if not current_user:
                logging.error(f"No user found in database for email: {email}")
                await websocket.send_json({"error": "User not found in database"})
                return
                
            if not current_user.is_active:
                logging.error(f"User {email} is not active")
                await websocket.send_json({"error": "User account is not active"})
                return

            logging.info(f"User found: {current_user.email}, active: {current_user.is_active}")

            # Debug logging for admin check
            logging.info("=== ADMIN CHECK DEBUG ===")
            logging.info(f"User email: {current_user.email}")
            logging.info(f"Admin emails from config: {settings.admin_emails}")
            logging.info(f"Admin domains from config: {settings.admin_domains}")
            
            # Check if user is admin
            is_user_admin = is_admin(current_user)
            logging.info(f"Is user admin: {is_user_admin}")
            
            if not is_user_admin:
                logging.info(f"User {current_user.email} is not an admin. Access denied.")
                await websocket.send_json({"error": "Only administrators can perform GitHub clone operations"})
                return

            logging.info(f"User {current_user.email} is admin. Access granted.")

        except jwt.PyJWTError as e:
            logging.error(f"JWT decode error: {str(e)}")
            await websocket.send_json({"error": f"Could not validate credentials: {str(e)}"})
            return

        # Initialize state
        if project_id not in _GITHUB_CLONE_STATE:
            _GITHUB_CLONE_STATE[project_id] = {
                'listeners': set(),
                'last_ping': time.time(),
                'is_active': False,
                'task': None
            }
            logging.info(f"Initialized new GitHub clone state for project {project_id}")

        state = _GITHUB_CLONE_STATE[project_id]
        state['listeners'].add(websocket)
        logging.info(f"Added WebSocket to listeners for project {project_id}. Total listeners: {len(state['listeners'])}")

        # Start ping task
        ping_task = asyncio.create_task(send_github_clone_ping(websocket, project_id))
        logging.info(f"Started ping task for project {project_id}")

        # Start GitHub clone task if not already running
        if not state['is_active']:
            state['is_active'] = True
            clone_task = asyncio.create_task(
                _run_github_clone_background(
                    project_id=project_id,
                    current_user=current_user
                )
            )
            logging.info(f"Started GitHub clone task for project {project_id}")

        try:
            while True:
                # Check if websocket is still connected and not closing
                if websocket.client_state != WebSocketState.CONNECTED or getattr(websocket, 'is_closing', False):
                    logging.info(f"WebSocket not connected or closing for project {project_id}, stopping message loop")
                    break

                try:
                    # Wait for client messages
                    message = await websocket.receive_json()
                    logging.info(f"Received message from client for project {project_id}: {message}")

                    # Handle pong messages
                    if message.get("type") == "pong":
                        state['last_ping'] = time.time()
                except WebSocketDisconnect:
                    logging.info(f"WebSocket disconnected for project {project_id}")
                    break
                except Exception:
                    logging.exception("Error receiving message")
                    # Mark websocket as closing to prevent further send attempts
                    if websocket and not getattr(websocket, 'is_closing', False):
                        websocket.is_closing = True
                    break

        finally:
            # Cleanup
            if 'ping_task' in locals() and not ping_task.done():
                ping_task.cancel()
                try:
                    await ping_task
                except asyncio.CancelledError:
                    pass
                logging.info(f"Cancelled ping task for project {project_id}")

            if websocket in state['listeners']:
                state['listeners'].remove(websocket)
                logging.info(f"Removed WebSocket from listeners for project {project_id}. Remaining listeners: {len(state['listeners'])}")

            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.close()
                    logging.info(f"Closed WebSocket connection for project {project_id}")
                except Exception as e:
                    logging.info(f"WebSocket already closed for project {project_id}: {type(e).__name__}: {str(e)}")

            # Clean up GitHub clone state if no more listeners
            if not state['listeners']:
                state['is_active'] = False
                logging.info(f"Cleaned up GitHub clone state for project {project_id}")

    except Exception as e:
        logging.error(f"Unexpected error in GitHub clone websocket handler for project {project_id}: {str(e)}")
        if websocket.client_state == WebSocketState.CONNECTED:
            await websocket.close(code=1011, reason="Internal server error") 