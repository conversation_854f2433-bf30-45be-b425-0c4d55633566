import logging
from fastapi import APIRouter, HTTPException, Depends, Query, Response, WebSocket
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr
from core.db import Database
from core.models import User, Project, Deployment, ProjectThread, MemoryModule, ProjectAgentRun
from core.platform.user import get_current_active_user
from core.platform.is_admin import is_admin
from core.config import settings
import stripe
import csv
import io
from datetime import datetime
import json
from core.platform.github_clone_websocket import github_clone_websocket_handler

# Initialize Router with prefix
router = APIRouter(prefix="/admin")
db = Database()

# Configure Stripe - use the correct settings attribute
stripe.api_key = settings.stripe_api_key  # Changed from stripe_secret_key to stripe_api_key

# Pydantic models for request/response validation
class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None
    plan: Optional[str] = None
    project_limit: Optional[int] = None
    total_free_request: Optional[int] = None
    free_total_token: Optional[int] = None
    isSubscribed: Optional[bool] = None
    isFreeTrial: Optional[bool] = None
    isRequestBased: Optional[bool] = None

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    env_id: Optional[str] = None
    isPublic: Optional[bool] = None
    onboarding_completed: Optional[bool] = None
    team_emails: Optional[List[str]] = None

class DeploymentUpdate(BaseModel):
    vercel_token: Optional[str] = None
    deployment_url: Optional[str] = None
    production_url: Optional[str] = None
    is_deployed: Optional[bool] = None

class UserResponse(BaseModel):
    id: int
    kinde_id: str
    email: str
    is_active: bool
    is_superuser: bool
    plan: Optional[str] = None
    project_limit: int
    total_free_request: int
    free_total_token: int
    isSubscribed: bool
    stripe_customer_id: Optional[str] = None
    subscription_id: Optional[str] = None
    token_event_name: Optional[str] = None

class BillingDetails(BaseModel):
    customer_id: str
    email: Optional[str] = None
    name: Optional[str] = None
    subscription_id: Optional[str] = None
    plan: Optional[str] = None
    status: Optional[str] = None
    current_period_start: Optional[int] = None
    current_period_end: Optional[int] = None
    amount: Optional[float] = None
    currency: Optional[str] = None
    interval: Optional[str] = None
    trial_end: Optional[int] = None
    cancel_at: Optional[int] = None
    canceled_at: Optional[int] = None

class DeleteUserRequest(BaseModel):
    email: EmailStr
    confirm: bool = False

class BanUserRequest(BaseModel):
    email: EmailStr

class UnbanUserRequest(BaseModel):
    email: EmailStr

class BanResponse(BaseModel):
    status: str
    message: str
    user: Optional[Dict[str, Any]] = None

class AdminService:
    def __init__(self, db):
        self.db = db
    
    async def get_all_users(self, skip: int = 0, limit: int = 100, search: Optional[str] = None):
        async with self.db.get_async_session() as session:
            # Start with the base query
            query = select(User)
            
            # Add search filter if provided
            if search and search.strip():
                search_term = search.strip()
                # Try to convert search term to integer for ID search
                try:
                    user_id = int(search_term)
                    query = query.filter(User.id == user_id)
                except ValueError:
                    # If not a valid integer, search by email
                    query = query.filter(User.email.ilike(f"%{search_term}%"))
            
            # Apply pagination after filtering
            query = query.offset(skip).limit(limit)
            
            # Execute the query
            result = await session.execute(query)
            users = result.scalars().all()
            
            # Ensure all users have valid integer values for total_free_request
            for user in users:
                if user.total_free_request is None:
                    user.total_free_request = 0
            
            return users
    
    async def get_user_by_email(self, email: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.email == email))
            return result.scalar_one_or_none()
    
    async def update_user(self, kinde_id: str, user_data: dict):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(User).filter(User.kinde_id == kinde_id))
            user = result.scalar_one_or_none()
            
            if not user:
                return None
            
            for key, value in user_data.items():
                if hasattr(user, key) and value is not None:
                    setattr(user, key, value)
            
            await session.commit()
            await session.refresh(user)
            return user
    
    async def get_user_projects(self, user_id: int):
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Project)
                .options(selectinload(Project.deployment))
                .options(selectinload(Project.environment))
                .filter(Project.owner_id == user_id)
            )
            return result.scalars().all()
    
    async def get_project_by_id(self, project_id: str):
        async with self.db.get_async_session() as session:
            result = await session.execute(
                select(Project)
                .options(selectinload(Project.deployment))
                .options(selectinload(Project.environment))
                .filter(Project.project_id == project_id)
            )
            return result.scalar_one_or_none()
    
    async def search_project(self, search_term: str):
        """
        Search for a project by either project_id or env_id.
        
        Args:
            search_term (str): The search term to match against project_id or env_id
            
        Returns:
            Project: The found project or None if not found
        """
        async with self.db.get_async_session() as session:
            query = select(Project).options(
                selectinload(Project.deployment),
                selectinload(Project.environment)
            ).filter(
                (Project.project_id == search_term) | 
                (Project.env_id == search_term)
            )

            print("query", query)
                
            result = await session.execute(query)
            print("result", result)
            return result.scalar_one_or_none()
    
    async def update_project(self, project_id: str, project_data: dict):
        async with self.db.get_async_session() as session:
            result = await session.execute(select(Project).filter(Project.project_id == project_id))
            project = result.scalar_one_or_none()
            
            if not project:
                return None
            
            for key, value in project_data.items():
                if hasattr(project, key) and value is not None:
                    if key == 'team_emails' and isinstance(value, list):
                        setattr(project, key, json.dumps(value))
                    else:
                        setattr(project, key, value)
            
            await session.commit()
            await session.refresh(project)
            return project
    
    async def update_deployment(self, project_id: str, deployment_data: dict):
        async with self.db.get_async_session() as session:
            # Get project with deployment
            result = await session.execute(
                select(Project)
                .options(selectinload(Project.deployment))
                .filter(Project.project_id == project_id)
            )
            project = result.scalar_one_or_none()
            
            if not project:
                return None
            
            # Create deployment if it doesn't exist
            if not project.deployment:
                project.deployment = Deployment(project_id=project_id)
            
            for key, value in deployment_data.items():
                if hasattr(project.deployment, key) and value is not None:
                    setattr(project.deployment, key, value)
            
            await session.commit()
            await session.refresh(project)
            return project.deployment
    
    
    async def get_stripe_subscription_details(self, subscription_id: str):
        try:
            return stripe.Subscription.retrieve(subscription_id)
        except Exception as e:
            logging.error(f"Error retrieving Stripe subscription: {str(e)}")
            return None
    
    async def get_stripe_customer_details(self, customer_id: str):
        try:
            return stripe.Customer.retrieve(customer_id)
        except Exception as e:
            logging.error(f"Error retrieving Stripe customer: {str(e)}")
            return None
    
    async def get_billing_details(self, customer_id: str):
        try:
            # Get customer details from Stripe
            customer = stripe.Customer.retrieve(customer_id)
            
            # Get subscription details if available
            subscriptions = stripe.Subscription.list(customer=customer_id, limit=1)
            
            # Create billing details object
            billing_details = BillingDetails(
                customer_id=customer_id,
                email=customer.get('email'),
                name=customer.get('name')
            )
            
            # Check if there are any subscriptions
            if subscriptions and hasattr(subscriptions, 'data') and len(subscriptions.data) > 0:
                subscription = subscriptions.data[0]
                
                billing_details.subscription_id = subscription.id
                billing_details.status = subscription.status
                billing_details.current_period_start = subscription.current_period_start
                billing_details.current_period_end = subscription.current_period_end
                billing_details.trial_end = subscription.trial_end
                billing_details.cancel_at = subscription.cancel_at
                billing_details.canceled_at = subscription.canceled_at
                
                # Get plan details
                if hasattr(subscription, 'items') and hasattr(subscription.items, 'data') and len(subscription.items.data) > 0:
                    plan = subscription.items.data[0].plan
                    billing_details.plan = plan.nickname or plan.id
                    billing_details.amount = plan.amount / 100  # Convert cents to dollars
                    billing_details.currency = plan.currency
                    billing_details.interval = plan.interval
            
            return billing_details
            
        except Exception as e:
            logging.error(f"Error retrieving billing details: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error retrieving billing details: {str(e)}")
    
    async def get_stripe_analytics(self):
        try:
            # Get all customers
            customers = stripe.Customer.list(limit=100)
            
            # Create CSV file in memory
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write header row
            writer.writerow([
                'Customer ID', 'Email', 'Name', 'Subscription ID', 'Plan', 
                'Status', 'Amount', 'Currency', 'Interval', 'Current Period Start', 
                'Current Period End', 'Trial End', 'Cancel At', 'Canceled At'
            ])
            
            # Write data rows
            if hasattr(customers, 'data'):
                for customer in customers.data:
                    # Get subscriptions for this customer
                    subscriptions = stripe.Subscription.list(customer=customer.id, limit=10)
                    
                    # If customer has no subscriptions or subscriptions has no data attribute, write a row with just customer info
                    if not hasattr(subscriptions, 'data') or not subscriptions.data:
                        writer.writerow([
                            customer.id,
                            getattr(customer, 'email', ''),
                            getattr(customer, 'name', ''),
                            '', '', '', '', '', '', '', '', '', '', ''
                        ])
                    else:
                        # Write a row for each subscription
                        for subscription in subscriptions.data:
                            # Get plan details
                            plan_name = ''
                            amount = ''
                            currency = ''
                            interval = ''
                            
                            if hasattr(subscription, 'items') and hasattr(subscription.items, 'data') and subscription.items.data:
                                plan = subscription.items.data[0].plan
                                plan_name = getattr(plan, 'nickname', '') or getattr(plan, 'id', '')
                                amount = getattr(plan, 'amount', 0) / 100 if hasattr(plan, 'amount') else ''
                                currency = getattr(plan, 'currency', '')
                                interval = getattr(plan, 'interval', '')
                            
                            # Format dates safely
                            current_period_start = ''
                            if hasattr(subscription, 'current_period_start') and subscription.current_period_start:
                                try:
                                    current_period_start = datetime.fromtimestamp(subscription.current_period_start).strftime('%Y-%m-%d')
                                except (ValueError, TypeError, OSError):
                                    current_period_start = str(subscription.current_period_start)

                            current_period_end = ''
                            if hasattr(subscription, 'current_period_end') and subscription.current_period_end:
                                try:
                                    current_period_end = datetime.fromtimestamp(subscription.current_period_end).strftime('%Y-%m-%d')
                                except (ValueError, TypeError, OSError):
                                    current_period_end = str(subscription.current_period_end)

                            trial_end = ''
                            if hasattr(subscription, 'trial_end') and subscription.trial_end:
                                try:
                                    trial_end = datetime.fromtimestamp(subscription.trial_end).strftime('%Y-%m-%d')
                                except (ValueError, TypeError, OSError):
                                    trial_end = str(subscription.trial_end)

                            cancel_at = ''
                            if hasattr(subscription, 'cancel_at') and subscription.cancel_at:
                                try:
                                    cancel_at = datetime.fromtimestamp(subscription.cancel_at).strftime('%Y-%m-%d')
                                except (ValueError, TypeError, OSError):
                                    cancel_at = str(subscription.cancel_at)

                            canceled_at = ''
                            if hasattr(subscription, 'canceled_at') and subscription.canceled_at:
                                try:
                                    canceled_at = datetime.fromtimestamp(subscription.canceled_at).strftime('%Y-%m-%d')
                                except (ValueError, TypeError, OSError):
                                    canceled_at = str(subscription.canceled_at)
                            
                            writer.writerow([
                                customer.id,
                                getattr(customer, 'email', ''),
                                getattr(customer, 'name', ''),
                                getattr(subscription, 'id', ''),
                                plan_name,
                                getattr(subscription, 'status', ''),
                                amount,
                                currency,
                                interval,
                                current_period_start,
                                current_period_end,
                                trial_end,
                                cancel_at,
                                canceled_at
                            ])
            
            # Get the CSV content
            output.seek(0)
            return output.getvalue()
            
        except Exception as e:
            logging.error(f"Error generating Stripe analytics: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error generating Stripe analytics: {str(e)}")

    async def delete_user_account(self, email: str, confirm: bool = False):
        """
        Delete a user account and all associated data.
        
        Args:
            email (str): Email of the user to delete
            confirm (bool): Set to True to confirm deletion
        
        Returns:
            dict: Summary of deletion operation
        """
        if not confirm:
            return {
                "status": "error",
                "message": "Deletion not confirmed. Set confirm=True to proceed."
            }
        
        deletion_summary = {
            "user": None,
            "projects": [],
            "threads": 0,
            "thread_runs": 0,
            "agent_runs": 0,
            "deployments": 0,
            "memory_modules": 0,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            async with self.db.get_async_session() as session:
                # 1. Find the user
                user_query = select(User).where(User.email == email)
                user_result = await session.execute(user_query)
                user = user_result.scalar_one_or_none()
                
                if not user:
                    return {
                        "status": "error",
                        "message": f"No user found with email: {email}"
                    }
                
                # Store user details for logging
                deletion_summary["user"] = {
                    "id": user.id,
                    "email": user.email,
                    "kinde_id": user.kinde_id,
                    "plan": user.plan,
                    "is_subscribed": user.isSubscribed,
                    "project_limit": user.project_limit
                }
                
                # 2. Find all user's projects
                projects_query = select(Project).where(Project.owner_id == user.id)
                projects_result = await session.execute(projects_query)
                projects = projects_result.scalars().all()
                
                # 3. For each project, delete all associated data
                for project in projects:
                    project_summary = {
                        "project_id": project.project_id,
                        "name": project.name,
                        "env_id": project.env_id,
                        "threads": 0,
                        "thread_runs": 0,
                        "agent_runs": 0,
                        "deployments": 0,
                        "memory_modules": 0
                    }
                    
                    # 3a. Find and delete all threads and their runs
                    threads_query = select(ProjectThread).where(ProjectThread.project_id == project.project_id)
                    threads_result = await session.execute(threads_query)
                    threads = threads_result.scalars().all()
                    
                    project_summary["threads"] = len(threads)
                    
                    for thread in threads:
                        # Since ProjectThreadRun is not available, we'll skip thread runs deletion
                        # and just delete the threads
                        await session.delete(thread)
                    
                    # 3b. Delete agent runs
                    agent_runs_query = select(ProjectAgentRun).where(ProjectAgentRun.project_id == project.project_id)
                    agent_runs_result = await session.execute(agent_runs_query)
                    agent_runs = agent_runs_result.scalars().all()
                    
                    project_summary["agent_runs"] = len(agent_runs)
                    deletion_summary["agent_runs"] += len(agent_runs)
                    
                    for agent_run in agent_runs:
                        await session.delete(agent_run)
                    
                    # 3c. Delete deployments
                    deployment_query = select(Deployment).where(Deployment.project_id == project.project_id)
                    deployment_result = await session.execute(deployment_query)
                    deployment = deployment_result.scalar_one_or_none()
                    
                    if deployment:
                        project_summary["deployments"] = 1
                        deletion_summary["deployments"] += 1
                        await session.delete(deployment)
                    
                    # 3d. Delete memory modules
                    memory_query = select(MemoryModule).where(MemoryModule.project_id == project.project_id)
                    memory_result = await session.execute(memory_query)
                    memory_modules = memory_result.scalars().all()
                    
                    project_summary["memory_modules"] = len(memory_modules)
                    deletion_summary["memory_modules"] += len(memory_modules)
                    
                    for module in memory_modules:
                        await session.delete(module)
                    
                    # 3e. Delete the project itself
                    await session.delete(project)
                    
                    deletion_summary["projects"].append(project_summary)
                    deletion_summary["threads"] += project_summary["threads"]
                
                # 4. Finally, delete the user
                await session.delete(user)
                await session.commit()
                deletion_summary["status"] = "success"
                deletion_summary["message"] = f"User {email} and all associated data have been deleted"
                
                return deletion_summary
                
        except Exception as e:
            logging.error(f"Error during user deletion: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "status": "error",
                "message": f"Error during user deletion: {str(e)}"
            }

    async def ban_user(self, email: str):
        """Ban a user by setting their plan to "banned" """
        try:
            async with self.db.get_async_session() as session:
                result = await session.execute(select(User).filter(User.email == email))
                user = result.scalar_one_or_none()
                
                if not user:
                    return {
                        "status": "error",
                        "message": f"No user found with email: {email}"
                    }
                
                if user.plan == "banned":
                    return {
                        "status": "error",
                        "message": f"User {email} is already banned"
                    }
                
                previous_plan = user.plan
                user.plan = "banned"
                user.is_active = False
                
                await session.commit()
                await session.refresh(user)
                
                return {
                    "status": "success",
                    "message": f"User {email} has been banned successfully",
                    "user": {
                        "id": user.id,
                        "email": user.email,
                        "kinde_id": user.kinde_id,
                        "previous_plan": previous_plan,
                        "current_plan": user.plan,
                        "is_active": user.is_active
                    }
                }
                
        except Exception as e:
            logging.error(f"Error banning user {email}: {str(e)}")
            return {
                "status": "error",
                "message": f"Error banning user: {str(e)}"
            }

    async def unban_user(self, email: str):
        """Unban a user by setting their plan to "free" """
        try:
            async with self.db.get_async_session() as session:
                result = await session.execute(select(User).filter(User.email == email))
                user = result.scalar_one_or_none()
                
                if not user:
                    return {
                        "status": "error",
                        "message": f"No user found with email: {email}"
                    }
                
                if user.plan != "banned":
                    return {
                        "status": "error",
                        "message": f"User {email} is not currently banned"
                    }
                
                user.plan = "free"
                user.is_active = True
                
                await session.commit()
                await session.refresh(user)
                
                return {
                    "status": "success",
                    "message": f"User {email} has been unbanned successfully",
                    "user": {
                        "id": user.id,
                        "email": user.email,
                        "kinde_id": user.kinde_id,
                        "previous_plan": "banned",
                        "current_plan": user.plan,
                        "is_active": user.is_active
                    }
                }
                
        except Exception as e:
            logging.error(f"Error unbanning user {email}: {str(e)}")
            return {
                "status": "error",
                "message": f"Error unbanning user: {str(e)}"
            }

# Initialize the admin service
admin_service = AdminService(db)

# Helper function to check if user is admin
async def admin_required(current_user: User = Depends(get_current_active_user)):
    if not is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to access this resource"
        )
    print("f1")
    return current_user

@router.websocket("/github-clone/{project_id}")
async def github_clone_websocket(
    websocket: WebSocket,
    project_id: str,
    token: str = Query(...),
):
    """WebSocket endpoint for GitHub clone functionality (admin only)."""
    print("project id",project_id)
    await github_clone_websocket_handler(websocket, project_id, token)

# Routes
@router.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None,
    current_user: User = Depends(admin_required)
):
    """
    Get all users with pagination and optional search (admin only)
    
    - Use `skip` and `limit` parameters for pagination
    - Use `search` parameter to search for users by either:
      - Email (partial match)
      - User ID (exact match)
    """
    users = await admin_service.get_all_users(skip, limit, search)
    return users

@router.get("/users/{email}", response_model=UserResponse)
async def get_user_by_email(
    email: str,
    current_user: User = Depends(admin_required)
):
    """Get user by email (admin only)"""
    user = await admin_service.get_user_by_email(email)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/users/{kinde_id}")
async def update_user(
    kinde_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(admin_required)
):
    """Update user details (admin only)"""
    updated_user = await admin_service.update_user(kinde_id, user_data.dict(exclude_unset=True))
    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found")
    return updated_user

@router.get("/users/{email}/projects")
async def get_user_projects(
    email: str,
    current_user: User = Depends(admin_required)
):
    """Get all projects for a user (admin only)"""
    user = await admin_service.get_user_by_email(email)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    projects = await admin_service.get_user_projects(user.id)
    return projects

@router.get("/projects/search")
async def search_project(
    search_term: str = Query(..., description="Search term to match against project_id or env_id"),
    current_user: User = Depends(admin_required)
):
    """
    Search for a project by project_id or env_id (admin only)
    
    - Provide a search term that matches either project_id or env_id
    - Returns 404 if no project is found
    """
    project = await admin_service.search_project(search_term=search_term)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.get("/projects/{project_id}")
async def get_project(
    project_id: str,
    current_user: User = Depends(admin_required)
):
    """Get project details (admin only)"""
    project = await admin_service.get_project_by_id(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.put("/projects/{project_id}")
async def update_project(
    project_id: str,
    project_data: ProjectUpdate,
    current_user: User = Depends(admin_required)
):
    """Update project details (admin only)"""
    updated_project = await admin_service.update_project(
        project_id, 
        project_data.dict(exclude_unset=True)
    )
    if not updated_project:
        raise HTTPException(status_code=404, detail="Project not found")
    return updated_project

@router.put("/projects/{project_id}/deployment")
async def update_deployment(
    project_id: str,
    deployment_data: DeploymentUpdate,
    current_user: User = Depends(admin_required)
):
    """Update deployment details (admin only)"""
    updated_deployment = await admin_service.update_deployment(
        project_id, 
        deployment_data.dict(exclude_unset=True)
    )
    if not updated_deployment:
        raise HTTPException(status_code=404, detail="Project not found")
    return updated_deployment

@router.get("/users/{kinde_id}/transactions")
async def get_user_transactions(
    kinde_id: str,
    current_user: User = Depends(admin_required)
):
    """Get all transactions for a user (admin only)"""
    transactions = await admin_service.get_user_transactions(kinde_id)
    return transactions

@router.get("/billing/customer/{customer_id}", response_model=BillingDetails)
async def get_billing_details(
    customer_id: str,
    current_user: User = Depends(admin_required)
):
    """Get billing details for a customer (admin only)"""
    billing_details = await admin_service.get_billing_details(customer_id)
    return billing_details

@router.get("/billing/analytics/download")
async def download_stripe_analytics(
    current_user: User = Depends(admin_required)
):
    """Download Stripe analytics as CSV (admin only)"""
    csv_content = await admin_service.get_stripe_analytics()
    
    # Create response with CSV content
    response = Response(content=csv_content)
    response.headers["Content-Disposition"] = f"attachment; filename=stripe_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    response.headers["Content-Type"] = "text/csv"
    
    return response

@router.post("/users/delete")
async def delete_user(
    request: DeleteUserRequest,
    current_user: User = Depends(admin_required)
):
    """
    Delete a user account and all associated data (admin only)
    
    - Set `confirm=true` to confirm deletion
    """
    # Double-check that the current user is an admin
    if not is_admin(current_user):
        raise HTTPException(
            status_code=403,
            detail="Only administrators can delete user accounts"
        )
    
    result = await admin_service.delete_user_account(
        email=request.email,
        confirm=request.confirm
    )
    
    if result.get("status") == "error":
        raise HTTPException(status_code=400, detail=result.get("message"))
    
    return result

@router.post("/users/ban", response_model=BanResponse)
async def ban_user(
    request: BanUserRequest,
    current_user: User = Depends(admin_required)
):
    """Ban a user by setting their plan to "banned" (admin only)"""
    result = await admin_service.ban_user(email=request.email)
    
    if result.get("status") == "error":
        raise HTTPException(status_code=400, detail=result.get("message"))
    
    return result

@router.post("/users/unban", response_model=BanResponse)
async def unban_user(
    request: UnbanUserRequest,
    current_user: User = Depends(admin_required)
):
    """Unban a user by setting their plan to "free" (admin only)"""
    result = await admin_service.unban_user(email=request.email)
    
    if result.get("status") == "error":
        raise HTTPException(status_code=400, detail=result.get("message"))
    
    return result 