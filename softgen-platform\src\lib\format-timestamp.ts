import { format, formatDistanceToNow, isToday, isYesterday } from "date-fns";

export function formatTimestamp(
  isoString: string,
  type:
    | "edited"
    | "lastChat"
    | "message-timestamp"
    | "deployed"
    | "default"
    | "commit-history" = "default",
  year: boolean = false,
): string {
  if (!isoString || isoString === "null" || isoString === "undefined") {
    return "Unknown date";
  }

  const utcString = isoString.endsWith("Z") ? isoString : isoString + "Z";
  const date = new Date(utcString);

  if (isNaN(date.getTime())) {
    return "Invalid date";
  }

  const currentYear = new Date().getFullYear();
  const dateYear = date.getFullYear();

  switch (type) {
    case "edited":
      return `Edited on ${format(date, dateYear === currentYear && !year ? "d MMMM" : "d MMMM yyyy")}`;
    case "message-timestamp":
      return format(
        date,
        dateYear === currentYear && !year ? "h:mm a d MMMM" : "h:mm a d MMMM yyyy",
      );
    case "lastChat":
      if (isToday(date)) {
        return `Last chat today at ${format(date, "h:mm a")}`;
      } else if (isYesterday(date)) {
        return "Last chat yesterday";
      } else {
        return `Last chat ${formatDistanceToNow(date)} ago`;
      }
    case "deployed":
      return `Deployed on ${format(date, dateYear === currentYear && !year ? "h:mm a d MMMM" : "h:mm a d MMMM yyyy")}`;
    case "commit-history":
      if (isToday(date)) {
        return `Last commit today at ${format(date, "h:mm a")}`;
      } else if (isYesterday(date)) {
        return "Last commit yesterday";
      } else {
        return `Last commit ${formatDistanceToNow(date)} ago`;
      }
    default:
      return format(
        date,
        dateYear === currentYear && !year ? "h:mm a d MMMM" : "h:mm a d MMMM yyyy",
      );
  }
}
