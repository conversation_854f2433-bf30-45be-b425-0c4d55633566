import { listProjects } from "@/lib/api";
import { Project } from "@/providers/project-provider";
import { CACHE_PARAMS } from "@/providers/query-provider";
import { AuthState } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

export type NavItem = {
  title: string;
  url?: string;
  icon: ReactNode;
  items?: NavItem[];
  projectLimit?: number;
  mobileUrl?: string;
  modal?: boolean;
};

export function useSidebarNavigation(user: AuthState) {
  const pathname = usePathname();
  const currentProjectId = pathname?.split("/")[2];

  const { data: rawProjects = [], isLoading } = useQuery<Project[]>({
    queryKey: ["projects"],
    queryFn: listProjects,
    enabled: !!user?.access_token,
    retry: 2, // Retry failed requests twice
    refetchOnWindowFocus: true,
    ...CACHE_PARAMS
  });

  const projects = [...rawProjects].sort((a, b) => {
    return new Date(b.last_updated_date).getTime() - new Date(a.last_updated_date).getTime();
  });

  const baseNavItems: NavItem[] = [
    {
      title: "Dashboard",
      url: "/app",
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 23 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.834039 7.30058C0.249939 8.36768 0.249939 9.65493 0.249939 12.2294V13.9406C0.249939 18.3291 0.249939 20.5233 1.56796 21.8867C2.88598 23.25 5.0073 23.25 9.24994 23.25H13.7499C17.9926 23.25 20.1139 23.25 21.4319 21.8867C22.7499 20.5233 22.7499 18.3291 22.7499 13.9406V12.2294C22.7499 9.65493 22.7499 8.36768 22.1658 7.30058C21.5817 6.23348 20.5146 5.5712 18.3804 4.24664L16.1304 2.85023C13.8744 1.45008 12.7464 0.75 11.4999 0.75C10.2535 0.75 9.1255 1.45008 6.86947 2.85023L4.61948 4.24664C2.48525 5.5712 1.41814 6.23348 0.834039 7.30058ZM8.62738 15.8222C8.25302 15.5447 7.72459 15.6232 7.4471 15.9976C7.16961 16.3719 7.24814 16.9004 7.6225 17.1778C8.71646 17.9887 10.0543 18.4687 11.4999 18.4687C12.9455 18.4687 14.2834 17.9887 15.3774 17.1778C15.7517 16.9004 15.8303 16.3719 15.5528 15.9976C15.2753 15.6232 14.7469 15.5447 14.3725 15.8222C13.5531 16.4295 12.5641 16.7812 11.4999 16.7812C10.4358 16.7812 9.44676 16.4295 8.62738 15.8222Z"
            fill="currentColor"
          />
        </svg>
      ),
    },
    {
      title: "Your Projects",
      projectLimit: user?.userFromDb?.project_limit,
      url: "/app",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M4.55592 5.51158C5.09004 4.57686 6.08407 4 7.16065 4H16.8394C17.9159 4 18.91 4.57686 19.4441 5.51158L22.6047 11.0427C22.8638 11.496 23 12.009 23 12.5311V17C23 18.6569 21.6569 20 20 20H4C2.34315 20 1 18.6569 1 17V12.5311C1 12.009 1.13625 11.496 1.39527 11.0427L4.55592 5.51158ZM21 13H16.5C16.1852 13 15.8889 13.1482 15.7 13.4L15.4 13.8C14.8334 14.5554 13.9443 15 13 15H11C10.0557 15 9.16656 14.5554 8.6 13.8L8.3 13.4C8.11115 13.1482 7.81476 13 7.5 13H3V17C3 17.5523 3.44772 18 4 18H20C20.5523 18 21 17.5523 21 17V13Z"
            fill="currentColor"
          />
        </svg>
      ),
      items: projects.map((project) => ({
        title: project.name,
        url: `/project/${project.project_id}`,
        icon: (
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.72796 3H7.5C6.25736 3 5.25 4.00736 5.25 5.25V18.75C5.25 19.9926 6.25736 21 7.5 21H16.5C17.7426 21 18.75 19.9926 18.75 18.75V12M9.72796 3C10.9706 3 12 4.00736 12 5.25V7.5C12 8.74264 13.0074 9.75 14.25 9.75H16.5C17.7426 9.75 18.75 10.7574 18.75 12M9.72796 3C13.4179 3 18.75 8.3597 18.75 12"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
      })),
    },
    {
      title: "Settings",
      modal: true,
      icon: (
        <span className="group-hover:animate-spin-once">
          <svg
            width="20"
            height="20"
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M10.565 2.075c-.394.189-.755.497-1.26.928l-.079.066a2.56 2.56 0 0 1-1.58.655l-.102.008c-.662.053-1.135.09-1.547.236a3.33 3.33 0 0 0-2.03 2.029c-.145.412-.182.885-.235 1.547l-.008.102a2.56 2.56 0 0 1-.655 1.58l-.066.078c-.431.506-.74.867-.928 1.261a3.33 3.33 0 0 0 0 2.87c.189.394.497.755.928 1.26l.066.079c.41.48.604.939.655 1.58l.008.102c.053.662.09 1.135.236 1.547a3.33 3.33 0 0 0 2.029 2.03c.412.145.885.182 1.547.235l.102.008c.629.05 1.09.238 1.58.655l.079.066c.505.431.866.74 1.26.928a3.33 3.33 0 0 0 2.87 0c.394-.189.755-.497 1.26-.928l.079-.066c.48-.41.939-.604 1.58-.655l.102-.008c.662-.053 1.135-.09 1.547-.236a3.33 3.33 0 0 0 2.03-2.029c.145-.412.182-.885.235-1.547l.008-.102c.05-.629.238-1.09.655-1.58l.066-.079c.431-.505.74-.866.928-1.26a3.33 3.33 0 0 0 0-2.87c-.189-.394-.497-.755-.928-1.26l-.066-.079a2.56 2.56 0 0 1-.655-1.58l-.008-.102c-.053-.662-.09-1.135-.236-1.547a3.33 3.33 0 0 0-2.029-2.03c-.412-.145-.885-.182-1.547-.235l-.102-.008a2.56 2.56 0 0 1-1.58-.655l-.079-.066c-.505-.431-.866-.74-1.26-.928a3.33 3.33 0 0 0-2.87 0M8.75 12a3.25 3.25 0 1 1 6.5 0 3.25 3.25 0 0 1-6.5 0" />
          </svg>
        </span>
      ),
    },
  ];

  const navItems = baseNavItems;

  const integrations: NavItem[] = [
    {
      title: "Firebase",
      url: `/integration/firebase?id=${currentProjectId}`,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 175 205">
          <path
            d="M51.2275 191.109C59.4031 194.399 68.2727 196.352 77.5857 196.677C90.1896 197.117 102.175 194.511 112.89 189.561C100.041 184.513 88.4043 177.131 78.4729 167.952C72.0352 178.26 62.5135 186.401 51.2275 191.109Z"
            fill="#FF9100"
          ></path>
          <path
            d="M78.4711 167.955C55.8006 146.988 42.0477 116.641 43.21 83.3558C43.2478 82.2749 43.3048 81.1947 43.3715 80.1148C39.3113 79.0647 35.0725 78.4239 30.7103 78.2716C24.4663 78.0535 18.4195 78.8279 12.7108 80.4355C6.659 91.0365 3.00696 103.2 2.552 116.228C1.37785 149.852 21.7254 179.231 51.2257 191.112C62.5117 186.404 72.0331 178.272 78.4711 167.955Z"
            fill="#FFC400"
          ></path>
          <path
            d="M78.472 167.954C83.7428 159.519 86.9386 149.63 87.3106 138.976C88.2893 110.95 69.4486 86.8412 43.3725 80.1137C43.3058 81.1936 43.2487 82.2738 43.211 83.3547C42.0486 116.64 55.8015 146.987 78.472 167.954Z"
            fill="#FF9100"
          ></path>
          <path
            d="M84.3945 1.33524C69.5432 13.233 57.8158 28.9212 50.7205 46.9936C46.6583 57.3453 44.1056 68.4647 43.3603 80.1206C69.4365 86.8481 88.2772 110.957 87.2985 138.983C86.9264 149.637 83.7213 159.516 78.4599 167.961C88.391 177.149 100.028 184.522 112.877 189.57C138.667 177.649 156.965 151.996 158.025 121.654C158.711 101.995 151.158 84.4744 140.485 69.6852C129.214 54.0442 84.3945 1.33524 84.3945 1.33524Z"
            fill="#DD2C00"
          ></path>
        </svg>
      ),
    },
  ];

  const isNavItemActive = (url: string) => pathname === url;

  const shouldShowIntegrations = pathname?.startsWith("/app/");

  return {
    navItems,
    integrations,
    isNavItemActive,
    shouldShowIntegrations,
    isLoading,
    projects,
  };
}
