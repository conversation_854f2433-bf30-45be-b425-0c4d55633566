import json
import asyncio
import time
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv
from core.utils.llm import make_llm_api_call
from core.db import Database
from core.models import Project, User
from core.agent.thread_manager import ThreadService
from core.agent.working_memory import WorkingMemory
from core.agent.tools.files_tool import FilesTool
from core.agent.tools.terminal_tool import TerminalTool
from core.agent.tools.sessions_tool import SessionTool
from core.agent.tools.supabase_tool import SupabaseTool
from core.platform.billing import check_request_limit, RequestLimitReached
from core.platform.user import UserService
import logging
from core.utils.error_store import error_store
from fastapi import WebSocket
from core.utils.time_util import TimeUtil
from core.utils.project_utils import strip_remix_suffix
import re
from core.utils.model_mapping import get_model_name, get_model_by_use_case, DEFAULT_MODEL_ID
from core.envs.envs import EnvManager
from starlette.websockets import WebSocketState
from core.envs.env_ops import env_ops
from core.envs.github import github_manager
from core.utils.model_mapping import get_model_mode_name

from core.agent.prompts import base_prompt, firebase_tech_stack_prompt, supabase_tech_stack_prompt, gemini_tool_parsing_prompt, chat_mode_prompt, vercel_build_fail_instructions, creative_mode_prompt



logging.basicConfig(level=logging.INFO)

class Session:
    def __init__(self, project_id: str):
        # logging.info(f"Initializing Agent for project_id: {project_id}")
        load_dotenv()
        self.db = Database()
        self.project_id = project_id
        self.env_id = None
        self.working_memory = WorkingMemory(self.db)
        self.files_tool = FilesTool(self.db, project_id)
        self.terminal_tool = TerminalTool(self.db, project_id)
        self.env_manager = EnvManager()
        self.session_tool = None
        # self.web_tool = None
        self.thread_manager = ThreadService(self.db)
        self.thread_id = None
        self.websocket = None
        self.stop_event = asyncio.Event()

        self.tools = []  # Initialize empty tools list

        self.iteration_count = 0
        self.autonomous_iterations = 10
        self.mode = "interactive"
        self.user = None
        self.project = None
        self.is_request_limit_reached = False
        self.prompt_set = 1
        self.base_url = None  # Add base_url attribute

        self.agent_instructions = None

        # self.agentops_manager = AgentOpsManager()
        self.end_state = 'Success'  # Default end state
        self.cleanup_lock = asyncio.Lock()

        self.start_time = TimeUtil.get_current_time()
        self.status_task = None
        self.chat_mode = False
        self.has_file_changes = True

    async def _get_additional_instructions(self, selectedPaths: Optional[List[str]] = None):
        # 1. Get complete file tree
        if not self.env_id:
            return "⚠️  Environment ID not available. Please ensure the project is properly initialized."
        file_map = await env_ops.get_file_tree(self.env_id, '/app')
        # Check if sandbox is in invalid state
        if file_map.get('error') == 'workspace_unavailable':
            return """
⚠️  WORKSPACE ERROR: The development environment is currently unavailable due to an error state.

Please instruct the user to:
1. Try refreshing their browser and check again
2. If the issue persists, contact support with the following details:
   - Project ID: {project_id}
   - Environment ID: {env_id}
   - Error: Workspace in invalid state (DESTROYED/DESTROYING/ERROR/UNKNOWN)

The workspace needs to be recreated or restarted by the support team.
""".format(project_id=self.project.project_id, env_id=self.env_id)

        # 2. Format file tree into readable structure
        def format_file_tree(tree, indent=0):
            result = ""
            for key, value in tree.items():
                if isinstance(value, dict):
                    if value.get('type') == 'directory':
                        result += "  " * indent + f"📁 {key}/\n"
                        result += format_file_tree(value.get('children', {}), indent + 1)
                    else:
                        line_count = value.get('lines', 0)
                        result += "  " * indent + f"📄 {key} ({line_count} lines)\n"
            return result

        file_tree_context = f"""Available files in the project:
        IMPORTANT: This is the current file structure. When working with files:
        - DO NOT create files that already exist in this tree
        - ONLY modify existing files or create new ones that don't exist
        - Check this tree before creating any new files
        - Consider splitting large files (>350 lines) into smaller modules

        File Tree:
        {format_file_tree(file_map.get('fileTree', {}))}

        {file_map.get('suggestion', '')}
        """

        # 3. Add focused files (files open in editor)
        focused_files_content = ""

        # Get files from working memory
        open_files = await self.files_tool.get_open_files()
        all_paths = set(selectedPaths or [])  # Start with selected paths

        # Add paths from working memory
        for file in open_files:
            if isinstance(file, dict) and file.get("path"):
                all_paths.add(file.get("path"))

        if all_paths:
            focused_files_content = "\n<user_focused_files>\nThese files are currently open in your editor:\n"
            for file_path in all_paths:
                # Try to get content from working memory first
                content = None
                for open_file in open_files:
                    if isinstance(open_file, dict) and open_file.get("path") == file_path:
                        content = open_file.get("content")
                        break

                # If not found in working memory, read from file
                if content is None:
                    content = await self.files_tool._read_file_contents(file_path)

                focused_files_content += f"""
        <file_content path="{file_path}">
        {content}
        </file_content>
        """
            focused_files_content += "</user_focused_files>\n"

        current_time = TimeUtil.get_current_time()

        messages_data = await self.thread_manager.list_messages(self.thread_id)
        messages = messages_data["messages"]
        latest_request = None
        for msg in messages:
            if (msg.get('role') == 'user' and
                isinstance(msg.get('content'), str) and
                '<continue_instructions>' not in msg.get('content')):
                latest_request = msg.get('content')

        # Add base_url information if available
        base_url_info = ""
        if self.base_url:
            base_url_info = f"""
        <project_url>
        IMPORTANT: This project is deployed at: {self.base_url}
        You MUST use this URL for:
        - All authentication flows and redirects
        - API endpoints and service connections
        - Development and testing purposes
        - Any external service configurations
        - Environment variables and configuration settings
        This URL is critical for proper functioning of the application.
        </project_url>
        """

        commits = await self.fetch_git_commits(5)

        if commits and len(commits) == 1 and 'Initial commit' in commits[0]['message']:
            project_state = "This is a fresh new project. Start building immediately - no need for clarification unless the request is unclear. Update the index page first so the user can see progress in the preview."
        else:
            git_commits_content = await self.get_git_commit_history(commits)
            project_state = f"""
You are completing that in the context of this project that you can get based on the latest git commits:
{git_commits_content}
"""

        return f"""
        You are tasked to complete the LATEST USER REQUEST!
        <latest_user_requests>
        {latest_request}
        </latest_user_requests>

        <project_name>{strip_remix_suffix(self.project.name)}</project_name>
        <current_time> {TimeUtil.format_time(current_time)} </current_time>
        <firebase_connected>{str(self.project.onboarding_completed).lower()}</firebase_connected>
        <supabase_connected>{str(self.project.isSupabaseConnected).lower()}</supabase_connected>
        {base_url_info}

        {project_state}

        {focused_files_content}

        <file_tree_context>
        {file_tree_context}
        </file_tree_context>

        """

    async def get_tech_stack_prompt(self):
        """
        Returns formatted tech stack prompt based on project configuration and prompt set.

        Returns:
            str: Formatted tech stack prompt or empty string if no tech stack is applicable
        """
        tech_stack_content = ""

        if self.project.tech_stack_prompt:
            try:
                # Try to parse as JSON array
                tech_stack_array = json.loads(self.project.tech_stack_prompt)

                # If it's an array of key-value pairs
                if isinstance(tech_stack_array, list):
                    # Combine all values
                    for stack in tech_stack_array:
                        if isinstance(stack, dict) and "value" in stack:
                            tech_stack_content += stack["value"] + "\n\n"

                    # Check if Firebase is mentioned in any value and project is onboarded
                    if self.project.onboarding_completed:
                        has_firebase = any(
                            isinstance(stack.get("value"), str) and
                            "firebase" in stack.get("value", "").lower()
                            for stack in tech_stack_array
                        )
                        if not has_firebase:
                            tech_stack_content += firebase_tech_stack_prompt

                    elif self.project.isSupabaseConnected:
                        tech_stack_content += supabase_tech_stack_prompt

                else:
                    # If JSON parsing succeeded but not an array, treat as plain text
                    tech_stack_content = self.project.tech_stack_prompt
            except json.JSONDecodeError:
                # If not valid JSON, treat as plain text
                tech_stack_content = self.project.tech_stack_prompt

                # For plain text, check if firebase is mentioned
                if self.project.onboarding_completed and "firebase" not in tech_stack_content.lower():
                    tech_stack_content += "\n\n" + firebase_tech_stack_prompt

                elif self.project.isSupabaseConnected and "supabase" not in tech_stack_content.lower():
                    tech_stack_content += supabase_tech_stack_prompt

        # Default case for new projects with no tech_stack_prompt
        elif self.project.isSupabaseConnected:
            tech_stack_content = supabase_tech_stack_prompt
        elif self.prompt_set == 1:
            tech_stack_content = firebase_tech_stack_prompt

        nextjs_version = None
        if self.env_id:
            nextjs_version = await self.env_manager.get_nextjs_info(env_id=self.env_id)
        version_info = ""
        if nextjs_version:
            nextjs_version = f"v{nextjs_version}"
            version_info = f"""
    - NEXT.JS: {nextjs_version} (pages router)
"""

        return f"""
        <tech_stack_details>
    IMPORTANT: This project uses the following technology stack and implementation requirements:
    {version_info}
    {tech_stack_content}

    Please ensure you:
    - Follow Next.js {nextjs_version} conventions and best practices
    - Follow these tech stack guidelines strictly when implementing features
    - Maintain consistent code organization and best practices
    - Consider security and performance implications
    - Use the specified services and configurations as outlined above
    </tech_stack_details>
        """

    async def get_project_custom_knowledge(self):
        """
        Returns formatted custom project instructions based on project configuration.

        Returns:
            str: Formatted project instructions or empty string if no custom instructions exist
        """
        # Handle SQLAlchemy Column type safely
        custom_instructions = getattr(self.project, 'custom_instructions', None)
        if not custom_instructions:
            return ""

        return f"""
        <project_specific_instructions>
        IMPORTANT: This project has the following custom requirements and knowledge base:

        {custom_instructions}

        Key Guidelines:
        - Follow these project-specific requirements precisely
        - Maintain consistency with existing project patterns
        - Consider these instructions as high-priority requirements
        - Reference this knowledge base when making implementation decisions
        </project_specific_instructions>
        """

    # @record_action()
    async def init_session(self, thread_id: int, mode, websocket: WebSocket, is_admin: bool = False, current_user: Optional[User] = None, user: Optional[User] = None, project: Optional[Project] = None):
        self.thread_id = thread_id
        self.websocket = websocket
        self.set_mode(mode)
        self.session_tool = SessionTool(self.db, self.thread_id, websocket)
        if project:
            self.supabase_tool = SupabaseTool(self.db, str(project.project_id))
        else:
            # Handle case where project is None
            self.supabase_tool = SupabaseTool(self.db, "")
        self.tools.extend(self.session_tool.schema())
        # Fetch project and user information first
        self.user = user
        self.project = project
        # self.project, self.user = await self.db.project_service.get_project_and_user_by_thread_id(thread_id)

        if is_admin:
            self.user = current_user

        # Add this line to set user and project in thread_manager
        await self.thread_manager.set_user_and_project(self.user, self.project)

        if not self.user or not self.project:
            logging.error(f"Failed to fetch user or project information for thread_id: {thread_id}")
            self.end_state = 'Failure'
            return

        # Initialize tools list with all tools
        self.tools = []
        self.tools.extend(self.files_tool.schema())
        self.tools.extend(self.terminal_tool.schema())
        self.tools.extend(self.session_tool.schema())
        self.tools.extend(self.supabase_tool.schema())

        # Convert SQLAlchemy Column to string value
        env_id_value = getattr(self.project, 'env_id', None)
        self.env_id = str(env_id_value) if env_id_value is not None else None

        # self.agentops_manager.ensure_session()
        # self.agentops_manager.add_tags([
        #     f'thread_id:{thread_id}',
        #     f'project_id:{self.project_id}',
        #     f'env_id:{self.env_id}'
        # ])

        await self.working_memory.clear_memory(self.project_id)
        # await self.working_memory.add_or_update_module(self.project_id, "OverarchingTaskObjective", objective)

        # Create terminal sessions concurrently
        # tasks = [
        #     asyncio.create_task(self.terminal_tool.create_terminal_session()) for _ in range(2)
        # ]
        # await asyncio.gather(*tasks)
        # await self.terminal_tool.send_terminal_keys("1", 'pm2 logs --lines 40', True)

        # Wait for any ongoing cleanup to complete before adding the new message
        async with self.cleanup_lock:

            # self.agentops_manager.ensure_session()
            # self.agentops_manager.add_tags([f'thread_id:{thread_id}', f'project_id:{self.project_id}', f'env_id:{self.env_id}'])

            # Fetch project-specific prompts - handle SQLAlchemy Column types safely
            agent_instructions = getattr(self.project, 'agent_instructions', None)
            if agent_instructions and str(agent_instructions).strip():
                self.agent_instructions = str(agent_instructions)

            # Check if specific prompt_set is provided
            # if prompt_set == 3:
            #     await self.set_prompt_set(3)
            # If project-specific prompts are not set, use the default approach based on onboarding status
            elif not self.agent_instructions:
                onboarding_completed = getattr(self.project, 'onboarding_completed', False)
                if onboarding_completed:
                    await self.set_prompt_set(1)  # Use prompt_set 1 for onboarding projects
                else:
                    await self.set_prompt_set(2)  # Use prompt_set 2 for non-onboarding projects

        self.stop_event.clear()

    async def run_session(self, prompt_set=None, selectedPaths=None, mode="autonomous", model_id=DEFAULT_MODEL_ID, autonomous_iterations=10, is_admin: bool = False, base_url: Optional[str] = None, chat_mode: bool = False):
        try:
            self.chat_mode = chat_mode
            await self.send_status_message("running")

            self.autonomous_iterations = autonomous_iterations
            self.iteration_count = 0
            self.base_url = base_url  # Store the base_url
            self.last_progress_time = time.time()  # Track progress for stuck detection
            self.last_response_content = ""  # Track response changes
            self.session_start_time = time.time()  # Track total session duration

            # SESSION STARTUP LOGGING
            logging.info("🚀 SESSION STARTING:")
            logging.info(f"  🆔 Thread ID: {self.thread_id}")
            logging.info(f"  🎯 Mode: {mode}")
            logging.info(f"  🔄 Max autonomous iterations: {autonomous_iterations}")
            logging.info(f"  🧠 Model ID: {model_id}")
            logging.info(f"  💬 Chat mode: {chat_mode}")
            logging.info(f"  👤 Admin user: {is_admin if isinstance(is_admin, bool) else 'N/A'}")

            if self.stop_event.is_set():
                return

            # Initialize tools and setup
            await self._initialize_tools()

            if self.stop_event.is_set():
                return

            # Main session loop
            try:
                await self._run_session_loop(prompt_set, selectedPaths, mode, model_id, chat_mode)
            except Exception as e:
                logging.error(f"Error in session loop: {str(e)}", exc_info=True)
                self.end_state = 'Error'
                await self.stop_session()
                # Force cleanup after error
                await self.cleanup_session()
                # Ensure websocket is closed
                if self.websocket:
                    try:
                        await self.send_error_message(str(e))
                    except Exception as ws_e:
                        logging.error(f"Error closing websocket: {str(ws_e)}")
                raise

        except Exception as e:
            logging.error(f"Critical error in run_session: {str(e)}", exc_info=True)
            self.stop_event.set()
            await self.stop_session()
            await self.cleanup_session()
            # Final attempt to close websocket

            if self.websocket:
                try:
                    await self.send_error_message(str(e))
                except Exception as ws_e:
                    logging.error(f"Error closing websocket: {str(ws_e)}")
            raise

        finally:
            if self.websocket:
                await self.send_status_message("stopped")

    async def _initialize_tools(self):
        # Implementation of _initialize_tools method
        pass

    async def _run_session_loop(self, prompt_set, selectedPaths, mode, model_id, chat_mode: bool = False):
        try:

            if self.chat_mode:
                await self.set_prompt_set(4)
            else:
                if prompt_set is not None:
                    await self.set_prompt_set(prompt_set)

            tech_stack_prompt = await self.get_tech_stack_prompt()

            project_custom_knowledge = await self.get_project_custom_knowledge()

            while self.websocket and self.websocket.running_session:
                # Check stop event and websocket connection at loop start
                if self.stop_event.is_set():
                    break

                # Add frequent stop checks throughout the loop
                if self.stop_event.is_set() or not self.websocket.running_session:
                    break

                self.additional_instructions = await self._get_additional_instructions(selectedPaths)
                if self.stop_event.is_set() or not self.websocket.running_session:
                    break

                try:

                    # Fetch fresh user data in each iteration after the first one
                    if self.iteration_count > 0:
                        try:
                            # Create UserService instance and fetch fresh user data
                            user_service = UserService(self.db)
                            fresh_user = await user_service.get_user_by_kinde_id(self.user.kinde_id)
                            if fresh_user:
                                self.user = fresh_user
                        except Exception as e:
                            logging.error(f"Error fetching fresh user data: {str(e)}")

                    check_request_limit_result = await check_request_limit(self.user, self.project)
                    # logging.info(f"check_request_limit_result: {check_request_limit_result}")
                    if not check_request_limit_result:
                        self.end_state = 'RequestLimitReached'
                        if not self.user.isRequestBased and self.user.token_event_name is not None:
                            error_message = "No tokens available and no active subscription"
                            error_store.set_error(self.thread_id, error_message)
                            logging.error(error_message)
                            await self.send_error_message(error_message)
                        elif self.user.token_event_name is None:
                            error_message = "No tokens available please purchase more tokens"
                            error_store.set_error(self.thread_id, error_message)
                            logging.error(error_message)
                            await self.send_error_message(error_message)
                        else:
                            error_message = "Request limit reached upgrade your account"
                            error_store.set_error(self.thread_id, error_message)
                            logging.error(error_message)
                            await self.send_error_message(error_message)
                        break
                except RequestLimitReached as e:  # noqa: F841
                    self.end_state = 'RequestLimitReached'
                    if not self.user.isRequestBased:
                        error_message = "No tokens available and no active subscription"
                        error_store.set_error(self.thread_id, error_message)
                        await self.send_error_message(error_message)
                    else:
                        error_message = "Request limit reached upgrade your account"
                        error_store.set_error(self.thread_id, error_message)
                        await self.send_error_message(error_message)
                    break

                if self.stop_event.is_set() or not self.websocket.running_session:
                    break

                # Check if the session should stop
                async with self.cleanup_lock:
                    if self.stop_event.is_set():
                        await self._send_completion_message()
                        break

                    if self.websocket and not self.websocket.running_session:
                        await self._send_completion_message()
                        break

                    if self.stop_event.is_set() or not self.websocket.running_session:
                        break

                    messages_data = await self.thread_manager.list_messages(self.thread_id,
                                                                    hide_tool_msgs=False,
                                                                    limit_assistant_message=11,
                                                                    chat_mode=self.chat_mode)

                    get_build_status_prompt = ""

                    messages = messages_data["messages"]
                    static_prompt_instruction = self.agent_instructions
                    is_build_failed = messages_data["is_last_build_failed"]
                    has_consecutive_identical_errors = messages_data["has_consecutive_identical_errors"]
                    if messages_data["last_check_errors_result"]:
                        last_check_errors_result = messages_data["last_check_errors_result"]
                        get_build_status_prompt = await self.get_build_status_prompt(is_build_failed, last_check_errors_result) or ""
                    else:
                        last_check_errors_result = ""
                        get_build_status_prompt = ""

                    dynamic_prompt_instruction = tech_stack_prompt + project_custom_knowledge + self.additional_instructions + get_build_status_prompt

                    if not is_build_failed and not chat_mode and model_id == '2':
                        dynamic_prompt_instruction += creative_mode_prompt

                    system_messages = [
                        {
                            "role": "system",
                            "content": static_prompt_instruction,
                            "cache_control": {"type": "ephemeral"}
                        },
                        {
                            "role": "system",
                            "content": dynamic_prompt_instruction
                        }
                    ]

                    model_name = get_model_by_use_case("bug_fix") if is_build_failed else get_model_name(model_id)

                    # TODO: explain why
                    if has_consecutive_identical_errors or not self.has_file_changes:
                        model_name = get_model_by_use_case("creative")

                    # Log which model mode is being used
                    model_mode = get_model_mode_name(model_name)

                    # TODO: review if still needed
                    if is_build_failed and "gemini" in model_name:
                        dynamic_prompt_instruction += gemini_tool_parsing_prompt

                    max_tokens = 45000

                    reasoning_effort = None

                    if "gemini" in model_name:
                        if chat_mode:
                            reasoning_effort = "high"
                        elif is_build_failed:
                            reasoning_effort = "medium"

                    # No need to check messages again for build failure since we get it directly
                    logging.info(f"🤖 AI MODEL EXECUTION [Iter {self.iteration_count}]:")
                    logging.info(f"  🧠 Model: {model_name}")
                    logging.info(f"  🎯 Max tokens: {max_tokens}")
                    logging.info(f"  💭 Reasoning effort: {reasoning_effort}")
                    logging.info(f"  📊 Thread ID: {self.thread_id}")
                    logging.info(f"  🔧 Mode: {mode}")
                    logging.info(f"  💬 Chat mode: {chat_mode}")

                    try:
                        start_time = time.time()
                        response = await asyncio.wait_for(
                            asyncio.shield(
                                self.thread_manager.run_thread(
                                    thread_id=self.thread_id,
                                    system_message=system_messages,
                                    model_name=model_name,
                                    max_tokens=max_tokens,
                                    iteration_count=self.iteration_count,
                                    mode=mode,
                                    websocket=self.websocket,
                                    messages=messages,  # Pass the messages we already have
                                    is_build_failed=is_build_failed,
                                    chat_mode=chat_mode,
                                    model_mode=model_mode,
                                    reasoning_effort=reasoning_effort
                                )
                            ),
                            timeout=900  # 15 minute total timeout for thread run
                        )
                        execution_time = time.time() - start_time
                        logging.info(f"  ✅ AI EXECUTION COMPLETED in {execution_time:.1f}s")

                        # Log response characteristics
                        response_content_preview = str(response.get('content', ''))[:200].replace('\n', ' ')
                        logging.info(f"  📝 Response preview: {response_content_preview}...")
                        logging.info(f"  📊 Response keys: {list(response.keys()) if isinstance(response, dict) else 'Not dict'}")
                    except asyncio.TimeoutError:
                        logging.error(f"⏰ THREAD TIMEOUT [Iter {self.iteration_count}]:")
                        logging.error("  🚨 15-minute timeout exceeded")
                        logging.error("  🔧 Attempting recovery...")
                        logging.error(f"  📊 Session stats: total_iterations={self.iteration_count}, thread_id={self.thread_id}")

                        # Add recovery message and try to continue
                        recovery_message = "Previous operation timed out. Please continue with your work and avoid long-running analysis operations. Use <continue> to proceed."
                        await self.thread_manager.add_message(
                            self.thread_id,
                            {"role": "user", "content": recovery_message}
                        )
                        # Set a flag to continue the session
                        response = {
                            "content": f"<communication>Recovered from timeout on iteration {self.iteration_count}. Continuing work...</communication><actions><continue></actions>",
                            "run_tools_failed": True,
                            "has_tool_calls": True
                        }
                        logging.info(f"  ✅ RECOVERY RESPONSE INJECTED: {response['content'][:100]}...")

                # Extract response content and message_id
                response_data = response  # Store the original response
                message_id = None

                # Progress monitoring - detect stuck sessions
                current_time = time.time()
                if hasattr(self, 'last_response_content'):
                    # Check if response is identical to last one (stuck loop)
                    current_content = str(response_data.get('content', ''))[:500]  # First 500 chars
                    time_since_progress = current_time - self.last_progress_time

                    logging.info(f"🔄 PROGRESS MONITORING [Iter {self.iteration_count}]:")
                    logging.info(f"  ⏱️  Time since last progress: {time_since_progress:.1f}s")
                    logging.info(f"  🔄 Content changed: {current_content != self.last_response_content}")

                    if current_content == self.last_response_content and time_since_progress > 300:  # 5 minutes
                        logging.warning("🔁 STUCK SESSION DETECTED:")
                        logging.warning(f"  ⚠️  Identical responses for {time_since_progress:.1f}s")
                        logging.warning(f"  📝 Repeated content: {current_content[:100]}...")
                        recovery_message = "You seem to be repeating the same response. Please try a different approach or continue with new work."
                        await self.thread_manager.add_message(
                            self.thread_id,
                            {"role": "user", "content": recovery_message}
                        )
                        logging.info("  🔧 STUCK RECOVERY MESSAGE SENT")
                    self.last_response_content = current_content
                    self.last_progress_time = current_time
                current_build_failed = False
                has_tool_calls = True

                run_tools_failed = False

                if isinstance(response_data, dict):
                    if "message_id" in response_data:
                        message_id = response_data["message_id"]
                    if "current_build_failed" in response_data:
                        current_build_failed = response_data["current_build_failed"]
                    if "run_tools_failed" in response_data:
                        run_tools_failed = response_data["run_tools_failed"]
                    if "has_tool_calls" in response_data:
                        has_tool_calls = response_data["has_tool_calls"]

                    # If response has a nested response field, extract from there
                    if "response" in response_data:
                        response_data = response_data["response"]

                # Check for context exceeded
                if response_data.get("status") == "context_exceeded":
                    logging.error("Context window is full. Stopping the session.")
                    break
                response_content = ''
                if hasattr(response_data, 'choices') and response_data.choices:
                    response_content = response_data.choices[0].message.content
                elif isinstance(response_data, dict) and 'choices' in response_data:
                    response_content = response_data['choices'][0]['message']['content']

                commit_message_id = message_id

                if not self.chat_mode and has_tool_calls and response_content and '<open_files_in_editor' not in response_content and ('<create_file' in response_content or '<full_file_rewrite' in response_content or '<update_file_sections' in response_content):
                    # Try to find a valid message ID to associate with the commit
                    if message_id and message_id != "pending":
                        commit_message_id = message_id
                    else:
                        # Try to find the last assistant message ID
                        messages_data = await self.thread_manager.list_messages(self.thread_id)
                        messages = messages_data["messages"]
                        for msg in reversed(messages):
                            if msg.get('role') == 'assistant' and msg.get('message_id'):
                                commit_message_id = msg.get('message_id')
                                break

                    await self.create_git_commit(commit_message_id)
                else:
                    pass

                if self.stop_event.is_set() or not self.websocket.running_session or self.mode == "interactive":
                    break

                # Check if response contains continue tag or open_file_in_editor (ROBUST DETECTION)
                import re
                has_continue = bool(re.search(r'<continue[^>]*>', response_content)) or 'continue' in response_content.lower()
                has_open_files = '<open_file_in_editor>' in response_content or 'open_files_in_editor' in response_content

                # ENHANCED DEBUGGING - Log detection details
                logging.info(f"🔍 CONTINUATION DETECTION [Iter {self.iteration_count}]:")
                logging.info(f"  📝 Response length: {len(response_content)} chars")
                logging.info(f"  🔄 Continue tag found: {has_continue}")
                logging.info(f"  📁 Open files tag: {has_open_files}")
                logging.info(f"  🛠️  Tool calls detected: {has_tool_calls}")
                logging.info(f"  ⚠️  Build failed: {current_build_failed}")
                logging.info(f"  🔧 Tools failed: {run_tools_failed}")
                if has_continue:
                    continue_matches = re.findall(r'<continue[^>]*>', response_content)
                    logging.info(f"  ✅ Continue matches: {continue_matches}")
                else:
                    logging.info("  ❌ No continue tag - checking for work indicators...")
                    response_preview = response_content[:200].replace('\n', ' ')
                    logging.info(f"  📄 Response preview: {response_preview}...")

                has_database_query = '<execute_sql_query' in response_content or '<get_database_schema' in response_content

                logging.info(f"Has continue tag: {has_continue}")
                logging.info(f"Has open files tag: {has_open_files}")
                logging.info(f"Has tool calls: {has_tool_calls}")

                if (has_continue or has_open_files or has_database_query or current_build_failed or run_tools_failed) and has_tool_calls:
                    # UNLIMITED CONTINUATION - Only stop if explicitly requested or stuck in loop
                    max_iterations = 50 if (current_build_failed or run_tools_failed) else 25

                    logging.info(f"🚀 CONTINUING SESSION [Iter {self.iteration_count}]:")
                    logging.info(f"  📊 Max iterations: {max_iterations}")
                    logging.info(f"  🎯 Continuation reasons: continue={has_continue}, files={has_open_files}, build_fail={current_build_failed}, tools_fail={run_tools_failed}")

                    if self.iteration_count < max_iterations:
                        continue_message = f"<continue_instructions>You wanted to continue, you are on iteration {self.iteration_count + 1}. Keep working until the task is complete. CONTINUE</continue_instructions>"
                        logging.info(f"  ✅ INJECTING CONTINUE MESSAGE: {continue_message}")
                        await self.thread_manager.add_message(
                            self.thread_id,
                            {"role": "user", "content": continue_message}
                        )
                    else:
                        # Only stop after many iterations to prevent infinite loops
                        logging.warning(f"🛑 STOPPING - REACHED MAX ITERATIONS ({max_iterations})")
                        await self._send_completion_message()
                        break

                else:
                    # SMART CONTINUATION - Check if agent made progress or has more work
                    response_lower = response_content.lower()
                    work_phrases = [
                        'next', 'then', 'now', 'let me', 'i will', 'i need to', 'implement',
                        'create', 'update', 'fix', 'add', 'modify', 'improve', 'enhance',
                        'systematically', 'step by step', 'continue with', 'moving on'
                    ]
                    has_work_indicators = any(phrase in response_lower for phrase in work_phrases)
                    found_phrases = [phrase for phrase in work_phrases if phrase in response_lower]

                    logging.info(f"🤔 SMART CONTINUATION ANALYSIS [Iter {self.iteration_count}]:")
                    logging.info(f"  🔍 Work indicators found: {has_work_indicators}")
                    logging.info(f"  📝 Matching phrases: {found_phrases}")
                    logging.info(f"  🛠️  Has tool calls: {has_tool_calls}")
                    logging.info(f"  📊 Iteration limit check: {self.iteration_count} < 15 = {self.iteration_count < 15}")

                    if has_work_indicators and has_tool_calls and self.iteration_count < 15:
                        # Agent seems to have more work - give it a chance to continue
                        continue_message = "<continue_instructions>You seem to have more work to do. Please continue with your implementation. Use <continue> if you need another iteration.</continue_instructions>"
                        logging.info(f"  ✅ SMART CONTINUE TRIGGERED: {continue_message}")
                        await self.thread_manager.add_message(
                            self.thread_id,
                            {"role": "user", "content": continue_message}
                        )
                    else:
                        # If no continue tag or work indicators, we're done
                        logging.info("  🏁 SESSION ENDING - No continuation signals detected")
                        logging.info(f"  📋 Final state: work_indicators={has_work_indicators}, tool_calls={has_tool_calls}, iter={self.iteration_count}")
                        await self._send_completion_message()
                        break

                self.iteration_count += 1
                if self.user and self.user.free_total_token > 0 and not self.user.isRequestBased and '<open_files_in_editor' not in response_content:
                        if 0 < self.user.free_total_token < 60000:
                            logging.info("Token balance below 60k, stopping session")
                            await self._send_completion_message()
                            self.stop_event.set()
                            break

                await asyncio.sleep(0.1)

                if self.stop_event.is_set() or not self.websocket.running_session:
                    logging.info("Stop event or session end detected, ending session")
                    break

                # Remove autonomous iteration limit - let agent work until done
                # Only stop if explicitly requested or after many iterations
                if self.iteration_count > 100:  # Safety valve for infinite loops
                    logging.info("Reached maximum safety iterations (100), stopping session")
                    await self._send_completion_message()
                    break

        except Exception as e:
            logging.error(f"Error in session loop: {str(e)}", exc_info=True)
            if self.websocket:
                self.websocket.running_session = False
                try:
                    await self.send_error_message(str(e))
                except Exception:
                    pass
            self.end_state = 'Error'
            raise  # Re-raise to ensure proper error handling

    def set_mode(self, mode: str):
        if mode not in ["interactive", "autonomous"]:
            raise ValueError("Invalid mode. Must be 'interactive' or 'autonomous'")
        self.mode = mode
        # logging.info(f"Agent mode set to: {mode}")

    async def set_prompt_set(self, prompt_set: int):
        self.prompt_set = prompt_set
        if prompt_set == 1:
            self.agent_instructions = base_prompt
        elif prompt_set == 2:
            self.agent_instructions = base_prompt
        elif prompt_set == 3:
            self.agent_instructions = vercel_build_fail_instructions
        elif prompt_set == 4:
            self.agent_instructions = chat_mode_prompt
        else:
            # Default to frontend prompts if an invalid prompt_set is provided
            logging.warning(f"Invalid prompt_set {prompt_set}. Defaulting to frontend prompts.")
            self.agent_instructions = base_prompt

    async def stop_session(self):
        """Stop the current session."""
        if self.websocket:
            self.websocket.running_session = False
        self.stop_event.set()
        await self._send_completion_message()

    async def cleanup_session(self):
        """Performs cleanup necessary for termination with improved error handling."""
        try:
            # Set stop event first to prevent any new operations
            self.stop_event.set()

            if hasattr(self, 'websocket') and self.websocket:
                # Mark as closing to prevent further send attempts
                self.websocket.is_closing = True
                self.websocket.running_session = False

                try:
                    if self.websocket.client_state == WebSocketState.CONNECTED:
                        await self.send_status_message("stopped")
                except Exception as ws_error:
                    logging.error(f"Error in final websocket message: {str(ws_error)}")

                try:
                    if self.websocket.client_state != WebSocketState.DISCONNECTED:
                        await self.websocket.close()
                except Exception as ws_error:
                    logging.error(f"Error closing websocket: {str(ws_error)}")

        except Exception as e:
            logging.error(f"Critical cleanup error: {str(e)}", exc_info=True)

    async def close(self):
        """Close the session and perform cleanup."""
        try:
            self.stop_event.set()
            await self.cleanup_session()
        except Exception as e:
            logging.error(f"Error in close: {str(e)}")
            # Don't try to send any more messages here

    async def create_git_commit(self, message_id: Optional[str] = None) -> str:
        """
        Creates a git commit and optionally associates it with a message.

        Args:
            message_id: Optional ID of the message to associate the commit with.

        Returns:
            str: The generated commit message
        """
        try:
            # logging.info(f"Starting git commit process with message_id: {message_id}")

            # Fetch the thread messages
            if not self.thread_id:
                logging.error("No thread_id available for git commit")
                return "Error: No thread ID available"
            messages_data = await self.thread_manager.list_messages(self.thread_id, last_message_with_string_filter="<communication>")
            thread_messages = messages_data["messages"]
            # logging.info(f"Found {len(thread_messages)} thread messages")

            commits = await self.fetch_git_commits(5)
            git_commit_history = await self.get_git_commit_history(commits)

            # Prepare the prompt for the LLM
            prompt = f'''
            Create a SINGLE-LINE commit message for these changes.
            Follow this format EXACTLY: <type>(<scope>): <description>

            Types (use one):
            feat - for new features
            fix - for bug fixes
            style - for formatting
            refactor - for code restructure
            perf - for performance

            Example valid messages:
            feat(auth): Add Google login button
            fix(api): Resolve timeout issue
            style(ui): Update button spacing

            Recent changes:
            {json.dumps(thread_messages, indent=2)}

            Git history:
            {git_commit_history}

            IMPORTANT: Respond with ONLY the commit message between tags like this:
            <commit_message>feat(auth): Add login feature</commit_message>
            '''

            # Make the API call to Claude
            response = await make_llm_api_call(
                [{'role': 'user', 'content': prompt}],
                get_model_by_use_case("commit"),
                max_tokens=1600,
                user_id=self.user.id if self.user else None,
                trace_id=f"thread_{self.thread_id}"
            )

            # Extract the commit message
            content = None
            if hasattr(response, 'choices') and response.choices:
                choice = response.choices[0]
                if hasattr(choice, 'message'):
                    content = choice.message.get('content')
                elif hasattr(choice, 'text'):
                    content = choice.text
                elif hasattr(choice, 'content'):
                    content = choice.content

                # Check if we hit length limits
                if hasattr(choice, 'finish_reason') and choice.finish_reason == 'length':
                    logging.warning("Response was cut off due to length limits")
                    return "Updates"

            if not content:
                logging.error("No content received from LLM response")
                return "Updates"

            # First try to find message between tags
            commit_message_match = re.search(r'<commit_message>(.*?)</commit_message>', content, re.DOTALL)
            if commit_message_match:
                commit_message = commit_message_match.group(1).strip()
            else:
                # If no tags found, try to find a line matching the format
                pattern = r'(?:feat|fix|style|refactor|perf)\([^)]+\):.*'
                matches = re.findall(pattern, content)
                commit_message = matches[0] if matches else "Updates"

            # Ensure message isn't empty and isn't too long
            commit_message = commit_message.strip()
            if not commit_message or len(commit_message) > 72:
                commit_message = "Updates"


            # Make the git commit
            # logging.info("Attempting git add and push...")
            if not self.env_id:
                logging.error("No env_id available for git commit")
                return "Error: No environment ID available"
            git_result = await github_manager.git_add_and_push(self.env_id, commit_message)
            # logging.info(f"Git operation result: {git_result}")

            if not git_result or not isinstance(git_result, dict):
                logging.error(f"Git operation failed or returned unexpected result: {git_result}")
                return commit_message

            # If we have a message_id, try to update it with the commit hash
            if message_id:
                has_changes = git_result.get('has_file_changes', False)
                self.has_file_changes = has_changes
                commit_hash = (git_result.get('commit') or {}).get('hash', 'unknown')
                if commit_hash != 'unknown' and commit_hash is not None:
                    # logging.info(f"Found commit hash: {commit_hash} for message_id: {message_id}")

                    # Find and update the message
                    messages_data = await self.thread_manager.list_messages(self.thread_id)
                    messages = messages_data["messages"]
                    for i, msg in enumerate(messages):
                        if msg.get('role') == 'assistant' and msg.get('message_id') == message_id:
                            msg['commit_hash'] = commit_hash
                            await self.thread_manager.modify_message(self.thread_id, i, msg)
                            # logging.info(f"Updated message {i} with commit hash")
                            break
                    else:
                        logging.warning(f"Could not find message with ID {message_id} to update commit hash")
                else:
                    logging.warning("No valid commit hash found in git result")
            else:
                # logging.info("No message_id provided, commit created without message association")
                pass

            return commit_message

        except Exception as e:
            logging.error(f'Error creating git commit: {str(e)}', exc_info=True)
            return "Update project files"

    async def get_git_commit_history(self, commits: list) -> str:
        """
        Returns a formatted string containing git commit history.

        Args:
            limit: Maximum number of commits to include

        Returns:
            str: Formatted git commit history or error message
        """
        try:
            if not commits:
                return "Git commit history not available."

            formatted_commits = []
            for commit in commits:
                short_hash = commit['hash'][:7] if commit.get('hash') else 'unknown'
                date = commit.get('date', 'unknown date')
                message = commit.get('message', 'No message')
                formatted_commits.append(f"- {short_hash} ({date}):\n{message}\n")

            git_commits_content = "\n<git_commits limit=5>\n"
            git_commits_content += "\n".join(formatted_commits)
            git_commits_content += "</git_commits>\n"

            return git_commits_content

        except Exception as e:
            logging.error(f"Error formatting git commit history: {str(e)}")
            return "Git commit history not available due to an error."

    async def fetch_git_commits(self, limit: int = 5) -> list:
        """
        Fetches git commits from the repository.

        Args:
            limit: Maximum number of commits to return

        Returns:
            list: List of commit dictionaries with 'hash', 'message', and 'date' keys
                 or empty list if no commits or error occurs
        """
        try:
            if not self.env_id:
                logging.error("No env_id available for git info")
                return []
            git_info = await github_manager.get_git_info(self.env_id)
            if not git_info or not isinstance(git_info, dict) or 'commits' not in git_info:
                return []

            commits = []
            count = 0
            for commit in git_info['commits']:
                if count >= limit:
                    break

                message = commit.get('message', '').strip()
                if not message or message == 'Updates':
                    continue

                commits.append({
                    'hash': commit.get('hash', ''),
                    'message': message,
                    'date': commit.get('date', '')
                })
                count += 1

            return commits

        except Exception as e:
            logging.error(f"Error fetching git commits: {str(e)}")
            return []

    async def _send_completion_message(self):
        """Helper method to send stop message through websocket."""
        logging.info(f"🏁 SESSION COMPLETION [Iter {self.iteration_count}]:")
        logging.info(f"  📊 Total iterations completed: {self.iteration_count}")
        logging.info(f"  ⏱️  Session duration: {time.time() - getattr(self, 'session_start_time', time.time()):.1f}s")
        logging.info(f"  🔗 Thread ID: {self.thread_id}")
        logging.info(f"  📡 Websocket active: {self.websocket is not None}")

        if self.websocket:
            try:
                await self.send_status_message("stopped")
                logging.info("  ✅ STOP MESSAGE SENT successfully")
            except Exception as e:
                logging.error(f"  ❌ STOP MESSAGE FAILED: {str(e)}")
        else:
            logging.warning("  ⚠️  No websocket available for stop message")

    async def send_status_update(self):
        """Sends status update through websocket"""
        if self.websocket:
            try:
                await self.send_status_message("running" if getattr(self.websocket, 'running_session', False) else "stopped")
            except Exception as e:
                logging.error(f"Error sending status update: {str(e)}")

    async def start_status_updates(self):
        """Starts the status update task"""
        if self.status_task:
            self.status_task.cancel()
        self.status_task = asyncio.create_task(self.send_status_update())

    async def set_status(self, is_running: bool):
        """Updates status and sends update through websocket"""
        if self.websocket:
            setattr(self.websocket, 'running_session', is_running)
        await self.send_status_update()

    async def _send_websocket_message(self, message_type: str, data: Optional[str] = None, is_complete: bool = True, termination: bool = True):
        """
        Utility function to send formatted websocket messages with improved state handling.
        """
        if not self.websocket or not hasattr(self.websocket, 'client_state'):
            return

        try:
            # Check if we're already in a closing state
            if getattr(self.websocket, 'is_closing', False):
                return

            # Only send if the connection is still active
            if self.websocket.client_state == WebSocketState.CONNECTED:
                message = {
                    "type": message_type,
                    "isCompleteMessage": is_complete,
                    "termination": termination
                }

                # Add optional fields based on message type
                if message_type == "error":
                    message["data"] = f"Error: {str(data)}"
                    setattr(self.websocket, 'running_session', False)
                elif message_type == "session_status":
                    message["status"] = data if data else "stopped"
                    message["thread_id"] = self.thread_id
                    message["project_id"] = self.project_id
                    if data == "stopped":
                        self.websocket.running_session = False

                await self.websocket.send_json(message)
        except Exception as e:
            logging.error(f"Error sending websocket message: {str(e)}")
            if self.websocket and not getattr(self.websocket, 'is_closing', False):
                self.websocket.running_session = False

    async def send_error_message(self, error: str):
        """Utility function to send error messages."""
        if self.websocket:
            self.websocket.running_session = False  # Reset running flag on error
        await self._send_websocket_message("error", error)

    async def send_status_message(self, status: str = "stopped", is_complete: bool = True, termination: bool = True):
        """Utility function to send status messages."""
        await self._send_websocket_message("session_status", status, is_complete, termination)


    async def get_build_status_prompt(self, is_build_failed: bool, last_check_errors_result: str) -> str:
        """
        Returns formatted build status information for the dynamic prompt.

        Args:
            isBuildFailed (bool): Whether the last build failed
            last_check_errors_result (str): The result of the last error check

        Returns:
            str: Formatted build status information
        """
        if not is_build_failed:
            return """
    <check_for_errors_status>
    ✅ Check for Errors Result: All checks passed successfully
    - No errors detected in the last check for errors
    - Code is running as expected
    - You can proceed with implementing new features or improvements
    - You can also improve the codebase based on the user's request
    - IMPORTANT: After updating any files, ALWAYS run check_for_errors to see if there are still any errors in the code
    </check_for_errors_status>
    """

        try:
            return f"""
    <check_for_errors_status>
    ⚠️ Check for Errors Result: Errors Detected
    IMPORTANT: The following errors must be resolved while also addressing the user's request:
    {last_check_errors_result}
    Action Required:
    1. First, analyze and fix these errors
    2. Then, continue with implementing the user's request
    3. Ensure both tasks are completed in your response
    4. After updating any files, ALWAYS run check_for_errors to see if there are still any errors in the code
    Remember: You must handle both the error resolution AND the user's request in your next response.
    </check_for_errors_status>
    """
        except Exception as e:
            logging.error(f"Error getting build status: {str(e)}")
            return """
    <build_status>
    ⚠️ Build Status: Error Check Failed
    - Unable to retrieve detailed error information
    - Please proceed with caution and verify the build status
    - IMPORTANT: After updating any files, ALWAYS run check_for_errors to see if there are still any errors in the code
    </build_status>
    """


