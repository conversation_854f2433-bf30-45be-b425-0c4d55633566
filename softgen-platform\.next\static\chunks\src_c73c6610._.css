/* [project]/src/features/font/geist_71a83cc0.module.css [app-client] (css) */
@font-face {
  font-family: geist;
  src: url("../media/GeistVF-s.p.7fe29570.woff") format("woff");
  font-display: swap;
  font-weight: 100 200 300 400 500 600 700 800 900;
}

@font-face {
  font-family: geist Fallback;
  src: local(Arial);
  ascent-override: 85.83%;
  descent-override: 20.52%;
  line-gap-override: 9.33%;
  size-adjust: 107.19%;
}

.geist_71a83cc0-module__GcBWLa__className {
  font-family: geist, geist Fallback;
}

.geist_71a83cc0-module__GcBWLa__variable {
  --font-geist-sans: "geist", "geist Fallback";
}


/* [project]/src/features/font/geistmono_cb8291bf.module.css [app-client] (css) */
@font-face {
  font-family: geistMono;
  src: url("../media/GeistMono-s.p.e643b44e.ttf") format("truetype");
  font-display: swap;
  font-weight: 100 200 300 400 500 600 700 800 900;
}

@font-face {
  font-family: geistMono Fallback;
  src: local(Arial);
  ascent-override: 76.43%;
  descent-override: 22.43%;
  line-gap-override: 0.0%;
  size-adjust: 131.49%;
}

.geistmono_cb8291bf-module__CIEVNa__className {
  font-family: geistMono, geistMono Fallback;
}

.geistmono_cb8291bf-module__CIEVNa__variable {
  --font-geist-mono: "geistMono", "geistMono Fallback";
}


/* [project]/src/features/font/inter_f772a68f.module.css [app-client] (css) */
@font-face {
  font-family: inter;
  src: url("../media/inter-s.p.7b3669ea.woff2") format("woff2");
  font-display: swap;
  font-weight: 100 200 300 400 500 600 700 800 900;
}

@font-face {
  font-family: inter Fallback;
  src: local(Arial);
  ascent-override: 89.79%;
  descent-override: 22.36%;
  line-gap-override: 0.0%;
  size-adjust: 107.89%;
}

.inter_f772a68f-module__ZWZ_iG__className {
  font-family: inter, inter Fallback;
}

.inter_f772a68f-module__ZWZ_iG__variable {
  --font-inter: "inter", "inter Fallback";
}


/* [project]/src/features/global/markdown.css [app-client] (css) */
.markdown .contains-task-list {
  padding-left: 0;
  list-style-type: none;
}

.markdown .task-list-item {
  align-items: center;
  gap: .5rem;
  margin-top: .25rem;
  margin-bottom: .25rem;
  display: flex;
}

.markdown .task-list-item input[type="checkbox"] {
  border-color: hsl(var(--primary) / .2);
  width: 1rem;
  height: 1rem;
  color: hsl(var(--primary) / .8);
  border-radius: .25rem;
}

.markdown .task-list-item input[type="checkbox"]:focus {
  --tw-ring-color: hsl(var(--primary) / .2);
  --tw-ring-offset-width: 0px;
}

.markdown .task-list-item input[type="checkbox"] {
  cursor: default;
}

.markdown .task-list-item input[type="checkbox"]:checked {
  border-color: hsl(var(--primary) / .8);
  background-color: hsl(var(--primary) / .8);
}

.markdown h1 {
  letter-spacing: -.025em;
  color: hsl(var(--primary));
  margin-top: .75rem;
  margin-bottom: .75rem;
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 2.5rem;
}

.markdown h2 {
  letter-spacing: -.025em;
  color: hsl(var(--primary));
  margin-top: .75rem;
  margin-bottom: .75rem;
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 2.25rem;
}

.markdown h3 {
  letter-spacing: -.025em;
  color: hsl(var(--primary));
  margin-top: .75rem;
  margin-bottom: .75rem;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 2rem;
}

.markdown h4 {
  letter-spacing: -.025em;
  color: hsl(var(--primary));
  margin-top: .75rem;
  margin-bottom: .75rem;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.75rem;
}

.markdown h5 {
  letter-spacing: -.025em;
  color: hsl(var(--primary));
  margin-top: .75rem;
  margin-bottom: .75rem;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.75rem;
}

.markdown h6 {
  letter-spacing: -.025em;
  color: hsl(var(--primary));
  margin-top: .75rem;
  margin-bottom: .75rem;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5rem;
}

.markdown p {
  color: hsl(var(--primary) / .8);
  margin-top: .75rem;
  margin-bottom: .75rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.75rem;
}

.markdown ul {
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  list-style-type: disc;
}

.markdown ol {
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  list-style-type: decimal;
}

.markdown li {
  margin-top: .5rem;
  margin-bottom: .5rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.markdown a {
  --tw-border-spacing-x: .5rem;
  --tw-border-spacing-y: .5rem;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
  border-bottom-width: 1px;
  border-color: hsl(var(--primary) / .8);
  font-size: 1.1rem;
  font-weight: 400;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.markdown a:hover {
  border-color: hsl(var(--primary));
}

.markdown a {
  color: hsl(var(--primary) / .8);
}

.markdown a:hover {
  color: hsl(var(--primary));
}

.markdown blockquote {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: hsl(var(--primary) / .05) var(--tw-gradient-from-position);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  border-width: 1px;
  border-color: hsl(var(--primary) / .1);
  border-radius: .38rem;
  width: 100%;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem 2rem;
  display: flex;
  overflow-x: auto;
}

.markdown blockquote > p {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown blockquote > ul {
  padding-left: 0;
  list-style-type: none;
}

.markdown table {
  border-collapse: separate;
  --tw-border-spacing-x: 0px;
  --tw-border-spacing-y: 0px;
  width: 100%;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
  border-radius: var(--radius);
  border-width: 1px;
  border-color: hsl(var(--primary) / .2);
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

thead tr:first-child {
  background-color: hsl(var(--primary) / .05);
}

.markdown th {
  text-align: left;
  border-bottom-width: 1px;
  border-color: hsl(var(--primary) / .2);
  color: hsl(var(--primary));
  padding: .5rem 1rem;
  font-weight: 600;
}

.markdown th:first-child {
  border-top-left-radius: calc(var(--radius)  + 4px);
}

.markdown th:last-child {
  border-top-right-radius: calc(var(--radius)  + 4px);
}

.markdown td {
  border-bottom-width: 1px;
  border-color: hsl(var(--primary) / .1);
  color: hsl(var(--primary) / .8);
  padding: .5rem 1rem;
}

.markdown tr:last-child td {
  border-bottom-width: 0;
}

.markdown tr:last-child td:first-child {
  border-bottom-left-radius: calc(var(--radius)  + 4px);
}

.markdown tr:last-child td:last-child {
  border-bottom-right-radius: calc(var(--radius)  + 4px);
}

:root {
  --ctp-bg: #1e1e2e;
  --ctp-text: #cdd6f4;
  --ctp-comment: #6c7086;
  --ctp-string: #a6e3a1;
  --ctp-keyword: #cba6f7;
  --ctp-function: #89b4fa;
  --ctp-error: #f38ba8;
  --ctp-warning: #fab387;
  --ctp-info: #89dceb;
  --ctp-success: #94e2d5;
  --shiki-color-text: #414141;
  --shiki-color-background: transparent;
  --shiki-token-constant: #1976d2;
  --shiki-token-string: #22863a;
  --shiki-token-comment: #6a737d;
  --shiki-token-keyword: #d32f2f;
  --shiki-token-parameter: #ff9800;
  --shiki-token-function: #6f42c1;
  --shiki-token-string-expression: #22863a;
  --shiki-token-punctuation: #24292e;
  --shiki-token-link: #22863a;
}

[data-theme="dark"] {
  --shiki-color-text: #d1d1d1;
  --shiki-token-constant: #79b8ff;
  --shiki-token-string: #ffab70;
  --shiki-token-comment: #6b737c;
  --shiki-token-keyword: #f97583;
  --shiki-token-parameter: #ff9800;
  --shiki-token-function: #b392f0;
  --shiki-token-string-expression: #ffab70;
  --shiki-token-punctuation: #bbb;
  --shiki-token-link: #ffab70;
}

.markdown code[data-line-numbers] > [data-line]:before {
  text-align: right;
  --tw-text-opacity: 1;
  width: 1rem;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  margin-right: 1rem;
  display: inline-block;
}

.markdown code > [data-line]:before {
  text-align: right;
  width: 1rem;
  color: var(--ctp-text);
  margin-right: 2rem;
  display: inline-block;
}

.markdown code[data-line-numbers-max-digits="2"] > [data-line]:before {
  width: 2rem;
}

.markdown code[data-line-numbers-max-digits="3"] > [data-line]:before {
  width: 3rem;
}

.markdown p code {
  background-color: hsl(var(--primary) / .05);
  border-width: 1px;
  border-color: hsl(var(--primary) / .1);
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  border-radius: .38rem;
  margin-left: .125rem;
  padding: .125rem .5rem;
}

.markdown :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--ctp-bg);
  text-align: left;
  border-radius: .375rem;
  width: auto;
  margin-top: 1.71429em;
  margin-bottom: 1.71429em;
  padding: 1.14286em;
  font-size: .875em;
  font-weight: 400;
  line-height: 1.71429;
  overflow-x: auto;
}

:root {
  --ctp-bg: #1e1e2e;
  --ctp-text: #cdd6f4;
  --ctp-comment: #6c7086;
  --ctp-string: #a6e3a1;
  --ctp-keyword: #cba6f7;
  --ctp-function: #89b4fa;
  --ctp-error: #f38ba8;
  --ctp-warning: #fab387;
  --ctp-info: #89dceb;
  --ctp-success: #94e2d5;
}

.markdown code {
  font-size: .875rem;
  line-height: 1.625;
}

@media (width >= 768px) {
  .markdown code {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.markdown code {
  border-radius: var(--radius);
  background-color: hsl(var(--primary) / .05);
  color: hsl(var(--primary));
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  padding: .125rem .5rem;
}

.markdown pre {
  border-radius: calc(var(--radius)  + 1px);
  background-color: hsl(var(--primary) / .05);
  border-width: 1px;
  border-color: hsl(var(--primary) / .1);
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.markdown pre code {
  background-color: #0000;
  padding: 0;
  font-size: .85rem;
  display: block;
}

.markdown :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  background-color: hsl(var(--primary) / .05);
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: .875rem;
  font-weight: 400;
  line-height: 1.25rem;
  overflow-x: auto;
}

@media (width >= 768px) {
  .markdown :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.markdown :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  border-radius: calc(var(--radius)  + 1px);
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  line-height: 1.625;
}

.markdown pre > code {
  counter-reset: line;
}

.markdown code[data-line-numbers] {
  counter-reset: line;
  padding: 0;
}

.markdown code[data-line-numbers] > [data-line]:before {
  counter-increment: line;
  content: counter(line);
  text-align: right;
  width: 1.5rem;
  color: hsl(var(--primary) / .4);
  margin-right: 1rem;
  display: inline-block;
}

.markdown img {
  border-radius: .38rem;
  max-width: 100%;
  height: auto;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.markdown p img {
  object-fit: cover;
  border-width: 1px;
  border-radius: .38rem;
  margin-bottom: .75rem;
  margin-left: auto;
  margin-right: auto;
}

@media (width >= 768px) {
  .markdown p img {
    border-radius: calc(var(--radius)  + 1px);
  }
}

.markdown p em {
  color: hsl(var(--primary) / .7);
  padding-right: .25rem;
  font-size: 1rem;
  font-style: italic;
  font-weight: 300;
  line-height: 1.5rem;
}

.markdown hr {
  border-top-width: 1px;
  border-color: hsl(var(--primary) / .2);
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.markdown .code-line {
  color: hsl(var(--primary-foreground));
}

.markdown .code-line:is(.dark *) {
  color: hsl(var(--primary));
}

.markdown .scrollbar-track-blue-lighter::-webkit-scrollbar-track {
  background-color: #00000015;
}

.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb {
  background-color: #13131374;
}

.markdown .scrollbar-thumb-rounded::-webkit-scrollbar-thumb {
  border-radius: 7px;
}

.markdown .scrollbar-w-2::-webkit-scrollbar {
  width: .375rem;
  height: .375rem;
}

.markdown .scrollbar-track-blue-lighter::-webkit-scrollbar-track {
  background-color: hsl(var(--primary) / .05);
  border-radius: 9999px;
}

.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb {
  background-color: hsl(var(--primary) / .2);
  border-radius: 9999px;
}

.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--primary) / .3);
}

.markdown .scrollbar-thumb-blue::-webkit-scrollbar-thumb {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-duration: .2s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  animation-duration: .2s;
}


/* [project]/src/app/globals.css [app-client] (css) */
*, :before, :after, ::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border: 0 solid #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
  font-family: ui-sans-serif, system-ui, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  line-height: 1.5;
}

body {
  line-height: inherit;
  margin: 0;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, input:where([type="button"]), input:where([type="reset"]), input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: #0000;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  vertical-align: middle;
  display: block;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
  --inherit: inherit;
  --current: currentColor;
  --transparent: transparent;
  --black: #000;
  --white: #fff;
  --slate-50: #f8fafc;
  --slate-100: #f1f5f9;
  --slate-200: #e2e8f0;
  --slate-300: #cbd5e1;
  --slate-400: #94a3b8;
  --slate-500: #64748b;
  --slate-600: #475569;
  --slate-700: #334155;
  --slate-800: #1e293b;
  --slate-900: #0f172a;
  --slate-950: #020617;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --gray-950: #030712;
  --zinc-50: #fafafa;
  --zinc-100: #f4f4f5;
  --zinc-200: #e4e4e7;
  --zinc-300: #d4d4d8;
  --zinc-400: #a1a1aa;
  --zinc-500: #71717a;
  --zinc-600: #52525b;
  --zinc-700: #3f3f46;
  --zinc-800: #27272a;
  --zinc-900: #18181b;
  --zinc-950: #09090b;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;
  --stone-50: #fafaf9;
  --stone-100: #f5f5f4;
  --stone-200: #e7e5e4;
  --stone-300: #d6d3d1;
  --stone-400: #a8a29e;
  --stone-500: #78716c;
  --stone-600: #57534e;
  --stone-700: #44403c;
  --stone-800: #292524;
  --stone-900: #1c1917;
  --stone-950: #0c0a09;
  --red-50: #fef2f2;
  --red-100: #fee2e2;
  --red-200: #fecaca;
  --red-300: #fca5a5;
  --red-400: #f87171;
  --red-500: #ef4444;
  --red-600: #dc2626;
  --red-700: #b91c1c;
  --red-800: #991b1b;
  --red-900: #7f1d1d;
  --red-950: #450a0a;
  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-300: #fdba74;
  --orange-400: #fb923c;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-800: #9a3412;
  --orange-900: #7c2d12;
  --orange-950: #431407;
  --amber-50: #fffbeb;
  --amber-100: #fef3c7;
  --amber-200: #fde68a;
  --amber-300: #fcd34d;
  --amber-400: #fbbf24;
  --amber-500: #f59e0b;
  --amber-600: #d97706;
  --amber-700: #b45309;
  --amber-800: #92400e;
  --amber-900: #78350f;
  --amber-950: #451a03;
  --yellow-50: #fefce8;
  --yellow-100: #fef9c3;
  --yellow-200: #fef08a;
  --yellow-300: #fde047;
  --yellow-400: #facc15;
  --yellow-500: #eab308;
  --yellow-600: #ca8a04;
  --yellow-700: #a16207;
  --yellow-800: #854d0e;
  --yellow-900: #713f12;
  --yellow-950: #422006;
  --lime-50: #f7fee7;
  --lime-100: #ecfccb;
  --lime-200: #d9f99d;
  --lime-300: #bef264;
  --lime-400: #a3e635;
  --lime-500: #84cc16;
  --lime-600: #65a30d;
  --lime-700: #4d7c0f;
  --lime-800: #3f6212;
  --lime-900: #365314;
  --lime-950: #1a2e05;
  --green-50: #f0fdf4;
  --green-100: #dcfce7;
  --green-200: #bbf7d0;
  --green-300: #86efac;
  --green-400: #4ade80;
  --green-500: #22c55e;
  --green-600: #16a34a;
  --green-700: #15803d;
  --green-800: #166534;
  --green-900: #14532d;
  --green-950: #052e16;
  --emerald-50: #ecfdf5;
  --emerald-100: #d1fae5;
  --emerald-200: #a7f3d0;
  --emerald-300: #6ee7b7;
  --emerald-400: #34d399;
  --emerald-500: #10b981;
  --emerald-600: #059669;
  --emerald-700: #047857;
  --emerald-800: #065f46;
  --emerald-900: #064e3b;
  --emerald-950: #022c22;
  --teal-50: #f0fdfa;
  --teal-100: #ccfbf1;
  --teal-200: #99f6e4;
  --teal-300: #5eead4;
  --teal-400: #2dd4bf;
  --teal-500: #14b8a6;
  --teal-600: #0d9488;
  --teal-700: #0f766e;
  --teal-800: #115e59;
  --teal-900: #134e4a;
  --teal-950: #042f2e;
  --cyan-50: #ecfeff;
  --cyan-100: #cffafe;
  --cyan-200: #a5f3fc;
  --cyan-300: #67e8f9;
  --cyan-400: #22d3ee;
  --cyan-500: #06b6d4;
  --cyan-600: #0891b2;
  --cyan-700: #0e7490;
  --cyan-800: #155e75;
  --cyan-900: #164e63;
  --cyan-950: #083344;
  --sky-50: #f0f9ff;
  --sky-100: #e0f2fe;
  --sky-200: #bae6fd;
  --sky-300: #7dd3fc;
  --sky-400: #38bdf8;
  --sky-500: #0ea5e9;
  --sky-600: #0284c7;
  --sky-700: #0369a1;
  --sky-800: #075985;
  --sky-900: #0c4a6e;
  --sky-950: #082f49;
  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;
  --blue-950: #172554;
  --indigo-50: #eef2ff;
  --indigo-100: #e0e7ff;
  --indigo-200: #c7d2fe;
  --indigo-300: #a5b4fc;
  --indigo-400: #818cf8;
  --indigo-500: #6366f1;
  --indigo-600: #4f46e5;
  --indigo-700: #4338ca;
  --indigo-800: #3730a3;
  --indigo-900: #312e81;
  --indigo-950: #1e1b4b;
  --violet-50: #f5f3ff;
  --violet-100: #ede9fe;
  --violet-200: #ddd6fe;
  --violet-300: #c4b5fd;
  --violet-400: #a78bfa;
  --violet-500: #8b5cf6;
  --violet-600: #7c3aed;
  --violet-700: #6d28d9;
  --violet-800: #5b21b6;
  --violet-900: #4c1d95;
  --violet-950: #2e1065;
  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-200: #e9d5ff;
  --purple-300: #d8b4fe;
  --purple-400: #c084fc;
  --purple-500: #a855f7;
  --purple-600: #9333ea;
  --purple-700: #7e22ce;
  --purple-800: #6b21a8;
  --purple-900: #581c87;
  --purple-950: #3b0764;
  --fuchsia-50: #fdf4ff;
  --fuchsia-100: #fae8ff;
  --fuchsia-200: #f5d0fe;
  --fuchsia-300: #f0abfc;
  --fuchsia-400: #e879f9;
  --fuchsia-500: #d946ef;
  --fuchsia-600: #c026d3;
  --fuchsia-700: #a21caf;
  --fuchsia-800: #86198f;
  --fuchsia-900: #701a75;
  --fuchsia-950: #4a044e;
  --pink-50: #fdf2f8;
  --pink-100: #fce7f3;
  --pink-200: #fbcfe8;
  --pink-300: #f9a8d4;
  --pink-400: #f472b6;
  --pink-500: #ec4899;
  --pink-600: #db2777;
  --pink-700: #be185d;
  --pink-800: #9d174d;
  --pink-900: #831843;
  --pink-950: #500724;
  --rose-50: #fff1f2;
  --rose-100: #ffe4e6;
  --rose-200: #fecdd3;
  --rose-300: #fda4af;
  --rose-400: #fb7185;
  --rose-500: #f43f5e;
  --rose-600: #e11d48;
  --rose-700: #be123c;
  --rose-800: #9f1239;
  --rose-900: #881337;
  --rose-950: #4c0519;
  --background: 0 0% 98.04%;
  --foreground: 0 0% 0%;
  --card: 223.814 -172.524% 100%;
  --card-foreground: 0 0% 0%;
  --popover: 223.814 0.0005% 98.6829%;
  --popover-foreground: 0 0% 0%;
  --primary: 0 0% 0%;
  --primary-foreground: 223.814 -172.524% 100%;
  --secondary: 223.814 0.0001% 92.1478%;
  --secondary-foreground: 0 0% 0%;
  --muted: 223.814 0.0002% 96.0587%;
  --muted-foreground: 223.814 0% 32.3067%;
  --accent: 223.814 0.0001% 92.1478%;
  --accent-foreground: 0 0% 0%;
  --destructive: 358.433 74.912% 59.7455%;
  --destructive-foreground: 223.814 -172.524% 100%;
  --border: 223.814 0.0001% 89.5577%;
  --input: 223.814 0.0001% 92.1478%;
  --ring: 0 0% 0%;
  --chart-1: 40.6655 100.236% 50.9228%;
  --chart-2: 223.749 85.9924% 55.8092%;
  --chart-3: 223.814 0% 64.471%;
  --chart-4: 223.814 0.0001% 89.5577%;
  --chart-5: 223.814 0% 45.6078%;
  --dark: hsl(var(--dark));
  --sidebar: 223.814 0.0005% 98.6829%;
  --sidebar-foreground: 0 0% 0%;
  --sidebar-primary: 0 0% 0%;
  --sidebar-primary-foreground: 223.814 -172.524% 100%;
  --sidebar-accent: 223.814 0.0001% 92.1478%;
  --sidebar-accent-foreground: 0 0% 0%;
  --sidebar-border: 223.814 0.0001% 92.1478%;
  --sidebar-ring: 0 0% 0%;
  --color-1: hsl(var(--color-1));
  --color-2: hsl(var(--color-2));
  --color-3: hsl(var(--color-3));
  --color-4: hsl(var(--color-4));
  --color-5: hsl(var(--color-5));
  --overlay: 240 11.11% 5.29%;
  --font-sans: Geist, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Geist Mono, monospace;
  --radius: .5rem;
  --shadow-2xs: 0px 1px 2px 0px #00000017;
  --shadow-xs: 0px 1px 2px 0px #00000017;
  --shadow-sm: 0px 1px 2px 0px #0000002e, 0px 1px 2px -1px #0000002e;
  --shadow: 0px 1px 2px 0px #0000002e, 0px 1px 2px -1px #0000002e;
  --shadow-md: 0px 1px 2px 0px #0000002e, 0px 2px 4px -1px #0000002e;
  --shadow-lg: 0px 1px 2px 0px #0000002e, 0px 4px 6px -1px #0000002e;
  --shadow-xl: 0px 1px 2px 0px #0000002e, 0px 8px 10px -1px #0000002e;
  --shadow-2xl: 0px 1px 2px 0px #00000073;
}

.dark {
  --background: 240 10.53% 7.45%;
  --foreground: 223.814 -172.524% 100%;
  --card: 223.814 0% 3.5452%;
  --card-foreground: 223.814 -172.524% 100%;
  --popover: 223.814 0% 6.8692%;
  --popover-foreground: 223.814 -172.524% 100%;
  --primary: 223.814 -172.524% 100%;
  --primary-foreground: 0 0% 0%;
  --secondary: 223.814 0% 13.1499%;
  --secondary-foreground: 223.814 -172.524% 100%;
  --muted: 223.814 0% 11.304%;
  --muted-foreground: 223.814 0% 64.471%;
  --accent: 223.814 0% 19.8916%;
  --accent-foreground: 223.814 -172.524% 100%;
  --destructive: 359.913 100.249% 67.8807%;
  --destructive-foreground: 0 0% 0%;
  --border: 223.814 0% 14.0871%;
  --input: 223.814 0% 19.8916%;
  --ring: 223.814 0% 64.471%;
  --chart-1: 40.6655 100.236% 50.9228%;
  --chart-2: 218.162 90.0354% 55.1618%;
  --chart-3: 223.814 0% 45.6078%;
  --chart-4: 223.814 0% 32.3067%;
  --chart-5: 223.814 0.0001% 89.5577%;
  --sidebar: 223.814 0% 6.8692%;
  --sidebar-foreground: 223.814 -172.524% 100%;
  --sidebar-primary: 223.814 -172.524% 100%;
  --sidebar-primary-foreground: 0 0% 0%;
  --sidebar-accent: 223.814 0% 19.8916%;
  --sidebar-accent-foreground: 223.814 -172.524% 100%;
  --sidebar-border: 223.814 0% 19.8916%;
  --sidebar-ring: 223.814 0% 64.471%;
  --font-sans: Geist, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Geist Mono, monospace;
  --radius: .5rem;
  --shadow-2xs: 0px 1px 2px 0px #00000017;
  --shadow-xs: 0px 1px 2px 0px #00000017;
  --shadow-sm: 0px 1px 2px 0px #0000002e, 0px 1px 2px -1px #0000002e;
  --shadow: 0px 1px 2px 0px #0000002e, 0px 1px 2px -1px #0000002e;
  --shadow-md: 0px 1px 2px 0px #0000002e, 0px 2px 4px -1px #0000002e;
  --shadow-lg: 0px 1px 2px 0px #0000002e, 0px 4px 6px -1px #0000002e;
  --shadow-xl: 0px 1px 2px 0px #0000002e, 0px 8px 10px -1px #0000002e;
  --shadow-2xl: 0px 1px 2px 0px #00000073;
}

.theme {
  --animate-shine: shine var(--duration) infinite linear;
}

* {
  border-color: hsl(var(--border));
  outline-color: hsl(var(--ring) / .5);
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

.\!container {
  width: 100% !important;
}

.container {
  width: 100%;
}

@media (width >= 640px) {
  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}

@media (width >= 768px) {
  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}

@media (width >= 1024px) {
  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}

@media (width >= 1280px) {
  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}

@media (width >= 1536px) {
  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}

.dark .shiki, .dark .shiki span {
  color: var(--shiki-dark) !important;
  background-color: var(--shiki-dark-bg) !important;
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0;
}

.inset-px {
  inset: 1px;
}

.inset-x-0 {
  left: 0;
  right: 0;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.-bottom-20 {
  bottom: -5rem;
}

.-bottom-\[105\%\] {
  bottom: -105%;
}

.-left-\[45\%\] {
  left: -45%;
}

.-right-1\.5 {
  right: -.375rem;
}

.-right-2\.5 {
  right: -.625rem;
}

.-right-20 {
  right: -5rem;
}

.-right-\[35\%\] {
  right: -35%;
}

.-right-\[45\%\] {
  right: -45%;
}

.-top-1\.5 {
  top: -.375rem;
}

.-top-2\.5 {
  top: -.625rem;
}

.-top-24 {
  top: -6rem;
}

.-top-6 {
  top: -1.5rem;
}

.-top-\[105\%\] {
  top: -105%;
}

.bottom-0 {
  bottom: 0;
}

.bottom-10 {
  bottom: 2.5rem;
}

.bottom-20 {
  bottom: 5rem;
}

.bottom-3 {
  bottom: .75rem;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-6 {
  bottom: 1.5rem;
}

.bottom-8 {
  bottom: 2rem;
}

.bottom-96 {
  bottom: 24rem;
}

.bottom-\[15\%\] {
  bottom: 15%;
}

.left-0 {
  left: 0;
}

.left-1\/2 {
  left: 50%;
}

.left-1\/4 {
  left: 25%;
}

.left-2 {
  left: .5rem;
}

.left-3 {
  left: .75rem;
}

.left-8 {
  left: 2rem;
}

.left-\[0\%\] {
  left: 0%;
}

.left-\[25\%\] {
  left: 25%;
}

.left-\[50\%\] {
  left: 50%;
}

.right-0 {
  right: 0;
}

.right-1\/2 {
  right: 50%;
}

.right-10 {
  right: 2.5rem;
}

.right-2 {
  right: .5rem;
}

.right-3 {
  right: .75rem;
}

.right-4 {
  right: 1rem;
}

.top-0 {
  top: 0;
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: .5rem;
}

.top-3 {
  top: .75rem;
}

.top-4 {
  top: 1rem;
}

.top-6 {
  top: 1.5rem;
}

.top-\[15\%\] {
  top: 15%;
}

.top-\[50\%\] {
  top: 50%;
}

.top-\[60\%\] {
  top: 60%;
}

.top-\[calc\(1\.5rem\+0\.125rem\)\] {
  top: 1.625rem;
}

.isolate {
  isolation: isolate;
}

.-z-10 {
  z-index: -10;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[100\] {
  z-index: 100;
}

.z-\[1\] {
  z-index: 1;
}

.z-\[200\] {
  z-index: 200;
}

.z-\[299\] {
  z-index: 299;
}

.z-\[50\] {
  z-index: 50;
}

.z-\[60\] {
  z-index: 60;
}

.z-\[999\] {
  z-index: 999;
}

.-order-1 {
  order: -1;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.row-span-1 {
  grid-row: span 1 / span 1;
}

.-m-2 {
  margin: -.5rem;
}

.m-0 {
  margin: 0;
}

.m-0\.5 {
  margin: .125rem;
}

.m-1 {
  margin: .25rem;
}

.-mx-1 {
  margin-left: -.25rem;
  margin-right: -.25rem;
}

.-mx-2 {
  margin-left: -.5rem;
  margin-right: -.5rem;
}

.-mx-6 {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}

.-my-1\.5 {
  margin-top: -.375rem;
  margin-bottom: -.375rem;
}

.mx-0 {
  margin-left: 0;
  margin-right: 0;
}

.mx-0\.5 {
  margin-left: .125rem;
  margin-right: .125rem;
}

.mx-1 {
  margin-left: .25rem;
  margin-right: .25rem;
}

.mx-2 {
  margin-left: .5rem;
  margin-right: .5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}

.my-1 {
  margin-top: .25rem;
  margin-bottom: .25rem;
}

.my-1\.5 {
  margin-top: .375rem;
  margin-bottom: .375rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-16 {
  margin-top: 4rem;
  margin-bottom: 4rem;
}

.my-2 {
  margin-top: .5rem;
  margin-bottom: .5rem;
}

.my-2\.5 {
  margin-top: .625rem;
  margin-bottom: .625rem;
}

.my-24 {
  margin-top: 6rem;
  margin-bottom: 6rem;
}

.my-3 {
  margin-top: .75rem;
  margin-bottom: .75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-7 {
  margin-top: 1.75rem;
  margin-bottom: 1.75rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.-mr-1 {
  margin-right: -.25rem;
}

.-mt-0\.5 {
  margin-top: -.125rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: .25rem;
}

.mb-1\.5 {
  margin-bottom: .375rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-14 {
  margin-bottom: 3.5rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-auto {
  margin-bottom: auto;
}

.ml-0 {
  margin-left: 0;
}

.ml-1 {
  margin-left: .25rem;
}

.ml-2 {
  margin-left: .5rem;
}

.ml-3 {
  margin-left: .75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-8 {
  margin-left: 2rem;
}

.ml-9 {
  margin-left: 2.25rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-0 {
  margin-right: 0;
}

.mr-1 {
  margin-right: .25rem;
}

.mr-2 {
  margin-right: .5rem;
}

.mr-8 {
  margin-right: 2rem;
}

.mr-auto {
  margin-right: auto;
}

.mt-0 {
  margin-top: 0;
}

.mt-0\.5 {
  margin-top: .125rem;
}

.mt-1 {
  margin-top: .25rem;
}

.mt-1\.5 {
  margin-top: .375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: .5rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-24 {
  margin-top: 6rem;
}

.mt-3 {
  margin-top: .75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-auto {
  margin-top: auto;
}

.box-border {
  box-sizing: border-box;
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-5 {
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.list-item {
  display: list-item;
}

.hidden {
  display: none;
}

.aspect-square {
  aspect-ratio: 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.\!size-6 {
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.size-1 {
  width: .25rem;
  height: .25rem;
}

.size-10 {
  width: 2.5rem;
  height: 2.5rem;
}

.size-12 {
  width: 3rem;
  height: 3rem;
}

.size-16 {
  width: 4rem;
  height: 4rem;
}

.size-2 {
  width: .5rem;
  height: .5rem;
}

.size-2\.5 {
  width: .625rem;
  height: .625rem;
}

.size-3 {
  width: .75rem;
  height: .75rem;
}

.size-3\.5 {
  width: .875rem;
  height: .875rem;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}

.size-8 {
  width: 2rem;
  height: 2rem;
}

.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}

.size-\[1\.1rem\] {
  width: 1.1rem;
  height: 1.1rem;
}

.size-\[var\(--icon-size\)\] {
  width: var(--icon-size);
  height: var(--icon-size);
}

.size-fit {
  width: fit-content;
  height: fit-content;
}

.size-full {
  width: 100%;
  height: 100%;
}

.h-0 {
  height: 0;
}

.h-0\.5 {
  height: .125rem;
}

.h-1 {
  height: .25rem;
}

.h-1\.5 {
  height: .375rem;
}

.h-1\/4 {
  height: 25%;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: .5rem;
}

.h-2\.5 {
  height: .625rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-28 {
  height: 7rem;
}

.h-3 {
  height: .75rem;
}

.h-3\.5 {
  height: .875rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-64 {
  height: 16rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[1\.7rem\] {
  height: 1.7rem;
}

.h-\[100\%\] {
  height: 100%;
}

.h-\[120px\] {
  height: 120px;
}

.h-\[140\%\] {
  height: 140%;
}

.h-\[169\%\] {
  height: 169%;
}

.h-\[1px\] {
  height: 1px;
}

.h-\[200\%\] {
  height: 200%;
}

.h-\[200px\] {
  height: 200px;
}

.h-\[20rem\] {
  height: 20rem;
}

.h-\[240px\] {
  height: 240px;
}

.h-\[25rem\] {
  height: 25rem;
}

.h-\[30px\] {
  height: 30px;
}

.h-\[30rem\] {
  height: 30rem;
}

.h-\[32rem\] {
  height: 32rem;
}

.h-\[350px\] {
  height: 350px;
}

.h-\[38px\] {
  height: 38px;
}

.h-\[400px\] {
  height: 400px;
}

.h-\[40rem\] {
  height: 40rem;
}

.h-\[45rem\] {
  height: 45rem;
}

.h-\[50\%\] {
  height: 50%;
}

.h-\[500px\] {
  height: 500px;
}

.h-\[60\%\] {
  height: 60%;
}

.h-\[600px\] {
  height: 600px;
}

.h-\[70px\] {
  height: 70px;
}

.h-\[800px\] {
  height: 800px;
}

.h-\[85vh\] {
  height: 85vh;
}

.h-\[90vh\] {
  height: 90vh;
}

.h-\[93\%\] {
  height: 93%;
}

.h-\[calc\(100vh-10rem\)\] {
  height: calc(100vh - 10rem);
}

.h-\[calc\(100vh-110px\)\] {
  height: calc(100vh - 110px);
}

.h-\[calc\(100vh-120px\)\] {
  height: calc(100vh - 120px);
}

.h-\[calc\(100vh-40px\)\] {
  height: calc(100vh - 40px);
}

.h-\[calc\(100vh-5\.9rem\)\] {
  height: calc(100vh - 5.9rem);
}

.h-\[calc\(100vh-55px\)\] {
  height: calc(100vh - 55px);
}

.h-\[calc\(100vh-90px\)\] {
  height: calc(100vh - 90px);
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-auto {
  height: auto;
}

.h-fit {
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.max-h-32 {
  max-height: 8rem;
}

.max-h-40 {
  max-height: 10rem;
}

.max-h-52 {
  max-height: 13rem;
}

.max-h-60 {
  max-height: 15rem;
}

.max-h-64 {
  max-height: 16rem;
}

.max-h-80 {
  max-height: 20rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[160px\] {
  max-height: 160px;
}

.max-h-\[200px\] {
  max-height: 200px;
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[370px\] {
  max-height: 370px;
}

.max-h-\[500px\] {
  max-height: 500px;
}

.max-h-\[531px\] {
  max-height: 531px;
}

.max-h-\[565px\] {
  max-height: 565px;
}

.max-h-\[70vh\] {
  max-height: 70vh;
}

.max-h-\[80vh\] {
  max-height: 80vh;
}

.max-h-\[85vh\] {
  max-height: 85vh;
}

.max-h-\[90\%\] {
  max-height: 90%;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.max-h-\[95\%\] {
  max-height: 95%;
}

.min-h-11 {
  min-height: 2.75rem;
}

.min-h-12 {
  min-height: 3rem;
}

.min-h-40 {
  min-height: 10rem;
}

.min-h-\[100px\] {
  min-height: 100px;
}

.min-h-\[300px\] {
  min-height: 300px;
}

.min-h-\[350px\] {
  min-height: 350px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[44px\] {
  min-height: 44px;
}

.min-h-\[60px\] {
  min-height: 60px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-\[95\%\] {
  min-height: 95%;
}

.min-h-fit {
  min-height: fit-content;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0;
}

.w-0\.5 {
  width: .125rem;
}

.w-1 {
  width: .25rem;
}

.w-1\.5 {
  width: .375rem;
}

.w-1\/2 {
  width: 50%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: .5rem;
}

.w-2\.5 {
  width: .625rem;
}

.w-2\/3 {
  width: 66.6667%;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-28 {
  width: 7rem;
}

.w-3 {
  width: .75rem;
}

.w-3\.5 {
  width: .875rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-72 {
  width: 18rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-9 {
  width: 2.25rem;
}

.w-96 {
  width: 24rem;
}

.w-\[113\.6\%\] {
  width: 113.6%;
}

.w-\[120px\] {
  width: 120px;
}

.w-\[138\%\] {
  width: 138%;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[250px\] {
  width: 250px;
}

.w-\[83\%\] {
  width: 83%;
}

.w-\[85vw\] {
  width: 85vw;
}

.w-\[90\%\] {
  width: 90%;
}

.w-\[95\%\] {
  width: 95%;
}

.w-\[calc\(100\%-2rem\)\] {
  width: calc(100% - 2rem);
}

.w-auto {
  width: auto;
}

.w-fit {
  width: fit-content;
}

.w-full {
  width: 100%;
}

.min-w-0 {
  min-width: 0;
}

.min-w-20 {
  min-width: 5rem;
}

.min-w-28 {
  min-width: 7rem;
}

.min-w-48 {
  min-width: 12rem;
}

.min-w-72 {
  min-width: 18rem;
}

.min-w-\[10rem\] {
  min-width: 10rem;
}

.min-w-\[11rem\] {
  min-width: 11rem;
}

.min-w-\[14rem\] {
  min-width: 14rem;
}

.min-w-\[256px\] {
  min-width: 256px;
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[calc\(var\(--radix-select-trigger-width\)-8px\)\] {
  min-width: calc(var(--radix-select-trigger-width)  - 8px);
}

.min-w-\[var\(--radix-popper-anchor-width\)\] {
  min-width: var(--radix-popper-anchor-width);
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.min-w-fit {
  min-width: fit-content;
}

.max-w-28 {
  max-width: 7rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-40 {
  max-width: 10rem;
}

.max-w-44 {
  max-width: 11rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-52 {
  max-width: 13rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-64 {
  max-width: 16rem;
}

.max-w-72 {
  max-width: 18rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-80 {
  max-width: 20rem;
}

.max-w-\[26rem\] {
  max-width: 26rem;
}

.max-w-\[80\%\] {
  max-width: 80%;
}

.max-w-\[90\%\] {
  max-width: 90%;
}

.max-w-\[90vw\] {
  max-width: 90vw;
}

.max-w-fit {
  max-width: fit-content;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0, .shrink-0 {
  flex-shrink: 0;
}

.flex-grow, .grow {
  flex-grow: 1;
}

.caption-bottom {
  caption-side: bottom;
}

.border-separate {
  border-collapse: separate;
}

.border-spacing-0 {
  --tw-border-spacing-x: 0px;
  --tw-border-spacing-y: 0px;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}

.border-spacing-2 {
  --tw-border-spacing-x: .5rem;
  --tw-border-spacing-y: .5rem;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0\.5 {
  --tw-translate-x: .125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/3 {
  --tw-translate-x: 33.3333%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[15\%\] {
  --tw-translate-y: 15%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[6\.2rem\] {
  --tw-translate-y: 6.2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[75\%\] {
  --tw-translate-y: 75%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[9\.9rem\] {
  --tw-translate-y: 9.9rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[calc\(-50\%_-_2px\)\] {
  --tw-translate-y: calc(-50% - 2px);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-3 {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-y-\[1\] {
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

.animate-\[blink_1s_step-end_infinite\] {
  animation: 1s step-end infinite blink;
}

@keyframes bounce-dots {
  0%, 100% {
    opacity: .5;
    transform: scale(.8);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.animate-\[bounce-dots_1\.4s_ease-in-out_infinite\] {
  animation: 1.4s ease-in-out infinite bounce-dots;
}

@keyframes loading-dots {
  0%, 100% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

.animate-\[loading-dots_1\.4s_infinite_0\.2s\] {
  animation: 1.4s .2s infinite loading-dots;
}

.animate-\[loading-dots_1\.4s_infinite_0\.4s\] {
  animation: 1.4s .4s infinite loading-dots;
}

.animate-\[loading-dots_1\.4s_infinite_0\.6s\] {
  animation: 1.4s .6s infinite loading-dots;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: .8;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

.animate-\[pulse-dot_1\.2s_ease-in-out_infinite\] {
  animation: 1.2s ease-in-out infinite pulse-dot;
}

@keyframes shimmer {
  0% {
    background-position: 200%;
  }

  100% {
    background-position: -200%;
  }
}

.animate-\[shimmer_4s_infinite_linear\] {
  animation: 4s linear infinite shimmer;
}

@keyframes spinner-fade {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.animate-\[spinner-fade_1\.2s_linear_infinite\] {
  animation: 1.2s linear infinite spinner-fade;
}

@keyframes text-blink {
  0%, 100% {
    color: var(--primary);
  }

  50% {
    color: var(--muted-foreground);
  }
}

.animate-\[text-blink_2s_ease-in-out_infinite\] {
  animation: 2s ease-in-out infinite text-blink;
}

@keyframes thin-pulse {
  0%, 100% {
    opacity: .8;
    transform: scale(.95);
  }

  50% {
    opacity: .4;
    transform: scale(1.05);
  }
}

.animate-\[thin-pulse_1\.5s_ease-in-out_infinite\] {
  animation: 1.5s ease-in-out infinite thin-pulse;
}

@keyframes typing {
  0%, 100% {
    opacity: .5;
    transform: translateY(0);
  }

  50% {
    opacity: 1;
    transform: translateY(-2px);
  }
}

.animate-\[typing_1s_infinite\] {
  animation: 1s infinite typing;
}

@keyframes wave-bars {
  0%, 100% {
    opacity: .5;
    transform: scaleY(1);
  }

  50% {
    opacity: 1;
    transform: scaleY(.6);
  }
}

.animate-\[wave-bars_1\.2s_ease-in-out_infinite\] {
  animation: 1.2s ease-in-out infinite wave-bars;
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }

  50% {
    transform: scaleY(.6);
  }
}

.animate-\[wave_1s_ease-in-out_infinite\] {
  animation: 1s ease-in-out infinite wave;
}

@keyframes aurora {
  0% {
    background-position: 0%;
    transform: rotate(-5deg)scale(.9);
  }

  25% {
    background-position: 50% 100%;
    transform: rotate(5deg)scale(1.1);
  }

  50% {
    background-position: 100%;
    transform: rotate(-3deg)scale(.95);
  }

  75% {
    background-position: 50% 0;
    transform: rotate(3deg)scale(1.05);
  }

  100% {
    background-position: 0%;
    transform: rotate(-5deg)scale(.9);
  }
}

.animate-aurora {
  animation: 8s ease-in-out infinite alternate aurora;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-100% - 4rem));
  }
}

.animate-marquee {
  animation: 60s linear infinite marquee;
}

@keyframes orbit {
  0% {
    transform: rotate(calc(var(--angle) * 1deg)) translateY(calc(var(--radius) * 1px)) rotate(calc(var(--angle) * -1deg));
  }

  100% {
    transform: rotate(calc(var(--angle) * 1deg + 360deg)) translateY(calc(var(--radius) * 1px)) rotate(calc((var(--angle) * -1deg)  - 360deg));
  }
}

.animate-orbit {
  animation: orbit calc(var(--duration) * 1s) linear infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes shiny-text {
  0%, 90%, 100% {
    background-position: calc(-100% - var(--shiny-width)) 0;
  }

  30%, 60% {
    background-position: calc(100% + var(--shiny-width)) 0;
  }
}

.animate-shiny-text {
  animation: 8s infinite shiny-text;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }

  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: 1s linear infinite spin;
}

@keyframes spotlight {
  0% {
    opacity: 0;
    transform: translate(-72%, -62%)scale(.5);
  }

  100% {
    opacity: 1;
    transform: translate(-50%, -40%)scale(1);
  }
}

.animate-spotlight {
  animation: 2s .75s forwards spotlight;
}

.cursor-auto {
  cursor: auto;
}

.cursor-default {
  cursor: default;
}

.cursor-help {
  cursor: help;
}

.cursor-n-resize {
  cursor: n-resize;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.touch-none {
  touch-action: none;
}

.select-none {
  user-select: none;
}

.select-text {
  user-select: text;
}

.resize-none {
  resize: none;
}

.resize {
  resize: both;
}

.scroll-m-20 {
  scroll-margin: 5rem;
}

.scroll-my-1 {
  scroll-margin-top: .25rem;
  scroll-margin-bottom: .25rem;
}

.list-inside {
  list-style-position: inside;
}

.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.columns-1 {
  columns: 1;
}

.break-inside-avoid {
  break-inside: avoid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-0 {
  gap: 0;
}

.gap-0\.5 {
  gap: .125rem;
}

.gap-1 {
  gap: .25rem;
}

.gap-1\.5 {
  gap: .375rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-2 {
  gap: .5rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-x-3 {
  column-gap: .75rem;
}

.gap-x-8 {
  column-gap: 2rem;
}

.gap-y-2 {
  row-gap: .5rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.-space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-.75rem * var(--tw-space-x-reverse));
  margin-left: calc(-.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.25rem * var(--tw-space-x-reverse));
  margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.5rem * var(--tw-space-x-reverse));
  margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(.75rem * var(--tw-space-x-reverse));
  margin-left: calc(.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.125rem * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.375rem * var(--tw-space-y-reverse));
}

.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-16 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.space-y-2\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.625rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-primary\/20 > :not([hidden]) ~ :not([hidden]) {
  border-color: hsl(var(--primary) / .2);
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-clip {
  overflow: clip;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-clip {
  overflow-y: clip;
}

.overflow-x-visible {
  overflow-x: visible;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.text-wrap {
  text-wrap: wrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: .25rem;
}

.rounded-2xl {
  border-radius: calc(var(--radius)  + 4px);
}

.rounded-3xl {
  border-radius: calc(var(--radius)  + 6px);
}

.rounded-4xl {
  border-radius: calc(var(--radius)  + 8px);
}

.rounded-5xl {
  border-radius: calc(var(--radius)  + 10px);
}

.rounded-\[0\.4rem\] {
  border-radius: .4rem;
}

.rounded-\[2px\] {
  border-radius: 2px;
}

.rounded-\[inherit\] {
  border-radius: inherit;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: .38rem;
}

.rounded-md {
  border-radius: var(--radius);
}

.rounded-none {
  border-radius: 0;
}

.rounded-sm {
  border-radius: calc(var(--radius)  - 4px);
}

.rounded-xl {
  border-radius: calc(var(--radius)  + 1px);
}

.rounded-b-2xl {
  border-bottom-right-radius: calc(var(--radius)  + 4px);
  border-bottom-left-radius: calc(var(--radius)  + 4px);
}

.rounded-b-lg {
  border-bottom-right-radius: .38rem;
  border-bottom-left-radius: .38rem;
}

.rounded-b-none {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.rounded-b-xl {
  border-bottom-right-radius: calc(var(--radius)  + 1px);
  border-bottom-left-radius: calc(var(--radius)  + 1px);
}

.rounded-l-2xl {
  border-top-left-radius: calc(var(--radius)  + 4px);
  border-bottom-left-radius: calc(var(--radius)  + 4px);
}

.rounded-l-xl {
  border-top-left-radius: calc(var(--radius)  + 1px);
  border-bottom-left-radius: calc(var(--radius)  + 1px);
}

.rounded-r-2xl {
  border-top-right-radius: calc(var(--radius)  + 4px);
  border-bottom-right-radius: calc(var(--radius)  + 4px);
}

.rounded-r-xl {
  border-top-right-radius: calc(var(--radius)  + 1px);
  border-bottom-right-radius: calc(var(--radius)  + 1px);
}

.rounded-t-2xl {
  border-top-left-radius: calc(var(--radius)  + 4px);
  border-top-right-radius: calc(var(--radius)  + 4px);
}

.rounded-t-3xl {
  border-top-left-radius: calc(var(--radius)  + 6px);
  border-top-right-radius: calc(var(--radius)  + 6px);
}

.rounded-t-lg {
  border-top-left-radius: .38rem;
  border-top-right-radius: .38rem;
}

.rounded-t-none {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.rounded-t-xl {
  border-top-left-radius: calc(var(--radius)  + 1px);
  border-top-right-radius: calc(var(--radius)  + 1px);
}

.rounded-se {
  border-start-end-radius: .25rem;
}

.rounded-tl-2xl {
  border-top-left-radius: calc(var(--radius)  + 4px);
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-\[1px\] {
  border-width: 1px;
}

.border-x-0 {
  border-left-width: 0;
  border-right-width: 0;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-\[1\.5px\] {
  border-bottom-width: 1.5px;
}

.border-b-\[1px\] {
  border-bottom-width: 1px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-0 {
  border-left-width: 0;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-\[1\.5px\] {
  border-left-width: 1.5px;
}

.border-r {
  border-right-width: 1px;
}

.border-r-\[1\.5px\] {
  border-right-width: 1.5px;
}

.border-r-\[1px\] {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0;
}

.border-t-\[1\.5px\] {
  border-top-width: 1.5px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-\[\#34B27B\]\/20 {
  border-color: #34b27b33;
}

.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}

.border-amber-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}

.border-amber-600\/40 {
  border-color: #d9770666;
}

.border-background {
  border-color: hsl(var(--background));
}

.border-background\/10 {
  border-color: hsl(var(--background) / .1);
}

.border-background\/30 {
  border-color: hsl(var(--background) / .3);
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}

.border-blue-500\/10 {
  border-color: #3b82f61a;
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.border-blue-800 {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}

.border-border {
  border-color: hsl(var(--border));
}

.border-border\/50 {
  border-color: hsl(var(--border) / .5);
}

.border-border\/60 {
  border-color: hsl(var(--border) / .6);
}

.border-border\/70 {
  border-color: hsl(var(--border) / .7);
}

.border-border\/80 {
  border-color: hsl(var(--border) / .8);
}

.border-chart-2\/50 {
  border-color: hsl(var(--chart-2) / .5);
}

.border-chart-2\/70 {
  border-color: hsl(var(--chart-2) / .7);
}

.border-current {
  border-color: currentColor;
}

.border-destructive {
  border-color: hsl(var(--destructive));
}

.border-destructive\/20 {
  border-color: hsl(var(--destructive) / .2);
}

.border-destructive\/50 {
  border-color: hsl(var(--destructive) / .5);
}

.border-emerald-400 {
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}

.border-emerald-500\/10 {
  border-color: #10b9811a;
}

.border-emerald-800 {
  --tw-border-opacity: 1;
  border-color: rgb(6 95 70 / var(--tw-border-opacity, 1));
}

.border-foreground\/10 {
  border-color: hsl(var(--foreground) / .1);
}

.border-foreground\/20 {
  border-color: hsl(var(--foreground) / .2);
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-green-600 {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}

.border-inherit {
  border-color: inherit;
}

.border-input {
  border-color: hsl(var(--input));
}

.border-neutral-400 {
  --tw-border-opacity: 1;
  border-color: rgb(163 163 163 / var(--tw-border-opacity, 1));
}

.border-neutral-600\/40 {
  border-color: #52525266;
}

.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}

.border-primary {
  border-color: hsl(var(--primary));
}

.border-primary\/10 {
  border-color: hsl(var(--primary) / .1);
}

.border-primary\/15 {
  border-color: hsl(var(--primary) / .15);
}

.border-primary\/20 {
  border-color: hsl(var(--primary) / .2);
}

.border-primary\/40 {
  border-color: hsl(var(--primary) / .4);
}

.border-primary\/5 {
  border-color: hsl(var(--primary) / .05);
}

.border-primary\/50 {
  border-color: hsl(var(--primary) / .5);
}

.border-primary\/80 {
  border-color: hsl(var(--primary) / .8);
}

.border-primary\/95 {
  border-color: hsl(var(--primary) / .95);
}

.border-purple-400 {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.border-purple-600\/40 {
  border-color: #9333ea66;
}

.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-500\/10 {
  border-color: #ef44441a;
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}

.border-red-600\/30 {
  border-color: #dc26264d;
}

.border-red-900\/50 {
  border-color: #7f1d1d80;
}

.border-ring\/15 {
  border-color: hsl(var(--ring) / .15);
}

.border-ring\/40 {
  border-color: hsl(var(--ring) / .4);
}

.border-secondary\/10 {
  border-color: hsl(var(--secondary) / .1);
}

.border-sky-400\/40 {
  border-color: #38bdf866;
}

.border-transparent {
  border-color: #0000;
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.border-yellow-400 {
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}

.border-yellow-500\/10 {
  border-color: #eab3081a;
}

.border-yellow-600 {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}

.border-yellow-600\/40 {
  border-color: #ca8a0466;
}

.border-zinc-800 {
  --tw-border-opacity: 1;
  border-color: rgb(39 39 42 / var(--tw-border-opacity, 1));
}

.border-l-transparent {
  border-left-color: #0000;
}

.border-t-transparent {
  border-top-color: #0000;
}

.bg-\[\#006239\]\/90 {
  background-color: #006239e6;
}

.bg-\[\#0C97B0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(12 151 176 / var(--tw-bg-opacity, 1));
}

.bg-\[\#0a0a0a\]\/95 {
  background-color: #0a0a0af2;
}

.bg-\[\#E1E1E1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(225 225 225 / var(--tw-bg-opacity, 1));
}

.bg-\[\#E5F6FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(229 246 255 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F5F5F5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.bg-\[\#FFD1C1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 209 193 / var(--tw-bg-opacity, 1));
}

.bg-\[\#FFE5E5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 229 229 / var(--tw-bg-opacity, 1));
}

.bg-\[\#FFEF94\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 239 148 / var(--tw-bg-opacity, 1));
}

.bg-\[\#fafafa\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.bg-\[rgba\(93\,208\,220\)\] {
  --tw-bg-opacity: 1;
  background-color: rgba(93, 208, 220, var(--tw-bg-opacity, 1));
}

.bg-accent {
  background-color: hsl(var(--accent));
}

.bg-accent\/50 {
  background-color: hsl(var(--accent) / .5);
}

.bg-accent\/60 {
  background-color: hsl(var(--accent) / .6);
}

.bg-accent\/80 {
  background-color: hsl(var(--accent) / .8);
}

.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}

.bg-amber-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}

.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-background\/20 {
  background-color: hsl(var(--background) / .2);
}

.bg-background\/50 {
  background-color: hsl(var(--background) / .5);
}

.bg-background\/70 {
  background-color: hsl(var(--background) / .7);
}

.bg-background\/80 {
  background-color: hsl(var(--background) / .8);
}

.bg-background\/90 {
  background-color: hsl(var(--background) / .9);
}

.bg-background\/95 {
  background-color: hsl(var(--background) / .95);
}

.bg-black\/80 {
  background-color: #000c;
}

.bg-black\/90 {
  background-color: #000000e6;
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-300\/70 {
  background-color: #93c5fdb3;
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-blue-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.bg-border {
  background-color: hsl(var(--border));
}

.bg-card {
  background-color: hsl(var(--card));
}

.bg-chart-2\/20 {
  background-color: hsl(var(--chart-2) / .2);
}

.bg-destructive {
  background-color: hsl(var(--destructive));
}

.bg-destructive\/5 {
  background-color: hsl(var(--destructive) / .05);
}

.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}

.bg-emerald-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));
}

.bg-foreground\/10 {
  background-color: hsl(var(--foreground) / .1);
}

.bg-foreground\/5 {
  background-color: hsl(var(--foreground) / .05);
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-300\/70 {
  background-color: #86efacb3;
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.bg-input\/40 {
  background-color: hsl(var(--input) / .4);
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-muted-foreground\/10 {
  background-color: hsl(var(--muted-foreground) / .1);
}

.bg-muted\/30 {
  background-color: hsl(var(--muted) / .3);
}

.bg-muted\/50 {
  background-color: hsl(var(--muted) / .5);
}

.bg-neutral-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.bg-neutral-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 229 229 / var(--tw-bg-opacity, 1));
}

.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-overlay\/90 {
  background-color: hsl(var(--overlay) / .9);
}

.bg-pink-600\/10 {
  background-color: #db27771a;
}

.bg-pink-600\/30 {
  background-color: #db27774d;
}

.bg-popover {
  background-color: hsl(var(--popover));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.bg-primary\/10 {
  background-color: hsl(var(--primary) / .1);
}

.bg-primary\/20 {
  background-color: hsl(var(--primary) / .2);
}

.bg-primary\/5 {
  background-color: hsl(var(--primary) / .05);
}

.bg-primary\/50 {
  background-color: hsl(var(--primary) / .5);
}

.bg-primary\/90 {
  background-color: hsl(var(--primary) / .9);
}

.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-600\/10 {
  background-color: #9333ea1a;
}

.bg-purple-600\/30 {
  background-color: #9333ea4d;
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-300\/70 {
  background-color: #fca5a5b3;
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-red-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}

.bg-secondary {
  background-color: hsl(var(--secondary));
}

.bg-secondary\/20 {
  background-color: hsl(var(--secondary) / .2);
}

.bg-secondary\/30 {
  background-color: hsl(var(--secondary) / .3);
}

.bg-sidebar-accent {
  background-color: hsl(var(--sidebar-accent));
}

.bg-sidebar-accent\/10 {
  background-color: hsl(var(--sidebar-accent) / .1);
}

.bg-sidebar-accent\/15 {
  background-color: hsl(var(--sidebar-accent) / .15);
}

.bg-sidebar-accent\/40 {
  background-color: hsl(var(--sidebar-accent) / .4);
}

.bg-sidebar-accent\/60 {
  background-color: hsl(var(--sidebar-accent) / .6);
}

.bg-sidebar-accent\/80 {
  background-color: hsl(var(--sidebar-accent) / .8);
}

.bg-sidebar-accent\/95 {
  background-color: hsl(var(--sidebar-accent) / .95);
}

.bg-sidebar-border\/70 {
  background-color: hsl(var(--sidebar-border) / .7);
}

.bg-sidebar-primary {
  background-color: hsl(var(--sidebar-primary));
}

.bg-sidebar-primary\/80 {
  background-color: hsl(var(--sidebar-primary) / .8);
}

.bg-sidebar-ring {
  background-color: hsl(var(--sidebar-ring));
}

.bg-sidebar-ring\/10 {
  background-color: hsl(var(--sidebar-ring) / .1);
}

.bg-sidebar-ring\/20 {
  background-color: hsl(var(--sidebar-ring) / .2);
}

.bg-sky-400\/10 {
  background-color: #38bdf81a;
}

.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}

.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
  background-color: #0000;
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}

.bg-yellow-300\/70 {
  background-color: #fde047b3;
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.bg-opacity-20 {
  --tw-bg-opacity: .2;
}

.bg-\[image\:radial-gradient\(90\%_40\%_at_50\%_-20\%\,rgba\(93\,208\,220\,0\.7\)\,rgba\(255\,255\,255\,0\)\)\] {
  background-image: radial-gradient(90% 40% at 50% -20%, #5dd0dcb3, #fff0);
}

.bg-\[image\:radial-gradient\(90\%_50\%_at_50\%_-20\%\,rgba\(93\,208\,220\,0\.7\)\,rgba\(255\,255\,255\,0\)\)\] {
  background-image: radial-gradient(90% 50% at 50% -20%, #5dd0dcb3, #fff0);
}

.bg-\[image\:radial-gradient\(90\%_50\%_at_50\%_-35\%\,rgba\(93\,208\,220\)\,rgba\(255\,255\,255\,0\)\)\] {
  background-image: radial-gradient(90% 50% at 50% -35%, #5dd0dc, #fff0);
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-\[\#FFE9C5\] {
  --tw-gradient-from: #ffe9c5 var(--tw-gradient-from-position);
  --tw-gradient-to: #ffe9c500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-amber-50 {
  --tw-gradient-from: #fffbeb var(--tw-gradient-from-position);
  --tw-gradient-to: #fffbeb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-amber-500\/80 {
  --tw-gradient-from: #f59e0bcc var(--tw-gradient-from-position);
  --tw-gradient-to: #f59e0b00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-amber-600\/80 {
  --tw-gradient-from: #d97706cc var(--tw-gradient-from-position);
  --tw-gradient-to: #d9770600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-background {
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-background\/10 {
  --tw-gradient-from: hsl(var(--background) / .1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-background\/85 {
  --tw-gradient-from: hsl(var(--background) / .85) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-100 {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: #dbeafe00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-500\/10 {
  --tw-gradient-from: #3b82f61a var(--tw-gradient-from-position);
  --tw-gradient-to: #3b82f600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-600\/80 {
  --tw-gradient-from: #2563ebcc var(--tw-gradient-from-position);
  --tw-gradient-to: #2563eb00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-cyan-600\/80 {
  --tw-gradient-from: #0891b2cc var(--tw-gradient-from-position);
  --tw-gradient-to: #0891b200 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-emerald-500\/10 {
  --tw-gradient-from: #10b9811a var(--tw-gradient-from-position);
  --tw-gradient-to: #10b98100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-emerald-600\/80 {
  --tw-gradient-from: #059669cc var(--tw-gradient-from-position);
  --tw-gradient-to: #05966900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-fuchsia-600\/80 {
  --tw-gradient-from: #c026d3cc var(--tw-gradient-from-position);
  --tw-gradient-to: #c026d300 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-600\/80 {
  --tw-gradient-from: #16a34acc var(--tw-gradient-from-position);
  --tw-gradient-to: #16a34a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-indigo-600\/80 {
  --tw-gradient-from: #4f46e5cc var(--tw-gradient-from-position);
  --tw-gradient-to: #4f46e500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-lime-500\/80 {
  --tw-gradient-from: #84cc16cc var(--tw-gradient-from-position);
  --tw-gradient-to: #84cc1600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-lime-600\/80 {
  --tw-gradient-from: #65a30dcc var(--tw-gradient-from-position);
  --tw-gradient-to: #65a30d00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-muted-foreground {
  --tw-gradient-from: hsl(var(--muted-foreground)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted-foreground) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-orange-600\/80 {
  --tw-gradient-from: #ea580ccc var(--tw-gradient-from-position);
  --tw-gradient-to: #ea580c00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-pink-600\/80 {
  --tw-gradient-from: #db2777cc var(--tw-gradient-from-position);
  --tw-gradient-to: #db277700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/20 {
  --tw-gradient-from: hsl(var(--primary) / .2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/40 {
  --tw-gradient-from: hsl(var(--primary) / .4) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/50 {
  --tw-gradient-from: hsl(var(--primary) / .5) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-purple-600\/80 {
  --tw-gradient-from: #9333eacc var(--tw-gradient-from-position);
  --tw-gradient-to: #9333ea00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-500\/10 {
  --tw-gradient-from: #ef44441a var(--tw-gradient-from-position);
  --tw-gradient-to: #ef444400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-red-600\/80 {
  --tw-gradient-from: #dc2626cc var(--tw-gradient-from-position);
  --tw-gradient-to: #dc262600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-rose-500\/80 {
  --tw-gradient-from: #f43f5ecc var(--tw-gradient-from-position);
  --tw-gradient-to: #f43f5e00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-rose-600\/80 {
  --tw-gradient-from: #e11d48cc var(--tw-gradient-from-position);
  --tw-gradient-to: #e11d4800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-sky-600\/80 {
  --tw-gradient-from: #0284c7cc var(--tw-gradient-from-position);
  --tw-gradient-to: #0284c700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-teal-600\/80 {
  --tw-gradient-from: #0d9488cc var(--tw-gradient-from-position);
  --tw-gradient-to: #0d948800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-violet-600\/80 {
  --tw-gradient-from: #7c3aedcc var(--tw-gradient-from-position);
  --tw-gradient-to: #7c3aed00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-500\/10 {
  --tw-gradient-from: #eab3081a var(--tw-gradient-from-position);
  --tw-gradient-to: #eab30800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-yellow-600\/80 {
  --tw-gradient-from: #ca8a04cc var(--tw-gradient-from-position);
  --tw-gradient-to: #ca8a0400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-20\% {
  --tw-gradient-from-position: 20%;
}

.from-5\% {
  --tw-gradient-from-position: 5%;
}

.via-\[\#0C97B0\] {
  --tw-gradient-to: #0c97b000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0c97b0 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-amber-500\/60 {
  --tw-gradient-to: #f59e0b00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f59e0b99 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-background\/50 {
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / .5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-background\/85 {
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / .85) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-background\/90 {
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / .9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-black\/80 {
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #000c var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-blue-500\/60 {
  --tw-gradient-to: #3b82f600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #3b82f699 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-cyan-500\/60 {
  --tw-gradient-to: #06b6d400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #06b6d499 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-emerald-500\/60 {
  --tw-gradient-to: #10b98100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #10b98199 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-foreground {
  --tw-gradient-to: hsl(var(--foreground) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--foreground)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-fuchsia-500\/60 {
  --tw-gradient-to: #d946ef00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #d946ef99 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-green-500\/60 {
  --tw-gradient-to: #22c55e00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #22c55e99 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-indigo-500\/60 {
  --tw-gradient-to: #6366f100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #6366f199 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-lime-500\/60 {
  --tw-gradient-to: #84cc1600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #84cc1699 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-orange-400\/60 {
  --tw-gradient-to: #fb923c00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fb923c99 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-orange-500\/60 {
  --tw-gradient-to: #f9731600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f9731699 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-pink-500\/60 {
  --tw-gradient-to: #ec489900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ec489999 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-primary\/40 {
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / .4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-primary\/90 {
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / .9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-purple-500\/60 {
  --tw-gradient-to: #a855f700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #a855f799 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-red-500\/60 {
  --tw-gradient-to: #ef444400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ef444499 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-rose-500\/60 {
  --tw-gradient-to: #f43f5e00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f43f5e99 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-sky-500\/60 {
  --tw-gradient-to: #0ea5e900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0ea5e999 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-teal-500\/60 {
  --tw-gradient-to: #14b8a600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #14b8a699 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-transparent {
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-violet-500\/60 {
  --tw-gradient-to: #8b5cf600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #8b5cf699 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-yellow-400\/60 {
  --tw-gradient-to: #facc1500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #facc1599 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-yellow-500\/60 {
  --tw-gradient-to: #eab30800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eab30899 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-50\% {
  --tw-gradient-via-position: 50%;
}

.via-60\% {
  --tw-gradient-via-position: 60%;
}

.to-\[\#11274c\] {
  --tw-gradient-to: #11274c var(--tw-gradient-to-position);
}

.to-amber-300\/70 {
  --tw-gradient-to: #fcd34db3 var(--tw-gradient-to-position);
}

.to-amber-400\/70 {
  --tw-gradient-to: #fbbf24b3 var(--tw-gradient-to-position);
}

.to-background {
  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);
}

.to-background\/5 {
  --tw-gradient-to: hsl(var(--background) / .05) var(--tw-gradient-to-position);
}

.to-blue-400\/70 {
  --tw-gradient-to: #60a5fab3 var(--tw-gradient-to-position);
}

.to-blue-500\/5 {
  --tw-gradient-to: #3b82f60d var(--tw-gradient-to-position);
}

.to-cyan-400\/70 {
  --tw-gradient-to: #22d3eeb3 var(--tw-gradient-to-position);
}

.to-emerald-400\/70 {
  --tw-gradient-to: #34d399b3 var(--tw-gradient-to-position);
}

.to-emerald-500\/5 {
  --tw-gradient-to: #10b9810d var(--tw-gradient-to-position);
}

.to-fuchsia-400\/70 {
  --tw-gradient-to: #e879f9b3 var(--tw-gradient-to-position);
}

.to-green-400\/70 {
  --tw-gradient-to: #4ade80b3 var(--tw-gradient-to-position);
}

.to-indigo-400\/70 {
  --tw-gradient-to: #818cf8b3 var(--tw-gradient-to-position);
}

.to-lime-400\/70 {
  --tw-gradient-to: #a3e635b3 var(--tw-gradient-to-position);
}

.to-muted-foreground {
  --tw-gradient-to: hsl(var(--muted-foreground)) var(--tw-gradient-to-position);
}

.to-orange-300\/70 {
  --tw-gradient-to: #fdba74b3 var(--tw-gradient-to-position);
}

.to-orange-50 {
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}

.to-pink-400\/70 {
  --tw-gradient-to: #f472b6b3 var(--tw-gradient-to-position);
}

.to-primary\/20 {
  --tw-gradient-to: hsl(var(--primary) / .2) var(--tw-gradient-to-position);
}

.to-primary\/30 {
  --tw-gradient-to: hsl(var(--primary) / .3) var(--tw-gradient-to-position);
}

.to-purple-100 {
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}

.to-purple-400\/70 {
  --tw-gradient-to: #c084fcb3 var(--tw-gradient-to-position);
}

.to-purple-500\/10 {
  --tw-gradient-to: #a855f71a var(--tw-gradient-to-position);
}

.to-red-400\/70 {
  --tw-gradient-to: #f87171b3 var(--tw-gradient-to-position);
}

.to-red-500\/5 {
  --tw-gradient-to: #ef44440d var(--tw-gradient-to-position);
}

.to-rose-400\/70 {
  --tw-gradient-to: #fb7185b3 var(--tw-gradient-to-position);
}

.to-sky-400\/70 {
  --tw-gradient-to: #38bdf8b3 var(--tw-gradient-to-position);
}

.to-teal-400\/70 {
  --tw-gradient-to: #2dd4bfb3 var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.to-violet-400\/70 {
  --tw-gradient-to: #a78bfab3 var(--tw-gradient-to-position);
}

.to-yellow-400\/70 {
  --tw-gradient-to: #facc15b3 var(--tw-gradient-to-position);
}

.to-yellow-500\/5 {
  --tw-gradient-to: #eab3080d var(--tw-gradient-to-position);
}

.to-100\% {
  --tw-gradient-to-position: 100%;
}

.to-50\% {
  --tw-gradient-to-position: 50%;
}

.bg-\[200\%_auto\] {
  background-size: 200%;
}

.bg-\[length\:200\%_100\%\] {
  background-size: 200% 100%;
}

.bg-\[length\:200\%_auto\] {
  background-size: 200%;
}

.bg-clip-padding {
  background-clip: padding-box;
}

.bg-clip-text {
  background-clip: text;
}

.bg-center {
  background-position: center;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.fill-amber-500 {
  fill: #f59e0b;
}

.fill-blue-500 {
  fill: #3b82f6;
}

.fill-current {
  fill: currentColor;
}

.fill-primary {
  fill: hsl(var(--primary));
}

.fill-purple-500 {
  fill: #a855f7;
}

.stroke-black\/10 {
  stroke: #0000001a;
}

.stroke-primary {
  stroke: hsl(var(--primary));
}

.stroke-primary\/10 {
  stroke: hsl(var(--primary) / .1);
}

.stroke-primary\/5 {
  stroke: hsl(var(--primary) / .05);
}

.stroke-1 {
  stroke-width: 1px;
}

.object-contain {
  object-fit: contain;
}

.object-cover {
  object-fit: cover;
}

.object-center {
  object-position: center;
}

.p-0 {
  padding: 0;
}

.p-0\.5 {
  padding: .125rem;
}

.p-1 {
  padding: .25rem;
}

.p-1\.5 {
  padding: .375rem;
}

.p-2 {
  padding: .5rem;
}

.p-2\.5 {
  padding: .625rem;
}

.p-3 {
  padding: .75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-7 {
  padding: 1.75rem;
}

.p-8 {
  padding: 2rem;
}

.p-\[1px\] {
  padding: 1px;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-1 {
  padding-left: .25rem;
  padding-right: .25rem;
}

.px-1\.5 {
  padding-left: .375rem;
  padding-right: .375rem;
}

.px-2 {
  padding-left: .5rem;
  padding-right: .5rem;
}

.px-2\.5 {
  padding-left: .625rem;
  padding-right: .625rem;
}

.px-3 {
  padding-left: .75rem;
  padding-right: .75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-\[0\.3rem\] {
  padding-left: .3rem;
  padding-right: .3rem;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.py-0\.5 {
  padding-top: .125rem;
  padding-bottom: .125rem;
}

.py-1 {
  padding-top: .25rem;
  padding-bottom: .25rem;
}

.py-1\.5 {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: .5rem;
  padding-bottom: .5rem;
}

.py-2\.5 {
  padding-top: .625rem;
  padding-bottom: .625rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.py-3 {
  padding-top: .75rem;
  padding-bottom: .75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-40 {
  padding-top: 10rem;
  padding-bottom: 10rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-\[0\.2rem\] {
  padding-top: .2rem;
  padding-bottom: .2rem;
}

.py-\[0\.8rem\] {
  padding-top: .8rem;
  padding-bottom: .8rem;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-0\.5 {
  padding-bottom: .125rem;
}

.pb-1 {
  padding-bottom: .25rem;
}

.pb-1\.5 {
  padding-bottom: .375rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-2 {
  padding-bottom: .5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-24 {
  padding-bottom: 6rem;
}

.pb-3 {
  padding-bottom: .75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pb-\[1\.6rem\] {
  padding-bottom: 1.6rem;
}

.pl-0 {
  padding-left: 0;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-2 {
  padding-left: .5rem;
}

.pl-2\.5 {
  padding-left: .625rem;
}

.pl-3 {
  padding-left: .75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-0 {
  padding-right: 0;
}

.pr-1 {
  padding-right: .25rem;
}

.pr-1\.5 {
  padding-right: .375rem;
}

.pr-2 {
  padding-right: .5rem;
}

.pr-3 {
  padding-right: .75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-0 {
  padding-top: 0;
}

.pt-0\.5 {
  padding-top: .125rem;
}

.pt-1 {
  padding-top: .25rem;
}

.pt-1\.5 {
  padding-top: .375rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pt-14 {
  padding-top: 3.5rem;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-2 {
  padding-top: .5rem;
}

.pt-24 {
  padding-top: 6rem;
}

.pt-3 {
  padding-top: .75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.pt-\[0\.14rem\] {
  padding-top: .14rem;
}

.pt-\[56\.25\%\] {
  padding-top: 56.25%;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-middle {
  vertical-align: middle;
}

.font-inter {
  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.font-mono {
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.font-sans {
  font-family: ui-sans-serif, system-ui, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-\[0\.95rem\] {
  font-size: .95rem;
}

.text-\[0\.9rem\] {
  font-size: .9rem;
}

.text-\[11px\] {
  font-size: 11px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.font-thin {
  font-weight: 100;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

.leading-4 {
  line-height: 1rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-\[130\%\] {
  line-height: 130%;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[34px\] {
  line-height: 34px;
}

.leading-loose {
  line-height: 2;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-normal {
  letter-spacing: 0;
}

.tracking-tight {
  letter-spacing: -.025em;
}

.tracking-wider {
  letter-spacing: .05em;
}

.tracking-widest {
  letter-spacing: .1em;
}

.text-\[\#000000\] {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-\[\#0a0a0a\] {
  --tw-text-opacity: 1;
  color: rgb(10 10 10 / var(--tw-text-opacity, 1));
}

.text-\[\#56eda1\] {
  --tw-text-opacity: 1;
  color: rgb(86 237 161 / var(--tw-text-opacity, 1));
}

.text-\[\#fafafa\]\/90 {
  color: #fafafae6;
}

.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}

.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}

.text-background {
  color: hsl(var(--background));
}

.text-background\/90 {
  color: hsl(var(--background) / .9);
}

.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-card-foreground {
  color: hsl(var(--card-foreground));
}

.text-current {
  color: currentColor;
}

.text-destructive {
  color: hsl(var(--destructive));
}

.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}

.text-destructive\/90 {
  color: hsl(var(--destructive) / .9);
}

.text-emerald-500 {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}

.text-emerald-800 {
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-foreground\/80 {
  color: hsl(var(--foreground) / .8);
}

.text-foreground\/90 {
  color: hsl(var(--foreground) / .9);
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-inherit {
  color: inherit;
}

.text-muted {
  color: hsl(var(--muted));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-muted-foreground\/50 {
  color: hsl(var(--muted-foreground) / .5);
}

.text-muted-foreground\/70 {
  color: hsl(var(--muted-foreground) / .7);
}

.text-muted-foreground\/80 {
  color: hsl(var(--muted-foreground) / .8);
}

.text-neutral-400\/80 {
  color: #a3a3a3cc;
}

.text-neutral-600\/70 {
  color: #525252b3;
}

.text-neutral-800 {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity, 1));
}

.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}

.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}

.text-primary {
  color: hsl(var(--primary));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.text-primary\/45 {
  color: hsl(var(--primary) / .45);
}

.text-primary\/50 {
  color: hsl(var(--primary) / .5);
}

.text-primary\/60 {
  color: hsl(var(--primary) / .6);
}

.text-primary\/65 {
  color: hsl(var(--primary) / .65);
}

.text-primary\/70 {
  color: hsl(var(--primary) / .7);
}

.text-primary\/75 {
  color: hsl(var(--primary) / .75);
}

.text-primary\/80 {
  color: hsl(var(--primary) / .8);
}

.text-primary\/85 {
  color: hsl(var(--primary) / .85);
}

.text-primary\/90 {
  color: hsl(var(--primary) / .9);
}

.text-primary\/95 {
  color: hsl(var(--primary) / .95);
}

.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}

.text-sidebar-foreground {
  color: hsl(var(--sidebar-foreground));
}

.text-teal-700 {
  --tw-text-opacity: 1;
  color: rgb(15 118 110 / var(--tw-text-opacity, 1));
}

.text-transparent {
  color: #0000;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-zinc-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(113 113 122 / var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: .2;
}

.opacity-25 {
  opacity: .25;
}

.opacity-50 {
  opacity: .5;
}

.opacity-60 {
  opacity: .6;
}

.opacity-65 {
  opacity: .65;
}

.opacity-70 {
  opacity: .7;
}

.opacity-75 {
  opacity: .75;
}

.opacity-80 {
  opacity: .8;
}

.opacity-90 {
  opacity: .9;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a;
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 #0000000d;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline-offset: 2px;
  outline: 2px solid #0000;
}

.outline {
  outline-style: solid;
}

.outline-primary {
  outline-color: hsl(var(--primary));
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-\[1\.2px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1.2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-inset {
  --tw-ring-inset: inset;
}

.ring-blue-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity, 1));
}

.ring-border {
  --tw-ring-color: hsl(var(--border));
}

.ring-border\/50 {
  --tw-ring-color: hsl(var(--border) / .5);
}

.ring-chart-2\/30 {
  --tw-ring-color: hsl(var(--chart-2) / .3);
}

.ring-primary\/10 {
  --tw-ring-color: hsl(var(--primary) / .1);
}

.ring-primary\/20 {
  --tw-ring-color: hsl(var(--primary) / .2);
}

.ring-red-900\/30 {
  --tw-ring-color: #7f1d1d4d;
}

.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-\[3rem\] {
  --tw-blur: blur(3rem);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-\[90rem\] {
  --tw-blur: blur(90rem);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-\[1px\] {
  --tw-backdrop-blur: blur(1px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-\[color\,box-shadow\] {
  transition-property: color, box-shadow;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-none {
  transition-property: none;
}

.transition-opacity {
  transition-property: opacity;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-transform {
  transition-property: transform;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.duration-100 {
  transition-duration: .1s;
}

.duration-1000 {
  transition-duration: 1s;
}

.duration-150 {
  transition-duration: .15s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-500 {
  transition-duration: .5s;
}

.duration-700 {
  transition-duration: .7s;
}

.ease-in {
  transition-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

.will-change-\[background-position\] {
  will-change: background-position;
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
  animation-name: enter;
  animation-duration: .15s;
}

.fade-in, .fade-in-0 {
  --tw-enter-opacity: 0;
}

.zoom-in-95 {
  --tw-enter-scale: .95;
}

.duration-100 {
  animation-duration: .1s;
}

.duration-1000 {
  animation-duration: 1s;
}

.duration-150 {
  animation-duration: .15s;
}

.duration-200 {
  animation-duration: .2s;
}

.duration-300 {
  animation-duration: .3s;
}

.duration-500 {
  animation-duration: .5s;
}

.duration-700 {
  animation-duration: .7s;
}

.ease-in {
  animation-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.ease-in-out {
  animation-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, .2, 1);
}

.running {
  animation-play-state: running;
}

.paused {
  animation-play-state: paused;
}

.\[animation-direction\:reverse\] {
  animation-direction: reverse;
}

.\[backface-visibility\:hidden\] {
  backface-visibility: hidden;
}

.\[background-position\:0_0\] {
  background-position: 0 0;
}

.\[background-size\:var\(--shiny-width\)_100\%\] {
  background-size: var(--shiny-width) 100%;
}

.\[mask-image\:radial-gradient\(75\%_50\%_at_top_center\,white\,transparent\)\] {
  mask-image: radial-gradient(75% 50% at top, #fff, #0000);
}

.\[mask-image\:radial-gradient\(75\%_70\%_at_top_center\,white\,transparent\)\] {
  mask-image: radial-gradient(75% 70% at top, #fff, #0000);
}

.\[perspective\:1000px\] {
  perspective: 1000px;
}

.\[transform\:translate3d\(0\,0\,0\)\] {
  transform: translate3d(0, 0, 0);
}

.\[transition\:background-position_1s_cubic-bezier\(\.6\,\.6\,0\,1\)_infinite\] {
  transition: background-position 1s cubic-bezier(.6, .6, 0, 1) infinite;
}

::-webkit-scrollbar {
  background: none;
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: none;
}

::-webkit-scrollbar-thumb {
  background: none;
  border-radius: 3px;
  transition: background .3s;
}

:hover::-webkit-scrollbar-thumb, :active::-webkit-scrollbar-thumb {
  background: #9b9b9b80;
}

:hover::-webkit-scrollbar-thumb:hover {
  background: #9b9b9bcc;
}

* {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:hover, :active {
  scrollbar-color: #9b9b9b80 transparent;
}

body {
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

.grainy {
  scroll-behavior: smooth;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwBAMAAAClLOS0AAAAElBMVEUAAAD8/vz08vT09vT8+vzs7uxH16TeAAAAAXRSTlMAQObYZgAAAAlwSFlzAAAOxAAADsQBlSsOGwAAAuFJREFUOI0Vk+3NLiEIRG1B8ClAYAsQ2AIEt4D9ePtv5Xp/mZgYJ2fOFJKEfInkVWY2aglmQFkimRTV7MblYyVqD7HXyhKsSuPX12MeDhRHLtGvRG+P+B/S0Vu4OswR9tmvwNPyhdCDbVayJGads/WiUWcjCvCnruTBNHS9gmX2VzVbk7ZvB1gb1hkWFGl+A/n+/FowcO34U/XvKqZ/fHY+6vgRfU92XrOBUbGeeDfQmjWjdrK+frc6FdGReQhfSF5JvR29O2QrfNw1huTwlgsyXLo0u+5So82sgv7tsFZR2nxB6lXiquHrfD8nfYZ9SeT0LiuvSoVrxGY16pCNRZKqvwWsn5OHypPBELzohMCaRaa0ceTHYqe7X/gfJEEtKFbJpWoNqO+aS1cuTykGPpK5Ga48m6L3NefTr013KqYBQu929iP1oQ/7UwSR+i3zqruUmT84qmhzLpxyj7pr9kg7LKvqaXxZmdpn+6o8sHqSqojy02gU3U8q9PnpidiaLks0mbMYz+q2uVXsoBQ8bfURULYxRgZVYCHMv9F4OA7qxT2NPPpvGQ/sTDH2yznKh7E2AcErfcNsaIoN1izzbJiaY63x4QjUFdBSvDCvugPpu5xDny0jzEeuUQbcP1aGT9V90uixngTRLYNEIIZ6yOF1H8tm7rj2JxiefsVy53zGVy3ag5uuPsdufYOzYxLRxngKe7nhx3VAq54pmz/DK9/Q3aDam2Yt3hNXB4HuU87jKNd/CKZn77Qdn5QkXPfqSkhk7hGOXXB+7v09KbBbqdvxGqa0AqfK/atIrL2WXdAgXAJ43Wtwe/aIoacXezeGPMlhDOHDbSfHnaXsL2QzbT82GRwZuezdwcoWzx5pnOnGMUdHuiY7lhdyWzWiHnucLZQxYStMJbtcydHaQ6vtMbe0AcDbxG+QG14AL94xry4297xpy9Cpf1OoxZ740gHDfrK+gtsy0xabwJmfgtCeii79B6aj0SJeLbd7AAAAAElFTkSuQmCC");
}

.message > span {
  color: hsl(var(--primary) / .85);
  font-size: .95rem;
  line-height: 1.75rem;
}

.message > span:last-child {
  margin-bottom: 1rem;
}

.message > span:not(:first-child) {
  margin-top: 1rem;
}

@keyframes slide-up {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: .6;
    transform: translateY(-8px);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: .3s ease-out forwards slide-up;
}

.animate-fade-in-up {
  animation: .3s ease-out forwards fade-in-up;
}

.jumping-dots span {
  -webkit-animation: 1.5s infinite jump;
  animation: 2s infinite jump;
  position: relative;
  bottom: 0;
}

.jumping-dots .dot-1 {
  -webkit-animation-delay: .2s;
  animation-delay: .2s;
}

.jumping-dots .dot-2 {
  -webkit-animation-delay: .4s;
  animation-delay: .4s;
}

.jumping-dots .dot-3 {
  -webkit-animation-delay: .6s;
  animation-delay: .6s;
}

@-webkit-keyframes jump {
  0% {
    bottom: 0;
  }

  20% {
    bottom: 5px;
  }

  40% {
    bottom: 0;
  }
}

@keyframes jump {
  0% {
    bottom: 0;
  }

  20% {
    bottom: 5px;
  }

  40% {
    bottom: 0;
  }
}

.file\:me-3::file-selector-button {
  margin-inline-end: .75rem;
}

.file\:inline-flex::file-selector-button {
  display: inline-flex;
}

.file\:h-7::file-selector-button {
  height: 1.75rem;
}

.file\:h-full::file-selector-button {
  height: 100%;
}

.file\:border-0::file-selector-button {
  border-width: 0;
}

.file\:border-r::file-selector-button {
  border-right-width: 1px;
}

.file\:border-solid::file-selector-button {
  border-style: solid;
}

.file\:border-input::file-selector-button {
  border-color: hsl(var(--input));
}

.file\:bg-transparent::file-selector-button {
  background-color: #0000;
}

.file\:px-3::file-selector-button {
  padding-left: .75rem;
  padding-right: .75rem;
}

.file\:text-sm::file-selector-button {
  font-size: .875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:not-italic::file-selector-button {
  font-style: normal;
}

.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground\/70::placeholder {
  color: hsl(var(--muted-foreground) / .7);
}

.after\:absolute:after {
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-0:after {
  content: var(--tw-content);
  inset: 0;
}

.after\:inset-y-0:after {
  content: var(--tw-content);
  top: 0;
  bottom: 0;
}

.after\:left-1\/2:after {
  content: var(--tw-content);
  left: 50%;
}

.after\:w-1:after {
  content: var(--tw-content);
  width: .25rem;
}

.after\:-translate-x-1\/2:after {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.first\:mt-0:first-child {
  margin-top: 0;
}

.first\:mt-4:first-child {
  margin-top: 1rem;
}

.first\:mt-6:first-child {
  margin-top: 1.5rem;
}

.first\:rounded-s-lg:first-child {
  border-start-start-radius: .38rem;
  border-end-start-radius: .38rem;
}

.last\:mb-0:last-child {
  margin-bottom: 0;
}

.last\:mb-2:last-child {
  margin-bottom: .5rem;
}

.last\:mb-4:last-child {
  margin-bottom: 1rem;
}

.last\:mb-6:last-child {
  margin-bottom: 1.5rem;
}

.last\:rounded-e-lg:last-child {
  border-start-end-radius: .38rem;
  border-end-end-radius: .38rem;
}

.last\:border-b-0:last-child {
  border-bottom-width: 0;
}

.last\:pb-0:last-child {
  padding-bottom: 0;
}

.focus-within\:border-primary\/15:focus-within {
  border-color: hsl(var(--primary) / .15);
}

.focus-within\:font-semibold:focus-within {
  font-weight: 600;
}

.focus-within\:text-primary:focus-within {
  color: hsl(var(--primary));
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-primary\/20:focus-within {
  --tw-ring-color: hsl(var(--primary) / .2);
}

.focus-within\:ring-offset-2:focus-within {
  --tw-ring-offset-width: 2px;
}

.hover\:z-30:hover {
  z-index: 30;
}

.hover\:-translate-y-2:hover {
  --tw-translate-y: -.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:transform:hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:border-none:hover {
  border-style: none;
}

.hover\:border-primary:hover {
  border-color: hsl(var(--primary));
}

.hover\:border-primary\/10:hover {
  border-color: hsl(var(--primary) / .1);
}

.hover\:border-primary\/50:hover {
  border-color: hsl(var(--primary) / .5);
}

.hover\:border-red-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(127 29 29 / var(--tw-border-opacity, 1));
}

.hover\:bg-\[\#006239\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 98 57 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#0a0a0a\]\/90:hover {
  background-color: #0a0a0ae6;
}

.hover\:bg-\[\#fafafa\]\/95:hover {
  background-color: #fafafaf2;
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-accent\/10:hover {
  background-color: hsl(var(--accent) / .1);
}

.hover\:bg-accent\/15:hover {
  background-color: hsl(var(--accent) / .15);
}

.hover\:bg-accent\/40:hover {
  background-color: hsl(var(--accent) / .4);
}

.hover\:bg-accent\/50:hover {
  background-color: hsl(var(--accent) / .5);
}

.hover\:bg-accent\/90:hover {
  background-color: hsl(var(--accent) / .9);
}

.hover\:bg-background:hover {
  background-color: hsl(var(--background));
}

.hover\:bg-background\/20:hover {
  background-color: hsl(var(--background) / .2);
}

.hover\:bg-background\/80:hover {
  background-color: hsl(var(--background) / .8);
}

.hover\:bg-background\/90:hover {
  background-color: hsl(var(--background) / .9);
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / .9);
}

.hover\:bg-foreground\/10:hover {
  background-color: hsl(var(--foreground) / .1);
}

.hover\:bg-foreground\/5:hover {
  background-color: hsl(var(--foreground) / .05);
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted\/30:hover {
  background-color: hsl(var(--muted) / .3);
}

.hover\:bg-muted\/40:hover {
  background-color: hsl(var(--muted) / .4);
}

.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / .5);
}

.hover\:bg-orange-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary:hover {
  background-color: hsl(var(--primary));
}

.hover\:bg-primary\/10:hover {
  background-color: hsl(var(--primary) / .1);
}

.hover\:bg-primary\/5:hover {
  background-color: hsl(var(--primary) / .05);
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / .9);
}

.hover\:bg-secondary\/50:hover {
  background-color: hsl(var(--secondary) / .5);
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / .8);
}

.hover\:bg-secondary\/90:hover {
  background-color: hsl(var(--secondary) / .9);
}

.hover\:bg-sidebar-accent:hover {
  background-color: hsl(var(--sidebar-accent));
}

.hover\:bg-sidebar-accent\/20:hover {
  background-color: hsl(var(--sidebar-accent) / .2);
}

.hover\:bg-sidebar-accent\/25:hover {
  background-color: hsl(var(--sidebar-accent) / .25);
}

.hover\:bg-sidebar-accent\/40:hover {
  background-color: hsl(var(--sidebar-accent) / .4);
}

.hover\:bg-sidebar-accent\/60:hover {
  background-color: hsl(var(--sidebar-accent) / .6);
}

.hover\:bg-sidebar-accent\/80:hover {
  background-color: hsl(var(--sidebar-accent) / .8);
}

.hover\:bg-sidebar-accent\/95:hover {
  background-color: hsl(var(--sidebar-accent) / .95);
}

.hover\:bg-sidebar-primary:hover {
  background-color: hsl(var(--sidebar-primary));
}

.hover\:bg-sidebar-ring\/20:hover {
  background-color: hsl(var(--sidebar-ring) / .2);
}

.hover\:bg-teal-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}

.hover\:bg-transparent:hover {
  background-color: #0000;
}

.hover\:bg-zinc-800\/50:hover {
  background-color: #27272a80;
}

.hover\:text-\[\#fafafa\]:hover {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-background:hover {
  color: hsl(var(--background));
}

.hover\:text-destructive:hover {
  color: hsl(var(--destructive));
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.hover\:text-green-400:hover {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.hover\:text-green-800:hover {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-800:hover {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  color: hsl(var(--primary));
}

.hover\:text-primary\/45:hover {
  color: hsl(var(--primary) / .45);
}

.hover\:text-primary\/85:hover {
  color: hsl(var(--primary) / .85);
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:ring-0:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:mb-0:focus {
  margin-bottom: 0;
}

.focus\:rounded-none:focus {
  border-radius: 0;
}

.focus\:rounded-xl:focus {
  border-radius: calc(var(--radius)  + 1px);
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:bg-accent\/50:focus {
  background-color: hsl(var(--accent) / .5);
}

.focus\:bg-foreground\/10:focus {
  background-color: hsl(var(--foreground) / .1);
}

.focus\:bg-teal-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:text-primary:focus {
  color: hsl(var(--primary));
}

.focus\:outline-none:focus {
  outline-offset: 2px;
  outline: 2px solid #0000;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-primary\/20:focus {
  --tw-ring-color: hsl(var(--primary) / .2);
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.hover\:focus\:ring-0:focus:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:z-10:focus-visible {
  z-index: 10;
}

.focus-visible\:border-ring:focus-visible {
  border-color: hsl(var(--ring));
}

.focus-visible\:border-ring\/60:focus-visible {
  border-color: hsl(var(--ring) / .6);
}

.focus-visible\:bg-teal-100:focus-visible {
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}

.focus-visible\:bg-transparent:focus-visible {
  background-color: #0000;
}

.focus-visible\:outline-none:focus-visible {
  outline-offset: 2px;
  outline: 2px solid #0000;
}

.focus-visible\:outline-0:focus-visible {
  outline-width: 0;
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-\[3px\]:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-destructive\/20:focus-visible {
  --tw-ring-color: hsl(var(--destructive) / .2);
}

.focus-visible\:ring-primary:focus-visible {
  --tw-ring-color: hsl(var(--primary));
}

.focus-visible\:ring-primary\/10:focus-visible {
  --tw-ring-color: hsl(var(--primary) / .1);
}

.focus-visible\:ring-primary\/20:focus-visible {
  --tw-ring-color: hsl(var(--primary) / .2);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-ring\/50:focus-visible {
  --tw-ring-color: hsl(var(--ring) / .5);
}

.focus-visible\:ring-offset-0:focus-visible {
  --tw-ring-offset-width: 0px;
}

.focus-visible\:ring-offset-1:focus-visible {
  --tw-ring-offset-width: 1px;
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-gray-100:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.disabled\:text-gray-400:disabled {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.disabled\:opacity-100:disabled {
  opacity: 1;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

.disabled\:ring-0:disabled {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.group:hover .group-hover\:flex {
  display: flex;
}

.group:hover .group-hover\:hidden {
  display: none;
}

.group:hover .group-hover\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-y-\[9\%\] {
  --tw-translate-y: 9%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-\[\#3ECF8E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(62 207 142 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:text-primary {
  color: hsl(var(--primary));
}

.group:hover .group-hover\:text-primary\/80 {
  color: hsl(var(--primary) / .8);
}

.group:hover .group-hover\:text-zinc-200 {
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:underline {
  text-decoration-line: underline;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.group:hover .group-hover\:duration-700 {
  transition-duration: .7s;
}

.group:hover .group-hover\:ease-in {
  transition-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.group:hover .group-hover\:duration-700 {
  animation-duration: .7s;
}

.group:hover .group-hover\:ease-in {
  animation-timing-function: cubic-bezier(.4, 0, 1, 1);
}

.group.toaster .group-\[\.toaster\]\:border-border {
  border-color: hsl(var(--border));
}

.group.toast .group-\[\.toast\]\:bg-muted {
  background-color: hsl(var(--muted));
}

.group.toast .group-\[\.toast\]\:bg-primary {
  background-color: hsl(var(--primary));
}

.group.toaster .group-\[\.toaster\]\:bg-background {
  background-color: hsl(var(--background));
}

.group.toast .group-\[\.toast\]\:text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.group.toast .group-\[\.toast\]\:text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.group.toaster .group-\[\.toaster\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group.toaster .group-\[\.toaster\]\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.peer:focus-visible ~ .peer-focus-visible\:outline {
  outline-style: solid;
}

.peer:focus-visible ~ .peer-focus-visible\:outline-2 {
  outline-width: 2px;
}

.peer:focus-visible ~ .peer-focus-visible\:outline-ring\/70 {
  outline-color: hsl(var(--ring) / .7);
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: .7;
}

.has-\[\>svg\]\:px-2\.5:has( > svg) {
  padding-left: .625rem;
  padding-right: .625rem;
}

.has-\[\>svg\]\:px-3:has( > svg) {
  padding-left: .75rem;
  padding-right: .75rem;
}

.has-\[\>svg\]\:px-4:has( > svg) {
  padding-left: 1rem;
  padding-right: 1rem;
}

.has-\[\>svg\]\:pl-\[10px\]:has( > svg) {
  padding-left: 10px;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[state\=active\]\:block[data-state="active"] {
  display: block;
}

.data-\[state\=active\]\:flex[data-state="active"] {
  display: flex;
}

.data-\[state\=inactive\]\:hidden[data-state="inactive"] {
  display: none;
}

.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
  height: 1px;
}

.data-\[size\=default\]\:h-9[data-size="default"] {
  height: 2.25rem;
}

.data-\[size\=sm\]\:h-8[data-size="sm"] {
  height: 2rem;
}

.data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"], .data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
  width: 100%;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: .25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-5[data-state="checked"] {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[orientation\=horizontal\]\:flex-row[data-orientation="horizontal"] {
  flex-direction: row;
}

.data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"], .data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
  flex-direction: column;
}

.data-\[state\=active\]\:border-0[data-state="active"] {
  border-width: 0;
}

.data-\[state\=open\]\:border-t[data-state="open"] {
  border-top-width: 1px;
}

.data-\[state\=active\]\:border-sidebar-accent\/80[data-state="active"] {
  border-color: hsl(var(--sidebar-accent) / .8);
}

.data-\[state\=open\]\:border-muted-foreground[data-state="open"] {
  border-color: hsl(var(--muted-foreground));
}

.data-\[state\=active\]\:bg-primary[data-state="active"], .data-\[state\=checked\]\:bg-primary[data-state="checked"], .data-\[state\=completed\]\:bg-primary[data-state="completed"] {
  background-color: hsl(var(--primary));
}

.data-\[state\=inactive\]\:bg-sidebar-accent\/80[data-state="inactive"] {
  background-color: hsl(var(--sidebar-accent) / .8);
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}

.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}

.data-\[state\=open\]\:bg-sidebar-accent\/70[data-state="open"] {
  background-color: hsl(var(--sidebar-accent) / .7);
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}

.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
  background-color: hsl(var(--input));
}

.data-\[inset\]\:pl-8[data-inset] {
  padding-left: 2rem;
}

.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
  color: hsl(var(--muted-foreground));
}

.data-\[state\=active\]\:text-background[data-state="active"] {
  color: hsl(var(--background));
}

.data-\[state\=active\]\:text-primary-foreground[data-state="active"], .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"], .data-\[state\=completed\]\:text-primary-foreground[data-state="completed"] {
  color: hsl(var(--primary-foreground));
}

.data-\[state\=inactive\]\:text-muted-foreground[data-state="inactive"] {
  color: hsl(var(--muted-foreground));
}

.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
  color: hsl(var(--accent-foreground));
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}

.data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
  color: hsl(var(--destructive));
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: .5;
}

.data-\[state\=active\]\:shadow[data-state="active"] {
  --tw-shadow: 0 1px 3px 0 #0000001a, 0 1px 2px -1px #0000001a;
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: .3s;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: .5s;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
  animation-name: enter;
  animation-duration: .15s;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
  animation-name: exit;
  animation-duration: .15s;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: .5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: .5rem;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}

.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: .3s;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: .5s;
}

.\*\:data-\[slot\=select-value\]\:line-clamp-1[data-slot="select-value"] > * {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.\*\:data-\[slot\=select-value\]\:flex[data-slot="select-value"] > * {
  display: flex;
}

.\*\:data-\[slot\=select-value\]\:items-center[data-slot="select-value"] > * {
  align-items: center;
}

.\*\:data-\[slot\=select-value\]\:gap-2[data-slot="select-value"] > * {
  gap: .5rem;
}

.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  left: 0;
}

.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  height: .25rem;
}

.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  width: 100%;
}

.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]:after {
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:data-\[state\=open\]\:translate-y-\[8\.5rem\][data-state="open"]:hover {
  --tw-translate-y: 8.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[variant\=destructive\]\:focus\:bg-destructive\/10:focus[data-variant="destructive"] {
  background-color: hsl(var(--destructive) / .1);
}

.data-\[variant\=destructive\]\:focus\:text-destructive:focus[data-variant="destructive"] {
  color: hsl(var(--destructive));
}

.group\/stepper[data-orientation="horizontal"] .group-data-\[orientation\=horizontal\]\/stepper\:h-0\.5 {
  height: .125rem;
}

.group\/stepper[data-orientation="vertical"] .group-data-\[orientation\=vertical\]\/stepper\:h-12 {
  height: 3rem;
}

.group\/stepper[data-orientation="vertical"] .group-data-\[orientation\=vertical\]\/stepper\:h-\[calc\(100\%-1\.5rem-0\.25rem\)\] {
  height: calc(100% - 1.75rem);
}

.group\/stepper[data-orientation="horizontal"] .group-data-\[orientation\=horizontal\]\/stepper\:w-full {
  width: 100%;
}

.group\/stepper[data-orientation="vertical"] .group-data-\[orientation\=vertical\]\/stepper\:w-0\.5 {
  width: .125rem;
}

.group\/stepper[data-orientation="horizontal"] .group-data-\[orientation\=horizontal\]\/stepper\:flex-1 {
  flex: 1;
}

.group[data-expanded] .group-data-\[expanded\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-expanded] .group-data-\[expanded\]\:rotate-90, .group[data-state="open"] .group-data-\[state\=open\]\:rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/step[data-state="completed"] .group-data-\[state\=completed\]\/step\:scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/step[data-state="completed"] .group-data-\[state\=completed\]\/step\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/stepper[data-orientation="horizontal"] .group-data-\[orientation\=horizontal\]\/stepper\:flex-row {
  flex-direction: row;
}

.group\/stepper[data-orientation="vertical"] .group-data-\[orientation\=vertical\]\/stepper\:flex-col {
  flex-direction: column;
}

.group[data-state="open"] .group-data-\[state\=open\]\:rounded-b-none {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.group\/step[data-state="completed"] .group-data-\[state\=completed\]\/step\:bg-primary {
  background-color: hsl(var(--primary));
}

.group[data-state="open"] .group-data-\[state\=open\]\:pb-1\.5 {
  padding-bottom: .375rem;
}

.group\/step[data-state="completed"] .group-data-\[state\=completed\]\/step\:opacity-0 {
  opacity: 0;
}

.group\/step[data-state="completed"] .group-data-\[state\=completed\]\/step\:opacity-100 {
  opacity: 1;
}

.dark\:block:is(.dark *) {
  display: block;
}

.dark\:hidden:is(.dark *) {
  display: none;
}

.dark\:divide-primary\/10:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  border-color: hsl(var(--primary) / .1);
}

.dark\:border-0:is(.dark *) {
  border-width: 0;
}

.dark\:border-\[\#34B27B\]\/15:is(.dark *) {
  border-color: #34b27b26;
}

.dark\:border-amber-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(146 64 14 / var(--tw-border-opacity, 1));
}

.dark\:border-blue-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}

.dark\:border-blue-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 58 138 / var(--tw-border-opacity, 1));
}

.dark\:border-destructive:is(.dark *) {
  border-color: hsl(var(--destructive));
}

.dark\:border-emerald-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(6 95 70 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}

.dark\:border-green-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(20 83 45 / var(--tw-border-opacity, 1));
}

.dark\:border-input:is(.dark *) {
  border-color: hsl(var(--input));
}

.dark\:border-neutral-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(38 38 38 / var(--tw-border-opacity, 1));
}

.dark\:border-orange-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));
}

.dark\:border-primary\/10:is(.dark *) {
  border-color: hsl(var(--primary) / .1);
}

.dark\:border-primary\/15:is(.dark *) {
  border-color: hsl(var(--primary) / .15);
}

.dark\:border-primary\/60:is(.dark *) {
  border-color: hsl(var(--primary) / .6);
}

.dark\:border-purple-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}

.dark\:border-red-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(127 29 29 / var(--tw-border-opacity, 1));
}

.dark\:border-yellow-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));
}

.dark\:border-yellow-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(113 63 18 / var(--tw-border-opacity, 1));
}

.dark\:bg-\[\#0a0a0a\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(10 10 10 / var(--tw-bg-opacity, 1));
}

.dark\:bg-\[\#29292D\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(41 41 45 / var(--tw-bg-opacity, 1));
}

.dark\:bg-\[\#fafafa\]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.dark\:bg-accent\/30:is(.dark *) {
  background-color: hsl(var(--accent) / .3);
}

.dark\:bg-amber-900\/30:is(.dark *) {
  background-color: #78350f4d;
}

.dark\:bg-amber-900\/50:is(.dark *) {
  background-color: #78350f80;
}

.dark\:bg-background:is(.dark *) {
  background-color: hsl(var(--background));
}

.dark\:bg-background\/60:is(.dark *) {
  background-color: hsl(var(--background) / .6);
}

.dark\:bg-black:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.dark\:bg-blue-900\/20:is(.dark *) {
  background-color: #1e3a8a33;
}

.dark\:bg-blue-900\/30:is(.dark *) {
  background-color: #1e3a8a4d;
}

.dark\:bg-blue-900\/50:is(.dark *) {
  background-color: #1e3a8a80;
}

.dark\:bg-destructive\/60:is(.dark *) {
  background-color: hsl(var(--destructive) / .6);
}

.dark\:bg-emerald-900\/30:is(.dark *) {
  background-color: #064e3b4d;
}

.dark\:bg-emerald-900\/50:is(.dark *) {
  background-color: #064e3b80;
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800\/50:is(.dark *) {
  background-color: #1f293780;
}

.dark\:bg-input\/30:is(.dark *) {
  background-color: hsl(var(--input) / .3);
}

.dark\:bg-neutral-900\/30:is(.dark *) {
  background-color: #1717174d;
}

.dark\:bg-neutral-900\/50:is(.dark *) {
  background-color: #17171780;
}

.dark\:bg-orange-950\/20:is(.dark *) {
  background-color: #43140733;
}

.dark\:bg-pink-600\/10:is(.dark *) {
  background-color: #db27771a;
}

.dark\:bg-primary:is(.dark *) {
  background-color: hsl(var(--primary));
}

.dark\:bg-purple-600\/10:is(.dark *) {
  background-color: #9333ea1a;
}

.dark\:bg-purple-900\/30:is(.dark *) {
  background-color: #581c874d;
}

.dark\:bg-purple-900\/50:is(.dark *) {
  background-color: #581c8780;
}

.dark\:bg-red-900\/20:is(.dark *) {
  background-color: #7f1d1d33;
}

.dark\:bg-red-900\/30:is(.dark *) {
  background-color: #7f1d1d4d;
}

.dark\:bg-sidebar-accent\/15:is(.dark *) {
  background-color: hsl(var(--sidebar-accent) / .15);
}

.dark\:bg-sidebar-primary\/40:is(.dark *) {
  background-color: hsl(var(--sidebar-primary) / .4);
}

.dark\:bg-transparent:is(.dark *) {
  background-color: #0000;
}

.dark\:bg-yellow-900\/30:is(.dark *) {
  background-color: #713f124d;
}

.dark\:bg-yellow-900\/50:is(.dark *) {
  background-color: #713f1280;
}

.dark\:bg-yellow-950\/20:is(.dark *) {
  background-color: #42200633;
}

.dark\:bg-opacity-40:is(.dark *) {
  --tw-bg-opacity: .4;
}

.dark\:from-amber-950\/20:is(.dark *) {
  --tw-gradient-from: #451a0333 var(--tw-gradient-from-position);
  --tw-gradient-to: #451a0300 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-black:is(.dark *) {
  --tw-gradient-from: #000 var(--tw-gradient-from-position);
  --tw-gradient-to: #0000 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-blue-500\/5:is(.dark *) {
  --tw-gradient-from: #3b82f60d var(--tw-gradient-from-position);
  --tw-gradient-to: #3b82f600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-blue-900\/20:is(.dark *) {
  --tw-gradient-from: #1e3a8a33 var(--tw-gradient-from-position);
  --tw-gradient-to: #1e3a8a00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:via-amber-500\/30:is(.dark *) {
  --tw-gradient-to: #f59e0b00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f59e0b4d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-blue-500\/30:is(.dark *) {
  --tw-gradient-to: #3b82f600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #3b82f64d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-cyan-500\/30:is(.dark *) {
  --tw-gradient-to: #06b6d400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #06b6d44d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-emerald-500\/30:is(.dark *) {
  --tw-gradient-to: #10b98100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #10b9814d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-fuchsia-500\/30:is(.dark *) {
  --tw-gradient-to: #d946ef00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #d946ef4d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-green-500\/30:is(.dark *) {
  --tw-gradient-to: #22c55e00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #22c55e4d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-indigo-500\/30:is(.dark *) {
  --tw-gradient-to: #6366f100 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #6366f14d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-lime-500\/30:is(.dark *) {
  --tw-gradient-to: #84cc1600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #84cc164d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-orange-400\/30:is(.dark *) {
  --tw-gradient-to: #fb923c00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fb923c4d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-orange-500\/30:is(.dark *) {
  --tw-gradient-to: #f9731600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f973164d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-pink-500\/30:is(.dark *) {
  --tw-gradient-to: #ec489900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ec48994d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-purple-500\/30:is(.dark *) {
  --tw-gradient-to: #a855f700 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #a855f74d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-red-500\/30:is(.dark *) {
  --tw-gradient-to: #ef444400 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ef44444d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-rose-500\/30:is(.dark *) {
  --tw-gradient-to: #f43f5e00 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f43f5e4d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-sky-500\/30:is(.dark *) {
  --tw-gradient-to: #0ea5e900 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0ea5e94d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-teal-500\/30:is(.dark *) {
  --tw-gradient-to: #14b8a600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #14b8a64d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-violet-500\/30:is(.dark *) {
  --tw-gradient-to: #8b5cf600 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #8b5cf64d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-white\/80:is(.dark *) {
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fffc var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-yellow-400\/30:is(.dark *) {
  --tw-gradient-to: #facc1500 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #facc154d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:via-yellow-500\/30:is(.dark *) {
  --tw-gradient-to: #eab30800 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eab3084d var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:to-\[color-mix\(in_oklab\,var\(--primary\)_75\%\,var\(--background\)\)\]:is(.dark *) {
  --tw-gradient-to: color-mix(in oklab, var(--primary) 75%, var(--background)) var(--tw-gradient-to-position);
}

.dark\:to-orange-950\/20:is(.dark *) {
  --tw-gradient-to: #43140733 var(--tw-gradient-to-position);
}

.dark\:to-purple-500\/5:is(.dark *) {
  --tw-gradient-to: #a855f70d var(--tw-gradient-to-position);
}

.dark\:to-purple-900\/20:is(.dark *) {
  --tw-gradient-to: #581c8733 var(--tw-gradient-to-position);
}

.dark\:stroke-white\/10:is(.dark *) {
  stroke: #ffffff1a;
}

.dark\:text-\[\#fafafa\]:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.dark\:text-amber-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

.dark\:text-amber-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.dark\:text-amber-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.dark\:text-background:is(.dark *) {
  color: hsl(var(--background));
}

.dark\:text-blue-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.dark\:text-emerald-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}

.dark\:text-emerald-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-green-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.dark\:text-neutral-400\/70:is(.dark *) {
  color: #a3a3a3b3;
}

.dark\:text-neutral-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.dark\:text-orange-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}

.dark\:text-primary:is(.dark *) {
  color: hsl(var(--primary));
}

.dark\:text-primary\/60:is(.dark *) {
  color: hsl(var(--primary) / .6);
}

.dark\:text-primary\/75:is(.dark *) {
  color: hsl(var(--primary) / .75);
}

.dark\:text-primary\/80:is(.dark *) {
  color: hsl(var(--primary) / .8);
}

.dark\:text-primary\/90:is(.dark *) {
  color: hsl(var(--primary) / .9);
}

.dark\:text-primary\/95:is(.dark *) {
  color: hsl(var(--primary) / .95);
}

.dark\:text-purple-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.dark\:text-purple-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.dark\:text-red-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.dark\:\[--color-border\:color-mix\(in_oklab\,var\(--primary\)_10\%\,transparent\)\]:is(.dark *) {
  --color-border: color-mix(in oklab, var(--primary) 10%, transparent);
}

.dark\:hover\:bg-\[\#0a0a0a\]\/95:hover:is(.dark *) {
  background-color: #0a0a0af2;
}

.dark\:hover\:bg-\[\#fafafa\]\/80:hover:is(.dark *) {
  background-color: #fafafacc;
}

.dark\:hover\:bg-accent\/50:hover:is(.dark *) {
  background-color: hsl(var(--accent) / .5);
}

.dark\:hover\:bg-gray-800\/50:hover:is(.dark *) {
  background-color: #1f293780;
}

.dark\:hover\:bg-input\/50:hover:is(.dark *) {
  background-color: hsl(var(--input) / .5);
}

.dark\:hover\:bg-primary\/90:hover:is(.dark *) {
  background-color: hsl(var(--primary) / .9);
}

.dark\:hover\:text-primary:hover:is(.dark *) {
  color: hsl(var(--primary));
}

.dark\:focus-visible\:ring-destructive\/40:focus-visible:is(.dark *) {
  --tw-ring-color: hsl(var(--destructive) / .4);
}

.dark\:data-\[state\=active\]\:border-inherit[data-state="active"]:is(.dark *) {
  border-color: inherit;
}

.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:focus[data-variant="destructive"]:is(.dark *) {
  background-color: hsl(var(--destructive) / .2);
}

@media (width >= 640px) {
  .sm\:inset-6 {
    inset: 1.5rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0;
  }

  .sm\:mr-3 {
    margin-right: .75rem;
  }

  .sm\:mt-0 {
    margin-top: 0;
  }

  .sm\:mt-1 {
    margin-top: .25rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:w-\[var\(--width\)\] {
    width: var(--width);
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-fit {
    width: fit-content;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-5xl {
    max-width: 64rem;
  }

  .sm\:max-w-\[425px\] {
    max-width: 425px;
  }

  .sm\:max-w-\[500px\] {
    max-width: 500px;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .sm\:translate-y-\[4\.6rem\] {
    --tw-translate-y: 4.6rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:translate-y-\[8\.2rem\] {
    --tw-translate-y: 8.2rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:gap-y-0 {
    row-gap: 0;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(.5rem * var(--tw-space-x-reverse));
    margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg {
    border-radius: .38rem;
  }

  .sm\:rounded-b-3xl {
    border-bottom-right-radius: calc(var(--radius)  + 6px);
    border-bottom-left-radius: calc(var(--radius)  + 6px);
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .sm\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .sm\:pb-32 {
    padding-bottom: 8rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .sm\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (width >= 768px) {
  .md\:absolute {
    position: absolute;
  }

  .md\:order-1 {
    order: 1;
  }

  .md\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .md\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .md\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:mb-2 {
    margin-bottom: .5rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:ml-2 {
    margin-left: .5rem;
  }

  .md\:mr-1 {
    margin-right: .25rem;
  }

  .md\:mt-0 {
    margin-top: 0;
  }

  .md\:mt-20 {
    margin-top: 5rem;
  }

  .md\:mt-32 {
    margin-top: 8rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[21rem\] {
    height: 21rem;
  }

  .md\:h-\[300px\] {
    height: 300px;
  }

  .md\:h-\[30rem\] {
    height: 30rem;
  }

  .md\:h-\[40\.5rem\] {
    height: 40.5rem;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:h-screen {
    height: 100vh;
  }

  .md\:min-h-\[300px\] {
    min-height: 300px;
  }

  .md\:min-h-min {
    min-height: min-content;
  }

  .md\:w-12 {
    width: 3rem;
  }

  .md\:w-3\/4 {
    width: 75%;
  }

  .md\:w-32 {
    width: 8rem;
  }

  .md\:w-80 {
    width: 20rem;
  }

  .md\:w-\[85\%\] {
    width: 85%;
  }

  .md\:w-\[95\%\] {
    width: 95%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-fit {
    width: fit-content;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:max-w-3xl {
    max-width: 48rem;
  }

  .md\:max-w-7xl {
    max-width: 80rem;
  }

  .md\:max-w-\[26rem\] {
    max-width: 26rem;
  }

  .md\:max-w-full {
    max-width: 100%;
  }

  .md\:columns-2 {
    columns: 2;
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-\[100px_1fr\] {
    grid-template-columns: 100px 1fr;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-0 {
    gap: 0;
  }

  .md\:gap-1 {
    gap: .25rem;
  }

  .md\:gap-1\.5 {
    gap: .375rem;
  }

  .md\:gap-2 {
    gap: .5rem;
  }

  .md\:gap-3 {
    gap: .75rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-y-16 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }

  .md\:overflow-hidden {
    overflow: hidden;
  }

  .md\:rounded-xl {
    border-radius: calc(var(--radius)  + 1px);
  }

  .md\:rounded-b-2xl {
    border-bottom-right-radius: calc(var(--radius)  + 4px);
    border-bottom-left-radius: calc(var(--radius)  + 4px);
  }

  .md\:border-b {
    border-bottom-width: 1px;
  }

  .md\:border-r {
    border-right-width: 1px;
  }

  .md\:border-t-0 {
    border-top-width: 0;
  }

  .md\:p-2 {
    padding: .5rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .md\:px-1 {
    padding-left: .25rem;
    padding-right: .25rem;
  }

  .md\:px-2 {
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:py-0 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .md\:py-1\.5 {
    padding-top: .375rem;
    padding-bottom: .375rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .md\:pb-20 {
    padding-bottom: 5rem;
  }

  .md\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .md\:pl-0 {
    padding-left: 0;
  }

  .md\:pl-2 {
    padding-left: .5rem;
  }

  .md\:pr-3 {
    padding-right: .75rem;
  }

  .md\:pt-0 {
    padding-top: 0;
  }

  .md\:pt-1 {
    padding-top: .25rem;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:leading-8 {
    line-height: 2rem;
  }

  .md\:hover\:-translate-y-2:hover {
    --tw-translate-y: -.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:hover\:-rotate-1:hover {
    --tw-rotate: -1deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:hover\:rotate-1:hover {
    --tw-rotate: 1deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:hover\:bg-sidebar-accent:hover {
    background-color: hsl(var(--sidebar-accent));
  }
}

@media (width >= 1024px) {
  .lg\:bottom-auto {
    bottom: auto;
  }

  .lg\:left-4 {
    left: 1rem;
  }

  .lg\:left-\[50\%\] {
    left: 50%;
  }

  .lg\:top-\[50\%\] {
    top: 50%;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mt-0 {
    margin-top: 0;
  }

  .lg\:mt-1 {
    margin-top: .25rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[600px\] {
    height: 600px;
  }

  .lg\:h-\[90vh\] {
    height: 90vh;
  }

  .lg\:h-auto {
    height: auto;
  }

  .lg\:h-fit {
    height: fit-content;
  }

  .lg\:w-\[84\%\] {
    width: 84%;
  }

  .lg\:w-fit {
    width: fit-content;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:max-w-2xl {
    max-width: 42rem;
  }

  .lg\:max-w-3xl {
    max-width: 48rem;
  }

  .lg\:max-w-4xl {
    max-width: 56rem;
  }

  .lg\:max-w-5xl {
    max-width: 64rem;
  }

  .lg\:max-w-6xl {
    max-width: 72rem;
  }

  .lg\:max-w-7xl {
    max-width: 80rem;
  }

  .lg\:max-w-\[90vw\] {
    max-width: 90vw;
  }

  .lg\:max-w-full {
    max-width: 100%;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:max-w-md {
    max-width: 28rem;
  }

  .lg\:max-w-xl {
    max-width: 36rem;
  }

  .lg\:translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:columns-4 {
    columns: 4;
  }

  .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-2 {
    gap: .5rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:space-y-20 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(5rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .lg\:overflow-visible {
    overflow: visible;
  }

  .lg\:rounded-2xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .lg\:rounded-xl {
    border-radius: calc(var(--radius)  + 1px);
  }

  .lg\:rounded-b-2xl {
    border-bottom-right-radius: calc(var(--radius)  + 4px);
    border-bottom-left-radius: calc(var(--radius)  + 4px);
  }

  .lg\:border {
    border-width: 1px;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:duration-200 {
    transition-duration: .2s;
    animation-duration: .2s;
  }

  .lg\:data-\[state\=open\]\:animate-in[data-state="open"] {
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
    animation-name: enter;
    animation-duration: .15s;
  }

  .lg\:data-\[state\=closed\]\:animate-out[data-state="closed"] {
    --tw-exit-opacity: initial;
    --tw-exit-scale: initial;
    --tw-exit-rotate: initial;
    --tw-exit-translate-x: initial;
    --tw-exit-translate-y: initial;
    animation-name: exit;
    animation-duration: .15s;
  }

  .lg\:data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .lg\:data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .lg\:data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .lg\:data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .lg\:data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
    --tw-exit-translate-x: -50%;
  }

  .lg\:data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
    --tw-exit-translate-y: -48%;
  }

  .lg\:data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
    --tw-enter-translate-x: -50%;
  }

  .lg\:data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
    --tw-enter-translate-y: -48%;
  }
}

@media (width >= 1280px) {
  .xl\:text-8xl {
    font-size: 6rem;
    line-height: 1;
  }
}

.rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.\[\&\:\:-webkit-search-cancel-button\]\:appearance-none::-webkit-search-cancel-button {
  appearance: none;
}

.\[\&\:\:-webkit-search-decoration\]\:appearance-none::-webkit-search-decoration {
  appearance: none;
}

.\[\&\:\:-webkit-search-results-button\]\:appearance-none::-webkit-search-results-button {
  appearance: none;
}

.\[\&\:\:-webkit-search-results-decoration\]\:appearance-none::-webkit-search-results-decoration {
  appearance: none;
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
  padding-right: 0;
}

.\[\&\:not\(\:first-child\)\]\:my-4:not(:first-child) {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.\[\&\:not\(\:first-child\)\]\:my-5:not(:first-child) {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.\[\&\:not\(\:first-child\)\]\:my-8:not(:first-child) {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.\[\&\:not\(\:first-child\)\]\:mb-2:not(:first-child) {
  margin-bottom: .5rem;
}

.\[\&\:not\(\:first-child\)\]\:mt-12:not(:first-child) {
  margin-top: 3rem;
}

.\[\&\:not\(\:first-child\)\]\:mt-2:not(:first-child) {
  margin-top: .5rem;
}

.\[\&\:not\(\:first-child\)\]\:mt-4:not(:first-child) {
  margin-top: 1rem;
}

.\[\&\:not\(\:first-child\)\]\:mt-5:not(:first-child) {
  margin-top: 1.25rem;
}

.\[\&\:not\(\:first-child\)\]\:mt-6:not(:first-child), .\[\&\>code\]\:mt-6 > code {
  margin-top: 1.5rem;
}

.\[\&\>code\]\:border-none > code {
  border-style: none;
}

.\[\&\>code\]\:bg-transparent > code {
  background-color: #0000;
}

.\[\&\>code\]\:font-mono > code {
  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.\[\&\>code\]\:text-\[0\.95rem\] > code {
  font-size: .95rem;
}

.\[\&\>code\]\:font-normal > code {
  font-weight: 400;
}

.\[\&\>li\]\:mt-1 > li {
  margin-top: .25rem;
}

.\[\&\>li\]\:text-primary\/45 > li {
  color: hsl(var(--primary) / .45);
}

.\[\&\>li\]\:text-primary\/85 > li {
  color: hsl(var(--primary) / .85);
}

.\[\&\>pre\]\:w-full > pre {
  width: 100%;
}

.\[\&\>pre\]\:whitespace-pre-wrap > pre {
  white-space: pre-wrap;
}

.\[\&\>pre\]\:break-words > pre {
  overflow-wrap: break-word;
}

.\[\&\>pre\]\:p-0 > pre {
  padding: 0;
}

.\[\&\>pre\]\:px-4 > pre {
  padding-left: 1rem;
  padding-right: 1rem;
}

.\[\&\>pre\]\:py-4 > pre {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.\[\&\>span\]\:line-clamp-1 > span {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.\[\&\>span\]\:flex > span {
  display: flex;
}

.\[\&\>span\]\:items-center > span {
  align-items: center;
}

.\[\&\>span\]\:gap-2 > span {
  gap: .5rem;
}

.\[\&\>span_svg\]\:shrink-0 > span svg {
  flex-shrink: 0;
}

.\[\&\>span_svg\]\:text-muted-foreground\/80 > span svg {
  color: hsl(var(--muted-foreground) / .8);
}

.\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div {
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:pointer-events-none > svg {
  pointer-events: none;
}

.\[\&\>svg\]\:absolute > svg {
  position: absolute;
}

.\[\&\>svg\]\:left-4 > svg {
  left: 1rem;
}

.\[\&\>svg\]\:top-4 > svg {
  top: 1rem;
}

.\[\&\>svg\]\:size-3 > svg {
  width: .75rem;
  height: .75rem;
}

.\[\&\>svg\]\:h-3\.5 > svg {
  height: .875rem;
}

.\[\&\>svg\]\:w-3\.5 > svg {
  width: .875rem;
}

.\[\&\>svg\]\:text-destructive > svg {
  color: hsl(var(--destructive));
}

.\[\&\>svg\]\:text-foreground > svg {
  color: hsl(var(--foreground));
}

.\[\&\>svg\~\*\]\:pl-7 > svg ~ * {
  padding-left: 1.75rem;
}

.\[\&\>tr\]\:last\:border-b-0:last-child > tr {
  border-bottom-width: 0;
}

.\[\&\[align\=center\]\]\:text-center[align="center"] {
  text-align: center;
}

.\[\&\[align\=right\]\]\:text-right[align="right"] {
  text-align: right;
}

.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction="vertical"] > div {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_\*\[role\=option\]\>span\>svg\]\:shrink-0 [role="option"] > span > svg {
  flex-shrink: 0;
}

.\[\&_\*\[role\=option\]\>span\>svg\]\:text-muted-foreground\/80 [role="option"] > span > svg {
  color: hsl(var(--muted-foreground) / .8);
}

.\[\&_\*\[role\=option\]\>span\]\:end-2 [role="option"] > span {
  inset-inline-end: .5rem;
}

.\[\&_\*\[role\=option\]\>span\]\:start-auto [role="option"] > span {
  inset-inline-start: auto;
}

.\[\&_\*\[role\=option\]\>span\]\:flex [role="option"] > span {
  display: flex;
}

.\[\&_\*\[role\=option\]\>span\]\:items-center [role="option"] > span {
  align-items: center;
}

.\[\&_\*\[role\=option\]\>span\]\:gap-2 [role="option"] > span {
  gap: .5rem;
}

.\[\&_\*\[role\=option\]\]\:pe-8 [role="option"] {
  padding-inline-end: 2rem;
}

.\[\&_\*\[role\=option\]\]\:ps-2 [role="option"] {
  padding-inline-start: .5rem;
}

.\[\&_code\]\:whitespace-pre-wrap code {
  white-space: pre-wrap;
}

.\[\&_code\]\:break-words code {
  overflow-wrap: break-word;
}

.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}

.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
  color: hsl(var(--muted-foreground));
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}


/*# sourceMappingURL=src_c73c6610._.css.map*/