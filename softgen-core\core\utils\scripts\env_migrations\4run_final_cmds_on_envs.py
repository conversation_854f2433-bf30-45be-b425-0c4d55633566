from daytona import Daytona, DaytonaConfig
import csv
import re
import base64
import time
import json
import asyncio
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from typing import List

# Initialize the Daytona client
config = DaytonaConfig(
    api_key="dtn_858727beec575bf6d960c089fc4e4e397008e0e6383bd205082ed5de724e1f63",
    server_url="https://daytona.work/api",
    target="us"
)
daytona = Daytona(config)

def execute_in_tmux(workspace, command, session_name, wait_for_completion=False):
    try:
        # Create new tmux session
        workspace.process.exec(f"tmux new-session -d -s {session_name}")
        
        # Create a done flag file
        if wait_for_completion:
            flag_file = f"/tmp/{session_name}.done"
            # The command will be executed and then touch the flag file when done
            full_command = f"{command} && touch {flag_file} || touch {flag_file}.error"
        else:
            full_command = command
            
        # Send command
        workspace.process.exec(f"tmux send-keys -t {session_name} '{full_command}' Enter")
        
        if wait_for_completion:
            # Wait for the flag file to appear (indicating command completion)
            max_wait = 600  # Maximum wait time in seconds
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                # Check for error flag
                error_check = workspace.process.exec(f"test -f {flag_file}.error")
                if error_check.code == 0:
                    workspace.process.exec(f"rm -f {flag_file}.error")
                    raise Exception(f"Command failed in session {session_name}")
                
                # Check for success flag
                done_check = workspace.process.exec(f"test -f {flag_file}")
                if done_check.code == 0:
                    # Clean up flag file
                    workspace.process.exec(f"rm -f {flag_file}")
                    break
                    
                time.sleep(2)
            else:
                raise Exception(f"Command timed out in session {session_name}")
        else:
            # If not waiting for completion, just sleep briefly
            workspace.process.exec("sleep 2")
        
        # Get output
        return workspace.process.exec(f"tmux capture-pane -t {session_name} -p")
        
    except Exception as e:
        print(f"Error in tmux execution: {str(e)}")
        return None

def create_env_file(workspace, contents):
    try:
        # Convert literal \n to actual newlines and handle other escapes
        processed_contents = contents.encode('utf-8').decode('unicode_escape')
        
        # Encode processed content to base64
        base64_content = base64.b64encode(processed_contents.encode('utf-8')).decode('utf-8')
        
        # Create the file using base64 in tmux
        create_cmd = f"echo {base64_content} | base64 -d > /app/.env.local"
        create_response = execute_in_tmux(workspace, create_cmd, "env-create-session")
        
        # Verify the file exists and has content
        verify_cmd = "cat /app/.env.local"
        verify_response = execute_in_tmux(workspace, verify_cmd, "env-verify-session")
        
        return verify_response
        
    except Exception as e:
        print(f"Error creating env file: {str(e)}")
        return None

def get_env_contents(workspace_id):
    # Extract just the number after any dash
    match = re.search(r'-(\d+)', workspace_id)
    if not match:
        return None
        
    number = match.group(1)
    search_id = f"preview-{number}"
    
    print(f"Looking for environment variables for {search_id}")  # Debug line
    
    # Read CSV and find matching entry
    with open('env-migration-marko/combined-env-data.csv', 'r') as file:
        csv_reader = csv.DictReader(file)
        for row in csv_reader:
            if row['env_id'] == search_id:
                print(f"Found matching entry: {row['env_id']}")  # Debug line
                return row['env_contents']  # This might be empty string ""
    return None  # Only return None if no matching entry found

def process_workspace(workspace_id):
    try:
        print(f"\nProcessing workspace: {workspace_id}")
        workspace = daytona.get(workspace_id)
        print(f"Current workspace ID: {workspace.id}")
        
        # Step 1: Run npm install (wait for completion)
        print("Starting npm install...")
        npm_response = execute_in_tmux(workspace, "cd /app && npm i", "npm-session", wait_for_completion=True)
        if npm_response:
            print("npm install completed:", npm_response.result)
        else:
            raise Exception("npm install failed")
        
        # Step 2: Remove existing .env.local if it exists
        remove_response = execute_in_tmux(workspace, "cd /app && rm -f .env.local", "remove-session")
        print("Removing existing .env.local:", remove_response.result)
        
        # Step 3: Create/update .env.local
        env_contents = get_env_contents(workspace.id)
        if env_contents is not None:
            verify_response = create_env_file(workspace, env_contents)
            if verify_response:
                print("Verification of .env.local contents:", verify_response.result)
                print("Created .env.local file" + (" with environment variables" if env_contents else " (empty)"))
            else:
                print("Failed to create .env.local file")
        else:
            print("No matching entry found for workspace:", workspace.id)
        
        # Step 4: Start PM2 (only if npm install succeeded)
        pm2_response = execute_in_tmux(workspace, "cd /app && pm2 start", "pm2-session")
        print("PM2 start output:", pm2_response.result)
        
        return True
        
    except Exception as e:
        print(f"Error processing workspace {workspace_id}: {str(e)}")
        return False

async def process_workspace_batch(workspace_ids: List[str], timestamp: str, batch_num: int):
    successful_ids = []
    failed_ids = []
    
    # Process each workspace in the batch
    with ThreadPoolExecutor(max_workers=len(workspace_ids)) as executor:
        # Create tasks for each workspace
        futures = [
            executor.submit(process_workspace, workspace_id)
            for workspace_id in workspace_ids
        ]
        
        # Process results as they complete
        for workspace_id, future in zip(workspace_ids, futures):
            try:
                success = future.result()
                if success:
                    successful_ids.append(workspace_id)
                else:
                    failed_ids.append(workspace_id)
            except Exception as e:
                print(f"Error processing {workspace_id}: {str(e)}")
                failed_ids.append(workspace_id)
    
    # Save batch results
    batch_suffix = f"{timestamp}_batch{batch_num}"
    if successful_ids:
        with open(f'env-migration-marko/successful_ids_{batch_suffix}.json', 'w') as f:
            json.dump(successful_ids, f, indent=4)
    if failed_ids:
        with open(f'env-migration-marko/failed_ids_{batch_suffix}.json', 'w') as f:
            json.dump(failed_ids, f, indent=4)
    
    return successful_ids, failed_ids

async def main():
    # Create timestamp for file names
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # # Load workspace IDs from JSON file
    # with open('env-migration-marko/workspace_ids.json', 'r') as f:
    #     workspace_data = json.load(f)
    #     # Extract just the IDs from the workspaces
    #     all_workspace_ids = [workspace["id"] for workspace in workspace_data["single_workspaces"]]

   # Load workspace IDs from JSON file
    with open('env-migration-marko/new_workspaces.json', 'r') as f:
        workspace_data = json.load(f)
        # Extract just the IDs from the workspaces
        all_workspace_ids = [workspace["workspace_id"] for workspace in workspace_data["successful"]]

    # with open('env-migration-marko/ID_to_fix.json', 'r') as f:
    #     all_workspace_ids = json.load(f)

    # # Load workspace IDs from JSON file
    # with open('env-migration-marko/4all_failed_ids_20250116_151049.json', 'r') as f:
    #     all_workspace_ids = json.load(f)

    
    batch_size = 100
    batches = [
        all_workspace_ids[i:i + batch_size]
        for i in range(0, len(all_workspace_ids), batch_size)
    ]
    
    print(f"Processing {len(all_workspace_ids)} workspaces in {len(batches)} batches")
    
    all_successful = []
    all_failed = []
    
    # Process batches
    for batch_num, batch in enumerate(batches, 1):
        print(f"\nProcessing batch {batch_num}/{len(batches)} ({len(batch)} workspaces)")
        
        # Add approval prompt
        while True:
            approval = input(f"\nDo you want to process batch {batch_num}/{len(batches)} with {len(batch)} workspaces? (y/n): ").lower()
            if approval in ['y', 'n']:
                break
            print("Please enter 'y' for yes or 'n' for no.")
        
        if approval == 'n':
            print(f"Skipping batch {batch_num} as per user request")
            continue
            
        successful, failed = await process_workspace_batch(batch, timestamp, batch_num)
        
        all_successful.extend(successful)
        all_failed.extend(failed)
        
        # Print batch summary
        print(f"\nBatch {batch_num} complete:")
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
    
    # Save final combined results
    with open(f'env-migration-marko/4all_successful_ids_{timestamp}.json', 'w') as f:
        json.dump(all_successful, f, indent=4)
    if all_failed:
        with open(f'env-migration-marko/4all_failed_ids_{timestamp}.json', 'w') as f:
            json.dump(all_failed, f, indent=4)
    
    # Print final summary
    print("\n=== Processing Complete ===")
    print(f"Total workspaces: {len(all_workspace_ids)}")
    print(f"Total successful: {len(all_successful)}")
    print(f"Total failed: {len(all_failed)}")
    
    # Print file locations
    print("\nFinal results saved to:")
    print(f"All successful IDs: env-migration-marko/4all_successful_ids_{timestamp}.json")
    if all_failed:
        print(f"All failed IDs: env-migration-marko/4all_failed_ids_{timestamp}.json")

if __name__ == "__main__":
    asyncio.run(main())

