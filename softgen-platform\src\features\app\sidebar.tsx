"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { RainbowButton } from "@/components/ui/rainbow-button";
import { ScrollArea } from "@/components/ui/scroll-area";
import Typography from "@/components/ui/typography";
import { useSidebarNavigation } from "@/hooks/use-sidebar-navigation";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { AuthState } from "@/types";
import Link from "next/link";
import Logo from "../global/logo";
import { SettingsAction } from "./type";

const Sidebar = ({
  setSettingsAction,
}: {
  setSettingsAction: (action: SettingsAction) => void;
}) => {
  const { user: authState } = useAuth();
  const { navItems, isNavItemActive } = useSidebarNavigation(authState);

  return (
    <nav>
      <aside className="hidden h-screen w-[250px] flex-shrink-0 flex-col justify-between border-r bg-background py-4 text-primary md:flex">
        <div className="flex h-[30px] items-center gap-2 px-4">
          <div className="px-2">
            <Logo />
          </div>
        </div>

        <ScrollArea className="relative mb-auto flex w-full flex-col">
          <nav className="my-8 flex-1 px-4">
            <ul className="flex flex-col gap-3">
              {navItems.map((item) => (
                <li key={`${item.url}-${item.title}`}>
                  {item.items && item.items.length > 0 ? (
                    <Collapsible>
                      <CollapsibleTrigger className="w-full rounded-md outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2">
                        <div
                          className={cn(
                            "group flex h-8 w-full items-center justify-between gap-2 rounded-md px-2 text-sm outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2",
                            isNavItemActive(item.url ?? "")
                              ? "bg-background text-primary"
                              : "text-primary/80 hover:bg-background hover:text-primary",
                          )}
                        >
                          <div className="flex items-center gap-2">
                            {item.icon}
                            {item.title}
                          </div>
                          <div className="flex items-center">
                            {item.projectLimit ? (
                              <Typography.P className="text-xs">
                                {item.items.length} / {item.projectLimit}
                              </Typography.P>
                            ) : (
                              <svg
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="text-primary/70"
                              >
                                <path
                                  d="M6 9l6 6 6-6"
                                  stroke="currentColor"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            )}
                          </div>
                        </div>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="transform-gpu duration-300 animate-in fade-in-0">
                        <ul className="ml-3 mt-2 flex flex-col gap-2">
                          {item.items.map((subItem) => (
                            <li key={`${subItem.url}-${subItem.title}`}>
                              <Link
                                className={cn(
                                  "group flex h-7 items-center gap-2 rounded-md px-2 text-xs outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2",
                                  isNavItemActive(subItem.url ?? "")
                                    ? "bg-background text-primary"
                                    : "text-primary/80 hover:bg-background hover:text-primary",
                                )}
                                href={subItem.url ?? ""}
                              >
                                {subItem.icon}
                                <span className="max-w-40 truncate">{subItem.title}</span>
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </CollapsibleContent>
                    </Collapsible>
                  ) : item.modal ? (
                    <Button
                      variant="ghost"
                      className={cn(
                        "group flex h-8 w-full items-center justify-between gap-2 rounded-md px-2 text-sm outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2",
                        "border-none text-primary/80 hover:bg-background hover:text-primary",
                      )}
                      onClick={() => setSettingsAction("profile")}
                    >
                      <div className="flex items-center gap-2">
                        {item.icon}
                        {item.title}
                      </div>
                      {item.projectLimit && (
                        <Typography.P className="text-xs">
                          {item.items?.length} / {item.projectLimit}
                        </Typography.P>
                      )}
                    </Button>
                  ) : (
                    <Link
                      className={cn(
                        "group flex h-8 items-center justify-between gap-2 rounded-md px-2 text-sm outline-none focus-within:ring-2 focus-within:ring-primary/20 focus-within:ring-offset-2",
                        isNavItemActive(item.url ?? "")
                          ? "bg-background text-primary"
                          : "text-primary/80 hover:bg-background hover:text-primary",
                      )}
                      href={item.url ?? ""}
                    >
                      <div className="flex items-center gap-2">
                        {item.icon}
                        {item.title}
                      </div>
                      {item.projectLimit && (
                        <Typography.P className="text-xs">
                          {item.items?.length} / {item.projectLimit}
                        </Typography.P>
                      )}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </nav>

          <div className="absolute inset-0 top-0 h-10 bg-gradient-to-b from-background to-transparent" />
        </ScrollArea>

        {authState.userFromDb && (
          <div className="flex flex-col gap-5 px-4">
            {authState.wholesalePlan ? (
              <WholesaleCTA authState={authState} />
            ) : authState.userFromDb && authState.userFromDb?.free_total_token === 0 ? (
              <div className="relative rounded-xl border p-4 shadow-sm">
                <h3 className="text-base font-medium">Token Limit Reached</h3>
                <Typography.P className="mt-1 text-sm text-muted-foreground">
                  You have reached your free tier limit. Please upgrade your plan to continue using
                  our services.
                </Typography.P>

                <div className="w-full rounded-2xl border bg-foreground/10 p-0.5">
                  <Button
                    className="h-fit w-full py-2 text-sm"
                    onClick={() => setSettingsAction("purchase")}
                  >
                    Upgrade Now
                  </Button>
                </div>
              </div>
            ) : (
              <RainbowButton
                className="relative rounded-lg"
                buttonClassName="h-10 w-full rounded-lg text-sm"
                asChild
              >
                <Link href="/pricing" target="_blank">
                  Upgrade Now
                </Link>
              </RainbowButton>
            )}
          </div>
        )}
      </aside>
    </nav>
  );
};

function WholesaleCTA({ authState }: { authState: AuthState }) {
  if (authState.wholesalePlan === "free") {
    return (
      <RainbowButton
        className="relative rounded-lg"
        buttonClassName="h-10 w-full rounded-lg text-sm"
        asChild
      >
        <Link href="/pricing/wholesale" target="_blank">
          Subscribe to wholesale plan
        </Link>
      </RainbowButton>
    );
  }

  if (
    authState.userFromDb?.wallet_balance != null &&
    authState.userFromDb?.wallet_balance <= 0.01
  ) {
    return (
      <div className="relative rounded-xl border p-4 shadow-sm">
        <h3 className="text-base font-medium">Not enough balance</h3>
        <Typography.P className="mt-1 text-sm text-muted-foreground">
          Please top up your wallet to continue using our services.
        </Typography.P>

        <div className="w-full rounded-2xl border bg-foreground/10 p-0.5">
          <Button className="h-fit w-full py-2 text-sm">
            <Link href="/wallet">Top up wallet</Link>
          </Button>
        </div>
      </div>
    );
  }

  return null;
}

export default Sidebar;
