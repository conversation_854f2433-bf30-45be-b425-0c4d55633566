import {
  <PERSON>ertD<PERSON>og,
  AlertDialog<PERSON>ontent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loading from "@/components/ui/loading";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Skeleton } from "@/components/ui/skeleton";
import Typography from "@/components/ui/typography";
import { getGithubInfo, revertGithubCommit } from "@/lib/api";
import { formatTimestamp } from "@/lib/format-timestamp";
import { cn } from "@/lib/utils";
import { useAuth } from "@/providers/auth-provider";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ChevronDown, ChevronUp, Clock, Crown } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import TaskStatus from "../global/task-status";
import { errorToast, successToast } from "../global/toast";

type Props = {
  id: string;
  refreshIframe?: () => void;
  restartServer?: () => Promise<void>;
  isActive?: boolean;
};

const revertFormSchema = z.object({
  revertType: z.enum(["standard", "full"]),
  confirmText: z.string().optional(),
});

type RevertFormValues = z.infer<typeof revertFormSchema>;

const ThreadHistory = ({ id, refreshIframe, restartServer, isActive }: Props) => {
  const [expandedCommits, setExpandedCommits] = useState<Record<string, boolean>>({});
  const [confirmRevert, setConfirmRevert] = useState<string | null>(null);
  const { user } = useAuth();

  const form = useForm<RevertFormValues>({
    resolver: zodResolver(revertFormSchema),
    defaultValues: {
      revertType: "standard",
      confirmText: "",
    },
  });

  const revertType = form.watch("revertType");
  const confirmText = form.watch("confirmText");

  const { data: githubInfo, isLoading } = useQuery({
    queryKey: ["githubInfo", id],
    queryFn: async () => {
      try {
        return await getGithubInfo(id);
      } catch (error: unknown) {
        throw error;
      }
    },
    enabled: !!id && user.userFromDb?.plan !== "free-tier" && isActive,
    staleTime: 1000 * 60,
  });

  const revertCommitMutation = useMutation({
    mutationFn: async ({ commitHash, fullReset }: { commitHash: string; fullReset: boolean }) => {
      const result = await revertGithubCommit(id, commitHash, fullReset);
      if (restartServer) {
        await restartServer();
      }
      if (refreshIframe) {
        refreshIframe();
      }
      return result;
    },
    onSuccess: (_, variables) => {
      successToast(
        `Successfully ${variables.fullReset ? "reset" : "reverted"} commit and rebuilt project`,
      );
      setConfirmRevert(null);
      form.reset({
        revertType: "standard",
        confirmText: "",
      });

      window.location.reload();
    },
    onError: (error, variables) => {
      console.error("Revert error:", error);
      errorToast(
        `Failed to ${variables.fullReset ? "reset" : "revert"} commit${error instanceof Error ? `: ${error.message}` : ""}`,
      );
    },
  });

  const handleRevertCommit = (commitHash: string) => {
    setConfirmRevert(commitHash);
  };

  const confirmRevertCommit = async () => {
    const values = form.getValues();
    const revertAndFullReset = values.revertType === "full";
    if (revertAndFullReset && values.confirmText !== "sudo reset") {
      errorToast("Please type 'sudo reset' to proceed with full reset.");
      return;
    }

    if (confirmRevert) {
      revertCommitMutation.mutate({
        commitHash: confirmRevert,
        fullReset: revertAndFullReset,
      });
    }
  };

  const toggleCommitExpansion = (commitHash: string) => {
    setExpandedCommits((prev) => ({
      ...prev,
      [commitHash]: !prev[commitHash],
    }));
  };

  const formatCommitMessage = (message: string) => {
    const [title, ...description] = message.split("\n").filter((line) => line.trim() !== "");
    return { title, description: description.join("\n") };
  };

  const isFirebaseSetupCommit = (message: string) => {
    return message.toLowerCase().includes("firebase setup completed");
  };

  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  const isCommitRevertible = (commit: any, commits: any[]) => {
    const firebaseSetupCommit = commits.find((c) => isFirebaseSetupCommit(c.message));
    if (!firebaseSetupCommit) return true;

    return new Date(commit.date) >= new Date(firebaseSetupCommit.date);
  };

  if (user.userFromDb?.plan === "free-tier") {
    return (
      <div className="flex h-full w-full flex-1 flex-col items-center justify-center overflow-y-auto">
        <Card
          className={cn(
            "flex h-full w-full flex-col items-center justify-center rounded-none border-none bg-transparent shadow-none",
          )}
          border={false}
        >
          <CardContent className="flex w-full max-w-xl flex-col items-center justify-center space-y-6 p-6">
            <div className="flex size-16 items-center justify-center rounded-2xl border-2 border-dashed border-border p-2">
              <Crown className="size-8 text-muted-foreground" />
            </div>
            <div className="flex items-center gap-4">
              <div className="space-y-1.5 text-center">
                <Typography.H4>You are on the free tier.</Typography.H4>
                <Typography.P className="mt-0 text-balance leading-normal">
                  Please upgrade to the pro tier to view your version history.
                </Typography.P>
              </div>
            </div>

            <Button asChild className="w-full max-w-xs" variant="default" size="lg">
              <Link href="/pricing" target="_blank">
                Upgrade
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading || !githubInfo) {
    return (
      <div className="h-full w-full space-y-2 p-3 pt-1">
        {[1, 2, 3].map((i) => (
          <Card border={false} key={i} className="bg-transparent p-4">
            <div className="flex h-full flex-col">
              <div className="flex-grow space-y-2">
                <div className="flex items-center justify-between gap-2">
                  <Skeleton className="h-6 w-3/4 py-0" />
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-6 w-10 py-0" />
                    <Skeleton className="h-6 w-8 rounded-md py-0" />
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="flex h-full w-full flex-1 flex-col overflow-y-auto p-3 pt-1">
      <div className="flex-1 overflow-y-auto pb-8">
        <div className="space-y-2">
          {
            //eslint-disable-next-line @typescript-eslint/no-explicit-any
            githubInfo.commits.map((commit: any) => {
              const { title, description } = formatCommitMessage(commit.message);
              const canRevert = isCommitRevertible(commit, githubInfo.commits);
              return (
                <Card
                  key={commit.hash}
                  border={false}
                  className="mb-2 cursor-pointer overflow-hidden border-primary/10 bg-background/90 p-4 transition-all duration-200 hover:bg-accent/15"
                >
                  <div className="flex h-full flex-col">
                    <div className="flex-grow space-y-1.5">
                      <div className="flex items-center justify-between gap-2">
                        <h3 className="line-clamp-2 text-sm font-medium text-foreground/90">
                          {title}
                        </h3>
                        <span className="flex-shrink-0 rounded-md bg-muted px-2 py-1 text-xs font-medium text-muted-foreground">
                          {commit.hash.substring(0, 7)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center text-muted-foreground/80">
                          <Clock className="mr-1 h-3 w-3" />
                          <span>{formatTimestamp(commit.date, "commit-history")}</span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRevertCommit(commit.hash);
                          }}
                          disabled={!canRevert}
                          title={
                            !canRevert
                              ? "Cannot revert before Firebase setup"
                              : "Revert to this commit"
                          }
                          className="h-7 bg-background text-sm hover:bg-accent/50"
                        >
                          Revert
                        </Button>
                      </div>
                      {description && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleCommitExpansion(commit.hash);
                            }}
                            className="mt-1 h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
                          >
                            {expandedCommits[commit.hash] ? (
                              <>
                                <ChevronUp className="mr-1 h-3 w-3" />
                                Hide details
                              </>
                            ) : (
                              <>
                                <ChevronDown className="mr-1 h-3 w-3" />
                                Show details
                              </>
                            )}
                          </Button>
                          {expandedCommits[commit.hash] && (
                            <p className="mt-2 whitespace-pre-wrap text-xs text-muted-foreground">
                              {description}
                            </p>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </Card>
              );
            })
          }
        </div>
      </div>

      <AlertDialog open={!!confirmRevert} onOpenChange={(open) => !open && setConfirmRevert(null)}>
        <AlertDialogContent className="lg:max-w-xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Revert</AlertDialogTitle>
            <AlertDialogDescription>
              Choose how you want to revert to this commit:
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="">
            <RadioGroup
              value={revertType}
              onValueChange={(value) => form.setValue("revertType", value as "standard" | "full")}
              className="space-y-4"
            >
              <Card
                className={cn(
                  "border-none bg-transparent shadow-none",
                  revertType === "standard" && "ring-2 ring-primary/20",
                )}
              >
                <CardContent className="pt-6">
                  <div className="flex cursor-pointer items-center space-x-2">
                    <RadioGroupItem value="standard" id="standard" />
                    <Label htmlFor="standard" className="cursor-pointer text-lg font-semibold">
                      Standard Revert
                    </Label>
                  </div>
                  <Typography.P className="mt-2 pl-6 text-sm text-muted-foreground">
                    This option will:
                    <ul className="mt-1 list-inside list-disc space-y-1">
                      <li>Create a new commit that undoes the changes</li>
                      <li>Keep the entire version history</li>
                      <li>Can be undone by reverting the revert commit</li>
                    </ul>
                  </Typography.P>
                  <TaskStatus variant="warning" className="mt-3 flex items-center text-sm">
                    This may lead to a confused project context as potentially conflicting commits
                    will remain.
                  </TaskStatus>
                </CardContent>
              </Card>
              <Card
                border={false}
                className={cn(
                  "rounded-xl border border-red-900/50 bg-transparent hover:border-red-900",
                  revertType === "full" && "ring-2 ring-red-900/30",
                )}
              >
                <CardContent className="pt-6">
                  <div className="flex cursor-pointer items-center space-x-2">
                    <RadioGroupItem value="full" id="full" />
                    <Label htmlFor="full" className="cursor-pointer text-lg font-semibold">
                      Full Reset (Caution)
                    </Label>
                  </div>
                  <Typography.P className="mt-2 pl-6 text-sm text-muted-foreground">
                    This option will:
                    <ul className="mt-1 list-inside list-disc space-y-1">
                      <li>Clear all subsequent version history</li>
                      <li>Reset the project entirely to the selected commit state</li>
                      <li>Remove all commits after the selected one</li>
                    </ul>
                  </Typography.P>

                  <TaskStatus variant="error" className="mt-3 flex items-center text-sm">
                    This action cannot be undone. You&apos;ll lose all work after this commit.
                  </TaskStatus>
                </CardContent>
                {revertType === "full" && (
                  <CardFooter className="flex flex-col items-start gap-2 bg-transparent pt-4">
                    <p className="mt-0 text-sm font-normal text-primary/80">
                      Type <p className="inline font-bold text-primary">sudo reset</p> to proceed:
                    </p>
                    <Input
                      id="confirmText"
                      value={confirmText}
                      onChange={(e) => form.setValue("confirmText", e.target.value)}
                      placeholder="Type sudo reset here"
                      className="w-full border-red-900/50"
                    />
                  </CardFooter>
                )}
              </Card>
            </RadioGroup>
          </div>
          <AlertDialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end">
            <Button
              variant="outline"
              className="w-full sm:w-fit"
              onClick={() => setConfirmRevert(null)}
              disabled={revertCommitMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              className={cn("w-full sm:w-fit")}
              variant={revertType === "full" ? "destructive" : "default"}
              onClick={confirmRevertCommit}
              disabled={
                revertCommitMutation.isPending ||
                (revertType === "full" && confirmText !== "sudo reset")
              }
            >
              {revertCommitMutation.isPending ? (
                <>
                  <Loading className="size-4 animate-spin text-background" />
                  Reverting...
                </>
              ) : revertType === "full" ? (
                "Confirm Full Reset"
              ) : (
                "Confirm Standard Revert"
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ThreadHistory;
