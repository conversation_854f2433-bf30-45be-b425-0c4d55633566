{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-lg px-3 py-1 text-xs font-medium transition-all focus:outline-none cursor-pointer \",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border border-primary/10 bg-primary text-primary-foreground\",\r\n        secondary: \"border border-secondary/10 bg-secondary text-secondary-foreground\",\r\n        destructive:\r\n          \"border-transparent bg-red-100 border border-red-600/30 text-red-800   dark:bg-red-900/30 dark:text-red-400\",\r\n        success:\r\n          \"border-transparent bg-emerald-200 border border-emerald-400 text-emerald-800 dark:border-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-500\",\r\n        badgeSuccess:\r\n          \"border-transparent bg-emerald-200   text-emerald-800   dark:bg-emerald-900/50 dark:text-emerald-500 disabled:border-alpha-300 focus-visible:ring-offset-background outline-hidden has-focus-visible:ring-2 pointer-events-none inline-flex shrink-0 cursor-pointer items-center justify-center gap-1.5 whitespace-nowrap rounded-full   ring-blue-600 transition-all focus-visible:ring-2 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-400 disabled:ring-0 [&>svg]:pointer-events-none   bg-teal-100 text-teal-700 hover:bg-teal-100  focus:bg-teal-100   focus-visible:bg-teal-100 has-[>svg]:pl-[10px] [&>svg]:size-3 h-5 px-1.5 text-[11px] font-medium\",\r\n        update:\r\n          \"border-transparent bg-blue-200 border border-blue-400 text-blue-800 dark:border-blue-800 dark:bg-blue-900/50 dark:text-blue-500\",\r\n        warning:\r\n          // \"border-transparent bg-yellow-100 border border-yellow-600/40  text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400\",\r\n          \"border-transparent bg-yellow-200 border border-yellow-400 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-500\",\r\n        // info: \"border-transparent bg-neutral-100 border border-neutral-600/40 text-muted-foreground dark:bg-neutral-900/30\",\r\n        info: \"border-transparent bg-neutral-200 border border-neutral-400 text-neutral-800 dark:border-neutral-800 dark:bg-neutral-900/50 dark:text-neutral-500\",\r\n        terminal:\r\n          // \"border-transparent bg-purple-100 border border-purple-600/40 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400\",\r\n          \"border-transparent bg-purple-200 border border-purple-400 text-purple-800 dark:border-purple-800 dark:bg-purple-900/50 dark:text-purple-500\",\r\n        reset:\r\n          // \"border-transparent bg-amber-100 border border-amber-600/40   text-amber-600 dark:bg-amber-900/30\",\r\n          \"border-transparent bg-amber-200 border border-amber-400 text-amber-800 dark:border-amber-800 dark:bg-amber-900/50 dark:text-amber-500\",\r\n        outline: \"bg-background text-foreground border border-foreground/20\",\r\n        opened:\r\n          \"flex-shrink-0 bg-neutral-100 border border-neutral-600/40 text-muted-foreground dark:bg-neutral-900/30\",\r\n        closed:\r\n          \"flex-shrink-0 bg-neutral-100 border border-neutral-600/40 text-muted-foreground dark:bg-neutral-900/30\",\r\n        loading:\r\n          \"border-transparent bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300\",\r\n        passed:\r\n          \"flex-shrink-0 rounded-lg px-3 py-1 text-xs font-medium bg-emerald-200 border border-emerald-400 text-emerald-800 dark:border-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-500\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return <div className={cn(badgeVariants({ variant }), className)} {...props} />;\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAGA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,uHACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aACE;YACF,SACE;YACF,cACE;YACF,QACE;YACF,SACE,8HAA8H;YAC9H;YACF,uHAAuH;YACvH,MAAM;YACN,UACE,6HAA6H;YAC7H;YACF,OACE,sGAAsG;YACtG;YACF,SAAS;YACT,QACE;YACF,QACE;YACF,SACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBAAO,6VAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAC7E", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/billing.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { errorToast } from \"@/features/global/toast\";\r\nimport { createCustomerPortalSession } from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useState } from \"react\";\r\n\r\nconst SettingsBilling = () => {\r\n  const { user } = useAuth();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const createCustomerPortal = async () => {\r\n    if (user.userFromDb?.stripe_customer_id) {\r\n      setIsLoading(true);\r\n      const customerPortalSession = await createCustomerPortalSession(\r\n        user.userFromDb.stripe_customer_id,\r\n      );\r\n      window.open(customerPortalSession.url, \"_blank\");\r\n      setIsLoading(false);\r\n    } else {\r\n      errorToast(\"Failed to create customer portal session. Please try again.\");\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <div className=\"flex items-center justify-between gap-1 space-y-1\">\r\n        <div className=\"flex flex-col gap-0\">\r\n          <Typography.H5>Check Stripe Billing</Typography.H5>\r\n          <Typography.P>Check your Stripe billing information.</Typography.P>\r\n        </div>\r\n\r\n        <Button size=\"sm\" className=\"h-8\" disabled={isLoading} onClick={createCustomerPortal}>\r\n          {isLoading ? \"Navigating to Stripe...\" : \"Check Stripe Billing\"}\r\n        </Button>\r\n      </div>\r\n\r\n      {/* <Button onClick={createCustomerPortal} disabled={isLoading}>\r\n        {isLoading ? \"Navigating to Stripe...\" : \"Check Stripe Billing\"}\r\n      </Button> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsBilling;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,kBAAkB;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,uBAAuB;QAC3B,IAAI,KAAK,UAAU,EAAE,oBAAoB;YACvC,aAAa;YACb,MAAM,wBAAwB,MAAM,CAAA,GAAA,iHAAA,CAAA,8BAA2B,AAAD,EAC5D,KAAK,UAAU,CAAC,kBAAkB;YAEpC,OAAO,IAAI,CAAC,sBAAsB,GAAG,EAAE;YACvC,aAAa;QACf,OAAO;YACL,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX,aAAa;QACf;IACF;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sCAAC;;;;;;sCACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;sCAAC;;;;;;;;;;;;8BAGhB,6VAAC,kIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,WAAU;oBAAM,UAAU;oBAAW,SAAS;8BAC7D,YAAY,4BAA4B;;;;;;;;;;;;;;;;;AASnD;uCAEe", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-sidebar-accent\", className)}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-sidebar-ring transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n));\r\nProgress.displayName = ProgressPrimitive.Root.displayName;\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6VAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;QACnF,GAAG,KAAK;kBAET,cAAA,6VAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/settings/purchase.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { tokenPackageCheckout } from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { Coins } from \"lucide-react\";\r\nimport { memo, useCallback, useMemo, useState } from \"react\";\r\nimport { z } from \"zod\";\r\nimport { errorToast } from \"../../global/toast\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nconst tokenSchema = z.object({\r\n  size: z.string(),\r\n  tokens: z.string(),\r\n  price: z.number().positive(),\r\n});\r\n\r\ntype TokenPackage = z.infer<typeof tokenSchema>;\r\n\r\nconst tokenPackages: TokenPackage[] = [\r\n  { size: \"2.5M\", tokens: \"2.5M\", price: 25 },\r\n  { size: \"4M\", tokens: \"4M\", price: 40 },\r\n  { size: \"6M\", tokens: \"6M\", price: 60 },\r\n  { size: \"8M\", tokens: \"8M\", price: 80 },\r\n  { size: \"10M\", tokens: \"10M\", price: 100 },\r\n  { size: \"12M\", tokens: \"12M\", price: 120 },\r\n  { size: \"15M\", tokens: \"15M\", price: 150 },\r\n  { size: \"20M\", tokens: \"20M\", price: 200 },\r\n  { size: \"25M\", tokens: \"25M\", price: 250 },\r\n  { size: \"30M\", tokens: \"30M\", price: 300 },\r\n] as const;\r\n\r\nconst SelectedPackageDisplay = memo(({ selectedPackage }: { selectedPackage: TokenPackage }) => (\r\n  <div className=\"rounded-lg bg-gradient-to-br from-primary/50 via-transparent to-transparent p-[1px]\">\r\n    <div className=\"rounded-lg bg-background p-4\">\r\n      <div className=\"mb-2 flex items-center justify-between\">\r\n        <span className=\"text-sm font-medium\">Selected Package</span>\r\n        <Coins className=\"h-5 w-5 text-primary\" />\r\n      </div>\r\n      <div className=\"flex flex-col space-y-1\">\r\n        <div className=\"flex justify-between\">\r\n          <span className=\"text-sm text-muted-foreground\">Tokens:</span>\r\n          <span className=\"font-medium\">{selectedPackage.tokens}</span>\r\n        </div>\r\n        <div className=\"flex justify-between\">\r\n          <span className=\"text-sm text-muted-foreground\">Price:</span>\r\n          <span className=\"font-medium text-primary\">${selectedPackage.price}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n));\r\n\r\nSelectedPackageDisplay.displayName = \"SelectedPackageDisplay\";\r\n\r\nconst TokenUsageBar = memo(\r\n  ({ remainingTokens, planToken }: { remainingTokens: string; planToken: number }) => {\r\n    const progressValue = useMemo(\r\n      () =>\r\n        Number(planToken) > 0\r\n          ? Math.min(100, (Number(remainingTokens) / Number(planToken)) * 100)\r\n          : 0,\r\n      [remainingTokens, planToken],\r\n    );\r\n\r\n    return (\r\n      <div className=\"flex flex-col gap-2 rounded-xl bg-sidebar-ring/10 p-3 px-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <span className=\"text-sm font-medium\">Token Usage</span>\r\n        </div>\r\n\r\n        <Progress value={progressValue} className=\"w-full\" />\r\n\r\n        <div className=\"mt-1 flex justify-between text-xs text-muted-foreground\">\r\n          <Typography.Muted>\r\n            Remaining:{\" \"}\r\n            <span className=\"font-medium text-foreground\">\r\n              {Number(remainingTokens).toLocaleString()}\r\n            </span>\r\n          </Typography.Muted>\r\n          <Typography.Muted>\r\n            Total:{\" \"}\r\n            <span className=\"font-medium text-foreground\">\r\n              {Number(planToken).toLocaleString()}\r\n            </span>\r\n          </Typography.Muted>\r\n        </div>\r\n      </div>\r\n    );\r\n  },\r\n);\r\n\r\nTokenUsageBar.displayName = \"TokenUsageBar\";\r\n\r\nconst SettingsPurchase = () => {\r\n  const { user } = useAuth();\r\n  const { planToken, remainingTokens } = useTokenInfo();\r\n  const [selectedPackage, setSelectedPackage] = useState<TokenPackage>(tokenPackages[2]);\r\n\r\n  const purchaseMutation = useMutation({\r\n    mutationFn: async () => {\r\n      if (!user.kinde_id) throw new Error(\"User not found\");\r\n      const response = await tokenPackageCheckout(selectedPackage.size, user.kinde_id);\r\n      if (!response?.url) throw new Error(\"Failed to initiate checkout\");\r\n      return response.url;\r\n    },\r\n    onSuccess: (url) => {\r\n      window.location.href = url;\r\n    },\r\n    onError: () => {\r\n      errorToast(\"Failed to initiate checkout. Please try again.\");\r\n    },\r\n  });\r\n\r\n  const handleValueChange = useCallback((value: string) => {\r\n    const selected = tokenPackages.find((pkg) => pkg.size === value);\r\n    if (selected) setSelectedPackage(selected);\r\n  }, []);\r\n\r\n  const handlePurchase = useCallback(() => {\r\n    purchaseMutation.mutate();\r\n  }, [purchaseMutation]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <TokenUsageBar remainingTokens={remainingTokens.toString()} planToken={planToken} />\r\n\r\n      <div className=\"grid gap-4\">\r\n        <div className=\"space-y-4\">\r\n          <Select\r\n            value={selectedPackage.size}\r\n            onValueChange={handleValueChange}\r\n            disabled={purchaseMutation.isPending}\r\n          >\r\n            <SelectTrigger>\r\n              <SelectValue placeholder=\"Select package size\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {tokenPackages.map((pkg) => (\r\n                <SelectItem key={pkg.size} value={pkg.size} disabled={purchaseMutation.isPending}>\r\n                  {pkg.tokens} tokens - ${pkg.price}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n\r\n          {selectedPackage && <SelectedPackageDisplay selectedPackage={selectedPackage} />}\r\n        </div>\r\n        <Button\r\n          onClick={handlePurchase}\r\n          disabled={purchaseMutation.isPending}\r\n          className=\"h-9 w-fit justify-end\"\r\n        >\r\n          {purchaseMutation.isPending ? (\r\n            <>\r\n              <Loading className=\"size-4 animate-spin text-background\" />\r\n              Purchasing...\r\n            </>\r\n          ) : (\r\n            `Purchase ${selectedPackage.tokens} Tokens for $${selectedPackage.price}`\r\n          )}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport function useTokenInfo() {\r\n  const { user } = useAuth();\r\n\r\n  const getPlanTokenLimit = useCallback((): number => {\r\n    const plan = user?.userFromDb?.plan?.toLowerCase() || \"launch\";\r\n    switch (plan) {\r\n      case \"launch\":\r\n        return 3_000_000;\r\n      case \"elite\":\r\n        return 7_000_000;\r\n      case \"business\":\r\n        return 16_000_000;\r\n      case \"entry\":\r\n        return 2_500_000;\r\n      case \"boost\":\r\n        return 6_000_000;\r\n      case \"fly\":\r\n        return 14_000_000;\r\n      case \"pro_enterprise\":\r\n        return 30_000_000;\r\n      case \"elite_enterprise\":\r\n        return 85_000_000;\r\n      case \"free-tier\":\r\n        return 50_000;\r\n      default:\r\n        return 3_000_000;\r\n    }\r\n  }, [user?.userFromDb?.plan]);\r\n\r\n  const getRemainingTokens = useCallback((): number => {\r\n    return user?.userFromDb?.free_total_token || 0;\r\n  }, [user?.userFromDb?.free_total_token]);\r\n\r\n  return useMemo(\r\n    () => ({\r\n      remainingTokens: getRemainingTokens(),\r\n      planToken: getPlanTokenLimit(),\r\n    }),\r\n    [getRemainingTokens, getPlanTokenLimit],\r\n  );\r\n}\r\n\r\nexport default memo(SettingsPurchase);\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;;AAEA,6DAA6D;AAC7D,MAAM,cAAc,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,MAAM,mOAAA,CAAA,IAAC,CAAC,MAAM;IACd,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM;IAChB,OAAO,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAIA,MAAM,gBAAgC;IACpC;QAAE,MAAM;QAAQ,QAAQ;QAAQ,OAAO;IAAG;IAC1C;QAAE,MAAM;QAAM,QAAQ;QAAM,OAAO;IAAG;IACtC;QAAE,MAAM;QAAM,QAAQ;QAAM,OAAO;IAAG;IACtC;QAAE,MAAM;QAAM,QAAQ;QAAM,OAAO;IAAG;IACtC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;IACzC;QAAE,MAAM;QAAO,QAAQ;QAAO,OAAO;IAAI;CAC1C;AAED,MAAM,uCAAyB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,eAAe,EAAqC,iBACzF,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAK,WAAU;sCAAsB;;;;;;sCACtC,6VAAC,wRAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;8BAEnB,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,6VAAC;oCAAK,WAAU;8CAAe,gBAAgB,MAAM;;;;;;;;;;;;sCAEvD,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,6VAAC;oCAAK,WAAU;;wCAA2B;wCAAE,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5E,uBAAuB,WAAW,GAAG;AAErC,MAAM,8BAAgB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACvB,CAAC,EAAE,eAAe,EAAE,SAAS,EAAkD;IAC7E,MAAM,gBAAgB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAC1B,IACE,OAAO,aAAa,IAChB,KAAK,GAAG,CAAC,KAAK,AAAC,OAAO,mBAAmB,OAAO,aAAc,OAC9D,GACN;QAAC;QAAiB;KAAU;IAG9B,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAK,WAAU;8BAAsB;;;;;;;;;;;0BAGxC,6VAAC,oIAAA,CAAA,WAAQ;gBAAC,OAAO;gBAAe,WAAU;;;;;;0BAE1C,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,KAAK;;4BAAC;4BACL;0CACX,6VAAC;gCAAK,WAAU;0CACb,OAAO,iBAAiB,cAAc;;;;;;;;;;;;kCAG3C,6VAAC,sIAAA,CAAA,UAAU,CAAC,KAAK;;4BAAC;4BACT;0CACP,6VAAC;gCAAK,WAAU;0CACb,OAAO,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAM7C;AAGF,cAAc,WAAW,GAAG;AAE5B,MAAM,mBAAmB;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAgB,aAAa,CAAC,EAAE;IAErF,MAAM,mBAAmB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY;YACV,IAAI,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAI,MAAM;YACpC,MAAM,WAAW,MAAM,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,IAAI,EAAE,KAAK,QAAQ;YAC/E,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,MAAM;YACpC,OAAO,SAAS,GAAG;QACrB;QACA,WAAW,CAAC;YACV,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QACA,SAAS;YACP,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;QACb;IACF;IAEA,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,MAAM,WAAW,cAAc,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;QAC1D,IAAI,UAAU,mBAAmB;IACnC,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACjC,iBAAiB,MAAM;IACzB,GAAG;QAAC;KAAiB;IAErB,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAc,iBAAiB,gBAAgB,QAAQ;gBAAI,WAAW;;;;;;0BAEvE,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,gBAAgB,IAAI;gCAC3B,eAAe;gCACf,UAAU,iBAAiB,SAAS;;kDAEpC,6VAAC,kIAAA,CAAA,gBAAa;kDACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6VAAC,kIAAA,CAAA,gBAAa;kDACX,cAAc,GAAG,CAAC,CAAC,oBAClB,6VAAC,kIAAA,CAAA,aAAU;gDAAgB,OAAO,IAAI,IAAI;gDAAE,UAAU,iBAAiB,SAAS;;oDAC7E,IAAI,MAAM;oDAAC;oDAAY,IAAI,KAAK;;+CADlB,IAAI,IAAI;;;;;;;;;;;;;;;;4BAO9B,iCAAmB,6VAAC;gCAAuB,iBAAiB;;;;;;;;;;;;kCAE/D,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,iBAAiB,SAAS;wBACpC,WAAU;kCAET,iBAAiB,SAAS,iBACzB;;8CACE,6VAAC,mIAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAwC;;2CAI7D,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,aAAa,EAAE,gBAAgB,KAAK,EAAE;;;;;;;;;;;;;;;;;;AAMrF;AAEO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACpC,MAAM,OAAO,MAAM,YAAY,MAAM,iBAAiB;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF,GAAG;QAAC,MAAM,YAAY;KAAK;IAE3B,MAAM,qBAAqB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,MAAM,YAAY,oBAAoB;IAC/C,GAAG;QAAC,MAAM,YAAY;KAAiB;IAEvC,OAAO,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EACX,IAAM,CAAC;YACL,iBAAiB;YACjB,WAAW;QACb,CAAC,GACD;QAAC;QAAoB;KAAkB;AAE3C;qDAEe,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/thread/thread-message/softgen-icon.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst SoftgenIcon = ({ className }: { className?: string }) => {\r\n  return (\r\n    <div className={cn(\"flex w-fit items-center justify-center\", className)}>\r\n      <div className=\"h-auto w-fit\">\r\n        <svg\r\n          width=\"80\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 482 109\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          className=\"text-primary\"\r\n        >\r\n          <path\r\n            d=\"M156.645 81.4936C150.337 81.4936 145.103 80.0845 140.943 77.2661C136.782 74.4477 134.232 70.1866 133.293 64.4827L142.956 62.1676C143.493 64.9189 144.399 67.0998 145.674 68.7103C146.949 70.3208 148.526 71.4616 150.405 72.1326C152.351 72.8036 154.431 73.1392 156.645 73.1392C159.933 73.1392 162.45 72.5017 164.194 71.2267C166.006 69.9517 166.912 68.3077 166.912 66.2945C166.912 64.2814 166.073 62.8051 164.396 61.8657C162.718 60.9262 160.235 60.1545 156.947 59.5506L153.525 58.9466C150.17 58.3427 147.116 57.4703 144.365 56.3296C141.614 55.1888 139.433 53.6118 137.823 51.5987C136.212 49.5856 135.407 47.0021 135.407 43.8482C135.407 39.1509 137.151 35.5272 140.641 32.9773C144.13 30.3602 148.76 29.0517 154.531 29.0517C160.101 29.0517 164.664 30.3267 168.221 32.8766C171.844 35.3595 174.193 38.7482 175.267 43.0429L165.604 45.7606C165 42.7409 163.725 40.6272 161.779 39.4193C159.833 38.1443 157.417 37.5068 154.531 37.5068C151.713 37.5068 149.499 38.0436 147.888 39.1173C146.278 40.1239 145.472 41.5666 145.472 43.4455C145.472 45.4587 146.244 46.935 147.787 47.8744C149.398 48.8139 151.545 49.5185 154.229 49.9882L157.752 50.5921C161.309 51.1961 164.564 52.0349 167.516 53.1086C170.469 54.1822 172.784 55.7256 174.461 57.7387C176.206 59.7519 177.078 62.436 177.078 65.7912C177.078 70.757 175.233 74.6155 171.542 77.3667C167.852 80.118 162.886 81.4936 156.645 81.4936Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M207.415 81.4936C202.45 81.4936 198.021 80.4871 194.129 78.474C190.304 76.3937 187.284 73.4411 185.07 69.6162C182.855 65.7913 181.748 61.2617 181.748 56.0276V54.5177C181.748 49.2836 182.855 44.7876 185.07 41.0298C187.284 37.2048 190.304 34.2523 194.129 32.172C198.021 30.0918 202.45 29.0517 207.415 29.0517C212.381 29.0517 216.81 30.0918 220.702 32.172C224.594 34.2523 227.647 37.2048 229.862 41.0298C232.076 44.7876 233.183 49.2836 233.183 54.5177V56.0276C233.183 61.2617 232.076 65.7913 229.862 69.6162C227.647 73.4411 224.594 76.3937 220.702 78.474C216.81 80.4871 212.381 81.4936 207.415 81.4936ZM207.415 72.2333C211.978 72.2333 215.669 70.7905 218.488 67.905C221.373 64.9524 222.816 60.8926 222.816 55.7256V54.8197C222.816 49.6527 221.407 45.6264 218.588 42.7409C215.77 39.7884 212.046 38.3121 207.415 38.3121C202.919 38.3121 199.229 39.7884 196.343 42.7409C193.525 45.6264 192.116 49.6527 192.116 54.8197V55.7256C192.116 60.8926 193.525 64.9524 196.343 67.905C199.229 70.7905 202.919 72.2333 207.415 72.2333Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M248.149 80.0845V39.218H234.258V30.4609H248.149V19.59C248.149 16.5703 249.055 14.1545 250.867 12.3427C252.678 10.5309 255.094 9.625 258.114 9.625H270.897V18.3821H261.436C259.49 18.3821 258.517 19.3887 258.517 21.4018V30.4609H272.81V39.218H258.517V80.0845H248.149Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M295.735 80.0845C292.715 80.0845 290.3 79.1785 288.488 77.3667C286.743 75.5549 285.871 73.1392 285.871 70.1195V39.218H272.181V30.4609H285.871V14.0539H296.238V30.4609H311.035V39.218H296.238V68.3077C296.238 70.3208 297.178 71.3274 299.057 71.3274H309.424V80.0845H295.735Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M313.997 55.5243V54.0145C313.997 48.7803 315.037 44.3179 317.117 40.6272C319.265 36.9364 322.083 34.0845 325.573 32.0714C329.062 30.0582 332.887 29.0517 337.047 29.0517C341.879 29.0517 345.57 29.9576 348.12 31.7694C350.737 33.5812 352.649 35.5272 353.857 37.6075H355.467V30.4609H365.533V90.2507C365.533 93.2704 364.627 95.6862 362.815 97.498C361.071 99.3098 358.655 100.216 355.568 100.216H322.15V91.1567H352.347C354.293 91.1567 355.266 90.1501 355.266 88.137V72.3339H353.656C352.918 73.5418 351.877 74.7832 350.535 76.0582C349.193 77.3332 347.415 78.3733 345.201 79.1785C343.053 79.9838 340.335 80.3864 337.047 80.3864C332.887 80.3864 329.028 79.4134 325.472 77.4674C321.982 75.4543 319.198 72.6023 317.117 68.9116C315.037 65.1538 313.997 60.6913 313.997 55.5243ZM339.866 71.3274C344.362 71.3274 348.052 69.9182 350.938 67.0998C353.891 64.2143 355.367 60.2552 355.367 55.2223V54.3164C355.367 49.1494 353.924 45.1903 351.039 42.439C348.153 39.6206 344.429 38.2114 339.866 38.2114C335.437 38.2114 331.746 39.6206 328.794 42.439C325.908 45.1903 324.465 49.1494 324.465 54.3164V55.2223C324.465 60.2552 325.908 64.2143 328.794 67.0998C331.746 69.9182 335.437 71.3274 339.866 71.3274Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M398.513 81.4936C393.48 81.4936 389.085 80.4535 385.327 78.3733C381.569 76.226 378.617 73.2398 376.469 69.4149C374.389 65.5228 373.349 61.0269 373.349 55.9269V54.7191C373.349 49.552 374.389 45.056 376.469 41.2311C378.549 37.339 381.435 34.3529 385.126 32.2727C388.884 30.1253 393.212 29.0517 398.11 29.0517C402.875 29.0517 407.035 30.1253 410.592 32.2727C414.215 34.3529 417.034 37.2719 419.047 41.0298C421.06 44.7876 422.067 49.183 422.067 54.2158V58.1414H383.918C384.052 62.5031 385.495 65.9926 388.246 68.6096C391.064 71.1596 394.554 72.4346 398.714 72.4346C402.606 72.4346 405.525 71.5622 407.471 69.8175C409.485 68.0728 411.028 66.0597 412.102 63.7781L420.657 68.207C419.718 70.0859 418.342 72.0655 416.53 74.1457C414.786 76.226 412.471 77.9707 409.585 79.3799C406.7 80.789 403.009 81.4936 398.513 81.4936ZM384.018 50.1895H411.498C411.229 46.4317 409.887 43.5126 407.471 41.4324C405.056 39.2851 401.902 38.2114 398.01 38.2114C394.118 38.2114 390.93 39.2851 388.447 41.4324C386.032 43.5126 384.555 46.4317 384.018 50.1895Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <path\r\n            d=\"M429.514 80.0845V30.4609H439.68V37.9094H441.29C442.23 35.8963 443.907 34.0174 446.323 32.2727C448.739 30.528 452.329 29.6556 457.093 29.6556C460.851 29.6556 464.173 30.4944 467.058 32.172C470.011 33.8496 472.326 36.2318 474.004 39.3186C475.681 42.3383 476.52 45.9955 476.52 50.2902V80.0845H466.153V51.0954C466.153 46.8008 465.079 43.6469 462.932 41.6337C460.784 39.5535 457.832 38.5134 454.074 38.5134C449.779 38.5134 446.323 39.9226 443.706 42.7409C441.156 45.5593 439.881 49.6527 439.881 55.021V80.0845H429.514Z\"\r\n            fill=\"currentColor\"\r\n          />\r\n          <g clipPath=\"url(#clip0_1503_289)\">\r\n            800%\r\n            <path\r\n              d=\"M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n            <path\r\n              d=\"M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n            <path\r\n              d=\"M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n            <path\r\n              d=\"M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z\"\r\n              fill=\"currentColor\"\r\n            />\r\n          </g>\r\n          <defs>\r\n            <clipPath id=\"clip0_1503_289\">\r\n              <rect width=\"109\" height=\"109\" fill=\"currentColor\" />\r\n            </clipPath>\r\n          </defs>\r\n        </svg>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SoftgenIcon;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,cAAc,CAAC,EAAE,SAAS,EAA0B;IACxD,qBACE,6VAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;kBAC3D,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;gBACN,WAAU;;kCAEV,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6VAAC;wBAAE,UAAS;;4BAAuB;0CAEjC,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;0CAEP,6VAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;;kCAGT,6VAAC;kCACC,cAAA,6VAAC;4BAAS,IAAG;sCACX,cAAA,6VAAC;gCAAK,OAAM;gCAAM,QAAO;gCAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD;uCAEe", "debugId": null}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport * as React from \"react\";\r\nimport {\r\n  Controller,\r\n  ControllerProps,\r\n  FieldPath,\r\n  FieldValues,\r\n  FormProvider,\r\n  useFormContext,\r\n} from \"react-hook-form\";\r\n\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState, formState } = useFormContext();\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\");\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nconst FormItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\r\n  ({ className, ...props }, ref) => {\r\n    const id = React.useId();\r\n\r\n    return (\r\n      <FormItemContext.Provider value={{ id }}>\r\n        <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n      </FormItemContext.Provider>\r\n    );\r\n  },\r\n);\r\nFormItem.displayName = \"FormItem\";\r\n\r\nconst FormLabel = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", \"text-base\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nFormLabel.displayName = \"FormLabel\";\r\n\r\nconst FormControl = React.forwardRef<\r\n  React.ElementRef<typeof Slot>,\r\n  React.ComponentPropsWithoutRef<typeof Slot>\r\n>(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nFormControl.displayName = \"FormControl\";\r\n\r\nconst FormDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-sm text-muted-foreground\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nFormDescription.displayName = \"FormDescription\";\r\n\r\nconst FormMessage = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message) : children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-sm font-medium text-destructive\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n});\r\nFormMessage.displayName = \"FormMessage\";\r\n\r\nexport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n  useFormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AACA;AASA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,uPAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6VAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6VAAC,uPAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uPAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,MAAM,yBAAW,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,MAAM,KAAK,CAAA,GAAA,oTAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6VAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6VAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AAEF,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6VAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB,aAAa;QACxD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6VAAC,oSAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6VAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW;IAE9C,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6VAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/modal/profile.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Form, FormControl, FormField, FormItem, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport * as z from \"zod\";\r\n\r\nconst profileFormSchema = z.object({\r\n  email: z.string().email(),\r\n});\r\n\r\ntype ProfileFormValues = z.infer<typeof profileFormSchema>;\r\n\r\nconst SettingsProfile = () => {\r\n  const { user } = useAuth();\r\n\r\n  const form = useForm<ProfileFormValues>({\r\n    resolver: zodResolver(profileFormSchema),\r\n    defaultValues: {\r\n      email: user.email || \"\",\r\n    },\r\n  });\r\n\r\n  const updateProfileMutation = useMutation({\r\n    mutationFn: async (values: ProfileFormValues) => {\r\n      debug(\"Updating profile:\", values);\r\n    },\r\n  });\r\n\r\n  function onSubmit(values: ProfileFormValues) {\r\n    updateProfileMutation.mutate(values);\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <div className=\"flex w-full flex-col gap-4 rounded-xl bg-sidebar-ring/10 p-3 px-4\">\r\n        <Label className=\"text-sm font-medium text-primary\">Email address</Label>\r\n\r\n        <Form {...form}>\r\n          <form\r\n            onSubmit={form.handleSubmit(onSubmit)}\r\n            className=\"flex w-full flex-col items-center justify-start gap-3 md:flex-row\"\r\n          >\r\n            <FormField\r\n              control={form.control}\r\n              name=\"email\"\r\n              render={({ field }) => (\r\n                <FormItem className=\"w-full\">\r\n                  <FormControl>\r\n                    <Input {...field} disabled className=\"w-full\" />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"ml-auto w-fit\"\r\n              size=\"sm\"\r\n              disabled={updateProfileMutation.isPending || !form.formState.isDirty}\r\n            >\r\n              {updateProfileMutation.isPending ? \"Saving...\" : \"Save Changes\"}\r\n            </Button>\r\n          </form>\r\n        </Form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsProfile;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAXA;;;;;;;;;;;;AAaA,MAAM,oBAAoB,CAAA,GAAA,mMAAA,CAAA,SAAQ,AAAD,EAAE;IACjC,OAAO,CAAA,GAAA,mMAAA,CAAA,SAAQ,AAAD,IAAI,KAAK;AACzB;AAIA,MAAM,kBAAkB;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAqB;QACtC,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,KAAK,KAAK,IAAI;QACvB;IACF;IAEA,MAAM,wBAAwB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,OAAO;YACjB,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,qBAAqB;QAC7B;IACF;IAEA,SAAS,SAAS,MAAyB;QACzC,sBAAsB,MAAM,CAAC;IAC/B;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAmC;;;;;;8BAEpD,6VAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,6VAAC;wBACC,UAAU,KAAK,YAAY,CAAC;wBAC5B,WAAU;;0CAEV,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oDAAE,GAAG,KAAK;oDAAE,QAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEvC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAKlB,6VAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,MAAK;gCACL,UAAU,sBAAsB,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC,OAAO;0CAEnE,sBAAsB,SAAS,GAAG,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/D;uCAEe", "debugId": null}}, {"offset": {"line": 1112, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/app/settings.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalTitle } from \"@/components/ui/modal\";\r\nimport SettingsBilling from \"@/features/project/settings/billing\";\r\nimport SettingsPurchase from \"@/features/project/settings/purchase\";\r\nimport SoftgenIcon from \"@/features/thread/thread-message/softgen-icon\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { CreditCardSolid, UserSolid } from \"@mynaui/icons-react\";\r\nimport Link from \"next/link\";\r\nimport { useMemo, useState } from \"react\";\r\nimport { FaMoneyBill } from \"react-icons/fa\";\r\nimport SettingsProfile from \"./modal/profile\";\r\n\r\ntype SettingsTab = \"billing\" | \"purchase\" | \"profile\";\r\n\r\ninterface SettingsModalProps {\r\n  tab?: SettingsTab;\r\n}\r\n\r\nexport default function SettingsModalContent({ tab }: SettingsModalProps) {\r\n  const { user } = useAuth();\r\n  const isFreeTier = useMemo(() => user?.userFromDb?.plan === \"free-tier\", [user]);\r\n\r\n  const [activeTab, setActiveTab] = useState<SettingsTab>(tab ?? \"profile\");\r\n\r\n  const handleTabChange = (tab: SettingsTab) => {\r\n    setActiveTab(tab);\r\n  };\r\n\r\n  const tabs: { id: SettingsTab; label: string; icon: React.ReactNode; disabled?: boolean }[] = [\r\n    { id: \"profile\", label: \"Profile\", icon: <UserSolid />, disabled: false },\r\n    { id: \"billing\", label: \"Billing\", icon: <CreditCardSolid />, disabled: isFreeTier },\r\n    { id: \"purchase\", label: \"Purchase\", icon: <FaMoneyBill />, disabled: false },\r\n  ];\r\n\r\n  return (\r\n    <ModalContentInner className=\"w-full lg:max-w-4xl\">\r\n      <div className=\"grid h-[600px] grid-cols-12 overflow-hidden\">\r\n        <div className=\"col-span-12 hidden flex-col border-r border-ring/15 bg-sidebar-ring/10 px-2 lg:col-span-3 lg:flex\">\r\n          <div className=\"flex flex-col gap-4 space-y-2 px-2 py-5 text-left\">\r\n            <SoftgenIcon />\r\n          </div>\r\n\r\n          <div className=\"flex flex-1 gap-1 md:flex-col\">\r\n            {tabs\r\n              .sort((a, b) => (a.disabled ? 1 : b.disabled ? -1 : 0))\r\n              .map((tab) => (\r\n                <Button\r\n                  key={tab.id}\r\n                  variant={activeTab === tab.id ? \"default\" : \"ghost\"}\r\n                  className=\"h-fit w-full justify-between rounded-md px-3 py-2\"\r\n                  onClick={() => {\r\n                    handleTabChange(tab.id);\r\n                  }}\r\n                  disabled={tab.disabled}\r\n                >\r\n                  <div className=\"flex items-center gap-2\">\r\n                    {tab.icon}\r\n                    {tab.label}\r\n                  </div>\r\n                  {tab.disabled && (\r\n                    <Badge variant=\"terminal\" className=\"px-2 py-0.5\">\r\n                      Pro\r\n                    </Badge>\r\n                  )}\r\n                </Button>\r\n              ))}\r\n          </div>\r\n\r\n          <ModalFooter className=\"-m-2 mb-0 flex flex-col items-start justify-start rounded-none border-t-0 bg-transparent py-0 dark:bg-transparent sm:flex-col sm:justify-start md:px-0 md:pr-3\">\r\n            {isFreeTier && (\r\n              <Button className=\"relative my-3 mb-2 h-10 w-full text-sm\">\r\n                <Link href=\"/pricing\" target=\"_blank\">\r\n                  Upgrade Now\r\n                </Link>\r\n              </Button>\r\n            )}\r\n          </ModalFooter>\r\n        </div>\r\n\r\n        <div className=\"col-span-12 hidden lg:col-span-9 lg:block\">\r\n          <ModalHeader>\r\n            <ModalTitle>\r\n              {tabs.find((tab) => tab.id === activeTab)?.label ||\r\n                activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}\r\n            </ModalTitle>\r\n          </ModalHeader>\r\n\r\n          <div className=\"p-4\">\r\n            {activeTab === \"billing\" && <SettingsBilling />}\r\n            {activeTab === \"purchase\" && <SettingsPurchase />}\r\n            {activeTab === \"profile\" && <SettingsProfile />}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"col-span-12 w-full lg:hidden\">\r\n          <ModalHeader>\r\n            <ModalTitle>\r\n              {tabs.find((tab) => tab.id === activeTab)?.label ||\r\n                activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}\r\n            </ModalTitle>\r\n          </ModalHeader>\r\n\r\n          <div className=\"flex grow flex-row flex-wrap gap-2 p-4 md:p-2\">\r\n            {tabs.map((tab) => (\r\n              <Button\r\n                key={tab.id}\r\n                variant={activeTab === tab.id ? \"default\" : \"ghost\"}\r\n                className=\"h-fit w-fit justify-between rounded-md px-3 py-2\"\r\n                onClick={() => {\r\n                  handleTabChange(tab.id);\r\n                }}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  {tab.icon}\r\n                  {tab.label}\r\n                </div>\r\n              </Button>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"p-4\">\r\n            {activeTab === \"billing\" && <SettingsBilling />}\r\n            {activeTab === \"purchase\" && <SettingsPurchase />}\r\n            {activeTab === \"profile\" && <SettingsProfile />}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ModalContentInner>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAqBe,SAAS,qBAAqB,EAAE,GAAG,EAAsB;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,YAAY,SAAS,aAAa;QAAC;KAAK;IAE/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAe,OAAO;IAE/D,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,MAAM,OAAwF;QAC5F;YAAE,IAAI;YAAW,OAAO;YAAW,oBAAM,6VAAC,6TAAA,CAAA,YAAS;;;;;YAAK,UAAU;QAAM;QACxE;YAAE,IAAI;YAAW,OAAO;YAAW,oBAAM,6VAAC,yUAAA,CAAA,kBAAe;;;;;YAAK,UAAU;QAAW;QACnF;YAAE,IAAI;YAAY,OAAO;YAAY,oBAAM,6VAAC,+NAAA,CAAA,cAAW;;;;;YAAK,UAAU;QAAM;KAC7E;IAED,qBACE,6VAAC,iIAAA,CAAA,oBAAiB;QAAC,WAAU;kBAC3B,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,kKAAA,CAAA,UAAW;;;;;;;;;;sCAGd,6VAAC;4BAAI,WAAU;sCACZ,KACE,IAAI,CAAC,CAAC,GAAG,IAAO,EAAE,QAAQ,GAAG,IAAI,EAAE,QAAQ,GAAG,CAAC,IAAI,GACnD,GAAG,CAAC,CAAC,oBACJ,6VAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,cAAc,IAAI,EAAE,GAAG,YAAY;oCAC5C,WAAU;oCACV,SAAS;wCACP,gBAAgB,IAAI,EAAE;oCACxB;oCACA,UAAU,IAAI,QAAQ;;sDAEtB,6VAAC;4CAAI,WAAU;;gDACZ,IAAI,IAAI;gDACR,IAAI,KAAK;;;;;;;wCAEX,IAAI,QAAQ,kBACX,6VAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAW,WAAU;sDAAc;;;;;;;mCAb/C,IAAI,EAAE;;;;;;;;;;sCAqBnB,6VAAC,iIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,4BACC,6VAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;0CAChB,cAAA,6VAAC,2QAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,QAAO;8CAAS;;;;;;;;;;;;;;;;;;;;;;8BAQ9C,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,6VAAC,iIAAA,CAAA,aAAU;0CACR,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,YAAY,SACzC,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;;;;;;;;;;;sCAI1D,6VAAC;4BAAI,WAAU;;gCACZ,cAAc,2BAAa,6VAAC,kJAAA,CAAA,UAAe;;;;;gCAC3C,cAAc,4BAAc,6VAAC,mJAAA,CAAA,UAAgB;;;;;gCAC7C,cAAc,2BAAa,6VAAC,2IAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;8BAIhD,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,6VAAC,iIAAA,CAAA,aAAU;0CACR,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,YAAY,SACzC,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;;;;;;;;;;;sCAI1D,6VAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6VAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,cAAc,IAAI,EAAE,GAAG,YAAY;oCAC5C,WAAU;oCACV,SAAS;wCACP,gBAAgB,IAAI,EAAE;oCACxB;8CAEA,cAAA,6VAAC;wCAAI,WAAU;;4CACZ,IAAI,IAAI;4CACR,IAAI,KAAK;;;;;;;mCATP,IAAI,EAAE;;;;;;;;;;sCAejB,6VAAC;4BAAI,WAAU;;gCACZ,cAAc,2BAAa,6VAAC,kJAAA,CAAA,UAAe;;;;;gCAC3C,cAAc,4BAAc,6VAAC,mJAAA,CAAA,UAAgB;;;;;gCAC7C,cAAc,2BAAa,6VAAC,2IAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxD", "debugId": null}}]}