{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/components/add-own-vercel-token.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { Form, FormControl, FormField, FormItem, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport {\r\n  Modal,\r\n  ModalContent,\r\n  ModalDescription,\r\n  ModalFooter,\r\n  ModalHeader,\r\n  ModalTitle,\r\n} from \"@/components/ui/modal\";\r\nimport { useCallback } from \"react\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { publishProjectSchema } from \"../type\";\r\n\r\ntype Props = {\r\n  open: boolean;\r\n  setShowModal: (open: boolean) => void;\r\n  projectForm: UseFormReturn<z.infer<typeof publishProjectSchema>>;\r\n};\r\n\r\nconst AddOwnVercelTokenModal = ({ open, setShowModal, projectForm }: Props) => {\r\n  const handleAddOwnVercelToken = useCallback((data: z.infer<typeof publishProjectSchema>) => {\r\n    projectForm.setValue(\"vercelToken\", data.vercelToken);\r\n    setShowModal(false);\r\n  }, []);\r\n\r\n  const handleClose = useCallback(() => {\r\n    const token = projectForm.getValues(\"vercelToken\");\r\n    if (!token) {\r\n      projectForm.setValue(\"ownVercelDeployment\", false);\r\n    }\r\n    setShowModal(false);\r\n  }, [projectForm, setShowModal]);\r\n\r\n  return (\r\n    <Modal open={open} onOpenChange={handleClose}>\r\n      <ModalContent>\r\n        <ModalHeader className=\"space-y-1\">\r\n          <ModalTitle>Add Own Vercel Token</ModalTitle>\r\n          <ModalDescription>Enter your Vercel token</ModalDescription>\r\n        </ModalHeader>\r\n\r\n        <div className=\"p-6 py-4\">\r\n          <Form {...projectForm}>\r\n            <form\r\n              onSubmit={projectForm.handleSubmit(handleAddOwnVercelToken)}\r\n              className=\"space-y-4\"\r\n            >\r\n              <FormField\r\n                name=\"vercelToken\"\r\n                control={projectForm.control}\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <Label>Vercel Token</Label>\r\n                    <FormControl>\r\n                      <div className=\"flex w-full flex-col items-start gap-2\">\r\n                        <div className=\"flex w-full items-center gap-2\">\r\n                          <Input\r\n                            {...field}\r\n                            placeholder=\"Vercel Token\"\r\n                            disabled={projectForm.formState.isSubmitting}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            </form>\r\n          </Form>\r\n        </div>\r\n\r\n        <ModalFooter className=\"flex w-full justify-end px-6 pt-2\">\r\n          <Button\r\n            onClick={projectForm.handleSubmit(handleAddOwnVercelToken)}\r\n            disabled={projectForm.formState.isSubmitting}\r\n          >\r\n            {projectForm.formState.isSubmitting ? (\r\n              <>\r\n                <Loading />\r\n                <span>Adding...</span>\r\n              </>\r\n            ) : (\r\n              \"Save Changes\"\r\n            )}\r\n          </Button>\r\n        </ModalFooter>\r\n      </ModalContent>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default AddOwnVercelTokenModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAQA;;;;;;;;;AAWA,MAAM,yBAAyB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAS;IACxE,MAAM,0BAA0B,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,YAAY,QAAQ,CAAC,eAAe,KAAK,WAAW;QACpD,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,MAAM,QAAQ,YAAY,SAAS,CAAC;QACpC,IAAI,CAAC,OAAO;YACV,YAAY,QAAQ,CAAC,uBAAuB;QAC9C;QACA,aAAa;IACf,GAAG;QAAC;QAAa;KAAa;IAE9B,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;kBAC/B,cAAA,6VAAC,iIAAA,CAAA,eAAY;;8BACX,6VAAC,iIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC,iIAAA,CAAA,aAAU;sCAAC;;;;;;sCACZ,6VAAC,iIAAA,CAAA,mBAAgB;sCAAC;;;;;;;;;;;;8BAGpB,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,gIAAA,CAAA,OAAI;wBAAE,GAAG,WAAW;kCACnB,cAAA,6VAAC;4BACC,UAAU,YAAY,YAAY,CAAC;4BACnC,WAAU;sCAEV,cAAA,6VAAC,gIAAA,CAAA,YAAS;gCACR,MAAK;gCACL,SAAS,YAAY,OAAO;gCAC5B,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0DACP,6VAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC;oDAAI,WAAU;8DACb,cAAA,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC,iIAAA,CAAA,QAAK;4DACH,GAAG,KAAK;4DACT,aAAY;4DACZ,UAAU,YAAY,SAAS,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;0DAKpD,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQxB,6VAAC,iIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,YAAY,YAAY,CAAC;wBAClC,UAAU,YAAY,SAAS,CAAC,YAAY;kCAE3C,YAAY,SAAS,CAAC,YAAY,iBACjC;;8CACE,6VAAC,mIAAA,CAAA,UAAO;;;;;8CACR,6VAAC;8CAAK;;;;;;;2CAGR;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/type.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\nexport const publishProjectSchema = z.object({\r\n  ownVercelDeployment: z.boolean().default(false),\r\n  vercelToken: z\r\n    .string()\r\n    .optional()\r\n    .refine((val) => !val || val.length > 0, {\r\n      message: \"Vercel token is required when using custom token\",\r\n    }),\r\n  pushEnvBeforeDeploy: z.boolean().default(false),\r\n  domain: z.string().optional(),\r\n});\r\n\r\nexport const customDomainSchema = z.object({\r\n  domain: z.string().min(1, \"Custom domain is required\"),\r\n  vercelToken: z.string().optional(),\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,MAAM,uBAAuB,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,qBAAqB,mOAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACzC,aAAa,mOAAA,CAAA,IAAC,CACX,MAAM,GACN,QAAQ,GACR,MAAM,CAAC,CAAC,MAAQ,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG;QACvC,SAAS;IACX;IACF,qBAAqB,mOAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACzC,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B;AAEO,MAAM,qBAAqB,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,aAAa,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAClC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/components/deployment-success.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Modal, ModalContent } from \"@/components/ui/modal\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { BrandXSolid } from \"@mynaui/icons-react\";\r\nimport confetti from \"canvas-confetti\";\r\nimport { Link2 } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { memo, useEffect, useState } from \"react\";\r\nimport { IoLogoFacebook, IoLogoLinkedin } from \"react-icons/io5\";\r\n\r\nconst count = 50;\r\nconst defaults = {\r\n  origin: { y: 0.7 },\r\n};\r\n\r\nfunction fire(particleRatio: number, opts: confetti.Options) {\r\n  confetti({\r\n    ...defaults,\r\n    ...opts,\r\n    particleCount: Math.floor(count * particleRatio),\r\n  });\r\n}\r\n\r\nconst DeploymentSuccess = memo(\r\n  ({\r\n    deploymentUrl,\r\n    open,\r\n    onOpenChange,\r\n    customDomain,\r\n    project,\r\n  }: {\r\n    deploymentUrl: string;\r\n    open: boolean;\r\n    onOpenChange: (open: boolean) => void;\r\n    customDomain?: boolean;\r\n    project?: {\r\n      name: string;\r\n      preview_url?: string;\r\n    };\r\n  }) => {\r\n    const [httpDomain, setHttpDomain] = useState(\r\n      deploymentUrl.startsWith(\"http\") ? deploymentUrl : `https://${deploymentUrl}`,\r\n    );\r\n\r\n    useEffect(() => {\r\n      if (!deploymentUrl.startsWith(\"http\")) {\r\n        setHttpDomain(`https://${deploymentUrl}`);\r\n      } else {\r\n        setHttpDomain(deploymentUrl);\r\n      }\r\n    }, [deploymentUrl]);\r\n\r\n    useEffect(() => {\r\n      fire(0.25, {\r\n        spread: 26,\r\n        startVelocity: 55,\r\n      });\r\n      fire(0.2, {\r\n        spread: 60,\r\n      });\r\n      fire(0.35, {\r\n        spread: 100,\r\n        decay: 0.91,\r\n        scalar: 0.8,\r\n      });\r\n      fire(0.1, {\r\n        spread: 120,\r\n        startVelocity: 25,\r\n        decay: 0.92,\r\n        scalar: 1.2,\r\n      });\r\n      fire(0.1, {\r\n        spread: 120,\r\n        startVelocity: 45,\r\n      });\r\n    }, []);\r\n\r\n    const handleShare = async (platform: \"twitter\" | \"linkedin\" | \"facebook\") => {\r\n      if (!project?.name || !deploymentUrl) return;\r\n\r\n      let url: string;\r\n\r\n      const encodedUrl = encodeURIComponent(deploymentUrl);\r\n      const tweetText = `🚀 Just shipped ${project.name} with @softgenai!\\n\\nCheck it out here:\\n${deploymentUrl}\\n\\n#buildinpublic #softgenai`;\r\n      const encodedTweet = encodeURIComponent(tweetText);\r\n\r\n      switch (platform) {\r\n        case \"linkedin\":\r\n          url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;\r\n          break;\r\n        case \"twitter\":\r\n          url = `https://twitter.com/intent/tweet?text=${encodedTweet}`;\r\n          break;\r\n        case \"facebook\":\r\n          url = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;\r\n          break;\r\n        default:\r\n          return;\r\n      }\r\n\r\n      window.open(url, \"_blank\");\r\n    };\r\n\r\n    return (\r\n      <Modal open={open} onOpenChange={onOpenChange}>\r\n        <ModalContent className=\"rounded-2xl text-center lg:max-w-lg\">\r\n          <div className=\"relative flex flex-col gap-4 overflow-hidden p-6 pt-10 md:p-8\">\r\n            <div className=\"absolute -right-20 top-0 z-0 h-20 w-64 bg-purple-600/30 blur-[3rem] dark:bg-purple-600/10\" />\r\n            <div className=\"absolute right-10 top-0 z-0 h-20 w-80 bg-pink-600/30 blur-[3rem] dark:bg-pink-600/10\" />\r\n            <div className=\"flex w-full flex-col items-start\">\r\n              <div className=\"h-[70px] w-fit\">\r\n                <svg\r\n                  width=\"90\"\r\n                  height=\"90\"\r\n                  viewBox=\"0 0 120 120\"\r\n                  fill=\"none\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  className=\"text-primary\"\r\n                >\r\n                  <g clipPath=\"url(#clip0_1503_289)\">\r\n                    <path\r\n                      d=\"M21.7227 92.6837L24.7012 89.6003C27.1119 87.1022 30.4224 85.6697 33.8988 85.6086L75.6942 84.9011C79.1706 84.8399 82.5247 86.1676 85.0228 88.5784L88.1062 91.5656L71.7898 108.441L54.9144 92.1246L38.598 109L21.7227 92.6837Z\"\r\n                      fill=\"currentColor\"\r\n                    />\r\n                    <path\r\n                      d=\"M16.3164 21.7231L19.3997 24.7017C21.8978 27.1124 23.3303 30.4229 23.3915 33.8993L24.099 75.6947C24.1601 79.1711 22.8324 82.5252 20.4217 85.0233L17.4344 88.1067L0.550284 71.7903L16.8667 54.9149L0 38.5985L16.3164 21.7231Z\"\r\n                      fill=\"currentColor\"\r\n                    />\r\n                    <path\r\n                      d=\"M87.2781 16.3164L84.2995 19.3997C81.8888 21.8978 78.5783 23.3303 75.1019 23.3915L33.3065 24.099C29.8301 24.1601 26.476 22.8324 23.9779 20.4217L20.8945 17.4344L37.2109 0.550284L54.0863 16.8667L70.4027 0L87.2781 16.3164Z\"\r\n                      fill=\"currentColor\"\r\n                    />\r\n                    <path\r\n                      d=\"M92.6831 87.2768L89.5997 84.2983C87.1016 81.8875 85.6691 78.5771 85.608 75.1007L84.9005 33.3053C84.8393 29.8289 86.167 26.4748 88.5778 23.9767L91.565 20.8933L108.44 37.2097L92.124 54.0851L108.999 70.4014L92.6831 87.2768Z\"\r\n                      fill=\"currentColor\"\r\n                    />\r\n                  </g>\r\n                  <defs>\r\n                    <clipPath id=\"clip0_1503_289\">\r\n                      <rect width=\"109\" height=\"109\" fill=\"currentColor\" />\r\n                    </clipPath>\r\n                  </defs>\r\n                </svg>\r\n              </div>\r\n\r\n              <div className=\"mt-10 flex w-full flex-col items-start gap-6\">\r\n                <div className=\"flex flex-col text-left\">\r\n                  <Typography.H2>\r\n                    {customDomain ? \"Your domain is live!\" : \"Your project is live!\"}\r\n                  </Typography.H2>\r\n                  <Typography.P className=\"text-base\">\r\n                    {customDomain\r\n                      ? \"You can now share your domain with the world.\"\r\n                      : \"You can now share your project with the world.\"}\r\n                  </Typography.P>\r\n                </div>\r\n\r\n                <Card\r\n                  className={cn(\r\n                    \"w-full border-primary/5 shadow-none focus-visible:ring-primary/10\",\r\n                  )}\r\n                >\r\n                  <CardContent className=\"flex w-full items-center justify-center gap-2 p-0 text-sm shadow-none\">\r\n                    <div className=\"flex w-full items-center justify-between gap-2 p-2 px-2.5 pl-3\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Link2 className=\"size-5 text-primary/75\" />\r\n                        <Typography.P className=\"mt-0 max-w-72 truncate text-sm font-medium text-primary\">\r\n                          {httpDomain}\r\n                        </Typography.P>\r\n                      </div>\r\n\r\n                      <Button className=\"h-7\" asChild>\r\n                        <Link href={httpDomain} target=\"_blank\" className=\"text-primary\">\r\n                          Visit\r\n                        </Link>\r\n                      </Button>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n\r\n                <div className=\"flex flex-col gap-2\">\r\n                  <Typography.Muted className=\"text-base font-normal\">\r\n                    Share your project with the world.\r\n                  </Typography.Muted>\r\n\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <Button variant=\"secondary\" size=\"icon\" onClick={() => handleShare(\"twitter\")}>\r\n                      <BrandXSolid className=\"size-5 text-primary/75\" />\r\n                    </Button>\r\n\r\n                    <Button variant=\"secondary\" size=\"icon\" onClick={() => handleShare(\"linkedin\")}>\r\n                      <IoLogoLinkedin className=\"size-5 text-primary/75\" />\r\n                    </Button>\r\n\r\n                    <Button variant=\"secondary\" size=\"icon\" onClick={() => handleShare(\"facebook\")}>\r\n                      <IoLogoFacebook className=\"size-5 text-primary/75\" />\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ModalContent>\r\n      </Modal>\r\n    );\r\n  },\r\n);\r\n\r\nDeploymentSuccess.displayName = \"DeploymentSuccess\";\r\n\r\nexport default DeploymentSuccess;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAcA,MAAM,QAAQ;AACd,MAAM,WAAW;IACf,QAAQ;QAAE,GAAG;IAAI;AACnB;AAEA,SAAS,KAAK,aAAqB,EAAE,IAAsB;IACzD,CAAA,GAAA,gOAAA,CAAA,UAAQ,AAAD,EAAE;QACP,GAAG,QAAQ;QACX,GAAG,IAAI;QACP,eAAe,KAAK,KAAK,CAAC,QAAQ;IACpC;AACF;AAEA,MAAM,kCAAoB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAC3B,CAAC,EACC,aAAa,EACb,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,OAAO,EAUR;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EACzC,cAAc,UAAU,CAAC,UAAU,gBAAgB,CAAC,QAAQ,EAAE,eAAe;IAG/E,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAc,UAAU,CAAC,SAAS;YACrC,cAAc,CAAC,QAAQ,EAAE,eAAe;QAC1C,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,KAAK,MAAM;YACT,QAAQ;YACR,eAAe;QACjB;QACA,KAAK,KAAK;YACR,QAAQ;QACV;QACA,KAAK,MAAM;YACT,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QACA,KAAK,KAAK;YACR,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;QACV;QACA,KAAK,KAAK;YACR,QAAQ;YACR,eAAe;QACjB;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,SAAS,QAAQ,CAAC,eAAe;QAEtC,IAAI;QAEJ,MAAM,aAAa,mBAAmB;QACtC,MAAM,YAAY,CAAC,gBAAgB,EAAE,QAAQ,IAAI,CAAC,yCAAyC,EAAE,cAAc,6BAA6B,CAAC;QACzI,MAAM,eAAe,mBAAmB;QAExC,OAAQ;YACN,KAAK;gBACH,MAAM,CAAC,oDAAoD,EAAE,YAAY;gBACzE;YACF,KAAK;gBACH,MAAM,CAAC,sCAAsC,EAAE,cAAc;gBAC7D;YACF,KAAK;gBACH,MAAM,CAAC,6CAA6C,EAAE,YAAY;gBAClE;YACF;gBACE;QACJ;QAEA,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;kBAC/B,cAAA,6VAAC,iIAAA,CAAA,eAAY;YAAC,WAAU;sBACtB,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;oCACN,WAAU;;sDAEV,6VAAC;4CAAE,UAAS;;8DACV,6VAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6VAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6VAAC;oDACC,GAAE;oDACF,MAAK;;;;;;8DAEP,6VAAC;oDACC,GAAE;oDACF,MAAK;;;;;;;;;;;;sDAGT,6VAAC;sDACC,cAAA,6VAAC;gDAAS,IAAG;0DACX,cAAA,6VAAC;oDAAK,OAAM;oDAAM,QAAO;oDAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM5C,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;0DACX,eAAe,yBAAyB;;;;;;0DAE3C,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gDAAC,WAAU;0DACrB,eACG,kDACA;;;;;;;;;;;;kDAIR,6VAAC,gIAAA,CAAA,OAAI;wCACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kDAGF,cAAA,6VAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,4RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gEAAC,WAAU;0EACrB;;;;;;;;;;;;kEAIL,6VAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;wDAAM,OAAO;kEAC7B,cAAA,6VAAC,2QAAA,CAAA,UAAI;4DAAC,MAAM;4DAAY,QAAO;4DAAS,WAAU;sEAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQzE,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,KAAK;gDAAC,WAAU;0DAAwB;;;;;;0DAIpD,6VAAC;gDAAI,WAAU;;kEACb,6VAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAY,MAAK;wDAAO,SAAS,IAAM,YAAY;kEACjE,cAAA,6VAAC,iUAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAGzB,6VAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAY,MAAK;wDAAO,SAAS,IAAM,YAAY;kEACjE,cAAA,6VAAC,gOAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;kEAG5B,6VAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAY,MAAK;wDAAO,SAAS,IAAM,YAAY;kEACjE,cAAA,6VAAC,gOAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C;AAGF,kBAAkB,WAAW,GAAG;uCAEjB", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/components/custom-domain-modal.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Form, FormControl, FormField, FormItem, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Modal,\r\n  ModalContent,\r\n  ModalDescription,\r\n  <PERSON>dal<PERSON>ooter,\r\n  ModalHeader,\r\n  ModalTitle,\r\n} from \"@/components/ui/modal\";\r\nimport TaskStatus from \"@/features/global/task-status\";\r\nimport { errorToast, successToast } from \"@/features/global/toast\";\r\nimport { vercelConnectDomain } from \"@/lib/api\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { DangerTriangleSolid } from \"@mynaui/icons-react\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport { useCallback, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { customDomainSchema } from \"../type\";\r\nimport DeploymentSuccess from \"./deployment-success\";\r\n\r\ntype Props = {\r\n  open: boolean;\r\n  setShowModal: (open: boolean) => void;\r\n  invalidateQueries: () => void;\r\n};\r\n\r\nconst CustomDomainModal = ({ open, setShowModal, invalidateQueries }: Props) => {\r\n  const { project } = useProject();\r\n  const [isDomainConnected, setIsDomainConnected] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const customDomainForm = useForm<z.infer<typeof customDomainSchema>>({\r\n    resolver: zodResolver(customDomainSchema),\r\n    defaultValues: {\r\n      domain: project?.deployment?.production_url || \"\",\r\n      vercelToken: project?.deployment?.vercel_token || \"\",\r\n    },\r\n  });\r\n\r\n  const connectDomainMutation = useMutation({\r\n    mutationFn: async ({\r\n      projectId,\r\n      domain,\r\n      token,\r\n    }: {\r\n      projectId: string;\r\n      domain: string;\r\n      token: string | null;\r\n    }) => {\r\n      return vercelConnectDomain(projectId, domain, token || null);\r\n    },\r\n    onSuccess: (response: { success: boolean; message: string; domain: string }) => {\r\n      invalidateQueries();\r\n\r\n      if (response.success) {\r\n        successToast(`Domain successfully connected to project`);\r\n        customDomainForm.setValue(\"domain\", response.domain);\r\n        setIsDomainConnected(true);\r\n      }\r\n    },\r\n    onError: (error: { detail?: string }) => {\r\n      const domain = customDomainForm.getValues(\"domain\");\r\n      const errorMessage = error.detail || `Failed to connect domain ${domain}`;\r\n      errorToast(errorMessage);\r\n      console.error(\"Connect domain error\", error);\r\n      setError(errorMessage);\r\n      connectDomainMutation.reset();\r\n    },\r\n  });\r\n\r\n  const handleDomainConnect = useCallback(async () => {\r\n    if (!project?.project_id) {\r\n      errorToast(\"Project ID is required\");\r\n      return;\r\n    }\r\n\r\n    const domainValue = customDomainForm.getValues(\"domain\")?.trim();\r\n    if (!domainValue) {\r\n      errorToast(\"Please enter a domain name.\");\r\n      return;\r\n    }\r\n\r\n    const vercelTokenValue = customDomainForm.getValues(\"vercelToken\");\r\n\r\n    connectDomainMutation.mutate({\r\n      projectId: project.project_id,\r\n      domain: domainValue,\r\n      token: vercelTokenValue || null,\r\n    });\r\n  }, [project?.project_id, customDomainForm, connectDomainMutation]);\r\n\r\n  return (\r\n    <Modal open={open} onOpenChange={setShowModal}>\r\n      <ModalContent>\r\n        <ModalHeader className=\"space-y-1\">\r\n          <ModalTitle>Connect Domain</ModalTitle>\r\n          <ModalDescription>Enter your custom domain</ModalDescription>\r\n        </ModalHeader>\r\n\r\n        <div className=\"p-6 py-4\">\r\n          <Form {...customDomainForm}>\r\n            <form\r\n              onSubmit={customDomainForm.handleSubmit(handleDomainConnect)}\r\n              className=\"space-y-4\"\r\n            >\r\n              <FormField\r\n                name=\"domain\"\r\n                control={customDomainForm.control}\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <Label>Your Domain</Label>\r\n                    <FormControl>\r\n                      <div className=\"flex w-full flex-col items-start gap-2\">\r\n                        <div className=\"flex w-full items-center gap-2\">\r\n                          <Input\r\n                            {...field}\r\n                            placeholder=\"yourdomain.com\"\r\n                            disabled={connectDomainMutation.isPending}\r\n                          />\r\n                        </div>\r\n\r\n                        {error && (\r\n                          <TaskStatus\r\n                            variant=\"error\"\r\n                            className=\"flex w-full cursor-default items-center justify-start text-base font-medium text-primary\"\r\n                          >\r\n                            <DangerTriangleSolid className=\"size-5\" />\r\n                            {error.includes(\"it's already assigned to another project\")\r\n                              ? \"This domain is already assigned to another project\"\r\n                              : error}\r\n                          </TaskStatus>\r\n                        )}\r\n                      </div>\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            </form>\r\n          </Form>\r\n        </div>\r\n\r\n        <ModalFooter className=\"w-full px-6 pt-2\">\r\n          <Button\r\n            onClick={handleDomainConnect}\r\n            disabled={\r\n              connectDomainMutation.isPending ||\r\n              connectDomainMutation.isSuccess ||\r\n              connectDomainMutation.isError\r\n            }\r\n            className=\"w-full\"\r\n          >\r\n            {connectDomainMutation.isPending ||\r\n            connectDomainMutation.isSuccess ||\r\n            connectDomainMutation.isError\r\n              ? \"Connecting...\"\r\n              : \"Connect Domain\"}\r\n          </Button>\r\n        </ModalFooter>\r\n      </ModalContent>\r\n\r\n      {isDomainConnected && (\r\n        <DeploymentSuccess\r\n          deploymentUrl={customDomainForm.getValues(\"domain\")}\r\n          open={isDomainConnected}\r\n          customDomain={true}\r\n          onOpenChange={setIsDomainConnected}\r\n        />\r\n      )}\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default CustomDomainModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;AAQA,MAAM,oBAAoB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,iBAAiB,EAAS;IACzE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAsC;QACnE,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE,6IAAA,CAAA,qBAAkB;QACxC,eAAe;YACb,QAAQ,SAAS,YAAY,kBAAkB;YAC/C,aAAa,SAAS,YAAY,gBAAgB;QACpD;IACF;IAEA,MAAM,wBAAwB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACxC,YAAY,OAAO,EACjB,SAAS,EACT,MAAM,EACN,KAAK,EAKN;YACC,OAAO,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,QAAQ,SAAS;QACzD;QACA,WAAW,CAAC;YACV;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,CAAC,wCAAwC,CAAC;gBACvD,iBAAiB,QAAQ,CAAC,UAAU,SAAS,MAAM;gBACnD,qBAAqB;YACvB;QACF;QACA,SAAS,CAAC;YACR,MAAM,SAAS,iBAAiB,SAAS,CAAC;YAC1C,MAAM,eAAe,MAAM,MAAM,IAAI,CAAC,yBAAyB,EAAE,QAAQ;YACzE,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;YACT,sBAAsB,KAAK;QAC7B;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,SAAS,YAAY;YACxB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX;QACF;QAEA,MAAM,cAAc,iBAAiB,SAAS,CAAC,WAAW;QAC1D,IAAI,CAAC,aAAa;YAChB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACX;QACF;QAEA,MAAM,mBAAmB,iBAAiB,SAAS,CAAC;QAEpD,sBAAsB,MAAM,CAAC;YAC3B,WAAW,QAAQ,UAAU;YAC7B,QAAQ;YACR,OAAO,oBAAoB;QAC7B;IACF,GAAG;QAAC,SAAS;QAAY;QAAkB;KAAsB;IAEjE,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;;0BAC/B,6VAAC,iIAAA,CAAA,eAAY;;kCACX,6VAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6VAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAGpB,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,gIAAA,CAAA,OAAI;4BAAE,GAAG,gBAAgB;sCACxB,cAAA,6VAAC;gCACC,UAAU,iBAAiB,YAAY,CAAC;gCACxC,WAAU;0CAEV,cAAA,6VAAC,gIAAA,CAAA,YAAS;oCACR,MAAK;oCACL,SAAS,iBAAiB,OAAO;oCACjC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;8DACP,6VAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6VAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;0EACb,cAAA,6VAAC,iIAAA,CAAA,QAAK;oEACH,GAAG,KAAK;oEACT,aAAY;oEACZ,UAAU,sBAAsB,SAAS;;;;;;;;;;;4DAI5C,uBACC,6VAAC,4IAAA,CAAA,UAAU;gEACT,SAAQ;gEACR,WAAU;;kFAEV,6VAAC,iVAAA,CAAA,sBAAmB;wEAAC,WAAU;;;;;;oEAC9B,MAAM,QAAQ,CAAC,8CACZ,uDACA;;;;;;;;;;;;;;;;;;8DAKZ,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQxB,6VAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UACE,sBAAsB,SAAS,IAC/B,sBAAsB,SAAS,IAC/B,sBAAsB,OAAO;4BAE/B,WAAU;sCAET,sBAAsB,SAAS,IAChC,sBAAsB,SAAS,IAC/B,sBAAsB,OAAO,GACzB,kBACA;;;;;;;;;;;;;;;;;YAKT,mCACC,6VAAC,6KAAA,CAAA,UAAiB;gBAChB,eAAe,iBAAiB,SAAS,CAAC;gBAC1C,MAAM;gBACN,cAAc;gBACd,cAAc;;;;;;;;;;;;AAKxB;uCAEe", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/components/deployed-project-view.tsx"], "sourcesContent": ["import {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Disclosure, DisclosureContent, DisclosureTrigger } from \"@/components/ui/disclosure\";\r\nimport { Form, FormField, FormItem } from \"@/components/ui/form\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { LazyModal, Modal, ModalContent, ModalHeader, ModalTitle } from \"@/components/ui/modal\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { errorToast, successToast } from \"@/features/global/toast\";\r\nimport { vercelDomainStatus } from \"@/lib/api\";\r\nimport { formatTimestamp } from \"@/lib/format-timestamp\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { CalendarSolid, ChevronDown, Globe } from \"@mynaui/icons-react\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport Link from \"next/link\";\r\nimport { memo, useState } from \"react\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { publishProjectSchema } from \"../type\";\r\nimport AddOwnVercelTokenModal from \"./add-own-vercel-token\";\r\nimport CustomDomainModal from \"./custom-domain-modal\";\r\n\r\nconst ensureHttps = (url: string | null): string => {\r\n  if (!url) return \"#\";\r\n  return url.startsWith(\"http\") ? url : `https://${url}`;\r\n};\r\n\r\nconst getDisplayUrl = (url: string | null): string => {\r\n  if (!url) return \"\";\r\n  return url.replace(/^https?:\\/\\//, \"\");\r\n};\r\n\r\nexport const DeployedProjectView = memo(\r\n  ({\r\n    form,\r\n    onUnlink,\r\n    onRedeploy,\r\n    invalidateQueries,\r\n    setStartDeployment,\r\n    isRemovingDomain,\r\n    open,\r\n    onOpenChange,\r\n    isRedeploying,\r\n    setIsRedeploying,\r\n    setIsRedeploy,\r\n    vercelToken,\r\n  }: {\r\n    form: UseFormReturn<z.infer<typeof publishProjectSchema>>;\r\n    onUnlink: () => void;\r\n    onRedeploy: () => void;\r\n    invalidateQueries: () => void;\r\n    setStartDeployment: (startDeployment: boolean) => void;\r\n    isRemovingDomain: boolean;\r\n    open: boolean;\r\n    onOpenChange: (open: boolean) => void;\r\n    isRedeploying: boolean;\r\n    setIsRedeploying: (isRedeploying: boolean) => void;\r\n    setIsRedeploy: (isRedeploy: boolean) => void;\r\n    vercelToken: string | null;\r\n  }) => {\r\n    const { project } = useProject();\r\n    const [showCustomDomainForm, setShowCustomDomainForm] = useState(false);\r\n    const [showAddOwnVercelTokenForm, setShowAddOwnVercelTokenForm] = useState(false);\r\n    const [alertRemoveDomain, setAlertRemoveDomain] = useState(false);\r\n    const [showDomainConfiguration, setShowDomainConfiguration] = useState(true);\r\n\r\n    const displayUrl =\r\n      project?.deployment?.production_url || project?.deployment?.deployment_url || null;\r\n\r\n    const domainStatusMutation = useMutation({\r\n      mutationFn: async (domain: string) => {\r\n        if (!project?.project_id) {\r\n          throw new Error(\"Project ID is required\");\r\n        }\r\n        const useCustomToken = form.getValues(\"ownVercelDeployment\");\r\n        return vercelDomainStatus(project.project_id, domain, useCustomToken ? vercelToken : null);\r\n      },\r\n      onSuccess: (data) => {\r\n        if (data.success) {\r\n          if (data.status === \"verified\") {\r\n            successToast(\"Domain Verified\", {\r\n              description: \"Domain is properly configured and verified.\",\r\n            });\r\n          } else if (data.status === \"not_found\") {\r\n            errorToast(\"Domain Not Found\", {\r\n              description: data.message || \"Domain not found in your Vercel account.\",\r\n            });\r\n          } else {\r\n            successToast(\"Domain Status Retrieved\", {\r\n              description: data.message || \"Domain status information has been loaded.\",\r\n            });\r\n          }\r\n        } else {\r\n          errorToast(\"Domain Status Check Failed\", {\r\n            description: data.message || \"Failed to check domain status.\",\r\n          });\r\n        }\r\n      },\r\n      onError: (error: Error) => {\r\n        errorToast(\"Error\", {\r\n          description: `Failed to check domain status: ${error.message}`,\r\n        });\r\n      },\r\n    });\r\n\r\n    const handleCheckDomainStatus = async (domainOverride: string | null) => {\r\n      const domainToCheck = domainOverride || getDisplayUrl(displayUrl);\r\n      if (!domainToCheck) {\r\n        errorToast(\"Missing Information\", {\r\n          description: \"Please enter a domain name.\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Basic domain validation\r\n      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z]{2,}$/;\r\n      if (!domainRegex.test(domainToCheck)) {\r\n        errorToast(\"Invalid Domain\", {\r\n          description: \"Please enter a valid domain name (e.g., example.com).\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      domainStatusMutation.mutate(domainToCheck);\r\n    };\r\n\r\n    return (\r\n      <Modal open={open} onOpenChange={onOpenChange}>\r\n        <ModalContent className=\"lg:max-w-3xl\">\r\n          <ModalHeader>\r\n            <ModalTitle>Deployed Project</ModalTitle>\r\n          </ModalHeader>\r\n\r\n          <div className=\"flex w-full flex-col gap-4 px-6 py-4\">\r\n            <div className=\"w-full space-y-3\">\r\n              {project?.deployment?.production_url ? (\r\n                <Disclosure\r\n                  open={showDomainConfiguration}\r\n                  onOpenChange={setShowDomainConfiguration}\r\n                >\r\n                  <DisclosureTrigger>\r\n                    <div className=\"flex w-full items-center justify-between gap-1 py-3\">\r\n                      <div className=\"flex flex-col gap-0\">\r\n                        <Typography.H5>Connected Domain</Typography.H5>\r\n                        <Typography.P className=\"mt-0\">View connected domain.</Typography.P>\r\n                      </div>\r\n\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Globe className=\"size-4 text-muted-foreground\" />\r\n                          <Button variant=\"link\" className=\"p-0\" asChild>\r\n                            <Link\r\n                              href={ensureHttps(displayUrl)}\r\n                              target=\"_blank\"\r\n                              className=\"text-sm text-primary\"\r\n                            >\r\n                              {getDisplayUrl(displayUrl)}\r\n                            </Link>\r\n                          </Button>\r\n                        </div>\r\n\r\n                        <ChevronDown />\r\n                      </div>\r\n                    </div>\r\n                  </DisclosureTrigger>\r\n\r\n                  <DisclosureContent>\r\n                    <div className=\"flex w-full items-center justify-between pb-3\">\r\n                      <div className=\"w-full space-y-3 rounded-md border bg-background p-4\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"flex flex-col gap-0\">\r\n                            <Typography.H5>Current Status</Typography.H5>\r\n                            <Typography.P className=\"mt-0\">\r\n                              {project?.deployment?.production_url\r\n                                ? \"Domain is properly configured and verified.\"\r\n                                : \"Domain is not properly configured and verified.\"}\r\n                            </Typography.P>\r\n                          </div>\r\n\r\n                          <Button\r\n                            size=\"sm\"\r\n                            onClick={() => handleCheckDomainStatus(null)}\r\n                            disabled={domainStatusMutation.isPending}\r\n                          >\r\n                            {domainStatusMutation.isPending ? (\r\n                              <>\r\n                                <Loading className=\"mr-2 size-4\" />\r\n                                Checking...\r\n                              </>\r\n                            ) : (\r\n                              \"Check Status\"\r\n                            )}\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex w-full items-center justify-between pb-3\">\r\n                      <div className=\"w-full space-y-3 rounded-md border bg-background p-4\">\r\n                        <Typography.H5>Domain Configuration Steps</Typography.H5>\r\n                        <Tabs defaultValue=\"vercel\" className=\"w-full\">\r\n                          <TabsList className=\"grid w-full grid-cols-2\">\r\n                            <TabsTrigger value=\"vercel\">Vercel Dashboard</TabsTrigger>\r\n                            <TabsTrigger value=\"manual\">Manual DNS</TabsTrigger>\r\n                          </TabsList>\r\n\r\n                          <TabsContent value=\"vercel\">\r\n                            <ol className=\"list-inside list-decimal space-y-2 text-sm text-muted-foreground\">\r\n                              <li>Go to your Vercel project settings</li>\r\n                              <li>\r\n                                Click on <span className=\"font-bold text-primary\">Domains</span> in\r\n                                the left sidebar\r\n                              </li>\r\n                              <li>Find your domain in the list</li>\r\n                              <li>\r\n                                Click on{\" \"}\r\n                                <span className=\"font-bold text-primary\">\r\n                                  View DNS Configuration\r\n                                </span>\r\n                              </li>\r\n                              <li>Follow the DNS configuration instructions provided by Vercel</li>\r\n                              <li>Wait for DNS propagation (can take up to 48 hours)</li>\r\n                            </ol>\r\n                          </TabsContent>\r\n\r\n                          <TabsContent value=\"manual\">\r\n                            <ol className=\"list-inside list-decimal space-y-2 text-sm text-muted-foreground\">\r\n                              <li>Add these DNS records to your domain provider:</li>\r\n                              <div className=\"ml-6 mt-2 rounded bg-muted p-3 font-mono text-sm\">\r\n                                <p>Type: A</p>\r\n                                <p>Name: @</p>\r\n                                <p>Value: 76.76.21.21</p>\r\n                              </div>\r\n                              <div className=\"ml-6 mt-2 rounded bg-muted p-3 font-mono text-sm\">\r\n                                <p>Type: CNAME</p>\r\n                                <p>Name: www</p>\r\n                                <p>Value: cname.vercel-dns.com</p>\r\n                              </div>\r\n                              <li>Wait for DNS propagation (can take up to 48 hours)</li>\r\n                              <li>\r\n                                Your domain will automatically be verified once DNS is propagated\r\n                              </li>\r\n                            </ol>\r\n                          </TabsContent>\r\n                        </Tabs>\r\n                      </div>\r\n                    </div>\r\n                  </DisclosureContent>\r\n                </Disclosure>\r\n              ) : (\r\n                <div className=\"flex w-full items-center justify-between gap-1 py-3\">\r\n                  <div className=\"flex flex-col gap-0\">\r\n                    <Typography.H5>Connected Domain</Typography.H5>\r\n                    <Typography.P className=\"mt-0\">View connected domain.</Typography.P>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Globe className=\"size-4 text-muted-foreground\" />\r\n                    <Button variant=\"link\" className=\"p-0\" asChild>\r\n                      <Link\r\n                        href={ensureHttps(displayUrl)}\r\n                        target=\"_blank\"\r\n                        className=\"text-sm text-primary\"\r\n                      >\r\n                        {getDisplayUrl(displayUrl)}\r\n                      </Link>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex w-full items-center justify-between pb-3\">\r\n                <div className=\"flex flex-col gap-0\">\r\n                  <Typography.H5>Last Deployed</Typography.H5>\r\n                  <Typography.P className=\"mt-0\">View last deployed version.</Typography.P>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CalendarSolid className=\"size-4 text-muted-foreground\" />\r\n                  <span className=\"text-sm text-primary\">\r\n                    {project?.deployment?.last_deployed\r\n                      ? formatTimestamp(project.deployment.last_deployed, \"deployed\")\r\n                      : \"Never\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex items-center justify-between gap-1 pb-3\">\r\n                <div className=\"flex flex-col gap-0\">\r\n                  <Typography.H5>Custom Domain</Typography.H5>\r\n                  <Typography.P className=\"mt-0\">Connect a domain you already own.</Typography.P>\r\n                </div>\r\n\r\n                <Button size=\"sm\" onClick={() => setShowCustomDomainForm(true)}>\r\n                  Connect Domain\r\n                </Button>\r\n              </div>\r\n\r\n              <Form {...form}>\r\n                <>\r\n                  <FormField\r\n                    name=\"ownVercelDeployment\"\r\n                    control={form.control}\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <div className=\"flex items-center justify-between gap-1 space-y-1 pb-3\">\r\n                          <div className=\"flex flex-col gap-0\">\r\n                            <Typography.H5>Use own Vercel account</Typography.H5>\r\n                            <Typography.P>\r\n                              Use your own Vercel account to deploy your project.\r\n                            </Typography.P>\r\n                          </div>\r\n\r\n                          <Switch\r\n                            checked={field.value}\r\n                            onCheckedChange={(value) => {\r\n                              field.onChange(value);\r\n                              if (value) {\r\n                                setShowAddOwnVercelTokenForm(true);\r\n                              }\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    name=\"pushEnvBeforeDeploy\"\r\n                    control={form.control}\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <div className=\"flex items-center justify-between gap-1 space-y-1 pb-3\">\r\n                          <div className=\"flex flex-col gap-0\">\r\n                            <Typography.H5>Push Environment Variables Before Deploy</Typography.H5>\r\n                            <Typography.P>\r\n                              Push environment variables before deploying your project.\r\n                            </Typography.P>\r\n                          </div>\r\n\r\n                          <Switch\r\n                            checked={field.value}\r\n                            onCheckedChange={(value) => {\r\n                              field.onChange(value);\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </>\r\n              </Form>\r\n\r\n              <div className=\"flex items-center justify-end gap-2 py-3\">\r\n                <Button\r\n                  variant=\"destructive\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    if (isRedeploying) {\r\n                      errorToast(\"Please wait to redeploy\");\r\n                      return;\r\n                    }\r\n                    setAlertRemoveDomain(true);\r\n                  }}\r\n                  disabled={isRemovingDomain}\r\n                  className=\"h-8\"\r\n                >\r\n                  {isRemovingDomain ? (\r\n                    <>\r\n                      <Loading className=\"size-4\" />\r\n                      <span className=\"text-sm font-medium\">Removing...</span>\r\n                    </>\r\n                  ) : (\r\n                    \"Delete Deployment\"\r\n                  )}\r\n                </Button>\r\n\r\n                <Button\r\n                  size=\"sm\"\r\n                  className=\"h-8\"\r\n                  onClick={() => {\r\n                    onOpenChange(false);\r\n                    setStartDeployment(true);\r\n                    setIsRedeploying(true);\r\n                    setIsRedeploy(true);\r\n                    onRedeploy();\r\n                  }}\r\n                  disabled={isRedeploying}\r\n                >\r\n                  {isRedeploying ? (\r\n                    <>\r\n                      <Loading className=\"size-4 text-background\" />\r\n                      <span className=\"text-sm font-medium\">Redeploying...</span>\r\n                    </>\r\n                  ) : (\r\n                    \"Redeploy\"\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ModalContent>\r\n\r\n        <LazyModal open={showCustomDomainForm} onOpenChange={setShowCustomDomainForm}>\r\n          <CustomDomainModal\r\n            open={showCustomDomainForm}\r\n            setShowModal={setShowCustomDomainForm}\r\n            invalidateQueries={invalidateQueries}\r\n          />\r\n        </LazyModal>\r\n\r\n        <LazyModal\r\n          open={showAddOwnVercelTokenForm}\r\n          onOpenChange={(open) => {\r\n            setShowAddOwnVercelTokenForm(open);\r\n            if (!open) {\r\n              const token = form.getValues(\"vercelToken\");\r\n              if (!token) {\r\n                form.setValue(\"ownVercelDeployment\", false);\r\n              }\r\n            }\r\n          }}\r\n        >\r\n          <AddOwnVercelTokenModal\r\n            open={showAddOwnVercelTokenForm}\r\n            setShowModal={setShowAddOwnVercelTokenForm}\r\n            projectForm={form}\r\n          />\r\n        </LazyModal>\r\n\r\n        {/* Unlink Confirmation Dialog */}\r\n        <AlertDialog open={alertRemoveDomain} onOpenChange={setAlertRemoveDomain}>\r\n          <AlertDialogContent>\r\n            <AlertDialogHeader>\r\n              <AlertDialogTitle>Confirm Unlink</AlertDialogTitle>\r\n              <AlertDialogDescription>\r\n                Are you sure you want to unlink this project from Vercel? This action cannot be\r\n                undone. Your deployment will remain active but will no longer be connected to this\r\n                project.\r\n              </AlertDialogDescription>\r\n            </AlertDialogHeader>\r\n            <AlertDialogFooter className=\"flex items-center justify-end\">\r\n              <AlertDialogCancel\r\n                className=\"w-full md:w-fit\"\r\n                onClick={() => {\r\n                  setAlertRemoveDomain(false);\r\n                }}\r\n              >\r\n                Cancel\r\n              </AlertDialogCancel>\r\n              <AlertDialogAction\r\n                onClick={() => onUnlink()}\r\n                className=\"h-8 w-full bg-destructive text-destructive-foreground hover:bg-destructive/90 md:w-fit\"\r\n              >\r\n                {isRemovingDomain ? \"Removing...\" : \"Remove\"}\r\n              </AlertDialogAction>\r\n            </AlertDialogFooter>\r\n          </AlertDialogContent>\r\n        </AlertDialog>\r\n      </Modal>\r\n    );\r\n  },\r\n);\r\n\r\nDeployedProjectView.displayName = \"DeployedProjectView\";\r\n"], "names": [], "mappings": ";;;;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAIA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,cAAc,CAAC;IACnB,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IAAI,UAAU,CAAC,UAAU,MAAM,CAAC,QAAQ,EAAE,KAAK;AACxD;AAEA,MAAM,gBAAgB,CAAC;IACrB,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IAAI,OAAO,CAAC,gBAAgB;AACrC;AAEO,MAAM,oCAAsB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACpC,CAAC,EACC,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,IAAI,EACJ,YAAY,EACZ,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,WAAW,EAcZ;IACC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,MAAM,aACJ,SAAS,YAAY,kBAAkB,SAAS,YAAY,kBAAkB;IAEhF,MAAM,uBAAuB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACvC,YAAY,OAAO;YACjB,IAAI,CAAC,SAAS,YAAY;gBACxB,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,iBAAiB,KAAK,SAAS,CAAC;YACtC,OAAO,CAAA,GAAA,iHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,UAAU,EAAE,QAAQ,iBAAiB,cAAc;QACvF;QACA,WAAW,CAAC;YACV,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,KAAK,MAAM,KAAK,YAAY;oBAC9B,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;wBAC9B,aAAa;oBACf;gBACF,OAAO,IAAI,KAAK,MAAM,KAAK,aAAa;oBACtC,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,oBAAoB;wBAC7B,aAAa,KAAK,OAAO,IAAI;oBAC/B;gBACF,OAAO;oBACL,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE,2BAA2B;wBACtC,aAAa,KAAK,OAAO,IAAI;oBAC/B;gBACF;YACF,OAAO;gBACL,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,8BAA8B;oBACvC,aAAa,KAAK,OAAO,IAAI;gBAC/B;YACF;QACF;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;gBAClB,aAAa,CAAC,+BAA+B,EAAE,MAAM,OAAO,EAAE;YAChE;QACF;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,MAAM,gBAAgB,kBAAkB,cAAc;QACtD,IAAI,CAAC,eAAe;YAClB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,uBAAuB;gBAChC,aAAa;YACf;YACA;QACF;QAEA,0BAA0B;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,CAAC,gBAAgB;YACpC,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;gBAC3B,aAAa;YACf;YACA;QACF;QAEA,qBAAqB,MAAM,CAAC;IAC9B;IAEA,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;;0BAC/B,6VAAC,iIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,6VAAC,iIAAA,CAAA,cAAW;kCACV,cAAA,6VAAC,iIAAA,CAAA,aAAU;sCAAC;;;;;;;;;;;kCAGd,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;gCACZ,SAAS,YAAY,+BACpB,6VAAC,sIAAA,CAAA,aAAU;oCACT,MAAM;oCACN,cAAc;;sDAEd,6VAAC,sIAAA,CAAA,oBAAiB;sDAChB,cAAA,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;0EAAC;;;;;;0EACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;gEAAC,WAAU;0EAAO;;;;;;;;;;;;kEAGjC,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,4SAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6VAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAO,WAAU;wEAAM,OAAO;kFAC5C,cAAA,6VAAC,2QAAA,CAAA,UAAI;4EACH,MAAM,YAAY;4EAClB,QAAO;4EACP,WAAU;sFAET,cAAc;;;;;;;;;;;;;;;;;0EAKrB,6VAAC,wTAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;sDAKlB,6VAAC,sIAAA,CAAA,oBAAiB;;8DAChB,6VAAC;oDAAI,WAAU;8DACb,cAAA,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAI,WAAU;;sFACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sFAAC;;;;;;sFACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;4EAAC,WAAU;sFACrB,SAAS,YAAY,iBAClB,gDACA;;;;;;;;;;;;8EAIR,6VAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAS,IAAM,wBAAwB;oEACvC,UAAU,qBAAqB,SAAS;8EAEvC,qBAAqB,SAAS,iBAC7B;;0FACE,6VAAC,mIAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAgB;;uFAIrC;;;;;;;;;;;;;;;;;;;;;;8DAOV,6VAAC;oDAAI,WAAU;8DACb,cAAA,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;0EAAC;;;;;;0EACf,6VAAC,gIAAA,CAAA,OAAI;gEAAC,cAAa;gEAAS,WAAU;;kFACpC,6VAAC,gIAAA,CAAA,WAAQ;wEAAC,WAAU;;0FAClB,6VAAC,gIAAA,CAAA,cAAW;gFAAC,OAAM;0FAAS;;;;;;0FAC5B,6VAAC,gIAAA,CAAA,cAAW;gFAAC,OAAM;0FAAS;;;;;;;;;;;;kFAG9B,6VAAC,gIAAA,CAAA,cAAW;wEAAC,OAAM;kFACjB,cAAA,6VAAC;4EAAG,WAAU;;8FACZ,6VAAC;8FAAG;;;;;;8FACJ,6VAAC;;wFAAG;sGACO,6VAAC;4FAAK,WAAU;sGAAyB;;;;;;wFAAc;;;;;;;8FAGlE,6VAAC;8FAAG;;;;;;8FACJ,6VAAC;;wFAAG;wFACO;sGACT,6VAAC;4FAAK,WAAU;sGAAyB;;;;;;;;;;;;8FAI3C,6VAAC;8FAAG;;;;;;8FACJ,6VAAC;8FAAG;;;;;;;;;;;;;;;;;kFAIR,6VAAC,gIAAA,CAAA,cAAW;wEAAC,OAAM;kFACjB,cAAA,6VAAC;4EAAG,WAAU;;8FACZ,6VAAC;8FAAG;;;;;;8FACJ,6VAAC;oFAAI,WAAU;;sGACb,6VAAC;sGAAE;;;;;;sGACH,6VAAC;sGAAE;;;;;;sGACH,6VAAC;sGAAE;;;;;;;;;;;;8FAEL,6VAAC;oFAAI,WAAU;;sGACb,6VAAC;sGAAE;;;;;;sGACH,6VAAC;sGAAE;;;;;;sGACH,6VAAC;sGAAE;;;;;;;;;;;;8FAEL,6VAAC;8FAAG;;;;;;8FACJ,6VAAC;8FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAWlB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;8DAAC;;;;;;8DACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;oDAAC,WAAU;8DAAO;;;;;;;;;;;;sDAGjC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,4SAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6VAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAO,WAAU;oDAAM,OAAO;8DAC5C,cAAA,6VAAC,2QAAA,CAAA,UAAI;wDACH,MAAM,YAAY;wDAClB,QAAO;wDACP,WAAU;kEAET,cAAc;;;;;;;;;;;;;;;;;;;;;;;8CAOzB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;8DAAC;;;;;;8DACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;oDAAC,WAAU;8DAAO;;;;;;;;;;;;sDAGjC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,qUAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6VAAC;oDAAK,WAAU;8DACb,SAAS,YAAY,gBAClB,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,UAAU,CAAC,aAAa,EAAE,cAClD;;;;;;;;;;;;;;;;;;8CAKV,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;8DAAC;;;;;;8DACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;oDAAC,WAAU;8DAAO;;;;;;;;;;;;sDAGjC,6VAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAS,IAAM,wBAAwB;sDAAO;;;;;;;;;;;;8CAKlE,6VAAC,gIAAA,CAAA,OAAI;oCAAE,GAAG,IAAI;8CACZ,cAAA;;0DACE,6VAAC,gIAAA,CAAA,YAAS;gDACR,MAAK;gDACL,SAAS,KAAK,OAAO;gDACrB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;kEACP,cAAA,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAI,WAAU;;sFACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sFAAC;;;;;;sFACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;sFAAC;;;;;;;;;;;;8EAKhB,6VAAC,kIAAA,CAAA,SAAM;oEACL,SAAS,MAAM,KAAK;oEACpB,iBAAiB,CAAC;wEAChB,MAAM,QAAQ,CAAC;wEACf,IAAI,OAAO;4EACT,6BAA6B;wEAC/B;oEACF;;;;;;;;;;;;;;;;;;;;;;0DAOV,6VAAC,gIAAA,CAAA,YAAS;gDACR,MAAK;gDACL,SAAS,KAAK,OAAO;gDACrB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;kEACP,cAAA,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAI,WAAU;;sFACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;sFAAC;;;;;;sFACf,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;sFAAC;;;;;;;;;;;;8EAKhB,6VAAC,kIAAA,CAAA,SAAM;oEACL,SAAS,MAAM,KAAK;oEACpB,iBAAiB,CAAC;wEAChB,MAAM,QAAQ,CAAC;oEACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASd,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,IAAI,eAAe;oDACjB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;oDACX;gDACF;gDACA,qBAAqB;4CACvB;4CACA,UAAU;4CACV,WAAU;sDAET,iCACC;;kEACE,6VAAC,mIAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6VAAC;wDAAK,WAAU;kEAAsB;;;;;;;+DAGxC;;;;;;sDAIJ,6VAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS;gDACP,aAAa;gDACb,mBAAmB;gDACnB,iBAAiB;gDACjB,cAAc;gDACd;4CACF;4CACA,UAAU;sDAET,8BACC;;kEACE,6VAAC,mIAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6VAAC;wDAAK,WAAU;kEAAsB;;;;;;;+DAGxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQZ,6VAAC,iIAAA,CAAA,YAAS;gBAAC,MAAM;gBAAsB,cAAc;0BACnD,cAAA,6VAAC,iLAAA,CAAA,UAAiB;oBAChB,MAAM;oBACN,cAAc;oBACd,mBAAmB;;;;;;;;;;;0BAIvB,6VAAC,iIAAA,CAAA,YAAS;gBACR,MAAM;gBACN,cAAc,CAAC;oBACb,6BAA6B;oBAC7B,IAAI,CAAC,MAAM;wBACT,MAAM,QAAQ,KAAK,SAAS,CAAC;wBAC7B,IAAI,CAAC,OAAO;4BACV,KAAK,QAAQ,CAAC,uBAAuB;wBACvC;oBACF;gBACF;0BAEA,cAAA,6VAAC,qLAAA,CAAA,UAAsB;oBACrB,MAAM;oBACN,cAAc;oBACd,aAAa;;;;;;;;;;;0BAKjB,6VAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;;sCACjB,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAM1B,6VAAC,2IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,6VAAC,2IAAA,CAAA,oBAAiB;oCAChB,WAAU;oCACV,SAAS;wCACP,qBAAqB;oCACvB;8CACD;;;;;;8CAGD,6VAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS,IAAM;oCACf,WAAU;8CAET,mBAAmB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD;AAGF,oBAAoB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/components/deployment-stepper.tsx"], "sourcesContent": ["import { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  StepperIndicator,\r\n  Stepper<PERSON><PERSON>,\r\n  StepperSeparator,\r\n  Stepper<PERSON><PERSON>le,\r\n  StepperTrigger,\r\n} from \"@/components/ui/stepper\";\r\nimport { TextShimmer } from \"@/components/ui/text-shimmer\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { memo, useEffect } from \"react\";\r\n\r\ntype StepStatus = {\r\n  status: \"completed\" | \"pending\" | \"failed\" | \"not-started\";\r\n  message?: string;\r\n};\r\n\r\ntype Step = {\r\n  name: string;\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  action: any;\r\n};\r\n\r\nconst StepperItemMemo = memo(\r\n  ({\r\n    step,\r\n    stepStatus,\r\n    isCurrentStep,\r\n    stepName,\r\n    isLastStep,\r\n    setIsFailed,\r\n  }: {\r\n    step: number;\r\n    stepStatus: StepStatus;\r\n    isCurrentStep: boolean;\r\n    stepName: string;\r\n    isLastStep: boolean;\r\n    setIsFailed: (isFailed: boolean) => void;\r\n  }) => {\r\n    useEffect(() => {\r\n      if (stepStatus?.status === \"failed\") {\r\n        setIsFailed(true);\r\n      }\r\n    }, [stepStatus?.status, setIsFailed]);\r\n\r\n    return (\r\n      <StepperItem\r\n        key={step}\r\n        step={step}\r\n        completed={stepStatus?.status === \"completed\"}\r\n        loading={isCurrentStep && stepStatus?.status === \"pending\"}\r\n        disabled={stepStatus?.status === \"failed\"}\r\n        className={cn(\r\n          \"not-last:flex-1 relative w-full items-start\",\r\n          stepStatus?.status === \"not-started\" && \"opacity-50\",\r\n        )}\r\n      >\r\n        <StepperTrigger className=\"w-full items-start rounded pb-8 last:pb-0\">\r\n          <StepperIndicator\r\n            className=\"group-data-loading/step:text-transparent\"\r\n            failed={stepStatus?.status === \"failed\"}\r\n          />\r\n          <div className=\"flex w-full items-center justify-between px-2 text-left\">\r\n            <StepperTitle>{stepName}</StepperTitle>\r\n            <div className=\"ml-auto\">\r\n              {stepStatus?.status === \"failed\" && <Badge variant=\"destructive\">Failed</Badge>}\r\n              {stepStatus?.status === \"completed\" && <Badge variant=\"success\">Completed</Badge>}\r\n              {isCurrentStep && stepStatus?.status === \"pending\" && (\r\n                <TextShimmer className=\"px-0 text-sm\">In progress...</TextShimmer>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </StepperTrigger>\r\n        {!isLastStep && (\r\n          <StepperSeparator className=\"absolute inset-y-0 left-3 top-[calc(1.5rem+0.125rem)] -order-1 m-0 -translate-x-1/2 group-data-[orientation=vertical]/stepper:h-[calc(100%-1.5rem-0.25rem)]\" />\r\n        )}\r\n      </StepperItem>\r\n    );\r\n  },\r\n);\r\n\r\nStepperItemMemo.displayName = \"StepperItemMemo\";\r\n\r\nexport const DeploymentStepper = memo(\r\n  ({\r\n    steps,\r\n    stepStatuses,\r\n    currentStep,\r\n    setIsFailed,\r\n  }: {\r\n    steps: Step[];\r\n    stepStatuses: StepStatus[];\r\n    currentStep: number;\r\n    setIsFailed: (isFailed: boolean) => void;\r\n  }) => (\r\n    <Stepper className=\"w-full\" value={currentStep} orientation=\"vertical\">\r\n      {steps.map((step, i) => (\r\n        <StepperItemMemo\r\n          key={`step-${step.name}-${i}`}\r\n          step={i}\r\n          stepStatus={stepStatuses[i]}\r\n          isCurrentStep={i === currentStep}\r\n          stepName={step.name}\r\n          isLastStep={i === steps.length - 1}\r\n          setIsFailed={setIsFailed}\r\n        />\r\n      ))}\r\n    </Stepper>\r\n  ),\r\n);\r\n\r\nDeploymentStepper.displayName = \"DeploymentStepper\";\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;AACA;AACA;;;;;;;AAaA,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACzB,CAAC,EACC,IAAI,EACJ,UAAU,EACV,aAAa,EACb,QAAQ,EACR,UAAU,EACV,WAAW,EAQZ;IACC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,WAAW,UAAU;YACnC,YAAY;QACd;IACF,GAAG;QAAC,YAAY;QAAQ;KAAY;IAEpC,qBACE,6VAAC,mIAAA,CAAA,cAAW;QAEV,MAAM;QACN,WAAW,YAAY,WAAW;QAClC,SAAS,iBAAiB,YAAY,WAAW;QACjD,UAAU,YAAY,WAAW;QACjC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+CACA,YAAY,WAAW,iBAAiB;;0BAG1C,6VAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;;kCACxB,6VAAC,mIAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,QAAQ,YAAY,WAAW;;;;;;kCAEjC,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,mIAAA,CAAA,eAAY;0CAAE;;;;;;0CACf,6VAAC;gCAAI,WAAU;;oCACZ,YAAY,WAAW,0BAAY,6VAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;oCAChE,YAAY,WAAW,6BAAe,6VAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAU;;;;;;oCAC/D,iBAAiB,YAAY,WAAW,2BACvC,6VAAC,2IAAA,CAAA,cAAW;wCAAC,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;YAK7C,CAAC,4BACA,6VAAC,mIAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;OA3BzB;;;;;AA+BX;AAGF,gBAAgB,WAAW,GAAG;AAEvB,MAAM,kCAAoB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EAClC,CAAC,EACC,KAAK,EACL,YAAY,EACZ,WAAW,EACX,WAAW,EAMZ,iBACC,6VAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;QAAS,OAAO;QAAa,aAAY;kBACzD,MAAM,GAAG,CAAC,CAAC,MAAM,kBAChB,6VAAC;gBAEC,MAAM;gBACN,YAAY,YAAY,CAAC,EAAE;gBAC3B,eAAe,MAAM;gBACrB,UAAU,KAAK,IAAI;gBACnB,YAAY,MAAM,MAAM,MAAM,GAAG;gBACjC,aAAa;eANR,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,GAAG;;;;;;;;;;AAavC,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2096, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/components/deployment-progress.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { Modal, ModalContent, ModalHeader, ModalTitle } from \"@/components/ui/modal\";\r\nimport TaskStatus from \"@/features/global/task-status\";\r\nimport { memo, useState } from \"react\";\r\nimport { DeploymentStepper } from \"./deployment-stepper\";\r\n\r\ntype StepStatus = {\r\n  status: \"completed\" | \"pending\" | \"failed\" | \"not-started\";\r\n  message?: string;\r\n};\r\n\r\ntype Step = {\r\n  name: string;\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  action: any;\r\n};\r\n\r\nexport const DeploymentProgress = memo(\r\n  ({\r\n    steps,\r\n    stepStatuses,\r\n    currentStep,\r\n    isDeploying,\r\n    onCancelDeployment,\r\n    open,\r\n    onOpenChange,\r\n  }: {\r\n    steps: Step[];\r\n    stepStatuses: StepStatus[];\r\n    currentStep: number;\r\n    isDeploying: boolean;\r\n    onCancelDeployment: () => void;\r\n    open: boolean;\r\n    onOpenChange: (open: boolean) => void;\r\n  }) => {\r\n    const [isFailed, setIsFailed] = useState(false);\r\n\r\n    return (\r\n      <Modal open={open} onOpenChange={onOpenChange}>\r\n        <ModalContent className=\"lg:max-w-3xl\">\r\n          <ModalHeader>\r\n            <ModalTitle>Deployment Progress</ModalTitle>\r\n          </ModalHeader>\r\n          <div className=\"space-y-6 px-6 py-6\">\r\n            <DeploymentStepper\r\n              setIsFailed={setIsFailed}\r\n              steps={steps}\r\n              stepStatuses={stepStatuses}\r\n              currentStep={currentStep}\r\n            />\r\n\r\n            {isDeploying && !isFailed && (\r\n              <TaskStatus variant=\"info\" className=\"mb-0\">\r\n                <div className=\"flex w-full items-center gap-2\">\r\n                  <Loading className=\"size-4\" />\r\n                  <span className=\"text-sm font-medium\">Deployment in progress</span>\r\n                </div>\r\n              </TaskStatus>\r\n            )}\r\n\r\n            {isDeploying && (\r\n              <div className=\"flex justify-end\">\r\n                <Button variant=\"outline\" onClick={onCancelDeployment}>\r\n                  Cancel Deployment\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </ModalContent>\r\n      </Modal>\r\n    );\r\n  },\r\n);\r\n\r\nDeploymentProgress.displayName = \"DeploymentProgress\";\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAaO,MAAM,mCAAqB,CAAA,GAAA,oTAAA,CAAA,OAAI,AAAD,EACnC,CAAC,EACC,KAAK,EACL,YAAY,EACZ,WAAW,EACX,WAAW,EACX,kBAAkB,EAClB,IAAI,EACJ,YAAY,EASb;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;kBAC/B,cAAA,6VAAC,iIAAA,CAAA,eAAY;YAAC,WAAU;;8BACtB,6VAAC,iIAAA,CAAA,cAAW;8BACV,cAAA,6VAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;;;;;;8BAEd,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,6KAAA,CAAA,oBAAiB;4BAChB,aAAa;4BACb,OAAO;4BACP,cAAc;4BACd,aAAa;;;;;;wBAGd,eAAe,CAAC,0BACf,6VAAC,4IAAA,CAAA,UAAU;4BAAC,SAAQ;4BAAO,WAAU;sCACnC,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,mIAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6VAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;wBAK3C,6BACC,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;0CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrE;AAGF,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/components/not-published.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Form, FormControl, FormField, FormItem, FormMessage } from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport Loading from \"@/components/ui/loading\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport Typography from \"@/components/ui/typography\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { QuestionCircle } from \"@mynaui/icons-react\";\r\nimport { UseMutateFunction } from \"@tanstack/react-query\";\r\nimport Link from \"next/link\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\n\r\ntype Props = {\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  startDeployment: UseMutateFunction<any, Error, z.infer<typeof formSchema>, unknown>;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  isDeploying: boolean;\r\n};\r\n\r\nconst formSchema = z.object({\r\n  ownVercelDeployment: z.boolean().default(false),\r\n  vercelToken: z.string().optional(),\r\n  pushEnvBeforeDeploy: z.boolean().default(false),\r\n  domain: z.string().optional(),\r\n});\r\n\r\nconst NotPublished = ({ startDeployment, open, onOpenChange, isDeploying }: Props) => {\r\n  const { user } = useAuth();\r\n  const { project } = useProject();\r\n  const [showTokenInstructions, setShowTokenInstructions] = useState(false);\r\n\r\n  const form = useForm<z.infer<typeof formSchema>>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      ownVercelDeployment: false,\r\n      pushEnvBeforeDeploy: false,\r\n      domain: \"\",\r\n    },\r\n  });\r\n\r\n  const isFreePlan = user?.userFromDb?.plan === \"free\";\r\n\r\n  debug(\"open\", open);\r\n\r\n  useEffect(() => {\r\n    debug(\"project?.deployment?.vercel_token\", project?.deployment?.vercel_token);\r\n    if (project?.deployment?.vercel_token) {\r\n      debug(\"project?.deployment?.vercel_token\", project?.deployment?.vercel_token);\r\n      form.setValue(\"ownVercelDeployment\", true);\r\n      form.setValue(\"vercelToken\", project?.deployment?.vercel_token);\r\n    } else if (isFreePlan) {\r\n      form.setValue(\"ownVercelDeployment\", true);\r\n    }\r\n  }, [isFreePlan]);\r\n\r\n  const ownVercelDeployment = form.watch(\"ownVercelDeployment\");\r\n  const isUsingSoftgenToken = isFreePlan && !ownVercelDeployment;\r\n\r\n  const onSubmit = (data: z.infer<typeof formSchema>) => {\r\n    if (data.ownVercelDeployment && (!data.vercelToken || data.vercelToken.length === 0)) {\r\n      form.setError(\"vercelToken\", {\r\n        type: \"manual\",\r\n        message: \"Vercel token is required when using custom token\",\r\n      });\r\n      return;\r\n    }\r\n    startDeployment(data);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button variant=\"default\" size=\"sm\" className=\"ml-auto w-fit\" disabled={false}>\r\n          Publish\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent\r\n        align=\"center\"\r\n        sideOffset={10}\r\n        side=\"bottom\"\r\n        alignOffset={100}\r\n        className=\"w-96 p-0\"\r\n      >\r\n        {isUsingSoftgenToken ? (\r\n          <div className=\"flex flex-col gap-4 p-4\">\r\n            <Typography.H5 className=\"font-medium\">\r\n              You are using the free plan. Please upgrade to a paid plan to publish your project\r\n              with our Vercel account, or use your own Vercel token.\r\n            </Typography.H5>\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"flex-1\"\r\n                onClick={() => form.setValue(\"ownVercelDeployment\", true)}\r\n              >\r\n                Use Own Token\r\n              </Button>\r\n              <Button variant=\"default\" size=\"sm\" className=\"flex-1\" asChild>\r\n                <Link href=\"/pricing\">Upgrade</Link>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"w-full\">\r\n            <div className=\"flex items-center justify-between gap-1 space-y-1 border-b p-4 pb-2\">\r\n              <div className=\"flex flex-col gap-0\">\r\n                <Typography.H5>Publish your project to the public.</Typography.H5>\r\n                {isFreePlan && (\r\n                  <Typography.P className=\"mt-0 text-sm text-muted-foreground\">\r\n                    Free plan users must use their own Vercel token\r\n                  </Typography.P>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <Form {...form}>\r\n              <form onSubmit={form.handleSubmit(onSubmit)} className=\"w-full\">\r\n                <div className=\"grid w-full gap-4 p-4 py-2\">\r\n                  <FormField\r\n                    name=\"ownVercelDeployment\"\r\n                    control={form.control}\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <div className=\"flex items-center justify-between gap-2\">\r\n                          <div className=\"flex flex-col gap-1\">\r\n                            <Label className=\"text-base\">Use own Vercel account</Label>\r\n                            {isFreePlan && (\r\n                              <Typography.P className=\"mt-0 text-sm text-muted-foreground\">\r\n                                Required for free plan users\r\n                              </Typography.P>\r\n                            )}\r\n                          </div>\r\n                          <Switch checked={field.value} onCheckedChange={field.onChange} />\r\n                        </div>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </div>\r\n\r\n                {ownVercelDeployment && (\r\n                  <>\r\n                    <Separator className=\"mt-0\" />\r\n                    <div className={cn(\"space-y-4 px-4 pb-4\", showTokenInstructions && \"pb-0\")}>\r\n                      <FormField\r\n                        name=\"vercelToken\"\r\n                        control={form.control}\r\n                        render={({ field }) => (\r\n                          <FormItem>\r\n                            <div className=\"flex w-full items-center justify-between gap-2\">\r\n                              <Label>Vercel Token</Label>\r\n\r\n                              <Button\r\n                                variant=\"ghost\"\r\n                                size=\"icon\"\r\n                                className=\"size-7\"\r\n                                type=\"button\"\r\n                                onClick={() => setShowTokenInstructions(true)}\r\n                              >\r\n                                <QuestionCircle className=\"size-4\" />\r\n                              </Button>\r\n                            </div>\r\n                            <FormControl>\r\n                              <Input {...field} placeholder=\"Vercel Token\" />\r\n                            </FormControl>\r\n                            <FormMessage />\r\n                          </FormItem>\r\n                        )}\r\n                      />\r\n\r\n                      {showTokenInstructions && (\r\n                        <div className=\"flex flex-col gap-1 pb-4\">\r\n                          <Typography.H4>\r\n                            How to create a Vercel token with full access\r\n                          </Typography.H4>\r\n\r\n                          <ol className=\"list-decimal pl-5\">\r\n                            <li className=\"leading-7 text-muted-foreground\">\r\n                              Go to{\" \"}\r\n                              <Link\r\n                                href=\"https://vercel.com/account/tokens\"\r\n                                target=\"_blank\"\r\n                                rel=\"noopener noreferrer\"\r\n                                className=\"text-primary underline\"\r\n                              >\r\n                                Vercel Tokens\r\n                              </Link>\r\n                            </li>\r\n                            <li className=\"leading-7 text-muted-foreground\">\r\n                              Click on{\" \"}\r\n                              <strong className=\"font-medium text-foreground\">Create Token.</strong>\r\n                            </li>\r\n                            <li className=\"leading-7 text-muted-foreground\">\r\n                              Enter a name for your token.\r\n                            </li>\r\n                            <li className=\"leading-7 text-muted-foreground\">\r\n                              Set the token scope to{\" \"}\r\n                              <strong className=\"font-medium text-foreground\">Full Account</strong>{\" \"}\r\n                              for complete access.\r\n                            </li>\r\n                            <li className=\"leading-7 text-muted-foreground\">\r\n                              Set{\" \"}\r\n                              <strong className=\"font-medium text-foreground\">\r\n                                EXPIRATION DATE\r\n                              </strong>{\" \"}\r\n                              <strong className=\"font-medium text-foreground\">No expiration</strong>{\" \"}\r\n                              for long-term use.\r\n                            </li>\r\n                            <li className=\"leading-7 text-muted-foreground\">\r\n                              Click <strong className=\"font-medium text-foreground\">Create</strong>{\" \"}\r\n                              to generate the token.\r\n                            </li>\r\n                            <li className=\"leading-7 text-muted-foreground\">\r\n                              Copy the generated token and paste it here.\r\n                            </li>\r\n                          </ol>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </>\r\n                )}\r\n\r\n                <div className=\"flex justify-end border-t px-4 py-2\">\r\n                  <Button type=\"submit\" size=\"sm\" className=\"ml-auto w-fit\" disabled={isDeploying}>\r\n                    {isDeploying ? (\r\n                      <>\r\n                        <Loading className=\"size-4 text-background\" />\r\n                        <span className=\"text-sm font-medium\">Publishing...</span>\r\n                      </>\r\n                    ) : (\r\n                      \"Publish\"\r\n                    )}\r\n                  </Button>\r\n                </div>\r\n              </form>\r\n            </Form>\r\n          </div>\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n\r\nexport default NotPublished;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;AAUA,MAAM,aAAa,mOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,qBAAqB,mOAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACzC,aAAa,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,qBAAqB,mOAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACzC,QAAQ,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B;AAEA,MAAM,eAAe,CAAC,EAAE,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAS;IAC/E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAA8B;QAC/C,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,qBAAqB;YACrB,qBAAqB;YACrB,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,MAAM,YAAY,SAAS;IAE9C,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,QAAQ;IAEd,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,qCAAqC,SAAS,YAAY;QAChE,IAAI,SAAS,YAAY,cAAc;YACrC,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,qCAAqC,SAAS,YAAY;YAChE,KAAK,QAAQ,CAAC,uBAAuB;YACrC,KAAK,QAAQ,CAAC,eAAe,SAAS,YAAY;QACpD,OAAO,IAAI,YAAY;YACrB,KAAK,QAAQ,CAAC,uBAAuB;QACvC;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,sBAAsB,KAAK,KAAK,CAAC;IACvC,MAAM,sBAAsB,cAAc,CAAC;IAE3C,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK,mBAAmB,IAAI,CAAC,CAAC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,KAAK,CAAC,GAAG;YACpF,KAAK,QAAQ,CAAC,eAAe;gBAC3B,MAAM;gBACN,SAAS;YACX;YACA;QACF;QACA,gBAAgB;IAClB;IAEA,qBACE,6VAAC,mIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,6VAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6VAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,WAAU;oBAAgB,UAAU;8BAAO;;;;;;;;;;;0BAIjF,6VAAC,mIAAA,CAAA,iBAAc;gBACb,OAAM;gBACN,YAAY;gBACZ,MAAK;gBACL,aAAa;gBACb,WAAU;0BAET,oCACC,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;4BAAC,WAAU;sCAAc;;;;;;sCAIvC,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,KAAK,QAAQ,CAAC,uBAAuB;8CACrD;;;;;;8CAGD,6VAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC5D,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;yCAK5B,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;kDAAC;;;;;;oCACd,4BACC,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wCAAC,WAAU;kDAAqC;;;;;;;;;;;;;;;;;sCAOnE,6VAAC,gIAAA,CAAA,OAAI;4BAAE,GAAG,IAAI;sCACZ,cAAA,6VAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;kDACrD,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC,gIAAA,CAAA,YAAS;4CACR,MAAK;4CACL,SAAS,KAAK,OAAO;4CACrB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;8DACP,cAAA,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAY;;;;;;oEAC5B,4BACC,6VAAC,sIAAA,CAAA,UAAU,CAAC,CAAC;wEAAC,WAAU;kFAAqC;;;;;;;;;;;;0EAKjE,6VAAC,kIAAA,CAAA,SAAM;gEAAC,SAAS,MAAM,KAAK;gEAAE,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAOtE,qCACC;;0DACE,6VAAC,qIAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6VAAC;gDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,yBAAyB;;kEACjE,6VAAC,gIAAA,CAAA,YAAS;wDACR,MAAK;wDACL,SAAS,KAAK,OAAO;wDACrB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;kFACP,6VAAC;wEAAI,WAAU;;0FACb,6VAAC,iIAAA,CAAA,QAAK;0FAAC;;;;;;0FAEP,6VAAC,kIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,WAAU;gFACV,MAAK;gFACL,SAAS,IAAM,yBAAyB;0FAExC,cAAA,6VAAC,8TAAA,CAAA,iBAAc;oFAAC,WAAU;;;;;;;;;;;;;;;;;kFAG9B,6VAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;4EAAE,GAAG,KAAK;4EAAE,aAAY;;;;;;;;;;;kFAEhC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;oDAKjB,uCACC,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,sIAAA,CAAA,UAAU,CAAC,EAAE;0EAAC;;;;;;0EAIf,6VAAC;gEAAG,WAAU;;kFACZ,6VAAC;wEAAG,WAAU;;4EAAkC;4EACxC;0FACN,6VAAC,2QAAA,CAAA,UAAI;gFACH,MAAK;gFACL,QAAO;gFACP,KAAI;gFACJ,WAAU;0FACX;;;;;;;;;;;;kFAIH,6VAAC;wEAAG,WAAU;;4EAAkC;4EACrC;0FACT,6VAAC;gFAAO,WAAU;0FAA8B;;;;;;;;;;;;kFAElD,6VAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAGhD,6VAAC;wEAAG,WAAU;;4EAAkC;4EACvB;0FACvB,6VAAC;gFAAO,WAAU;0FAA8B;;;;;;4EAAsB;4EAAI;;;;;;;kFAG5E,6VAAC;wEAAG,WAAU;;4EAAkC;4EAC1C;0FACJ,6VAAC;gFAAO,WAAU;0FAA8B;;;;;;4EAEtC;0FACV,6VAAC;gFAAO,WAAU;0FAA8B;;;;;;4EAAuB;4EAAI;;;;;;;kFAG7E,6VAAC;wEAAG,WAAU;;4EAAkC;0FACxC,6VAAC;gFAAO,WAAU;0FAA8B;;;;;;4EAAgB;4EAAI;;;;;;;kFAG5E,6VAAC;wEAAG,WAAU;kFAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU5D,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,MAAK;4CAAK,WAAU;4CAAgB,UAAU;sDACjE,4BACC;;kEACE,6VAAC,mIAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6VAAC;wDAAK,WAAU;kEAAsB;;;;;;;+DAGxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtB;uCAEe", "debugId": null}}, {"offset": {"line": 2807, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/hooks/use-deployment-state.ts"], "sourcesContent": ["import { useCallback, useReducer } from \"react\";\r\n\r\nexport type StepStatus = {\r\n  status: \"completed\" | \"pending\" | \"failed\" | \"not-started\";\r\n  message?: string;\r\n};\r\n\r\nexport type DeploymentError = {\r\n  step: string;\r\n  status: string;\r\n  error: string;\r\n  specific_error: string;\r\n  deployment_url: string;\r\n  logs: string;\r\n  detailed_logs: string;\r\n  canAutoFix?: boolean;\r\n};\r\n\r\ntype DeploymentState = {\r\n  deploymentUrl: string | null;\r\n  currentStep: number;\r\n  stepStatuses: StepStatus[];\r\n  isDeploying: boolean;\r\n  isDisconnected: boolean;\r\n  deploymentInProgress: boolean;\r\n  shouldSolvedError: boolean;\r\n  removeDomain: boolean;\r\n  deploymentSocket: WebSocket | null;\r\n  vercelDeployModalError: DeploymentError;\r\n};\r\n\r\ntype DeploymentAction =\r\n  | { type: \"SET_DEPLOYMENT_URL\"; payload: string | null }\r\n  | { type: \"SET_CURRENT_STEP\"; payload: number }\r\n  | { type: \"UPDATE_STEP_STATUS\"; payload: { index: number; status: StepStatus } }\r\n  | { type: \"SET_IS_DEPLOYING\"; payload: boolean }\r\n  | { type: \"SET_IS_DISCONNECTED\"; payload: boolean }\r\n  | { type: \"SET_DEPLOYMENT_IN_PROGRESS\"; payload: boolean }\r\n  | { type: \"SET_SHOULD_SOLVED_ERROR\"; payload: boolean }\r\n  | { type: \"SET_REMOVE_DOMAIN\"; payload: boolean }\r\n  | { type: \"SET_DEPLOYMENT_SOCKET\"; payload: WebSocket | null }\r\n  | { type: \"SET_VERCEL_DEPLOY_MODAL_ERROR\"; payload: DeploymentError }\r\n  | { type: \"RESET_STATE\" }\r\n  | { type: \"INITIALIZE_STEPS\"; payload: { count: number; startStep?: number } };\r\n\r\nconst initialState: DeploymentState = {\r\n  deploymentUrl: null,\r\n  currentStep: 0,\r\n  stepStatuses: [],\r\n  isDeploying: false,\r\n  isDisconnected: false,\r\n  deploymentInProgress: false,\r\n  shouldSolvedError: false,\r\n  removeDomain: false,\r\n  deploymentSocket: null,\r\n  vercelDeployModalError: {\r\n    step: \"\",\r\n    status: \"\",\r\n    error: \"\",\r\n    specific_error: \"\",\r\n    deployment_url: \"\",\r\n    logs: \"\",\r\n    detailed_logs: \"\",\r\n  },\r\n};\r\n\r\nfunction deploymentReducer(state: DeploymentState, action: DeploymentAction): DeploymentState {\r\n  switch (action.type) {\r\n    case \"SET_DEPLOYMENT_URL\":\r\n      return { ...state, deploymentUrl: action.payload };\r\n\r\n    case \"SET_CURRENT_STEP\":\r\n      return { ...state, currentStep: action.payload };\r\n\r\n    case \"UPDATE_STEP_STATUS\":\r\n      const newStepStatuses = [...state.stepStatuses];\r\n      newStepStatuses[action.payload.index] = action.payload.status;\r\n      return { ...state, stepStatuses: newStepStatuses };\r\n\r\n    case \"SET_IS_DEPLOYING\":\r\n      return { ...state, isDeploying: action.payload };\r\n\r\n    case \"SET_IS_DISCONNECTED\":\r\n      return { ...state, isDisconnected: action.payload };\r\n\r\n    case \"SET_DEPLOYMENT_IN_PROGRESS\":\r\n      return { ...state, deploymentInProgress: action.payload };\r\n\r\n    case \"SET_SHOULD_SOLVED_ERROR\":\r\n      return { ...state, shouldSolvedError: action.payload };\r\n\r\n    case \"SET_REMOVE_DOMAIN\":\r\n      return { ...state, removeDomain: action.payload };\r\n\r\n    case \"SET_DEPLOYMENT_SOCKET\":\r\n      return { ...state, deploymentSocket: action.payload };\r\n\r\n    case \"SET_VERCEL_DEPLOY_MODAL_ERROR\":\r\n      return { ...state, vercelDeployModalError: action.payload };\r\n\r\n    case \"INITIALIZE_STEPS\":\r\n      const { count, startStep = 0 } = action.payload;\r\n      return {\r\n        ...state,\r\n        stepStatuses: Array(count)\r\n          .fill(null)\r\n          .map((_, i) => ({\r\n            status: i < startStep ? \"completed\" : \"pending\",\r\n          })),\r\n        currentStep: startStep,\r\n      };\r\n\r\n    case \"RESET_STATE\":\r\n      return { ...initialState };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n}\r\n\r\nexport function useDeploymentState() {\r\n  const [state, dispatch] = useReducer(deploymentReducer, initialState);\r\n\r\n  const actions = {\r\n    setDeploymentUrl: useCallback((url: string | null) => {\r\n      dispatch({ type: \"SET_DEPLOYMENT_URL\", payload: url });\r\n    }, []),\r\n\r\n    setCurrentStep: useCallback((step: number) => {\r\n      dispatch({ type: \"SET_CURRENT_STEP\", payload: step });\r\n    }, []),\r\n\r\n    updateStepStatus: useCallback((index: number, status: StepStatus) => {\r\n      dispatch({ type: \"UPDATE_STEP_STATUS\", payload: { index, status } });\r\n    }, []),\r\n\r\n    setIsDeploying: useCallback((isDeploying: boolean) => {\r\n      dispatch({ type: \"SET_IS_DEPLOYING\", payload: isDeploying });\r\n    }, []),\r\n\r\n    setIsDisconnected: useCallback((isDisconnected: boolean) => {\r\n      dispatch({ type: \"SET_IS_DISCONNECTED\", payload: isDisconnected });\r\n    }, []),\r\n\r\n    setDeploymentInProgress: useCallback((inProgress: boolean) => {\r\n      dispatch({ type: \"SET_DEPLOYMENT_IN_PROGRESS\", payload: inProgress });\r\n    }, []),\r\n\r\n    setShouldSolvedError: useCallback((shouldSolve: boolean) => {\r\n      dispatch({ type: \"SET_SHOULD_SOLVED_ERROR\", payload: shouldSolve });\r\n    }, []),\r\n\r\n    setRemoveDomain: useCallback((remove: boolean) => {\r\n      dispatch({ type: \"SET_REMOVE_DOMAIN\", payload: remove });\r\n    }, []),\r\n\r\n    setDeploymentSocket: useCallback((socket: WebSocket | null) => {\r\n      dispatch({ type: \"SET_DEPLOYMENT_SOCKET\", payload: socket });\r\n    }, []),\r\n\r\n    setVercelDeployModalError: useCallback((error: DeploymentError) => {\r\n      dispatch({ type: \"SET_VERCEL_DEPLOY_MODAL_ERROR\", payload: error });\r\n    }, []),\r\n\r\n    initializeSteps: useCallback((count: number, startStep?: number) => {\r\n      dispatch({ type: \"INITIALIZE_STEPS\", payload: { count, startStep } });\r\n    }, []),\r\n\r\n    resetState: useCallback(() => {\r\n      dispatch({ type: \"RESET_STATE\" });\r\n    }, []),\r\n  };\r\n\r\n  return { state, actions };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AA6CA,MAAM,eAAgC;IACpC,eAAe;IACf,aAAa;IACb,cAAc,EAAE;IAChB,aAAa;IACb,gBAAgB;IAChB,sBAAsB;IACtB,mBAAmB;IACnB,cAAc;IACd,kBAAkB;IAClB,wBAAwB;QACtB,MAAM;QACN,QAAQ;QACR,OAAO;QACP,gBAAgB;QAChB,gBAAgB;QAChB,MAAM;QACN,eAAe;IACjB;AACF;AAEA,SAAS,kBAAkB,KAAsB,EAAE,MAAwB;IACzE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,eAAe,OAAO,OAAO;YAAC;QAEnD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QAEjD,KAAK;YACH,MAAM,kBAAkB;mBAAI,MAAM,YAAY;aAAC;YAC/C,eAAe,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,OAAO,CAAC,MAAM;YAC7D,OAAO;gBAAE,GAAG,KAAK;gBAAE,cAAc;YAAgB;QAEnD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QAEjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB,OAAO,OAAO;YAAC;QAEpD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,sBAAsB,OAAO,OAAO;YAAC;QAE1D,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,mBAAmB,OAAO,OAAO;YAAC;QAEvD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,cAAc,OAAO,OAAO;YAAC;QAElD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,kBAAkB,OAAO,OAAO;YAAC;QAEtD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,wBAAwB,OAAO,OAAO;YAAC;QAE5D,KAAK;YACH,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,GAAG,OAAO,OAAO;YAC/C,OAAO;gBACL,GAAG,KAAK;gBACR,cAAc,MAAM,OACjB,IAAI,CAAC,MACL,GAAG,CAAC,CAAC,GAAG,IAAM,CAAC;wBACd,QAAQ,IAAI,YAAY,cAAc;oBACxC,CAAC;gBACH,aAAa;YACf;QAEF,KAAK;YACH,OAAO;gBAAE,GAAG,YAAY;YAAC;QAE3B;YACE,OAAO;IACX;AACF;AAEO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,aAAU,AAAD,EAAE,mBAAmB;IAExD,MAAM,UAAU;QACd,kBAAkB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC7B,SAAS;gBAAE,MAAM;gBAAsB,SAAS;YAAI;QACtD,GAAG,EAAE;QAEL,gBAAgB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC3B,SAAS;gBAAE,MAAM;gBAAoB,SAAS;YAAK;QACrD,GAAG,EAAE;QAEL,kBAAkB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;YAC5C,SAAS;gBAAE,MAAM;gBAAsB,SAAS;oBAAE;oBAAO;gBAAO;YAAE;QACpE,GAAG,EAAE;QAEL,gBAAgB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC3B,SAAS;gBAAE,MAAM;gBAAoB,SAAS;YAAY;QAC5D,GAAG,EAAE;QAEL,mBAAmB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC9B,SAAS;gBAAE,MAAM;gBAAuB,SAAS;YAAe;QAClE,GAAG,EAAE;QAEL,yBAAyB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YACpC,SAAS;gBAAE,MAAM;gBAA8B,SAAS;YAAW;QACrE,GAAG,EAAE;QAEL,sBAAsB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YACjC,SAAS;gBAAE,MAAM;gBAA2B,SAAS;YAAY;QACnE,GAAG,EAAE;QAEL,iBAAiB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAC5B,SAAS;gBAAE,MAAM;gBAAqB,SAAS;YAAO;QACxD,GAAG,EAAE;QAEL,qBAAqB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YAChC,SAAS;gBAAE,MAAM;gBAAyB,SAAS;YAAO;QAC5D,GAAG,EAAE;QAEL,2BAA2B,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC;YACtC,SAAS;gBAAE,MAAM;gBAAiC,SAAS;YAAM;QACnE,GAAG,EAAE;QAEL,iBAAiB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;YAC3C,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAO;gBAAU;YAAE;QACrE,GAAG,EAAE;QAEL,YAAY,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;YACtB,SAAS;gBAAE,MAAM;YAAc;QACjC,GAAG,EAAE;IACP;IAEA,OAAO;QAAE;QAAO;IAAQ;AAC1B", "debugId": null}}, {"offset": {"line": 2997, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/parse-vercel-error.ts"], "sourcesContent": ["import { type DeploymentError } from \"./hooks/use-deployment-state\";\r\n\r\nexport function parseVercelError(data: DeploymentError) {\r\n  // Parse error type\r\n  const isBuildError =\r\n    data.detailed_logs?.includes(\"Build failed\") ||\r\n    data.error?.includes(\"build\") ||\r\n    data.specific_error?.includes(\"build\") ||\r\n    data.logs?.includes(\"vercel login\") ||\r\n    data.specific_error?.includes(\"Error: The specified token is not valid.\");\r\n\r\n  const isLintError =\r\n    data.detailed_logs?.includes(\"ESLint\") ||\r\n    data.detailed_logs?.includes(\"Parsing error\") ||\r\n    data.error?.includes(\"lint\") ||\r\n    data.specific_error?.includes(\"lint\") ||\r\n    data.logs?.includes(\"vercel login\") ||\r\n    data.specific_error?.includes(\"Error: The specified token is not valid.\");\r\n\r\n  // Parse error details\r\n  const errorDetails = {\r\n    title: data.error || \"Deployment Error\",\r\n    message:\r\n      data.specific_error?.replace(/^Error: */, \"\") || data.error || \"An unknown error occurred\",\r\n    solution: \"\",\r\n    learnMoreUrl: \"\",\r\n  };\r\n\r\n  // Extract solution and learn more URL if available\r\n  if (data.specific_error) {\r\n    const learnMoreMatch = data.specific_error.match(/Learn More: (https:\\/\\/[^\\s]+)/);\r\n    if (learnMoreMatch) {\r\n      errorDetails.learnMoreUrl = learnMoreMatch[1];\r\n      errorDetails.solution = data.specific_error\r\n        .replace(/Learn More:.*$/, \"\")\r\n        .replace(/^Error: */, \"\")\r\n        .trim();\r\n    } else {\r\n      errorDetails.solution = data.specific_error.replace(/^Error: */, \"\").trim();\r\n    }\r\n  }\r\n\r\n  return {\r\n    isBuildError,\r\n    isLintError,\r\n    canAutoFix: isBuildError || isLintError,\r\n    errorDetails,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEO,SAAS,iBAAiB,IAAqB;IACpD,mBAAmB;IACnB,MAAM,eACJ,KAAK,aAAa,EAAE,SAAS,mBAC7B,KAAK,KAAK,EAAE,SAAS,YACrB,KAAK,cAAc,EAAE,SAAS,YAC9B,KAAK,IAAI,EAAE,SAAS,mBACpB,KAAK,cAAc,EAAE,SAAS;IAEhC,MAAM,cACJ,KAAK,aAAa,EAAE,SAAS,aAC7B,KAAK,aAAa,EAAE,SAAS,oBAC7B,KAAK,KAAK,EAAE,SAAS,WACrB,KAAK,cAAc,EAAE,SAAS,WAC9B,KAAK,IAAI,EAAE,SAAS,mBACpB,KAAK,cAAc,EAAE,SAAS;IAEhC,sBAAsB;IACtB,MAAM,eAAe;QACnB,OAAO,KAAK,KAAK,IAAI;QACrB,SACE,KAAK,cAAc,EAAE,QAAQ,aAAa,OAAO,KAAK,KAAK,IAAI;QACjE,UAAU;QACV,cAAc;IAChB;IAEA,mDAAmD;IACnD,IAAI,KAAK,cAAc,EAAE;QACvB,MAAM,iBAAiB,KAAK,cAAc,CAAC,KAAK,CAAC;QACjD,IAAI,gBAAgB;YAClB,aAAa,YAAY,GAAG,cAAc,CAAC,EAAE;YAC7C,aAAa,QAAQ,GAAG,KAAK,cAAc,CACxC,OAAO,CAAC,kBAAkB,IAC1B,OAAO,CAAC,aAAa,IACrB,IAAI;QACT,OAAO;YACL,aAAa,QAAQ,GAAG,KAAK,cAAc,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI;QAC3E;IACF;IAEA,OAAO;QACL;QACA;QACA,YAAY,gBAAgB;QAC5B;IACF;AACF", "debugId": null}}, {"offset": {"line": 3034, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/hooks/use-websocket-handler.ts"], "sourcesContent": ["import { errorToast, successToast } from \"@/features/global/toast\";\r\nimport { connectVercelDeployWebSocket } from \"@/lib/api\";\r\nimport { debug } from \"@/lib/debug\";\r\nimport { useCallback } from \"react\";\r\nimport { parseVercelError } from \"../parse-vercel-error\";\r\nimport { DeploymentError, StepStatus } from \"./use-deployment-state\";\r\n\r\nconst stepMapping = {\r\n  setup_cli: 0,\r\n  login: 1,\r\n  link: 2,\r\n  env_push: 3,\r\n  deploy: 4,\r\n};\r\n\r\ninterface WebSocketConfig {\r\n  projectId: string;\r\n  vercelToken: string | null;\r\n  startStep: number;\r\n}\r\n\r\ninterface WebSocketHandlers {\r\n  updateStepStatus: (index: number, status: StepStatus) => void;\r\n  setCurrentStep: (step: number) => void;\r\n  setDeploymentUrl: (url: string | null) => void;\r\n  setVercelDeployModalError: (error: DeploymentError) => void;\r\n  setIsDeploying: (isDeploying: boolean) => void;\r\n  setDeploymentInProgress: (inProgress: boolean) => void;\r\n  setIsDisconnected: (isDisconnected: boolean) => void;\r\n  setShouldSolvedError: (shouldSolve: boolean) => void;\r\n  onDeploymentComplete: () => void;\r\n}\r\n\r\nexport function useVercelDeployWebSocketHandler(handlers: WebSocketHandlers) {\r\n  const {\r\n    updateStepStatus,\r\n    setCurrentStep,\r\n    setDeploymentUrl,\r\n    setVercelDeployModalError,\r\n    setIsDeploying,\r\n    setDeploymentInProgress,\r\n    setIsDisconnected,\r\n    setShouldSolvedError,\r\n    onDeploymentComplete,\r\n  } = handlers;\r\n\r\n  const handleWebSocketMessage = useCallback(\r\n    (socket: WebSocket, stepsLength: number) => (event: MessageEvent) => {\r\n      try {\r\n        const data = JSON.parse(event.data);\r\n\r\n        // Handle ping/pong messages\r\n        if (data.type === \"ping\") {\r\n          socket.send(JSON.stringify({ type: \"pong\", timestamp: data.timestamp }));\r\n          return;\r\n        }\r\n\r\n        // Handle errors\r\n        if (data.specific_error) {\r\n          debug(\"data.specific_error\", data.specific_error);\r\n\r\n          const errorDetails = parseVercelError({\r\n            step: data.step || \"\",\r\n            status: data.status || \"\",\r\n            error: data.error || \"\",\r\n            specific_error: data.specific_error,\r\n            deployment_url: data.deployment_url || \"\",\r\n            logs: data.logs || \"\",\r\n            detailed_logs: data.detailed_logs || \"\",\r\n          });\r\n\r\n          setVercelDeployModalError({\r\n            ...data,\r\n            canAutoFix: errorDetails.canAutoFix,\r\n          });\r\n\r\n          updateStepStatus(stepMapping[data.step as keyof typeof stepMapping] || 0, {\r\n            status: \"failed\",\r\n          });\r\n\r\n          if (errorDetails.canAutoFix) {\r\n            setShouldSolvedError(true);\r\n          }\r\n\r\n          setDeploymentInProgress(false);\r\n          return;\r\n        }\r\n\r\n        // Handle step updates\r\n        if (data.step && data.status) {\r\n          const stepIndex = stepMapping[data.step as keyof typeof stepMapping] || 0;\r\n\r\n          switch (data.status) {\r\n            case \"started\":\r\n              updateStepStatus(stepIndex, { status: \"pending\", message: data.message });\r\n              setCurrentStep(stepIndex);\r\n              break;\r\n\r\n            case \"completed\":\r\n              updateStepStatus(stepIndex, { status: \"completed\", message: data.message });\r\n\r\n              if (stepIndex < stepsLength - 1) {\r\n                setCurrentStep(stepIndex + 1);\r\n              }\r\n\r\n              if (data.step === \"deploy\" || data.step === 4) {\r\n                if (data.deployment_url) {\r\n                  setDeploymentUrl(data.deployment_url);\r\n                }\r\n                setIsDeploying(false);\r\n                setDeploymentInProgress(false);\r\n                successToast(\"Deployment completed successfully!\");\r\n                onDeploymentComplete();\r\n              }\r\n              break;\r\n\r\n            case \"failed\":\r\n              updateStepStatus(stepIndex, { status: \"failed\" });\r\n              debug(\"data.error 2nd\", data.error);\r\n              setVercelDeployModalError({\r\n                step: data.step,\r\n                status: data.status,\r\n                error: data.error,\r\n                specific_error: data.specific_error,\r\n                deployment_url: data.deployment_url,\r\n                logs: data?.logs || \"\",\r\n                detailed_logs: data?.detailed_logs || \"\",\r\n              });\r\n              setShouldSolvedError(true);\r\n              setIsDeploying(false);\r\n              setDeploymentInProgress(false);\r\n              break;\r\n          }\r\n        }\r\n\r\n        // Handle deployment URL\r\n        if (data.deployment_url) {\r\n          setDeploymentUrl(data.deployment_url);\r\n        }\r\n\r\n        // Handle logs\r\n        if (data.logs) {\r\n          debug(\"data.logs\", data.logs);\r\n        }\r\n\r\n        debug(\"data\", data);\r\n      } catch (error) {\r\n        console.error(\"Error processing WebSocket message:\", error);\r\n      }\r\n    },\r\n    [\r\n      updateStepStatus,\r\n      setCurrentStep,\r\n      setDeploymentUrl,\r\n      setVercelDeployModalError,\r\n      setIsDeploying,\r\n      setDeploymentInProgress,\r\n      setShouldSolvedError,\r\n      onDeploymentComplete,\r\n    ],\r\n  );\r\n\r\n  const handleWebSocketError = useCallback(\r\n    (error: Event) => {\r\n      console.error(\"WebSocket error:\", error);\r\n      setIsDisconnected(true);\r\n      errorToast(\"Lost connection to deployment server. Please try again.\");\r\n    },\r\n    [setIsDisconnected],\r\n  );\r\n\r\n  const handleWebSocketClose = useCallback(\r\n    (deploymentInProgress: boolean) => (event: CloseEvent) => {\r\n      debug(\"WebSocket closed:\", event.code, event.reason);\r\n      setIsDisconnected(true);\r\n      if (deploymentInProgress) {\r\n        errorToast(\"Connection closed. Your deployment might still be in progress.\");\r\n      }\r\n    },\r\n    [setIsDisconnected],\r\n  );\r\n\r\n  const setupWebSocket = useCallback(\r\n    (config: WebSocketConfig, stepsLength: number, deploymentInProgress: boolean) => {\r\n      const socket = connectVercelDeployWebSocket(config);\r\n\r\n      socket.onmessage = handleWebSocketMessage(socket, stepsLength);\r\n      socket.onerror = handleWebSocketError;\r\n      socket.onclose = handleWebSocketClose(deploymentInProgress);\r\n\r\n      return socket;\r\n    },\r\n    [handleWebSocketMessage, handleWebSocketError, handleWebSocketClose],\r\n  );\r\n\r\n  return { setupWebSocket };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGA,MAAM,cAAc;IAClB,WAAW;IACX,OAAO;IACP,MAAM;IACN,UAAU;IACV,QAAQ;AACV;AAoBO,SAAS,gCAAgC,QAA2B;IACzE,MAAM,EACJ,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,yBAAyB,EACzB,cAAc,EACd,uBAAuB,EACvB,iBAAiB,EACjB,oBAAoB,EACpB,oBAAoB,EACrB,GAAG;IAEJ,MAAM,yBAAyB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EACvC,CAAC,QAAmB,cAAwB,CAAC;YAC3C,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAElC,4BAA4B;gBAC5B,IAAI,KAAK,IAAI,KAAK,QAAQ;oBACxB,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC;wBAAE,MAAM;wBAAQ,WAAW,KAAK,SAAS;oBAAC;oBACrE;gBACF;gBAEA,gBAAgB;gBAChB,IAAI,KAAK,cAAc,EAAE;oBACvB,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,uBAAuB,KAAK,cAAc;oBAEhD,MAAM,eAAe,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD,EAAE;wBACpC,MAAM,KAAK,IAAI,IAAI;wBACnB,QAAQ,KAAK,MAAM,IAAI;wBACvB,OAAO,KAAK,KAAK,IAAI;wBACrB,gBAAgB,KAAK,cAAc;wBACnC,gBAAgB,KAAK,cAAc,IAAI;wBACvC,MAAM,KAAK,IAAI,IAAI;wBACnB,eAAe,KAAK,aAAa,IAAI;oBACvC;oBAEA,0BAA0B;wBACxB,GAAG,IAAI;wBACP,YAAY,aAAa,UAAU;oBACrC;oBAEA,iBAAiB,WAAW,CAAC,KAAK,IAAI,CAA6B,IAAI,GAAG;wBACxE,QAAQ;oBACV;oBAEA,IAAI,aAAa,UAAU,EAAE;wBAC3B,qBAAqB;oBACvB;oBAEA,wBAAwB;oBACxB;gBACF;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,EAAE;oBAC5B,MAAM,YAAY,WAAW,CAAC,KAAK,IAAI,CAA6B,IAAI;oBAExE,OAAQ,KAAK,MAAM;wBACjB,KAAK;4BACH,iBAAiB,WAAW;gCAAE,QAAQ;gCAAW,SAAS,KAAK,OAAO;4BAAC;4BACvE,eAAe;4BACf;wBAEF,KAAK;4BACH,iBAAiB,WAAW;gCAAE,QAAQ;gCAAa,SAAS,KAAK,OAAO;4BAAC;4BAEzE,IAAI,YAAY,cAAc,GAAG;gCAC/B,eAAe,YAAY;4BAC7B;4BAEA,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,GAAG;gCAC7C,IAAI,KAAK,cAAc,EAAE;oCACvB,iBAAiB,KAAK,cAAc;gCACtC;gCACA,eAAe;gCACf,wBAAwB;gCACxB,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;gCACb;4BACF;4BACA;wBAEF,KAAK;4BACH,iBAAiB,WAAW;gCAAE,QAAQ;4BAAS;4BAC/C,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,kBAAkB,KAAK,KAAK;4BAClC,0BAA0B;gCACxB,MAAM,KAAK,IAAI;gCACf,QAAQ,KAAK,MAAM;gCACnB,OAAO,KAAK,KAAK;gCACjB,gBAAgB,KAAK,cAAc;gCACnC,gBAAgB,KAAK,cAAc;gCACnC,MAAM,MAAM,QAAQ;gCACpB,eAAe,MAAM,iBAAiB;4BACxC;4BACA,qBAAqB;4BACrB,eAAe;4BACf,wBAAwB;4BACxB;oBACJ;gBACF;gBAEA,wBAAwB;gBACxB,IAAI,KAAK,cAAc,EAAE;oBACvB,iBAAiB,KAAK,cAAc;gBACtC;gBAEA,cAAc;gBACd,IAAI,KAAK,IAAI,EAAE;oBACb,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,aAAa,KAAK,IAAI;gBAC9B;gBAEA,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,QAAQ;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,MAAM,uBAAuB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EACrC,CAAC;QACC,QAAQ,KAAK,CAAC,oBAAoB;QAClC,kBAAkB;QAClB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;IACb,GACA;QAAC;KAAkB;IAGrB,MAAM,uBAAuB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EACrC,CAAC,uBAAkC,CAAC;YAClC,CAAA,GAAA,mHAAA,CAAA,QAAK,AAAD,EAAE,qBAAqB,MAAM,IAAI,EAAE,MAAM,MAAM;YACnD,kBAAkB;YAClB,IAAI,sBAAsB;gBACxB,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YACb;QACF,GACA;QAAC;KAAkB;IAGrB,MAAM,iBAAiB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC/B,CAAC,QAAyB,aAAqB;QAC7C,MAAM,SAAS,CAAA,GAAA,iHAAA,CAAA,+BAA4B,AAAD,EAAE;QAE5C,OAAO,SAAS,GAAG,uBAAuB,QAAQ;QAClD,OAAO,OAAO,GAAG;QACjB,OAAO,OAAO,GAAG,qBAAqB;QAEtC,OAAO;IACT,GACA;QAAC;QAAwB;QAAsB;KAAqB;IAGtE,OAAO;QAAE;IAAe;AAC1B", "debugId": null}}, {"offset": {"line": 3200, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/Softgen/softgen-platform/src/features/project/publish/index.tsx"], "sourcesContent": ["import {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { errorToast, successToast } from \"@/features/global/toast\";\r\nimport { useThread } from \"@/hooks/use-threads\";\r\nimport {\r\n  setupVercelCLI,\r\n  vercelDeploy,\r\n  vercelEnvPush,\r\n  vercelLink,\r\n  vercelLogin,\r\n  vercelUnlink,\r\n} from \"@/lib/api\";\r\nimport { useAuth } from \"@/providers/auth-provider\";\r\nimport { useProject } from \"@/providers/project-provider\";\r\nimport { useSettingsStore } from \"@/stores/settings-tab\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { DeployedProjectView } from \"./components/deployed-project-view\";\r\nimport { DeploymentProgress } from \"./components/deployment-progress\";\r\nimport DeploymentSuccess from \"./components/deployment-success\";\r\nimport NotPublished from \"./components/not-published\";\r\nimport { useDeploymentState } from \"./hooks/use-deployment-state\";\r\nimport { useVercelDeployWebSocketHandler } from \"./hooks/use-websocket-handler\";\r\nimport { parseVercelError } from \"./parse-vercel-error\";\r\nimport { publishProjectSchema } from \"./type\";\r\n\r\nconst formattedMessage = (message: string) => {\r\n  // Escape special characters and handle multiline content\r\n  const escapedMessage = message\r\n    .replace(/\"/g, \"&quot;\")\r\n    .replace(/\\n/g, \"\\\\n\")\r\n    .replace(/\\r/g, \"\\\\r\");\r\n  const vercelErrorTag = `<vercel_error error=\"${escapedMessage}\" />`;\r\n  return `Help me fix this Vercel deployment error ${vercelErrorTag}`;\r\n};\r\n\r\nconst PublishProject = () => {\r\n  const queryClient = useQueryClient();\r\n  const { project, refetch } = useProject();\r\n  const { state: deploymentState, actions: deploymentActions } = useDeploymentState();\r\n  const [isDeploymentComplete, setIsDeploymentComplete] = useState(false);\r\n  const { sendMessage } = useThread();\r\n  const [showPublishPopover, setShowPublishPopover] = useState(false);\r\n  const [showDeploymentModal, setShowDeploymentModal] = useState(false);\r\n  const [isDomainRemoved, setIsDomainRemoved] = useState(false);\r\n  const [isRedeploying, setIsRedeploying] = useState(false);\r\n  const [isRedeploy, setIsRedeploy] = useState(false);\r\n  const [showDeploymentProgressModal, setShowDeploymentProgressModal] = useState(false);\r\n  const setSettingsTab = useSettingsStore((state) => state.setSettingsTab);\r\n\r\n  const { user } = useAuth();\r\n  const isFreeTier = user?.userFromDb?.plan === \"free-tier\";\r\n\r\n  const steps = useMemo(\r\n    () => [\r\n      { name: \"Setup Vercel CLI\", action: setupVercelCLI },\r\n      { name: \"Vercel Login\", action: vercelLogin },\r\n      { name: \"Link Project\", action: vercelLink },\r\n      { name: \"Push Environment Variables\", action: vercelEnvPush },\r\n      { name: \"Deploy to Vercel\", action: vercelDeploy },\r\n    ],\r\n    [],\r\n  );\r\n\r\n  const invalidateQueries = useCallback(() => {\r\n    queryClient.invalidateQueries({\r\n      queryKey: [\"project\", project?.project_id],\r\n      refetchType: \"active\",\r\n    });\r\n    queryClient.invalidateQueries({\r\n      queryKey: [\"get-project\", project?.project_id],\r\n      refetchType: \"active\",\r\n    });\r\n    refetch?.();\r\n  }, [queryClient, project?.project_id, refetch]);\r\n\r\n  const onDeploymentComplete = useCallback(() => {\r\n    invalidateQueries();\r\n    setShowPublishPopover(false);\r\n    setIsDomainRemoved(false);\r\n\r\n    if (!isRedeploy) {\r\n      setIsDeploymentComplete(true);\r\n      setShowDeploymentModal(false);\r\n    } else {\r\n      successToast(\"Project updated and redeployed successfully\");\r\n      setShowDeploymentModal(true);\r\n      setIsRedeploy(false);\r\n      setIsRedeploying(false);\r\n    }\r\n  }, [invalidateQueries, isRedeploy]);\r\n\r\n  const publishProjectForm = useForm<z.infer<typeof publishProjectSchema>>({\r\n    resolver: zodResolver(publishProjectSchema),\r\n    defaultValues: {\r\n      ownVercelDeployment: false,\r\n      vercelToken: project?.deployment?.vercel_token || \"\",\r\n      pushEnvBeforeDeploy: false,\r\n      domain: project?.deployment?.deployment_url,\r\n    },\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (project?.deployment?.vercel_token) {\r\n      publishProjectForm.setValue(\"ownVercelDeployment\", true);\r\n      publishProjectForm.setValue(\"vercelToken\", project?.deployment?.vercel_token);\r\n    }\r\n  }, [project?.deployment?.vercel_token, publishProjectForm]);\r\n\r\n  const webSocketHandlers = useMemo(\r\n    () => ({\r\n      updateStepStatus: deploymentActions.updateStepStatus,\r\n      setCurrentStep: deploymentActions.setCurrentStep,\r\n      setDeploymentUrl: deploymentActions.setDeploymentUrl,\r\n      setVercelDeployModalError: deploymentActions.setVercelDeployModalError,\r\n      setIsDeploying: deploymentActions.setIsDeploying,\r\n      setDeploymentInProgress: deploymentActions.setDeploymentInProgress,\r\n      setIsDisconnected: deploymentActions.setIsDisconnected,\r\n      setShouldSolvedError: deploymentActions.setShouldSolvedError,\r\n      onDeploymentComplete,\r\n    }),\r\n    [deploymentActions, onDeploymentComplete],\r\n  );\r\n\r\n  const { setupWebSocket } = useVercelDeployWebSocketHandler(webSocketHandlers);\r\n\r\n  const unlinkMutation = useMutation({\r\n    mutationFn: async (projectId: string) => {\r\n      return vercelUnlink(projectId);\r\n    },\r\n    onSuccess: () => {\r\n      invalidateQueries();\r\n      setShowDeploymentModal(false);\r\n      setIsDomainRemoved(true);\r\n      setShowPublishPopover(false);\r\n      deploymentActions.setRemoveDomain(false);\r\n      deploymentActions.setIsDeploying(false);\r\n      setShowDeploymentProgressModal(false);\r\n    },\r\n    onError: (error) => {\r\n      errorToast(\r\n        `Failed to unlink project: ${error instanceof Error ? error.message : String(error)}`,\r\n      );\r\n      deploymentActions.setRemoveDomain(false);\r\n    },\r\n  });\r\n\r\n  const handleUnlinkClick = useCallback(() => {\r\n    unlinkMutation.mutate(project?.project_id || \"\");\r\n  }, [project?.project_id, unlinkMutation]);\r\n\r\n  const handleSolveDeploymentError = useCallback(() => {\r\n    setShowDeploymentModal(false);\r\n    deploymentActions.setIsDeploying(false);\r\n    deploymentActions.setShouldSolvedError(false);\r\n\r\n    const vercelDeploymentError =\r\n      deploymentState.vercelDeployModalError.detailed_logs ||\r\n      `${deploymentState.vercelDeployModalError.specific_error}\\n\\n${deploymentState.vercelDeployModalError.logs}`;\r\n\r\n    const formattedMessageResult = formattedMessage(vercelDeploymentError);\r\n    sendMessage({ content: formattedMessageResult });\r\n  }, [deploymentActions, deploymentState.vercelDeployModalError]);\r\n\r\n  const handleCancelDeployment = useCallback(() => {\r\n    if (deploymentState.deploymentSocket) {\r\n      deploymentState.deploymentSocket.close();\r\n      deploymentActions.setDeploymentSocket(null);\r\n    }\r\n    deploymentActions.setIsDeploying(false);\r\n    deploymentActions.setDeploymentInProgress(false);\r\n    setShowDeploymentProgressModal(false);\r\n    setShowPublishPopover(true);\r\n  }, [deploymentState.deploymentSocket, deploymentActions]);\r\n\r\n  const parsedDeploymentError = useMemo(\r\n    () => parseVercelError(deploymentState.vercelDeployModalError),\r\n    [deploymentState.vercelDeployModalError],\r\n  );\r\n\r\n  const deployMutation = useMutation({\r\n    mutationFn: async (data: z.infer<typeof publishProjectSchema>) => {\r\n      if (!project?.project_id) throw new Error(\"Project ID is required\");\r\n\r\n      const { ownVercelDeployment, vercelToken, pushEnvBeforeDeploy } = data;\r\n\r\n      deploymentActions.setIsDeploying(true);\r\n      deploymentActions.setIsDisconnected(false);\r\n      deploymentActions.setDeploymentInProgress(true);\r\n      setShowDeploymentProgressModal(true);\r\n      setShowPublishPopover(false);\r\n      setShowDeploymentModal(false);\r\n      deploymentActions.setVercelDeployModalError({\r\n        step: \"\",\r\n        status: \"\",\r\n        error: \"\",\r\n        specific_error: \"\",\r\n        deployment_url: \"\",\r\n        logs: \"\",\r\n        detailed_logs: \"\",\r\n      });\r\n\r\n      let startStep = 0;\r\n      if (project?.deployment?.is_deployed) {\r\n        startStep = pushEnvBeforeDeploy ? 3 : 4;\r\n      }\r\n\r\n      deploymentActions.initializeSteps(steps.length, startStep);\r\n\r\n      if (deploymentState.deploymentSocket) {\r\n        deploymentState.deploymentSocket.close();\r\n        deploymentActions.setDeploymentSocket(null);\r\n      }\r\n\r\n      const socket = setupWebSocket(\r\n        {\r\n          projectId: project.project_id,\r\n          vercelToken: ownVercelDeployment ? vercelToken || null : null,\r\n          startStep,\r\n        },\r\n        steps.length,\r\n        deploymentState.deploymentInProgress,\r\n      );\r\n\r\n      deploymentActions.setDeploymentSocket(socket);\r\n      return { success: true };\r\n    },\r\n    onSuccess: () => {\r\n      // Don't invalidate here, let onDeploymentComplete handle it\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Deployment error\", error);\r\n      deploymentActions.setIsDeploying(false);\r\n      deploymentActions.setShouldSolvedError(true);\r\n      setIsRedeploying(false);\r\n      setIsRedeploy(false);\r\n      errorToast(`Deployment failed: ${error instanceof Error ? error.message : String(error)}`);\r\n    },\r\n  });\r\n\r\n  const handleDeploymentSuccessClose = useCallback(\r\n    (open: boolean) => {\r\n      if (!open) {\r\n        setIsDeploymentComplete(false);\r\n        deploymentActions.setIsDeploying(false);\r\n        deploymentActions.setDeploymentInProgress(false);\r\n        setShowDeploymentProgressModal(false);\r\n        invalidateQueries();\r\n        setTimeout(() => {\r\n          setShowDeploymentModal(true);\r\n          refetch?.();\r\n        }, 200);\r\n      }\r\n    },\r\n    [refetch, deploymentActions, invalidateQueries],\r\n  );\r\n\r\n  const handlePublishClick = useCallback(() => {\r\n    if (deploymentState.isDeploying) {\r\n      setShowDeploymentProgressModal(true);\r\n    } else {\r\n      setShowDeploymentModal(true);\r\n      setShowPublishPopover(true);\r\n    }\r\n  }, [deploymentState.isDeploying]);\r\n\r\n  const handleDeploymentModalChange = useCallback(\r\n    (open: boolean) => {\r\n      setShowDeploymentModal(open);\r\n      if (!open) {\r\n        if (!isRedeploying) {\r\n          setIsRedeploying(false);\r\n          setIsRedeploy(false);\r\n        }\r\n        invalidateQueries();\r\n        setShowDeploymentProgressModal(false);\r\n      }\r\n    },\r\n    [isRedeploying, invalidateQueries],\r\n  );\r\n\r\n  const handleNotPublishedOpenChange = useCallback(\r\n    (open: boolean) => {\r\n      if (!open) {\r\n        setShowPublishPopover(false);\r\n      } else {\r\n        setShowPublishPopover(true);\r\n      }\r\n\r\n      if (open && deploymentState.isDeploying) {\r\n        setShowDeploymentProgressModal(true);\r\n        setShowPublishPopover(false);\r\n      }\r\n    },\r\n    [deploymentState.isDeploying],\r\n  );\r\n\r\n  if (isDeploymentComplete) {\r\n    return (\r\n      <DeploymentSuccess\r\n        deploymentUrl={deploymentState.deploymentUrl || \"https://softgen.ai\"}\r\n        open={true}\r\n        onOpenChange={handleDeploymentSuccessClose}\r\n        project={{\r\n          name: project?.name || \"\",\r\n          preview_url: deploymentState.deploymentUrl || \"\",\r\n        }}\r\n      />\r\n    );\r\n  }\r\n\r\n  if (isFreeTier) {\r\n    return (\r\n      <Button\r\n        variant=\"default\"\r\n        size=\"sm\"\r\n        className=\"ml-auto w-fit\"\r\n        onClick={() => setSettingsTab(\"publish\")}\r\n      >\r\n        Upgrade to publish\r\n      </Button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {!project?.deployment?.is_deployed || deploymentState.isDeploying || isDomainRemoved ? (\r\n        <NotPublished\r\n          open={showPublishPopover}\r\n          onOpenChange={handleNotPublishedOpenChange}\r\n          startDeployment={(data) => {\r\n            deployMutation.mutate(data);\r\n          }}\r\n          isDeploying={deploymentState.isDeploying}\r\n        />\r\n      ) : (\r\n        <Button\r\n          variant=\"default\"\r\n          size=\"sm\"\r\n          className=\"ml-auto w-fit\"\r\n          disabled={false}\r\n          onClick={handlePublishClick}\r\n        >\r\n          Publish\r\n        </Button>\r\n      )}\r\n\r\n      {deploymentState.isDeploying && (\r\n        <DeploymentProgress\r\n          steps={steps}\r\n          stepStatuses={deploymentState.stepStatuses}\r\n          currentStep={deploymentState.currentStep}\r\n          isDeploying={deploymentState.isDeploying}\r\n          onCancelDeployment={handleCancelDeployment}\r\n          open={showDeploymentProgressModal}\r\n          onOpenChange={setShowDeploymentProgressModal}\r\n        />\r\n      )}\r\n\r\n      {showDeploymentModal && (\r\n        <DeployedProjectView\r\n          form={publishProjectForm}\r\n          onUnlink={handleUnlinkClick}\r\n          onRedeploy={() => deployMutation.mutate(publishProjectForm.getValues())}\r\n          invalidateQueries={invalidateQueries}\r\n          setStartDeployment={handlePublishClick}\r\n          isRemovingDomain={unlinkMutation.isPending}\r\n          open={showDeploymentModal}\r\n          onOpenChange={handleDeploymentModalChange}\r\n          isRedeploying={deploymentState.isDeploying}\r\n          setIsRedeploying={setIsRedeploying}\r\n          setIsRedeploy={setIsRedeploy}\r\n          vercelToken={project?.deployment?.vercel_token || null}\r\n        />\r\n      )}\r\n\r\n      <AlertDialog\r\n        open={deploymentState.shouldSolvedError}\r\n        onOpenChange={deploymentActions.setShouldSolvedError}\r\n      >\r\n        <AlertDialogContent className=\"max-h-[80vh] max-w-3xl overflow-y-auto\">\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Deployment Error</AlertDialogTitle>\r\n            <AlertDialogDescription className=\"space-y-4\">\r\n              <p className=\"font-semibold\">\r\n                {project?.deployment?.is_deployed ? \"Redeployment\" : \"Deployment\"} failed with the\r\n                following details:\r\n              </p>\r\n              <pre className=\"max-h-[300px] space-y-2 overflow-auto whitespace-pre-wrap rounded-md bg-muted p-3 font-mono text-xs\">\r\n                {deploymentState.vercelDeployModalError.detailed_logs ||\r\n                  `${deploymentState.vercelDeployModalError.specific_error}\\n\\n${deploymentState.vercelDeployModalError.logs}`}\r\n              </pre>\r\n              {parsedDeploymentError.isBuildError && (\r\n                <p>\r\n                  This appears to be a {parsedDeploymentError.isLintError ? \"lint\" : \"build\"} error\r\n                  that can be fixed automatically. Would you like to solve this error in a thread?\r\n                </p>\r\n              )}\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter className=\"flex items-center justify-end\">\r\n            <AlertDialogCancel\r\n              className=\"w-full md:w-fit\"\r\n              onClick={() => deploymentActions.setShouldSolvedError(false)}\r\n            >\r\n              Cancel\r\n            </AlertDialogCancel>\r\n            {parsedDeploymentError.canAutoFix && (\r\n              <AlertDialogAction\r\n                onClick={handleSolveDeploymentError}\r\n                className=\"h-8 w-full bg-blue-600 text-white hover:bg-blue-700 md:w-fit\"\r\n              >\r\n                Fix Error\r\n              </AlertDialogAction>\r\n            )}\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PublishProject;\r\n"], "names": [], "mappings": ";;;;AAAA;AAUA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,mBAAmB,CAAC;IACxB,yDAAyD;IACzD,MAAM,iBAAiB,QACpB,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,OAAO,OACf,OAAO,CAAC,OAAO;IAClB,MAAM,iBAAiB,CAAC,qBAAqB,EAAE,eAAe,IAAI,CAAC;IACnE,OAAO,CAAC,yCAAyC,EAAE,gBAAgB;AACrE;AAEA,MAAM,iBAAiB;IACrB,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IACtC,MAAM,EAAE,OAAO,eAAe,EAAE,SAAS,iBAAiB,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD;IAChF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD;IAChC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/E,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC,QAAU,MAAM,cAAc;IAEvE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,aAAa,MAAM,YAAY,SAAS;IAE9C,MAAM,QAAQ,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAClB,IAAM;YACJ;gBAAE,MAAM;gBAAoB,QAAQ,iHAAA,CAAA,iBAAc;YAAC;YACnD;gBAAE,MAAM;gBAAgB,QAAQ,iHAAA,CAAA,cAAW;YAAC;YAC5C;gBAAE,MAAM;gBAAgB,QAAQ,iHAAA,CAAA,aAAU;YAAC;YAC3C;gBAAE,MAAM;gBAA8B,QAAQ,iHAAA,CAAA,gBAAa;YAAC;YAC5D;gBAAE,MAAM;gBAAoB,QAAQ,iHAAA,CAAA,eAAY;YAAC;SAClD,EACD,EAAE;IAGJ,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACpC,YAAY,iBAAiB,CAAC;YAC5B,UAAU;gBAAC;gBAAW,SAAS;aAAW;YAC1C,aAAa;QACf;QACA,YAAY,iBAAiB,CAAC;YAC5B,UAAU;gBAAC;gBAAe,SAAS;aAAW;YAC9C,aAAa;QACf;QACA;IACF,GAAG;QAAC;QAAa,SAAS;QAAY;KAAQ;IAE9C,MAAM,uBAAuB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACvC;QACA,sBAAsB;QACtB,mBAAmB;QAEnB,IAAI,CAAC,YAAY;YACf,wBAAwB;YACxB,uBAAuB;QACzB,OAAO;YACL,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;YACb,uBAAuB;YACvB,cAAc;YACd,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAmB;KAAW;IAElC,MAAM,qBAAqB,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAwC;QACvE,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE,6IAAA,CAAA,uBAAoB;QAC1C,eAAe;YACb,qBAAqB;YACrB,aAAa,SAAS,YAAY,gBAAgB;YAClD,qBAAqB;YACrB,QAAQ,SAAS,YAAY;QAC/B;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,YAAY,cAAc;YACrC,mBAAmB,QAAQ,CAAC,uBAAuB;YACnD,mBAAmB,QAAQ,CAAC,eAAe,SAAS,YAAY;QAClE;IACF,GAAG;QAAC,SAAS,YAAY;QAAc;KAAmB;IAE1D,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAC9B,IAAM,CAAC;YACL,kBAAkB,kBAAkB,gBAAgB;YACpD,gBAAgB,kBAAkB,cAAc;YAChD,kBAAkB,kBAAkB,gBAAgB;YACpD,2BAA2B,kBAAkB,yBAAyB;YACtE,gBAAgB,kBAAkB,cAAc;YAChD,yBAAyB,kBAAkB,uBAAuB;YAClE,mBAAmB,kBAAkB,iBAAiB;YACtD,sBAAsB,kBAAkB,oBAAoB;YAC5D;QACF,CAAC,GACD;QAAC;QAAmB;KAAqB;IAG3C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,kCAA+B,AAAD,EAAE;IAE3D,MAAM,iBAAiB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY,OAAO;YACjB,OAAO,CAAA,GAAA,iHAAA,CAAA,eAAY,AAAD,EAAE;QACtB;QACA,WAAW;YACT;YACA,uBAAuB;YACvB,mBAAmB;YACnB,sBAAsB;YACtB,kBAAkB,eAAe,CAAC;YAClC,kBAAkB,cAAc,CAAC;YACjC,+BAA+B;QACjC;QACA,SAAS,CAAC;YACR,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EACP,CAAC,0BAA0B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;YAEvF,kBAAkB,eAAe,CAAC;QACpC;IACF;IAEA,MAAM,oBAAoB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACpC,eAAe,MAAM,CAAC,SAAS,cAAc;IAC/C,GAAG;QAAC,SAAS;QAAY;KAAe;IAExC,MAAM,6BAA6B,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,uBAAuB;QACvB,kBAAkB,cAAc,CAAC;QACjC,kBAAkB,oBAAoB,CAAC;QAEvC,MAAM,wBACJ,gBAAgB,sBAAsB,CAAC,aAAa,IACpD,GAAG,gBAAgB,sBAAsB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,sBAAsB,CAAC,IAAI,EAAE;QAE9G,MAAM,yBAAyB,iBAAiB;QAChD,YAAY;YAAE,SAAS;QAAuB;IAChD,GAAG;QAAC;QAAmB,gBAAgB,sBAAsB;KAAC;IAE9D,MAAM,yBAAyB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACzC,IAAI,gBAAgB,gBAAgB,EAAE;YACpC,gBAAgB,gBAAgB,CAAC,KAAK;YACtC,kBAAkB,mBAAmB,CAAC;QACxC;QACA,kBAAkB,cAAc,CAAC;QACjC,kBAAkB,uBAAuB,CAAC;QAC1C,+BAA+B;QAC/B,sBAAsB;IACxB,GAAG;QAAC,gBAAgB,gBAAgB;QAAE;KAAkB;IAExD,MAAM,wBAAwB,CAAA,GAAA,oTAAA,CAAA,UAAO,AAAD,EAClC,IAAM,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB,sBAAsB,GAC7D;QAAC,gBAAgB,sBAAsB;KAAC;IAG1C,MAAM,iBAAiB,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY,OAAO;YACjB,IAAI,CAAC,SAAS,YAAY,MAAM,IAAI,MAAM;YAE1C,MAAM,EAAE,mBAAmB,EAAE,WAAW,EAAE,mBAAmB,EAAE,GAAG;YAElE,kBAAkB,cAAc,CAAC;YACjC,kBAAkB,iBAAiB,CAAC;YACpC,kBAAkB,uBAAuB,CAAC;YAC1C,+BAA+B;YAC/B,sBAAsB;YACtB,uBAAuB;YACvB,kBAAkB,yBAAyB,CAAC;gBAC1C,MAAM;gBACN,QAAQ;gBACR,OAAO;gBACP,gBAAgB;gBAChB,gBAAgB;gBAChB,MAAM;gBACN,eAAe;YACjB;YAEA,IAAI,YAAY;YAChB,IAAI,SAAS,YAAY,aAAa;gBACpC,YAAY,sBAAsB,IAAI;YACxC;YAEA,kBAAkB,eAAe,CAAC,MAAM,MAAM,EAAE;YAEhD,IAAI,gBAAgB,gBAAgB,EAAE;gBACpC,gBAAgB,gBAAgB,CAAC,KAAK;gBACtC,kBAAkB,mBAAmB,CAAC;YACxC;YAEA,MAAM,SAAS,eACb;gBACE,WAAW,QAAQ,UAAU;gBAC7B,aAAa,sBAAsB,eAAe,OAAO;gBACzD;YACF,GACA,MAAM,MAAM,EACZ,gBAAgB,oBAAoB;YAGtC,kBAAkB,mBAAmB,CAAC;YACtC,OAAO;gBAAE,SAAS;YAAK;QACzB;QACA,WAAW;QACT,4DAA4D;QAC9D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,oBAAoB;YAClC,kBAAkB,cAAc,CAAC;YACjC,kBAAkB,oBAAoB,CAAC;YACvC,iBAAiB;YACjB,cAAc;YACd,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;QAC3F;IACF;IAEA,MAAM,+BAA+B,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC7C,CAAC;QACC,IAAI,CAAC,MAAM;YACT,wBAAwB;YACxB,kBAAkB,cAAc,CAAC;YACjC,kBAAkB,uBAAuB,CAAC;YAC1C,+BAA+B;YAC/B;YACA,WAAW;gBACT,uBAAuB;gBACvB;YACF,GAAG;QACL;IACF,GACA;QAAC;QAAS;QAAmB;KAAkB;IAGjD,MAAM,qBAAqB,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,gBAAgB,WAAW,EAAE;YAC/B,+BAA+B;QACjC,OAAO;YACL,uBAAuB;YACvB,sBAAsB;QACxB;IACF,GAAG;QAAC,gBAAgB,WAAW;KAAC;IAEhC,MAAM,8BAA8B,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC5C,CAAC;QACC,uBAAuB;QACvB,IAAI,CAAC,MAAM;YACT,IAAI,CAAC,eAAe;gBAClB,iBAAiB;gBACjB,cAAc;YAChB;YACA;YACA,+BAA+B;QACjC;IACF,GACA;QAAC;QAAe;KAAkB;IAGpC,MAAM,+BAA+B,CAAA,GAAA,oTAAA,CAAA,cAAW,AAAD,EAC7C,CAAC;QACC,IAAI,CAAC,MAAM;YACT,sBAAsB;QACxB,OAAO;YACL,sBAAsB;QACxB;QAEA,IAAI,QAAQ,gBAAgB,WAAW,EAAE;YACvC,+BAA+B;YAC/B,sBAAsB;QACxB;IACF,GACA;QAAC,gBAAgB,WAAW;KAAC;IAG/B,IAAI,sBAAsB;QACxB,qBACE,6VAAC,6KAAA,CAAA,UAAiB;YAChB,eAAe,gBAAgB,aAAa,IAAI;YAChD,MAAM;YACN,cAAc;YACd,SAAS;gBACP,MAAM,SAAS,QAAQ;gBACvB,aAAa,gBAAgB,aAAa,IAAI;YAChD;;;;;;IAGN;IAEA,IAAI,YAAY;QACd,qBACE,6VAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,SAAS,IAAM,eAAe;sBAC/B;;;;;;IAIL;IAEA,qBACE;;YACG,CAAC,SAAS,YAAY,eAAe,gBAAgB,WAAW,IAAI,gCACnE,6VAAC,wKAAA,CAAA,UAAY;gBACX,MAAM;gBACN,cAAc;gBACd,iBAAiB,CAAC;oBAChB,eAAe,MAAM,CAAC;gBACxB;gBACA,aAAa,gBAAgB,WAAW;;;;;qCAG1C,6VAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,UAAU;gBACV,SAAS;0BACV;;;;;;YAKF,gBAAgB,WAAW,kBAC1B,6VAAC,8KAAA,CAAA,qBAAkB;gBACjB,OAAO;gBACP,cAAc,gBAAgB,YAAY;gBAC1C,aAAa,gBAAgB,WAAW;gBACxC,aAAa,gBAAgB,WAAW;gBACxC,oBAAoB;gBACpB,MAAM;gBACN,cAAc;;;;;;YAIjB,qCACC,6VAAC,mLAAA,CAAA,sBAAmB;gBAClB,MAAM;gBACN,UAAU;gBACV,YAAY,IAAM,eAAe,MAAM,CAAC,mBAAmB,SAAS;gBACpE,mBAAmB;gBACnB,oBAAoB;gBACpB,kBAAkB,eAAe,SAAS;gBAC1C,MAAM;gBACN,cAAc;gBACd,eAAe,gBAAgB,WAAW;gBAC1C,kBAAkB;gBAClB,eAAe;gBACf,aAAa,SAAS,YAAY,gBAAgB;;;;;;0BAItD,6VAAC,2IAAA,CAAA,cAAW;gBACV,MAAM,gBAAgB,iBAAiB;gBACvC,cAAc,kBAAkB,oBAAoB;0BAEpD,cAAA,6VAAC,2IAAA,CAAA,qBAAkB;oBAAC,WAAU;;sCAC5B,6VAAC,2IAAA,CAAA,oBAAiB;;8CAChB,6VAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6VAAC,2IAAA,CAAA,yBAAsB;oCAAC,WAAU;;sDAChC,6VAAC;4CAAE,WAAU;;gDACV,SAAS,YAAY,cAAc,iBAAiB;gDAAa;;;;;;;sDAGpE,6VAAC;4CAAI,WAAU;sDACZ,gBAAgB,sBAAsB,CAAC,aAAa,IACnD,GAAG,gBAAgB,sBAAsB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,sBAAsB,CAAC,IAAI,EAAE;;;;;;wCAE/G,sBAAsB,YAAY,kBACjC,6VAAC;;gDAAE;gDACqB,sBAAsB,WAAW,GAAG,SAAS;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMnF,6VAAC,2IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,6VAAC,2IAAA,CAAA,oBAAiB;oCAChB,WAAU;oCACV,SAAS,IAAM,kBAAkB,oBAAoB,CAAC;8CACvD;;;;;;gCAGA,sBAAsB,UAAU,kBAC/B,6VAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}