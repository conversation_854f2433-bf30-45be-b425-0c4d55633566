import asyncio
import sys
import signal
from fastapi import APIRouter
from core.db import Database
from core.models import ProjectAgentRun
from sqlalchemy import update

# Initialize APIRouter
router = APIRouter()

db = Database()

async def update_all_running_sessions_to_cancelled():
    async with db.get_async_session() as session:
        async with session.begin():
            stmt = update(ProjectAgentRun).where(ProjectAgentRun.status.in_(['running', 'stopping','initializing'])).values(status='cancelled')
            await session.execute(stmt)

async def shutdown_event():
    await update_all_running_sessions_to_cancelled()
    await db.close()


@router.on_event("shutdown")
async def shutdown():
    await shutdown_event()

def signal_handler(sig, frame):
    print("Shutting down gracefully...")
    asyncio.create_task(shutdown_event())
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
